import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Marker } from 'src/app/lib/@bus-tier/models';
import { Sortment } from 'src/app/lib/@pres-tier';
import { AreaService, CharacterService, MarkerService, ReviewService } from 'src/app/services';
import { EasyMVC } from 'src/lib/darkcloud/angular';
import { MarkerType } from 'src/lib/darkcloud/dialogue-system';

interface Preloadable {
  pinDialogueMarkers: Marker[];
}

@Component({
  selector: 'app-marked-collectible',
  templateUrl: './marked-collectible.component.html',
  styleUrls: ['./marked-collectible.component.scss']
})
export class MarkedCollectibleComponent 
extends EasyMVC.PreloadComponent<Preloadable>
implements OnInit {

  constructor(
    private _activatedRoute: ActivatedRoute,
    public readonly router: Router,
    public readonly areaService: AreaService,
    private _markerService: MarkerService,
    private _reviewService: ReviewService,
    private _characterService: CharacterService
  ) { 
    super(_activatedRoute, (data) => data.preloadedMarkedCollectible);
  }

  public sorting = new Sorting(this);

  public collectibles: Marker[] = [];

  ngOnInit(): void
  {
    this.collectibles = this._markerService.filterByType(MarkerType.MARK_COLLECTIBLE);
  }

  access(id:string)
  {
    this.router.navigate(
      [
        'levels/' + this._reviewService.reviewResults[id].levelId +
        '/dialogues/' + this._reviewService.reviewResults[id].dialogueId
      ],
      {fragment: id}
    )
  }

  sortedByAlphabet: boolean = false;
  sortByCharacterName()
  {
    if(!this.sortedByAlphabet)
    {
      this.collectibles.sort((a,b) => {
        let characterA = this._characterService.svcFindById(a.characterId);
        let characterB = this._characterService.svcFindById(b.characterId);

        return (characterA.name < characterB.name) ? -1 : (characterA.name > characterB.name) ? 1 : 0;
      });
      this.sortedByAlphabet = true;
    }
    else
    {
      this.collectibles.sort((a,b) => {
        let characterA = this._characterService.svcFindById(a.characterId);
        let characterB = this._characterService.svcFindById(b.characterId);

        return (characterA.name > characterB.name) ? -1 : (characterA.name < characterB.name) ? 1 : 0;
      });
      this.sortedByAlphabet = false;
    }
  }

}

class Sorting extends Sortment.Sorting {
  byLocation = Sortment.byLocation(this._component.areaService);
  byLevelLocation = Sortment.byLevelLocation(this._component.areaService);
  byPin = (marker: Marker) => marker.pin;
  byType = (marker: Marker) => marker.type;
  constructor(private readonly _component: MarkedCollectibleComponent) {
    super();
  }
  public sortPinMarkers(by: Sortment.Sortion<Marker>) {
    this.execute(this._component.collectibles, by);
  }
}