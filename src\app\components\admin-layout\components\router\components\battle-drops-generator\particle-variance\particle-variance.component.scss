.card {
  // padding-top: 8px !important;
   padding-top: 17px !important;
 }

 .card-header-content {
   display: block;
   margin-left: 30px;
   margin-right: 15px;
   width: 40%;
 }

.sticky th
{
  background-color: rgba(255, 255, 255, 0);
  top: 0px;
}
th 
{
  width: 200px !important; /* Adjust the width value as needed */
  height: 35px !important;
}

.col_index {
  padding-left: 20px;
  padding-right: 20px
}
input
{
  width: 100px !important;
}
.titleParticle {
  display: flex;
  align-items: center;
  padding-left: 30%;
  width: 150px !important;
}
.contentParticle {
  display: flex; 
  width: 150px;
  justify-content: center;
  padding: 0 !important;
}
