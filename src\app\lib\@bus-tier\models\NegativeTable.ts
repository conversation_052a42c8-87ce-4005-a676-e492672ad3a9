import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';

export class NegativeTable extends Base<Data.Hard.INegativeTable, Data.Result.INegativeTable> implements Required<Data.Hard.INegativeTable>
{
  public static generateId(index: number): string {
    return IdPrefixes.NEGATIVETABLE + index;
  }

  constructor( index: number, dataAccess: NegativeTable['TDataAccess']) 
  {
    super(
    {
      hard: 
      {
        id: NegativeTable.generateId(index),     
      },
    },
    dataAccess
    );
  }
  protected getInternalFetch() 
  {
    return {};
  }
  public get idNegative(): string
  {
    return this.hard.idNegative;
  }
  public set idNegative(value: string) 
  {
    this.hard.idNegative = value;
  }
  public get category(): string
  {
    return this.hard.category;
  }
  public set category(value: string) 
  {
    this.hard.category = value;
  } 
  public get idAffliction(): string
  {
    return this.hard.idAffliction;
  }
  public set idAffliction(value: string) 
  {
    this.hard.idAffliction = value;
  } 
  public get status(): string
  {
    return this.hard.status;
  }
  public set status(value: string) 
  {
    this.hard.status = value;
  }
  public get skillResistUser(): string
  {
    return this.hard.skillResistUser;
  }
  public set skillResistUser(value: string) 
  {
    this.hard.skillResistUser = value;
  }
  public get skillResistEnemy(): string
  {
    return this.hard.skillResistEnemy;
  }
  public set skillResistEnemy(value: string) 
  {
    this.hard.skillResistEnemy = value;
  }

  public get operator(): string
  {
    return this.hard.operator;
  }
  public set operator(value: string) 
  {
    this.hard.operator = value;
  }
  public get value(): string
  {
    return this.hard.value;
  }
  public set value(value: string) 
  {
    this.hard.value = value;
  }
  public get statusEffectName(): string
  {
    return this.hard.statusEffectName;
  }
  public set statusEffectName(value: string) 
  {
    this.hard.statusEffectName = value;
  }
  public get description(): string
  {
    return this.hard.description;
  }
  public set description(value: string) 
  {
    this.hard.description = value;
  }
  public get powerPoints(): string
  {
    return this.hard.powerPoints;
  }
  public set powerPoints(value: string) 
  {
    this.hard.powerPoints = value;
  }
  public get allNegative(): string
  {
    return this.hard.allNegative;
  }
  public set allNegative(value: string) 
  {
    this.hard.allNegative = value;
  }
  public get duration(): string
  {
    return this.hard.duration;
  }
  public set duration(value: string) 
  {
    this.hard.duration = value;
  }


}
