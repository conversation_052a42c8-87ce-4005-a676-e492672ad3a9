import { Component, OnInit } from '@angular/core';
import { PopupService } from 'src/app/services';
import { fadeIn } from './loading.animations';

@Component({
  selector: 'app-loading',
  templateUrl: './loading.component.html',
  styleUrls: ['./loading.component.scss'],
  animations: [fadeIn]
})
export class LoadingComponent implements OnInit {

  constructor(
    private popupService: PopupService
  ) { }

  isLoading: boolean = false;

  ngOnInit(): void {
    this.popupService.loading.subscribe(x => {
      this.loading(x);
    });
  }

  loading(val: boolean)
  {
    this.isLoading = val;
  }

}
