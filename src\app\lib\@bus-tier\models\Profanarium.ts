import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { Base } from './Base';

export class Profanarium extends Base<Data.Hard.IProfanarium, Data.Result.IProfanarium>
  implements Required<Data.Hard.IProfanarium>
{
  private static generateId(index: number): string 
  {
    return IdPrefixes.PROFANARIUM + index;
  }
  constructor(
    index: number,
    profanariumLevel: number,
    dataAccess: Profanarium['TDataAccess']
  ) 
  {
    super(
      {
        hard: 
        {
          id: Profanarium.generateId(index),
          profanariumLevel,
        },
      },
      dataAccess
    );
  }
  protected getInternalFetch() 
  {
    return {};
  }

  public get profanariumLevel(): number
  {
    return this.hard.profanariumLevel;
  }
  public set profanariumLevel(value: number)
  {
    this.hard.profanariumLevel = value;
  }

  public get improveTitanium(): number
  {
    return this.hard.improveTitanium;
  }
  public set improveTitanium(value: number)
  {
    this.hard.improveTitanium = value;
  }

  public get improveTime(): number
  {
    return this.hard.improveTime;
  }
  public set improveTime(value: number)
  {
    this.hard.improveTime = value;
  }

  public get improveRubies(): number
  {
    return this.hard.improveRubies;
  }
  public set improveRubies(value: number)
  {
    this.hard.improveRubies = value;
  }

  public get researchSouls(): number
  {
    return this.hard.researchSouls;
  }
  public set researchSouls(value: number)
  {
    this.hard.researchSouls = value;
  }

  public get researchTime(): number
  {
    return this.hard.researchTime;
  }
  public set researchTime(value: number)
  {
    this.hard.researchTime = value;
  }

  public get researchRubies(): number
  {
    return this.hard.researchRubies;
  }
  public set researchRubies(value: number)
  {
    this.hard.researchRubies = value;
  }
}
