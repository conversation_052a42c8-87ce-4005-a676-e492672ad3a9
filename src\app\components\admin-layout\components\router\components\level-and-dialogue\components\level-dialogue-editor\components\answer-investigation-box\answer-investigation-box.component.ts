import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import {
  Area,
  Box,
  Character,
  DCKnowledgeGuide,
  Dialogue,
  Item,
  Level,
  Mission,
  Option,
  OptionBox,
  SpokePlace,
  StoryBox,
  StoryProgress,
} from 'src/app/lib/@bus-tier/models';
import { RoadBlock } from 'src/app/lib/@bus-tier/models/RoadBlock';
import { Typing } from 'src/app/lib/@pres-tier';
import {
  AreaService,
  DialogueService,
  ItemService,
  LevelService,
  OptionService,
  ReviewService,
} from 'src/app/services';
import { EventService } from 'src/app/services/event.service';
import { MarkerService } from 'src/app/services/marker.service';
import { RoadBlockService } from 'src/app/services/road-block.service';
import { SpeechService } from 'src/app/services/speech.service';
import { StoryBoxService } from 'src/app/services/story-box.service';
import { Alert } from 'src/lib/darkcloud';
import { RoadBlockType } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard';
import { EventType, GameTypes } from 'src/lib/darkcloud/dialogue-system';
import { LevelHelperService } from './../../../../../../../../../../services/level-helper.service';
import { HighlightElement } from 'src/lib/others';

interface SpokePlaceHelper {
  elementId: string;
  text: string;
  label: string;
  component: string;
}

@Component({
  selector: 'app-answer-investigation-box',
  templateUrl: './answer-investigation-box.component.html',
  styleUrls: ['./answer-investigation-box.component.scss'],
})
export class AnswerInvestigationBoxComponent implements OnInit, AfterViewInit {
  @Input() option: Option;
  @Input() answerBox: StoryBox;
  @Input() preloadedSpeakers: Character[];
  @Input() preloadedMissionsOfArea: Mission[];
  @Input() optionBox: OptionBox;
  @Input() language: string;
  @Output() refresh: EventEmitter<void> = new EventEmitter();
  @Output() updateOption: EventEmitter<Option> = new EventEmitter();

  timeout;
  toMoveStoryProgressFunc: (marker: any) => void;
  toRemoveStoryProgressFunc: (marker: any) => void;
  toRemoveProcessConditionFunc: (roadblock: any) => void;

  roadBlockId: string;
  myRoadblock: RoadBlock;
  selectedStoryBox: string[] = [];
  public roadblock: RoadBlock;
  textLabel: string;

  trackByIndex(index: number, box: Box): any {
    return index;
  }

  listDCKnowledgeGuide: DCKnowledgeGuide[] = [];
  public itemList: Item[] = [];
  roadBlocksUseds: RoadBlock[] = [];
  hasLabelText: boolean = false;
  countingRoadblocks: number = 0;
  usedRoadBlocks = [];
  usedOnLevels = [];
  positive: string;
  isTextValid = true;
  isUsedRoad = false;
  listSpokePlace: SpokePlace[] = [];
  listStoryProgressIds = [];

  constructor(
    private _storyBoxService: StoryBoxService,
    private _optionService: OptionService,
    private _speechService: SpeechService,
    private _eventService: EventService,
    private _markerService: MarkerService,
    private _change: ChangeDetectorRef,
    private _roadblockService: RoadBlockService,
    private _itemService: ItemService,
    private _levelHelperService: LevelHelperService,
    private _dialogueService: DialogueService,
    private _areaService: AreaService,
    private _levelService: LevelService,
    private _reviewService: ReviewService,   
  ) {
    this.toMoveStoryProgressFunc = this.toMoveStoryProgress.bind(this);
    this.toRemoveStoryProgressFunc = this.toRemoveStoryProgress.bind(this);
    this.toRemoveProcessConditionFunc = this.toRemoveRoadblock.bind(this);
  }

  async ngOnInit() {
    await this._roadblockService.toFinishLoading();
    this._levelHelperService.toFinishLoading();
    this.listStoryProgressIds = [];
    this.itemList = this._itemService.models;
    this.listSpokePlace = this._levelHelperService.models;
    this.roadblock = this._roadblockService.filterByStoryBoxId(
      this.answerBox?.id
    );
    if (this.option.AndOrCondition == undefined) {
      this.option['AndOrCondition'] = 'OR';
    }
    this.getRoadblockInThisBox();
    this.getInfoDiceOption();
    this.roadblocksForKeyInformation();
  }

  transaleteKnowledge(knowledge: string, cdKnowledge: DCKnowledgeGuide) {
    const knowledgeMap = {
      Arcanismo: cdKnowledge?.arcana,
      Engenharia: cdKnowledge?.engineering,
      Investigação: cdKnowledge?.investigation,
      Furtividade: cdKnowledge?.stealth,
    };
    return knowledgeMap[knowledge];
  }

  getInfoDiceOption() {
    
     const orderStoryProgressIds = this.answerBox.storyProgressIds;
    // DICE Positive
    if (this.answerBox.id === this.option.answerBoxId) {
      this.answerBox.labelOption = this.option?.labelAnswerPositive || this.option?.label;
      this.answerBox.investigationPositive = this.option.investigationPositive;
      this.textLabel = '';
      this.checkEventsAndMarkers(this.option.answerBoxId);
    } //DICE Negative
    if (this.answerBox.id === this.option.answerBoxNegativeId) {
      this.answerBox.investigationNegative = this.option.investigationNegative;
      this.answerBox.subContextDescription = this.option.subContextDescription;
      this.answerBox.labelOption = this.option?.labelAnswerNegative;
      this.checkEventsAndMarkers(this.option.answerBoxNegativeId);
    }

    this.answerBox.type = this.option?.type;
    this.answerBox.nameKnowledge = this.option.nameKnowledge;
    this.answerBox.resultDCOption = this.option.resultDC;
    this.answerBox.messageOption = this.option.message;
    this.answerBox.subcontext = this.option.subcontext;
    this.answerBox.descriptionDCGuideOption = this.option?.descriptionDCGuide;
    this.answerBox.descriptionKnowledge = this.option.descriptionInvestigation;
    this.answerBox.fatorSituationModifier = this.option.fatorSituationModifier;
    this.answerBox.valueSituationModifier = this.option.valueSituationModifier;
    this.answerBox.descriptionSituationalModifier = this.option.descriptionSituationalModifier;

    const newListStoryProgressIds = this.maintainIdsPosition(this.listStoryProgressIds, orderStoryProgressIds);

    this.answerBox.storyProgressIds = newListStoryProgressIds;
    this._storyBoxService.svcToModify(this.answerBox);
  }

  checkEventsAndMarkers(id: string) {

    this.answerBox.storyProgressIds = [];
  
    this._speechService.models.forEach((speech) => {
      if (speech.id.includes(id)) {
        this.listStoryProgressIds.push(speech.id);
      }
    });
  
    this._eventService.models.forEach((event) => {
      if (event.id.includes(id)) {
        this.listStoryProgressIds.push(event.id);
      }
    });
  
    this._markerService.models.forEach((marker) => {
      if (marker.id.includes(id)) {
        this.listStoryProgressIds.push(marker.id);
      }
    });  
  }

  //Método criado para manter os IDs existentes na mesma posição
maintainIdsPosition(listStoryProgressIds: string[], storyBoxStoryProgressIds: string[]): string[] {
  const result = [];
  for (let i = 0; i < storyBoxStoryProgressIds.length; i++) { 
    if (listStoryProgressIds.includes(storyBoxStoryProgressIds[i])) {
      result.push(storyBoxStoryProgressIds[i]);
    }
  }
  return result;
}


  async roadblocksForKeyInformation() {
    const novoId = this.answerBox.id.split('.').slice(0, -1).join('.');
    const roadblocks = this._roadblockService.models.filter((roadblock) => {
      return (
        roadblock.spokeElementId === this.answerBox.id/* ||
        roadblock.spokeElementId === novoId*/
      );
    });

    for (const roadblock of roadblocks) {
      const dialogueIds = this._dialogueService.models.filter((dialogue) => {
        return (
          roadblock.StoryBoxId &&
          !dialogue.id.includes('ML') &&
          roadblock.StoryBoxId.includes(dialogue.id)
        );
      });

      for (const dialogue of dialogueIds) {
        if (dialogue.boxIds.length > 0) {
          const areaId = Area.getSubIdFrom(dialogue.id);
          const area = this._areaService.svcFindById(areaId);
          if (!area) return;
  
          const levelId = Level.getSubIdFrom(dialogue.id);
          const level = this._levelService.svcFindById(levelId);
          const dialogueId = Dialogue.getSubIdFrom(dialogue.id, 'PT-BR');
          const dialogueData = this._dialogueService.svcFindById(dialogueId);
          const hierarchyCode = area.hierarchyCode;
          const levelIndex = this._reviewService.reviewResults[levelId]?.index;
          const type = GameTypes.dialogueTypeDisplay[+dialogueData.type];
  
          if (levelIndex && level?.name) {
            this.usedOnLevels.push(
              `[${hierarchyCode}] ${levelIndex} "${level.name}" (${type})`
            );
            this.usedRoadBlocks.push(roadblock);
          }
        }
      }
    }
  }

  isUsedRoadblock(answerBox: StoryBox): boolean {
    const novoId = answerBox.id.split('.').slice(0, -1).join('.');
    // Retorna true se encontrar um elemento em usedRoadBlocks com o mesmo spokeElementId que o id do answerBox
    const isUsed = this.usedRoadBlocks.some(
      (rb) => rb.spokeElementId == answerBox.id || rb.spokeElementId == novoId
    );

    if (isUsed) {
      this.isUsedRoad = true;
    } else {
      this.isUsedRoad = false;
    }
    return isUsed;
  }

  countRoadblocks() {
    this.countingRoadblocks = 0;
    this.roadBlocksUseds.forEach((rb) => {
      if (
        this.answerBox?.id.includes(rb?.StoryBoxId) &&
        rb?.StoryBoxId != this.optionBox.id
      ) {
        this.countingRoadblocks++;
      }
    });
  }

  async changeLabel(event: Event, answerBox: StoryBox) {
    const inputElement = event.target as HTMLInputElement;
    const text = inputElement.value.trim();

    const existingLabels = {};
    this.listSpokePlace.forEach((spoke) => {
      existingLabels[spoke.originalLabel] = true;
    });

    const spoke = this.listSpokePlace.find(
      (spoke) => spoke.elementId === answerBox.id
    );

    if (text === '') {
      inputElement.value = '<<Label for progress condition>>';
      answerBox.labelOption = undefined;     
      this._storyBoxService.svcToModify(answerBox);
      this._storyBoxService.toSave();
      this.getLabelAnswerToOption(answerBox); 

      if (spoke) {  
        this.removeBDLabelPlaces(answerBox.id, spoke.id)
       }

       this.refresh.emit();  
    } 
    else {
      if (existingLabels[text]) {
      // Label já existe na base de dados do Places 
       Alert.showError('This Label ALREADY Exists!!', '');

        if(answerBox.labelOption === undefined) {
            inputElement.value = '<<Label for progress condition>>';
            this._change.detectChanges();
          } else {
           inputElement.value = answerBox.labelOption;
        }
        this.refresh.emit();  
      } 
      else {
        // Atualiza label
        if (spoke) {
          spoke.originalLabel = text;
          spoke.text = '[InvestigationBox > Answer] ' + text;
          this._levelHelperService.svcToModify(spoke);
        } 
        else {
          // Adiciona label
          const helper: SpokePlaceHelper = {
            elementId: answerBox.id,
            label: text,
            component: '[InvestigationBox > Answer]',
            text: '[InvestigationBox > Answer] ' + text,
          };
          this._levelHelperService.createNewLevelHelper(helper);
        }
        inputElement.value = text;
        answerBox.labelOption = text;
        this.getLabelAnswerToOption(answerBox);
      }
    }
}

  getLabelAnswerToOption(answerBox: StoryBox) {
    if (answerBox.id === this.option.answerBoxId) {
      this.option.labelAnswerPositive = answerBox.labelOption;
    } else {
      this.option.labelAnswerNegative = answerBox.labelOption;
    }
    this._optionService.svcToModify(this.option);
    this._optionService.toSave();
  }

  async removeBDLabelPlaces(idAnswerBox: string, idPlaces: string) { 
    await this._roadblockService.models.forEach((roadblock) => {
      if (roadblock.spokeElementId === idAnswerBox) {
        roadblock.spokeElementId = undefined;
        this._roadblockService.svcToModify(roadblock);
      }
    });

    await this._levelHelperService.svcToRemove(idPlaces);   
  }

  public async toAddSpeech() {
    try {
      const speech = await this._speechService.svcPromptCreateNew(
        this.answerBox.id
      );
      this._speechService.srvAdd(speech);

      await this._storyBoxService.addStoryProgress(this.answerBox, speech.id);
      this._change.detectChanges();
      
      // Scroll para o novo componente com efeito suave
    setTimeout(() => {
        HighlightElement(speech.id, 110, true, 'transparent');
      }, 100);

    } catch (error) {
      Alert.showError(error);
    }
  }

  public async toAddEvent() {
    try {
      const event = await this._eventService.svcPromptCreateNew(
        this.answerBox.id
      );
      await this._eventService.srvAdd(event);
      await this._storyBoxService.addStoryProgress(this.answerBox, event.id);
      this._change.detectChanges();
    } catch (error) {
      Alert.showError(error);
    }
  }
  public async toAddMissionEvent() {
    let event = await this._eventService.svcPromptCreateNew(this.answerBox.id);
    event.type = EventType.ASSIGN_MISSION;
    await this._eventService.srvAdd(event);
    await this._storyBoxService.addStoryProgress(this.answerBox, event.id);
    this._change.detectChanges();
  }

  public async toAddBossEvent() {
    try {
      const marker = await this._markerService.svcPromptCreateNew(
        this.answerBox?.id
      );
      marker.type = 0; //Pass this type here to make it be of boss event.
      await this._markerService.srvAdd(marker);
      await this._storyBoxService.addStoryProgress(this.answerBox, marker.id);
      this._change.detectChanges();
    } catch (error) {
      Alert.showError(error);
    }
  }

  public async toAddItemEvent() {
    let event = await this._eventService.svcPromptCreateNew(this.answerBox.id);
    event.type = EventType.GIVE_ITEM;
    await this._eventService.srvAdd(event);
    await this._storyBoxService.addStoryProgress(this.answerBox, event.id);
    this._change.detectChanges();
  }

  public async toAddCinematicEvent() {
    let event = await this._eventService.svcPromptCreateNew(this.answerBox.id);
    event.type = EventType.PLAY_VIDEO;
    await this._eventService.srvAdd(event);
    await this._storyBoxService.addStoryProgress(this.answerBox, event.id);
    this._change.detectChanges();
  }

  public async toAddLoopEvent() {
    let event = await this._eventService.svcPromptCreateNew(this.answerBox.id);
    event.type = EventType.REFUSE_PAYMENT;
    await this._eventService.srvAdd(event);
    await this._storyBoxService.addStoryProgress(this.answerBox, event.id);
    this._change.detectChanges();
  }

  public async toAddMarker() {
    try {
      const marker = await this._markerService.svcPromptCreateNew(
        this.answerBox.id
      );
      await this._markerService.srvAdd(marker);
      await this._storyBoxService.addStoryProgress(this.answerBox, marker.id);
      this._change.detectChanges();
    } catch (error) {
      Alert.showError(error);
    }
  }

  public async toAddRoadblock() {
    let roadblock = await this._roadblockService.svcPromptCreateNew(
      this.answerBox.id,
      RoadBlockType.OBTAINED_ITEM
    );
    this.roadBlockId = roadblock.id;
    await this._roadblockService.srvAdd(roadblock);
    this.roadBlocksUseds.push(roadblock);
    this._change.detectChanges();
    this.ngOnInit();
    this.refresh.emit();
  }

  async toRemoveRoadblock(roadblock: RoadBlock) {
    if (await Alert.showRemoveAlert(Typing.typeName(roadblock))) {
      this.roadBlockId = undefined;
      this._roadblockService.svcToRemove(roadblock.id);
      this.roadBlocksUseds = this.roadBlocksUseds.filter(
        (rb) => rb.id != roadblock.id
      );
      this._change.detectChanges();
      this.ngOnInit();
    }
  }

  public async toMoveStoryProgress(sp: StoryProgress, transpose: number) {
    if (this.answerBox.storyProgressIds.length <= 1) return;

    const oldIndex = this.answerBox.storyProgressIds.indexOf(sp.id);
    const newIndex = oldIndex + transpose;
    if (!this.answerBox.storyProgressIds[newIndex]) return;

    this.answerBox.storyProgressIds[oldIndex] =
      this.answerBox.storyProgressIds[newIndex];
    this.answerBox.storyProgressIds[newIndex] = sp.id;
    await this._storyBoxService.svcToModify(this.answerBox);
  }

  async ngAfterViewInit() {
    await this._roadblockService.toFinishLoading();

    let roadblock = this._roadblockService.filterByStoryBoxId(
      this.answerBox?.id
    );
    this.roadblock = roadblock;

    if (roadblock) this.roadBlockId = roadblock.ID;

    this._storyBoxService.models.forEach((sb) => {
      if (sb.label != undefined) this.selectedStoryBox.push(sb.label);
    });

    this._optionService.models.forEach((sb) => {
      if (sb.label != undefined) this.selectedStoryBox.push(sb.label);
    });
  }

  getRoadblockInThisBox() {
    this.roadBlocksUseds = [];
    for (let i = 0; i < this._roadblockService.models.length; i++) {
      if (
        this._roadblockService.models[i]?.StoryBoxId?.trim() ==
        this.answerBox?.id?.trim()
      ) {
        this.roadBlocksUseds.push(this._roadblockService.models[i]);
      }
    }

    this.countRoadblocks();
  }

  public async toRemoveStoryProgress(sp: StoryProgress) {
    if (await Alert.showRemoveAlert(Typing.typeName(sp))) {
      await this._speechService.svcToRemove(sp.id);
      await this._eventService.svcToRemove(sp.id);
      await this._markerService.svcToRemove(sp.id);
      await this._storyBoxService.toRemoveStoryProgress(this.answerBox.id,sp.id);
      this._change.detectChanges();
    }
  }

  ngOnDestroy(): void {
    clearTimeout(this.timeout);
  }

  async chooseAndOrCondition(event) {
    //AND = FALSE, OR = TRUE.
    if (event.target.checked == false) this.option['AndOrCondition'] = 'AND';
    else this.option['AndOrCondition'] = 'OR';

    await this._optionService.svcToModify(this.option);
    await this._optionService.toSave();
  }
}
