import { Component } from '@angular/core';
import { Keyword } from 'src/app/lib/@bus-tier/models';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { TranslationService } from 'src/app/services/translation.service';
import { LanguageService } from 'src/app/services/language.service';
import { KeywordService, KeywordsTagsService, PopupService } from 'src/app/services';
import { Button } from 'src/app/lib/@pres-tier/data';
import { Alert, Popup } from 'src/lib/darkcloud';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-keywords-list',
  templateUrl: './keywords-list.component.html',
  styleUrls: ['./keywords-list.component.scss']
})

export class KeywordsListComponent extends TranslatableListComponent<Keyword> 
{
  public keywordsTags = [];  
  keywordList = [];
  keywordsToSearch = [];
  description;
  isFirstTime = true;
  indexTagSorting = 0;
    
  accentSensitive = false;
  caseSensitive = false;

  constructor(
    _activatedRoute: ActivatedRoute,
    private _keywordService: KeywordService,
    _userSettingsService: UserSettingsService,
    protected override _translationService: TranslationService,
    protected override _languageService: LanguageService,
    private _router: Router,
    private _popupService: PopupService,
    private _keywordsTagsService : KeywordsTagsService

  ) 
  {
    super(_keywordService, _activatedRoute, _userSettingsService, 'name', _translationService, _languageService);
  }

  public readonly tagsButton: Button.Templateable = 
  {
    title: 'Go to the Tags List',
    onClick: this.goToTags.bind(this),
    iconClass: 'pe-7s-ticket',
    btnClass: Button.Klasses.FILL_BRIGHTBLUE,
  };

  public override readonly addButtonTemplate: Button.Templateable = 
  {
    title: 'Add a new instance to the list',
    onClick: this.addKeywordTag.bind(this),
    iconClass: 'pe-7s-plus',
    btnClass: Button.Klasses.FILL_GREEN,
  };

  public getKeywordsOrtography(keyword: Keyword)
  {
    this._translationService.getKeywordsOrtography(keyword, true);
  }

  override async lstInit()
  {
    await this._keywordsTagsService.toFinishLoading();
    this.keywordsTags = this._keywordsTagsService.models;
  
    this.keywordList = this._keywordService.models.filter(keyword => this.lstIds.includes(keyword.id));
    this.keywordsToSearch = this.keywordList;

    this.description = `Showing ${ this.keywordsToSearch.length} results`;
  }

  async removeKeyword(keyword:Keyword)
  {
    const removed = await this._modelService.toPromptRemove(keyword);
    if (!removed) return;
    
    this.keywordList = this._keywordService.models.filter(key=> key.key !== keyword.key);
    this._keywordService.svcToRemove(keyword.id);
    this._keywordService.toSave();
    this.keywordsToSearch = this.keywordList;
  } 

  keywordsTagsAlphabeticOrder()
  {
    if(this.isFirstTime)
    {
      this.keywordsTags.sort((a, b) => a.name.localeCompare(b.name));
      this.isFirstTime = false;
    }
    
    this.keywordsTags.sort((a, b) => this.lstIds.indexOf(a.name)-this.lstIds.indexOf(b.name));
  }

  sortByTags()
  {
    //Get tagsIds list
    let tagsIds = this._keywordsTagsService.svcGetTagsUsed(this.keywordList);

    if(tagsIds.length != 0)
    {
      let searchTagIds = [];
      for(let i = 0; i < tagsIds.length; i++)
      {
        let index = i + this.indexTagSorting;
        if (index >= tagsIds.length)
          index -= tagsIds.length;

          searchTagIds.push(tagsIds[index]);
      }

      this.keywordsToSearch = this.keywordsToSearch
        .map(item => 
          {
          let firstTab = 99999;
          if (item.keywordTags)
          {
            for (let tabIndex = 0; tabIndex < searchTagIds.length; tabIndex++)
            {
              if(item.keywordTags.includes(searchTagIds[tabIndex]))
              {
                firstTab = tabIndex;
                tabIndex = searchTagIds.length;
              }
            }
          }
          item["firsTag"] = firstTab;
          return item
        })
        .sort((a, b) => 
        {
          if(a["firsTag"] > b["firsTag"])
            return 1;
          else if(a["firsTag"] < b["firsTag"])
            return -1;
          return 0;
        })

      this.indexTagSorting++;

      if(this.indexTagSorting >= tagsIds.length) 
      {
        this.indexTagSorting = 0;
      }
    }
  }
  
  areaHeight(word)
  {
    let el = document.getElementById(word); 
    el.style.height = "auto";
    el.style.height = (5 + el.scrollHeight) + "px";
  }

  goToTags()
  {
    this._router.navigate(['/keywordsTags']);
  }

  protected override lstAfterFetchList() 
  {
    this.renderList();
  }

  protected override filterItem(keyword: Keyword) 
  {
    return (keyword.word.includes(this.lstSearchTerm) || keyword.key.includes(this.lstSearchTerm));
  }
  
  renderList() 
  {
    this._keywordService.InitValues();
    this.lstIds = this._keywordService.modelIds;
  } 
  
  async addKeywordTag()
  {
    let itemClass;
    try 
    {
      itemClass = await this._keywordService.svcPromptCreateNew();
    
    } 
    catch (e) 
    {
      Alert.showError("Keyword já existe!");
    }
    if(!itemClass) return;

    this.lstResetHighlights();
    this.HighlightElement(itemClass.id, 110, true);
    
    await this._keywordService.srvAdd(itemClass);
    await this._keywordService.toSave();
    this.lstInitFetchList();
    this.lstAfterInitFetchList();
    this.lstAfterFetchList();
    this.lstInit();
  }

  public async addTagWithPopup(item: Keyword)
  {
    let buttons: Popup.Button<Keyword>[] = [];
    const selectedTagIds = item.keywordTags || [];

    this.keywordsTags.forEach(x => 
    {
      buttons.push(new Popup.Button<Keyword>(x.name, x, 'btn btn-fill'));
    });

    const selectedTag = await this._popupService.fire< Keyword, Keyword >(
      new Popup.Interface
      ({
          title: 'Select Tag',
          actionsClass: 'column'
        },
        buttons
      )
    );
    if(selectedTag == undefined) return;
    
    if (selectedTagIds.includes(selectedTag.value.id)) 	
    {	
        Alert.showError("This tag has been selected previously.","Tag already added!")	
        return;	
    }	


      if (item.keywordTags) 
      {
        if (!selectedTagIds.includes(selectedTag.value.id)) 
        {
          item.keywordTags.push(selectedTag.value.id);
        }
      } 
      else 
      {
        item.keywordTags = [];
        item.keywordTags.push(selectedTag.value.id);
      }
      
      this.keywordsTags = this.keywordsTags.filter(tag => tag.id !== selectedTag.value.id);    

      this._keywordService.svcToModify(item);
      this.lstInit();
  }

  changeTag(item: Keyword, tagId: string, index: number)
  {
    if(tagId == "")
    {
      item.keywordTags.splice(index, 1);
    }
    else
    {
      item.keywordTags[index] = tagId;
    }

    this._keywordService.svcToModify(item);
  }

  override lstOnChangeFilterOptions(name: {accentSensitive, caseSensitive})
  {
    this.accentSensitive = name.accentSensitive;
    this.caseSensitive = name.caseSensitive;
  }
  
   override lstOnChangeFilter(name:string)
  {
    if(name == '')
    {
      this.keywordsToSearch = this.keywordList
      this.description = `Showing ${ this.keywordsToSearch.length} results`;
      return;
    }
    //Accept all words
    if(!this.accentSensitive && !this.caseSensitive)
    {
      name = this.simplifyString(name).toUpperCase()
      this.keywordsToSearch = this.keywordList.filter(keyword => this.simplifyString(keyword.word)?.toUpperCase()?.includes(name) 
      || this.simplifyString(keyword.key)?.toUpperCase()?.includes(name) || this.simplifyString(keyword.id)?.toUpperCase()?.includes(name)
      || this.simplifyString(keyword.notes)?.toUpperCase()?.includes(name))
    } 
    //case sensitive
    else if(!this.accentSensitive)
    {
      name = this.simplifyString(name)
      this.keywordsToSearch = this.keywordList.filter(keyword => this.simplifyString(keyword.word)?.includes(name) 
      || this.simplifyString(keyword.key)?.includes(name) || this.simplifyString(keyword.id)?.includes(name)
      || this.simplifyString(keyword.notes)?.includes(name))
    }
    //accent sensitive
    else if(!this.caseSensitive)
    {
      name = name.toUpperCase()
      this.keywordsToSearch = this.keywordList.filter(keyword => keyword.word?.toUpperCase()?.includes(name) 
      || keyword.key?.toUpperCase()?.includes(name) || keyword.id?.toUpperCase()?.includes(name)
      || keyword.notes?.toUpperCase()?.includes(name))
      //case sensitive and accent sensitive
    }
    else
    {
      this.keywordsToSearch = this.keywordList.filter(keyword => keyword.word?.includes(name) 
      || keyword.key?.includes(name) || keyword.id?.includes(name)  || keyword.notes?.includes(name))
    }
    
    this.description = `Showing ${ this.keywordsToSearch.length} results`;
  } 

  override simplifyString(str: string): string 
  {
    return (str ?.normalize('NFD') ?.replace(/[\u0300-\u036f]/g, '') ?.trim())
  }

  wordChange(keyword: Keyword, word: string, value: string) {
    keyword.isReviewedWord = false;
    keyword.revisionCounterWordAI = 0;
    this.lstOnChange(keyword, 'word', value);
  }
}
