import { Component, Input, SimpleChanges } from '@angular/core';
import { DCGuide } from 'src/app/lib/@bus-tier/models/DCGuide';
import { DCGuideService } from 'src/app/services';
import { Alert } from 'src/lib/darkcloud';

@Component({
  selector: 'app-dc-attribute-guide',
  templateUrl: './dc-attribute-guide.component.html',
  styleUrls: ['./dc-attribute-guide.component.scss']
})
export class DcAttributeGuideComponent {

  @Input() copyAttributeBehavior: string[];
  listDCGuide: DCGuide[] = [];
  listDC: DCGuide;
  isFirstChange = true;

  constructor(
    private _dcGuideService: DCGuideService,
  ) { }

  ngOnChanges(changes: SimpleChanges) {

    if (changes['copyAttributeBehavior']) {
      if (this.isFirstChange) {
        // Ignorar a primeira alteração no ciclo de vida
        this.isFirstChange = false;
      } else if (this.copyAttributeBehavior && this.copyAttributeBehavior.length > 0) {
        this.onExcelPaste();
      }
    }
  }

  public async ngOnInit() {
    this._dcGuideService.toFinishLoading();
    this.listDCGuide = this._dcGuideService.models || [];    
  }

  async onExcelPaste() {

    // Verifica se `this.copyAttributeBehavior` contém dados
    if (!this.copyAttributeBehavior || this.copyAttributeBehavior.length === 0) {
      Alert.showError('No data found in the copied Excel content.');
      return this.ngOnInit();
    }

    // Primeira linha deve conter os títulos das colunas
    const [headers, ...data] = this.copyAttributeBehavior;
    const columns = headers.split('\t');

    // Validação das colunas copiadas
    if (columns.length < 3) {
      Alert.showError("Excel copied missing columns.");
      return;
    }

    // Processa as linhas copiadas (a partir da segunda linha)
    this.listDCGuide = [];
    for (const line of this.copyAttributeBehavior) {
      const [dcMin, dcMax, description] = line.split('\t');

      // Validação para garantir que todas as colunas tenham valores
      if (dcMin && dcMax && description) {
        const newItem = await this._dcGuideService.createNewCDGuide();
        newItem.dcMin = +dcMin;  // Converte para número
        newItem.dcMax = +dcMax;  // Converte para número
        newItem.description = description;
        this._dcGuideService.svcToModify(newItem);
      } else {
        console.warn("Linha com dados incompletos encontrada.");
      }
    }

    await this._dcGuideService.toSave();
    Alert.ShowSuccess('DC Guide imported successfully!');
    this.ngOnInit();
  }

  changeDCValue(index: number, field: string, value: string) {
    // Altera o campo do item baseado no index
    if (field === 'dcMin') {
      this.listDCGuide[index].dcMin = value === '' ? null : +value;
    } else if (field === 'dcMax') {
      this.listDCGuide[index].dcMax = value === '' ? null : +value;
    } else if (field === 'description') {
      this.listDCGuide[index].description = value;
    }

    this._dcGuideService.svcToModify(this.listDCGuide[index]);
    this._dcGuideService.toSave();
  }

  removeLineDC(index: number) {
    const itemToRemove = this.listDCGuide[index];

    if (itemToRemove && itemToRemove.id) {
      this._dcGuideService.svcToRemove(itemToRemove.id);
    }

    this.listDCGuide.splice(index, 1); // Remove o item da lista local
    this.ngOnInit();
  }
}
