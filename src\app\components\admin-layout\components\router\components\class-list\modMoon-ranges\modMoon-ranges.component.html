<div class="main-content">
  <div class="container-fluid">
      <div class="list-header-row update">
        <div class="card">
          <div style="display: flex; justify-content: space-between; padding-bottom: 10px;">
            <div class="card-header-content">
              <h3 class="title">MODMoon Ranges</h3>
              <p style="width:60vw;" class="category">{{ description ===  undefined ? 'Tabela vazia' : description}}</p>
            </div>
            <div style="display: flex; align-items: end; justify-content: end; align-items: center;">
              <ng-container>
                <!--BUTTON EXCEL-->
                <div id="button" style="position: absolute; margin-top: 60px;">
                  <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'" class="add-buttons"
                    [buttonTemplates]="[excelButtonTemplate]">
                  </app-button-group>
                </div>
              </ng-container>
            </div>
          </div>
        </div>
      </div>

      <!-- <PERSON><PERSON><PERSON> ModMoon Ranges -->
        <div class="card-body">
          <!-- Container com scroll horizontal -->
           <ng-container *ngIf="listModMoonRanges.length > 0">
          <div class="table-responsive horizontal-scroll">
            <table class="table table-bordered">
            <thead>
              <!-- Cabeçalho Superior -->
              <tr>
                <th rowspan="2" class="styleTitle">Index</th>
                <th rowspan="2" class="styleTitle">Fase da Lua</th>
                <th rowspan="2" class="styleTitle">Nomenclatura Técnica</th>
                <!-- Colunas dinâmicas baseadas no primeiro item do array -->
                <ng-container *ngIf="listModMoonRanges.length > 0">
                  <th [attr.colspan]="listModMoonRanges[0].knowledge?.length || 0" class="styleTitle">DC KNOWLEDGE</th>
                  <th colspan="2" class="styleTitle">DANO</th>
                  <th [attr.colspan]="listModMoonRanges[0].attribute?.length || 0" class="styleTitle">DC ATTRIBUTE</th>
                </ng-container>
                <!-- Fallback quando não há dados -->
                <ng-container *ngIf="listModMoonRanges.length === 0">
                  <th [attr.colspan]="listKnowledge.length" class="styleTitle">DC KNOWLEDGE</th>
                  <th colspan="2" class="styleTitle">DANO</th>
                  <th [attr.colspan]="listAttributte.length" class="styleTitle">DC ATTRIBUTE</th>
                </ng-container>
              </tr>
              <!-- Subcabeçalho -->
              <tr>
                <!-- Subcabeçalho baseado no primeiro item do array -->
                <ng-container *ngIf="listModMoonRanges.length > 0">
                  <!-- Colunas de Knowledge do primeiro item -->
                  <th class="styleTitle subTitle" *ngFor="let knowledgeItem of listModMoonRanges[0].knowledge">
                    {{ getKnowledgeNameById(knowledgeItem.id) }}
                  </th>

                  <!-- Colunas de Dano -->
                  <th class="styleTitle subTitle">MOD Dano Party</th>
                  <th class="styleTitle subTitle">MOD Dano Oponente</th>

                  <!-- Colunas de Attributes do primeiro item -->
                  <th class="styleTitle subTitle" *ngFor="let attributeItem of listModMoonRanges[0].attribute">
                    {{ getAttributeNameById(attributeItem.id) }}
                  </th>
                </ng-container>

                <!-- Fallback quando não há dados -->
                <ng-container *ngIf="listModMoonRanges.length === 0">
                  <th class="styleTitle subTitle" *ngFor="let knowledge of listKnowledge">{{ knowledge.knowledge }}</th>
                  <th class="styleTitle subTitle">MOD Dano Party</th>
                  <th class="styleTitle subTitle">MOD Dano Oponente</th>
                  <th class="styleTitle subTitle" *ngFor="let attribute of listAttributte">{{ attribute.atributte }}</th>
                </ng-container>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let moonRange of listModMoonRanges; let i = index;" [id]="moonRange.id">
                <!-- Index -->
                <td class="gray" style="width: 50px !important;">{{ i + 1 }}</td>

                <!-- Fase da Lua -->
                <td class="td-notes alignLeft backtable" style="padding-left: 5px; width: 250px !important;">
                  {{ moonRange.moonPhase }}
                </td>

                <!-- Nomenclatura Técnica -->
                <td class="td-notes alignLeft backtable" style="padding-left: 5px; width: 250px !important;">
                  {{ moonRange.technicalNomenclature }}
                </td>

                <!-- DC KNOWLEDGE - Baseado nos dados do moonRange -->
                <td class="td-notes backtable" *ngFor="let knowledgeItem of moonRange.knowledge" style="width: 90px !important;">
                  {{ knowledgeItem.value }}
                </td>

                <!-- MOD Dano Party -->
                <td class="td-notes subTitle" style="width: 90px !important;">
                  {{ moonRange.modDanoParty }}
                </td>

                <!-- MOD Dano Oponente -->
                <td class="td-notes subTitle" style="width: 90px !important;">
                  {{ moonRange.modDanoOponente }}
                </td>

                <!-- DC ATTRIBUTE - Baseado nos dados do moonRange -->
                <td class="td-notes backtable" *ngFor="let attributeItem of moonRange.attribute" style="width: 90px !important;">
                  {{ attributeItem.value }}
                </td>
              </tr>
            </tbody>
            </table>
          </div>
          </ng-container> 
          <!-- Mensagem quando não há dados -->
          <div *ngIf="listModMoonRanges.length === 0" class="card" style="text-align: center; padding: 20px;">
            <h3>Empty list. Use Excel paste to import data.</h3>
          </div>
        </div>
      </div>
    </div>
  
