import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { LabelColorService } from './label-color.service';
import { RoadBlockService } from './road-block.service';
import { LevelHelperService } from './level-helper.service';
import { DialogueService } from './dialogue.service';
import { LevelService } from './level.service';
import { StoryBoxService } from './story-box.service';
import { RoadBlockType } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard';

/**
 * Service for enhanced label and color management in dialogue trees.
 * Handles label extraction, matching, hover text generation, and dialogue navigation.
 */
@Injectable({
  providedIn: 'root'
})
export class DialogueTreeLabelManagementService {

  constructor(
    private _router: Router,
    private _labelColorService: LabelColorService,
    private _roadBlockService: RoadBlockService,
    private _levelHelperService: LevelHelperService,
    private _dialogueService: DialogueService,
    private _levelService: LevelService,
    private _storyBoxService: StoryBoxService
  ) {}

  /**
   * Get the current dialogue ID from the router
   */
  getCurrentDialogueId(): string {
    const url = this._router.url;
    const urlParts = url.split("/");
    const dialogueId = urlParts[4]; // URL structure: /levels/LEVEL_ID/dialogues/DIALOGUE_ID
    return dialogueId;
  }

  /**
   * Check if the current label has a matching roadblock in the dialogue
   */
  hasRoadblockMatchInDialogue(currentLabel: string): boolean {
    if (!currentLabel) return false;

    const currentDialogueId = this.getCurrentDialogueId();
    if (!currentDialogueId) return false;

    // Extract the dialogue prefix (before the #) for roadblock filtering
    const dialoguePrefix = currentDialogueId.split('#')[0];

    // Get all roadblocks
    const allRoadblocks = this._roadBlockService.models;

    // Filter roadblocks for current dialogue using the prefix
    const dialogueRoadblocks = allRoadblocks.filter(roadblock =>
      roadblock.ID && roadblock.ID.startsWith(dialoguePrefix + '.')
    );

    // Check if any roadblock in this dialogue references the current label
    const hasMatch = dialogueRoadblocks.some(roadblock => {
      if (+roadblock.Type !== +RoadBlockType.SPOKE_IN || !roadblock.spokeElementId) {
        return false;
      }

      const spokeElement = this._levelHelperService.models.find((sp: any) => sp.elementId === roadblock.spokeElementId);
      if (!spokeElement?.text) {
        return false;
      }

      const referencedLabel = this._labelColorService.extractLabelFromSpokeText(spokeElement.text);
      return referencedLabel === currentLabel;
    });

    return hasMatch;
  }

  /**
   * Get the color for the progress condition label with matching logic
   */
  getProgressLabelColor(currentLabel: string): string {
    if (!currentLabel) return '#CCCCCC';

    const hasMatch = this.hasRoadblockMatchInDialogue(currentLabel);
    return this._labelColorService.getColorForLabelWithMatching(currentLabel, hasMatch);
  }

  /**
   * Get the icon color for the key (black for unmatched, white for matched)
   */
  getKeyIconColor(currentLabel: string): string {
    if (!currentLabel) return '#000000';

    const hasMatch = this.hasRoadblockMatchInDialogue(currentLabel);
    return hasMatch ? '#FFFFFF' : '#000000'; // White for matched, black for unmatched
  }

  /**
   * Get the hover text for the progress label key icon
   * Returns the actual label name for interior labels, and adds "Used in" info for exterior labels
   */
  getProgressLabelHoverText(currentLabel: string): string {
    if (!currentLabel) return 'Progress Condition Label';

    // Extract the clean label name without the bracketed content
    // Example: "[ChoiceBox > Answer] MyLabel" becomes "MyLabel"
    const cleanLabel = this.extractCleanLabelName(currentLabel);
    const baseLabelText = cleanLabel || 'Progress Condition Label';

    // Check if this is an exterior label (no match in current dialogue)
    const hasMatch = this.hasRoadblockMatchInDialogue(currentLabel);
    if (!hasMatch) {
      // This is an exterior label - find where it's used
      const usedInDialogues = this.findDialoguesUsingLabel(cleanLabel);
      if (usedInDialogues.length > 0) {
        // Format: Label: "LabelName"\nUsed in:\n1. DialogueA\n2. DialogueB
        let hoverText = `Label: "${baseLabelText}"\nUsed in:\n`;
        usedInDialogues.forEach((dialogue, index) => {
          hoverText += `${index + 1}. ${dialogue}`;
          if (index < usedInDialogues.length - 1) {
            hoverText += '\n';
          }
        });
        return hoverText;
      }
    }

    // Interior label or no usage found - return with Label: format for consistency
    return `Label: "${baseLabelText}"`;
  }

  /**
   * Extract clean label name from formatted label text
   * Removes content inside brackets like "[ChoiceBox > Answer] MyLabel" -> "MyLabel"
   */
  extractCleanLabelName(labelText: string): string {
    if (!labelText) return '';

    // Look for pattern like "] LabelName" and extract the label name
    const match = labelText.match(/\]\s*(.+)$/);
    if (match && match[1]) {
      return match[1].trim();
    }

    // If no brackets found, return the original text (it might already be clean)
    return labelText.trim();
  }

  /**
   * Find all dialogues where a specific label is referenced (used in roadblocks)
   * Returns an array of dialogue display names where the label is referenced
   */
  findDialoguesUsingLabel(targetLabel: string): string[] {
    if (!targetLabel) return [];

    const currentDialogueId = this.getCurrentDialogueId();
    const currentDialoguePrefix = currentDialogueId ? currentDialogueId.split('#')[0] : '';
    const dialoguesWithLabel: Set<string> = new Set();

    // Search through all roadblocks to find where this label is referenced
    this._roadBlockService.models.forEach((roadblock: any) => {
      if (+roadblock.Type !== +RoadBlockType.SPOKE_IN || !roadblock.spokeElementId) {
        return; // Only check SPOKE_IN roadblocks
      }

      // Get the spoke element referenced by this roadblock
      const spokeElement = this._levelHelperService.models.find((sp: any) =>
        sp.elementId === roadblock.spokeElementId
      );

      if (!spokeElement?.text) return;

      // Extract the label from the spoke element text
      const referencedLabel = this._labelColorService.extractLabelFromSpokeText(spokeElement.text);
      if (referencedLabel === targetLabel) {
        // Extract dialogue ID from the roadblock ID (where the roadblock exists)
        // Format: "A8.L1794.D1_PT-BR.something" -> "A8.L1794.D1_PT-BR"
        const roadblockDialogueId = this.extractFullDialogueIdFromElementId(roadblock.ID);

        if (roadblockDialogueId && roadblockDialogueId !== currentDialoguePrefix) {
          // Only include dialogues that are NOT the current dialogue
          const dialogueDisplayName = this.getDialogueDisplayName(roadblockDialogueId);
          if (dialogueDisplayName) {
            dialoguesWithLabel.add(dialogueDisplayName);
          }
        }
      }
    });

    return Array.from(dialoguesWithLabel).sort();
  }

  /**
   * Extract full dialogue ID from element ID
   * e.g., "A8.L1794.D0_PT-BR.OB1265.opt3453" -> "A8.L1794.D0_PT-BR"
   */
  extractFullDialogueIdFromElementId(elementId: string): string | null {
    if (!elementId) return null;

    const parts = elementId.split('.');
    let dialogueIndex = -1;

    // Find the dialogue part (starts with 'D' and contains '_')
    for (let i = 0; i < parts.length; i++) {
      if (parts[i].startsWith('D') && parts[i].includes('_')) {
        dialogueIndex = i;
        break;
      }
    }

    if (dialogueIndex >= 0) {
      // Return everything up to and including the dialogue part
      return parts.slice(0, dialogueIndex + 1).join('.');
    }

    return null;
  }

  /**
   * Get dialogue display name in format: "Level Name" (Type)
   * e.g., "A Mensageira da Beleza Divina" (Init)
   */
  getDialogueDisplayName(fullDialogueId: string): string | null {
    if (!fullDialogueId) return null;

    try {
      // Get the dialogue object
      const dialogue = this._dialogueService.svcFindById(fullDialogueId);
      if (!dialogue) return null;

      // Extract level ID from dialogue ID (remove the dialogue part)
      // e.g., "A8.L1794.D0_PT-BR" -> "A8.L1794"
      const parts = fullDialogueId.split('.');
      const levelId = parts.slice(0, -1).join('.');

      // Get the level object
      const level = this._levelService.svcFindById(levelId);
      if (!level || !level.name) return null;

      // Get dialogue type display name
      const dialogueTypeDisplay = this.getDialogueTypeDisplay(dialogue.type);

      // Return in format: "Level Name" (Type)
      return `"${level.name}" (${dialogueTypeDisplay})`;
    } catch (error) {
      console.warn('Error getting dialogue display name:', error);
      return null;
    }
  }

  /**
   * Get dialogue type display name
   */
  getDialogueTypeDisplay(dialogueType: number): string {
    const typeNames: { [key: number]: string } = {
      0: 'Init',
      1: 'End',
      2: 'Return',
      3: 'Release',
      4: 'Special Return'
    };
    return typeNames[dialogueType] || 'Unknown';
  }

  /**
   * Get the And/Or condition from a dialogue element
   */
  getAndOrCondition(element: any): string {
    if (element?.AndOrCondition) {
      return element.AndOrCondition;
    }
    return 'OR'; // Default fallback
  }

  /**
   * Check if a dialogue element has a progress condition label
   */
  hasProgressLabel(element: any, elementType: string): boolean {
    if (!element) return false;

    switch (elementType) {
      case 'storyBox':
        return !!(element.label);
      case 'optionBox':
        return !!(element.label);
      case 'dilemmaBox':
        return !!(element.label);
      case 'option':
        // For options, check labelOption property on answer box
        return !!(element.labelOption);
      case 'dilemma':
        // For dilemmas, check label property on answer dilemma box
        return !!(element.label);
      default:
        return false;
    }
  }

  /**
   * Get the current dialogue element label
   */
  getCurrentLabel(element: any, elementType: string): string {
    if (!element) return '';

    switch (elementType) {
      case 'storyBox':
        return element.label || '';
      case 'optionBox':
        return element.label || '';
      case 'dilemmaBox':
        return element.label || '';
      case 'option':
        return element.labelOption || '';
      case 'dilemma':
        return element.label || '';
      default:
        return '';
    }
  }

  /**
   * Check if an option has a specific label (either on option or answer box)
   */
  optionHasLabel(option: any, targetLabel: string): boolean {
    // Check option's direct label
    if (option.label && option.label.trim() === targetLabel.trim()) {
      return true;
    }

    // Check answer box label
    if (option.answerBoxId) {
      const answerBox = this._storyBoxService.svcFindById(option.answerBoxId);
      if (answerBox?.labelOption && answerBox.labelOption.trim() === targetLabel.trim()) {
        return true;
      }
    }

    // Check negative answer box label (for dice systems)
    if (option.answerBoxNegativeId) {
      const negativeAnswerBox = this._storyBoxService.svcFindById(option.answerBoxNegativeId);
      if (negativeAnswerBox?.labelOption && negativeAnswerBox.labelOption.trim() === targetLabel.trim()) {
        return true;
      }
    }

    return false;
  }

  /**
   * Check if a dilemma has a specific label (either on dilemma or answer box)
   */
  dilemmaHasLabel(dilemma: any, targetLabel: string): boolean {
    // Check dilemma's direct label
    if (dilemma.label && dilemma.label.trim() === targetLabel.trim()) {
      return true;
    }

    // Check answer box label
    if (dilemma.answerBoxId) {
      const answerBox = this._storyBoxService.svcFindById(dilemma.answerBoxId);
      if (answerBox?.labelOption && answerBox.labelOption.trim() === targetLabel.trim()) {
        return true;
      }
    }

    // Check negative answer box label (for dice systems)
    if (dilemma.answerBoxNegativeId) {
      const negativeAnswerBox = this._storyBoxService.svcFindById(dilemma.answerBoxNegativeId);
      if (negativeAnswerBox?.labelOption && negativeAnswerBox.labelOption.trim() === targetLabel.trim()) {
        return true;
      }
    }

    return false;
  }
}
