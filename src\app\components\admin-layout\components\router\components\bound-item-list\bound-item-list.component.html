<div class="background-div" (mousedown)="handleOutsideMouseClick($event)">
    <!-- This is a popup that show some info from the LEVEL column of the table. -->
    <!-- ############################# START: Level Column ################################ -->
     <ng-container *ngIf="popupReport">
        <div class="modal-backdrop" *ngIf="popupReport"></div> 
        <div @popup class="popup-report"  style="background-color: transparent;">   
            <div class="total-modal">
                <div class="c-title">
                    <div style="padding-left: 15px; width: 95%; text-align: left;">
                        <i class="i-txt" style="font-size: 35px;">{{popupItem.name}}</i>
                    </div>
                    <div class="modal-close margin-inline-start: auto;">
                        <button (click)="closeLevelReferencePopup()" class="btn btn-danger btn-fill btn-remove modal-btn-close">
                            <i class="pe-7s-close i-icon"></i>
                        </button>
                    </div>
                    <hr>
                </div>
    
                <ng-container *ngIf="(popupItem | review).givenAt.length > 0 || (popupItem | review).receivedAt.length > 0 || (popupItem | review).tradedAt.length > 0; else noClasItem">
                    <div class="total-content">                    
                    <ng-container  *ngIf="(popupItem | review).givenAt.length > 0">
                        <div class="c-content-item">
                            <div>
                                <p class="ptextItem">Gave an item
                                    <i class="pe-7s-right-arrow error"></i>
                                    {{ (popupItem | review).givenAt.length }}
                                </p>
                            </div>
                            <div class="scrollable-div">
                                <div *ngFor="let givenAt of (popupItem | review).givenAt" style="text-align: center; font-size: 18px;">
                                    {{
                                        [givenAt] | location
                                    }}
                                </div>
                            </div>
                        </div>
                    
                    </ng-container>
                    <ng-container *ngIf="(popupItem | review).receivedAt.length > 0">
                        <div class="c-content-item"> 
                        <div>
                            <p class="ptextItem">Received an item
                                <i class="pe-7s-left-arrow success"></i>
                                {{ (popupItem | review).receivedAt.length }}
                            </p>
                        </div>
                        <div class="scrollable-div">
                            <div *ngFor="let receivedAt of (popupItem | review).receivedAt" style="text-align: center; font-size: 18px;">
                                {{
                                    [receivedAt] | location
                                }}
                            </div>
                        </div>
                    </div>
                    </ng-container>
                    <ng-container *ngIf="(popupItem | review).tradedAt.length > 0">
                        <div class="c-content-item"> 
                        <div>
                            <p class="ptextItem">Traded an item
                                <i class="pe-7s-star success"></i>
                                {{ (popupItem | review).tradedAt.length }}
                            </p>
                        </div>
                        <div class="scrollable-div">
                            <div *ngFor="let tradedAt of (popupItem | review).tradedAt" style="text-align: center; font-size: 18px;">
                                {{
                                    [tradedAt] | location
                                }}
                            </div>
                        </div>
                    </div>
                    </ng-container>
                </div>
                </ng-container>
                
                <ng-template #noClasItem>
                    <div style="margin-top: 30px; margin-bottom: 20px;">
                        <p >
                        <i class="pe-7s-attention attention"></i>
                        No related content.
                        </p>
                    </div>                
                </ng-template>          
            </div>      
        </div>
     </ng-container>

    <!-- ###########################  END: Level Column  ################################## -->

    <!-- ############################ START: Items List ################################# -->
    <div class="main-content">
        <div @fadeIn class="container-fluid" *ngIf="!showItemList">
            <div class="list-header-row update">
                <div class="card">
                    <app-header-with-buttons
                        [cardTitle]="generatedListName"
                        [rightButtonTemplates]="[exportExcelButtonTemplate, createItemButtonTemplate]"
                        [isBackButtonEnabled]="true" (cardBackButtonClick)="redirectToItemClasses()">
                    </app-header-with-buttons>
                    <app-header-search (inputKeyup)="filter($event)" (searchOptions)="lstOnChangeFilterOptions($event)">                        
                    </app-header-search>
                </div>
            </div>
            <!--List-->
            <div class="card">
                <table class="table table-list">
                    <thead class="sticky">
                    <tr>
                        <th class="col_index">Index</th>
                        <th class="th-clickable" (click)="sortListById(itemClass.itemIds)">
                            ID
                        </th>
                        <th 
                            class="th-clickable" 
                            (click)="sortListByAssigned(itemClass.itemIds)">
                            Assigned
                        </th>
                        <th 
                            class="th-clickable padGating" (click)="sortListByXP(itemClass.itemIds)"
                            *ngIf="custom?.gateXPclassItem == itemClass?.id">
                            Gating-XP
                        </th>
                        <th 
                            [ngClass]="lstFilterValue['areaId'] == 'ALL' ?  'th-clickable' : ''"
                            (click)="sortListByArea(itemClass.itemIds)">
                            Area
                        <!--Area filter dropdown-->
                        <select class="dropdown filter-dropdown limited center"
                                name="areaIdFilter"
                                [(ngModel)]="lstFilterValue['areaId']"
                                (change)="lstOnChangeFilter()">
                            <option value="ALL">All</option>
                            <option *ngFor="let area of preloadedAreas"
                                    [value]="area.id">
                                {{ area.hierarchyCode }}: {{ area.name }}
                            </option>
                        </select>
                        </th>
                        <th class="th-clickable" (click)="sortListByName(itemClass.itemIds)">
                            Name & Description
                            <div class="ball-circle"></div>
                        </th>
                        <th class="th-clickable" (click)="sortListByTags(itemClass.itemIds)">
                            Tags
                        </th>
                        <th class="col_level">
                            Level                        
                        </th>
                        <th class="col_action">Actions</th>
                    </tr>
                    </thead>
                    <tbody *ngIf="itemClass">
                        <ng-container *ngFor="let item of filterList(itemClass.itemIds) | itemsNoSort | filteredItem : undefined; let i = index;">
                            <tr class="" [class.odd]="i % 2" id="{{ item.id }}">
                                <td class="td-sort">{{ i + 1 }}</td>
                                <td class="td-id">{{ item.id }}</td>
                                <td>
                                    <ng-container *ngIf="(item | review).assignedAt.length === 0 || 
                                        (item | review).receivedAt.length === 0;else assigned">
                                        <i *ngIf="(item | review).givenAt.length == 0"
                                            id="checkAssigned" class="pe-7s-attention warning"
                                            tooltip="{{item | checkIfAssigned }}"
                                            placement="top" delay="100">
                                        </i>
                                        <img tooltip="Item gived but never received" *ngIf="(item | review).givenAt.length !== 0" 
                                            class="requested-not-assigned" src="assets/img/icons/warning.png">
                                    </ng-container>
                                    <ng-template #assigned>
                                        <i class="pe-7s-check success"></i>
                                    </ng-template>
                                </td>
                                <td *ngIf="custom?.gateXPclassItem == itemClass.id">
                                    <input class="form-control form-short" type="number"
                                        value="{{ item.gateXP }}" style="padding-left: 5px !important;"
                                        #xp
                                        (change)="changeGateXp(item, +xp.value)" />
                                </td>
                                <td class="td-8">
                                    <table class="table-responsive pipe-table" style="width: 100px;">
                                        <tbody>
                                            <tr *ngFor="let assignedAt of (item | review).assignedAt">
                                            {{
                                                (assignedAt | area : true : true)?.name
                                            }}
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                                <td class="td-highlight td-50">
                                    <input 
                                        class="form-control form-title large form-short borderless"
                                        [class.rpg-color-3]="item.type == ItemType.CURRENCY"
                                        [class.rpg-color-0]="item.type != ItemType.CURRENCY" #itemName
                                        value="{{ (item | translation : lstLanguage : item.id : 'name') }}"                                     
                                        (change)="changeItemName(item, itemName.value)" />
                                    <textarea 
                                        class="form-control form-long form-100"
                                        value="{{ (item | translation : lstLanguage : item.id : 'description') }}"
                                        #itemDescription
                                        (change)="descriptionChange(item, 'description', itemDescription.value)">
                                    </textarea>
                                </td>             
                                <td class="td-auto">
                                    <button class="btn btn-primary btn-fill" *ngFor="let tag of item.tagIds; let i = index"
                                        (click)="changeTag(item, '', i)" 
                                        tooltip="Create a new tag"[ngStyle]="{
                                        'background-color': (((tag | tag) | async) | information: {hex: '#828282'})?.hex,
                                        'border-color': (((tag | tag) | async) | information: {hex: '#828282'})?.hex
                                        }">{{ ((tag | tag) | async)?.name }}
                                    </button>
                                    <button class="btn btn-success btn-fill" (click)="addTagWithPopup(item)"><i
                                        class="pe-7s-plus"></i>
                                    </button>               
                                </td>
                                <td>
                                    <button (click)="levelReferencePopup(item)"><i class="pe-7s-more"></i></button>              
                                </td>
                                <td class="td-actions td-small">
                                    <button class="btn btn-danger btn-fill btn-remove" (click)="removeItemId(item)">
                                        <i class="pe-7s-close"></i>
                                    </button>
                                    <button class="btn btn-primary btn-fill btn-remove" (click)="toPromptMoveItem(item)"
                                        tooltip="Move item to other class" placement="top" delay="100">
                                        <i class="pe-7s-next-2"></i>
                                    </button>
                                </td>
                            </tr>
                        </ng-container>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <!-- ############################# END: Items List ################################ -->
</div>