/* Baldur's Gate 3 Style Dice Overlay - Full screen modal interface */

/* Main overlay container covering entire viewport */
.dice-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  /* Dark radial gradient background for dramatic effect */
  background: radial-gradient(ellipse at center, rgba(20, 10, 40, 0.9) 0%, rgba(0, 0, 0, 0.95) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99999; /* Highest z-index to overlay everything */
  animation: fadeIn 0.5s ease-in;
  cursor: default; /* Default cursor for overlay background */
}

/* Central frame containing the dice interface */
.dice-frame {
  position: relative;
  /* Fantasy-themed brown gradient background */
  background: linear-gradient(145deg, rgba(40, 30, 20, 0.95), rgba(20, 15, 10, 0.98));
  border-radius: 20px;
  padding: 40px;
  min-width: 450px;
  min-height: 500px;
  /* Glowing brown shadow effects for depth */
  box-shadow:
    0 0 50px rgba(139, 69, 19, 0.3),
    inset 0 0 30px rgba(139, 69, 19, 0.1);
  animation: slideIn 0.6s ease-out;
}

/* Decorative border frame system */
.frame-border {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none; /* Allow clicks to pass through */
}

/* Ornate corner decorations */
.frame-corner {
  position: absolute;
  width: 40px;
  height: 40px;
  background: linear-gradient(45deg, #8B4513, #CD853F);
  border-radius: 50%;

  /* Position corners at frame edges */
  &.top-left { top: -10px; left: -10px; }
  &.top-right { top: -10px; right: -10px; }
  &.bottom-left { bottom: -10px; left: -10px; }
  &.bottom-right { bottom: -10px; right: -10px; }
}

.frame-edge {
  position: absolute;
  background: linear-gradient(90deg, #8B4513, #CD853F, #8B4513);

  &.top, &.bottom {
    height: 3px;
    left: 30px;
    right: 30px;
  }

  &.left, &.right {
    width: 3px;
    top: 30px;
    bottom: 30px;
  }

  &.top { top: 0; }
  &.bottom { bottom: 0; }
  &.left { left: 0; }
  &.right { right: 0; }
}

.dice-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  color: #F5DEB3;
  text-align: center;
}

/* Header Section */
.dice-header {
  margin-bottom: 30px;
}

.difficulty-label {
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 2px;
  color: #CD853F;
  margin-bottom: 8px;
  text-transform: uppercase;
}

.dc-number {
  font-size: 48px;
  font-weight: bold;
  color: #F5DEB3;
  text-shadow: 0 0 20px rgba(245, 222, 179, 0.5);
  font-family: 'Cinzel', serif;
}

/* Dice Area */
.dice-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 20px 0;
}

.dice-container {
  perspective: 1500px;
  margin-bottom: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}

// 3D D20 Dice Variables - adapted from CodePen
$containerWidth: 160px; // Increased size
$containerHeight: $containerWidth;

$faceWidth: $containerWidth * 0.5;
$faceHeight: $faceWidth * 0.86; // Height of equilateral triangle

// D20 geometry angles - from CodePen
/* 3D Dice Geometry Variables - Mathematical constants for icosahedral d20 */
$angle: 53deg; /* Primary angle for icosahedral geometry */
$ringAngle: -11deg; /* Ring rotation angle */
$sideAngle: 360deg / 5; /* 72deg - Pentagon angle for 5-sided rings */

/* Calculated 3D positioning values - derived from icosahedral mathematics */
$rotateX: -$angle;
$translateZ: $faceWidth * 0.335; /* Z-depth for face positioning */
$translateY: -$faceHeight * 0.15; /* Y-offset for vertical alignment */
$translateRingZ: $faceWidth * 0.75; /* Z-depth for middle ring faces */
$translateRingY: $faceHeight * 0.78 + $translateY; /* Y-position for middle ring */
$translateLowerZ: $translateZ; /* Z-depth for bottom ring faces */
$translateLowerY: $faceHeight * 0.78 + $translateRingY; /* Y-position for bottom ring */

/* Main 3D dice container with interaction states */
.dice-3d {
  position: relative;
  width: $containerWidth;
  height: $containerHeight;
  transform-style: preserve-3d; /* Enable 3D transformations */
  cursor: default;
  transition: transform 0.8s ease-out;

  /* Show pointer cursor only when dice is clickable (before rolling) */
  &:not([data-face]):not(.rolling) {
    cursor: pointer;
  }

  /* Reset CSS counter for face numbering */
  counter-reset: face-counter;

  /* Default 3D orientation showing dice at optimal viewing angle */
  transform: rotateX($rotateX);

  /* Hover effect: slight scale up when dice is interactive */
  &:hover:not(.rolling):not([data-face]) {
    transform: rotateX($rotateX) scale(1.05);
  }

  /* Rolling animation state - uses dynamic CSS variables for target rotation */
  &.rolling {
    animation: diceRollToTarget 1.2s ease-out forwards;
  }
}

/* Individual D20 Face Geometry - Creates triangular faces using CSS borders */
.face {
  $horizontalMargin: -$faceWidth * 0.5;

  position: absolute;
  left: 50%;
  top: 0;
  margin: 0 $horizontalMargin;

  /* Create triangular face using CSS border technique */
  /* This creates an equilateral triangle pointing downward */
  width: 0;
  height: 0;
  border-left: #{$faceWidth * 0.5} solid transparent;
  border-right: #{$faceWidth * 0.5} solid transparent;
  border-bottom: $faceHeight solid #B8B8B8; /* Metallic silver base color */

  transform-style: preserve-3d;
  backface-visibility: hidden; /* Hide faces when rotated away from viewer */

  // Enhanced 3D effect with multiple shadows and highlights
  filter:
    drop-shadow(0 0 1px rgba(0, 0, 0, 0.8))
    drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.4))
    drop-shadow(-1px -1px 1px rgba(255, 255, 255, 0.3));

  // Add subtle animation on hover
  transition: all 0.2s ease;

  // Face number counter
  counter-increment: face-counter;

  &::before {
    content: counter(face-counter);
    position: absolute;
    top: #{$faceHeight * 0.25};
    left: -$faceWidth;
    width: #{$faceWidth * 2};
    height: $faceHeight;

    color: #2C1810; // Dark brown for better contrast
    font-size: #{$faceHeight * 0.45}; // Slightly smaller for better fit
    font-weight: bold;
    font-family: 'Cinzel', 'Times New Roman', serif; // Elegant serif font
    text-align: center;
    line-height: #{$faceHeight * 0.9};
    text-shadow:
      1px 1px 2px rgba(255, 255, 255, 0.8),
      0 0 3px rgba(255, 255, 255, 0.5),
      1px 1px 1px rgba(0, 0, 0, 0.3);
    user-select: none;
    pointer-events: none;
    z-index: 10; // Ensure numbers appear above face styling
  }

  // Top pentagon ring (faces 1-5) - from CodePen
  &:nth-child(1) { transform: rotateY(-$sideAngle * 0) translateZ($translateZ) translateY($translateY) rotateX($angle); }
  &:nth-child(2) { transform: rotateY(-$sideAngle * 1) translateZ($translateZ) translateY($translateY) rotateX($angle); }
  &:nth-child(3) { transform: rotateY(-$sideAngle * 2) translateZ($translateZ) translateY($translateY) rotateX($angle); }
  &:nth-child(4) { transform: rotateY(-$sideAngle * 3) translateZ($translateZ) translateY($translateY) rotateX($angle); }
  &:nth-child(5) { transform: rotateY(-$sideAngle * 4) translateZ($translateZ) translateY($translateY) rotateX($angle); }

  // Upper middle ring (faces 6-10) - from CodePen
  &:nth-child(6) { transform: rotateY(-$sideAngle * -5) translateZ($translateRingZ) translateY($translateRingY) rotateZ(180deg) rotateX($ringAngle); }
  &:nth-child(7) { transform: rotateY(-$sideAngle * -4) translateZ($translateRingZ) translateY($translateRingY) rotateZ(180deg) rotateX($ringAngle); }
  &:nth-child(8) { transform: rotateY(-$sideAngle * -3) translateZ($translateRingZ) translateY($translateRingY) rotateZ(180deg) rotateX($ringAngle); }
  &:nth-child(9) { transform: rotateY(-$sideAngle * -2) translateZ($translateRingZ) translateY($translateRingY) rotateZ(180deg) rotateX($ringAngle); }
  &:nth-child(10) { transform: rotateY(-$sideAngle * -1) translateZ($translateRingZ) translateY($translateRingY) rotateZ(180deg) rotateX($ringAngle); }

  // Lower middle ring (faces 11-15) - from CodePen
  &:nth-child(11) { transform: rotateY($sideAngle * 0 + $sideAngle/2) translateZ($translateRingZ) translateY($translateRingY) rotateX($ringAngle); }
  &:nth-child(12) { transform: rotateY($sideAngle * 1 + $sideAngle/2) translateZ($translateRingZ) translateY($translateRingY) rotateX($ringAngle); }
  &:nth-child(13) { transform: rotateY($sideAngle * 2 + $sideAngle/2) translateZ($translateRingZ) translateY($translateRingY) rotateX($ringAngle); }
  &:nth-child(14) { transform: rotateY($sideAngle * 3 + $sideAngle/2) translateZ($translateRingZ) translateY($translateRingY) rotateX($ringAngle); }
  &:nth-child(15) { transform: rotateY($sideAngle * 4 + $sideAngle/2) translateZ($translateRingZ) translateY($translateRingY) rotateX($ringAngle); }

  // Bottom pentagon ring (faces 16-20) - from CodePen
  &:nth-child(16) { transform: rotateY($sideAngle * -2 + $sideAngle/2) translateZ($translateLowerZ) translateY($translateLowerY) rotateZ(180deg) rotateX($angle); }
  &:nth-child(17) { transform: rotateY($sideAngle * -1 + $sideAngle/2) translateZ($translateLowerZ) translateY($translateLowerY) rotateZ(180deg) rotateX($angle); }
  &:nth-child(18) { transform: rotateY($sideAngle * 0 + $sideAngle/2) translateZ($translateLowerZ) translateY($translateLowerY) rotateZ(180deg) rotateX($angle); }
  &:nth-child(19) { transform: rotateY($sideAngle * 1 + $sideAngle/2) translateZ($translateLowerZ) translateY($translateLowerY) rotateZ(180deg) rotateX($angle); }
  &:nth-child(20) { transform: rotateY($sideAngle * 2 + $sideAngle/2) translateZ($translateLowerZ) translateY($translateLowerY) rotateZ(180deg) rotateX($angle); }

  // Different shading for different face groups to enhance 3D effect
  // Top faces (1-5) - lighter (facing up toward light)
  &:nth-child(1), &:nth-child(2), &:nth-child(3), &:nth-child(4), &:nth-child(5) {
    border-bottom-color: #D0D0D0; // Lighter silver
  }

  // Upper middle faces (6-10) - medium shade
  &:nth-child(6), &:nth-child(7), &:nth-child(8), &:nth-child(9), &:nth-child(10) {
    border-bottom-color: #B8B8B8; // Medium silver
  }

  // Lower middle faces (11-15) - medium shade
  &:nth-child(11), &:nth-child(12), &:nth-child(13), &:nth-child(14), &:nth-child(15) {
    border-bottom-color: #A8A8A8; // Slightly darker
  }

  // Bottom faces (16-20) - darker (facing away from light)
  &:nth-child(16), &:nth-child(17), &:nth-child(18), &:nth-child(19), &:nth-child(20) {
    border-bottom-color: #909090; // Darker silver
  }
}

/* 3D Rolling Animation - Smooth tumbling motion with optimized keyframes */
@keyframes diceRollToTarget {
  0% {
    transform: rotateX(-15deg) rotateY(15deg) rotateZ(0deg);
  }
  6% {
    transform: rotateX(var(--spin-x-1, 80deg)) rotateY(var(--spin-y-1, 120deg)) rotateZ(var(--spin-z-1, 60deg));
  }
  13% {
    transform: rotateX(var(--spin-x-2, 180deg)) rotateY(var(--spin-y-2, 240deg)) rotateZ(var(--spin-z-2, 140deg));
  }
  20% {
    transform: rotateX(var(--spin-x-3, 140deg)) rotateY(var(--spin-y-3, 320deg)) rotateZ(var(--spin-z-3, 200deg));
  }
  26% {
    transform: rotateX(var(--spin-x-4, 280deg)) rotateY(var(--spin-y-4, 180deg)) rotateZ(var(--spin-z-4, 120deg));
  }
  33% {
    transform: rotateX(var(--spin-x-5, 220deg)) rotateY(var(--spin-y-5, 400deg)) rotateZ(var(--spin-z-5, 280deg));
  }
  40% {
    transform: rotateX(var(--spin-x-6, 360deg)) rotateY(var(--spin-y-6, 280deg)) rotateZ(var(--spin-z-6, 180deg));
  }
  46% {
    transform: rotateX(var(--spin-x-7, 320deg)) rotateY(var(--spin-y-7, 480deg)) rotateZ(var(--spin-z-7, 340deg));
  }
  53% {
    transform: rotateX(var(--spin-x-8, 460deg)) rotateY(var(--spin-y-8, 360deg)) rotateZ(var(--spin-z-8, 240deg));
  }
  60% {
    transform: rotateX(var(--spin-x-9, 400deg)) rotateY(var(--spin-y-9, 560deg)) rotateZ(var(--spin-z-9, 400deg));
  }
  66% {
    transform: rotateX(var(--spin-x-10, 540deg)) rotateY(var(--spin-y-10, 440deg)) rotateZ(var(--spin-z-10, 300deg));
  }
  73% {
    transform: rotateX(var(--spin-x-11, 480deg)) rotateY(var(--spin-y-11, 640deg)) rotateZ(var(--spin-z-11, 460deg));
  }
  80% {
    transform: rotateX(var(--spin-x-12, 620deg)) rotateY(var(--spin-y-12, 520deg)) rotateZ(var(--spin-z-12, 360deg));
  }
  86% {
    transform: rotateX(var(--spin-x-13, 560deg)) rotateY(var(--spin-y-13, 720deg)) rotateZ(var(--spin-z-13, 520deg));
  }
  93% {
    transform: rotateX(var(--spin-x-14, 700deg)) rotateY(var(--spin-y-14, 600deg)) rotateZ(var(--spin-z-14, 420deg));
  }
  97% {
    transform: rotateX(var(--spin-x-15, 640deg)) rotateY(var(--spin-y-15, 800deg)) rotateZ(var(--spin-z-15, 580deg));
  }
  100% {
    transform: rotateX(var(--target-x, 0deg)) rotateY(var(--target-y, 0deg)) rotateZ(var(--target-z, 0deg));
  }
}

/* Face Display Logic - Position dice to show specific face - from CodePen */
.dice-3d {
  // Top pentagon ring (faces 1-5)
  &[data-face="1"] { transform: rotateX(-$angle) rotateY($sideAngle * 0); }
  &[data-face="2"] { transform: rotateX(-$angle) rotateY($sideAngle * 1); }
  &[data-face="3"] { transform: rotateX(-$angle) rotateY($sideAngle * 2); }
  &[data-face="4"] { transform: rotateX(-$angle) rotateY($sideAngle * 3); }
  &[data-face="5"] { transform: rotateX(-$angle) rotateY($sideAngle * 4); }

  // Bottom pentagon ring (faces 16-20)
  &[data-face="16"] { transform: rotateX(-$angle + 180deg) rotateY(-$sideAngle * 1); }
  &[data-face="17"] { transform: rotateX(-$angle + 180deg) rotateY(-$sideAngle * 2); }
  &[data-face="18"] { transform: rotateX(-$angle + 180deg) rotateY(-$sideAngle * 3); }
  &[data-face="19"] { transform: rotateX(-$angle + 180deg) rotateY(-$sideAngle * 4); }
  &[data-face="20"] { transform: rotateX(-$angle + 180deg) rotateY(-$sideAngle * 0); }

  // Upper middle ring (faces 6-10)
  &[data-face="6"] { transform: rotateX(-$ringAngle) rotateZ(180deg) rotateY($sideAngle * 0); }
  &[data-face="7"] { transform: rotateX(-$ringAngle) rotateZ(180deg) rotateY($sideAngle * 1); }
  &[data-face="8"] { transform: rotateX(-$ringAngle) rotateZ(180deg) rotateY($sideAngle * 2); }
  &[data-face="9"] { transform: rotateX(-$ringAngle) rotateZ(180deg) rotateY($sideAngle * 3); }
  &[data-face="10"] { transform: rotateX(-$ringAngle) rotateZ(180deg) rotateY($sideAngle * 4); }

  // Lower middle ring (faces 11-15)
  &[data-face="11"] { transform: rotateX(-$ringAngle) rotateY(-$sideAngle * 0 - $sideAngle/2); }
  &[data-face="12"] { transform: rotateX(-$ringAngle) rotateY(-$sideAngle * 1 - $sideAngle/2); }
  &[data-face="13"] { transform: rotateX(-$ringAngle) rotateY(-$sideAngle * 2 - $sideAngle/2); }
  &[data-face="14"] { transform: rotateX(-$ringAngle) rotateY(-$sideAngle * 3 - $sideAngle/2); }
  &[data-face="15"] { transform: rotateX(-$ringAngle) rotateY(-$sideAngle * 4 - $sideAngle/2); }
}

/* Dice Label */
.dice-label {
  font-size: 16px;
  color: #CD853F;
  font-style: italic;
  margin-top: 20px;
  text-align: center;
  animation: pulse 2s infinite;

  /* Box styling similar to the reference image */
  background: rgba(0, 0, 0, 0.7);
  border: 2px solid #8B4513;
  border-radius: 8px;
  padding: 8px 16px;
  display: inline-block;
  box-shadow:
    0 0 10px rgba(139, 69, 19, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);

  /* Subtle gradient for depth */
  background: linear-gradient(145deg,
    rgba(0, 0, 0, 0.8),
    rgba(20, 20, 20, 0.9)
  );
}

/* Roll Result Display */
.roll-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  margin-top: 20px;
  animation: resultSlideIn 0.8s ease-out;
}

.base-roll {
  font-size: 36px;
  font-weight: bold;
  color: #F5DEB3;
  text-shadow: 0 0 15px rgba(245, 222, 179, 0.6);
  padding: 10px 20px;
  border: 2px solid #CD853F;
  border-radius: 10px;
  background: rgba(139, 69, 19, 0.3);
}

.modifiers {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.modifier {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  color: #98FB98;

  .modifier-sign {
    font-weight: bold;
    color: #90EE90;
  }

  .modifier-value {
    font-weight: bold;
    color: #90EE90;
  }

  .modifier-label {
    font-size: 14px;
    color: #DDD;
    font-style: italic;
  }
}

.total-result {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 28px;
  font-weight: bold;
  margin-top: 10px;

  .equals {
    color: #CD853F;
  }

  .total {
    color: #F5DEB3;
    text-shadow: 0 0 20px rgba(245, 222, 179, 0.8);
    padding: 8px 16px;
    border: 2px solid #CD853F;
    border-radius: 8px;
    background: rgba(139, 69, 19, 0.4);
  }
}

/* Action Section */
.dice-action {
  margin-top: auto;
  padding-top: 20px;
}

.dice-button {
  background: linear-gradient(145deg, #8B4513, #CD853F);
  border: 2px solid #D2691E;
  border-radius: 12px;
  color: #F5DEB3;
  font-size: 18px;
  font-weight: 600;
  padding: 15px 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 4px 15px rgba(139, 69, 19, 0.4);

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(139, 69, 19, 0.6);
    background: linear-gradient(145deg, #A0522D, #DEB887);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(139, 69, 19, 0.4);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.result-display {
  text-align: center;
  animation: resultFadeIn 1s ease-out;
}

.outcome {
  font-size: 32px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 3px;
  margin-bottom: 15px;
  text-shadow: 0 0 20px currentColor;

  &.success {
    color: #32CD32;
    animation: successGlow 2s ease-in-out infinite alternate;
  }

  &.failure {
    color: #DC143C;
    animation: failureGlow 2s ease-in-out infinite alternate;
  }
}

.continue-prompt {
  font-size: 16px;
  color: #CD853F;
  font-style: italic;
  animation: pulse 2s infinite;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    transform: translateY(-50px) scale(0.8);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

/* Additional visual enhancements for the 3D dice */
.dice-3d:hover:not(.rolling):not([data-face]) {
  .face {
    // Enhanced metallic shine on hover (only when not rolled yet)
    filter:
      drop-shadow(0 0 2px rgba(255, 255, 255, 0.6))
      drop-shadow(1px 1px 3px rgba(0, 0, 0, 0.5))
      drop-shadow(-1px -1px 2px rgba(255, 255, 255, 0.4));

    // Slightly brighter faces on hover
    &:nth-child(1), &:nth-child(2), &:nth-child(3), &:nth-child(4), &:nth-child(5) {
      border-bottom-color: #E0E0E0;
    }
    &:nth-child(6), &:nth-child(7), &:nth-child(8), &:nth-child(9), &:nth-child(10) {
      border-bottom-color: #C8C8C8;
    }
    &:nth-child(11), &:nth-child(12), &:nth-child(13), &:nth-child(14), &:nth-child(15) {
      border-bottom-color: #B8B8B8;
    }
    &:nth-child(16), &:nth-child(17), &:nth-child(18), &:nth-child(19), &:nth-child(20) {
      border-bottom-color: #A0A0A0;
    }
  }
}

@keyframes resultSlideIn {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes resultFadeIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes successGlow {
  from { text-shadow: 0 0 20px #32CD32; }
  to { text-shadow: 0 0 30px #32CD32, 0 0 40px #32CD32; }
}

@keyframes failureGlow {
  from { text-shadow: 0 0 20px #DC143C; }
  to { text-shadow: 0 0 30px #DC143C, 0 0 40px #DC143C; }
}

@keyframes pulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* Responsive Design */
@media (max-width: 768px) {
  .dice-frame {
    min-width: 350px;
    min-height: 400px;
    padding: 30px;
  }

  .dc-number {
    font-size: 36px;
  }

  // Responsive 3D dice sizing
  $mobileDiceSize: 100px;
  $mobileFaceSize: $mobileDiceSize * 0.5;
  $mobileFaceHeight: $mobileFaceSize * 0.86;

  .dice-3d {
    width: $mobileDiceSize;
    height: $mobileDiceSize;

    .face {
      margin-left: -#{$mobileFaceSize * 0.5};
      margin-top: -#{$mobileFaceHeight * 0.5};
      border-left-width: #{$mobileFaceSize * 0.5};
      border-right-width: #{$mobileFaceSize * 0.5};
      border-bottom-width: $mobileFaceHeight;

      &::before {
        font-size: #{$mobileFaceHeight * 0.3};
        top: #{$mobileFaceHeight * 0.3};
        left: -#{$mobileFaceSize * 0.5};
        width: $mobileFaceSize;
        height: #{$mobileFaceHeight * 0.4};
        line-height: #{$mobileFaceHeight * 0.4};
      }
    }
  }

  .base-roll {
    font-size: 28px;
  }

  .outcome {
    font-size: 24px;
  }
}
