import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { Area, Box, Character, Dialogue, DilemmaBox, Level, Mission, OptionBox, StoryBox } from 'src/app/lib/@bus-tier/models';
import { RoadBlock } from 'src/app/lib/@bus-tier/models/RoadBlock';
import { Typing } from 'src/app/lib/@pres-tier';
import { Button } from 'src/app/lib/@pres-tier/data';
import { AnswerDilemmaBoxService, ClassService, ConfigThresholdService, DilemmaBoxService, DilemmaService, LevelHelperService, TutorialService, VideoService } from 'src/app/services';
import { CharacterService } from 'src/app/services/character.service';
import { DialogueService } from 'src/app/services/dialogue.service';
import { EmotionService } from 'src/app/services/emotion.service';
import { EventService } from 'src/app/services/event.service';
import { ItemService } from 'src/app/services/item.service';
import { LanguageService } from 'src/app/services/language.service';
import { LevelService } from 'src/app/services/level.service';
import { MarkerService } from 'src/app/services/marker.service';
import { MicroloopContainerService } from 'src/app/services/microloop-container.service';
import { MicroloopService } from 'src/app/services/microloop.service';
import { MissionService } from 'src/app/services/mission.service';
import { ObjectiveService } from 'src/app/services/objective.service';
import { OptionBoxService } from 'src/app/services/option-box.service';
import { OptionService } from 'src/app/services/option.service';
import { RoadBlockService } from 'src/app/services/road-block.service';
import { SearchOnPageService } from 'src/app/services/search-on-page.service';
import { SpeechService } from 'src/app/services/speech.service';
import { StoryBoxService } from 'src/app/services/story-box.service';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Alert } from 'src/lib/darkcloud';
import { EasyMVC } from 'src/lib/darkcloud/angular';
import { operatorString, typeNames } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard';
import { GameTypes, IdPrefixes, MarkerType, OptionBoxType } from 'src/lib/darkcloud/dialogue-system';
import { HighlightElement, sortData } from 'src/lib/others';
import { AtributteService } from '../../../../../../../../services/atributte.service';
import { fadeIn, popup } from '../../../bound-item-list/bound-list.component.animations';
import { SpinnerService } from 'src/app/spinner/spinner.service';
import { ActivatedRoute, Router } from '@angular/router';

export interface Preloadable {
  dialogue: Dialogue;
  level: Level;
  speakers: Character[];
  missions: Mission[];
  preloadedCharacters: Character[];
}

@Component({
  selector: 'app-level-dialogue-editor',
  templateUrl: './level-dialogue-editor.component.html',
  styleUrls: ['./level-dialogue-editor.component.scss'],
  animations: [fadeIn, popup],
  changeDetection: ChangeDetectionStrategy.OnPush,
})

export class LevelDialogueEditorComponent extends EasyMVC.PreloadComponent<Preloadable> implements OnInit, AfterViewInit {
  @ViewChild('scrollableDiv') scrollableDiv: ElementRef;
  public readonly HighlightElement = HighlightElement;
  popupOpen: boolean = false;
  popupSimulatorOpen: boolean = false;
  public preloadedMicroloopContainers: Area[];
  isStgoryBox = false;

  public readonly clipboardButtonTemplate: Button.Templateable =
    {
      title: 'Copy to Clipboard',
      onClick: this.copyDialogueToClipboard.bind(this),
      iconClass: 'pe-7s-paperclip',
      btnClass: Button.Klasses.BLUE,
    };

  public readonly flowTreeButtonTemplate: Button.Templateable =
    {
      title: 'Show a Flow Tree Diagram',
      onClick: this.openTreePopup.bind(this),
      iconClass: 'pe-7s-network',
      btnClass: Button.Klasses.FILL_BRIGHTBLUE,
    };

  public readonly simulatorPopupButtonTemplate: Button.Templateable =
    {
      title: 'Simulates the current Dialogue',
      onClick: this.openSimulatorPopup.bind(this),
      iconClass: 'pe-7s-phone',
      btnClass: Button.Klasses.FILL_ORANGE,
    };

  public readonly headerRightButtonTemplates: Button.Templateable[] =
    [
      {
        title: 'Add Dilemma Box ',
        text: 'Add Dilemma Box',
        onClick: this.toPromptAddDilemmaBox.bind(this),
        iconClass: 'pe-7s-network',
        btnClass: Button.Klasses.FILL_DILEMMA,
      },
      {
        title: 'Add Story Box',
        text: 'Add Story Box',
        onClick: this.toPromptAddStoryBox.bind(this),
        iconClass: 'pe-7s-comment',
        btnClass: Button.Klasses.FILL_STORYBOX,
      },
      {
        title: 'Add Choice Box',
        text: 'Add Choice Box',
        onClick: (() => this.toPromptAddOptionBox(0)).bind(this),
        iconClass: 'pe-7s-network',
        btnClass: Button.Klasses.FILL_CHOICEBOX,
      },
      {
        title: 'Add Investigation Box',
        text: 'Add Investigation Box',
        onClick: (() => this.toPromptAddOptionBox(1)).bind(this),
        iconClass: 'pe-7s-repeat',
        btnClass: Button.Klasses.FILL_INVESTIGATIONBOX,
      },
    ];

  public level: Level;
  public dialogue: Dialogue;
  public preloadedSpeakers: Character[];
  public preloadedMissionsOfArea: Mission[];

  public lazyIds = []
  public loadedIds = 0;
  searchQuery: string;
  isComponentStoryBox = false;
  public get optionBoxType() {
    return OptionBoxType;
  }

  constructor(
    private _microloopService: MicroloopService,
    private _router: Router,
    private _activatedRoute: ActivatedRoute,
    protected _searchOnPageService: SearchOnPageService,
    private _levelService: LevelService,
    private _dialogueService: DialogueService,
    private _optionBoxService: OptionBoxService,
    private _storyBoxService: StoryBoxService,
    private _optionService: OptionService,
    private _speechService: SpeechService,
    private _eventService: EventService,
    private _markerService: MarkerService,
    private _characterService: CharacterService,
    private _videoService: VideoService,
    private _tutorialService: TutorialService,
    private _itemService: ItemService,
    private _emotionService: EmotionService,
    private _missionService: MissionService,
    private _objectiveService: ObjectiveService,
    private _userSettingsService: UserSettingsService,
    private _change: ChangeDetectorRef,
    private _classService: ClassService,
    private _dilemmaBoxService: DilemmaBoxService,
    private _dilemmaService: DilemmaService,
    private _atributteService: AtributteService,
    private _answerDilemmaBoxService: AnswerDilemmaBoxService,
    private _microloopContainerService: MicroloopContainerService,
    private _configThresholdService: ConfigThresholdService,
    private spinnerService: SpinnerService,
    private _levelHelperService: LevelHelperService,
    private _roadblockService?: RoadBlockService,
    private _languageService?: LanguageService,
  ) {
    super(_activatedRoute);
  }

  public language: string = 'PT-BR';
  trackByIndex(index: number, item: any): any {
    return index;
  }
  trackById(index: number, box: Box): any {
    return box.id;
  }

  public ngOnInit(): void {   
    this._levelHelperService.toFinishLoading();
    this.language = this._languageService.activeLanguage.name;
    this.dialogue = this._dialogueService.svcFindById(this._router.url.split('/')[4].split('#')[0]);
    this.level = this._levelService.svcFindById(this._router.url.split("/")[2]);
    //When we are comming from the microloop
    if (!this.level) {
      this.level = this._microloopService.svcFindById(this._router.url.split('/')[2].split('.')[0] +
        "." + this._router.url.split('/')[2].split('.')[1]);
    }
    this.preloadedSpeakers = this.preloadedData.speakers;
    this.preloadedMissionsOfArea = this.preloadedData.missions;
    this._dialogueService.lastModifiedId = this.dialogue?.id;
    this._levelService.lastModifiedId = this.level.id;

    if (this.dialogue.boxIds.length > 5) {
      this.lazyIds = this.dialogue.boxIds.slice(0, 5);
      this.loadedIds = 5;
      this.loadMoreDialogues()
      this.loadDialogues();
    }
    else {    
      this.lazyIds = this.dialogue.boxIds;
      this.loadedIds = this.dialogue.boxIds.length;
    }
    this.clearSearchQuery();
  }

  filterContent() {
    this._searchOnPageService.setSearchQuery(this.searchQuery);
  }
  scrollToNextMatch() {
    this._searchOnPageService.scrollToNextElement();
  }
  scrollToPreviousMatch() {
    this._searchOnPageService.scrollToPreviousElement();
  }
  clearSearchQuery() {
    this._searchOnPageService.clearHighlight();
    this.searchQuery = '';
    this.filterContent();
    this._searchOnPageService.setContentElements();
  }


  forceRefresh() {
    this._change.detectChanges();
  }

  identify(index, item) {
    return item?.id;
  }

  public loadMoreDialogues() {
    this.loadDialogues()
    setTimeout(() => {
      this.loadDialogues();
    }, 800)
  }

  private loadDialogues() {
    let increment = 5;
    this.loadedIds += increment;
    if (this.loadedIds >= this.dialogue.boxIds.length) {
      this.loadedIds = this.dialogue.boxIds.length;
      this.lazyIds = this.dialogue.boxIds.slice(0, this.loadedIds);
    }
    else {
      this.lazyIds = this.dialogue.boxIds.slice(0, this.loadedIds);
     this.loadMoreDialogues();
    }
    this._change.detectChanges();

    setTimeout(() => {
      this.forceRefresh();
    }, 700);
  }

  public refreshList() {
    this.lazyIds = this.dialogue.boxIds;
  }

  public async ngAfterViewInit() {
    const objId = this._activatedRoute.snapshot.fragment;
    if (objId) {
      this.HighlightElement(objId, 100, true, '#e9d1ff');
    }
  }

  public async toPromptAddDilemmaBox() {
    try {
      const dilemmaBox = await this._dilemmaBoxService.svcPromptCreateNew(this.dialogue.id);
      this._dilemmaBoxService.srvAdd(dilemmaBox);
      this._userSettingsService.updateInformation(dilemmaBox.id, 'authorNotes', 'New Box ');

      this._dialogueService.addBox(this.dialogue, dilemmaBox.id);
      this.loadDialogues();
      this.HighlightElement(dilemmaBox.id, 100, true);
    }
    catch (error) {
      Alert.showError(error);
    }
    this._change.detectChanges();
  }

shouldLoadSTORYBOX(box: any): boolean  {
    return box?.id.includes(IdPrefixes.STORYBOX) ? true : false;
}

  public async toPromptAddStoryBox() {
    try {
      const storyBox = await this._storyBoxService.svcPromptCreateNew(this.dialogue.id);
      this._storyBoxService.srvAdd(storyBox);
      this._userSettingsService.updateInformation(storyBox.id, 'authorNotes', 'New Box ');

      this._dialogueService.addBox(this.dialogue, storyBox.id);
      this.isStgoryBox = true;
      this.loadDialogues();
      this.HighlightElement(storyBox.id, 100, true);     
    }
    catch (error) {
      Alert.showError(error);
    }
    this._change.detectChanges();
  }

  public async toPromptAddOptionBox(type: OptionBoxType) {
    try {
      const optionBox = await this._optionBoxService.svcPromptCreateNew(this.dialogue.id, type);
      this._optionBoxService.srvAdd(optionBox);
      this._userSettingsService.updateInformation(optionBox.id, 'authorNotes', 'New Box ');

      this._dialogueService.addBox(this.dialogue, optionBox.id);
      this.loadDialogues();
      this.HighlightElement(optionBox.id, 100, true);
    }
    catch (error) {
      Alert.showError(error);
    }
    this._change.detectChanges();
  }

  goBack() {
    let route = this.activatedRoute.snapshot.url[0].path + '/' + this.level?.id + '/dialogues';
    this._router.navigate([route]);
  }
  copyDialogueToClipboard() {
    let finalText = 'Dialogue - ' + GameTypes.dialogueTypeName[this.dialogue.type] + ' {' + this.dialogue.id + '}\n\n';
    this.dialogue.boxIds.forEach((boxId) => {
      const box = this._storyBoxService.svcFindById(boxId) || this._optionBoxService.svcFindById(boxId) || this._dilemmaBoxService.svcFindById(boxId);
      const boxName = Typing.typeName(box);
      finalText += '\n';
      finalText += boxName + '\n';
      finalText += box?.label ? '[Label] ' + box.label + '\n' : '';


      if (box instanceof StoryBox) {
        finalText += this.storyBoxToString(box);
      }
      else if (box instanceof OptionBox) {
        let idLength = box.id.length;
        //1. Enter in each box.
        for (let i = 0; i < this._roadblockService.models.length; i++) {
          //2. Get each roadblock of each box
          if (this._roadblockService.models[i].id.substring(0, idLength).trim() == box.id.trim() &&
            this._roadblockService.models[i].id.substring(0, idLength + 1).trim().charAt(idLength) == '.' &&
            this._roadblockService.models[i].id.substring(0, idLength + 2).trim().charAt(idLength + 1) != 'o') {
            //3. Passes the RB to be serialized.
            finalText = this.roadblockBoxToString(finalText, this._roadblockService.models[i]);
          }
        }
        for (let i = 0; i < box.optionIds.length; i++) {
          const option = this._optionService.svcFindById(box.optionIds[i]);
          if (option.resultDC === undefined) {
            finalText += option?.label ? '[Label] ' + option.label + '\n' : '';
          }

          finalText += '[Option] ';
          finalText += 'Omit [' + (option?.isOmitted ? 'x' : ' ') + '] ';
          finalText += option?.message + ' ';
          finalText += '- Weight[' + (option?.weight || 0) + '] ';
          finalText += '\n';

          if (option.choiceDifficulty  && !option.investigaDifficulty) { //Choice
            finalText += option?.choiceDifficulty ? 'Dice' + '\n' : '';
            finalText += option?.choiceSpeech ? '[Opponent] ' + option.choiceSpeech + '\n': '';
            finalText += option?.bl === 0 ? '[Boss Level (BL)]: ' + 0 + '\n' : '[Boss Level (BL)]: ' + option.bl + '\n';
            finalText += option?.classeNameOpponet ? '[Class]: ' + option.classeNameOpponet + '\n' : '';
            finalText += option?.choiceAtributte ? '[Attribute] ' + option.choiceAtributte + '\n': '';
            finalText += option?.classModifierValue === 0 ? '[Class modifier (CM)]: ' + 0 + '\n' : '[Class modifier (CM)]: ' + option.classModifierValue + '\n' ;
            finalText += option?.subcontext ? '[Subcontext] ' + option.subcontext + '\n' : '';
           // finalText += option?.subContextDescription ? '[Description] ' + option.subContextDescription + '\n' : '' + '\n';
            finalText += option?.choiceDifficulty ? '[ITD - Intrinsic Task Difficulty] ' + option.choiceDifficulty  + '\n': '';
            finalText += option?.resultDC ? '[DC - Difficulty Class] ' + option.resultDC + '\n' : '';
           // finalText += option?.descriptionDCGuide ? '[Description] ' + option.descriptionDCGuide + '\n' : '';
          } 
          else {
            //Investigation
            finalText += option?.choiceDifficulty ? 'Dice' + '\n': '';
            finalText += option?.nameKnowledge ? '[Knowledge] ' + option.nameKnowledge + '\n' : '';
            finalText += option?.subcontext ? '[Subcontext] ' + option.subcontext + '\n' : '';
           // finalText += option?.subContextDescription ? '[Description] ' + option.subContextDescription + '\n' : '' + '\n';
            finalText += option?.fatorSituationModifier ? '[Situational Modifier]: ' + option.fatorSituationModifier + '\n' : '';
            finalText += option?.valueSituationModifier ? '[Situational Modifier (SM)] ' + option.valueSituationModifier + '\n': '';
            finalText += option?.investigaDifficulty ? '[ITD - Intrinsic Task Difficulty] ' + option.investigaDifficulty + '\n' : '';
            finalText += option?.resultDC ? '[DC - Difficulty Class] ' + option.resultDC + '\n': '';
          //  finalText += option?.descriptionDCGuide ? ' - [Description] ' + option.descriptionDCGuide + ' - ' : '[Description] ' + '';
           // finalText += option?.descriptionInvestigation ? `[${option.nameKnowledge}] ` + option.descriptionInvestigation + '\n' : '';
          }

          //finalText = this.setTextToChoiceBox(box, finalText, option);
          finalText = this.getChoiceboxText(option, finalText);
          finalText += '\n';
        }
    
          finalText += '[ End ] ';
          finalText += '\n';
          finalText += '\n';
          finalText += '―――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――' + '\n';
          finalText += '\n';
      
      }
      //Dilemma Box
      else if (box instanceof DilemmaBox) {

        let idLength = box.id.length;
        //1. Enter in each box.
        for (let i = 0; i < this._roadblockService.models.length; i++) {
          //2. Get each roadblock of each box
          if (this._roadblockService.models[i].StoryBoxId === box.id) {
            //3. Passes the RB to be serialized.
            finalText = this.roadblockBoxToString(finalText, this._roadblockService.models[i]);
          }
        }

        for (let i = 0; i < box.optionDilemmaIds.length; i++) {
          const dilemma = this._dilemmaService.svcFindById(box.optionDilemmaIds[i]);
          finalText += '\n';
          finalText += 'Omit [' + (dilemma?.isOmitted ? 'x' : ' ') + '] - ';
          finalText += '[Message] ' + dilemma.message;
          finalText += ' - [Weight] ' + (dilemma?.weight || 0);
          finalText += '\n';
          finalText += 'Dilemma Points' + '\n';
          finalText += dilemma?.threshold != undefined ? '[Threshold] ' + dilemma.threshold : '[Threshold] ' + this._configThresholdService.models[0].valueThreshold;
          finalText += ' - ';
          // Dilemma Points
          for (let index = 0; index < this._atributteService.models.length; index++) {
            finalText += '[' + this._atributteService.models[index].atributte + '] ' + dilemma?.points[index].valuePoints + ' - ';
          }

          finalText += '\n';
          finalText = this.setTextToDilemmaBox(box, finalText, dilemma);
          finalText = this.findTagsInOptions(box.optionDilemmaIds[i], finalText);
          finalText += '\n';
        }
   
          finalText += '[ End ] ';
          finalText += '\n';
          finalText += '\n';
          finalText += '―――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――' + '\n';
          finalText += '\n';   

      }


      finalText += '\n';
    });

    const dialogTextElement = document.createElement('textarea');
    document.body.append(dialogTextElement);

    dialogTextElement.value = finalText;
    dialogTextElement.focus();
    dialogTextElement.select();
    document.execCommand('copy');

    document.body.removeChild(dialogTextElement);

    Alert.ShowSuccess('Copied ' + this.level.id + ' dialogues.');
    this._change.detectChanges();
  }
  //FIM DO Copy Dialogue To Clipboard

  setTextToDilemmaBox(box, finalText: string, option): string {
    finalText = this.getAnswerDilemmmaText(option, finalText);

    for (let i = 0; i < box.optionDilemmaIds.length; i++) {
      for (let j = 0; j < this._roadblockService.models.length; j++) {
        if (option.id == box.optionDilemmaIds[i] && this._roadblockService.models[j].StoryBoxId == option.idDilemmaBox) {
          finalText = this.roadblockBoxToString(finalText, this._roadblockService.models[j]);
        }
      }

    }

    return finalText;
  }

  setTextToChoiceBox(box, finalText: string, option): string {
    for (let i = 0; i < box.optionIds.length; i++) {
      for (let j = 0; j < this._roadblockService.models.length; j++) {
        if (option.id == box.optionIds[i] && this._roadblockService.models[j]?.StoryBoxId == option.answerBoxId) //Dice Positive
        {
          finalText = this.roadblockBoxToString(finalText, this._roadblockService.models[j]);
        }
      }

    }
    finalText = this.getChoiceboxText(option, finalText);
    return finalText;
  }

  getChoiceboxText(option, finalText: string): string {
    //Get answerbox 
    let answer = this._storyBoxService.svcFindById(option.answerBoxId);

    if (option.answerBoxId != undefined) { //Positive
      finalText += '\n';
      finalText += answer?.subcontext ?  `Answer Positive ` + '\n' : `Answer` + '\n';
      finalText += answer?.labelOption ? '[Label] ' + answer.labelOption + '\n' : '';

    //Get speech based on the answer.storyProgressIds array.
    
      for (let j = 0; j < this._roadblockService.models.length; j++) {
        if (this._roadblockService.models[j]?.StoryBoxId == option.answerBoxId) //Dice Positive
        {
          finalText = this.roadblockBoxToString(finalText, this._roadblockService.models[j]);
        }
      }

      for (let i = 0; i < answer.storyProgressIds.length; i++) {

      let speech = this._speechService.svcFindById(answer?.storyProgressIds[i]);
      if (!speech) continue;
      finalText += `[Speech] `;
      finalText += 'Intro[' + (speech?.isIntroduction ? 'x' : ' ') + '] ';
      finalText += this.preloadedSpeakers.find((speaker) => speaker.id === speech?.speakerId)?.name + ' ';
      finalText += '(' + this._emotionService.svcFindById(speech?.emotionId)?.name + '): ';
      finalText += this._speechService.svcFindById(answer?.storyProgressIds[i])?.message + '\n';
      
    }

    finalText = this.findTagsInOptions(option.answerBoxId, finalText);
   
    }



    let answerNegative = this._storyBoxService.svcFindById(option.answerBoxNegativeId);

    if (option.choiceNegative || option.investigationNegative) {
      finalText += '\n';
      finalText += `Answer Negative ` + '\n';
      finalText += answerNegative?.labelOption ? '[Label] ' + answerNegative.labelOption + '\n' : '';

      for (let j = 0; j < this._roadblockService.models.length; j++) {
        if (this._roadblockService.models[j]?.StoryBoxId == option.answerBoxNegativeId) //Dice Negative
        {
          finalText = this.roadblockBoxToString(finalText, this._roadblockService.models[j]);
        }
      }

      for (let i = 0; i < answerNegative.storyProgressIds.length; i++) {

        let speech = this._speechService.svcFindById(answerNegative?.storyProgressIds[i]);
        if (!speech) continue;
        finalText += `[Speech] `;
        finalText += 'Intro[' + (speech?.isIntroduction ? 'x' : ' ') + '] ';
        finalText += this.preloadedSpeakers.find((speaker) => speaker.id === speech?.speakerId)?.name + ' ';
        finalText += '(' + this._emotionService.svcFindById(speech?.emotionId)?.name + '): ';
        finalText += this._speechService.svcFindById(answerNegative?.storyProgressIds[i])?.message + '\n';
      }
   
      finalText = this.findTagsInOptions(option.answerBoxNegativeId, finalText);
      
    }

    return finalText;
  }

  getAnswerDilemmmaText(option, finalText: string): string {
    //Get answerbox 
    let answer = this._answerDilemmaBoxService.svcFindById(option.idDilemmaBox);

    finalText += '\n';
    finalText += `Answer Dilemma ` + '\n';
    finalText += answer?.label ? '[Label] ' + answer.label + '\n' : '';

    //Get speech based on the answer.storyProgressIds array.
    for (let i = 0; i < answer.storyProgressIds.length; i++) {
      let speech = this._speechService.svcFindById(answer?.storyProgressIds[i]);
      if (!speech) continue;
      finalText += `[Speech] `;
      finalText += 'Intro[' + (speech?.isIntroduction ? 'x' : ' ') + '] ';
      finalText += this.preloadedSpeakers.find((speaker) => speaker.id === speech?.speakerId)?.name + ' ';
      finalText += '(' + this._emotionService.svcFindById(speech?.emotionId)?.name + '): ';
      finalText += this._speechService.svcFindById(answer?.storyProgressIds[i])?.message + '\n';
    }
    return finalText;
  }

  roadblockBoxToString(finalText: string, roadblock: RoadBlock): string {
    finalText += '[Roadblock] ';
    let typeName = typeNames[+roadblock.hard.type];
    finalText += '{[Type] -> ' + typeName + ' ';
    finalText += '[Validate if true] -> ' + roadblock.validateIfTrue + ' '

    if (+roadblock.Type == 1) {
      let item = this._itemService.svcFindById(roadblock.ItemID);

      finalText += '[Item] -> ' + item?.name + ' ';
      finalText += '[Amount] -> ' + roadblock.ItemAmount + ' ';

      let operatorDisplay = operatorString[roadblock.operator];
      finalText += '[Operator] -> ' + operatorDisplay + ' ';
    }
    else if (+roadblock.Type == 4) {
      let classs: string = ''
      for (let i = 0; i < this._classService.models.length; i++) {
        if (roadblock.klassId == this._classService.models[i].id) {
          classs = this._classService.models[i].name;
        }
      }
      finalText += '[Class] -> ' + classs + ' ';
    }
    else if (+roadblock.Type == 5) {
      let places: string = ''
      for (let i = 0; i < this._levelHelperService.models.length; i++) {
        if (roadblock.spokeElementId == this._levelHelperService.models[i].elementId) {
          places = this._levelHelperService.models[i].text;
        }
      }
      finalText += '[Places] -> ' + places + ' ';
    }
    else if (+roadblock.Type == 6) {
      let operatorDisplay = operatorString[roadblock.operator];
      finalText += '[Karmic Requirement] -> ' + roadblock.ItemAmount + ' [Operator] -> ' + operatorDisplay + ' ';
    }
    else {
      let character = this._characterService.svcFindById(roadblock.CharacterID);
      finalText += '[Character] -> ' + character?.name + ' ';
    }

    finalText += '}';
    finalText += '\n';
    return finalText;
  }

  private storyBoxToString(storyBox: StoryBox): string {
    let text = '';
    //Getting appropriate Roadblocks on storybox
    let roadblockIds: RoadBlock[] = [];
    for (let i = 0; i < this._roadblockService.models.length; i++) {
      //Here we are removing the storybox that are also option. This is already computed in another place.
      if (this._roadblockService.models[i].id.includes(storyBox.id) && !this._roadblockService.models[i].id.includes('opt')) {
        roadblockIds.push(this._roadblockService.models[i]);
      }
    }
    for (let i = 0; i < roadblockIds.length; i++) {
      let roadblock = roadblockIds[i];
      //Empty string here to avoid text repetition. We want add just the roadblock.
      text += this.roadblockBoxToString('', roadblock);
    }
    storyBox.storyProgressIds.forEach((storyProgressId) => {
      const speech = this._speechService.svcFindById(storyProgressId);
      const event = this._eventService.svcFindById(storyProgressId);
      const marker = this._markerService.svcFindById(storyProgressId);

      if (speech) {
        text += '[Speech] ';
        text += 'Intro[' + (speech.isIntroduction ? 'x' : ' ') + '] ';
        text += this.preloadedSpeakers.find((speaker) => speaker.id === speech.speakerId)?.name + ' ';
        text += '(' + this._emotionService.svcFindById(speech.emotionId)?.name + '): ';
        text += speech?.message;
      }
      else {
        this.preloadedMicroloopContainers = sortData(this._microloopContainerService.models, 'name');
        const eventOrMarker = event || marker;
        text += '[' + (event ? GameTypes.eventTypeName[eventOrMarker.type] : GameTypes.markerTypeName[eventOrMarker.type]) + '] ';
        const anything: any = eventOrMarker;
        const microloop = this.preloadedMicroloopContainers.filter(pre => pre.id == anything.microloopId);
        const unlockLevel = this._levelService.svcFindById(anything?.levelId);
        const mission = this._missionService.svcFindById(anything?.missionId);
        const objective = this._objectiveService.svcFindById(anything?.objectiveId);
        const item = this._itemService.svcFindById(anything?.itemId);
        const amount = anything?.amount;
        const video = this._videoService.svcFindById(anything?.videoId);
        const tutorial = this._tutorialService.svcFindById(anything?.tutorialId);
        const character = this._characterService.svcFindById(anything?.characterId);
        text += MarkerType[anything?.type] == 'RESTART_DIALOGUE' ? this.setTextForRestartDialogue(anything, text) : '';
        text += microloop[0]?.name != undefined ? '{Microloop: ' + microloop[0]?.name + '}' : '';
        text += unlockLevel ? '{Level: ' + unlockLevel.name + '}' : '';
        text += mission ? '{Mission: ' + mission.name + '}' : '';
        text += objective ? '{Objective: ' + objective.description + '}' : '';
        text += item ? '{Item: ' + item.name + '}' : '';
        text += amount ? '{Amount: ' + amount + '}' : '';
        text += video ? '{Video: ' + video['name'] + '}' : '';
        text += tutorial ? '{Tutorial: ' + tutorial.name + '}' : '';
        text += character ? '{Character: ' + character.name + '}' : '';
      }
      text += '\n';
    });
    text += '\n'; 
    text += '[ End ] ';
    text += '\n';
    text += '\n';
    text += '―――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――――' + '\n';
    text += '\n';  

    this._change.detectChanges();    
    return text;
  }

  setTextForRestartDialogue(anything: any, text: string): string {
    if (anything.unlockCondition == 'Karmic Equilibrium')//UnlockCondition: Karmic Equilibrium
    {
      text = '{Unlock Condition: Karmic Equilibrium}' + `{Condition Operator: ${' ' + anything.choosedCondition}}` +
        `{Amount: ${' ' + anything.amountRequired}}`;
    }
    else if (anything.unlockCondition == "Defeated Boss")//UnlockCondition: Defeated Boss
    {
      text = '{Unlock Condition: Defeated Boss}' + `{Character ID: ${' ' + anything.choosedCondition}}`;
    }
    else if (anything.unlockCondition == "Obtained Item")//UnlockCondition: Obtained Item
    {
      text = '{Unlock Condition: Obtained Item}' + `{Item Requirement: ${' ' + anything.choosedCondition}}` +
        `{Condition Operator: ${' ' + anything.conditionOperator}}` + `{Amount: ${' ' + anything.amountRequired}}`;
    }
    else if (anything.unlockCondition == "Spoke In")//UnlockCondition: Spoke In
    {
      text = '{Unlock Condition: Spoke In}' + `{Places: ${' ' + anything.choosedCondition}}`;
    }
    else if (anything.unlockCondition == "Collected Class")//UnlockCondition: Collected Class
    {
      text = '{Unlock Condition: Collected Class}' + `{Class: ${' ' + anything.choosedCondition}}`;
    }
    else if (anything.unlockCondition == "Talked to Character")//UnlockCondition: Talked to Character
    {
      text = '{Unlock Condition: Talked to Character}' + `{Character ID: ${' ' + anything.choosedCondition}}`;
    }
    return text;
  }

  findTagsInOptions(id: string, text: string) {
    let event = [];
    for (let i = 0; i < this._eventService.models.length; i++) {
      if (this._eventService.models[i].id.substring(0, id.length) == id) {
        event.push(this._eventService.models[i]);
      }
    }
    let marker = [];
    for (let i = 0; i < this._markerService.models.length; i++) {
      if (this._markerService.models[i].id.substring(0, id.length) == id) {
        marker.push(this._markerService.models[i]);
      }
    }
    text = this.setTextBasedOnTag(text, event, true);
    text = this.setTextBasedOnTag(text, marker);

    return text;
  }

  setTextBasedOnTag(text: string, tag, isEvent: boolean = false): string {
    const eventOrMarker = tag;

    for (let i = 0; i < eventOrMarker.length; i++) {
      let seenMicroloop = [];
      if ((isEvent ? GameTypes.eventTypeName[eventOrMarker[i]?.type] : GameTypes.markerTypeName[eventOrMarker[i]?.type]) == undefined) continue;

      text += '[' + (isEvent ? GameTypes.eventTypeName[eventOrMarker[i]?.type] : GameTypes.markerTypeName[eventOrMarker[i]?.type]) + '] ';
      const anything: any = eventOrMarker;
      //Avoid putting garbage on the removeSpecialItem and removeSpecialItemOnGrind.
      if ((anything[i]?.type == 8 || anything[i]?.type == 12) && anything[i]?.id.includes('opt') && !anything[i]?.id.includes('mrk')) {
        text += '\n'
        continue;
      };
      let microloop = undefined;
      microloop = this.calculateMicroloop(eventOrMarker, i, seenMicroloop, microloop);

      const unlockLevel = this._levelService.svcFindById(anything[i]?.levelId);
      const mission = this._missionService.svcFindById(anything[i]?.missionId);
      const objective = this._objectiveService.svcFindById(anything[i]?.objectiveId);
      const item = this._itemService.svcFindById(anything[i]?.itemId);
      const amount = anything[i]?.amount;
      const video = this._videoService.svcFindById(anything[i]?.videoId);
      const tutorial = this._tutorialService.svcFindById(anything[i]?.tutorialId);
      const character = this._characterService.svcFindById(anything[i]?.characterId);

      text += +eventOrMarker[i]?.type == 11 && microloop?.name != undefined ? '{Microloop: ' + microloop?.name + '}' : '';
      text += unlockLevel ? '{Level: ' + unlockLevel?.name + '}' : '';
      text += MarkerType[anything[i]?.type] == 'RESTART_DIALOGUE' ? this.setTextForRestartDialogue(anything[i], text) : '';
      text += mission ? '{Mission: ' + mission?.name + '}' : '';
      text += objective ? '{Objective: ' + objective?.description + '}' : '';
      text += item ? '{Item: ' + item?.name + '}' : '';
      text += amount ? '{Amount: ' + amount + '}' : '';
      text += video ? '{Video: ' + video['name'] + '}' : '';
      text += tutorial ? '{Tutorial: ' + tutorial?.name + '}' : '';
      text += character ? '{Character: ' + character?.name + '}' : '';
      text += '\n';
    }

    return text;
  }

  calculateMicroloop(eventOrMarker, i, seenMicroloop, microloop) {
    this.preloadedMicroloopContainers = sortData(this._microloopContainerService.models, 'name');

    for (let j = 0; j < this.preloadedMicroloopContainers.length; j++) {
      if (+eventOrMarker[i]?.type == 11 &&
        this.preloadedMicroloopContainers[j].id == eventOrMarker[i].microloopId &&
        !seenMicroloop.includes(this.preloadedMicroloopContainers[j].id)) {
        microloop = this.preloadedMicroloopContainers[j];
        seenMicroloop.push(microloop.id);
        break;
      }
    }
    return microloop;
  }

  openTreePopup() {
    this.popupOpen = true;
    this._change.detectChanges();
  }

  closeTreePopup() {
    this.popupOpen = false;
    this._change.detectChanges();
  }

  openSimulatorPopup() {
    this.popupSimulatorOpen = true;
    this._change.detectChanges();
  }

  closeSimulatorPopup() {
    this.popupSimulatorOpen = false;
    this._change.detectChanges();
  }
}
