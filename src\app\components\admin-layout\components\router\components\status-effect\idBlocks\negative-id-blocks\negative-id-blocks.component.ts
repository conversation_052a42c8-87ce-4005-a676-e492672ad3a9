import { Component, EventEmitter, Output } from '@angular/core';
import { Alert } from '../../../../../../../../../lib/darkcloud';
import { NegativeIdBlocks } from '../../../../../../../../lib/@bus-tier/models/NegativeIdBlocks';
import { Button } from '../../../../../../../../lib/@pres-tier/data';
import { NegativeIdBlockservice } from '../../../../../../../../services';

@Component({
  selector: 'app-negative-id-blocks',
  templateUrl: './negative-id-blocks.component.html',
  styleUrls: ['./negative-id-blocks.component.scss']
})
export class NegativeIdBlocksComponent {

  titles = [1, 2, 3, 4, 5, 6];
  listNegative: NegativeIdBlocks[] = [];
  activeLanguage = 'PTBR';
  @Output() activeTab2 = new EventEmitter<string>();

  public readonly excelButtonTemplate: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };
  constructor(
    private _negativeIdBlockservice: NegativeIdBlockservice
  ){}


  async ngOnInit(): Promise<void>{

      this._negativeIdBlockservice.toFinishLoading();
      this._negativeIdBlockservice.models = this._negativeIdBlockservice.models.filter(negativeItem => {
        return negativeItem.positionNameNegative.some(value => value !== "" && value !== undefined && value !== null);
      });
      this._negativeIdBlockservice.toSave();
      this.listNegative = this._negativeIdBlockservice.models;
    }

    async onExcelPaste() {
      const text = await navigator.clipboard.readText();
      const lines = text.split(/\r?\n/).filter(line => line);    
      const processedData: string[][] = [];
    
      if (lines.length > 0) {
        lines.forEach(line => {
          // Divide cada linha em colunas e remove a primeira coluna
          const values = line.split("\t").map(value => value.trim()).slice(1);
    
          processedData.push(values);
        });
    
        // Verifica se o número de colunas em cada linha corresponde ao número de colunas em this.titles
        const isColumnCountValid = processedData.every(row => row.length === this.titles.length);
    
        if (!isColumnCountValid) {
          Alert.showError('Invalid number of columns');
          return;
        }
  
        this._negativeIdBlockservice.models = [];
        this._negativeIdBlockservice.toSave();
    
        for (let index = 0; index < processedData.length; index++) {
          this._negativeIdBlockservice.createNewNegativeIdBlocks(processedData[index]);
        }    

        Alert.ShowSuccess('Negative imported successfully!');
        this.activeTab2.emit('negative');
        this.ngOnInit();
      }
    }
    
 
    changeNegative(rowIndex: number, colIndex: number, newValue: string){

      if (this.listNegative[rowIndex]) {
        this.listNegative[rowIndex].positionNameNegative[colIndex] = newValue;
        this._negativeIdBlockservice.svcToModify(this.listNegative[rowIndex]);
      }
    }
    
 

}
