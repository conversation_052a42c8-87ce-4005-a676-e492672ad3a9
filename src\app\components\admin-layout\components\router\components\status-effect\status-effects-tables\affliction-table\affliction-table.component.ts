import { Component, EventEmitter, Output } from '@angular/core';
import { AfflictionTable } from 'src/app/lib/@bus-tier/models';
import { Button } from 'src/app/lib/@pres-tier/data';
import { AfflictionTableService } from 'src/app/services';
import { Alert } from 'src/lib/darkcloud';

@Component({
  selector: 'app-affliction-table',
  templateUrl: './affliction-table.component.html',
  styleUrls: ['./affliction-table.component.scss']
})
export class AfflictionTableComponent {
  
  titles = ['ID', 'CATEGORY', 'STATUS', 'SKILL RESIST USER','OPERATOR', 'VALUE', 'DESCRIPTION'];
    activeLanguage = 'PTBR';
    listAfflictionTable: AfflictionTable[] = [];

    @Output() activeTab2 = new EventEmitter<string>();
    isListAfflictionEmpty: boolean;
   
    public readonly excelButtonTemplate: Button.Templateable = {
      title: 'Paste content from excel',
      onClick: this.onExcelPaste.bind(this),
      iconClass: 'excel-icon',
      btnClass: Button.Klasses.FILL_ORANGE,
    };
    constructor(
      private _afflictionTableService: AfflictionTableService
    ){}
   
   
    async ngOnInit(): Promise<void>{
      
        this.removeEmptyItems();
         this.listAfflictionTable = this._afflictionTableService.models;
         this.isListAfflictionEmpty = this.listAfflictionTable.length === 0;   
   
      }

      removeEmptyItems() {
        this._afflictionTableService.toFinishLoading();
        this._afflictionTableService.models = this._afflictionTableService.models.filter(afflictionItem => afflictionItem.idAffliction !== "");
        this._afflictionTableService.toSave();
      }
   
      async onExcelPaste() {
        const text = await navigator.clipboard.readText();
        const lines = text.split(/\r?\n/).filter(line => line);    
        const processedData: string[][] = [];
      
        if (lines.length > 0) {
          lines.forEach(line => {
            // Divide cada linha em colunas e remove a primeira coluna
            const values = line.split("\t").map(value => value.trim()).slice(1);
      
            processedData.push(values);
          });
      
          // Verifica se o número de colunas em cada linha corresponde ao número de colunas em this.titles
          const isColumnCountValid = processedData.every(row => row.length === this.titles.length);
      
          if (!isColumnCountValid) {
            Alert.showError('Invalid number of columns');
            return;
          }
    
          this._afflictionTableService.models = [];
          this._afflictionTableService.toSave();
      
          for (let index = 0; index < processedData.length; index++) {
            this._afflictionTableService.createNewAfflictionTable(processedData[index]);
          }    
   
          Alert.ShowSuccess('Affliction Table imported successfully!');
          this.activeTab2.emit('afflictionTable');
          this.ngOnInit();
        }
      }
      
   
      changeDefensive(rowIndex: number, name: string, newValue: string){  
   
        if (name === 'idAffliction') {
         this.listAfflictionTable[rowIndex].idAffliction = newValue;        
        }
        else if (name === 'category') {
          this.listAfflictionTable[rowIndex].category = newValue;        
         }   
         else if (name === 'status') {
          this.listAfflictionTable[rowIndex].status = newValue;        
         }
         else if (name === 'skillResistUser') {
           this.listAfflictionTable[rowIndex].skillResistUser = newValue;        
          }
         else if (name === 'operator') {
          this.listAfflictionTable[rowIndex].operator = newValue;        
         }
         else if (name === 'value') {
          this.listAfflictionTable[rowIndex].value = newValue;        
         }
         else if (name === 'description') {
          this.listAfflictionTable[rowIndex].description = newValue;        
         }
   
        this._afflictionTableService.svcToModify(this.listAfflictionTable[rowIndex]);
      }    
   

}
