<div class="main-content">
    <div class="container-fluid">
        <!--Header-->
        <div class="list-header-row update">
            <div class="card">

                <div class="card-header-content" style="position: absolute; top: 10%;">
                    <h3 class="title">{{ cardTitle }}</h3>
                    <p style="width:60vw;" class="category">{{ description}}</p>
                </div>

                <div style="display: flex; align-items: end; justify-content: end;">
                    <div style="margin-right: 15px; margin-bottom: 16px;">
                        <div id="add"
                            style="display: flex; align-items: flex-end; justify-content: end; margin-right: 10px; position: relative;">
                            <button class="btn btn-success btn-fill ng-star-inserted" (click)="addPoints()">
                                <i class="pe-7s-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <ng-container *ngIf="listValueConfig.length > 0">
            <table class="table-bordered">
                <thead>
                    <tr>
                        <th class="trBC" style="width: 1%;">Index</th>
                        <th class="trBC" style="width: 6%;">Value</th>
                        <th class="trBC" style="width: 1%;">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <ng-container *ngFor="let item of listValueConfig; let i = index">
                        <tr>
                            <td class="gray">{{i + 1}}</td>
                            <td>
                                <input style="width: 100%;"
                                    class="background-input-table-color form-control form-short " placeholder=" "
                                    type="number" #numValue (change)="addValue(i, numValue.value)"
                                    [(ngModel)]="item.valueThreshold" />
                            </td>
                            <td style="display: flex; justify-content: center;">
                                <div style="width: 50px;">
                                    <button _ngcontent-hkn-c250="" class="btn btn-danger btn-fill btn-remove"
                                        style="padding: 0 !important;" (click)="removeThreshold(i)">
                                        <i _ngcontent-hkn-c250="" class="pe-7s-close"></i>
                                    </button>
                                </div>

                            </td>
                        </tr>
                    </ng-container>
                </tbody>
            </table>
        </ng-container>

        <ng-container *ngIf="listValueConfig.length === 0">
            <div class="card" style="text-align: center; padding: 20px;">
                <h3>Empty list. Click to create the list.</h3>
            </div>
        </ng-container>

    </div>
</div>