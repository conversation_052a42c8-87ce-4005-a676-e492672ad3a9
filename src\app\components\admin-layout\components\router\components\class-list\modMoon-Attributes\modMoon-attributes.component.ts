import { Component } from '@angular/core';
import { ModMoonAttributes } from 'src/app/lib/@bus-tier/models';
import { Atributte } from 'src/app/lib/@bus-tier/models/Atributte';
import { MODMoonAttributesService } from 'src/app/services';
import { AtributteService } from 'src/app/services/atributte.service';
import { Alert } from 'src/lib/darkcloud';
import { HighlightElement } from 'src/lib/others';

@Component({
  selector: 'app-modmoon-attributes',
  templateUrl: './modMoon-attributes.component.html',
  styleUrls: ['./modMoon-attributes.component.scss']
})
export class MODMoonAttributesComponent {

  cardTitle: string;
  description: string;
  atributtoClasses: Atributte[] = [];
  modMoonAttributes: ModMoonAttributes[] = [];

  constructor(
    private _atributteService: AtributteService,
    private _modMoonAttributesService: MODMoonAttributesService,
  ) { }

  ngOnInit(): void {
    this.cardTitle = 'MOD Moon Attributes';
    this.description = 'Value setting for MOD Moon Attributes';
    this._modMoonAttributesService.toFinishLoading();
    this._atributteService.toFinishLoading();    
    this.atributtoClasses = this._atributteService.models;
    this.checkAndAddAtributte();
    this.sortAtributtoClassesByAtributte();

    this.modMoonAttributes = this._modMoonAttributesService.models;    
    this.description = `Showing ${this.modMoonAttributes.length} results`;
  }

  async addMoonAttribute() {
   const moonAttribute = this._modMoonAttributesService.createNewMoonAttribute();
    this.ngOnInit();
  
    setTimeout(() => {
      HighlightElement(moonAttribute.id, 110, true);
    }, 100);
  }


checkAndAddAtributte() {
  this.atributtoClasses.forEach((atributte) => {
    const allModMoonAttributesHaveId = this._modMoonAttributesService.models.every((modMoonAttribute) => {
      return modMoonAttribute.idsAtributte.includes(atributte.id);
    });

    if (!allModMoonAttributesHaveId) {
      this._modMoonAttributesService.models.forEach((modMoonAttribute) => {
        if (!modMoonAttribute.idsAtributte.includes(atributte.id)) {
          modMoonAttribute.idsAtributte.push(atributte.id);
          modMoonAttribute.modMoonAtributte.push("");
          this._modMoonAttributesService.svcToModify(modMoonAttribute);
        }
      });
    }
  });
}

  sortAtributtoClassesByAtributte() {
  this.atributtoClasses.sort((a, b) => {
    return a.atributte.localeCompare(b.atributte);
  });
}
  
  async removeElement(id: string, displayName?: string) {

    const confirm = await Alert.showRemoveAlert((displayName || id) + ' ' + 'Moon Attribute');
    if (!confirm) return;

    this._modMoonAttributesService.svcToRemove(id);
    this.ngOnInit();
  }

  moonAtributteChange( index: number, value: string, position: number) {
    this.modMoonAttributes[index].modMoonAtributte[position] = value;
    this._modMoonAttributesService.svcToModify(this.modMoonAttributes[index]);
    this._modMoonAttributesService.toSave();
  }

}
