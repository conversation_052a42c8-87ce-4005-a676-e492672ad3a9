import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { IngredientDrop, WeaponUpgrade } from 'src/app/lib/@bus-tier/models';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';
import { ChestService, IngredientDropService, UserSettingsService } from 'src/app/services';
import { Button } from 'src/app/lib/@pres-tier/data';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc';
import { TranslationService } from 'src/app/services/translation.service';
import { SpinnerService } from '../../../../../../../spinner/spinner.service';
import { Alert } from 'src/lib/darkcloud';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-ingredient-drop',
  templateUrl: './ingredient-drop.component.html',
  styleUrls: ['./ingredient-drop.component.scss'],
})

export class IngredientDropComponent extends SortableListComponent<IngredientDrop> implements OnInit
{
  @Input() probability = true;
  particlesList:IngredientDrop[] = [];
  description:string = '';
  sortNameOrder = -1;
  custom: Custom;
  weaponUpgrades: WeaponUpgrade[];
  activeTab:string = 'ingredientDrop';

  constructor(
    private spinnerService: SpinnerService,
    _activatedRoute: ActivatedRoute,
    protected _ingredientDropService: IngredientDropService,
    _userSettingsService: UserSettingsService,
    private _router: Router,
    private _chestService: ChestService,
    protected _translationService: TranslationService,
    private ref: ChangeDetectorRef
  ) 
  {
    super(_ingredientDropService, _activatedRoute, _userSettingsService, 'name');
  }

  public override async ngOnInit(): Promise<void>
  {
    await this._ingredientDropService.toFinishLoading();
    await this._chestService.toFinishLoading();
    this.generateFirstParticleDrops();
    this.description = `Showing ${this.particlesList.length} results`; 
  }
  
  async generateFirstParticleDrops()
  {
    let particleLength: number = this._ingredientDropService.models.length;
    if(particleLength > 0)
    for(let i = 0; i < this._chestService.models.length; i++)
    {
      for(let j = 0; j < particleLength; j++)
      {
        if(this._chestService.models[i].acronym != undefined &&
          this._chestService.models[i].acronym != '' &&
          this._chestService.models[i].acronym == this._ingredientDropService.models[j].type)
        {
          this.particlesList.push(this._ingredientDropService.models[j]);
          break;
        }
        if(j == particleLength-1 && this._chestService.models[i].acronym != undefined &&
          this._chestService.models[i].acronym != '') 
        {
          let particleDrop: IngredientDrop = 
          await this._ingredientDropService.createNewParticleDrop(this._chestService.models[i].acronym);
          this.particlesList.push(particleDrop);
        }
      }
    }
    else
    for(let i = 0; i < this._chestService.models.length; i++)
    {
      if(this._chestService.models[i].acronym != undefined &&
        this._chestService.models[i].acronym != '') 
      {
        let particleDrop: IngredientDrop = 
         await this._ingredientDropService.createNewParticleDrop(this._chestService.models[i].acronym);
        this.particlesList.push(particleDrop);
      }
    }
    this.lineupOrderParticlesList();
    this.description = `Showing ${this.particlesList.length} results`;
  }

  lineupOrderParticlesList() 
  {
  this.sortNameOrder *= -1;
    this.particlesList.sort((a, b) => 
    {  
      return this.sortNameOrder * a.type.localeCompare(b.type);
    });

  }

  public readonly excelButtonTemplate: Button.Templateable = 
  {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  goBack()
  {
    this._router.navigate(['battleDropsGenerator/']);	
  }

  async onExcelPaste(): Promise<void> {
    this.spinnerService.setState(true);
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter(line => line.trim());

    const errors: string[] = [];
    const processedTypes = new Set<string>();

    for (let l = 0; l < lines.length; l++) {
        let line = lines[l];
        let cols = line.split(/\t/).map(col => col.trim());

        if (cols.length !== 2) {
            Alert.showError(`Number of columns copies wrong.`);
            this.spinnerService.setState(false);
            return;    
        }

        const particleType = cols[0];
        const amountStr = cols[1];

        // Verifica se o particleType existe na lista particlesList
        let particleDrop = this._ingredientDropService.models.find(x => x.type?.trim() === particleType);
        if (!particleDrop) {
           errors.push(`Particle type names not found in the system: "${particleType}"`);
           this.spinnerService.setState(false);
           return;
        }

        if (processedTypes.has(particleType)) {
          errors.push(`Duplicate particle type names found:"${particleType}".`);
          this.spinnerService.setState(false);
          return;
        }
        processedTypes.add(particleType);
   
        if (amountStr) {
            let amount = parseFloat(amountStr.split(' ').join('').split('.').join('').replace(',', '.'));
            if (isNaN(amount)) {
                errors.push(`Invalid amount value: "${amountStr}".`);
                this.spinnerService.setState(false);
                return;
            } else {
                particleDrop.amount = amount;
            }
        } else {
            particleDrop.amount = undefined;
        }
        await this._ingredientDropService.svcToModify(particleDrop);
        await this._ingredientDropService.toSave();  
    }
            
    if (this.displayErrors(errors)) {
        this.spinnerService.setState(false);
        return;
    }
    Alert.ShowSuccess('Ingredient Drop copied successfully!');
    this.lstFetchLists();
    this.spinnerService.setState(false);
    
    this.particlesList = this._ingredientDropService.models.filter(par => par.type !== undefined);
    this.sortNameOrder *= +1;
    this.particlesList.sort((a, b) => this.sortNameOrder * a.type.localeCompare(b.type));
    

}

displayErrors(errors: string[]): boolean {
    if (errors.length > 0) {
        this.spinnerService.setState(false);
        Alert.showError(errors.join('\n'));
        return true;
    }
    return false;
}

  async changeParticle(ingredient:IngredientDrop, value:string)
  {
    let par = this._ingredientDropService.svcFindById(ingredient.id);
    par.amount = value == '' ? undefined : +value;
    await this._ingredientDropService.svcToModify(par);
    await this._ingredientDropService.toSave();
  }
}
