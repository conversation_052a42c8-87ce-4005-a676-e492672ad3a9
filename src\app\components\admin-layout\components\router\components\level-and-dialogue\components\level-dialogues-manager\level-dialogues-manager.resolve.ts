import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Injectable } from '@angular/core';
import { DialogueService } from 'src/app/services/dialogue.service';
import { LevelService } from 'src/app/services/level.service';
import { Dialogue, Level } from 'src/app/lib/@bus-tier/models';
import { Preloadable } from './level-dialogues-manager.component';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';

@Injectable({
  providedIn: 'root',
})
export class LevelDialoguesManagerResolve implements Resolve<Preloadable> {
  constructor(
    private _dialogueService: DialogueService,
    private _levelService: LevelService,
    private _userSettingsService: UserSettingsService
  ) {}

  async resolve(snapshot: ActivatedRouteSnapshot): Promise<Preloadable> {
    const levelId = snapshot.paramMap.get('levelId');

    await this._levelService.toFinishLoading();
    await this._dialogueService.toFinishLoading();
    await this._userSettingsService.toFinishLoading();

    const level = this._levelService.svcFindById(levelId);
    const dialogues = await this.toTryCreateRemainingDialogues(level);
    
    return {
      dialogues,
      level,
    };
  }
  
  async toTryCreateRemainingDialogues(level: Level): Promise<Dialogue[]> {
    const availableDialogues = this._dialogueService.svcFilterByIds(
      level?.dialogueIds
    );

    if (availableDialogues.length < 5) {
      await this._levelService.toCreateRemainingDialogues(level);
    }

    await this._levelService.toOrderDialogues(level);

    return this._dialogueService.svcCloneByIds(level.dialogueIds, true);
  }
}
