import { Component, OnInit } from '@angular/core';
import {
  Video,
  Theme,
  Area,
  Animatic,
  Character,
  Cutscene,
} from 'src/app/lib/@bus-tier/models';
import { Popup, Review } from 'src/lib/darkcloud';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/SortableListComponent';
import { Sorting } from 'src/lib/darkcloud/angular/easy-mvc/Sorting';
import { comparable, Index } from 'src/lib/others';
import { GameTypes, VideoType } from 'src/lib/darkcloud/dialogue-system';
import {
  AreaService,
  ThemeService,
  VideoService,
  UserSettingsService,
  PopupService,
  ReviewService,
  CharacterService,
  QnAService,
} from 'src/app/services';
import { Button } from 'src/app/lib/@pres-tier/data';
import { EasyMVC } from 'src/lib/darkcloud/angular';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-video-list',
  templateUrl: './video-list.component.html',
  styleUrls: ['./video-list.component.scss'],
})
/**
 * Displays and edits video data as a list
 */
export class VideoListComponent extends SortableListComponent<Video> {
  public readonly VideoType = VideoType;
  protected override lstFilterParameters: EasyMVC.Filter[] = [{ name: 'areaId' }];

  public readonly rightButtonTemplates: Button.Templateable[] = [
    {
      btnClass: Button.Klasses.BLUE,
      title: 'Go to Theme List',
      onClick: this.redirectToThemeList.bind(this),
      iconClass: 'pe-7s-box2',
    },
    {
      btnClass: ['Cutscene', 'fill'],
      title: 'Add Cutscene',
      onClick: (() => this.lstPromptAdd(VideoType.CUTSCENE)).bind(this),
      iconClass: 'pe-7s-video',
    },
    {
      btnClass: ['Animatic', 'fill'],
      title: 'Add Animatic',
      onClick: (() => this.lstPromptAdd(VideoType.ANIMATIC)).bind(this),
      iconClass: 'pe-7s-film',
    },
  ];

  /**
   * Stores area data
   */
  public preloadedAreas: Area[];
  public animaticIdToggle: string;

  public areaNames: Index<string>;

  constructor(
    private _areaService: AreaService,
    private _themeService: ThemeService,
    _activatedRoute: ActivatedRoute,
    protected _videoService: VideoService,
    _userSettingsService: UserSettingsService,
    private _popupService: PopupService,
    private _reviewService: ReviewService,
    private _characterService: CharacterService,
    private _qnaService: QnAService,
    private _router: Router,
    protected _languageService: LanguageService,
    protected _translationService: TranslationService
  ) {
    super(_videoService, _activatedRoute, _userSettingsService, 'name');
  }

  override lstInit() {
    this._videoService.models.forEach(video => {
      if(!this._reviewService.reviewResults[video.id])
      {
        let result: Review.Result = {blankQnA: false, assignedAt: []}
        this._reviewService.reviewResults[video.id] = result;
      }

      this._videoService.assignReviews(video);
    });
    this.preloadAreas();
    this.animaticIdToggle = this._activatedRoute.snapshot.fragment;

  }

  preloadAreas(): void {
    const areaIds: string[] = [];
    this._videoService.models.forEach((video) => {
      
      //this._reviewService.reviewResults[video.id]?.assignedAt?.forEach(
        //(eventId) => {
          //const areaId = Area.getSubIdFrom(eventId);
          //if (!areaIds.includes(areaId)) {
            //areaIds.push(areaId);
          //}
        //}
      //);

      if(!areaIds.includes(video.areaId))
      {
        areaIds.push(video.areaId)
      }

    });
    this.preloadedAreas = this._areaService.svcFilterByIds(areaIds);
  }

  protected override filterItem(video: Video) {
    return (
      (this.lstFilterValue['areaId'] as string) === 'ALL' ||
      video.areaId === (this.lstFilterValue['areaId'] as string)
    );
  }

  override lstAfterInitFetchList() {
    this.areaNames = {};
    this._areaService.models.forEach((area) => {
      this.areaNames[area.id] = area.name;
    });
  }

  protected override specialSort(parameter: Sorting.Parameter) {
    switch (parameter) {
      case 'videoName':
        this._modelService.models.sort((a, b) => {
          const aName =
            +a.type === VideoType.CUTSCENE
              ? (a as Cutscene).name
              : this._characterService.svcFindById((a as Animatic).characterId)
                  ?.name;
          const bName =
            +b.type === VideoType.CUTSCENE
              ? (b as Cutscene).name
              : this._characterService.svcFindById((b as Animatic).characterId)
                  ?.name;

          return this.srtLstOrder === 'ascending'
            ? comparable(aName) < comparable(bName)
              ? 1
              : -1
            : comparable(aName) > comparable(bName)
            ? 1
            : -1;
        });
        break;
      case 'area':
        this._modelService.models.sort((a, b) =>
          this.srtLstOrder === 'ascending'
            ? comparable(this.areaNames[a.areaId]) >
              comparable(this.areaNames[b.areaId])
              ? 1
              : -1
            : comparable(this.areaNames[a.areaId]) <
              comparable(this.areaNames[b.areaId])
            ? 1
            : -1
        );
        break;
      case 'assigned':
        this._modelService.models.sort((a, b) =>
          this.srtLstOrder === 'ascending'
            ? this._reviewService.reviewResults[a.id]?.assignedAt?.length >
              this._reviewService.reviewResults[b.id]?.assignedAt?.length
              ? 1
              : -1
            : this._reviewService.reviewResults[b.id]?.assignedAt?.length >
              this._reviewService.reviewResults[a.id]?.assignedAt?.length
            ? 1
            : -1
        );
        break;
      default:
        this.defaultSort(parameter);
        break;
    }
  }

  /**
   * Prompts a theme list and addsit to the video theme list
   * @param video Video to which adds the theme
   */
  async toPromptAddThemeToAnimatic(animatic: Video) {
    const selectedThemeButton = await this._popupService.fire<Theme, Theme>(
      new Popup.Interface(
        {
          title: 'Select Theme',
          actionsClass: 'column',
        },
        Popup.toButtonList(
          this._themeService.models.filter(
            (th) => !(animatic as Animatic).themeIds.includes(th.id)
          ),
          'name'
        )
      )
    );
    if (!selectedThemeButton) {
      return;
    }

    await this._videoService.addTheme(animatic as Animatic, selectedThemeButton.value.id);
  }

  /**
   * Removes the theme from the animatic theme list by id
   * @param video Video from which removes the theme
   */
  async toRemoveThemeFromAnimatic(animatic: Video, themeId: string) {
    await this._videoService.removeTheme(animatic as Animatic, themeId);
  }

  public async toPromptSelectBindArea(video: Video) {
    const selectedAreaButton = await this._popupService.fire<Area, Area>(
      new Popup.Interface(
        {
          title: 'Select Area',
          actionsClass: 'column',
        },
        Popup.toButtonList(
          this._areaService.models,
          {
            parameters: ['hierarchyCode', 'name'],
            mapping: '[<hierarchyCode>]: <name>',
          },
          { undefinedTitle: 'No Area' }
        )
      )
    );
    if (!selectedAreaButton) {
      return;
    }

    video.areaId = selectedAreaButton.value?.id;
    await this._videoService.svcToModify(video);
  }

  public async toPromptChangeVideoType(video: Video) {
    const selectedVideoTypeButton = await this._popupService.fire<
      VideoType,
      VideoType
    >(
      new Popup.Interface(
        {
          buttonSize: 'lg',
          title: 'Convert ' + GameTypes.videoTypeName[+video.type] + ' to...',
          actionsClass: 'column',
        },
        Popup.videoTypeButtons,
        {
          hideButton: { value: video.type },
        }
      )
    );
    if (!selectedVideoTypeButton) {
      return;
    }
    const type = selectedVideoTypeButton.value;
    await this._videoService.changeType(video, type);
    this.lstFetchLists();
  }

  public async toPromptSelectBindCharacter(animatic: Video) {
    const selectedCharacterButton = await this._popupService.fire<
      Character,
      Character
    >(
      new Popup.Interface<Character, Character>(
        {
          title: 'Select Character',
          actionsClass: 'column',
        },
        Popup.toButtonList(this._characterService.models, 'name')
      )
    );
    if (!selectedCharacterButton) {
      return;
    }

    (animatic as Animatic).characterId = selectedCharacterButton.value?.id;
    await this._videoService.svcToModify(animatic);
  }

  async onChangeQuestion(video: Video, index: number, value: string) {
    const animatic = video as Animatic;
    const qna = this._qnaService.svcFindById(animatic.qnaIds[index]);
    qna.question = value;
    await this._qnaService.svcToModify(qna);
  }

  async onChangeAnswer(video: Video, index: number, value: string) {
    const animatic = video as Animatic;
    const qna = this._qnaService.svcFindById(animatic.qnaIds[index]);
    qna.answer = value;
    await this._qnaService.svcToModify(qna);
  }

  public redirectToThemeList() {
    this._router.navigate(['themes']);
  }
}
