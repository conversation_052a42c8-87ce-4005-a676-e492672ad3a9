    <div style="margin-top: 20px" class="list-header-row update">
      <div style="padding-top: 10px;" class="card">
        <app-header-with-buttons [cardTitle]="'Ailment Defenses'" [cardDescription]="''"
          [valueBossLevel]="valueBossLevel" [nameClass]="nameClass" [nameRarity]="nameRarity" [type]="type"
          [rightButtonTemplates]="[excelButtonTemplate]" [isBackButtonEnabled]="false">
        </app-header-with-buttons>
      </div>
    </div>
    <!--List-->
    <div style="display: flex; flex-direction: row; overflow-x: auto; width: 100%; justify-content: center;">
      <table class="table table-list">
        <thead>
          <tr>
            <th colspan="10">
              <h4>Ailment Defenses</h4>
            </th>
          </tr>
          <tr>
            <th class="light-gray" *ngFor=" let ail of this.ailmentList; let i = index;">{{ail.ailment}}</th>
          </tr>
          <tr>
            <th style="background-color: white !important;" *ngFor=" let ail of this.ailmentList; let i = index;">
              <input class="background-input-table-color form-control form-short " placeholder=" " type="text" #defenses
                [value]="this.ailmentDefenses?.ailmentDefensesList.length == 0 ? '' : this.ailmentDefenses?.ailmentDefensesList[i]?.fieldValueDefense"
                [ngClass]="{'empty-input': !defenses}"
                (change)="changeAilmentDefense(defenses.value, i, ail.ailment)" />
            </th>
          </tr>
        </thead>
      </table>
    </div>

