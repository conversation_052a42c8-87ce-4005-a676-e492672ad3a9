.sidebar-footer,
#searcher,
.bottom-nav {
  position:-ms-page;
  z-index: 10;
}

.bottom-nav {
  bottom: 80px;
}
 .scrollMenuBar {
  overflow: scroll;
  max-height: calc(100vh - 75px);
  min-height: 100%;
  padding-bottom: 210px;
  scrollbar-width: thin; 
 }

#searcher {
  left: 30px;
  max-height: calc(100vh - 85px);
  width: 170px;
  height: 30px;
  bottom: 50px;
  margin-left: 8px;
  margin-right: 12px;
  margin-top: 0;
}

.sidebar-footer {
  text-align: center;
  left: 25px;
  bottom: 10px;
  opacity: 0.5;
  font-size: 13px;
}

/* li {
  height: 45px;
  a {
    padding: 8px;
  }
} */

.not {
  position: relative;
  width: 100%;
  right: 15px !important;
}

li {
  min-height: 30px;
  height: 3vh;
  a {
    padding-left: 8px;
    padding-right: 8px;
    padding-bottom: 0.5vh;
    padding-top: 0.5vh;
  }
  p {
    font-size: calc(8px + 0.35vh);
  }
  i {
    font-size: calc(18px + 0.9vh);
  }
  margin-bottom: calc(2px + 0.6vh);
}

@media screen and (max-height: 960px) {
  li {
    height: 2vh;
    margin-bottom: calc(2px + 0.4vh);
  }
}
@media screen and (max-height: 900px) {
  li {
    height: 1vh;
    a {
      padding-bottom: 0px;
      padding-top: 0px;
    }
    p {
      font-size: 8px;
    }
    i {
      font-size: 18px;
    }
    margin-bottom: 0px;
  }
}
