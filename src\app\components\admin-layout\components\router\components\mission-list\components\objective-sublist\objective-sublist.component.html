<table class="table table-list" style="background-color: #e6e6e6">
  <thead>
    <tr>
      <th>ID</th>
      <th>Assigned</th>
      <th style="width: 100px;">XP</th>
      <th>Hidded</th>
      <th>Description
        <div class="ball-circle"></div>
      </th>
      <th>Actions</th>
    </tr>
  </thead>
  <tbody>
    <ng-container *ngFor="let objective of mission.objectiveIds | objectives; let i = index">
      <tr id="{{ objective.id }}">
        <td class="td-sort">
          <div class="objective-star">
            <i class="pe-7s-star category"></i>
            <div class="text">{{ i }}</div>
          </div>
          <div class="category">{{ objective.id }}</div>
        </td>
        <td class="td-medium">

          <!--if objective was assigned once-->
          <ng-container
            *ngIf="(objective | review).completesAt.length === 1 || (objective | review).failsAt.length > 0">
            <i style="position: relative" placement='top' delay='250' ttWidth="auto" ttAlign="left" ttPadding="10px"
              *ngIf="(objective | review).completesAt.length === 1"
              tooltip="Completes at {{((objective | review).completesAt | location).toString()}}"
              class="pe-7s-check success">
            </i>
            <i *ngIf="(objective | review).failsAt.length === 1" style="position: relative" placement='top' delay='250'
              ttWidth="auto" ttAlign="left" ttPadding="10px"
              tooltip="Fails at {{((objective | review).failsAt | location).toString()}}" class="pe-7s-check warning">
            </i>
          </ng-container>

          <!--if objective was assigned more than once-->
          <ng-container *ngIf="(objective | review).completesAt.length > 1">
            <i style="position: relative;" placement='top' delay='250' ttWidth="auto" ttAlign="left" ttPadding="10px"
              tooltip="Objective completed more than once {{((objective | review).completesAt | location).toString()}}"
              class="pe-7s-check success">
              <i class="pe-7s-attention warning icon-notification-sm"></i>
            </i>
          </ng-container>

          <ng-container *ngIf="(objective | review).failsAt.length > 1">
            <i style="position: relative;" placement='top' delay='250' ttWidth="auto" ttAlign="left" ttPadding="10px"
              tooltip="Objective failed more than once {{((objective | review).completesAt | location).toString()}}"
              class="pe-7s-check warning">
              <i class="pe-7s-attention warning icon-notification-sm"></i>
            </i>
          </ng-container>

          <!--if objective was not assigned-->
          <i tooltip="Objective not assigned" *ngIf="(objective | review).assignedAt.length === 0"
            class="pe-7s-attention attention" placement='top' delay='250' ttWidth="auto" ttAlign="left"
            ttPadding="10px">
          </i>

          <br />
          <p [ngStyle]="{'background-color': a.status == 'complete' ? 'aquamarine' : a.status == 'fail' ? 'rgb(255, 204, 127)' : ''}"
            style="text-align: left;" class="th-clickable"
            *ngFor="let a of ((objective | review).assignedAt | location3 : objective)" (click)="accessSPO(a)">
            {{ a.location }}
          </p>
        </td>

        <td>
          <div>
            <input class="form-control form-short" type="number" value="{{ objective.xp }}" #xp
              (change)="changeXp(objective, xp.value)" />
          </div>
        </td>
        <td>
          <div>
            <input class="form-control form-short" type="checkbox" [checked]="objective?.hidded"
              (click)="changeHidded(objective)" />
          </div>
        </td>
        <td>
          <div>
            <input class="form-control form-short" value="{{ objective.description }}" #description
              (change)="onChange(objective, 'description', description.value)" />
          </div>
        </td>
        <td class="td-actions td-auto">
          <button class="btn btn-fill btn-danger btn-remove" (click)="toRemove(objective)">
            <i class="pe-7s-close"></i>
          </button>
        </td>
      </tr>
    </ng-container>
  </tbody>
  <td style="padding: 0; margin: 0">
    <div>
      <button class="btn btn-success" style="border: 0" (click)="add()">
        <i class="pe-7s-plus" style="font-size: 50px; border: 0; font-weight: bold; color:green !important;"></i>
      </button>
    </div>
  </td>
</table>