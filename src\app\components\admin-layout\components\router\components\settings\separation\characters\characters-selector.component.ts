import { AfterViewInit, Component, OnInit } from '@angular/core';
import { CharactersSelector } from 'src/app/lib/@bus-tier/models';
import { Button } from 'src/app/lib/@pres-tier/data';
import { AreaService, CharacterService, CharactersSelectorService, UserSettingsService } from 'src/app/services';
import { Alert } from 'src/lib/darkcloud';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/SortableListComponent';
import * as XLSX from 'xlsx';
import { SpinnerService } from './../../../../../../../../spinner/spinner.service';
import { ActivatedRoute } from '@angular/router';


@Component({
  selector: 'app-characters-selector',
  templateUrl: './charaters-selector.component.html',
  styleUrls: ['./characters-selector.component.scss'],
})

export class CharactersSelectorComponent extends SortableListComponent<CharactersSelector> implements AfterViewInit, OnInit
{
  charactersSelectors:CharactersSelector[] = [];
  inThisPage:boolean = false;
  loadingSpinner: Boolean = false;
  sortByNameOrder = -1;
  caseSentitive;
  accentSentitive;
  description;

  // Objeto para controlar o estado da ordenação de cada campo
  private sortStates: { [key: string]: boolean } = {};


  constructor(
    private _spinnerService:SpinnerService,
    public _areaService:AreaService,
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    public _charactersSelectorService: CharactersSelectorService,
    private _charactersService: CharacterService
    ) 
    {
      super(_charactersSelectorService, _activatedRoute, _userSettingsService, 'name');
    }

    public readonly excelButtonTemplate: Button.Templateable = 
    {
      title: 'Paste content from excel',
      onClick: this.onExcelPaste.bind(this),
      iconClass: 'excel-icon',
      btnClass: Button.Klasses.FILL_ORANGE,
    };
  
    public readonly exportExcelButtonTemplate: Button.Templateable = 
    {
      btnClass: [...Button.Klasses.FILL_BLUE, 'Excel'],
      title: 'Export to Excel',
      onClick: this.downloadAsExcel.bind(this),
      iconClass: 'pe-7s-cloud-download',
    };

    public override async ngAfterViewInit(): Promise<void> 
    {
      this.inThisPage = true;
      this.loadingSpinner = true;
        setTimeout(()=>
        {
          if(this.isCharactersSelectorEmpty())
          {
            this._charactersService.models.forEach(itemsClass => 
            {
                let char = this._charactersSelectorService.createNewCharactersSelector();
                char.character = itemsClass.name;
                char.concept = false;
                char.separation = false;
                char.preFinish = false;
                char.finish = false;
                char.postFinish = false;
                char.rev1 = false;
                char.rev2 = false;
                char.characterArea = itemsClass.areaId;
                this.charactersSelectors.push(char)
            })             
          }
          else
          {
            this._charactersSelectorService.models.forEach(itemSel =>
            {
              this.charactersSelectors.push(itemSel);
            })
          }
          console.log('CharactersSelectors', this.charactersSelectors);
          this.loadingSpinner = false;
          this.description = `Showing ${this.charactersSelectors.length} results`;
        },1000)
        return null
      }

      isCharactersSelectorEmpty(): boolean
      {
        return this._charactersSelectorService.models.length == 0;
      }

      sortElements(value: string)
      {
        let notChecked = [];
        let checked = [];

        // Separar elementos em arrays baseado no valor true/false
        for(let i = 0; i < this.charactersSelectors.length; i++)
        {
          if(this.charactersSelectors[i][value] == true)
          {
            checked.push(this.charactersSelectors[i]);
          }
          else
          {
            notChecked.push(this.charactersSelectors[i]);
          }
        }

        // Verificar o estado atual da ordenação para este campo
        // Se não existe estado, inicializar como false (primeiro clique mostra true primeiro)
        if (this.sortStates[value] === undefined) {
          this.sortStates[value] = false;
        }

        // Alternar o estado da ordenação
        this.sortStates[value] = !this.sortStates[value];

        // Aplicar ordenação baseada no estado atual
        if (this.sortStates[value]) {
          // Estado true: mostrar elementos true primeiro, depois false
          this.charactersSelectors = [].concat(checked).concat(notChecked);
        } else {
          // Estado false: mostrar elementos false primeiro, depois true
          this.charactersSelectors = [].concat(notChecked).concat(checked);
        }
      }

      sortByName(value) 
      {
        this.sortByNameOrder *= -1;
        this.charactersSelectors.sort((a, b) => 
        {
            return this.sortByNameOrder * a[value]?.localeCompare(b[value]);
        });
      }
      
   
   async onChangeCheckbox(value:any, key:string)
  {
    if(value[key] == undefined) value[key] = true;      
    else value[key] = !value[key];
    
    await this._charactersSelectorService.svcToModify(value);
    await this._charactersSelectorService.toSave();
  }

    async onChangeNotes(charSelector:any, description:any)
    {
      if(charSelector.description == undefined) charSelector.description = description;      
      else charSelector.description = description;      
      await this._charactersSelectorService.svcToModify(charSelector); 
      await this._charactersSelectorService.toSave();
    }

    onChangeArea(areaName)
    {
      this.charactersSelectors = [];
      
      if(areaName == 'All')
      {
        this._charactersSelectorService.models.forEach(char => this.charactersSelectors.push(char));
        this.description = `Showing ${this.charactersSelectors.length} results`;

        return;
      }

      let selectedArea = this._areaService.models.filter(area => area.name == areaName)[0];

      let character = this._charactersService.models.filter(char => char.areaId == selectedArea.id)[0];

      this._charactersSelectorService.models.forEach(char => 
        {
          if(char.characterArea == character.areaId)
          {
            this.charactersSelectors.push(char);
          }
        })

        this.description = `Showing ${this.charactersSelectors.length} results`;
      
    }

    search(value)
    {
      if(value == '') 
      {
        this.charactersSelectors = [];
        this._charactersSelectorService.models.forEach(char => this.charactersSelectors.push(char));
      } 

      if(!this.caseSentitive && !this.accentSentitive)
        this.charactersSelectors = this._charactersSelectorService.models.filter(char=> 
          this.simplifyString(char.character)?.includes(this.simplifyString(value)) || 
          this.simplifyString(char.description)?.includes(this.simplifyString(value)));

      else if(this.caseSentitive && this.accentSentitive)
        this.charactersSelectors = this._charactersSelectorService.models.filter(char=> 
          char.character?.includes(value) || char.description?.includes(value));

      else if(this.caseSentitive && !this.accentSentitive)
        this.charactersSelectors = this._charactersSelectorService.models.filter(char=> 
          this.simplifyString(char.character)?.toUpperCase().includes(this.simplifyString(value).toUpperCase()) || 
          this.simplifyString(char.description)?.toUpperCase().includes(this.simplifyString(value).toUpperCase()));

      else if(!this.caseSentitive && this.accentSentitive)
        this.charactersSelectors = this._charactersSelectorService.models.filter(char=> 
          char.character?.toUpperCase().includes(value.toUpperCase()) || 
          char.description?.toUpperCase().includes(value.toUpperCase()));

        this.description = `Showing ${this.charactersSelectors.length} results`;      
    }

    searchConditions(value)
    {
      if(value.caseSensitive == true && value.accentSensitive == false)
      {
        this.caseSentitive = true;
        this.accentSentitive = false;
      }
      else if (value.accentSensitive == true && value.caseSensitive == false)
      {
        this.accentSentitive = true;
        this.caseSentitive = false;
      }
      else if(value.caseSensitive == true  && value.accentSensitive == true )
      {
        this.accentSentitive = true;
        this.caseSentitive = true;
      }
      else
      {
        this.accentSentitive = false;
        this.caseSentitive = false;
      }
    }

    public downloadAsExcel() 
    {
      const characterTable = document.createElement('table');
      const tHead = characterTable.createTHead();
      const tHeadRow = tHead.insertRow();
      tHeadRow.insertCell().innerText = 'Name';
      tHeadRow.insertCell().innerText = 'Notes';
      tHeadRow.insertCell().innerText = 'Concept';
      tHeadRow.insertCell().innerText = 'Separation';
      tHeadRow.insertCell().innerText = 'Pre Finish';
      tHeadRow.insertCell().innerText = 'Finish';
      tHeadRow.insertCell().innerText = 'PostFinnish';
      tHeadRow.insertCell().innerText = 'Rev1';
      tHeadRow.insertCell().innerText = 'Rev2';
      tHeadRow.insertCell().innerText = 'Character Area';
 
      const tBody = characterTable.createTBody();

      this.charactersSelectors.forEach(item =>
      {
        const tBodyRow = tBody.insertRow();
        tBodyRow.insertCell().innerText = item.character;
        tBodyRow.insertCell().innerText = item.description == undefined ? '' : item.description;
        tBodyRow.insertCell().innerText = item.concept ? 'X' : '';
        tBodyRow.insertCell().innerText = item.separation ? 'X' : '';
        tBodyRow.insertCell().innerText = item.preFinish ? 'X' : '';
        tBodyRow.insertCell().innerText = item.finish ? 'X' : '';
        tBodyRow.insertCell().innerText = item.postFinish ? 'X' : '';
        tBodyRow.insertCell().innerText = item.rev1 ? 'X' : '';
        tBodyRow.insertCell().innerText = item.rev2 ? 'X' : '';
        tBodyRow.insertCell().innerText = this._areaService.svcFindById(item.characterArea)?.name;
      })

      const ws: XLSX.WorkSheet = XLSX.utils.table_to_sheet(characterTable);
      const wb: XLSX.WorkBook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'Character Selector List');
  
      const now = new Date();
      XLSX.writeFile(wb, now.toLocaleDateString() + '_' + now.toLocaleTimeString() +' DSAdmin Character Selector List.xlsx');
    }


    async onExcelPaste(): Promise<void> 
    {
      this._spinnerService.setState(true);
      const text = await navigator.clipboard.readText();
      const lines = text.split(/\r?\n/).filter(line => line);
      if(this.DisplayErrors(lines, 9)) return;

      for (let l = 0; l < lines.length; l++) 
      {
        let line = lines[l];
        let cols = line.split(/\t/);
        let item = this.charactersSelectors.find((i) => this.simplifyString(i.character) == this.simplifyString(cols[0]));

        if (!item) continue;
        let character = this.charactersSelectors.find((i) => i.character == item.character);

        if (!character) continue;
  
        if (cols[1]?.trim()) character.description = cols[1];        
        else character.description = undefined;

        let fields = ['concept', 'separation', 'preFinish', 'finish', 'postFinish', 'rev1', 'rev2']

        for(let i = 0; i < fields.length; i++)
        {
          if (cols[2+i]?.trim()) 
          {
            character[fields[i]] = cols[2+i]
            .split(' ')
            .join('')
            .split('.')
            .join('')
            .replace(',','.') == 'X' ? true : false;
          } 
          else character[fields[i]] = undefined;
        }
     
        await this._charactersSelectorService.svcToModify(character);
        await this._charactersSelectorService.toSave();
      }
        this._spinnerService.setState(false);
        this.description = `Showing ${this.charactersSelectors.length} results`;
        this.ngOnInit();
    }

    override simplifyString(str: string): string 
    {
      return (str?.normalize('NFD')?.replace(/[\u0300-\u036f]/g, '')?.toLocaleUpperCase())?.trim();
    }

    DisplayErrors(array, length:number) 
    {
      let count = array[0].split(/\t/);
      if (count.length < length) 
      {
        Alert.showError('Copy ALL '+ length +' columns!');
        this._spinnerService.setState(false);
        return true;
      }
  
      if (count[0] === '') 
      {
        Alert.showError('You are probably copying a blank column!');
        this._spinnerService.setState(false);
        return true;
      }
  
      return false;
    }
}
