.ht:hover .tooltip 
{
    display:block;
}
.tooltip 
{
    display: none;
    color: red;
    margin-left: 28px; /* moves the tooltip to the right */
    margin-top: 15px; /* moves it down */
    position: absolute;
    z-index: 1000;
}

.hover-hand
{
    cursor: pointer !important;
}

.iMission {
    padding: 15px;
    text-align: center;
}

.ball-circle {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #2196F3;
    border: 2px solid #FFFFFF;
    float: right;
  }

/* CSS para estilizar o ícone pe-7s-menu como documento com linhas */
.pe-7s-file {
  display: inline-block;
  transition: transform 0.2s ease; /* Suave transição para hover */
}

/* Efeito hover opcional */
.btn:hover .pe-7s-file {
  transform: scale(1.1); /* Aumenta ligeiramente no hover */
}

