import { Component } from '@angular/core';
import { AreaService, LevelService, ReviewService, UserSettingsService } from 'src/app/services';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc';
import { Button } from 'src/app/lib/@pres-tier/data';
import { Alert } from 'src/lib/darkcloud';
import { Level } from 'src/app/lib/@bus-tier/models';
import { SpinnerService } from 'src/app/spinner/spinner.service';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-level-xp-view',
  templateUrl: './level-xp.component.html',
})
export class LevelXPComponent extends SortableListComponent<Level> {

  constructor(
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    private _levelService: LevelService,
    private _areaService: AreaService,
    private _router: Router,
    private _reviewService: ReviewService,
    private spinnerService: SpinnerService
  )
  {
    super(_levelService, _activatedRoute, _userSettingsService, 'name');
  }

  filteredLevels = [];
  processedLevelIds = [];
  path = 0;
  mainAreas = [];
  selectedArea;
  listLevelxp = [];

  public readonly excelButtonTemplate: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onLevelUpgradesPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  override async lstInit()
  {
    if(!(this.lstIds?.length <= 0))return;
    await this._levelService.toFinishLoading();
    await this._areaService.toFinishLoading();
    this.mainAreas = this._areaService.GetMainAreas();
    this.selectedArea = this.mainAreas[0];  
    this.xpAlertLevel();
    this.lstIds = await this.GetLevelsIds();
    this.lstIds.forEach(lId => {
    this.filteredLevels.push(this._levelService.svcFindById(lId));   
    }); 
  
  }

  async GetLevelsIds()
  {
    await this._areaService.toFinishLoading();
    await this._levelService.toFinishLoading();  
    this.path = 0;
    let subAreas = this._areaService.GetSubAreas(this.selectedArea);
    let levels = [];
    this.selectedArea?.levelIds.forEach(levelId =>{
      let level = this._levelService.svcFindById(levelId);
      if(level.type != 3) levels.push(level);
    });
    subAreas.forEach(area => {
      area?.levelIds.forEach(levelId =>{
        let level = this._levelService.svcFindById(levelId);
        if(level.type != 3) levels.push(level);
      });
    });
    this.processedLevelIds = [];
    levels.forEach(level => {
      this.processLevelId(level, levels);
    });

    return this.processedLevelIds;
  }

  xpAlertLevel() {
    
     if(this._reviewService.objIdsToReview['Stats'].length > 0) {
      Alert.showMessage(`Levels with no designated XP`)
     }    
  }

  async processLevelId(level: Level, levels: Level[])
  {
    await this._levelService.toFinishLoading();
    
    if(!level)return;
    if(!(levels.map(l => l?.id).includes(level?.id)))
    {
      let l = levels.find(l => l.pathOrder?.includes(this.path));
      if(!l)return;
      let index = l.pathOrder.indexOf(this.path);
      l = this._levelService.svcFindById(l.linkedLevelIds[index]);
      this.path++;
      this.processLevelId(l, levels);
      return;
    }
    if(this.processedLevelIds.includes(level?.id))
    {
      let l = levels.find(l => l.pathOrder?.includes(this.path));
      if(!l)return;
      let index = l.pathOrder.indexOf(this.path);
      l = this._levelService.svcFindById(l.linkedLevelIds[index]);
      this.path++;
      this.processLevelId(l, levels);
      return;
    }
    this.processedLevelIds.push(level?.id);
    if(level.linkedLevelIds.length == 0)
    {
      let l = levels.find(l => l.pathOrder?.includes(this.path));
      if(!l)return;
      let index = l.pathOrder.indexOf(this.path);
      l = this._levelService.svcFindById(l.linkedLevelIds[index]);
      this.path++;
      this.processLevelId(l, levels);
      return;
    }
    if(level.linkedLevelIds.length > 1)
    {
      let l = levels.find(l => l.pathOrder?.includes(this.path));
      if(!l)return;
      let index = l.pathOrder.indexOf(this.path);
      l = this._levelService.svcFindById(l.linkedLevelIds[index]);
      this.path++;
      this.processLevelId(l, levels);
      return;
    }
    else
    {
      this.processLevelId(this._levelService.svcFindById(level.linkedLevelIds[0]), levels);
    }
  }

  async onLevelUpgradesPaste(): Promise<void> {
   //this.spinnerService.setState(true)
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/);
    
    for(let l = 0; l < lines.length; l++)
    {
      let line = lines[l];      
      let rows = line.split(/\t/);      
      this.AddLevelXP(l, +(rows[rows.length -1]
        .split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',', '.')
        .replace('%', '')
      ));
    }
    this.lstIds = await this.GetLevelsIds();
    this.filteredLevels = []
    this.lstIds.forEach(lId => {this.filteredLevels.push(this._levelService.svcFindById(lId)) });
    Alert.ShowSuccess('Level XP imported successfully!');
    
    //this.spinnerService.setState(false)
  }

  async AddLevelXP(index: number, xp: number)
  {
    let level = this.filteredLevels[index];
    if(level)
    {
      level.xp = xp;
      this._levelService.svcToModify(level);
    }
  }

  public changeLevelXP(level: Level, value: string)
  {
    level.xp = value == '' ? undefined : +value;
    this._levelService.svcToModify(level);
    this._levelService.toSave();
  }

  public async SelectArea(areaId: any)
  {
    await this._levelService.toFinishLoading();
    await this._areaService.toFinishLoading();

    this.selectedArea = this._areaService.svcFindById(areaId.target.value);
    this.lstIds = await this.GetLevelsIds();
    this.filteredLevels = []
    this.lstIds.forEach(lId => {
      this.filteredLevels.push(this._levelService.svcFindById(lId));
    });  
  }


  DisplayErrors(array)
  {
    let count = array[0].split(/\t/)
    if(count.length < 5)
    {
      Alert.showError("Copy the ORDER or MASTERY EXTREMES column values too!")
      return true
    }
    
    if(count[0] === "")
    {
      Alert.showError("You are probably copying a blank column!")
      return true
    }

    return false
  }
}
