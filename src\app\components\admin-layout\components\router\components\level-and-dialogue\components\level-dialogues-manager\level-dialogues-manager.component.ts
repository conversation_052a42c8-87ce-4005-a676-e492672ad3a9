import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Area, Character, Dialogue, Level } from 'src/app/lib/@bus-tier/models';
import { StoryExpansionPkg } from 'src/app/lib/@bus-tier/models/StoryExpansionPkg';
import { Button } from 'src/app/lib/@pres-tier/data';
import { DialogueService, EventService, MarkerService, ReviewService } from 'src/app/services';
import { AreaService } from 'src/app/services/area.service';
import { CharacterService } from 'src/app/services/character.service';
import { LevelService } from 'src/app/services/level.service';
import { PopupService } from 'src/app/services/popup.service';
import { RoadBlockService } from 'src/app/services/road-block.service';
import { StoryExpansionPkgService } from 'src/app/services/story-expansion-pkg.service';
import { TranslationService } from 'src/app/services/translation.service';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Alert, Popup } from 'src/lib/darkcloud';
import { PreloadComponent } from 'src/lib/darkcloud/angular/easy-mvc/PreloadComponent';
import { CharacterType, DialogueType, GameTypes, LevelType } from 'src/lib/darkcloud/dialogue-system';

export interface Preloadable 
{
  dialogues: Dialogue[];
  level: Level;
}

@Component({
  selector: 'app-level-dialogues-manager',
  templateUrl: './level-dialogues-manager.component.html',
  styleUrls: ['./level-dialogues-manager.component.scss']
})

export class LevelDialoguesManagerComponent extends PreloadComponent<Preloadable> implements OnInit
{
  public readonly LevelType = LevelType;
  public readonly DialogueType = DialogueType;
  canChangeBossButtonColor: boolean = false;
  canChangeSpecialItemButtonColor: boolean = false;
  blockBossPhrase: string = 'Block Grind';
  removeSpecialItemPhrase: string = 'Remove Special Item On Grind';
  public readonly headerDescription = 'List of Dialogs';
  public readonly headerRightButtonTemplates: Button.Templateable[] = [
    {
      title: 'Copy dialogues',
      onClick: this.onCopyDialogues.bind(this),
      iconClass: 'pe-7s-copy-file',
      btnClass: Button.Klasses.FILL_BLUE,
    },
    {
      title: 'Paste dialogues',
      onClick: this.onPasteDialoguesAsync.bind(this),
      iconClass: 'pe-7s-note2',
      btnClass: Button.Klasses.FILL_ORANGE,
    },
  ];

  level : Level = undefined;
  tempLevel : Level = undefined;
  public get dialogues(): Dialogue[] 
  {
    return this.preloadedData.dialogues;
  }

  constructor(
    private _levelService: LevelService,
    private _popupService: PopupService,
    private _areaService: AreaService,
    private _characterService: CharacterService,
    private _markerService : MarkerService,
    private _router: Router,
    public override readonly activatedRoute: ActivatedRoute,
    private _change: ChangeDetectorRef,
    private _dialogueService : DialogueService,
    readonly userSettingsService: UserSettingsService,
    private _translationService: TranslationService,
    private _reviewService : ReviewService,
    private _eventService: EventService,
    private _storyExpansionService: StoryExpansionPkgService,	
    private _roadblockService: RoadBlockService
  ) 
  {
    super(activatedRoute);
  }

  public async ngOnInit(): Promise<void> 
  {
    this.level = this._levelService.svcFindById(this._router.url.split("/")[2]);
    this._levelService.lastModifiedId = this.level.id;
    this.changeBlockgrindButtonColor();
    this.changeSpecialItemOnGrindButtonColor();
  }

  getLevelDialogues()
  {
    for(let i = 0; i < this._dialogueService.models.length; i++)
    {
      let dialogueId: string = this._dialogueService.models[i].id.split('.')[0] + '.'+ this._dialogueService.models[i].id.split('.')[1];
      if(this.level.id == dialogueId)
      {
        this.dialogues.push(this._dialogueService.models[i]);
      }
    }
  }

  sortDialoguesByType(dialogues: Dialogue[]): Dialogue[]
  {
    let sortingArr: number[] = [0, 1, 4, 3,  2]
    return dialogues.sort((a, b) => sortingArr.indexOf(a.type) - sortingArr.indexOf(b.type));
  }

  public onCardBackButtonClick(): void 
  {
    this._levelService.lastModifiedId = this.level.id;
    this._router.navigate(['levels']);
  }

  public onCopyDialogues(): void 
  {
    this._levelService.copyLevelDialogues(this.level);
    Alert.ShowSuccess('Copied ' + this.level.id + ' dialogues.');
  }

  async onPasteDialoguesAsync(): Promise<void> 
  {
    const wasOverwritten = await this._levelService.pasteLevelDialogues2(this.level, this.dialogues);
    if (!wasOverwritten) return;

    // refresh component
    this._router.navigateByUrl('/levels', 
    {
        skipLocationChange: true,
    })
    .then(() => 
    {
      this._router.navigate(['levels/' + this.level.id + '/dialogues']);
    });
  }

  public trackByIndex(index: number, dialogue: Dialogue): any 
  {
    return index;
  }

  changeBlockgrindButtonColor()
  {
    if(this.level.blockGrind == undefined)
    {
      this.canChangeBossButtonColor = false;
      return;
    }

    for(let i = 0; i < this._markerService.models.length; i++)
    {
      if(this.isMarkerTypeBlockGrind(i) && this.isMarkerTypeFromThisLevel(i))
      {
        const dialogueId = Dialogue.getSubIdFrom(this._markerService.models[i].id.split('.mrk')[0], 'PT-BR');
        this.blockBossPhrase = this.setupButtonTitlePhrase(dialogueId, this.blockBossPhrase);
        this.canChangeBossButtonColor = true;
        break;
      }
    }
  }

  isMarkerTypeBlockGrind(index: number): boolean
  {
    return this._markerService.models[index].type == 12;
  }

  isMarkerTypeFromThisLevel(index : number) : boolean
  {
    return this._markerService.models[index]?.id?.split('.D')[0] == this.level?.id?.trim()
  }

  changeSpecialItemOnGrindButtonColor()
  {
    let counter = 0; 
    for(let i = 0; i < this._eventService.models.length; i++)
    {
      let id = this._eventService.models[i]?.id?.split('.')[0] + '.' + this._eventService.models[i]?.id?.split('.')[1];

      if(this.isLevelSpecialItemOnGrindType(i, id))
      {
        counter = counter + 1;
        let s = this._eventService.models[i].id.split('.')[0] + '.' + this._eventService.models[i].id.split('.')[1] + '.' +
        this._eventService.models[i].id.split('.')[2];
        const dialogueId = Dialogue.getSubIdFrom(s, 'PT-BR');
        this.removeSpecialItemPhrase = this.setupButtonTitlePhrase(dialogueId, this.removeSpecialItemPhrase);
        if(counter >= 1) break;        
      }
    }
    if(counter >= 1)
    {
      this.canChangeSpecialItemButtonColor = true;
      this.level.removeSpecialItemBeforeGrind = true;
    }     
  }

  isLevelSpecialItemOnGrindType(index: number, id:string):boolean
  {
    return +this._eventService.models[index].type == 12 && id == this.level?.id;
  }

  setupButtonTitlePhrase(areaID, phrase: string) : string
  {
    const areaId = Area.getSubIdFrom(areaID);
    const area = this._areaService.svcFindById(areaId);
    if (!area) return null;
    
    const levelId = Level.getSubIdFrom(areaID);
    const level = this.level;
    
    const dialogueId = Dialogue.getSubIdFrom(areaID, 'PT-BR');
    const dialogue = this._dialogueService.svcFindById(dialogueId);
    const hierarchyCode = this._areaService.svcFindById(areaId).hierarchyCode;
    let levelIndex = this._reviewService.reviewResults[levelId]?.index
    let type = GameTypes.dialogueTypeDisplay[+dialogue.type]
    phrase += ' ::: ' + '['+hierarchyCode+'] ' + levelIndex + ' "' + level.name + '"' + ' ' + '(' +type + ')';
    return phrase;
  }

  downloadTextContent(dialogue: Dialogue)
  {
    this._translationService.getDialogueOrtography(dialogue, true);
  }

  public redirectToDialogue(dialogue: Dialogue) 
  {
    Alert.showLoadingPleaseWiat();
    setTimeout(() => {        
    this._router.navigate([
      'levels/' + this.level.id + '/dialogues/' + dialogue.id,
    ]);
    }, 200);  

  }

  public async addStoryExpansionContent(levelId: string)	
  {	
    let storyExp : StoryExpansionPkg = this.verifyLevelidInsideStoryexpansionpkg(levelId);	
    if(storyExp != undefined)	
    {	
      //1.1 Se existir e for igual, remove esse e NAO adiciona nada.
      if(storyExp.level == levelId)
      {
        this._storyExpansionService.svcToRemove(storyExp.id);
      }
      //1.2 Se existir e for diferente desse, remove o velho e adiciona esse.
      else 
      {
        let canRemove = await this._storyExpansionService.toPromptRemove(storyExp, storyExp.id);	
        if(canRemove)	
        {	
           const area = this._areaService.svcFindById(Area.getSubIdFrom(levelId));	
           this._storyExpansionService.createNewExpansionPkg(levelId, 1, area.id);	
           this.ngOnInit();	
          
        }
      }
    }	
    else	
    {	
      //1.2 Se NAO existir cria novo 	
      const area = this._areaService.svcFindById(Area.getSubIdFrom(levelId));	
      this._storyExpansionService.createNewExpansionPkg(levelId, 1,  area.id);	
    }	
    this.updatePage();	
  }

  updatePage()	
  {	
    this._router.navigate(['levels/']);	
    setTimeout(()=>	
    {	
        this._router.navigate(['levels/' + this.level.id + '/dialogues']);	
    },300)	
  }	


  verifyLevelidInsideStoryexpansionpkg(levelId:string)	
  {	
    //1. Verificar se o id ja existe nos models	
    for(let i = 0; i < this._storyExpansionService.models.length; i++)	
    {	
      if(this._storyExpansionService.models[i]?.level == levelId || 	
        this._storyExpansionService.models[i]?.level.split('.D')[0] == levelId ||	
        this._storyExpansionService.models[i]?.level.split('.D')[0] == levelId.split('.D')[0])	
        {	
          return this._storyExpansionService.models[i];	
        }	
    }	
    return undefined;	
  }	


  public async removeSpecialItemBeforeGrind() 
  {
    this.level.removeSpecialItemBeforeGrind = !this.level.removeSpecialItemBeforeGrind;
    if(this.level.removeSpecialItemBeforeGrind == undefined)
    {
      this.level.removeSpecialItemBeforeGrind = true;
    }
    await this._levelService.svcToModify(this.level);
  }
    
  public async blockGrind() 
  {
    this.level.blockGrind = this.level.blockGrind ? false : true;
    await this._levelService.svcToModify(this.level);
    this.changeSpecialItemOnGrindButtonColor();
  }

  public async toPromptAddBattleCharacterToLevel() 
  {
    const selectedCharacterButton = await this._popupService.fire<Area, Character>(
      new Popup.Interface(
        {
          title: 'Select Area',
          actionsClass: 'column',
        },
        Popup.toButtonList(
          this._areaService.models,
          {
            parameters: ['hierarchyCode', 'name'],
            mapping: '[<hierarchyCode>]: <name>',
          },
          { undefinedTitle: 'No Area' }
        ),
        {
          inputButton: 
          {
            value: this._areaService.svcFindById(
              Area.getSubIdFrom(this.level.id)
            ),
          },
          next: (selectedAreaButton) => 
          {
            if (!selectedAreaButton) return null;
            
            let characters = +this.level.type === LevelType.BOSS
                ? this._characterService.filterByType(CharacterType.BOSS, CharacterType.SUBBOSS)
                : this._characterService.filterByType(CharacterType.MINION, CharacterType.NPC);

            characters = characters.filter((character) => character.areaId === selectedAreaButton.value?.id);

            return new Popup.Interface<Character, Character>(
              {
                title: 'Select Character',
                actionsClass: 'column',
              },
              Popup.toButtonList(characters, 'name', 
              {
                classEnum: GameTypes.characterTypeName,
                classParameter: 'type',
              })
            );
          },
        }
      )
    );

    if (!selectedCharacterButton) return;

    this._levelService.addBattleCharacter(this.level, selectedCharacterButton.value.id);
    this._characterService.svcReviewById(selectedCharacterButton.value.id);
    setTimeout(() => {
      this._change.detectChanges();
    }, 100);
    this.ngOnInit();
      setTimeout(() => {
      this._change.detectChanges();
    }, 100);
  }
}
