import { ChangeDetectorRef, Component } from '@angular/core';
import { Router } from '@angular/router';
import { OpenAIEnvironment } from 'src/app/lib/@bus-tier/models/OpenAIEnvironment';
import { Button } from 'src/app/lib/@pres-tier/data';
import { OpenAIEnvironmentService, OpenAIKeyGeneralService, OpenAIModelAPIService } from 'src/app/services';
import { catchError } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { OpenAIKeyGeneral } from 'src/app/lib/@bus-tier/models';
import { Alert } from 'src/lib/darkcloud';

@Component({
  selector: 'app-open-ai-environment',
  templateUrl: './open-ai-environment.component.html',
  styleUrls: ['./open-ai-environment.component.scss']
})
export class OpenAiEnvironmentComponent {

  title = "Environments for AI";
  titleModal = 'Create new environment for AI';
  titleModalEdit = 'Edit environment for AI';
  error401 = 'Request to the server was not authorized due to authentication problems. \n Add a Primary KEY.';
  errorKey = 'Wrong key. Add a correct key.';
  titlditKeyGeneral = 'Edit Primary KEY';
  models: any[] = [];
  selectedModel: any = null;
  name: string = '';
  apiKey:string = '';
  titleError401:string = '';
  titleModalEditKeyGeneral:string = '';
  titelErrorKey:string = '';
  temperature: number = 0.7;
  limitForReview: number = 3;
  listEnvironment: OpenAIEnvironment[] = [];
  listKeyGeneral: OpenAIKeyGeneral[] = [];
  createdEnvironment: OpenAIEnvironment;
  createdKeyGeneral: OpenAIKeyGeneral;
  editedEnvironment: OpenAIEnvironment;
  editedKeyGeneral: OpenAIKeyGeneral;
  popupStats = false;
  isEdit = false;
  isEditKeyGeneral = false;
  withoutGeneralKEY= false;
  openModalGeneralKey = false;
  isCreatedEnvironment = false;

  constructor(
    private _router: Router,
    private _openAIModelAPIService: OpenAIModelAPIService,
    private _openAIEnvironmentService: OpenAIEnvironmentService,
    private _openAIKeyGeneralService: OpenAIKeyGeneralService,
    private _change: ChangeDetectorRef,
  ) { }
    
    public readonly addEnvironmentAI: Button.Templateable = 
    {
      title: 'Create new environment for AI',
      onClick: this.addOpenEnvironmentAI.bind(this),
      iconClass: 'pe-7s-plus',
      btnClass: Button.Klasses.FILL_GREEN,
    };
  
    
  ngOnInit(): void {

  this.listKeyGeneral = this._openAIKeyGeneralService.models;

  setTimeout(() => {
    this._openAIModelAPIService.getAvailableModels().pipe(
      catchError(err => {
        const statusCode = err.status;
        const errorMessage = err.message;
        if (statusCode === 404) {
          console.error('Modelos não encontrados:', errorMessage);
        } else if (statusCode === 500) {
          Alert.showError(errorMessage, 'OpenAi Server Error!');
          console.error('Erro interno do servidor:', errorMessage);
        } else {          
          this.erroKey();
          console.error('Erro desconhecido:', errorMessage);
        }
        return throwError(err);
      })
    ).subscribe({
      next: (response) => {
        this.models = response.data.filter(model => model.id.includes('gpt'));
      }
    });
    this.listEnvironment = this._openAIEnvironmentService.models;
  },200)   
    
    console.log('Lista listKeyGeneral:', this.listKeyGeneral);
    console.log('Lista Environment:', this.listEnvironment);
  }

erroKey() {
    if (this.listKeyGeneral.length > 0) {          
      this.name = this.listKeyGeneral[0].name;
      this.apiKey = this.listKeyGeneral[0].apiKey;
      this.titelErrorKey = this.errorKey;          
  }
  else {    
    this.titleError401 = this.error401;
  }
  this.withoutGeneralKEY = true; 
  this.openModalGeneralKey = true;
  }
  
submitForm() { 
  const formData = {
    name: this.name,
    model: this.selectedModel,
    apiKey: this.apiKey,
    temperature: this.temperature,
    limitForReview: this.limitForReview,
  };

  if (this.isCreatedEnvironment) { //Create
    this.createdEnvironment = this._openAIEnvironmentService.createNewOpenAIEnvironment();          
    this.createdEnvironment.name = formData?.name;
    this.createdEnvironment.apiKey = formData?.apiKey;
    this.createdEnvironment.model = formData?.model;
    this.createdEnvironment.temperature = formData?.temperature;
    this.createdEnvironment.limitForReview = formData?.limitForReview;    
    this._openAIEnvironmentService.svcToModify(this.createdEnvironment);
    this.isCreatedEnvironment = false;
   } 
  if(this.isEdit) {// Edit
    console.log('EditedEnvironment', this.editedEnvironment);
    this.editedEnvironment.name = formData?.name;
    this.editedEnvironment.apiKey = formData?.apiKey;
    this.editedEnvironment.model = formData?.model;
    this.editedEnvironment.temperature = formData?.temperature;
    this.editedEnvironment.limitForReview = formData?.limitForReview;
    this._openAIEnvironmentService.svcToModify(this.editedEnvironment);
    this.isEdit = false;  
  }  

  this.popupStats = false;  
  this.listEnvironment = this._openAIEnvironmentService.models;      
  this._change.detectChanges();
}



submitFormKeyGeneral(): void { 
  
      const formDataGernal = {
         name: this.name,
         apiKey: this.apiKey, 
       };

   if (this.withoutGeneralKEY && this.titleError401 != '') { //Create
          this.createdKeyGeneral = this._openAIKeyGeneralService.createNewOpenAIKeyGeneral();          
          this.createdKeyGeneral.name = formDataGernal?.name;
          this.createdKeyGeneral.apiKey = formDataGernal?.apiKey;
          this._openAIKeyGeneralService.svcToModify(this.createdKeyGeneral); 
         } 
   else {// Edit
          console.log('formDataGernal:', formDataGernal);
          this.listKeyGeneral[0].name = formDataGernal?.name;
          this.listKeyGeneral[0].apiKey = formDataGernal?.apiKey;  
          this._openAIKeyGeneralService.svcToModify(this.listKeyGeneral[0]);
          this.isEditKeyGeneral = false;           
         }  
         this.titleError401 = '';
         this.titelErrorKey = '';
         this.titleModalEditKeyGeneral = '';
         this.openModalGeneralKey = false;
         this.withoutGeneralKEY = false;    
       
    setTimeout(() => {
      this.ngOnInit();
    },300)

  }

  public redirectToSettings() {
    this._router.navigate(['settings']);
  }

  async addOpenEnvironmentAI() {
    this.cleanForm();
    this.cleanGeneralKeyForm();

    if (this.withoutGeneralKEY) {
      this.titelErrorKey = 'Please add a correct Primary Key before creating or editing the environment key.';
      this.erroKey();
    } 
    else {
      this.temperature = 0.7;
      this.limitForReview = 3;
      this.isCreatedEnvironment = true;
      this.popupStats = true;  
    }
  }
  
closeModal() {
  this.popupStats = false;
  this.isEdit = false; 
  this.cleanForm();
}

closeModalKeyGeneral() {
  this.openModalGeneralKey = false;
  this.isEditKeyGeneral = false;
  this.cleanGeneralKeyForm();
}

editEnvironment(id: string) {
  this.cleanForm();

  if (this.withoutGeneralKEY) {
    this.titelErrorKey = 'Please add a correct Primary Key before creating or editing the environment key.';
    this.erroKey();
  } 
  else {
    this.editedEnvironment = this.listEnvironment.find(item => item.id === id);

    if (this.editedEnvironment) {
      this.name = this.editedEnvironment.name;
      this.apiKey = this.editedEnvironment.apiKey;
      this.selectedModel = this.editedEnvironment.model;
      this.temperature = this.editedEnvironment.temperature;
      this.limitForReview = this.editedEnvironment.limitForReview;
      this.isEdit = true;
      this.isCreatedEnvironment = false;
      this.popupStats = true;
    }
  }
}

deleteEnvironment(id: string) {
  this._openAIEnvironmentService.svcToRemove(id);
  this.listEnvironment = this.listEnvironment.filter(item => item.id !== id);
  this.ngOnInit(); 
}

//KEY GENERAL
editKeyGeneral(id: string) {
  this.editedKeyGeneral = this.listKeyGeneral.find(item => item.id === id);

  if (this.editedKeyGeneral) {
    this.name = this.editedKeyGeneral.name;
    this.apiKey = this.editedKeyGeneral.apiKey;
    this.isEditKeyGeneral = true;
   this.titleModalEditKeyGeneral = this.titlditKeyGeneral
  //  this.withoutGeneralKEY = true;
  }
}

deleteKeyGeneral(id: string) {
  this._openAIKeyGeneralService.svcToRemove(id);
  this.listKeyGeneral = this.listKeyGeneral.filter(item => item.id !== id);
  this.ngOnInit(); 
}


cleanForm() {
  // Limpar os campos do formulário
  this.name = '';
  this.apiKey = '';
  this.selectedModel = null;
  this.editedEnvironment = null;
}
cleanGeneralKeyForm() {
  this.name = '';
  this.apiKey = ''; 
}

}
