<div class="main-menu-efect">
  <div class="container-fluid">
    <div class="list-header-row update">
      <div class="card">
        <div style="display: flex; justify-content: space-between; margin-top: 15px; margin-bottom: 15px;">
          <div class="card-header-content">
            <div style="display: flex; gap: 7px;">
              <h3 class="title">RPC</h3>
              <h5>(Rendição Pré-Combate)</h5>
            </div>            
            <p style="width:60vw;" class="category">{{ description}}</p>
          </div>
        </div>
      </div>
    </div>

    <!--TABLE OF TRIBUTE AND SUBMISSION -->
      <div class="card paddingTop"  *ngIf="rpcTableTributeAndSubmission.length > 0">
        <div class="table-header">
          <h3>Table of Tribute and Submission</h3>
            <div class="addButton">
               <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'" 
                      [buttonTemplates]="[excelButtonSubmission]">
              </app-button-group>
            </div>
        </div>
        <div class="rpc-table-container">
          <table class="rpc-table">
            <thead>
              <tr>
                <th rowspan="2">Index</th>
                <th rowspan="2">E</th>
                <th rowspan="2">Tone</th>
                <th rowspan="2">Description</th>
                <th colspan="3">Phrase Options</th>
              </tr>
              <tr>
                <th>A</th>
                <th>B</th>
                <th>C</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let item of rpcTableTributeAndSubmission; let i = index" class="rpc-row">
                <td class="rpc-cell" style="background-color: #ddd;">{{ i + 1 }}</td>
                <td class="rpc-cell">{{ item.valueE || '' }}</td>
             <td class="rpc-cell text-left" [ngClass]="{'empty-td': !item.tone}">{{ item.tone || '' }}</td>
                <td class="rpc-cell description-cell text-left" [ngClass]="{'empty-td': !item.description}">{{ item.description || '' }}</td>
                <td class="rpc-cell text-left" [ngClass]="{'empty-td': !item.phraseOptionsA}">{{ item.phraseOptionsA || '' }}</td>
                <td class="rpc-cell text-left" [ngClass]="{'empty-td': !item.phraseOptionsB}">{{ item.phraseOptionsB || '' }}</td>
                <td class="rpc-cell text-left" [ngClass]="{'empty-td': !item.phraseOptionsC}">{{ item.phraseOptionsC || '' }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- ESCAPE TABLE -->
      <div class="card paddingTop"  *ngIf="rpcCEscapeTable.length > 0">
        <div class="table-header">
          <h3>Escape Table</h3>
          <div class="addButton">
                 <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'" 
                      [buttonTemplates]="[excelButtonEscale]">
                </app-button-group>
            </div>
        </div>
        <div class="rpc-table-container">
          <table class="rpc-table">
            <thead>
              <tr>
                <th rowspan="2">Index</th>
                <th rowspan="2">E</th>
                <th rowspan="2">Tone</th>
                <th rowspan="2">Description</th>
                <th colspan="3">Phrase Options</th>
              </tr>
              <tr>
                <th>A</th>
                <th>B</th>
                <th>C</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let item of rpcCEscapeTable; let i = index" class="rpc-row">
                <td class="rpc-cell" style="background-color: #ddd;">{{ i + 1 }}</td>
                <td class="rpc-cell">{{ item.valueE || '' }}</td>
                <td class="rpc-cell text-left" [ngClass]="{'empty-td': !item.tone}">{{ item.tone || '' }}</td>
                <td class="rpc-cell description-cell text-left" [ngClass]="{'empty-td': !item.description}">{{ item.description || '' }}</td>
                <td class="rpc-cell text-left" [ngClass]="{'empty-td': !item.phraseOptionsA}">{{ item.phraseOptionsA || '' }}</td>
                <td class="rpc-cell text-left" [ngClass]="{'empty-td': !item.phraseOptionsB}">{{ item.phraseOptionsB || '' }}</td>
                <td class="rpc-cell text-left" [ngClass]="{'empty-td': !item.phraseOptionsC}">{{ item.phraseOptionsC || '' }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>


        <ng-container *ngIf="rpcTableTributeAndSubmission.length === 0">
      <div class="card" style="text-align: center; padding: 20px;">
        <h3>Empty list of <span>Table of Tribute and Submission</span>. Click to create list</h3>
           <div class="btn-excel">
                 <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'" 
                      [buttonTemplates]="[excelButtonSubmission]">
                </app-button-group>
           </div>
      </div>
    </ng-container>
  

      <ng-container *ngIf="rpcCEscapeTable.length === 0">
      <div class="card" style="text-align: center; padding: 20px;">
        <h3>Empty list of <span>Escape Table</span>. Click to create list</h3>
           <div class="btn-excel-boos">
                 <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'" 
                      [buttonTemplates]="[excelButtonEscale]">
                </app-button-group>
           </div>
      </div>
    </ng-container>

    </div>
  </div>
