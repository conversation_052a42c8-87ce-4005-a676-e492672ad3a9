import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ConfigThreshold, Dilemma } from 'src/app/lib/@bus-tier/models';
import { Atributte } from 'src/app/lib/@bus-tier/models/Atributte';
import { ConfigThresholdService, DilemmaService, UserSettingsService } from 'src/app/services';
import { AtributteService } from 'src/app/services/atributte.service';
import { RoadBlockService } from 'src/app/services/road-block.service';
import { SortingService } from 'src/app/services/sorting.service';
import { AtributteDilemma } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard/IDilemma';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc';
import { comparable } from 'src/lib/others';

@Component({
  selector: 'app-dilemma-report',
  templateUrl: './dilemma-report.component.html',
  styleUrls: ['./dilemma-report.component.scss']
})
export class DilemmaReportComponent extends SortableListComponent<Dilemma> implements OnInit {

  dilemmaBoxes: Dilemma[] = [];
  atributtoClasses: Atributte[] = [];
  thresholdOptions: ConfigThreshold[] = [];
  totalValuePoints: AtributteDilemma [] = [];
  reverseLocation: boolean = false;

  constructor( 
    private _dilemmaService: DilemmaService,
    private _atributteService: AtributteService,
    private _roadblockService: RoadBlockService,
    public readonly router: Router,
    private _configThresholdService: ConfigThresholdService,
    private _sorting : SortingService,
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
  ) {
    super(_dilemmaService, _activatedRoute, _userSettingsService, 'name');
  }

  override async ngOnInit() {    
    this._configThresholdService.toFinishLoading();
    this.thresholdOptions = this._configThresholdService.models;
    await this._atributteService.toFinishLoading();
    this.atributtoClasses = this._atributteService.models;
/*
    this._dilemmaService.models = [];
    this._dilemmaService.toSave();
*/
    const dilemmas = this._dilemmaService.models.filter(x => x.threshold != undefined || x.message != undefined);
    dilemmas.forEach(x =>  this.dilemmaBoxes.push(x) );

    this.sortArrayByLocation(this.dilemmaBoxes);
    
    // Verifica se os atributos foram carregados antes de chamar checkDilemmaPoints
    /*if (this.atributtoClasses && this.atributtoClasses.length > 0 ) {
        this.checkDilemmaPoints();
    } else {
        console.warn('Atributos não foram carregados corretamente.');
    }
    */
}

checkDilemmaPoints() {
    this.dilemmaBoxes.forEach((item: any) => {
        if (!item.points) {
            item.points = []; // Inicializa o array 'points' se estiver undefined
        }

        for (let i = 0; i < this.atributtoClasses.length; i++) {
            if (!item.points[i]) {
                item.points.push({
                    idAtributte: '',
                    nameAtributte: '',
                    valuePoints: 0
                });
            }
        }
    });
}


  access(id: string)
  {
    let levelId = this._roadblockService.getLevelId(id);
    let dialogueId = this._roadblockService.getDialogueId(id);

      this.router.navigate([
        'levels/' + levelId +
        '/dialogues/' + dialogueId
      ],
      {fragment: id});
   
  }

  sortArrayByType(array: Dilemma[]) {	
  }	
  sortArrayByLocation(array: Dilemma[]){  
    array.sort((a, b) => {	
      return this._sorting.sortArrayByLevelId(this.reverseLocation, a.id, b.id)	
    });	
    this.reverseLocation = !this.reverseLocation;	
  }

  private isSortedByMessage: boolean = false;
  sortArrayByDilemma(array: Dilemma[]){  
    array.sort((a, b) => {
      let messageA = comparable(a.message);
      let messageB = comparable(b.message);

      if (!messageA || !messageB) return 0;

      if (messageA > messageB) return this.isSortedByMessage ? 1 : -1;
      else if (messageB > messageA) return this.isSortedByMessage ? -1 : 1;

      return 0;
    });

    this.isSortedByMessage = !this.isSortedByMessage; 
  }

private isAscendingOrder: boolean = true;

sortArrayByAtributto(dilemma: Dilemma[], id: string) {
    dilemma.sort((a, b) => {
        // Encontra o valor de 'valuePoints' para o 'idAtributte' correspondente no item 'a'
        const aValue = a.points.find(point => point.idAtributte === id)?.valuePoints ?? 0;
        // Encontra o valor de 'valuePoints' para o 'idAtributte' correspondente no item 'b'
        const bValue = b.points.find(point => point.idAtributte === id)?.valuePoints ?? 0;

        // Ordena com base no valor de 'valuePoints', levando em consideração a ordem atual
        if (this.isAscendingOrder) {
            return aValue - bValue; // Ordem crescente
        } else {
            return bValue - aValue; // Ordem decrescente
        }
    });

    // Alterna o estado da ordenação para a próxima vez que for chamado
    this.isAscendingOrder = !this.isAscendingOrder;
}

  sortArrayByThreshold(dilemma: Dilemma[]) {

    dilemma.forEach((item: any) => {
      if(!item.threshold) {
        item.threshold = this.thresholdOptions[0]?.valueThreshold;
      }      
    });

    dilemma.sort((a, b) => {
 
      const aValue = a.threshold ?? 0;

      const bValue = b.threshold ?? 0;

      if (this.isAscendingOrder) {
          return aValue - bValue; // Ordem crescente
      } else {
          return bValue - aValue; // Ordem decrescente
      }
  });
  this.isAscendingOrder = !this.isAscendingOrder;
    
  }

}
