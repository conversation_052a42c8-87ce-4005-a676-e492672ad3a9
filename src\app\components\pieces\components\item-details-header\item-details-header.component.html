<div class="col-md-12" style="display: contents">

  <div class="header card-header-wrapper">
    <app-back-button *ngIf="isBackButtonEnabled" (buttonClick)="cardBackButtonClick.emit()">
    </app-back-button>

      <div class="card-header-content" [ngClass]="{'card-title-desccription': textDescriptionRecord}">
        <h3 class="title">{{ cardTitle }}</h3>
        <div [ngClass]="{'text-description': textDescriptionRecord}">
          <p style="width:60vw;" class="category">{{ cardDescription }}</p>
        </div>     
      </div> 

    <div class="textTitles">
      <ng-container *ngIf="valueBossLevel">
        <div>
          <h3 class="title">{{titleBossLevel}} </h3>
        </div>
      </ng-container>
      <ng-container *ngIf="nameClass">
        <div style="margin-left: 10px;">
          <h3 class="title">{{nameClass}} - </h3>
        </div>
      </ng-container>
      <ng-container *ngIf="nameRarity">
        <div class="c-nameRarity"
          [ngStyle]="{'background-color': nameRarity | tierColor : 'Character Rarity', 'color':'#fff'}">
          <h3 class="title">{{nameRarity}}</h3>
        </div>
      </ng-container>
      <ng-container *ngIf="type">
        <div style="margin-left: 10px;">
          <h3 class="title"> - {{type}}</h3>
        </div>
      </ng-container>
    </div>

    <app-button-group *ngIf="activeLanguage === 'PTBR'|| activeLanguage === 'PT-BR'"
      [buttonTemplates]="leftButtonTemplates">
    </app-button-group>

    <!-- Fixed positioned button group for item details -->
    <div class="item-details-buttons">
      <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'"
        [buttonTemplates]="rightButtonTemplates">
      </app-button-group>
    </div>
  </div>

  <div *ngIf="cardTooltip != ''" class="card-header-tooltip">
    <h6 style="color:azure !important; text-align: center;" class="title">{{ cardTooltip }}</h6>
  </div>

  <ng-container *ngIf="btnSubContext">
    <div class="btnSubContext">
      <button (click)="btnClickContext()"
        class="{{isActive === true ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}">{{buttonLabel}}</button>
    </div>
  </ng-container>

  <div *ngIf="hasDropdownSelect" style="width: 50px; height: 50px; margin: 0 auto;">
    <select (change)="changeElement($event)" class="dropdown filter-dropdown limited">
      <option *ngFor="let element of elements" [selected]="selectedOption == element" [value]="element">
        {{ element }}
      </option>
    </select>
  </div>
</div>
