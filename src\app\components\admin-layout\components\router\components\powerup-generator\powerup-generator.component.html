
<div class="card list-header"
style="height: 70px; margin: 30px; margin-bottom: 0px;">
  <div class="header">
    <button  class="{{activeTab === 'class-selection' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
            (click)="switchToTab('class-selection')">
      1 - Item Class
    </button>
    <button  class="{{activeTab === 'powerup-selection' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
            (click)="switchToTab('powerup-selection')" style="margin-left: 5px;">
      2 - Powerups
    </button>
    <div *ngIf="activeTab === 'powerup-selection'" style="position:relative; float:right">
      <button class="{{activeTab2 === 'Ingredients' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
              (click)="switchToTab2('Ingredients')">
        1 - Ingredients for construction
      </button>
      <button class="{{activeTab2 === 'statistics' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
              (click)="switchToTab2('statistics')" style="margin-left: 5px;">
        2 - Distribution of statistics
      </button>
    </div>
  </div>
</div>

<app-powerup-class-selection *ngIf="activeTab === 'class-selection'"> </app-powerup-class-selection>
<app-powerup-information [activeTab]="activeTab2" *ngIf="activeTab === 'powerup-selection'" (itemSelected)="switchToTab('item-record')"> </app-powerup-information>