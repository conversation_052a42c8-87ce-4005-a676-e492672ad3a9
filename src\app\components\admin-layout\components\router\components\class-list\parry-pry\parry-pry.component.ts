import { ChangeDetectorRef, Component } from '@angular/core';
import { Class, ParryPry, TierList } from 'src/app/lib/@bus-tier/models';
import { Button } from 'src/app/lib/@pres-tier/data';
import { ClassService, ParryPryService, TierService } from 'src/app/services';
import { Alert } from 'src/lib/darkcloud';

// Interface para os objetos de RarityValue
interface RarityValue {
  idRarity?: string;
  value?: string;
}

@Component({
  selector: 'app-parry-pry',
  templateUrl: './parry-pry.component.html',
  styleUrls: ['./parry-pry.component.scss']
})
export class ParryPryComponent {

  description: string;
  activeLanguage = 'PTBR';
  classes: Class[] = [];
  rarities: TierList[] = [];
  listParryPry: ParryPry[] = [];
  isClassNameSortedAsc: boolean = true; // Controla a direção da ordenação

public readonly excelButtonTemplate: Button.Templateable = {
   title: 'Paste content from excel',
  onClick: this.onExcelPaste.bind(this),
  iconClass: 'excel-icon',
  btnClass: Button.Klasses.FILL_ORANGE,
 }; 

 constructor(
  private change: ChangeDetectorRef,
  private _classService: ClassService,
  private _tierService: TierService,
  private _parryPryService: ParryPryService,
 ){}


  ngOnInit(): void {

    this._classService.toFinishLoading();
    this._tierService.toFinishLoading();
    this._parryPryService.toFinishLoading();  

    this.classes = this._classService.models;
    this.rarities = this._tierService.getCollectibleRarity('Character Rarity'); 
    this.listParryPry = this._parryPryService.models;
   this.sortRaritiesByCustomOrder();
    setTimeout(() => {
     // this.listModMoonRanges = this._modMoonRangesService.models;
     this.description = `Showing ${this.listParryPry.length} results`;
    }, 100);

    console.log('ListParryPry', this.listParryPry);
 }


 /**
 * Ordena o array de raridades na ordem específica: TR5, TR4, TR2, TR3, TR1, TR6
 */
private sortRaritiesByCustomOrder(): void {
  const customOrder = ['TR5', 'TR4', 'TR2', 'TR3', 'TR1', 'TR6'];
  
  this.rarities.sort((a, b) => {
    const indexA = customOrder.indexOf(a.id);
    const indexB = customOrder.indexOf(b.id);
    
    // Se o ID não estiver na ordem customizada, coloca no final
    if (indexA === -1 && indexB === -1) return 0;
    if (indexA === -1) return 1;
    if (indexB === -1) return -1;
    
    return indexA - indexB;
  });
}


   async onExcelPaste() {
     try {
        const text = await navigator.clipboard.readText();
        const lines = text.split(/\r?\n/).filter((line) => line);

        if (lines.length === 0) {
          Alert.showError('No data found in clipboard.');
          return;
        }

        // Limpa a lista atual
        this._parryPryService.models = [];
        this.listParryPry = [];
        this._parryPryService.toSave();

        // Processa a primeira linha (cabeçalho)
        const headerValues = lines[0].split('\t');

        // 1) Validação do número de colunas do cabeçalho - deve ser igual ao tamanho do array this.rarities.length + 1
        if (headerValues.length !== this.rarities.length + 1) {
          Alert.showError(`Error: Number of columns in header does not match the expected (${this.rarities.length + 1}). Found: ${headerValues.length}`);
          return;
        }

        // 2) Validação do cabeçalho - da 2ª coluna em diante deve corresponder aos nomes das rarities
        for (let i = 1; i < headerValues.length; i++) {
          const headerRarityName = headerValues[i];
          const rarityIndex = i - 1; // Índice no array de rarities
          const expectedRarityName = this.rarities[rarityIndex].name;

          // Compara removendo espaços e em letras minúsculas para validação case-insensitive
          if (headerRarityName.trim().toLowerCase() !== expectedRarityName.trim().toLowerCase()) {
            Alert.showError(`Error: Header column ${i + 1} "${headerRarityName}" does not match expected rarity name "${expectedRarityName}".`);
            return;
          }
        }

        // Processa as demais linhas (dados)
        for (let index = 1; index < lines.length; index++) {
          const values = lines[index].split('\t');

          // 3) Validação do número de colunas dos dados - deve ser igual ao número de colunas do cabeçalho
          if (values.length !== headerValues.length) {
            Alert.showError(`Error: Number of columns in line ${index + 1} does not match header columns (${headerValues.length}). Found: ${values.length}`);
            return;
          }

          // 4) Validação se o nome da 1ª coluna existe no array this.classes
          const className = values[0];
          // Compara removendo espaços e em letras minúsculas para validação case-insensitive
          const foundClass = this.classes.find(c => c.name.trim().toLowerCase() === className.trim().toLowerCase());
          if (!foundClass) {
            Alert.showError(`Error: Class name "${className}" in line ${index + 1} not found in classes list. Please verify the class name.`);
            return;
          }
          const classId = foundClass.id; // Salva o id da classe para usar depois

          // 5) Validação dos valores de raridade (da 2ª coluna em diante) - devem ser numéricos
          for (let i = 1; i < values.length; i++) {
            const rarityValue = values[i];

            if (isNaN(Number(rarityValue))) {
              Alert.showError(`Error: Invalid value "${rarityValue}" in line ${index + 1}, column ${i + 1}. Expected a numeric value.`);
              return;
            }
          }

          // Após todas as validações, cria o novo ParryPry
          const newMParryPry = await this._parryPryService.createNewParryPry();

          // Atribui o nome da classe
          newMParryPry.className = className;

          // Atribui o id da classe
          newMParryPry.classId = classId;

          // Inicializa o array de rarityValue
          newMParryPry.rarityValue = [];

          // Adiciona os valores de raridade no array rarityValue
          for (let i = 1; i < values.length; i++) {
            const rarityIndex = i - 1;
            const rarityId = this.rarities[rarityIndex].id;
            const rarityValue = values[i];

            const rarityValueObj: RarityValue = {
              idRarity: rarityId,
              value: rarityValue
            };

            newMParryPry.rarityValue.push(rarityValueObj);
          }

          this._parryPryService.svcToModify(newMParryPry);
        }

        this._parryPryService.toSave();
        this.change.detectChanges();
        Alert.ShowSuccess('Parry (PRY) list copied successfully!');
        this.ngOnInit();
      } catch (error) {
        Alert.showError('Error importing data from Excel.');
        console.error(error);
      }
    }

  /**
   * Ordena a lista de ParryPry por className em ordem alfabética
   * Alterna entre ordem crescente e decrescente a cada clique
   */
  sortByClassName(): void {
    this.listParryPry.sort((a, b) => {
      const nameA = a.className?.toLowerCase() || '';
      const nameB = b.className?.toLowerCase() || '';

      if (this.isClassNameSortedAsc) {
        // Ordem crescente (A-Z)
        return nameA.localeCompare(nameB);
      } else {
        // Ordem decrescente (Z-A)
        return nameB.localeCompare(nameA);
      }
    });

    // Alterna a direção da ordenação para o próximo clique
    this.isClassNameSortedAsc = !this.isClassNameSortedAsc;

    // Força a detecção de mudanças para atualizar a view
    this.change.detectChanges();
  }

getRarityValue(parry: ParryPry, rarityName: string): string {

       if (!parry.rarityValue) return '';
    const rarityValue = this.rarities.find((r) => r.name === rarityName);

     if (!rarityValue) return '';
     // Procura diretamente no array de objetos
     const entry = parry.rarityValue.find(a => a.idRarity === rarityValue.id);
  return entry ? entry.value : '';
}

}
