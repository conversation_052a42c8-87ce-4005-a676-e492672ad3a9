
/* Main wrapper for the dialogue simulator interface */
.wrapper {
  height: 100%;
  width: 50vh;
  margin: auto;
}

/* Primary container for the chat-style dialogue simulator */
.simulator-container {
  height: 90%;
  width: 100%;
  margin-top: 15px;
  overflow: hidden;

  /* Semi-transparent dark background with rounded corners */
  box-shadow: 0 5px 30px rgba(0, 0, 0, .2);
  background: rgba(0, 0, 0, .5);
  border-radius: 20px;

  /* Flexbox layout: header at top, messages in middle, input at bottom */
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  position: relative;
}

/*--------------------
Header Section with Controls
--------------------*/
.chat-title {
  flex: 0 1 auto;
  position: relative;
  z-index: 2;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.2));
  color: #fff;
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Flexbox layout for header content: dialogue info on left, controls on right */
.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

/* Dialogue identification section */
.dialogue-info {
  flex: 0 0 auto;

  h1 {
    font-weight: 600;
    font-size: 16px;
    margin: 0 0 3px 0;
    padding: 0;
    letter-spacing: 0.8px;
    color: #fff;
  }

  h2 {
    color: rgba(255, 255, 255, .6);
    font-size: 11px;
    font-weight: 400;
    letter-spacing: 0.4px;
    margin: 0;
    padding: 0;
  }
}

/* Simulation controls section - right side of header */
.controls-section {
  display: flex;
  align-items: center;
  gap: 20px;
  flex: 1;
  justify-content: flex-end;
}

/* Individual control group (label + input/button) */
.control-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
}

/* Labels for control groups */
.control-label {
  font-size: 10px;
  font-weight: 500;
  letter-spacing: 0.8px;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  margin: 0;
  white-space: nowrap;
}

/* Speed control container (slider + value display) */
.speed-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Custom styled range slider for message speed */
.speed-slider {
  width: 100px;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  outline: none;
  -webkit-appearance: none;
  position: relative;

  /* Custom track with green progress indicator */
  &::-webkit-slider-runnable-track {
    width: 100%;
    height: 4px;
    background: linear-gradient(to right, #4CAF50 0%, #4CAF50 var(--slider-progress, 67%), rgba(255, 255, 255, 0.2) var(--slider-progress, 67%), rgba(255, 255, 255, 0.2) 100%);
    border-radius: 2px;
  }

  /* Custom slider thumb */
  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    background: #4CAF50;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    margin-top: -7px;
  }

  &::-moz-range-track {
    width: 100%;
    height: 4px;
    background: linear-gradient(to right, #4CAF50 0%, #4CAF50 var(--slider-progress, 67%), rgba(255, 255, 255, 0.2) var(--slider-progress, 67%), rgba(255, 255, 255, 0.2) 100%);
    border-radius: 2px;
    border: none;
  }

  &::-moz-range-thumb {
    width: 18px;
    height: 18px;
    background: #4CAF50;
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  }
}

.speed-value {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
  min-width: 40px;
  text-align: center;
  font-weight: 500;
}

.roadblock-button, .dice-button {
  padding: 8px 14px;
  border: 2px solid #dc3545;
  background-color: #dc3545;
  color: white;
  cursor: pointer;
  border-radius: 5px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  min-width: 75px;

  &:hover {
    background-color: #c82333;
    border-color: #c82333;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
  }

  &.active {
    background-color: #28a745;
    border-color: #28a745;

    &:hover {
      background-color: #218838;
      border-color: #218838;
      box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
    }
  }
}

.dice-button {
  border-color: #6f42c1;
  background-color: #6f42c1;

  &:hover {
    background-color: #5a32a3;
    border-color: #5a32a3;
    box-shadow: 0 4px 8px rgba(111, 66, 193, 0.3);
  }

  &.active {
    background-color: #28a745;
    border-color: #28a745;

    &:hover {
      background-color: #218838;
      border-color: #218838;
      box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
    }
  }
}



/*--------------------
Message Box
--------------------*/
.messages
{
  overflow-y: auto;
  overflow-x: hidden;
  position: absolute;
  top: 90px;
  bottom: 55px;
  left: 0px;
  right: 0px;
}
.messages-content
{
  position: absolute;
  overflow:visible;

  height: 95%;
  bottom: 0px;
  left: 0px;
  right: 0px;
}

.messages::-webkit-scrollbar {
  width: 12px;
}

.messages::-webkit-scrollbar-track {
  background-color: #111111;
}

.messages::-webkit-scrollbar-thumb {
  background-color: #585858;
}

.message-box {
  flex: 0 1 40px;
  width: 100%;
  background: rgba(0, 0, 0, 0.3);
  padding: 10px;
  position: relative;

  & .message-input {
    background: none;
    border: none;
    outline: none!important;
    resize: none;
    color: rgba(255, 255, 255, .7);
    font-size: 16px;
    height: 28px;
    margin: 0;
    margin-right: 20px;
    width: 0%!important;
    opacity: 0;
  }

  textarea:focus:-webkit-placeholder{
      color: transparent;
  }

  & .message-submit {
    position: absolute;
    z-index: 1;
    top: 9px;
    right: 10px;
    color: #fff;
    border: none;
    background: #248A52;
    font-size: 14px;
    text-transform: uppercase;
    line-height: 1;
    padding: 8px 12px;
    border-radius: 15px;
    outline: none!important;
    transition: background .2s ease;

    &:hover {
      background: #1D7745;
    }
  }
}


/*--------------------
Others
--------------------*/

.lockChat
{
  clear: both;
  float: left;
  position: relative;
}

.endMessage
{
  clear: both;
  float: right;
  padding: 7px 12px 9px;
  border-radius: 10px 10px 10px 10px;
  background: rgba(83, 8, 8, 0.3);
  margin: 8px 0;
  font-size: 15px;
  line-height: 1.4;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 12px;
  position: relative;
  text-shadow: 0 1px 1px rgba(0, 0, 0, .2);
  color: rgb(204, 204, 204);
  text-align: center;
  width: 80px;
}


