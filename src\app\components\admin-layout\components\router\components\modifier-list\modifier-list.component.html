<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons 
          [cardTitle]="listName"
          [cardDescription]="cardDescription"
          [rightButtonTemplates]="[statusTemplate]">
        </app-header-with-buttons>
        <app-header-search (inputKeyup)="lstOnChangeFilter($event)" (searchOptions)="lstOnChangeFilterOptions($event)"></app-header-search>
      </div>
    </div>
    <!--List-->
    <div class="card">
      <table class="table table-list">
        <thead class="sticky">
          <tr>
            <th class="th-clickable" >Index</th>
            <th class="th-clickable"
              (click)="sortBySkill()">Skill
              <div class="ball-circle"></div>
            </th>
            <th class="th-clickable"
              (click)="sortByAcronym()">Acronym</th>
            <th >Description
              <div class="ball-circle"></div>
            </th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor=" let modifier of this.modifierClasses; let i = index; trackBy: trackById">
            <tr id="{{ modifier.id }}">
              <td class="td-sort">{{ i + 1 }}</td>
              <td class="td-notes">
                <input class="form-control form-short "
                  type="text"
                  value="{{ (modifier | translation : lstLanguage : modifier.id : 'skill') }}"
                  #skill
                  [ngStyle]="{'background-color': +skill.value <= 0 ? '#404040' : '#ffffff'}"
                  (change)="skillChange(modifier, 'skill', skill.value)"/>
              </td>
              <td class="td-notes" style="width: 20%;">
                <input class="form-control form-short "
                  type="text"
                  value="{{ modifier.acronym }}"
                  #acronym
                  [ngStyle]="{'background-color': +acronym.value <= 0 ? '#404040' : '#ffffff'}"
                  (change)="lstOnChange(modifier, 'acronym', acronym.value)"/>                
              </td>
              <td class="td-notes" style="text-align:right; width:100%">
                <textarea class="form-control " style="height:50px"
                  type="text"
                  value="{{ (modifier | translation : lstLanguage : modifier.id : 'description') }}"
                  #description
                  [ngStyle]="{'background-color': +description.value <= 0 ? '#404040' : '#ffffff'}"
                  (change)="descriptionChange(modifier, 'description', description.value)">
                </textarea>
              </td>
              <td  class="td-actions">
                <button class="btn btn-danger btn-fill btn-remove"
                  (click)="removeElement(modifier)">
                  <i class="pe-7s-close"></i>
                </button><br>                
                <button 
                  class="btn btn-gray btn-fill translation-button"
                  (click)="getModifierOrtography(modifier)">
                  <div class="mat-translate"></div>
                </button>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>
