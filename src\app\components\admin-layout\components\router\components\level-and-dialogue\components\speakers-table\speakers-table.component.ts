import { Component, Input } from '@angular/core';
import { Level, Area, Character } from 'src/app/lib/@bus-tier/models';
import { SpeechService } from 'src/app/services/speech.service';
import { LevelService } from 'src/app/services/level.service';
import { CharacterService } from 'src/app/services/character.service';
import { Alert } from 'src/lib/darkcloud';
import { PopupService } from 'src/app/services/popup.service';
import { Popup } from 'src/lib/darkcloud';
import { AreaService } from 'src/app/services/area.service';
import { GameTypes } from 'src/lib/darkcloud/dialogue-system';
import { MicroloopService } from 'src/app/services/microloop.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-speakers-table',
  templateUrl: './speakers-table.component.html',
})

export class SpeakersTableComponent 
{
  @Input() level: Level;
  @Input() microloop: boolean = false;

  constructor(
    private _router: Router,
    private _speechService: SpeechService,
    private _levelService: LevelService,
    private _characterService: CharacterService,
    private _popupService: PopupService,
    private _areaService: AreaService,
    private _microloopService: MicroloopService
  ) {}

  public accessCharacter(characterId: string) 
  {
    this._router.navigate(['characters'], { fragment: characterId });
  }

  public async toPromptRemoveSpeakerFromLevel(level: Level, speakerId: string) 
  {
    const timesAppearsOnDialog = this._speechService.filterByLocation(level.id).filter((speech) => speech.speakerId === speakerId).length;

    if (timesAppearsOnDialog === 0 || (await Alert.showConfirm('Are you sure you want to remove \n"' +
        this._characterService.svcFindById(speakerId).name + '"?',
        'This character appears in ' + timesAppearsOnDialog + ' speeches', 'Yes, remove it!'))) 
    {
      this.microloop ? this._microloopService.RemoveSpeaker(level, speakerId) :
      this._levelService.RemoveSpeaker(level, speakerId);

      this._characterService.svcReviewById(speakerId);
    }
  }

  public async toPromptAddSpeakerToLevel(level: Level) 
  {
    const selectedCharacterButton = await this._popupService.fire< Area, Character>(new Popup.Interface(
    {
      title: 'Select Area',
      actionsClass: 'column',
    },
    Popup.toButtonList(
      this._areaService.models,
      {
        parameters: ['hierarchyCode', 'name'],
        mapping: '[<hierarchyCode>]: <name>',
      },
      { undefinedTitle: 'No Area' }
    ),
    {
      inputButton: 
      {
        value: this._areaService.svcFindById(Area.getSubIdFrom(level.id)),
      },
      next: (selectedAreaButton) => 
      {
          if (!selectedAreaButton) return null;
          
          let characters = this._characterService.models;
          characters = characters.filter((character) => character.areaId === selectedAreaButton.value?.id);
          return new Popup.Interface<Character, Character>(
          {
            title: 'Select Character',
            actionsClass: 'column',
          },
          Popup.toButtonList(characters, 'name', 
          {
            classEnum: GameTypes.characterTypeName,
            classParameter: 'type',
          })
        );
      },
    }));

    if (!selectedCharacterButton) return;    

    this.microloop ? this._microloopService.addSpeaker(selectedCharacterButton.value.id, level)
    : this._levelService.addSpeaker(selectedCharacterButton.value.id, level);
  
    this._characterService.svcReviewById(selectedCharacterButton.value.id);
  }
}
