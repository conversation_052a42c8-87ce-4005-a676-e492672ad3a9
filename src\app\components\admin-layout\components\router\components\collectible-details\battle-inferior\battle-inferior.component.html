<div style="margin-top: 20px" class="list-header-row update">
    <div style="padding-top: 10px;" class="card">
      <app-header-with-buttons 
        [cardTitle]="'Battle'"
        [valueBossLevel]="valueBossLevel"
        [nameClass]="nameClass" [nameRarity]="nameRarity" [type]="type" 
        [cardDescription]="''"
        [rightButtonTemplates]="[excelButtonTemplate]"
        [isBackButtonEnabled]="false">
      </app-header-with-buttons>
    </div>
  </div>
  
  <div class="card" style="margin-bottom: 0;">
    <h2 style="text-align:center;">{{this.hierachyCode}}</h2>
    <table class="table table-list">
      <thead>
        <tr>       
          <th class="dark-gray" colspan="6">
            <h4>Battle</h4>
            </th>
        </tr>
        <tr>     
          <th class="gray">BL</th>
          <th class="gray">HP</th>        
          <th class="gray">ATK</th>        
          <th class="gray">DEF</th>        
          <th class="gray">ATKlim</th>        
        </tr>      
      </thead>
      <tbody style="height: 40px;">        
          <tr>   
            <td>
                <input 
                class="background-input-table-color form-control form-short " placeholder=" "
                type="number" 
                #bl  
                [value]="currentCharacter?.bl"
                (change)="changeBattleupgradeValue(currentCharacter, bl.value, 'bl')"/>
            </td>
            <td>
              <input 
                class="background-input-table-color form-control form-short " placeholder=" "
                type="number" 
                #hp  
                [value]="currentCharacter?.hp"
                (change)="changeBattleupgradeValue(currentCharacter, hp.value, 'hp')"/>
            </td>  
            <td>
              <input 
                class="background-input-table-color form-control form-short " placeholder=" "
                type="number" 
                #atk  
                [value]="currentCharacter?.atk"
                (change)="changeBattleupgradeValue(currentCharacter, atk.value, 'atk')"/>
            </td> 
            <td>
              <input 
                class="background-input-table-color form-control form-short " placeholder=" "
                type="number" 
                #def  
                [value]="currentCharacter?.def"
                (change)="changeBattleupgradeValue(currentCharacter, def.value, 'def')"/>
            </td> 
            <td>
              <input 
                class="background-input-table-color form-control form-short " placeholder=" "
                type="number" 
                #atkLim  
                [value]="currentCharacter?.atkLim"
                (change)="changeBattleupgradeValue(currentCharacter, atkLim.value, 'atkLim')"/>
            </td>       
          </tr>  
      </tbody>
    </table>
  </div>