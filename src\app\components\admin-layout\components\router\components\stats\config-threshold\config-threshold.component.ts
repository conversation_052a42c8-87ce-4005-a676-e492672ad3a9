import { Component } from '@angular/core';
import { ConfigThreshold } from 'src/app/lib/@bus-tier/models';
import { ConfigThresholdService } from 'src/app/services';

@Component({
  selector: 'app-config-threshold',
  templateUrl: './config-threshold.component.html',
  styleUrls: ['./config-threshold.component.scss']
})
export class ConfigThresholdComponent {

  cardTitle: string;
  description: string;
  listValueConfig: ConfigThreshold[] = [];
  
  constructor(
    private _configThresholdService: ConfigThresholdService
  ) { }

  ngOnInit(): void { 
    this.cardTitle = 'Config Threshold';
    this.description = 'Value setting for Dilemma Points';
    this._configThresholdService.toFinishLoading();
    this.listValueConfig = this._configThresholdService.models || [];
  }


  async addPoints() {
    this._configThresholdService.createNewConfigThreshold();
    this.ngOnInit();
  }

  addValue(index: number, value: string){
    this.listValueConfig[index].valueThreshold = value === '' ? null : +value;   
    this._configThresholdService.svcToModify(this.listValueConfig[index]);
    this._configThresholdService.toSave();
  }

  removeThreshold(index: number) {
    const itemToRemove = this.listValueConfig[index];

    if (itemToRemove && itemToRemove.id) {
      this._configThresholdService.svcToRemove(itemToRemove.id);
    }

    this.listValueConfig.splice(index, 1); // Remove o item da lista local
    this.ngOnInit(); 
  }
  }


