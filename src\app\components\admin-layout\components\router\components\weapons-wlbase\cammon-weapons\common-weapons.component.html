<div class="content-weapon">
  <div class="card" style="margin-top: 20px;">
    <app-header-with-buttons
      [cardTitle]="title"
      [cardDescription]="description"
      [isBackButtonEnabled]="false">
    </app-header-with-buttons>
  </div>
  <br />
  <div class="content" style="margin-top: 20px;">
    <div class="m-cammon">
        <table class="table-bordered">
        <thead>
            <tr>
                <th>Index</th>
                <th >Name</th>
                <th class="th-clickable" (click)="orderSortCommonHC()">Hell Circle (HC)</th>
                <th class="light-blue-bg th-clickable" (click)="orderSortCommon()">Common Weapon Received HC</th>
                <th>Qi-min<br>50 - 300</th>
                <th>Luck-min<br>1 - 12</th>
                <th>WLBase</th>
                <th>WL Range</th>
            </tr>
        </thead>
        <tbody>
            <ng-container *ngFor="let comm of listCommonWeapons; let i = index">
                <ng-container *ngFor="let received of comm.commonWeaponReceivedHC; let j = index">
                    <tr>
                        <!-- Index -->
                        <td class="gray-bg" *ngIf="j === 0" [attr.rowspan]="comm.commonWeaponReceivedHC.length">{{i + 1}}</td>
                        
                        <!-- Name -->
                        <td *ngIf="j === 0" [attr.rowspan]="comm.commonWeaponReceivedHC.length">{{ comm.name }}</td>
    
                        <!-- Hell Circle (HC) -->
                        <td *ngIf="j === 0" [attr.rowspan]="comm.commonWeaponReceivedHC.length">{{ comm.hc }}</td>
                        
                        <!-- Common Weapon Received HC -->
                        <td>
                            <p style="padding-top: 8px; text-align: left; padding-left: 10px;">{{received.nameHc}}</p>
                        </td>
                        
                        <!-- Qi-min -->
                        <td >
                            <input placeholder=" " style="padding-left: 10px;" type="number" [value]="received.qi_Min" #qiMin [ngClass]="{'empty-input': !qiMin.value}"
                            (change)="onChangeCommonWeapons(comm, qiMin.value, 'qi_Min', j)">  
                        </td>
    
                        <!-- Luck-min -->
                        <td>
                            <input placeholder=" " style="padding-left: 10px;" type="number" [value]="received.luck_min" #luckMin [ngClass]="{'empty-input': !luckMin.value}"
                            (change)="onChangeCommonWeapons(comm, luckMin.value, 'luck_min', j)">  
                        </td>
    
                        <!-- WLBase -->
                        <td>
                            <input placeholder=" " style="padding-left: 10px;" type="number" [value]="received.wlBase" #wlBase [ngClass]="{'empty-input': !wlBase.value}"
                            (change)="onChangeCommonWeapons(comm, wlBase.value, 'wlBase', j)">  
                        </td>
    
                        <!-- WL Range -->
                        <td [ngClass]="getWlRangeClass(comm.hc)" *ngIf="j === 0" [attr.rowspan]="comm.commonWeaponReceivedHC.length">
                            {{valueRange}}
                         </td>
                    </tr>
                </ng-container>
            </ng-container>
        </tbody>
    </table>
    </div>
  </div>
</div>

