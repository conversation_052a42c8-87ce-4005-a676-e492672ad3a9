import { Component, OnInit } from '@angular/core';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';
import { ItemClass } from 'src/app/lib/@bus-tier/models/ItemClass';
import { CustomService } from 'src/app/services/custom.service';
import { ItemClassService } from 'src/app/services/item-class.service';


@Component({
  selector: 'app-special-weapon-class-selection',
  templateUrl: './special-weapon-class-selection.component.html',
  styleUrls: ['./special-weapon-class-selection.component.scss'],
})
export class SpecialWeaponClassSelectionComponent implements OnInit {

  itemClasses: ItemClass[];
  custom: Custom;

  constructor(
    private _itemClassService: ItemClassService,
    private _customService: CustomService,
  ) {

  }

  async ngOnInit(): Promise<void>
  {
    this.itemClasses = this._itemClassService.models;
    this.custom = await this._customService.svcGetInstance();
    if(!this.custom.specialWeaponClassItem)
    {
      this.custom.specialWeaponClassItem = [];
    }
  }

  async onItemClassSelected(itemClass: ItemClass)
  {
    this._customService.toggleItemClass(itemClass, 'specialWeaponClassItem');
    this.custom = await this._customService.svcGetInstance();
  }

}
