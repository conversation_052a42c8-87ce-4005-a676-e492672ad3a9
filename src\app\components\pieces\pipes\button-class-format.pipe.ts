import { Pipe, PipeTransform } from '@angular/core';
import { Button } from 'src/app/lib/@pres-tier/data';

@Pipe({
  name: 'buttonClassFormat',
})
export class ButtonClassFormatPipe implements PipeTransform {
  private static readonly _PREFIX = 'btn';
  private static appendPrefix(value: string): string {
    return `${this._PREFIX}-${value}`;
  }
  transform(value: Button.klass | Button.klass[]): string | string[] {
    if (typeof value === 'string') {
      return 'btn ' + ButtonClassFormatPipe.appendPrefix(value);
    } else {
      return value.map((el) => ButtonClassFormatPipe.appendPrefix(el)).concat('btn');
    }
  }
}
