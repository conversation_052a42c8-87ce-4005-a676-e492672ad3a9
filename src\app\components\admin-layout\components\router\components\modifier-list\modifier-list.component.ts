import { Component } from '@angular/core';
import { Modifier } from 'src/app/lib/@bus-tier/models';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { TranslationService } from 'src/app/services/translation.service';
import { LanguageService } from 'src/app/services/language.service';
import { ModifierService } from 'src/app/services';
import { Button } from 'src/app/lib/@pres-tier/data';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';
import { language } from 'src/lib/darkcloud/dialogue-system/game-types';
import { TranslationCheckService } from 'src/app/services/translation-check.service';
import { Alert } from 'src/lib/darkcloud';
import { ActivatedRoute } from '@angular/router';


@Component({
  selector: 'app-modifier-list',
  templateUrl: './modifier-list.component.html',
  styleUrls: ['./modifier-list.component.scss']
})

export class ModifierListComponent extends TranslatableListComponent<Modifier> 
{
  constructor(
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    private _modifierService: ModifierService,
    protected override _translationService: TranslationService,
    protected override _languageService: LanguageService,
    private _translationCheckService: TranslationCheckService,

  ) 
  {
    super(_modifierService, _activatedRoute, _userSettingsService, 'name', _translationService, _languageService);
  }

  public language: language = 'PT-BR';
  public modifierClasses: Modifier[] = [];
 
  public readonly statusTemplate: Button.Templateable = 
  {
    title: 'Add a new instance to the list',
    onClick: this.addStatus.bind(this),
    iconClass: 'pe-7s-plus',
    btnClass: Button.Klasses.FILL_GREEN,
  };
  
  override async lstInit ()
  {
    this.modifierClasses = this._modifierService.models;
    this._modifierService.svcReviewAll();
    this.lstIds = this.modifierClasses.map(x => x.id);
  }

  async addStatus()
  {
    let modifierClasses;

    try 
    {
      modifierClasses = await this._modifierService.svcPromptCreateNew();
    } 
    catch (e) 
    {
      Alert.showError("This SKILL already exists!");
      return
    }
    if(!modifierClasses) return;

    await this._modifierService.srvAdd(modifierClasses);

    if(this.modifierClasses.includes(modifierClasses)) return;    
    else this.modifierClasses.push(modifierClasses);
  }

  async removeElement(status : Modifier)
  {
    const confirm = await Alert.showRemoveAlert(status.skill + ' ' + 'skill');  
    if (!confirm) return;
    
    this._modifierService.models = await this._modifierService.models.filter(s => s.id !== status.id);
    await this._modifierService.toSave();
    this.modifierClasses = this.modifierClasses.filter(s => s !== status);
  }

  public getModifierOrtography(status: Modifier)
  {
    this._translationService.getModifierOrtography(status, true);
  }

  //The two below sort methods are here because it dosent work with the old code but the others parameters did worked.
  sortBySkillOrder = -1;
  sortBySkill() 
  {
    this.sortBySkillOrder *= -1;
    this._modifierService.models.sort((a, b) => 
    {
        return this.sortBySkillOrder *  a.skill.localeCompare(b.skill);
    });
    this.lstFetchLists();
  }
 
  sortByAcronymOrder = -1;
  sortByAcronym() 
  {
    this.sortByAcronymOrder *= -1;
    this._modifierService.models.sort((a, b) => 
    {
        return this.sortByAcronymOrder *  a.acronym.localeCompare(b.acronym);
    });
    this.lstFetchLists();
  }

  skillChange(modifier: Modifier, skill: string, value: string) {
    modifier.isReviewedSkill = false;
    modifier.revisionCounterSkillAI = 0;
    this.lstOnChange(modifier, 'skill', value);
  }

  descriptionChange(modifier: Modifier, description: string, value: string) {
    modifier.isReviewedDescription = false;
    modifier.revisionCounterDescriptionAI = 0;
    this.lstOnChange(modifier, 'description', value);
  }
}
