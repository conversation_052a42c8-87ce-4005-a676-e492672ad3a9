<div class="content-weapon">
  <div class="card list-header-row">
    <app-header-with-buttons 
      class="card"
      [isBackButtonEnabled]="true"
      (cardBackButtonClick)="onBack()"
      [cardTitle]="currentCharacter?.name"
      [rightButtonTemplates]="[leftButtonTemplate,rightButtonTemplate]"
      [cardDescription]="currentCharacter?.description"  [textDescriptionRecord]="true">     
    </app-header-with-buttons>    
  </div>    

  <div class="card list-header"
  style="height: 70px; margin-bottom: 0px;">
  <div class="header">
    <ng-container *ngIf="currentCharacter?.rarity !== 'Inferior'">
        <button class="{{activeTab === 'battle' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}" 
        (click)="switchToTab('battle')">
          1 - Battle Upgrade
      </button>
      </ng-container>
      <ng-container *ngIf="currentCharacter?.rarity === 'Inferior'">
        <button class="{{activeTab === 'battleInferior' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}" 
        (click)="switchToTab('battleInferior')">
          1 - Battle
      </button>
      </ng-container>
      <button class="{{activeTab === 'elementalAff' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}" 
              (click)="switchToTab('elementalAff')">
        2 - Elemental Affinities
      </button>
      <button class="{{activeTab === 'ailmentDefenses' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}" 
      (click)="switchToTab('ailmentDefenses')">
       3 - Ailment Defenses
      </button>
      <button  class="{{activeTab === 'primal' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}" 
              (click)="switchToTab('primal')">
        4 - Primal Modifiers
      </button>
       <!--  Enum CharacterType { UNDEFINED = undefined, NPC = 1, *MINION* = 2, *BOSS* = 3, *SUBBOSS* = 4, SECONDARY = 5,} --> 
        <button [disabled]="(currentCharacter?.type === 2 && !currentCharacter?.isCollectible) || currentCharacter?.type === 4" class="{{activeTab === 'specialSkill' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}" 
          (click)="switchToTab('specialSkill')" >
          5 - Special Skill
        </button>

        <ng-container>
          <div class="btn-new">
            <button [disabled]="!currentCharacter?.isCollectible" [ngClass]="{'btn-disabled': !currentCharacter?.isCollectible}" class="{{activeTab === 'ctr' ? 'btn btn-fill selectedBtnCollectible' : 'btn btn-light-red'}}" 
            (click)="switchToTab('ctr')">
              6 - CTR
            </button>
            <button [disabled]="!currentCharacter?.isCollectible" [ngClass]="{'btn-disabled': !currentCharacter?.isCollectible}" class="{{activeTab === 'luk' ? 'btn btn-fill selectedBtnCollectible' : 'btn btn-light-red'}}" 
            (click)="switchToTab('luk')">
              7 - LUK
            </button>
            <button [disabled]="!currentCharacter?.isCollectible" [ngClass]="{'btn-disabled': !currentCharacter?.isCollectible}" class="{{activeTab === 'int' ? 'btn btn-fill selectedBtnCollectible' : 'btn btn-light-red'}}" 
            (click)="switchToTab('int')">
              8 - INT
            </button>
            <button [disabled]="!currentCharacter?.isCollectible" [ngClass]="{'btn-disabled' : !currentCharacter?.isCollectible}" class="{{activeTab === 'spd' ? 'btn btn-fill selectedBtnCollectible' : 'btn btn-light-red'}}" 
            (click)="switchToTab('spd')">
              9 - SPD
            </button>
          </div>
        </ng-container>
      </div>
  </div>

  <app-battle-upgrade #query [character]="characterFromQuery" *ngIf="activeTab === 'battle'"> </app-battle-upgrade>
  <app-battle-inferior #query [character]="characterFromQuery" *ngIf="activeTab === 'battleInferior'"></app-battle-inferior>
  <app-status-info #query [character]="characterFromQuery" *ngIf="activeTab === 'elementalAff'"> </app-status-info>
  <app-ailment-defenses #query [character]="characterFromQuery" *ngIf="activeTab === 'ailmentDefenses'"></app-ailment-defenses>
  <app-primal-modifier #query [character]="characterFromQuery" *ngIf="activeTab === 'primal'"> </app-primal-modifier>


  <ng-container *ngIf="(currentCharacter?.type === 2 && currentCharacter?.isCollectible) || currentCharacter?.type === 3">
    <app-special-skills #query [character]="characterFromQuery" *ngIf="activeTab === 'specialSkill'"></app-special-skills>
  </ng-container>

  <ng-container *ngIf="currentCharacter?.isCollectible">
    <app-ctr-collectible #query [character]="currentCharacter" *ngIf="activeTab === 'ctr'"></app-ctr-collectible>
    <app-luk-collectible #query [character]="currentCharacter" *ngIf="activeTab === 'luk'"></app-luk-collectible>
    <app-int-collectible #query [character]="currentCharacter" *ngIf="activeTab === 'int'"></app-int-collectible>
    <app-spd-collectible #query [character]="currentCharacter" *ngIf="activeTab === 'spd'"></app-spd-collectible>
  </ng-container>
  

  





