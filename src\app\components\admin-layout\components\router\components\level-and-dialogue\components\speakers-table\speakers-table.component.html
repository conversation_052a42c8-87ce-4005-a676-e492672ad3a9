<!--Character encounters-->
<!--Add Character encounter Button-->
<div class="collumn no-horizontal-margin"
     *ngIf="level.speakerIds.length === 0; else speakerTable">
  <button class="btn-absolute td-btn-focus btn btn-simple center"
          (click)="toPromptAddSpeakerToLevel(level)">
    <i class="pe-7s-plus icon center success"></i>
    <div>Add Speaker</div>
  </button>
</div>

<ng-template #speakerTable>
  <div class="collumn no-horizontal-margin">
    <table class="table compact-battle-character-table">

      <thead  style="color:rgb(0, 0, 0) !important">
        <tr  >
           <th style="border-color:rgb(219, 218, 218) !important; color:black !important;">Speaker</th>
          <th style="border-color:rgb(219, 218, 218) !important"><button class="btn btn-simple btn-success"
                    (click)="toPromptAddSpeakerToLevel(level)">
              <i class="pe-7s-plus"></i> </button></th>
        </tr>
      </thead>

      <tbody>
        <tr class="tr-focus"
            *ngFor="let speakerId of level.speakerIds;">
          <td>
            <button (click)="accessCharacter(speakerId)"
                    ngClass="btn btn-fill btn-{{((speakerId | character)?.type | characterTypeClass)}}">
              {{ (speakerId | character)?.name }}</button>
            <div class="relative"
                 *ngIf="!((level | review)?.characterIds.includes(speakerId))">
              <div class="not-circle-speakers bkg-warning"></div>
            </div>
          </td>
          <td> <button class="btn tr-btn-focus btn-simple btn-danger"
                    (click)="toPromptRemoveSpeakerFromLevel(level, speakerId)">
              <i class="pe-7s-less"></i> </button>
          </td>
        </tr>
      </tbody>
      
    </table>
  </div>
</ng-template>
