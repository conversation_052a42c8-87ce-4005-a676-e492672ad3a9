<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons [cardTitle]="'Hellnium Storage C'" [cardDescription]="description"
          [rightButtonTemplates]="[excelButtonTemplate]">
        </app-header-with-buttons>
        <app-header-search (inputKeyup)="lstOnChangeFilter($event)"
          (searchOptions)="lstOnChangeFilterOptions($event)"></app-header-search>
      </div>
    </div>
    <!--List-->
    <div class="card">
      <table class="table table-list">
        <thead class="sticky">
          <tr>
            <th rowspan="3" class="th-clickable" (click)="sortListByParameter('hellniumLevel')">
              Hellnium Storage - Level
            </th>
            <th class="th-clickable" (click)="sortListByParameter('souls')">
              Cost
            </th>
            <th class="th-clickable" (click)="sortListByParameter('time')">
              Building Time
            </th>
            <th class="th-clickable" (click)="sortListByParameter('improveRubies')">
              Skip Cost
            </th>
            <th rowspan="3" class="th-clickable" (click)="sortListByParameter('storage')">
              Store Capacity C
            </th>
          </tr>
          <tr>
            <th class="th-clickable silver-color" (click)="sortListByParameter('souls')">
              TITANIUM
            </th>
            <th class="th-clickable time-color" (click)="sortListByParameter('time')">
              MINUTES
            </th>
            <th class="th-clickable rubies-color" (click)="sortListByParameter('improveRubies')">
              RUBIES
            </th>
          </tr>
          <tr>
            <th class="th-clickable light-gray" (click)="sortListByParameter('souls')">
              Required to get to this level
            </th>
            <th class="th-clickable light-gray" (click)="sortListByParameter('time')">
              Time to upgrade to this level
            </th>
            <th class="th-clickable light-gray" (click)="sortListByParameter('improveRubies')">
              Gem to skip the wait (build instantly)
            </th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="
                  let hellniumStorage of lstIds | hellniumStorages;
                  let i = index;
                  trackBy: trackById
                ">
            <tr *ngIf="hellniumStorage.type === 'C'" id="{{ hellniumStorage.id}}">
              <td>{{ hellniumStorage.hellniumLevel}}</td>
              <td class="td-id">
                <input class="background-input-table-color" placeholder=" " type="number" #Inputsouls
                  [value]="hellniumStorage.souls" (change)="lstOnChange(hellniumStorage, 'souls', Inputsouls.value)" />
              </td>
              <td class="td-id">
                <input class="background-input-table-color" placeholder=" " type="number" #Inputtime
                  [value]="hellniumStorage.time" (change)="lstOnChange(hellniumStorage, 'time', Inputtime.value)" />
              </td>
              <td class="td-id">
                <input class="background-input-table-color" placeholder=" " type="number" #Inputrubies
                  [value]="hellniumStorage.rubies"
                  (change)="lstOnChange(hellniumStorage, 'rubies', Inputrubies.value)" />
              </td>
              <td class="td-id">
                <input class="background-input-table-color" placeholder=" " type="number" #Inputstorage
                  [value]="hellniumStorage.storage"
                  (change)="lstOnChange(hellniumStorage, 'storage', Inputstorage.value)" />
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>