

.character
{
  background-color: rgb(226, 226, 226);
  margin-right: 10px;
  padding: 5px;
  border-radius: 3px;
}


.character-group
{
  overflow-wrap: break-word;
  min-height: 50px;
  border: 1px solid rgb(199, 199, 199);
  background-color: #ffffff;
  padding: 10px;
  line-height: 2.2;
  cursor: pointer;
}


.character-group:hover
{
  background-color: rgb(144, 160, 212);
}

.sticky th
{
  top: 130px!important;
}

.wrapper
{
  height: 90%;
  width: 95vw;
  max-width: 1200px;
  margin: auto;
  margin-top: 50px;
  margin-bottom: 50px;
  min-height: 0px;
  overflow-y: auto;
  overflow-x: hidden;
}
.popup.hide
{
  display: none;
}
.popup
{
    position: fixed;
    height: 100%;
    width: 100%;
    background-color: hsla(0, 0%, 15%, 0.24);
    z-index: 150;

    margin: auto;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin-left: auto;
    margin-right: auto;
    margin-top: auto;
    margin-bottom: auto;

    text-align: center;
}

.table-popup th
{
  top: 0px;
}

input[type='checkbox']
{
  width: 20px;
  height: 20px;
}

.table-popup {
  width: 100%;
  table-layout: fixed;
  min-width: 800px;
}

.table-popup th:nth-child(1) { width: 60px; }   /* ID */
.table-popup th:nth-child(2) { width: 150px; }  /* Name & Title */
.table-popup th:nth-child(3) {
  width: 300px;
  word-wrap: break-word;
  overflow-wrap: normal;
}  /* Description */
.table-popup th:nth-child(4) { width: 100px; }  /* Class */
.table-popup th:nth-child(5) { width: 100px; }  /* Area */
.table-popup th:nth-child(6) { width: 100px; }  /* Group */
.table-popup th:nth-child(7) { width: 80px; }   /* Selected */

.table-popup td:nth-child(1) { width: 60px; }   /* ID */
.table-popup td:nth-child(2) { width: 150px; }  /* Name & Title */
.table-popup td:nth-child(3) {
  width: 300px;
  word-wrap: break-word;
  overflow-wrap: normal;
  max-width: 300px;
}  /* Description */
.table-popup td:nth-child(4) { width: 100px; }  /* Class */
.table-popup td:nth-child(5) { width: 100px; }  /* Area */
.table-popup td:nth-child(6) { width: 100px; }  /* Group */
.table-popup td:nth-child(7) { width: 80px; }   /* Selected */

/* Button styling specifically for table buttons */
.table-popup td .btn {
  max-width: 100px;
  word-wrap: break-word;
  overflow-wrap: normal;
  white-space: normal;
  text-align: center;
}

.card {
  width: 100%;
  min-width: 800px;
}
