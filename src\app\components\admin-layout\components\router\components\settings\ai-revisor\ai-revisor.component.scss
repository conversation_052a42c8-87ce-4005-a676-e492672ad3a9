.card-container.card-container 
{
    display: flex;
    flex-direction: column;
    margin-left: 30px;
    height: 1000px;
  
    .card 
    {
      border: 1px solid #ccc;
      padding: 10px;
      margin: 5px;
     // width: 60vw;
      min-width: min-content;
      opacity: 1;
      transition: opacity 0.5s ease-in-out;
      padding-top: 0!important;
    }
}

.text-card
{
    padding: 5px !important;
    padding-left: 15px !important;
    display: flex; 
    width: 100%; 
    flex-direction: column;
}

p
{
    margin: 0;
}

button
{
    background-color: transparent;
    border: none;
}

.btn-height
{
  height: 40px !important;
}

.card-send {
  position: relative; 
  width: 100%; 
  margin: auto; 
  top: -20px; z-index: 1;
}

.card-send-database {
  text-align: center; 
  font-size: 25px; 
  display: flex; 
  justify-content: center;
}

.card-send-text {
  text-decoration: underline; 
  color: #3472f7; 
  font-size: 25px;
  margin-left: 7px; 
  margin-right: 5px; 
  font-weight: 700;
}

.card-send-api {
  text-align: center; 
  font-size: 17px; 
  display: flex; 
  justify-content: center; 
  margin-left: 30px;
}

.card-send-cancel {
  position: relative; 
  float: right; 
  top: -45px;
  display: flex;
  gap: 15px;
}

#customers {
  font-family: Arial, Helvetica, sans-serif;
  border-collapse: collapse;
  width: 55vw;
}

#customers td, #customers th {
  border: 1px solid #ddd;
  padding: 8px;
}

.h3-number {
  padding-left: 5px;
  padding-right: 10px;
}
#customers tr:nth-child(even){background-color: #f2f2f2;}

#customers tr:hover {background-color: #ddd;}

#customers th {
  padding-top: 12px;
  padding-bottom: 12px;
  text-align: left;
  background-color: #04AA6D;
  color: white;
}

.db-itens {
    display: flex;
    align-content: center;
    flex-direction: column;
    flex-wrap: wrap;
    padding-bottom: 10px; 
    padding-top: 10px; 
    padding-left: 10px; 
    margin: 0; 
    height: 1000px; 
    display: flex; 
}
.div-menu {
  overflow-y: auto; 
  overflow-x: hidden;
  width: 280px;
}

.div-menu-span {
  font-weight: 600; 
  text-decoration: underline; 
  font-size: 20px;
}

.container-card {
  width: 60vw; 
  background-color: #f5f5f5; 
 // height: 85vmin; 
  overflow-y: auto;
  overflow-x: hidden;
}

.text-holder
{
    display: flex; 
    flex-direction: column;
    flex-wrap: wrap;
    align-content: center;
    align-items: center;
    justify-content: space-around;
   // width: 60vw;
}

.card-div-name {
  display: flex; 
  flex-direction: row; 
  justify-content: space-around; 
  align-items: center;
}

span {
    font-weight: 700;
}

.span-font-weight {
    font-weight: normal !important;
}

.text-to-be-revised
{
    font-style: normal !important;
    font-size: 15px !important;
    text-align: justify;
    margin: 0px;  
    padding: 5px;
}

.div-buttom {
  display: flex; 
  width: 100%; 
  justify-content: center;
  margin-top: 10px;
}

.buttomApprove {
  color: green; 
  font-weight: 600; 
  font-size: 30px;
}

.buttom-reject {
  color: red; 
  font-weight: 600; 
  font-size: 30px;
}

.div-highlight {
  display: flex; 
  flex-direction: row; 
  justify-content: space-around; 
  align-items: center;
}

.originalText
{
    font-style: normal !important;
    font-size: 15px !important;
    text-align: justify;
    width: 60vw;
    height: auto;
    border-radius: 4px;
    padding: 10px;
    padding-left: 20px;
    margin-top: 10px;
    margin-bottom: 0px;
    background-color: #f2f2f2;
    color: #333;
}

.p-sendinhg {
  font-weight: 700; 
  font-size: 25px;
}

.content-holder
{
    display: flex;
  /*  flex-direction: row;
    justify-content: space-around;
    */
    width: max-content;
    margin-left: 50px;
    margin-right: 50px;
}

.score-cards
{
    display: flex;
    width: 7vw;
    height: 50px;
    align-content: center;
    flex-direction: column;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
}

.score
{
 font-size: 20px;
}

.original-text 
{
    white-space: pre-wrap;
}
  
.insert 
{
    background-color: #89ff89; /* Light green for insertions */
}
  
.delete 
{
    background-color: #fbaaaa; /* Light red for deletions */
}

li
{
    list-style: none;
}

i
{
    font-style: normal;
}

.dif-holder
{
    background-color: #dfdfdf40;
    box-shadow: 0 1px 2px #0000000d, 0 0 0 1px #3f3f441a;
    border-radius: 4px;

    padding: 10px;
    padding-left: 22px;
    margin: 10px;

    width: 48vw;
}

.dif
{
    width: auto;
    overflow: hidden;
    white-space: wrap;
    font-size: 20px;
}

.group-together
{
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    align-content: center;
    justify-content: center;
    align-items: flex-start;
}

.cards-group
{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.no-contents-message
{
    display: flex;
    justify-content: center;
    align-items: center;
    height: 20vw;
    color: grey;
}

.content-refresh {
    margin: auto;
    display: flex;
    max-height: 200vh;
    align-self: center;
    align-items: center;
  }

  .icon-refresh-text {
    font-size: 50px !important;
    animation: rotate 2s linear infinite;
  }
  
  @keyframes rotate {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  
  .icon	
{	
    font-size: 30px;	
    color: #999;	
    position: relative;	
    transition: transform 0.3s;	
    transform-origin: center;	
}	
.icon:hover	
{	
    font-size: 30px;	
    color: #555;	
    transform: translate(-2.5%) rotate(180deg);	
}	

  .loading-dots {
    display: flex;
    width: 60px;
    height: 20px;
    position: relative;
    justify-self: center;
    justify-content: center;
    margin: auto;
  }
  
  .loading-dots .dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #333;
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    animation: move 2s linear infinite;
  }
  
  .loading-dots .dot:nth-child(1) {
    animation-delay: 0s;
  }
  
  .loading-dots .dot:nth-child(2) {
    animation-delay: 0.5s;
  }
  
  .loading-dots .dot:nth-child(3) {
    animation-delay: 1s;
  }
  
  @keyframes move {
    0% {
      left: 0;
    }
    50% {
      left: 40px;
    }
    100% {
      left: 0;
    }
  }

.div-loading {
    display: grid; 
    align-self: center; 
    margin: auto; 
    padding-top: 10px; 
    padding-bottom: 10px;
    text-align: center;
  }
  
  .bt-width {
    width: 130px;
  }

  .m-right {
    margin-right: 5px;
  }
  .btn-green-approved {
    background-color: green !important; 
  }

  .selected {
    background-color: #AFDDFF !important;
  }
  .added {
    color: green;
  }
  
  .removed {
    color: red;
    text-decoration: line-through;
  }


  .div-buttons-general {
    display: flex; 
    justify-content: center; 
    margin-top: 15px; 
    width: 100%; 
    margin-bottom: 20px;
  }

  .i-color-font {
    color: white; 
    font-size: 25px;
  }

/* Ícone de informações do modal */
.iconInter {
  text-align: center;
  font-size: 20px !important;
  margin-top: 1px;
  margin-left: 3px;
  position: relative;
  z-index: 12;
}

i:hover{
  color: red;
  cursor: pointer;
}

/* Estilos para campos editáveis do AI Revisor */

/* Campo editável em modo de visualização */
.text-to-be-revised p[style*="cursor: pointer"] {
  transition: all 0.2s ease;
  border-radius: 4px !important;
}

.text-to-be-revised p[style*="cursor: pointer"]:hover {
  background-color: #f8f9fa;
  border-color: #007bff !important;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Container do campo em edição */
.edit-field {
  margin-top: 5px;
}

.edit-field textarea,
.edit-field input {
  border: 2px solid #007bff;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.4;
}

.edit-field textarea:focus,
.edit-field input:focus {
  border-color: #0056b3;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  outline: none;
}

/* Botões de edição */
.edit-buttons {
  display: flex;
  gap: 5px;
  justify-content: flex-start;
}

.edit-buttons .btn {
  padding: 4px 8px;
  font-size: 12px;
  line-height: 1.2;
  border-radius: 3px;
}

.edit-buttons .btn-success {
  background-color: #28a745;
  border-color: #28a745;
  color: white;
}

.edit-buttons .btn-success:hover {
  background-color: #218838;
  border-color: #1e7e34;
}

.edit-buttons .btn-secondary {
  background-color: #6c757d;
  border-color: #6c757d;
  color: white;
}

.edit-buttons .btn-secondary:hover {
  background-color: #5a6268;
  border-color: #545b62;
}

/* Responsividade para dispositivos móveis */
@media (max-width: 768px) {
  .edit-buttons {
    flex-direction: column;
    gap: 3px;
  }

  .edit-buttons .btn {
    width: 100%;
    text-align: center;
  }
}