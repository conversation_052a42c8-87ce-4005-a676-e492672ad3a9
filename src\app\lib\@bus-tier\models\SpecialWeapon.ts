import {
  CharacterType,
  Gender,
  IdPrefixes,
} from 'src/lib/darkcloud/dialogue-system';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { Base } from './Base';

export class SpecialWeapon
  extends Base<Data.Hard.ISpecialWeapon, Data.Result.ISpecialWeapon>
  implements Required<Data.Hard.ISpecialWeapon>
{
  private static generateId(index: number): string {
    return IdPrefixes.SPECIAL_WEAPON + index;
  }
  constructor(
    index: number,
    itemId: string,
    dataAccess: SpecialWeapon['TDataAccess']
  ) {
    super(
      {
        hard: {
          id: SpecialWeapon.generateId(index),
          itemId,
        },
      },
      dataAccess
    );
  }
  protected getInternalFetch() {
    return {};
  }

  public get itemId(): string {
    return this.hard.itemId;
  }
  public set itemId(value: string) {
    this.hard.itemId = value;
  }
  public get bpId(): string {
    return this.hard.bpId;
  }
  public set bpId(value: string) {
    this.hard.bpId = value;
  }
  public get bpArchiveLevel(): number
  {
    return this.hard.bpArchiveLevel;
  }
  public set bpArchiveLevel(value: number)
  {
    this.hard.bpArchiveLevel = value;
  }
  public get souls(): number
  {
    return this.hard.souls;
  }
  public set souls(value: number)
  {
    this.hard.souls = value;
  }
  public get time(): number
  {
    return this.hard.time;
  }
  public set time(value: number)
  {
    this.hard.time = value;
  }
  public get rubies(): number
  {
    return this.hard.rubies;
  }
  public set rubies(value: number)
  {
    this.hard.rubies = value;
  }
  public get ingredientId(): string
  {
    return this.hard.ingredientId;
  }
  public set ingredientId(value: string)
  {
    this.hard.ingredientId = value;
  }
  public get ingredientsAmount(): number[]
  {
    return this.hard.ingredientsAmount;
  }
  public set ingredientsAmount(value: number[])
  {
    this.hard.ingredientsAmount = value;
  }

  public get ingredientsOrder(): string[]
  {
    return this.hard.ingredientsOrder;
  }
  public set ingredientsOrder(value: string[])
  {
    this.hard.ingredientsOrder = value;
  }
  
}
