import { MajorModule } from 'src/app/major.module';
import { SettingsComponent } from './settings.component';
import { CommonModule } from '@angular/common';
import { BrowserModule } from '@angular/platform-browser';
import { FormsModule } from '@angular/forms';
import { IconListComponent } from './components/icon-list/icon-list.component';
import { KeywordListComponent } from './components/keyword-list/keyword-list.component';
import { ContentInputModule } from 'src/app/components/content-input/content-input.module';
import { PopupModule } from 'src/app/components/popup/popup.module';
import { TranslationPipe } from 'src/app/pipes/translation.pipe';
import { RouterModule } from '@angular/router';
import { NgModule } from '@angular/core';

@NgModule({
  imports: [
    CommonModule,
    BrowserModule,
    FormsModule,
    RouterModule,
    MajorModule,
    ContentInputModule,
    PopupModule,
  ],
  declarations: [SettingsComponent, IconListComponent, KeywordListComponent],  
  providers: [TranslationPipe, SettingsComponent],
  bootstrap: [SettingsComponent],
})
export class SettingsModule {}
