import { ChangeDetectorRef, Component, Input } from '@angular/core';
import { Character, LUKCollectible, PrimalModifier } from 'src/app/lib/@bus-tier/models';
import { CTREVMAXService, LUKCollectibleService, PrimalModifierService } from 'src/app/services';

@Component({
  selector: 'app-luk-collectible',
  templateUrl: './luk-collectible.component.html',
  styleUrls: ['./luk-collectible.component.scss']
})
export class LukCollectibleComponent {
  
  @Input() character: Character;
    primalModifier: PrimalModifier;
    ev_base: number;
    pr_base: number;
    luck: number;
    ev_max: number;
    int: number;
    listLukCollectibes: LUKCollectible;

    constructor(
      private _cTREVMAXService: CTREVMAXService,
      private _primalModifierService: PrimalModifierService,
      private _lUKCollectibleService: LUKCollectibleService,
      private _change: ChangeDetectorRef,
    ) { }
  
 async ngOnInit(): Promise<void> {
  this._lUKCollectibleService.toFinishLoading();
  this._primalModifierService.toFinishLoading();
  
   this.listLukCollectibes = this._lUKCollectibleService.createNewLukCollectible(this.character);        
   // this._change.detectChanges();
   this.getDataValuesLuk();
}
  
getDataValuesLuk() {
  this.listLukCollectibes.valuesInt = [];
  this.ev_base = 0;
  this.luck = 0;
  this.ev_max = 0;

  setTimeout(() => {
    this.primalModifier = this._primalModifierService.models.find((i) => i.character === this.character.id);
    this.ev_base = this.primalModifier.primalModifier.find((i) => i.fieldName === 'Evasão').fieldValue;
    this.pr_base = this.primalModifier.primalModifier.find((i) => i.fieldName === 'Precisão').fieldValue;
    this.int =  this.primalModifier.primalModifier.find((i) => i.fieldName === 'Inteligência').fieldValue;
    this.luck = this.primalModifier.primalModifier.find((i) => i.fieldName === 'Sorte').fieldValue;
        
    if (this._cTREVMAXService.models.length > 0) {  
      this.ev_max = this._cTREVMAXService.models[0].ev_max;
    }
  
    for (let newLuck = 1; newLuck <= 12; newLuck++) {
      if (this.ev_base && this.luck) { 
        this.listLukCollectibes.valuesInt.push({
          ev_defluk: this.calculateEV(newLuck).toFixed(2),
          pr_atkluk: this.calculatePR(newLuck).toFixed(2),
          qiNew: newLuck
        });
      } else {
        this.listLukCollectibes.valuesInt.push({
          ev_defluk: '',
          pr_atkluk: '',
          qiNew: newLuck
        });
      }       
    }
    this._lUKCollectibleService.svcToModify(this.listLukCollectibes);
  },200);

}

private sign(value: number): number {
  return value > 0 ? 1 : value < 0 ? -1 : 0;
}

public calculateEV(newLuck: number): number {
  const controlPos = this.controlPositive(newLuck);
  const controlNeg = this.controlNegative(newLuck);

  const term1 = controlNeg * this.ev_base * (newLuck / this.luck);
  const term2 =
    controlPos *
    (((this.ev_max - this.ev_base) / (11.999999 - this.luck)) *
      (newLuck - this.luck) +
      this.ev_base);

  return term1 + term2;
}

public calculatePR(newLuck: number): number {
  const controlPos = this.controlPositive(newLuck);
  const controlNeg = this.controlNegative(newLuck);

  const prBaseFraction = this.pr_base / 100; // Convertendo PR para fração

  const term1 = controlNeg * prBaseFraction * (newLuck / this.luck);
  const term2 =
    controlPos *
    (((1 - prBaseFraction) / (11.999999 - this.luck)) *
      (newLuck - this.luck) +
      prBaseFraction);

  return (term1 + term2) * 100; // Convertendo de volta para porcentagem
}


private controlPositive(newLuck: number): number {
  return (this.sign(newLuck - this.luck) + 1) / 2;
}

private controlNegative(newLuck: number): number {
  return 1 - this.controlPositive(newLuck);
}

  
reset(character) {
  this.character = character;
  this.ngOnInit();
}


}
