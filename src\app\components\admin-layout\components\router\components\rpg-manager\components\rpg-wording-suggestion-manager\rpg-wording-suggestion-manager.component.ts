import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { RPGFormatting } from 'src/lib/darkcloud';
import { Subscription } from 'rxjs/internal/Subscription';
import { Dialogue, Level } from 'src/app/lib/@bus-tier/models';
import { comparable } from 'src/lib/others';
import { RpgService } from 'src/app/services/rpg.service';
import { Search } from 'src/lib/darkcloud/index';
import { Suggesting } from 'src/app/lib/@bus-tier';
import { AreaService, CharacterService, ItemService, OptionService, SpeechService, StoryBoxService } from 'src/app/services';
import { RPGType } from 'src/lib/darkcloud/rpg-formatting';
import { Router } from '@angular/router';


@Component({
  selector: 'app-rpg-wording-suggestion-manager',
  templateUrl: './rpg-wording-suggestion-manager.component.html',
})
export class RpgWordingSuggestionManagerComponent implements OnInit, OnDestroy 
{
  SearchResultType = Search.ResultType;
  RPGType = RPGFormatting.RPGType;
  suggestionsIndex: { [id: number]: Suggesting.Suggestion };
  ids: string[];
  all: any[];
  private _suggestionsAreSelected = false;
  private _subscription: Subscription;
  nAnalyzedSuggestions: number;

  constructor(
    protected _rpgService: RpgService, 
    private _router: Router,
    private _itemService: ItemService,
    private _characterService: CharacterService,
    private _storyboxService: StoryBoxService,
    private _speechService: SpeechService,
    private _areaService: AreaService,
    private _optionService: OptionService
    ) {}

  ngOnInit(): void 
  {
    this.suggestionsIndex = this._rpgService.suggestionsIndex;
    this.ids = this._rpgService.idsWithSuggestions.sort((a, b) =>
      comparable(this.suggestionsIndex[a].wordsIdentified[0]) > comparable(this.suggestionsIndex[b].wordsIdentified[0]) ||
      a === undefined ? 1 : -1);

    this._subscription = this._rpgService.idsWithSuggestionsSubject.subscribe(
      (v) => 
      {
        this.ids = v;
        this.ids.forEach((id) => 
        {
          this.suggestionsIndex[id] = this._rpgService.suggestionsIndex[id];
        });
        this.ids.sort((a, b) =>
          comparable(this.suggestionsIndex[a].wordsIdentified[0]) > comparable(this.suggestionsIndex[b].wordsIdentified[0]) ||
          a === undefined ? 1 : -1);
      }
    );

    this.all = this._rpgService.all;
    this._subscription.add(this._rpgService.allSubject.subscribe((v) => { this.all = v;}));

    this.nAnalyzedSuggestions = this._rpgService.nAnalyzedSuggestions;
    this._subscription.add(
      this._rpgService.nAnalyzedSuggestionsSubject.subscribe(
        (v) => (this.nAnalyzedSuggestions = v)
      )
    );
    this._rpgService.allSpeeches = [];
    this.getNotAssignedElements();
  }
  
  detectedSpecialWords = [];
  auxDetections = [];
  getNotAssignedElements()
  {
    //These are words that are surround by special symbols, exemple: <<>>, {{}}, [[]],...
    this.detectedSpecialWords = []

    for(let i = 0; i < this._rpgService.detections[+RPGType.CHARACTER].length; i++)
    {
      this._rpgService.detections[+RPGType.CHARACTER][i].elementType = RPGType.CHARACTER;
      this.detectedSpecialWords.push(this._rpgService.detections[+RPGType.CHARACTER][i]);
    }
    for(let i = 0; i < this._rpgService.detections[+RPGType.GENERAL_ITEM].length; i++)
    {
      this._rpgService.detections[+RPGType.GENERAL_ITEM][i].elementType = RPGType.GENERAL_ITEM;
      this.detectedSpecialWords.push(this._rpgService.detections[+RPGType.GENERAL_ITEM][i]);
    }
    for(let i = 0; i < this._rpgService.detections[+RPGType.PLACE].length; i++)
    {
      this._rpgService.detections[+RPGType.PLACE][i].elementType = RPGType.PLACE;
      this.detectedSpecialWords.push(this._rpgService.detections[+RPGType.PLACE][i]);
    }
    for(let i = 0; i < this._rpgService.detections[+RPGType.CURRENCY].length; i++)
    {
      this._rpgService.detections[+RPGType.CURRENCY][i].elementType = RPGType.CURRENCY;
      this.detectedSpecialWords.push(this._rpgService.detections[+RPGType.CURRENCY][i]);
    }
    for(let i = 0; i < this._rpgService.detections[+RPGType.EXPRESSIONS].length; i++)
    {
      this._rpgService.detections[+RPGType.EXPRESSIONS][i].elementType = RPGType.EXPRESSIONS;
      this.detectedSpecialWords.push(this._rpgService.detections[+RPGType.EXPRESSIONS][i]);
    }
    let avoidDuplicatedElementsNames = [];

    //Get characters names
    for(let i = 0; i < this._characterService.models.length; i++)
    {
      avoidDuplicatedElementsNames.push(this._characterService.models[i].name);
    }

    //Get items names
    for(let i = 0; i < this._itemService.models.length; i++)
    {
      avoidDuplicatedElementsNames.push(this._itemService.models[i].name);
    }

    //Get places names
    for(let i = 0; i < this._areaService.models.length; i++)
    {
      avoidDuplicatedElementsNames.push(this._areaService.models[i].name);
    }
    for(let k = 0; k < this.detectedSpecialWords.length; k++)
    {      
      if(!avoidDuplicatedElementsNames.includes(this.detectedSpecialWords[k].word))
      {
        this.auxDetections.push(this.detectedSpecialWords[k]);
      }      
    }

    this.auxDetections = this.auxDetections.sort((a, b) => 
    {
      return ('' + a.word).localeCompare(b.word);
    });

    this.getSpeechesWhereWordIsNotMarkedAsSpecial();
  }

  hasExactWord(sentence, targetWord):boolean
  {
    const pattern = new RegExp(`\\b${targetWord}\\b`);
    return pattern.test(sentence);
  }

  auxArray = [];
  //For more info look the method name on documentation.
  //We get the speeches that have the word that is not paint but have a chance to be painted. It is an exact match!!!
  getSpeechesWhereWordIsNotMarkedAsSpecial()
  {
    for(let i = 0; i < this.auxDetections.length; i++)
    {
      for(let j = 0; j < this._speechService.models.length; j++)
      {
        let splietedOptionArray = this._optionService.models[j]?.message?.split(this.auxDetections[i]?.word);
        let splietedSpeechArray = this._speechService.models[j]?.message?.split(this.auxDetections[i]?.word);
        //From storybox speeches
        if(splietedSpeechArray?.length > 1 && 
        ((splietedSpeechArray[0] == '' && splietedSpeechArray[1] == '') || 
        (splietedSpeechArray[0] == '' && splietedSpeechArray[1][0] == ' ') ||
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == ' ' && splietedSpeechArray[1] == '') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == ' ' && splietedSpeechArray[1] == '.') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == ' ' && splietedSpeechArray[1] == ',') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == ' ' && splietedSpeechArray[1] == ';') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == ' ' && splietedSpeechArray[1] == '!') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == ' ' && splietedSpeechArray[1] == '?') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == ' ' && splietedSpeechArray[1] == ';') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == ' ' && splietedSpeechArray[1] == '"') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == ' ' && splietedSpeechArray[1] == '\'') || 

        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == '' && splietedSpeechArray[1] == ',') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == '' && splietedSpeechArray[1] == '.') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == '' && splietedSpeechArray[1] == ';') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == '' && splietedSpeechArray[1] == '!') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == '' && splietedSpeechArray[1] == '?') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == '' && splietedSpeechArray[1] == ';') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == '' && splietedSpeechArray[1] == '"') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == '' && splietedSpeechArray[1] == '\'') || 

        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == '.' && splietedSpeechArray[1] == '') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == '.' && splietedSpeechArray[1] == '.') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == '.' && splietedSpeechArray[1] == ',') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == '.' && splietedSpeechArray[1] == ';') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == '.' && splietedSpeechArray[1] == '!') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == '.' && splietedSpeechArray[1] == '?') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == '.' && splietedSpeechArray[1] == ';') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == '.' && splietedSpeechArray[1] == '"') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == '.' && splietedSpeechArray[1] == '\'') ||

        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == ' ' && splietedSpeechArray[1][0] == ' ') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == ' ' && splietedSpeechArray[1][0] == '') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == '' && splietedSpeechArray[1][0] == '.') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == ' '&& splietedSpeechArray[1][0] == '.') ||  

        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == '' && splietedSpeechArray[1][0] == ',') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == ' '&& splietedSpeechArray[1][0] == ',') || 

        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == '' && splietedSpeechArray[1][0] == '!') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == ' '&& splietedSpeechArray[1][0] == '!') ||
        
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == '' && splietedSpeechArray[1][0] == '?') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == ' '&& splietedSpeechArray[1][0] == '?') ||
        
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == '' && splietedSpeechArray[1][0] == ';') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == ' '&& splietedSpeechArray[1][0] == ';') || 

        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == '' && splietedSpeechArray[1][0] == ':') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == ' '&& splietedSpeechArray[1][0] == ':') || 

        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == '' && splietedSpeechArray[1][0] == '"') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == ' '&& splietedSpeechArray[1][0] == '"') ||
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == '"' && splietedSpeechArray[1][0] == '"') || 

        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == '\'' && splietedSpeechArray[1][0] == '\'') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == '' && splietedSpeechArray[1][0] == '\'') || 
        (splietedSpeechArray[0][splietedSpeechArray[0].length-1] == ' '&& splietedSpeechArray[1][0] == '\'') ||
        (splietedSpeechArray[0] == '' && splietedSpeechArray[1][0] == '.') ||
        (splietedSpeechArray[0] == '' && splietedSpeechArray[1][0] == ',') ||
        (splietedSpeechArray[0] == '' && splietedSpeechArray[1][0] == '!') ||
        (splietedSpeechArray[0] == '' && splietedSpeechArray[1][0] == '?') ||
        (splietedSpeechArray[0] == '' && splietedSpeechArray[1][0] == ';') ||
        (splietedSpeechArray[0] == '' && splietedSpeechArray[1][0] == ':') ||
        (splietedSpeechArray[0] == '' && splietedSpeechArray[1][0] == '\'') ||
        (splietedSpeechArray[0] == '' && splietedSpeechArray[1][0] == '"') 
        ))
        {
          //Remove the character word that is already painted
          if(this.auxDetections[i].elementType == +RPGType.CHARACTER && 
            !this._speechService.models[j]?.message?.includes('{{'+this.auxDetections[i]?.word+'}}') &&
            this.canPaintSpecialWord(this.sentencePieceAfterSpecialWordInSpeech(i, j)))
          {
            this.setAllSpeechesWithSpeech(i,j, '{{','}}');
          }
          else if(this.auxDetections[i].elementType == +RPGType.CURRENCY && 
            !this._speechService.models[j]?.message?.includes('<<'+this.auxDetections[i]?.word+'>>')&&
            this.canPaintSpecialWord(this.sentencePieceAfterSpecialWordInSpeech(i, j)))
          {
            this.setAllSpeechesWithSpeech(i,j,'<<','>>');
          }
          else if(this.auxDetections[i].elementType == +RPGType.PLACE && 
            !this._speechService.models[j]?.message?.includes('««'+this.auxDetections[i]?.word+'»»')&&
            this.canPaintSpecialWord(this.sentencePieceAfterSpecialWordInSpeech(i, j)))
          {
            this.setAllSpeechesWithSpeech(i,j,'««','»»');
          }
          else if(this.auxDetections[i].elementType == +RPGType.GENERAL_ITEM && 
            !this._speechService.models[j]?.message?.includes('[['+this.auxDetections[i]?.word+']]')&&
            this.canPaintSpecialWord(this.sentencePieceAfterSpecialWordInSpeech(i, j)))
          {
            this.setAllSpeechesWithSpeech(i,j,'[[',']]');
          }
          else if(this.auxDetections[i].elementType == +RPGType.EXPRESSIONS && 
            !this._speechService.models[j]?.message?.includes('#$'+this.auxDetections[i]?.word+'$#')&&
            this.canPaintSpecialWord(this.sentencePieceAfterSpecialWordInSpeech(i, j)))
          {
            this.setAllSpeechesWithSpeech(i,j,'#$','$#');
          }
        }//From investigation/choice boxes options
        else if( splietedOptionArray?.length > 1 && //The word is inside the sentence.
          ((splietedOptionArray[0] == '' && splietedOptionArray[1] == '') || //'dog' => ['', '']
          (splietedOptionArray[0] == '' && splietedOptionArray[1][0] == ' ') || //'dog ' => ['', ' ']
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == ' ' && splietedOptionArray[1] == '') || //' dog' => [' ', '']
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == ' ' && splietedOptionArray[1] == '.') || //' dog.' => [' ', '.']
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == ' ' && splietedOptionArray[1] == ',') || //' dog,' => [' ', ',']
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == ' ' && splietedOptionArray[1] == ';') || //' dog' => [' ', ';']
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == ' ' && splietedOptionArray[1] == '!') || 
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == ' ' && splietedOptionArray[1] == '?') || 
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == ' ' && splietedOptionArray[1] == ';') || 
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == ' ' && splietedOptionArray[1] == '"') || 
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == ' ' && splietedOptionArray[1] == '\'') || 

          (splietedOptionArray[0][splietedOptionArray[0].length-1] == '' && splietedOptionArray[1] == ',') || //'dog,' => ['', ',']
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == '' && splietedOptionArray[1] == '.') || //'dog.' => ['', '.']
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == '' && splietedOptionArray[1] == ';') || //'dog;' => ['', ';']
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == '' && splietedOptionArray[1] == '!') || 
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == '' && splietedOptionArray[1] == '?') || 
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == '' && splietedOptionArray[1] == ';') || 
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == '' && splietedOptionArray[1] == '"') || 
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == '' && splietedOptionArray[1] == '\'') || 

          (splietedOptionArray[0][splietedOptionArray[0].length-1] == '.' && splietedOptionArray[1] == '.') || //'.dog.' => ['.', '.']
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == '.' && splietedOptionArray[1] == '') || //'.dog' => ['.', '']
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == '.' && splietedOptionArray[1] == ',') || //'.dog' => ['.', ',']
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == '.' && splietedOptionArray[1] == ';') || 
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == '.' && splietedOptionArray[1] == '!') || 
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == '.' && splietedOptionArray[1] == '?') || 
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == '.' && splietedOptionArray[1] == ';') || 
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == '.' && splietedOptionArray[1] == '"') || 
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == '.' && splietedOptionArray[1] == '\'') ||

          (splietedOptionArray[0][splietedOptionArray[0].length-1] == ' ' && splietedOptionArray[1][0] == ' ') || 
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == '' && splietedOptionArray[1][0] == '.') || 
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == ' ' && splietedOptionArray[1][0] == '.') ||
          
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == '' && splietedOptionArray[1][0] == ',') || 
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == ' '&& splietedOptionArray[1][0] == ',') || 
  
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == '' && splietedOptionArray[1][0] == '!') || 
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == ' '&& splietedOptionArray[1][0] == '!') ||
          
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == '' && splietedOptionArray[1][0] == '?') || 
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == ' '&& splietedOptionArray[1][0] == '?') ||
          
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == '' && splietedOptionArray[1][0] == ';') || 
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == ' '&& splietedOptionArray[1][0] == ';') || 
  
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == '' && splietedOptionArray[1][0] == ':') || 
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == ' '&& splietedOptionArray[1][0] == ':') || 
          
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == '' && splietedOptionArray[1][0] == '"') || 
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == ' '&& splietedOptionArray[1][0] == '"') ||
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == '"'&& splietedOptionArray[1][0] == '"') ||

          (splietedOptionArray[0][splietedOptionArray[0].length-1] == '\'' && splietedOptionArray[1][0] == '\'') || 
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == '' && splietedOptionArray[1][0] == '\'') || 
          (splietedOptionArray[0][splietedOptionArray[0].length-1] == ' '&& splietedOptionArray[1][0] == '\'') ||

          (splietedOptionArray[0] == '' && splietedOptionArray[1][0] == '.') || //'dog.' => ['', '.']
          (splietedOptionArray[0] == '' && splietedOptionArray[1][0] == ',') ||
          (splietedOptionArray[0] == '' && splietedOptionArray[1][0] == '!') ||
          (splietedOptionArray[0] == '' && splietedOptionArray[1][0] == '?') ||
          (splietedOptionArray[0] == '' && splietedOptionArray[1][0] == ';') ||
          (splietedOptionArray[0] == '' && splietedOptionArray[1][0] == '\'') ||
          (splietedOptionArray[0] == '' && splietedOptionArray[1][0] == '"') ||
          (splietedOptionArray[0] == '' && splietedOptionArray[1][0] == ':') 
          ))
        {
          //Remove the character word that is already painted
          if(this.auxDetections[i].elementType == +RPGType.CHARACTER && 
            !this._optionService.models[j]?.message?.includes('{{'+this.auxDetections[i]?.word+'}}')&&
            this.canPaintSpecialWord(this.sentencePieceAfterSpecialWordInOption(i, j)))
          {
            this.setAllSpeechesWithOption(i,j,'{{','}}');
          }
          else if(this.auxDetections[i].elementType == +RPGType.CURRENCY && 
            !this._optionService.models[j]?.message?.includes('<<'+this.auxDetections[i]?.word+'>>')&&
            this.canPaintSpecialWord(this.sentencePieceAfterSpecialWordInOption(i, j)))
          {
            this.setAllSpeechesWithOption(i,j,'<<','>>');
          }
          else if(this.auxDetections[i].elementType == +RPGType.PLACE && 
            !this._optionService.models[j]?.message?.includes('««'+this.auxDetections[i]?.word+'»»')&&
            this.canPaintSpecialWord(this.sentencePieceAfterSpecialWordInOption(i, j)))
          {
            this.setAllSpeechesWithOption(i,j,'««','»»');
          }
          else if(this.auxDetections[i].elementType == +RPGType.GENERAL_ITEM && 
            !this._optionService.models[j]?.message?.includes('[['+this.auxDetections[i]?.word+']]')&&
            this.canPaintSpecialWord(this.sentencePieceAfterSpecialWordInOption(i, j)))
          {
            this.setAllSpeechesWithOption(i,j,'[[',']]');
          }
          else if(this.auxDetections[i].elementType == +RPGType.EXPRESSIONS && 
            !this._optionService.models[j]?.message?.includes('#$'+this.auxDetections[i]?.word+'$#')&&
            this.canPaintSpecialWord(this.sentencePieceAfterSpecialWordInOption(i, j)))
          {
            this.setAllSpeechesWithOption(i,j,'#$','$#');
          }
        }
      }
    }
    this.auxArray = this._rpgService.allSpeeches;
  }

  setAllSpeechesWithSpeech(i:number, j:number, leftClose:string, rightClose:string)
  {
    this._rpgService.allSpeeches.push
    ({
      id:this._speechService.models[j].id, 
      word: this.auxDetections[i]?.word,
      message:
        this.canPaintSpecialWord(this.sentencePieceAfterSpecialWordInSpeech(i, j)) ? 
        //change simple word by special symbol, ex: casa => <<casa>>
        this._speechService.models[j]?.message.replace(this.auxDetections[i]?.word,leftClose+this.auxDetections[i]?.word+rightClose) : 
        this._speechService.models[j]?.message,
      selected: false,
      elementType: this.auxDetections[i].elementType
    });
  }

  setAllSpeechesWithOption(i:number, j:number, leftClose:string, rightClose:string)
  {
    this._rpgService.allSpeeches.push
    ({
      id:this._optionService.models[j].id, 
      word: this.auxDetections[i]?.word,
      message:
        this.canPaintSpecialWord(this.sentencePieceAfterSpecialWordInOption(i, j)) ?    
        //change simple word by word + special symbol, ex: casa => <<casa>>
        this._optionService.models[j]?.message.replace(this.auxDetections[i]?.word,leftClose+this.auxDetections[i]?.word+rightClose) : 
        this._optionService.models[j]?.message,
      selected: false,
      elementType: this.auxDetections[i].elementType
    });
  }

  canPaintSpecialWord(word:string): boolean
  {
    for(let i = 0; i < word.length; i++)
    {
      //If we find an open symbol it means we cannot paint the already painted word.
      if(word[i] == '}' || word[i] == ']' || word[i] == '»' || word[i] == '>' || word[i] == '$')
      {
        return false;
      }
    }
    return true;
  }

  sentencePieceAfterSpecialWordInOption = (i, j):string => this._optionService.models[j]?.message.split(this.auxDetections[i]?.word)[1];
  sentencePieceAfterSpecialWordInSpeech = (i, j):string => this._speechService.models[j]?.message.split(this.auxDetections[i]?.word)[1];


  toggleAllNoIdetifieds()
  {
    this._rpgService.allSpeeches = [];
    for(let i = 0; i < this.auxArray.length; i++)
    {
      this.auxArray[i].selected = !this.auxArray[i].selected;
    }

    this._rpgService.allSpeeches = this.auxArray;
  }

  toggleNoIdetifieds(field)
  {
    for(let i = 0; i < this._rpgService.allSpeeches.length; i++)
    {
      if(this._rpgService.allSpeeches[i].id == field.id)
      {
        this._rpgService.allSpeeches[i].selected = !this._rpgService.allSpeeches[i].selected;
        break;
      }
    }
  }

  toggleSelectAllSuggestions(type: Search.ResultType) 
  {
    this._suggestionsAreSelected = !this._suggestionsAreSelected;
    this.ids
      .filter((id) => +this.suggestionsIndex[id].type === type)
      .forEach((id) => (this.suggestionsIndex[id].accepted = this._suggestionsAreSelected));
  }

  async acceptSelectedSuggestions() 
  {
    await this._rpgService.acceptSuggestions(this.suggestionsIndex);
  }

  access(id: string): void 
  {
    this._router.navigate(
      [
        'levels/' + Level.getSubIdFrom(id) + '/dialogues/' + Dialogue.getSubIdFrom(id, 'PT-BR'),
      ],
      { fragment: id }
    );
  }

  trackById(index: number, id): any 
  {
    return this.suggestionsIndex[id];
  }
  
  ngOnDestroy(): void 
  {
    this._subscription.unsubscribe();
  }
}
