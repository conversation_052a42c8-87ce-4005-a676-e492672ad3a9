import { Component, Input } from '@angular/core';
import { Dialogue } from 'src/app/lib/@bus-tier/models';
import { OptionBoxService, DilemmaBoxService } from 'src/app/services';
import { OptionService } from 'src/app/services/option.service';
import { DialogueTreeUtilityService } from 'src/app/services/dialogue-tree-utility.service';

@Component({
  selector: 'dialogue-tree-view',
  templateUrl: './dialogue-tree-view.html',
  styleUrls: ['./dialogue-tree-view.css']
})
/**
 * Main dialogue tree view component that displays interactive dialogue trees.
 * Handles user selections for choice, investigation, and dilemma options.
 * Supports roadblock evaluation to show/hide elements based on dependencies.
 */
export class DialogueTreeViewComponent
{
  /** The dialogue data to display in the tree */
  @Input() public dialogue: Dialogue;

  /** Map of selected choice options: optionBoxId -> selectedOptionId */
  public selectedChoiceOptions: Map<string, string> = new Map();

  /** Map of selected investigation options: optionBoxId -> Set of selectedOptionIds */
  public selectedInvestigationOptions: Map<string, Set<string>> = new Map();

  /** Map of selected dilemma options: dilemmaBoxId -> selectedDilemmaId */
  public selectedDilemmaOptions: Map<string, string> = new Map();

  /** Whether roadblock evaluation is enabled (affects element visibility) */
  public roadblockEvaluationEnabled: boolean = true;

  constructor(
    private _optionBoxService: OptionBoxService,
    private _dilemmaBoxService: DilemmaBoxService,
    private _optionService: OptionService,
    private _dialogueTreeUtility: DialogueTreeUtilityService
  ) {}

  /**
   * Handle choice option selection from child components.
   * Choice options allow only single selection per option box.
   * @param optionBoxId - The ID of the option box containing the option
   * @param optionId - The selected option ID, or null to deselect
   */
  public onChoiceOptionSelected(optionBoxId: string, optionId: string | null): void {
    if (optionId === null) {
      this.selectedChoiceOptions.delete(optionBoxId);
    } else {
      this.selectedChoiceOptions.set(optionBoxId, optionId);
    }
  }

  /**
   * Handle investigation option selection from child components.
   * Investigation options allow multiple selections per option box.
   * @param optionBoxId - The ID of the option box containing the option
   * @param optionId - The option ID to toggle
   * @param selected - Whether the option should be selected or deselected
   */
  public onInvestigationOptionSelected(optionBoxId: string, optionId: string, selected: boolean): void {
    let selectedOptions = this.selectedInvestigationOptions.get(optionBoxId);
    if (!selectedOptions) {
      selectedOptions = new Set<string>();
      this.selectedInvestigationOptions.set(optionBoxId, selectedOptions);
    }

    if (selected) {
      selectedOptions.add(optionId);
    } else {
      selectedOptions.delete(optionId);
      // Clean up empty sets to keep the map tidy
      if (selectedOptions.size === 0) {
        this.selectedInvestigationOptions.delete(optionBoxId);
      }
    }
  }

  /**
   * Handle dilemma option selection from child components.
   * Dilemma options allow only single selection per dilemma box.
   * @param dilemmaBoxId - The ID of the dilemma box containing the option
   * @param dilemmaId - The selected dilemma ID, or null to deselect
   */
  public onDilemmaOptionSelected(dilemmaBoxId: string, dilemmaId: string | null): void {
    if (dilemmaId === null) {
      this.selectedDilemmaOptions.delete(dilemmaBoxId);
    } else {
      this.selectedDilemmaOptions.set(dilemmaBoxId, dilemmaId);
    }
  }

  /**
   * Clear all user selections across all option types.
   * Resets the dialogue tree to its initial state.
   */
  public clearSelections(): void {
    this.selectedChoiceOptions.clear();
    this.selectedInvestigationOptions.clear();
    this.selectedDilemmaOptions.clear();
  }

  /**
   * Toggle roadblock evaluation on/off.
   * When disabled, all elements are visible regardless of roadblock dependencies.
   */
  public toggleRoadblockEvaluation(): void {
    this.roadblockEvaluationEnabled = !this.roadblockEvaluationEnabled;
  }

  /**
   * Get a formatted string describing the current selection count for display.
   * Used in the UI to show how many options are currently selected.
   * @returns A string like "3 selections" or "No selections"
   */
  public getRelevantSelectionsInfo(): string {
    const choiceCount = this.selectedChoiceOptions?.size ?? 0;
    const dilemmaCount = this.selectedDilemmaOptions?.size ?? 0;

    // Count total investigation selections across all option boxes
    let investigationCount = 0;
    if (this.selectedInvestigationOptions) {
      for (const selectedSet of this.selectedInvestigationOptions.values()) {
        investigationCount += selectedSet.size;
      }
    }

    const total = choiceCount + dilemmaCount + investigationCount;
    return total === 0 ? "No selections" : `${total} selection${total === 1 ? '' : 's'}`;
  }

  /**
   * Check if there are any selections made by the user.
   * Used to enable/disable the "Clear Selections" button.
   * @returns True if any options are selected, false otherwise
   */
  public hasAnySelections(): boolean {
    // Check choice selections
    if (this.selectedChoiceOptions?.size > 0) {
      return true;
    }

    // Check dilemma selections
    if (this.selectedDilemmaOptions?.size > 0) {
      return true;
    }

    // Check investigation selections
    if (this.selectedInvestigationOptions) {
      for (const selectedSet of this.selectedInvestigationOptions.values()) {
        if (selectedSet.size > 0) {
          return true;
        }
      }
    }

    return false;
  }


  /**
   * Check if the previous layer in the dialogue tree has option roadblocks.
   * This is used to determine branch styling and spacing for visual consistency.
   * @param currentIndex - The current layer index in the dialogue tree
   * @returns True if the previous layer has option roadblocks, false otherwise
   */
  hasPreviousLayerOptionRoadblocks(currentIndex: number): boolean {
    // Validate input parameters
    if (currentIndex <= 0 || !this.dialogue?.boxIds) {
      return false;
    }

    // Calculate the actual box index (accounting for start/end layers)
    const actualBoxIndex = currentIndex - 2;
    if (actualBoxIndex < 0 || actualBoxIndex >= this.dialogue.boxIds.length) {
      return false;
    }

    const previousLayerId = this.dialogue.boxIds[actualBoxIndex];
    if (!previousLayerId) {
      return false;
    }

    const boxType = this._dialogueTreeUtility.detectBoxType(previousLayerId);

    // Check option box roadblocks (including dice variants)
    if (boxType === 'OptionBox') {
      const optionBox = this._optionBoxService.svcFindById(previousLayerId);
      if (optionBox?.optionIds) {
        // Check regular options first
        if (this.checkOptionsForRoadblocks(optionBox.optionIds)) {
          return true;
        }

        // Also check dice negative outcomes (dice variants)
        const diceNegativeOutcomeIds = this.collectDiceNegativeOutcomes(optionBox.optionIds);
        if (diceNegativeOutcomeIds.length > 0) {
          for (const diceId of diceNegativeOutcomeIds) {
            if (this._dialogueTreeUtility.hasRoadblocks(diceId)) {
              return true;
            }
          }
        }
      }
    }

    // Check dilemma box roadblocks
    if (boxType === 'DilemmaBox') {
      const dilemmaBox = this._dilemmaBoxService.svcFindById(previousLayerId);
      if (dilemmaBox?.optionDilemmaIds) {
        return this.checkDilemmasForRoadblocks(dilemmaBox.optionDilemmaIds);
      }
    }

    return false;
  }

  /**
   * Check if any options in the given list have roadblocks on their answer boxes.
   * Used to determine if special styling should be applied for roadblock spacing.
   * @param optionIds - Array of option IDs to check
   * @returns True if any option has roadblocks, false otherwise
   */
  private checkOptionsForRoadblocks(optionIds: string[]): boolean {
    return this._dialogueTreeUtility.checkOptionsForRoadblocks(optionIds);
  }

  /**
   * Check if any dilemmas in the given list have roadblocks on their answer boxes.
   * Used to determine if special styling should be applied for roadblock spacing.
   * @param dilemmaIds - Array of dilemma IDs to check
   * @returns True if any dilemma has roadblocks, false otherwise
   */
  private checkDilemmasForRoadblocks(dilemmaIds: string[]): boolean {
    return this._dialogueTreeUtility.checkDilemmasForRoadblocks(dilemmaIds);
  }

  /**
   * Collect dice negative outcome IDs from a list of option IDs.
   * This matches the logic used in DialogueTreeLayerComponent.
   * @param optionIds - Array of option IDs to check for dice variants
   * @returns Array of dice negative outcome StoryBox IDs
   */
  private collectDiceNegativeOutcomes(optionIds: string[]): string[] {
    const diceNegativeOutcomeIds: string[] = [];

    if (optionIds) {
      optionIds.forEach(optionId => {
        const option = this._optionService.svcFindById(optionId);
        if (option?.answerBoxNegativeId) {
          diceNegativeOutcomeIds.push(option.answerBoxNegativeId);
        }
      });
    }

    return diceNegativeOutcomeIds;
  }
}

