<div class="m-container">

</div>
<table class="table-bordered">
    <thead>
        <tr>
            <th rowspan="2" class="trBC" style="width: 10%;">INDEX</th>
            <th colspan="5" class="trBC">PASSIVE ALLOWED FOR WEAPONS</th>
        </tr>
        <tr>
            <th>ID1</th>
            <th>ID2</th>
        </tr>
    </thead>
    <tbody>
        <ng-container *ngIf="passiveAllowed.length > 0">
            <tr *ngFor="let pass of passiveAllowed; let i = index">
                <td>{{ i + 1 }}</td>
                <td class="bc" [ngStyle]="{'background-color': pass?.idValue1? 'white' : '#404040'}">{{pass?.idValue1}}
                </td>
                <td class="bc" [ngStyle]="{'background-color': pass?.idValue2? 'white' : '#404040'}">{{pass?.idValue2}}
                </td>
            </tr>
        </ng-container>
    </tbody>
</table>

<ng-container *ngIf="passiveAllowed.length === 0">
    <div style="text-align: center;">
        <h4>Empty List</h4>
    </div>
</ng-container>