import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes, VideoType } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';

export abstract class Video<
    THardData extends Data.Hard.IVideo = Data.Hard.IVideo,
    TResult extends {} = {}
  >
  extends Base<THardData, TResult>
  implements Required<Data.Hard.IVideo>
{
  protected static generateId(index: number): string {
    return IdPrefixes.VIDEO + index;
  }
  public get type(): VideoType {
    return this.hard.type;
  }
  public get areaId(): string {
    return this.hard.areaId;
  }
  public set areaId(value: string) {
    this.hard.areaId = value;
  }
}
