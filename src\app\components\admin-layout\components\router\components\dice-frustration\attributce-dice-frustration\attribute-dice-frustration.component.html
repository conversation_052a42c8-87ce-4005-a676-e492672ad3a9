<div class="list-header-row update">
    <div class="card">
        <div style="display: flex; justify-content: space-between;">
            <div class="card-header-content">
                <h3 class="title">Attribute - Dice Frustration</h3>
                <p style="width:60vw;" class="category">{{ description}}</p>
            </div>
            <div style="display: flex; align-items: end; justify-content: end; align-items: center;">
                <ng-container>
                    <!--BUTTON EXCEL-->
                    <div id="button" style="position: absolute; margin-top: 60px;">
                        <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'"
                            class="add-buttons" [buttonTemplates]="[excelButtonTemplate]">
                        </app-button-group>
                    </div>
                </ng-container>
            </div>
        </div>
    </div>

    <ng-container *ngIf="listFrustrationLevels.length > 0">
        <div style="display: flex; flex-direction: row; overflow-x: auto; width: 100%;">
            <table class="table table-list borderList">
                <thead>
                    <tr>
                        <th>SUBCONTEXT</th>
                        <th *ngFor="let title of titles"> {{ title }} <div class="ball-circle"></div></th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    <ng-container *ngFor="let item of listFrustrationLevels; let i = index">
                        <tr>
                            <!-- Primeira coluna com rowspan -->
                            <td class="td-id aligTitle" [attr.rowspan]="item.light.length">
                                {{ item.subContext }}
                            </td>
                            <!-- Primeira linha para light, moderate e critical -->
                            <td class="td-id">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #light [value]="item.light[0]"
                                    (change)="changeFrustrationValue(i, 0, 'light', light.value)" />
                            </td>
                            <td class="td-id">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #moderate [value]="item.moderate[0]"
                                    (change)="changeFrustrationValue(i, 0, 'moderate', moderate.value)" />
                            </td>
                            <td class="td-id">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #critical [value]="item.critical[0]"
                                    (change)="changeFrustrationValue(i, 0, 'critical', critical.value)" />
                            </td>
                            <td style="width: 60px;">             
                                <button class="btn btn-gray btn-fill translation-button" (click)="downloadAttributeDiceOrtography(item, 0)">
                                <div class="mat-translate"></div>
                                </button>
                           </td>    
                        </tr>
                        <!-- Linhas subsequentes (light, moderate, critical) sem subContext -->
                        <tr *ngFor="let light of item.light.slice(1); let j = index">
                            <td class="td-id">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #light2 [value]="item.light[j + 1]"
                                    (change)="changeFrustrationValue(i, j + 1, 'light', light2.value)" />
                            </td>
                            <td class="td-id">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #moderate [value]="item.moderate[j + 1]"
                                    (change)="changeFrustrationValue(i, j + 1, 'moderate', moderate.value)" />
                            </td>
                            <td class="td-id">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #critical [value]="item.critical[j + 1]"
                                    (change)="changeFrustrationValue(i, j + 1, 'critical', critical.value)" />
                            </td>                     
                                <td style="width: 60px;">
                                    <button class="btn btn-gray btn-fill translation-button" (click)="downloadAttributeDiceOrtography(item, j + 1)">
                                    <div class="mat-translate"></div>
                                    </button>
                               </td>                           
                        </tr>
                        <tr>
                            
                        </tr>
                    </ng-container>
                </tbody>
            </table>
        </div>
    </ng-container>


    <ng-container *ngIf="listFrustrationLevels.length === 0">
        <div class="card" style="text-align: center; padding: 20px;">
            <h3>Empty list. Click to create the list.</h3>
        </div>
    </ng-container>

</div>