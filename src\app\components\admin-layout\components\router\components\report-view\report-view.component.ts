import { Component, OnInit } from '@angular/core';
import { FILTER_SUFFIX_PATH } from 'src/lib/darkcloud/angular/dsadmin/constants/others';

@Component({
  selector: 'app-report-view',
  templateUrl: './report-view.component.html',
  styleUrls: ['./report-view.component.scss']
})
export class ReportViewComponent implements OnInit 
{
  public activeTab: string;

  ngOnInit(): void 
  {
    const tab = localStorage.getItem(`tab-ReportComponent${FILTER_SUFFIX_PATH}`);
    this.activeTab = tab === 'null' || !tab ? 'unlock' : tab;
  }

  public switchToTab(tab: string) 
  {
    this.activeTab = tab;
    localStorage.setItem(`tab-ReportComponent${FILTER_SUFFIX_PATH}`, this.activeTab);
  }
}
