    <div class="card card-header">
      <div style="width: 40%;">
        <app-header-with-buttons [isBackButtonEnabled]="true"
                                 (cardBackButtonClick)="redirectToSettings()"
                                 [cardTitle]="title"
                                 [cardDescription]="'Describes the scale of each DC assigned to opponents.'">
        </app-header-with-buttons>
      </div>

      <div class="positian-Buttons">
        <button class="{{activeTab === 'attributeGuide' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
        (click)="switchToTab('attributeGuide')">
        DC Attribute Guide
      </button>
      <button class="{{activeTab === 'knowledgeGuide' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
        (click)="switchToTab('knowledgeGuide')">
        DC Knowledge Guide
      </button>
      </div>

      <div id="master" class="div-excel">
        <div id="button" style="position: absolute; top: -30px;">
          <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'" class="add-buttons"
            [buttonTemplates]="[excelButtonTemplate]">
          </app-button-group>
        </div>
      </div>
    </div>

    <app-dc-attribute-guide *ngIf="activeTab === 'attributeGuide'"  [copyAttributeBehavior]="listExcel"></app-dc-attribute-guide>
    <app-dc-knowledge-guide *ngIf="activeTab === 'knowledgeGuide'" [copyKnowledgeBehavior]="listExcel"></app-dc-knowledge-guide>

  