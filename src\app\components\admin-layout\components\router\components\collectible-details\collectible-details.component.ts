import { Component, OnInit, QueryList, ViewChildren } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Character, Item } from 'src/app/lib/@bus-tier/models';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';
import { Button } from 'src/app/lib/@pres-tier/data';
import { CharacterService, ItemService } from 'src/app/services';
import { CustomService } from 'src/app/services/custom.service';
import { FILTER_SUFFIX_PATH } from 'src/lib/darkcloud/angular/dsadmin/constants/others';
import { ICollectibleDetails } from 'src/lib/darkcloud/angular/dsadmin/v9/data/result';

@Component({
  selector: 'app-collectible-details',
  templateUrl: './collectible-details.component.html',
  styleUrls: ['./collectible-details.component.scss'],
})
export class CollectibleDetailsComponent implements OnInit {

  @ViewChildren('query')
  childGames: QueryList<ICollectibleDetails>
  public activeTab: string;
  custom: Custom;
  item: Item;
  characterFromQuery = '';
  currentCharacter : Character;
  characterListIndex = 0;
  collectibleFromQueryList = [];
  characterList = []; 
  listOptionSelectedCharacters = [];


  constructor(
    protected _customService: CustomService,
    protected _itemService: ItemService,
    protected _characterService : CharacterService,
    private _router: Router,
    private _activatedRoute: ActivatedRoute,
  ){}

  public readonly leftButtonTemplate: Button.Templateable = 
  {
    title: 'Back to last character',
    onClick: this.leftButton.bind(this),
    iconClass: 'pe-7s-angle-left',
    btnClass: Button.Klasses.FILL_BRIGHTBLUE,
  };

  public readonly rightButtonTemplate: Button.Templateable = 
  {
    title: 'Go to next character',
    onClick: this.rightButton.bind(this),
    iconClass: 'pe-7s-angle-right',
    btnClass: Button.Klasses.FILL_BRIGHTBLUE,
  };

  async ngOnInit(): Promise<void>
  {
    this.characterFromQuery = await this._activatedRoute.snapshot.queryParams['character'];
    this.collectibleFromQueryList = await this._activatedRoute.snapshot.queryParams['collectibles']; 
    this.characterList = await this._activatedRoute.snapshot.queryParams['charactersListIds'];
    this.listOptionSelectedCharacters = this._activatedRoute.snapshot.queryParams['listOptionCharacters'];
    this.currentCharacter = this._characterService.svcFindById(this.characterFromQuery);
  
    for(let i = 0; i < this.characterList.length; i++)
    {
      if(this.characterList[i] === this.characterFromQuery)
      {
        this.characterListIndex = i;      
        break;
      }     
    } 
    this.custom = await this._customService.svcGetInstance();
    this.item = this._itemService.svcFindById(this.custom.selectedWeaponId);

    this.getBattle()  
    this.resetAll();
  }

  getBattle(){
    if (this.activeTab !== 'elementalAff' && this.activeTab !== 'ailmentDefenses' && this.activeTab !== 'primal' && this.activeTab !== 'specialSkill' 
      && this.activeTab !== 'ctr' && this.activeTab !== 'luk' && this.activeTab !== 'int' && this.activeTab !== 'spd') {
      if(this.currentCharacter.rarity === 'Inferior') {
         this.activeTab = 'battleInferior' ; 
         localStorage.setItem(`tab-ItemDetailsReportComponent${FILTER_SUFFIX_PATH}`, this.activeTab); 
       } else {
          this.activeTab = 'battle';
          localStorage.setItem(`tab-ItemDetailsReportComponent${FILTER_SUFFIX_PATH}`, this.activeTab);
       }   
    }
  }
  
leftButton() {
  // Se estiver no início da lista, vá para o último elemento
  if (this.characterListIndex === 0) {
    this.characterListIndex = this.characterList.length - 1;
  } else {
    this.characterListIndex -= 1;
  }

  const nextCharacter = this.characterList[this.characterListIndex];
  this.currentCharacter = this._characterService.svcFindById(nextCharacter);
  this.characterFromQuery = nextCharacter;    
  this.resetAll();
  this.getBattle()
}

rightButton() {
  // Se estiver no fim da lista, vá para o primeiro elemento
  if (this.characterListIndex === this.characterList.length - 1) {
    this.characterListIndex = 0;
  } else {
    this.characterListIndex += 1;
  }

  const nextCharacter = this.characterList[this.characterListIndex];
  this.currentCharacter = this._characterService.svcFindById(nextCharacter);
  this.characterFromQuery = nextCharacter;   
  this.resetAll();
  this.getBattle()
}
  resetAll() 
  {   
    this.childGames.forEach(c => c.reset(this.characterFromQuery))
  }

  public switchToTab(tab: string) 
  {
    this.activeTab = tab;
    localStorage.setItem(`tab-ItemDetailsReportComponent${FILTER_SUFFIX_PATH}`, this.activeTab);
  }

  public onBack()
  {// devole a lista de character ao voltar para o Character Rarity
    this._router.navigate(['others'], {queryParams: {charactersListIds: this.characterList, listOptionCharacters: this.listOptionSelectedCharacters}, skipLocationChange: true});
  }

}
