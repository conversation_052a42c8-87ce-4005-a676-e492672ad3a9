<div class="btn-group" style="display: flex; padding-top: 6px;">
  <ng-container *ngFor="let buttonTemplate of buttonTemplates; let i = index">
    <button [title]="buttonTemplate.title" class="btn" [ngClass]="buttonTemplate.btnClass | buttonClassFormat"
      (click)="buttonTemplate.onClick(null)" [style.margin-left]="i > 0 ? '5px' : '0'">
      {{ buttonTemplate.text }}
      <i [class]="buttonTemplate.iconClass"></i>
    </button>
  </ng-container>
</div>