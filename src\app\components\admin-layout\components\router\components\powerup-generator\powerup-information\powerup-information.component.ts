import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Item, PowerUp, PowerUpStat } from 'src/app/lib/@bus-tier/models';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';
import { Particle } from 'src/app/lib/@bus-tier/models/Particle';
import { Button } from 'src/app/lib/@pres-tier/data';
import { ItemService, ParticleService } from 'src/app/services';
import { CustomService } from 'src/app/services/custom.service';
import { ItemClassService } from 'src/app/services/item-class.service';
import { PowerUpService } from 'src/app/services/powerup.service';
import { PowerUpStatService } from 'src/app/services/powerupstat.service';
import { SpinnerService } from 'src/app/spinner/spinner.service';
import { Alert } from 'src/lib/darkcloud';
import { FILTER_SUFFIX_PATH } from 'src/lib/darkcloud/angular/dsadmin/constants/others';

@Component({
  selector: 'app-powerup-information',
  templateUrl: './powerup-information.component.html',
  styleUrls: ['./powerup-information.component.scss'],
})
export class PowerupInformationComponent implements OnInit {
  public readonly excelButtonTemplate: Button.Templateable =
    {
      title: 'Paste content from excel',
      onClick: this.onExcelPaste.bind(this),
      iconClass: 'excel-icon',
      btnClass: Button.Klasses.FILL_ORANGE,
    };

  lastLab = undefined;

  @Input() activeTab: string;
  custom: Custom;
  item: Item;
  powerups: PowerUp[];
  powerupsStats: PowerUpStat[];
  particles: Particle[];
  title: string;
  description: string;
  selectedClasses = [];
  selectedStatsClasses = [];
  ids = [];
  idsStats = [];

  constructor(
    private spinnerService: SpinnerService,
    protected _customService: CustomService,
    protected _itemService: ItemService,
    private _powerupService: PowerUpService,
    private _powerupStatService: PowerUpStatService,
    private _particleService: ParticleService,
    private _itemClassService: ItemClassService,
    private _router: Router,
    private changeDetectorRef: ChangeDetectorRef
  ) { }



  async ngOnInit(): Promise<void> {
    await this._customService.toFinishLoading();
    await this._powerupService.toFinishLoading();
    await this._powerupStatService.toFinishLoading();
    await this._particleService.toFinishLoading();
    await this._itemService.toFinishLoading();
    await this._itemClassService.toFinishLoading();

    this.custom = await this._customService.svcGetInstance();

    //this.item = this._itemService.svcFindById(this.custom.selectedWeaponId);

    if (!this.custom.particlesOrder)
      this.custom.particlesOrder = [];


    if (this.custom.particlesOrder.length != this._particleService.models.length) {
      this.custom.particlesOrder = [];
      this._particleService.models.forEach((particle) => {
        this.custom.particlesOrder.push(particle.id);
      });
    }
    this.particles = this._particleService.models;

    this.selectPowerupsStatsClasses();
    this.selectPowerupsClasses();

    this.intializeTab();
  }

  selectPowerupsClasses() {
    this.selectedClasses = this._itemClassService.models.filter((klass) => {
      return this.custom.powerupClassItem.includes(klass.id);
    });
    let itemIds = [];

    this.selectedClasses.forEach((itemClass) => {
      itemClass.itemIds.forEach((itemId) => {
        itemIds.push(this._itemService.svcFindById(itemId));
      });
    });

    this.ids = [];
    itemIds.forEach((id) => {
      this.ids.push(id.id);
    });

    this.powerups = [];

    this._powerupService.models.forEach((sw) => {
      if (this.ids.includes(sw.itemId)) {
        this.powerups.push(sw);
        this.ids = this.ids.filter((id) => id !== sw.itemId);
      }
    });

    this.ids.forEach((id) => {
      this.powerups.push(this._powerupService.createNewPowerUp(id));
    });

    this._powerupService.toSave();
    this.powerups.concat(this._powerupService.models);
  }

  selectPowerupsStatsClasses() {
    this.selectedStatsClasses = this._itemClassService.models.filter((klass) => {
      return this.custom.powerupClassItem.includes(klass.id);
    }
    );
    let itemIds = [];

    this.selectedStatsClasses.forEach((itemClass) => {
      itemClass.itemIds.forEach((itemId) => {
        itemIds.push(this._itemService.svcFindById(itemId));
      });
    });

    this.idsStats = [];
    itemIds.forEach((id) => {
      this.ids.push(id.id);
    });

    this.powerupsStats = [];

    this._powerupStatService.models.forEach((sw) => {
      if (this.ids.includes(sw.itemId)) {
        this.powerupsStats.push(sw);
        this.ids = this.ids.filter((id) => id !== sw.itemId);
      }
    });

    this.ids.forEach((id) => {
      this.powerupsStats.push(this._powerupStatService.createNewPowerUpStat(id));
    });

    this._powerupStatService.toSave();
    this.powerupsStats.concat(this._powerupStatService.models);
  }

  intializeTab() {
    const tab = localStorage.getItem(`tab-PowerupInformationComponent${FILTER_SUFFIX_PATH}`);

    this.activeTab = tab === 'null' || !tab ? 'Ingredients' : tab;

    if (this.activeTab === 'Ingredients') {
      this.title = 'Ingredients for construction';
      this.description = `Showing ${this.powerups.length} results`;
    } else {
      this.title = 'Distribuition of statistics';
      this.description = `Showing ${this.powerupsStats.length} results`;
    }
  }

  public switchToTab(tab: string) {
    this.activeTab = tab;
    localStorage.setItem(`tab-PowerupInformationComponent${FILTER_SUFFIX_PATH}`, this.activeTab);

    if (this.activeTab === 'Ingredients') {
      this.title = 'Ingredients for construction';
      this.description = `Showing ${this.powerups.length} results`;
    }
    else {
      this.title = 'Distribuition of statistics';
      this.description = `Showing ${this.powerupsStats.length} results`;
    }
    this.changeDetectorRef.detectChanges();
  }

  GetParticleName(particleId: string): string {
    return this._itemService.svcFindById(this._particleService.svcFindById(particleId).itemId)?.name;
  }

  changeParticleOrder(particleId: string, index: number) {
    this.custom.particlesOrder[index] = particleId;
    this._customService.svcToModify(this.custom);
    this._customService.toSave();
  }

  GetPowerupName(powerupId: string): string {
    return this._itemService.svcFindById(this._powerupService.svcFindById(powerupId).itemId).name;
  }

  GetPowerupStatName(powerupStatId: string): string {
    return this._itemService.svcFindById(this._powerupStatService.svcFindById(powerupStatId).itemId).name;
  }

  public onBack() {
    this._router.navigate(['powerupGenerator']);
  }

  changePowerUpValue(powerup: PowerUp, value: string, fieldName: string) {
    powerup[fieldName] = value == '' ? undefined : +value;
    this._powerupService.svcToModify(powerup);
    this._powerupService.toSave();
  }

  changePowerUpStatValue(powerup: PowerUpStat, value: string, fieldName: string) {
    powerup[fieldName] = value == '' ? undefined : +value;
    this._powerupStatService.svcToModify(powerup);
    this._powerupStatService.toSave();
  }

  GetParticleValue(powerup: PowerUp, index: number) {
    if (!powerup.particlesValue)
      powerup.particlesValue = [];
    return powerup.particlesValue[index];
  }

  changeParticleValue(powerup: PowerUp, value: string, index: number) {
    powerup.particlesValue[index] = value == '' ? undefined : +value;
    this._powerupService.svcToModify(powerup);
    this._powerupService.toSave();
  }

  async onExcelPaste(): Promise<void> {
    if (this.activeTab === 'Ingredients')
      this.PasteIngredients();
    else
      this.PasteStats();
  }

  async PasteIngredients() {
    this.spinnerService.setState(true)
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter((line) => line);

    if (this.displayErrors(lines, this.particles.length + 5)) return;

    for (let l = 0; l < lines.length; l++) {
      let line = lines[l];
      let rows = line.split(/\t/);

      let searchName = this.simplifyString(rows[0]);
      this.powerups.forEach((powerup) => {
        let item = this._itemService.svcFindById(powerup.itemId);
        if (this.simplifyString(item.name) === searchName)
          this.PasteIngredientsLine(powerup, rows);
      });
    }
    this.spinnerService.setState(false)
  }

  async PasteIngredientsLine(powerup: PowerUp, rows: string[]) {
    if (rows[1]?.trim()) {
      powerup.labLevel = +rows[1]
        .split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',', '.');
      this.lastLab = powerup.labLevel;
    }
    else {
      powerup.labLevel = this.lastLab;
    }
    if (rows[2]?.trim()) {
      powerup.souls = +rows[2]
        .split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',', '.');
    }
    else {
      powerup.souls = undefined;
    }
    if (rows[3]?.trim()) {
      powerup.time = +rows[3]
        .split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',', '.');
    }
    else {
      powerup.time = undefined;
    }
    if (rows[4]?.trim()) {
      powerup.rubies = +rows[4]
        .split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',', '.');
    }
    else {
      powerup.rubies = undefined;
    }

    for (let i = 5; i < rows.length; i++) {
      if (rows[i]?.trim()) {
        powerup.particlesValue[i - 5] = +rows[i]
          .split(' ')
          .join('')
          .split('.')
          .join('')
          .replace(',', '.');
      }
      else {
        powerup.particlesValue[i - 5] = undefined;
      }
    }

    await this._powerupService.svcToModify(powerup);
    await this._powerupService.toSave();
    Alert.ShowSuccess('Weapon Upgrades imported successfully!');
  }

  async PasteStats() {
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter((line) => line);
    if (this.displayErrors(lines, 18)) return;

    for (let l = 0; l < lines.length; l++) {
      let line = lines[l];
      let rows = line.split(/\t/);
      let searchName = this.simplifyString(rows[0]);
      this.powerupsStats.forEach((powerupStat) => {
        let item = this._itemService.svcFindById(powerupStat.itemId);
        if (this.simplifyString(item.name) === searchName)
          this.PasteStatsLine(powerupStat, rows);
      });
    }

    this.spinnerService.setState(false)
  }

  PasteStatsLine(powerupStat: PowerUpStat, rows: string[]) {
    let powerupStatFields: string[] = ['duration', 'partnerRegen', 'partnerHP', 'partnerATK', 'partnerDEF', 'enemyHP', 'enemyATK', 'enemyDEF',
      'partnerQI', 'partnerLuck', 'enemyQI', 'enemyLuck', 'enemyIntimidate', 'enemySoothe', 'enemyInvisibility', 'enemyInvulnerability', 'enemySlowDown'];

    for (let i = 0; i < powerupStatFields.length; i++) {
      if (rows[i + 1]?.trim()) {
        powerupStat[powerupStatFields[i]] = +rows[i + 1]
          .split(' ')
          .join('')
          .split('.')
          .join('')
          .replace(',', '.')
          .replace('%', '');
      }
      else
        powerupStat[powerupStatFields[i]] = undefined;

    }

    this._powerupStatService.svcToModify(powerupStat);
    this._powerupStatService.toSave();
    Alert.ShowSuccess(`${this.title} imported successfully!`);
  }

  simplifyString(str: string): string {
    return str.normalize('NFD')?.replace(/[\u0300-\u036f]/g, '')?.toLocaleUpperCase()?.trim();
  }

  displayErrors(array, length) {
    let count = array[0].split(/\t/);
    if (count.length < length) {
      Alert.showError('Copy the POWER UP column values too! You must copy ALL ' + length + ' Columns!');
      this.spinnerService.setState(false)
      return true;
    }

    if (count[0] === '') {
      Alert.showError('You are probably copying a blank column!');
      this.spinnerService.setState(false)
      return true;
    }

    return false;
  }

  sortPowerupListByNumberOrder: number = 1;
  sortPowerupListByNumber(fieldName: string) {
    this.sortPowerupListByNumberOrder *= -1;
    this.powerups.sort((a, b) => {
      if (!a[fieldName] && b[fieldName]) return 1;
      if (a[fieldName] && !b[fieldName]) return -1;
      if (!a[fieldName] && !b[fieldName]) return 0;
      return this.sortPowerupListByNumberOrder * (+a[fieldName] - +b[fieldName]);
    });
  }

  sortpowerupStatListByNumberOrder: number = 1;
  sortPowerupStatListByNumber(fieldName: string) {
    this.sortpowerupStatListByNumberOrder *= -1;
    this.powerupsStats.sort((a, b) => {
      if (!a[fieldName] && b[fieldName]) return 1;
      if (a[fieldName] && !b[fieldName]) return -1;
      if (!a[fieldName] && !b[fieldName]) return 0;
      return this.sortpowerupStatListByNumberOrder * (+a[fieldName] - +b[fieldName]);
    });
  }

  sortNameOrder = -1;
  sortListByName() {
    this.sortNameOrder *= -1;
    this.powerups.sort((a, b) => {
      let nameA = this._itemService.svcFindById(a.itemId).name;
      let nameB = this._itemService.svcFindById(b.itemId).name;
      return this.sortNameOrder * nameA.localeCompare(nameB);
    });
  }

  sortParticleIndexOrder = -1;
  sortListByParticleIndex(index: number) {
    this.sortParticleIndexOrder *= -1;
    this.powerups.sort((a, b) => {
      return (this.sortParticleIndexOrder * (a.particlesValue[index] - b.particlesValue[index]));
    });
  }

  sortStatsNameOrder = -1;
  sortStatsByName() {
    this.sortNameOrder *= -1;
    this.powerupsStats.sort((a, b) => {
      let nameA = this._itemService.svcFindById(a.itemId).name;
      let nameB = this._itemService.svcFindById(b.itemId).name;
      return this.sortNameOrder * nameA.localeCompare(nameB);
    });
  }
}
