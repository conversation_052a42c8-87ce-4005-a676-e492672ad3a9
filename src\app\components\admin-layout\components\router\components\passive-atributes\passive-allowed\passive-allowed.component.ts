import { ChangeDetectorRef, Component, EventEmitter, Output } from '@angular/core';
import { Alert } from '../../../../../../../../lib/darkcloud';
import { PassiveAllowed } from '../../../../../../../lib/@bus-tier/models/PassiveAllowed';
import { PassiveAllowedService } from '../../../../../../../services/passiveAllowed.service';

@Component({
  selector: 'app-passive-allowed',
  templateUrl: './passive-allowed.component.html',
  styleUrls: ['./passive-allowed.component.scss']
})
export class PassiveAllowedComponent {

  @Output() pasteExcelData = new EventEmitter<void>();
  @Output() descrptionOutput = new EventEmitter<string>();
  excelPassiveAllowed: any[] = [];
  passiveAllowed: PassiveAllowed[] = [];
  listPassiveAllowed: PassiveAllowed[] = [];

  constructor(  
    private ref: ChangeDetectorRef,
    private _passiveAllowedService: PassiveAllowedService,
  ) {}

  public async ngOnInit()  {
    this._passiveAllowedService.toFinishLoading();
    this.listPassiveAllowed = this._passiveAllowedService.models;
   // this.removeEmptyLines();   

    this.passiveAllowed = this.listPassiveAllowed;
    this.descrptionOutput.emit(`Showing ${this.passiveAllowed.length} results`); 
  }

  removeEmptyLines() {
    this.passiveAllowed = this.listPassiveAllowed.filter((pass) => pass?.idValue1 && pass?.idValue2);
   this.passiveAllowed.forEach((x) => this._passiveAllowedService.svcToModify(x))
    this._passiveAllowedService.toSave(); 
    this.ref.detectChanges();
    
  }
  
  async onExcelPaste(): Promise<void> {
    const durationFields: string[] = ['ID', 'ID2',];
    
    try {
       const text = await navigator.clipboard.readText();  
      const lines = text.split(/\r?\n/).filter(line => line); 

      // Remove linhas vazias no final
      while (lines.length > 0 && !lines[lines.length - 1].trim()) {
        lines.pop();
      }
      this._passiveAllowedService.toFinishLoading();      
      this.passiveAllowed = this._passiveAllowedService.models = [];
      this._passiveAllowedService.toSave();

      if (lines.length === 0) {
        throw new Error('No data found. Check if you copied the lines correctly.');
      }

      const headerColumns = lines[0].split(/\t/).map(col => col.trim());
      
      if (headerColumns.length !== durationFields.length) {
        throw new Error('Number of incorrect columns. Check Excel format.');
      }      

      this.excelPassiveAllowed = lines.slice(0).map((line, lineindex) => {
        const cols = line.split(/\t/).map(col => col.trim());  
              // Verifica se a primeira coluna (ID) está vazia
       if (!cols[0]) {
        throw new Error(`The ${lineindex + 1} line has the first empty column. Check the data.`);
       }        
             
        return {
          idValue1: cols[0], 
          idValue2: cols[1],    
        };
      });
  
      this.excelPassiveAllowed.forEach((x) => this._passiveAllowedService.createNewPassiveAllowed(x));
       await this._passiveAllowedService.toSave();
  
      Alert.ShowSuccess('Excel copied successfully!');  
      this.ref.detectChanges();
      this.ngOnInit();
    } catch (error) {
      Alert.showError(error.message || 'Error in processing Excel data.');
    }
  }


}
