<div class="card list-header"
     style="height: 70px; margin: 15px 30px 0;">
  <div class="header">
    <button class="{{activeTab === 'particles' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
            (click)="switchToTab('particles')">
      Particles
    </button>
    <button class="{{activeTab === 'power-Ups' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
            (click)="switchToTab('power-Ups')" style="margin-left: 5px;">
      Power-Ups
    </button>
    <button class="{{activeTab === 'memory' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
            (click)="switchToTab('memory')" style="margin-left: 5px;">
      Memory Modules
    </button>
    <button class="{{activeTab === 'silicates' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
            (click)="switchToTab('silicates')" style="margin-left: 5px;">
      Silicates
    </button>
  </div>
</div>

<app-particles-generator *ngIf="activeTab === 'particles'"></app-particles-generator>
<app-powerup-generator *ngIf="activeTab === 'power-Ups'"></app-powerup-generator>
<app-memorymodule-generator *ngIf="activeTab === 'memory'"></app-memorymodule-generator>
<app-silicate-generator *ngIf="activeTab === 'silicates'"></app-silicate-generator>