@keyframes spin {
    0%{ transform: rotate(0deg); }
    100%{ transform: rotate(360deg);}
}
.loader
{
    border: 16px solid #f3f3f3;
    border-top: 16px solid #3498db;
    border-radius: 50%;
    width: 120px;
    height: 120px;
    animation: spin 2s linear infinite;

    width: 100px;
    height: 100px;

    position: inherit;
    top: 0;
    bottom: 0%;
    left: 0;
    right: 0;
    

    margin: auto;

    z-index: 99 !important;
    backdrop-filter: blur(10px);
}
.backdrop
{
    width: 100%;
    height: 100%;

    position:absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    
    z-index: 98 !important;
    background-color: rgba($color: #000000, $alpha: 0.5);
    backdrop-filter: blur(10px);
}
