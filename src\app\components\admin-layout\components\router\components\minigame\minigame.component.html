<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="card list-header-row">
      <app-header-with-buttons
        [cardTitle]="listName"
        [cardDescription]="cardDescription"
        [rightButtonTemplates]="[addButtonTemplate]"
      >
      </app-header-with-buttons>
    </div>
  </div>
  <div class="card m-container">
    <table class="table table-list">
      <thead style="top: 220px">
        <tr>
          <th class="th-clickable" (click)="sortListByParameter('id')">ID</th>
          <th class="th-clickable" (click)="sortListByParameter('name')">
            <!--TEST-->
            Name & Description
          </th>
          <th class="th-clickable" (click)="sortListByParameter('notes')">
            Notes
          </th>
          <th class="th-clickable" (click)="sortListByParameter('id')">
            <!--TEST-->
            Delete Entry
          </th>
        </tr>
      </thead>

      <tbody>
        <ng-container
          *ngFor="let minigame of minigames; let i = index; trackBy: trackById"
        >
          <tr>
            <td class="td-id">
              {{ minigame.id }}
            </td>
            <td>
              <input
                type="text"
                class="form-control form-short"
                value="{{ minigame.name }}"
                #name
                (change)="lstOnChange(minigame, 'name', name.value)"
                placeholder="Name..."
              />
              <textarea
                name="description"
                id="description"
                style="width: 100%"
                value="{{ minigame.description }}"
                #description
                (change)="
                  lstOnChange(minigame, 'description', description.value)
                "
                placeholder="Description..."
              ></textarea>
            </td>
            <td class="td-notes">
              <textarea
                class="form-control borderless"
                value="{{
                  minigame | translation : lstLanguage : minigame.id : 'notes'
                }}"
                #notes
                (change)="lstOnChange(minigame, 'notes', notes.value)"
              ></textarea>
            </td>
            <td class="td-actions">
              <button
                class="btn btn-danger btn-fill btn-remove"
                (click)="removeMinigame(minigame)"
              >
                <i class="pe-7s-close"></i>
              </button>
            </td>
          </tr>
        </ng-container>
      </tbody>
    </table>
  </div>
</div>
