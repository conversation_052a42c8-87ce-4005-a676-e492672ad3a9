import { NO_ERRORS_SCHEMA } from "@angular/core";
import { ComponentFixture, TestBed } from "@angular/core/testing";
import { IndexStorageService } from "src/app/services";
import { ChoiceCadenceReportComponent } from "./choice-cadence-report.component";
import { Router } from "@angular/router";

class MockService{
    getData<T>(...args: any){return [];}
}

class RouterStub{}

describe('ChoiceCadenceReportComponent', () => {
    let component: ChoiceCadenceReportComponent;
    let fixture: ComponentFixture<ChoiceCadenceReportComponent>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            declarations: [],
            providers: [
                {provide: Router, useClass: RouterStub},
                {provide: IndexStorageService, useClass: MockService}
            ],
            schemas: [NO_ERRORS_SCHEMA]
        });

        fixture = TestBed.createComponent(ChoiceCadenceReportComponent);
        component = fixture.componentInstance;
    });

    it('should be created', () => {
        expect(component).toBeTruthy();
    });
});