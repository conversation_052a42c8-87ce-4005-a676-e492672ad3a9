import { Injectable } from '@angular/core';
import { IndexStorageService, ReviewService } from 'src/app/services';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { ModelService } from 'src/lib/darkcloud/angular/dsadmin';
import { BoostIdBlocks } from '../lib/@bus-tier/models';
import { ChaosIdBlocks } from '../lib/@bus-tier/models/ChaosIdBlocks';

@Injectable({
  providedIn: 'root',
})
export class ChaosIdBlockservice extends  ModelService<ChaosIdBlocks> 
{

  public override svcPromptCreateNew(...args: any): Promise<ChaosIdBlocks> {
    throw new Error('Method not implemented.');
  }

  constructor(
    indexStorageService: IndexStorageService,
    readonly userSettingsService: UserSettingsService,
    reviewService: ReviewService,
  ) 
  {
    super(
      {
        defaultConstructor: () =>
          new ChaosIdBlocks(0, this.userSettingsService),
      },
      'ChaosIdBlocks',
      indexStorageService,
      reviewService,
    );
  }
  
  public async createNewChaosIdBlocks(value: string[]) { 

    let newChaosIdBlocks = new ChaosIdBlocks(this.svcNextIndex(), this.userSettingsService);
    newChaosIdBlocks.positionNameChaos =  value;
    this.srvAdd(newChaosIdBlocks);
    return newChaosIdBlocks;
  }
}
