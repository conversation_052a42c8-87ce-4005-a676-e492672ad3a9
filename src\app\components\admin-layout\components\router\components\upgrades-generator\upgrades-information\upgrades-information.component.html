<div class="content-weapon">
  <div class="card">
    <app-header-with-buttons 
      [cardTitle]="'Upgrades'"
      [cardDescription]="description"
      [rightButtonTemplates]="[excelButtonTemplate]"
      [isBackButtonEnabled]="false">
    </app-header-with-buttons>
  </div>

  <div class="c-container">
    <table class="table table-list">
      <thead class="sticky" style="margin-bottom: 20px;">
        <tr >
          <th class="th-clickable" (click)="sortingUpgradesNumber('hellCicle')" rowspan="3">Hell Circle (HC)</th>
          <th class="th-clickable" (click)="sortingUpgradesNumber('buildingEfficiency')" rowspan="3">HC Building Inefficiency (%)</th>
          <th class="th-clickable" (click)="sortingUpgradesString()" rowspan="3">UPGRADES</th>
          <th class="th-clickable" (click)="sortingUpgradesNumber('workshopLevelUnlock')" rowspan="3">WORKSHOP Level UNLOCK (GATING.WORKSHOP)</th>
          <th class="th-clickable" (click)="sortingUpgradesNumber('newEfficiency')" rowspan="3">New Inefficiency (%)</th>
          <th class="th-clickable" (click)="sortingUpgradesNumber('efficiencyBoost')" rowspan="3">Efficiency Boost (%)</th>
          <th class="th-clickable" (click)="sortingUpgradesNumber('souls')">Cost</th>
          <th class="th-clickable" (click)="sortingUpgradesNumber('minutes')">Time to Create</th>
          <th class="th-clickable" (click)="sortingUpgradesNumber('rubies')">Skip Price</th>
          <th class="th-clickable" rowspan="2" [attr.colspan]="subatomicParticles?.length">Subatomic Particles (Ingredients)</th>
        </tr>
        <tr>
          <th class="green-color th-clickable" (click)="sortingUpgradesNumber('souls')">souls</th>
          <th class="blue-color th-clickable" (click)="sortingUpgradesNumber('minutes')">minutes</th>
          <th class="red-color th-clickable" (click)="sortingUpgradesNumber('rubies')">rubies</th>
        </tr>
        <tr>
          <th class="light-gray-color th-clickable" (click)="sortingUpgradesNumber('souls')">Required to Create</th>
          <th class="light-gray-color th-clickable" (click)="sortingUpgradesNumber('minutes')">Waiting time</th>
          <th class="light-gray-color th-clickable" (click)="sortingUpgradesNumber('rubies')">Gems to Skip</th>
          <th  
            (click)="sortComonIngredients(i)"
            class="th-clickable"  *ngFor="let particle of subatomicParticles; let i = index">
            <select id="select" #selectedId (change)="changeElementsPosition(selectedId.value, particle.id)" style="color:#000">
              <option *ngFor="let p of subatomicParticles"
                [selected]="p.name == particle.name" 
                value="{{p.name}}">{{ p.name }}
              </option>
            </select>
          </th> 
        </tr>
      </thead>
      <tbody>
        <ng-container *ngFor="let upgrade of this.upgrades">
          <tr id="{{ upgrade.id }}">
            <td class="td-id">
              <input
                readonly
                (click)="changeField(upgrade, hellCicle.value, 'hellCicle')"
                class="background-input-table-color"
                placeholder=" "
                type="number"
                #hellCicle
                [value]="upgrade.hellCicle"/>
            </td>
            <td>
             <input
                (click)="changeField(upgrade, buildingEfficiency.value, 'buildingEfficiency')"
                class="background-input-table-color"
                placeholder=" "
                type="number"
                #buildingEfficiency
                [value]="upgrade.buildingEfficiency"/>
            </td>
            <td class="td-id"> 
              <input
                readonly
                class="background-input-table-color"
                type="text"
                [value]=" GetSpecialWeaponName(upgrade.id)"/>              
            </td>
            <td>
              <input
                (click)="changeField(upgrade, workshopLevelUnlock.value, 'workshopLevelUnlock')"
                class="background-input-table-color"
                placeholder=" "
                type="number"
                #workshopLevelUnlock
                [value]="upgrade.workshopLevelUnlock"/>
            </td>
            <td>
              <input
                (click)="changeField(upgrade, newEfficiency.value, 'newEfficiency')"
                class="background-input-table-color"
                placeholder=" "
                type="number"
                #newEfficiency
                [value]="upgrade.newEfficiency"/>
            </td>
            <td>
              <input
                (click)="changeField(upgrade, efficiencyBoost.value, 'efficiencyBoost')"
                class="background-input-table-color"
                placeholder=" "
                type="number"
                #efficiencyBoost
                [value]="upgrade.efficiencyBoost"/>
            </td>
            <td>
              <input
                (click)="changeField(upgrade, souls.value, 'souls')"
                class="background-input-table-color"
                placeholder=" "
                type="number"
                #souls
                [value]="upgrade.souls"/>
            </td>
            <td>
              <input
                (click)="changeField(upgrade, minutes.value, 'minutes')"
                class="background-input-table-color"
                placeholder=" "
                type="number"
                #minutes
                [value]="upgrade.minutes"/>
            </td>
            <td>
              <input
                (click)="changeField(upgrade, rubies.value, 'rubies')"
                class="background-input-table-color"
                placeholder=" "
                type="number"
                #rubies
                [value]="upgrade.rubies"/>
            </td>
            <td *ngFor="let ingredient of subatomicParticles, let i = index">
              <input
                class="background-input-table-color"
                placeholder=" "
                type="number"
                #InputIngredient
                [value]="GetAmountValue(upgrade, i)"
                (change)="changeIngredientAmount(upgrade, +InputIngredient.value, i)"
                />
            </td>
          </tr>
        </ng-container>
      </tbody>
    </table>
  </div>
</div>

