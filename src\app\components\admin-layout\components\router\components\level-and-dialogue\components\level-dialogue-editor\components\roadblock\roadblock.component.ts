import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Character, Class, Item } from 'src/app/lib/@bus-tier/models';
import { RoadBlock } from 'src/app/lib/@bus-tier/models/RoadBlock';
import { CharacterService, ClassService, ItemService, OptionBoxService, OptionService } from 'src/app/services';
import { RoadBlockService } from 'src/app/services/road-block.service';
import { Alert } from 'src/lib/darkcloud';
import { RoadBlockType } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard';
import { LevelHelperService } from './../../../../../../../../../../services/level-helper.service';
import { SettingsComponent } from '../../../../../settings/settings.component';

interface SpokePlace
{
  elementId: string,
  text: string,
  type?:number
}

@Component({
  selector: 'app-roadblock',
  templateUrl: './roadblock.component.html',
  styleUrls: ['./roadblock.component.scss']
})
export class RoadblockComponent implements OnInit 
{

  @Output() onDelete:EventEmitter<RoadBlock> = new EventEmitter<RoadBlock>();
  @Input() toRemove: (roadBlock: RoadBlock) => void; 

  public itemList: Item[] = [];
  public displayItemList: Item[] = [];

  public characterList: Character[] = [];
  public displayCharacterList: Character[] = [];

  public bossList: Character[] = [];
  public displayBossList: Character[] = [];

  public classList: Class[] = [];
  public displayClassList: Class[] = [];

 public spokePlaces: SpokePlace[] = [];
 public displaySpokePlaces: SpokePlace[] = [];
 isDisabled = true; // O input começa desabilitado

  types: RoadBlockType[] = 
  [
    RoadBlockType.DEFEATED_BOSS,
    RoadBlockType.OBTAINED_ITEM,
    RoadBlockType.TALKED_TO_CHARACTER,
    RoadBlockType.COLLECTED_CLASS,
    RoadBlockType.SPOKE_IN,
    RoadBlockType.KARMIC_EQUILIBRIUM,
  ];

  typeNames: string[] = 
  [
    "Defeated Boss",
    "Obtained Item",
    "Talked to Character",
    "Collected Class",
    "Spoke In",
    "Karmic Equilibrium",
  ];

  private _storyBoxId: string;
  @Input() set storyBoxId(value: string)
  {
    this._storyBoxId = value;
  };
  roadblock: RoadBlock
  @Input() set currentRoadblock(roadblock: RoadBlock)
  {
    this.roadblock = roadblock

  };
  @Input() optionBox;
  private _markerId: string;
  @Input() public set markerId(value: string)
  {
    this._markerId = value;
  };

  public get markerId() : string 
  {
    return this._markerId;
  }


  constructor(
    private _roadBlockService: RoadBlockService,
    private _characterService: CharacterService,
    private _itemService: ItemService,
    private _classService: ClassService,    
    private _optionService: OptionService,
    private _settingsComponent :SettingsComponent,
    public _optionBoxService: OptionBoxService,
    protected _levelHelperService: LevelHelperService,

  ) { }

  async ngOnInit() 
  {
    this._levelHelperService.toFinishLoading();
    this._settingsComponent.loadPlaces();
    this.itemList = this._itemService.models;
    this.displayItemList = this.itemList;
    this.characterList = this._characterService.models;
    this.displayCharacterList = this.characterList;
    this.bossList = this.characterList.filter(x => 
    {
      //BOSS AND SUBBOSS
      return(x.type == 3 || x.type == 4);
    });
    this.displayBossList = this.bossList;
    this.classList = this._classService.models;
    this.displayClassList = this.classList;
    this.spokePlaces = this._levelHelperService.models;
    this.displaySpokePlaces = this.spokePlaces;
  }

  simplifyString(str: string): string 
  {
    return (str
      ?.normalize('NFD')
      ?.replace(/[\u0300-\u036f]/g, '')
      ?.toLocaleUpperCase())
      ?.trim()
  }

  async getRoadBlock(storyBoxId: string)
  {
    await this._roadBlockService.toFinishLoading();

    let entry = this._roadBlockService.filterByStoryBoxId(storyBoxId);

    if(entry.Type == 1 && !entry.ItemID) 
    {
      entry.ItemID = this.itemList[0].id;
    }
    if(entry.Type == 1 && !entry.operator) 
    {
      entry.operator = 'greater';
    }

    this.roadblock = entry;
  }

  async getRoadBlockByMarker(markerId: string)
  {
    await this._roadBlockService.toFinishLoading();

    let entry = this._roadBlockService.filterByStoryBoxIdByMarker(markerId);
    if(!entry)
    {
      entry = await this._roadBlockService.svcPromptCreateNewMarker(markerId, RoadBlockType.OBTAINED_ITEM);
      await this._roadBlockService.srvAdd(entry);
    }

    if(entry.Type == 1 && !entry.ItemID) 
    {
      entry.ItemID = this.itemList[0].id;
    }
    if(entry.Type == 1 && !entry.operator) 
    {
      entry.operator = 'greater';
    }

    this.roadblock = entry;
  }

  changeType(type: any)
  {
    this.roadblock.Type = type.target.value;

    let bossIds = this.bossList.map(x => 
    {
      return x.id;
    });
    let isBossCharacter = bossIds.includes(this.roadblock.CharacterID);

    if(type == 0 && !isBossCharacter)
    {
      this.roadblock.CharacterID = this.bossList[0].id;
    }
    if(type == 1 && !this.roadblock.ItemID)
    {
      this.roadblock.ItemID = this.itemList[0].id;
      this.roadblock.operator = 'greater';
    }
    if(type > 1 && !this.roadblock.CharacterID)
    {
      this.roadblock.CharacterID = this.characterList[0].id;
    }

    this._roadBlockService.svcToModify(this.roadblock);
    this.ngOnInit();
  }

  async changeItem(itemId: any)
  {
    this.roadblock.ItemID = itemId.target.value;
    await this._roadBlockService.svcToModify(this.roadblock);
  }

  changeItemAmount(value: any)
  {
    this.roadblock.ItemAmount = value.target.value;
    this._roadBlockService.svcToModify(this.roadblock);
  }

  changeCharacterId(value: any)
  {
    this.roadblock.CharacterID = value.target.value;
    this._roadBlockService.svcToModify(this.roadblock);
  }

  changeOperator(value: any)
  {
    this.roadblock.operator = value.target.value;
    this._roadBlockService.svcToModify(this.roadblock);
  }

  changeKlassId(value: any)
  {
    this.roadblock.klassId = value.target.value;
    this._roadBlockService.svcToModify(this.roadblock)
  }
  
  //Verify if the label that you a picking is inside the same choice box and its children.
  async changeSpokeId(value: any)
  {
    let someError : boolean = false;
    let selectedElement = this._optionBoxService.svcFindById(this.optionBox?.id.split('.op')[0]);
    let selectedElement2 = null;
    
    //Get the selected option
    await this.displaySpokePlaces.forEach(dis => 
    {
      if(dis.elementId == value.target.value)
      {
        selectedElement2 = dis;
      }
    })
 

    //if this selectedElement is of type choice box.
    if(selectedElement?.type == 0)
    {
      //If the spoke place choosed is from selected choice box or its children then through error.
      for(let i = 0; i < selectedElement.optionIds.length; i++)
      {
        let element = this._optionService.svcFindById(selectedElement.optionIds[i]);

        if(element?.label?.trim() == selectedElement2?.text?.split(']')[1]?.trim())
        {
          Alert.showError('This spoke place can not be choosed. It is in the same choice box!');
          someError = true;
          break;
        }
      }
    }

    if(someError) return;
    this.roadblock.spokeElementId = value.target.value;
    await this._roadBlockService.svcToModify(this.roadblock);
    this._roadBlockService.toSave();
  }

  async delete()
  {
    await this._roadBlockService.svcToRemove(this.roadblock.id);
    await this._roadBlockService.toSave();
    this.onDelete.emit(this.roadblock);
    this.roadblock = undefined;
  }

  changeRoadblock()
  {
    this.roadblock.validateIfTrue = !this.roadblock.validateIfTrue;
    this._roadBlockService.svcToModify(this.roadblock)
  }

  filterObtainedTextFromList: string = '';

  filterObtainedListItem(event)
  {
    this.displayItemList = this._roadBlockService.filterListItem(event, this.itemList, 'name');
  } 

  filterClassListItem(event)
  {
    this.displayClassList = this._roadBlockService.filterListItem(event, this.classList, 'name');
  } 



  filterSpokePlacesListItem(event: string) { 
    this.displaySpokePlaces = this._roadBlockService.filterListItem(event, this.spokePlaces, 'text'); 
  }
  
  // Habilita o input ao clicar ou passar o mouse
  enableSearch() {
    this.isDisabled = false;
  }
  
  // Desabilita o input se estiver vazio e o mouse não estiver sobre ele
  disableSearchIfEmpty() {
    if (!this.filterObtainedTextFromList) {
      this.isDisabled = true;
    }
  }
  
  filterCharacterListItem(event)
  {
    this.displayCharacterList = this._roadBlockService.filterListItem(event, this.characterList, 'name');
  } 

  filterBossListItem(event)
  {
    this.displayBossList = this._roadBlockService.filterListItem(event, this.bossList, 'name');
  }
}
