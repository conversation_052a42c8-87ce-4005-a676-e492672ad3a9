<div class="main-content">
    <div class="container-fluid">
      <!--Header-->
      <div class="list-header-row update">
        <div class="card">
          <app-header-with-buttons 
            [cardTitle]="'Elemental Defenses List'"
            [cardDescription]="description" [elements]="elements" [hasDropdownSelect]="true"
            (selectedElement)="changeElement($event)" [rightButtonTemplates]="[statusTemplate]">
          </app-header-with-buttons>
          <app-header-search (inputKeyup)="search($event)"(searchOptions)="lstOnChangeFilterOptions($event)"></app-header-search>
        </div>
      </div>
      <!--List-->
      <div class="card">
        <table class="table table-list">
          <thead class="sticky">
            <tr>
              <th class="th-clickable" >Index</th>
              <th class="th-clickable"
                (click)="sortByDefenses()">{{selectedElement}}
                <div class="ball-circle"></div>
              </th>
              <th class="th-clickable"
                (click)="sortByAcronym()">Acronym</th>
              <th >Description
                <div class="ball-circle"></div>                
              </th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor=" let elemental of this.elementalDefenses; let i = index; trackBy: trackById">
              <tr id="{{ elemental.id }}">
                <td class="td-sort">{{ i + 1 }}</td>
                <td class="td-notes">
                  <input class="form-control form-short "
                    type="text"
                    value="{{ (elemental | translation : lstLanguage : elemental.id : 'defenses') }}"
                    #defenses
                    [ngStyle]="{'background-color': +defenses.value <= 0 ? '#404040' : '#ffffff'}"
                    (change)="defenseChange(elemental, 'defenses', defenses.value)"/>
                </td>
                <td class="td-notes" style="width: 20%;">
                  <input class="form-control form-short "
                    type="text"
                    value="{{ elemental.acronym }}"
                    #acronym
                    [ngStyle]="{'background-color': +acronym.value <= 0 ? '#404040' : '#ffffff'}"
                    (change)="lstOnChange(elemental, 'acronym', acronym.value)"/>                
                </td>
                <td class="td-notes" style="text-align:right; width:100%">
                  <textarea class="form-control " style="height:50px"
                    type="text"
                    value="{{ (elemental | translation : lstLanguage : elemental.id : 'description') }}"
                    #description
                    [ngStyle]="{'background-color': +description.value <= 0 ? '#404040' : '#ffffff'}"
                    (change)="descriptionChange(elemental, 'description', description.value)">
                  </textarea>
                </td>
                <td  class="td-actions">
                  <button class="btn btn-danger btn-fill btn-remove"
                    (click)="removeElement(elemental)">
                    <i class="pe-7s-close"></i>
                  </button><br>                
                  <button 
                    class="btn btn-gray btn-fill translation-button"
                    (click)="getElementalDefensesOrtography(elemental)">
                    <div class="mat-translate"></div>
                  </button>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
    </div>
  </div>
  