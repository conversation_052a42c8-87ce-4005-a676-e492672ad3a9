<div class="main-content fixed-height">
  <div class="container-fluid">
    <div class="content">
      <div class="row">
        <div class="col-md-12 card rpg-content">
          <div class="card list-header"
               style="height: 83px">
            <div class="header">
              <div class="col-md-5">
                <ng-container *ngIf="nAnalyzedDetected > 0">
                  <div class="btn-group">
                    <button 
                        class="btn btn-lg btn-success {{activeTab === 'detections' ? 'btn-fill' : ''}}"
                        (click)="switchToTab('detections')">
                        <ng-container 
                            *ngIf="nAnalyzedDetected === totalN; else loadingView">DETECTIONS
                        </ng-container>
                        <ng-template 
                            #loadingView>
                            Analyzing {{ nAnalyzedDetected }} of {{ totalN }}...
                        </ng-template>
                    </button>
                    <button 
                        class="btn btn-lg btn-primary {{activeTab === 'suggestions' ? 'btn-fill' : ''}}"
                        (click)="switchToTab('suggestions')">
                        <ng-container 
                            *ngIf="nAnalyzedSuggestions < totalN; else loadingDetect">
                            Analyzing {{ nAnalyzedSuggestions }} of {{ totalN }}...
                        </ng-container>
                        <ng-template 
                            #loadingDetect> SUGGESTIONS 
                        </ng-template>
                    </button>
                  </div>
                </ng-container>
              </div>
              <div class="col-md-3">
                <ng-container *ngIf="nAnalyzedDetected === 0; else separateButtons">
                  <button class="btn btn-fill btn-lg btn-info center"
                          (click)="generateSuggestions()">
                    DETECT & SUGGEST
                  </button>
                </ng-container>
                <ng-template #separateButtons>
                  <ng-container *ngIf="activeTab === 'suggestions'">
                    <ng-container
                                  *ngIf="(nAnalyzedSuggestions === 0 || nAnalyzedSuggestions === totalN); else cancelButton">
                      <button class="btn btn-fill btn-lg btn-warning center"
                              (click)="generateSuggestions()">
                        ANALYZE AGAIN
                      </button>
                    </ng-container>
                    <ng-template #cancelButton>
                      <button class="btn btn-fill btn-lg btn-danger center"
                              (click)="cancelSuggestions()">
                        CANCEL
                      </button>
                    </ng-template>
                  </ng-container>
                </ng-template>
              </div>
              <div class="col-md-3">
                <button *ngIf="
                    activeTab === 'suggestions' &&
                    nAnalyzedSuggestions === totalN
                  "
                        class="btn btn-fill btn-lg btn-info"
                        (click)="acceptSelectedSuggestions()">
                  ACCEPT SELECTED
                </button>
              </div>
            </div>
          </div>
          <app-rpg-wording-detection-manager *ngIf="activeTab === 'detections'">
          </app-rpg-wording-detection-manager>
          <app-rpg-wording-suggestion-manager *ngIf="activeTab === 'suggestions'">
          </app-rpg-wording-suggestion-manager>
        </div>
      </div>
    </div>
  </div>
</div>
