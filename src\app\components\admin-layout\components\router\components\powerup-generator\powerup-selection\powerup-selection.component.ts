import { Component, EventEmitter, Output } from '@angular/core';
import { Item } from 'src/app/lib/@bus-tier/models';
import { ItemService, LevelService, UserSettingsService, } from 'src/app/services';
import { TranslationCheckService } from 'src/app/services/translation-check.service';
import { TranslationService } from 'src/app/services/translation.service';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';
import { LanguageService } from 'src/app/services/language.service';
import { CustomService } from 'src/app/services/custom.service';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';
import { ItemClassService } from 'src/app/services/item-class.service';
import { PowerUpService } from 'src/app/services/powerup.service';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-powerup-selection',
  templateUrl: './powerup-selection.component.html',
})
export class PowerupSelectionComponent  extends TranslatableListComponent<Item>{

  constructor(
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    private _itemService: ItemService,
    private _itemClassService: ItemClassService,
    private _powerupService: PowerUpService,
    private _levelService: LevelService,
    private _translationCheckService: TranslationCheckService,
    protected override _translationService: TranslationService,
    protected override _languageService: LanguageService,
    protected _customService: CustomService,
    private _router: Router,
  ) {
    super(_itemService, _activatedRoute, _userSettingsService, 'name', _translationService, _languageService);
  }

  @Output() itemSelected: EventEmitter<string> = new EventEmitter();

  listDescription = "";
  itemList: Item[] = [];
  custom: Custom;

 override async lstInit()
  {
    this.custom = await this._customService.svcGetInstance();
    this.itemList = [];
    this.custom.powerupClassItem.forEach(itemClassId => {
      let itemClass = this._itemClassService.svcFindById(itemClassId);
      itemClass.itemIds.forEach(itemId => {
        let item = this._itemService.svcFindById(itemId);
        this.itemList.push(item);
      });
    });
    this.listDescription = "Showing " + this.itemList.length + " results";
  }

  sortIdOrder = -1;
  sortListById()
  {
    this.sortIdOrder *= -1;
    this.itemList.sort((a, b) => {
      return this.sortIdOrder * a.id.localeCompare(b.id);
    });
  }

  sortNameOrder = -1;
  override sortListByName()
  {
    this.sortNameOrder *= -1;
    this.itemList.sort((a, b) => {
      return this.sortNameOrder * a.name.localeCompare(b.name);
    });
  }

  sortWeaponOrder = -1;
  sortListByWeapon()
  {
    this.sortWeaponOrder *= -1;
    this.itemList.sort((a, b) => {
      let aCount = 0;
      let bCount = 0;
      if(this.checkIfParticleIsCompleted(a, 1) != "rgb(128, 128, 128)") aCount++;
      if(this.checkIfParticleIsCompleted(b, 1) != "rgb(128, 128, 128)") bCount++;
      return this.sortWeaponOrder * (aCount - bCount);
    });
  }

  selectWeapon(item: Item)
  {
    this._customService.setCustomField(item.id, 'selectedPowerupId');
    this._router.navigate(['powerupInformation']);
  }

  checkIfParticleIsCompleted(item: Item, phase: number)
  {
    /* if(phase == 1)
    {
      let particle = this._particleService.models.find(w => w.itemId == item.id);
      if(!particle) return "rgb(128, 128, 128)";
      if(particle.description || particle.charactersId?.length > 0 || particle.classesId?.length > 0 || particle.shake ||
        particle.hit || particle.split || particle.atk || particle.hcLevel)
      {
        return "rgb(29, 199, 234)";
      }
    } */

    return "rgb(128, 128, 128)";
  }
}
