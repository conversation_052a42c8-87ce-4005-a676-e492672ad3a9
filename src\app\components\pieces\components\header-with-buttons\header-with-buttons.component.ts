import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Button } from 'src/app/lib/@pres-tier/data';
import { IndexStorageService } from 'src/app/services';

@Component({
  selector: 'app-header-with-buttons',
  templateUrl: './header-with-buttons.component.html',
  styleUrls: ['./header-with-buttons.component.scss'],
})

export class HeaderWithButtonsComponent implements OnInit {
  constructor(private _indexStorageService: IndexStorageService) { }

  @Input() cardTooltip: string = '';
  @Input() selectedOption: string = '';
  @Input() cardTitle: string;
  @Input() valueBossLevel: string;
  @Input() nameRarity: string;
  @Input() nameClass: string;
  @Input() type: string;
  @Input() cardDescription: string;
  @Input() btnSubContext = false;
  @Input() textDescriptionRecord = false;
  @Input() rightButtonTemplates: Button.Templateable[] = [];
  @Input() leftButtonTemplates: Button.Templateable[] = [];
  @Output() cardBackButtonClick = new EventEmitter();
  @Input() isBackButtonEnabled = false;
  @Input() hasDropdownSelect = false;
  activeLanguage = 'PTBR';
  @Input() elements = [];
  @Output() selectedElement = new EventEmitter<any>();
  @Output() clickBtn = new EventEmitter<any>();
  @Input() buttonLabel: string;
  isActive: boolean = false;


  get titleBossLevel(): string {
    return this.nameClass ? `${this.valueBossLevel} - ` : this.valueBossLevel;
  }

  ngOnInit(): void {
    this.activeLanguage = IndexStorageService.activeLanguage;
    this._indexStorageService.onLanguageChange$.subscribe(l => {
      this.activeLanguage = IndexStorageService.activeLanguage;
    });
  }

  changeElement(element): void {
    this.selectedElement.emit(element.target.value);
  }

  btnClickContext() {
    this.isActive = !this.isActive; // Alterna o estado ativo
    this.clickBtn.emit(this.isActive); // Notifica o pai
  }
}
