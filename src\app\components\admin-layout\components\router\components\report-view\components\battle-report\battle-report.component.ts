import { Component, OnInit } from '@angular/core';
import { LevelService } from 'src/app/services/level.service';
import { Condition, Item, Level, Marker } from 'src/app/lib/@bus-tier/models';
import { byAreaAndLevelByExtraction } from 'src/app/lib/@bus-tier/sorting';
import { AreaService } from 'src/app/services/area.service';
import { Sorting } from 'src/lib/darkcloud/angular/easy-mvc/Sorting';
import { MarkerService } from 'src/app/services/marker.service';
import { DialogueService } from 'src/app/services/dialogue.service';
import {
  ConditionType,
  DialogueType,
  EventType,
  LevelType,
  MarkerType,
} from 'src/lib/darkcloud/dialogue-system';
import {
  CharacterService,
  ConditionService,
  EventService,
  ItemService,
  ReviewService,
} from 'src/app/services';
import { IconChangeService } from 'src/app/services/icon-change.service';
import { Router } from '@angular/router';

interface ChangeColor {
  iconColor: boolean;
  levelId: string;
}

@Component({
  selector: 'app-battle-report',
  templateUrl: './battle-report.component.html',
  styleUrls: ['./battle-report.component.scss'],
})
export class BattleReportComponent implements OnInit {
  local: string;
  isDanger: boolean = false;
  isBlack: string;
  typeLocation: number;
  canDanger: boolean = false;
  marker: Marker[] = [];
  blockBattle: Marker[] = [];
  DialogueType = DialogueType;
  minionBattleLevels: Level[] = [];
  bossBattleLevels: Level[] = [];
  canChangeBossButtonColor: ChangeColor[] = [];
  levelsWithBlockMatch3: Level[] = [];
  private _blockMatch3Levels: Level[];
  public get blockMatch3Levels() {
    return this._blockMatch3Levels;
  }
  protected sortingOrder: Sorting.Order = 'ascending';
  protected sortingParameter: Sorting.Parameter;

  constructor(
    private _areaService: AreaService,
    private _levelService: LevelService,
    private _dialogueService: DialogueService,
    private _markerService: MarkerService,
    private _router: Router,
    private _conditionService: ConditionService,
    private _itemService: ItemService,
    private _characterService: CharacterService,
    private _eventService: EventService,
    private _iconChangeService: IconChangeService,
    private _reviewService: ReviewService
  ) {}

  ngOnInit(): void {
    this.fetchMinionsLevels();
    this.fetchBossesBattleLevel();

    this._markerService.filterByType(MarkerType.BLOCK_BATTLE).forEach((m) => {
      this.levelsWithBlockMatch3.push(
        this._levelService.svcFindById(Level.getSubIdFrom(m.id))
      );
    });

    // sorts levels by area hierarchy code
    this.sortsByParameter('hierarchyCode');
    this.changeBlockgrindButtonColor();
  }

  fetchMinionsLevels() {
    this.minionBattleLevels = this._levelService.models.filter(
      (level) => +level.type === LevelType.MINION
    );
  }

  fetchBossesBattleLevel() {
    this.bossBattleLevels = this._levelService.models.filter(
      (level) => +level.type === LevelType.BOSS
    );
  }

  changeBlockgrindButtonColor() {
    this.canChangeBossButtonColor = [];

    for (let i = 0; i < this.bossBattleLevels.length; i++) {
      let levelId: string =
        this.bossBattleLevels[i].id.split('.')[0] +
        '.' +
        this.bossBattleLevels[i].id.split('.')[1];
      this._iconChangeService.changeIconColor(levelId);
      const changeColor: ChangeColor | undefined =
        this._iconChangeService.changeColor.find(
          (color) => color.levelId === levelId
        );

      if (changeColor) this.canChangeBossButtonColor.push(changeColor);
    }
  }

  getIconColor(levelId: string): boolean {
    const changeColor: ChangeColor | undefined =
      this._iconChangeService.changeColor.find(
        (color) => color.levelId === levelId
      );
    return changeColor ? changeColor.iconColor : false;
  }

  fromLevelGetBoundEventIds(level: Level) {
    let conditions: Condition[] = this._conditionService.svcFilterByIds(
      level.conditionIds
    );
    let itemIds: string[] = [];

    conditions.forEach((element) => {
      if (element.itemId) itemIds.push(element.itemId);
    });

    let eventIds = this._eventService.models
      .filter((x) => {
        return itemIds.some((obj) => obj === x.itemId);
      })
      .map((x) => {
        return x.id;
      });

    return eventIds;
  }

  routeToProblemEvent(level: Level) {
    let validEvents = this.fromLevelGetBoundEventIds(level).filter((x) => {
      return +this._eventService.svcFindById(x).type == +EventType.RECEIVE_ITEM;
    });

    let problemEventIds = validEvents.filter((x) => {
      return x.includes('OB');
    });
    if (problemEventIds.length > 0) {
      this._router.navigate(
        [
          'levels/' +
            this._eventService.getLevelId(problemEventIds[0]) +
            '/dialogues/' +
            this._eventService.getDialogueId(problemEventIds[0]),
        ],
        { fragment: problemEventIds[0] }
      );

      return;
    }

    let yellowProblem = validEvents.filter((x) => {
      if (x.includes('D2')) this.local = 'Return B';
      if (x.includes('D3')) this.local = 'Release Dialog';
      if (x.includes('D4')) this.local = 'Return A';

      return x.includes('D3') || x.includes('D2') || x.includes('D4');
    });

    if (yellowProblem.length > 0) {
      this._router.navigate(
        [
          'levels/' +
            this._eventService.getLevelId(yellowProblem[0]) +
            '/dialogues/' +
            this._eventService.getDialogueId(yellowProblem[0]),
        ],
        { fragment: yellowProblem[0] }
      );
    }
  }

  instantDeathStyling(level: Level): string {
  
    let item: Item = this.getItem(level.conditionIds);
    const id = this.truncateId(level.id); 
    let isMarkBattle = false;
    //item not delivered
    this.isDanger = this.checkIfAssigned(item);  

    let resultReceivedAt = this._reviewService.reviewResults[item?.id]?.receivedAt;
    let resultGivenAt = this._reviewService.reviewResults[item?.id]?.givenAt;
    let resulttradedAt = this._reviewService.reviewResults[item?.id]?.tradedAt;
  
    if(resultReceivedAt?.length > 0) {

      resultReceivedAt.forEach((x) => (this.isDanger = x.includes('op')));//Choice e Investigation (color: red)
     
      for (let index = 0; index < resultReceivedAt.length; index++) {
           this.typeLocation = this.getLocation(resultReceivedAt[index]);
           
           //Check if it's Boss Event Block Battle
           this.marker = this._markerService.models.filter((ma) => ma.id.includes(id) && ma.origin !== undefined);       
         if(resultReceivedAt[index].includes('SB') && !this.isDanger) { //storyBox            
          if(this.typeLocation !== undefined) {     
            //(Return A, Return B e Release Dialog)
          if (this.typeLocation === 2 || this.typeLocation === 3 || this.typeLocation === 4) { // See code in export enum DialogueType            
            this.canDanger = true; //Yellow
          }  
          if(this.marker.length > 0) {                
            for (let i = 0; i < this.marker.length; i++) {           
              if(resultReceivedAt[index].includes('SB') && this.typeLocation === 1 && this.marker[i]?.origin === 0) {               
               this.isDanger = true;
               isMarkBattle = true;
              }              
            }
         }      
          if(this.typeLocation === 0 || this.typeLocation === 1 && !isMarkBattle) {//Init e End       
              return 'black';                  
          } 
          }
        } 
      }
     }// end Receive   
     if(resultGivenAt?.length > 0 || resulttradedAt?.length > 0) { 
      return 'black';
     }

    if (this.isDanger) return 'red';
    else if (this.canDanger) return 'yellow';
    return 'black';
  }

  getLocation(id) {
    const storyId = this.truncateId(id);   
    const dialogue = this._dialogueService.svcFindById(storyId); 
    //const local = GameTypes.dialogueTypeDisplay[+dialogue?.type];  
    return dialogue?.type;
  }

  // Função para truncar o ID
  truncateId(id: string): string {
    // Divide a string pelo ponto
    const parts = id.split('.');
    // Remove os últimos dois elementos do array
    const truncatedParts = parts.slice(0, -2);
    // Junta o array de volta em uma string, com pontos como delimitadores
    return truncatedParts.join('.');
  }

  getItem(conditionIds: string[]) {
    let query = this._conditionService.svcFilterByIds(conditionIds);

    if (+query[0]?.type == +ConditionType.LOADSOUT_ITEM) {
      let item: Item = this._itemService.svcFindById(query[0].itemId);
      return item;
    }
    return null;
  }

  checkIfAssigned = (item: Item): boolean =>
    this._reviewService.reviewResults[item?.id]?.assignedAt?.length == 0 ? true : false;

   getConditionDisplay(conditionIds: string[]) {
    let query = this._conditionService.svcFilterByIds(conditionIds);

    if (+query[0]?.type == +ConditionType.LOADSOUT_ITEM) {
      let item = this._itemService.svcFindById(query[0].itemId);
      return 'Requires ' + query[0].amount + ' ' + item.name + '(s)';
    }
    if (+query[0]?.type == +ConditionType.LOADSOUT_BOSS) {
      let character = this._characterService.svcFindById(query[0]?.characterId);
      return 'Requires ' + character?.name;
    }
    return null;
  }

  accessLevel(levelId: string) {
    this._router.navigate(['levels'], { fragment: levelId });
  }

  public sortsByParameter(param: Sorting.Parameter, minionTable?: boolean) {
    this.sortingOrder = Sorting.nextOrder(
      this.sortingOrder,
      this.sortingParameter,
      param
    );
    this.sortingParameter = param;
    if (minionTable) this.sortsIntroductorySpeeches(this.minionBattleLevels);
    else this.sortsIntroductorySpeeches(this.bossBattleLevels);
  }

  private sortsIntroductorySpeeches(data: Level[]) {
    switch (this.sortingParameter) {
      case 'hierarchyCode':
        data.sort((a, b) =>
          this.sortingOrder === 'descending'
            ? byAreaAndLevelByExtraction(a,b,this._areaService,this._levelService)
            : byAreaAndLevelByExtraction(b,a,this._areaService,this._levelService)
        );
        break;
      case 'blockGrind':
        data.sort((a, b) =>
          this.sortingOrder === 'ascending'
            ? a.blockGrind && !b.blockGrind ? 1 : -1
            : !a.blockGrind && b.blockGrind ? 1 : -1);
        break;
      case 'blockMatch3':
        data.sort((a, b) =>
          this.sortingOrder === 'ascending'
            ? !this.levelsWithBlockMatch3.includes(a) &&
              this.levelsWithBlockMatch3.includes(b) ? -1 : 1
            : this.levelsWithBlockMatch3.includes(a) &&
              !this.levelsWithBlockMatch3.includes(b) ? -1 : 1);
        break;
      case 'firstAttack':
        data.sort((a, b) =>
          this.sortingOrder === 'ascending'
            ? a.firstAttack && !b.firstAttack ? 1 : -1
            : !a.firstAttack && b.firstAttack ? 1 : -1);
        break;
      case 'hasCondition':
        data.sort((a, b) =>
          this.sortingOrder === 'ascending'
            ? a.conditionIds.length > b.conditionIds.length ? 1 : -1
            : a.conditionIds.length < b.conditionIds.length ? 1 : -1);
        break;
    }
  }
}
