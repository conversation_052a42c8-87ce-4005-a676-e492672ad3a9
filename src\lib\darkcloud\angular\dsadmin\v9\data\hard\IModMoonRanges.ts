import { IBase } from './IBase';

// Interfaces para os objetos de Knowledge e Attribute
interface KnowledgeValue {
  id: string;
  value: string;
}

interface AttributeValue {
  id: string;
  value: string;
}

export interface IModMoonRanges extends IBase
{
  moonPhase?: string;
  technicalNomenclature?: string;
  knowledge?: KnowledgeValue[];
  modDanoParty?: string;
  modDanoOponente?: string;
  attribute?: AttributeValue[];
}
