import { Component, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HostListener } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { SearchService } from 'src/app/services/search.service';

@Component({
  selector: 'app-searcher',
  templateUrl: './searcher.component.html',
  styleUrls: ['./searcher.component.scss'],
})
export class SearcherComponent implements OnInit, OnDestroy {
  public term: string;
  public subscription: Subscription;

  @HostListener('window:keydown', ['$event'])
  onKeyDown(event: KeyboardEvent): void {
    if (event.ctrlKey && event.key === 'f') {
      // electron.ipcRenderer.send('find-in-page', 'since');
    } else if (event.ctrlKey && event.key === 'F') {
    }
  }

  constructor(private _searchService: SearchService, private _router: Router) {}

  public ngOnInit() {
    this.subscription = this._searchService.subjectTerm.subscribe((value) => {
      this.term = value;
    });
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
    this._searchService.clearPendingResults();
  }

  public search() {
    this._searchService.searchFor(this.term);
    this._router.navigate(['search']);
  }

}