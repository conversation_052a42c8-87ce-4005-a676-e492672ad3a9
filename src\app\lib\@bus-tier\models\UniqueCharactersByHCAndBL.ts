import { Data } from "src/lib/darkcloud/angular/dsadmin";
import { IdPrefixes } from "src/lib/darkcloud/dialogue-system";
import { IListArea } from "../../../../lib/darkcloud/angular/dsadmin/v9/data/hard";
import { Base } from "./Base";

export class UniqueCharactersByHCAndBL extends Base<Data.Hard.IUniqueCharactersByHCAndBL, Data.Result.IUniqueCharactersByHCAndBL> implements Required<Data.Hard.IUniqueCharactersByHCAndBL> {
  static generateId(index: number): string {
    return IdPrefixes.UNIQUECARACTERSBYHCANDBL + index;
  }

  constructor(
    index: number,
    dataAccess: UniqueCharactersByHCAndBL['TDataAccess']) {
    super(
      {
        hard:
        {
          id: UniqueCharactersByHCAndBL.generateId(index),
        },
      },
      dataAccess
    );
  }

  public get idRarity(): string {
    return this.hard.idRarity;
  }
  public set idRarity(value: string) {
    this.hard.idRarity = value;
  }
  public get nameRarity(): string {
    return this.hard.nameRarity;
  }
  public set nameRarity(value: string) {
    this.hard.nameRarity = value;
  }
  public get hcList(): IListArea[] {
    return this.hard.hcList;
  }
  public set hcList(value: IListArea[]) {
    this.hard.hcList = value;
  }

}