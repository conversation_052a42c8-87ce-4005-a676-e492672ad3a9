import { Injectable } from '@angular/core';
import { IndexStorageService, ReviewService } from 'src/app/services';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { ModelService } from 'src/lib/darkcloud/angular/dsadmin';
import { ModMoonRanges } from '../lib/@bus-tier/models';

@Injectable({
  providedIn: 'root',
})
export class ModMoonRangesService extends  ModelService<ModMoonRanges> 
{

  public override svcPromptCreateNew(): Promise<ModMoonRanges> {
    throw new Error('Method not implemented.');
  }

  constructor(
    indexStorageService: IndexStorageService,
    readonly userSettingsService: UserSettingsService,
    reviewService: ReviewService,
  ) 
  {
    super(
      {
        defaultConstructor: () =>
          new ModMoonRanges(0, this.userSettingsService),
      },
      'ModMoonRanges',
      indexStorageService,
      reviewService,
    );
  }
  
  public async createNewModMoonRanges() {
    let modMoon = new ModMoonRanges(this.svcNextIndex(), this.userSettingsService);
    this.srvAdd(modMoon);
    return modMoon;
  }
}
