<div class="progress-bar" role="progressbar" aria-valuenow="70" aria-valuemin="0" aria-valuemax="100"></div>
<link rel="preload" href="assets/img/icons/AIIcon.png" as="image" />
<link rel="preload" href="assets/img/logo-openAI-Color.png" as="image" />
<div class="main-content">
  <div class="container-fluid">


    <ng-container *ngIf="!justLoadedDataInfo">      
      <div id="loader" style="text-align: center; top: 30%;">
        <h3 style="width: 280px;">Welcome to DS Admin</h3>
        <img alt="loading123" src="assets/img/Dark-cloud-logo-preview.png" style="width: 290px;">
       </div>
    </ng-container>

    <ng-container *ngIf="justLoadedDataInfo">
      <div class="row">
        <div class="col-md-6">
          <!-- START: Export Section -->
          <div class="card">
            <div class="header">
              <h4 class="title">Export</h4>
            </div>
            <div class="content">
              <div class="text-center">
                <label>File title:</label>
                <input type="text" class="form-control" [(ngModel)]="fileTitle" />
              </div>
              <div class="text-center">
                <button style="margin: 15px" class="btn btn-warning btn-fill" (click)="toPromptExportData('dsa')">
                  Export as DSA9
                </button>    
                <button style="margin: 15px" class="btn btn-primary btn-fill" (click)="toPromptExportData('dso')">
                  Export as DSA9 & DSO9
                </button>
                <button style="margin: 15px" class="btn btn-info btn-fill" routerLink="/cleanOrphans">
                  Clean Orphans & Export as DSA9
                </button>
              </div>
            </div>
          </div>
          <!-- END: Export Section -->
  
          <!-- START: INFO Section -->
          <div class="card">
            <div class="header">
              <h4 class="title">Info</h4>
              <div>
                Local Storage Space:
                {{ (localStorageSizeInKB | thousandNumberFormat) + "KB" }}
              </div>
              <p class="peace">
                Time since last loaded data: {{ minutes | customTime }}
              </p>
              <p class="category">Loaded data on {{ lastLoadedDataInfo?.date }}
              </p>
            </div>
            <div class="content">
              <div class="text-center">
                <ng-container *ngFor="
                    let a of justLoadedDataInfo?.slice(0, justLoadedDataInfo.length / infoLineQuant);
                    let i = index">
                  <div class="row">
                    <div class="col-md-1"></div>
                    <ng-container *ngFor="
                        let info of justLoadedDataInfo?.slice(0, infoColQuant);
                        let j = index">
                      <div class="col-md-2">
                        <div class="card" [title]="justLoadedDataInfo[i * infoColQuant + j].description">
                          <div class="content">
                            <h6>{{ justLoadedDataInfo[i * infoColQuant + j].title}}</h6>
                            {{ justLoadedDataInfo[i * infoColQuant + j].entriesCount }}
                            <div class="error" *ngIf="lastLoadedDataInfo.info[i * infoColQuant + j].entriesCount >
                              justLoadedDataInfo[i * infoColQuant + j].entriesCount">
                              (-{{lastLoadedDataInfo.info[i * infoColQuant + j].entriesCount - justLoadedDataInfo[i *
                              infoColQuant + j].entriesCount}})
                            </div>
                            <div class="success" *ngIf="justLoadedDataInfo[i * infoColQuant + j].entriesCount >
                              lastLoadedDataInfo.info[i * infoColQuant + j].entriesCount">
                              (+{{justLoadedDataInfo[i * infoColQuant + j].entriesCount - lastLoadedDataInfo.info[i *
                              infoColQuant + j].entriesCount }})
                            </div>
                          </div>
                        </div>
                      </div>
                    </ng-container>
                  </div>
                </ng-container>
              </div>
            </div>
          </div>
        </div>
        <!-- END: INFO Section -->
  
  
        <div class="col-md-6">
          <div class="col-md-12">
            <app-icon-list #iconListComponent></app-icon-list>
  
            <!-- START: IMPORT Section -->
            <div class="card">
              <div style="display: flex;">
                
                <div class="content" style="width: 50%;">                   
                    <div class="center">
                      <h4 class="title">Import</h4> 
                      <p class="category">Supports .dsa8 & .dsa9</p>
                      <input class="input-file" id="dsa" type="file" (change)="toPromptImportDSA($event)" />
                      <label class="btn btn-fill btn-success" for="dsa">DSAdmin Project File</label>
                    </div>
                    <div *ngIf="loadedTempDSA" class="center">
                      <div class="category">Supports .dso8</div>
                    </div>                   
                  </div>
                  <!-- START: Clear Data Section -->
                    <div class="content div_color" style="width: 50%;">                
                      <div class="center">
                          <h4 class="title">Clear Data</h4>
                        <p class="category">Resets all current data</p>              
                          <button class="btn btn-danger btn-fill btn-remove btn-height" style="width: 115px;" (click)="toPromptClearData()">
                              <i class="pe-7s-trash icon-size" style="margin-left: -10px;"></i>Clear Data
                        </button>                   
                      </div>
                    </div>          
              </div>
            </div>
            <!-- END: IMPORT Section -->
  
          </div>
          <div class="row">
  
            <!-- START: D&D DC Guide -->
            <div class="col-md-4">
              <div class="card">
                <div class="header">
                  <h4 class="title">Environments for AI</h4>
                  <p class="category">OpenAI Information Configuration Environment.</p>
                </div>
                <div class="content">
                  <div class="row">
                    <div class="col-md-12">
                      <div class="text-center" style="margin-top: 5px; margin-bottom: 10px">
                        <button class="btn btn-fill btn-OpenAi btn-height" style="width: 115px;" routerLink="/openAiEnvironment">
                          <img src="assets/img/logo-openAI-Color.png" class="icon-OpenAi" loading="lazy">
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- END: D&D DC Guide  -->
  
            <!-- START: AI Section -->
            <div class="col-md-4">
              <div class="card" style="height: 172px;">
                <div class="header">
                  <h4 class="title">AI Section</h4>
                  <p class="category">Create AI prompts</p>
                </div>
                <div class="content">
                  <div class="row">
                    <div class="col-md-12">
                      <div class="text-center" style="margin-top: 5px; margin-bottom: 10px">
                        <button class="btn btn-fill btn-primary btn-height" style="padding: 0 !important; width: 115px;" routerLink="/aiPrompt">
                          <img src="assets/img/icons/AIIcon.png" class="ai-icon" loading="lazy">
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- END: AI Section -->
  
            <!-- START: Ai Revisor Section -->
            <div class="col-md-4">
              <div class="card" style="height: 172px;">
                <div class="header">
                  <h4 class="title">AI Revisor</h4>
                  <p class="category">Corrects all contents grammar, except from
                    notes</p>
                </div>
                <div class="content">
                  <div class="row">
                    <div class="col-md-12">
                      <div class="text-center" style="margin-top: 5px; margin-bottom: 10px">
                        <button class="btn btn-Boss btn-fill btn-remove btn-height" style="width: 115px;" routerLink="/ai-revisor">
                          <i class="pe-7s-magic-wand icon-size" style="margin-left: -15px;"></i> Correct text
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- END: Ai Revisor Section --> 
  

                        <!-- START: D&D DC Guide -->
                        <div class="col-md-4">
                          <div class="card">
                            <div class="header">
                              <h4 class="title">D&D DC Guide</h4>
                              <p class="category">Describes the scale of each DC assigned to opponents.</p>
                            </div>
                            <div class="content">
                              <div class="row">
                                <div class="col-md-12">
                                  <div class="text-center" style="margin-top: 5px; margin-bottom: 10px">
                                    <button class="btn btn-fill btn-primary btn-height" style="width: 115px;" routerLink="/dcGuide">
                                      <i class="pe-7s-news-paper icon-size" style="margin-left: -10px;"></i>DC Guide
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <!-- END: D&D DC Guide  -->
  
            <!-- START: Translation Section -->
            <div class="col-md-4" style=" width: 60% !important;">
              <div class="card" style="height: 176px;">
                <div class="header">
                  <h4 class="title">Translation Data</h4>
                  <p class="category">Import Translation Data</p>
                </div>
                <div class="content-card">
                  <div class="row" style="display: flex; justify-content: center;">                
                      <div class="text-center">               
                        <button for="dst" class="btn btn-primary btn-fill btn-height" style="width: 136px; height: 50px;  margin-right: 5px;" (change)="importTranslationData($event)">
                          <i class="pe-7s-pen pe-2x pe-va icon-size" style="font-size: 25px; margin-left: -5px;"></i>
                          Import Data
                        </button>
                        <button class="btn btn-fill btn-danger btn-height" style="width: 136px; height: 50px; margin-right: 5px;"
                          (click)="clearTranslationData()">
                          <i class="pe-7s-trash pe-2x pe-va icon-size" style="font-size: 25px; margin-left: -5px;"></i>
                          Clear Data
                        </button>
                        <button class="btn btn-fill btn-success btn-height" style="width: 136px; height: 50px;"
                          (click)="exportTranslationData()">
                          <i class="pe-7s-download pe-2x pe-va icon-size" style="font-size: 25px; margin-left: -10px;"></i>
                          Export Target
                        </button>
                      </div>                    
                  </div>
                </div>
              </div>
            </div>
            <!-- END: Translation Section -->

              
            <!-- START: Separation Section -->
            <div class="col-md-4">
              <div class="card" style="height: 176px;">
                <div class="header">
                  <h4 class="title">Separation Section</h4>
                </div>
                <div class="content">
                  <div class="row">
                    <div class="col-md-12" style="margin-top: 5px; margin-bottom: 10px">
                      <div class="text-center">
                        <button style="background-color:#0badb3; width: 115px;" class="btn btn-Currency btn-fill btn-remove btn-height"
                          routerLink="/charactersSelector">
                          <i class="pe-7s-way icon-size" style="margin-left: -8px;"></i> Separation
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- END: Separation Section -->
    
          </div>
        </div>
      </div>
    </ng-container>
  
  </div>
  <!-- START: Roadblock Section -->
  <div *ngIf="this.showRoadblocks" class="roadblockContainer">
    <div *ngFor="let roadblock of this.emptyRoadblocks" class="roadblockContent">
      <div (click)="this.navigateToRoadblock(roadblock.id)" class="roadblockCard"
        onmouseover="this.style.backgroundColor = 'gray';" onmouseleave="this.style.backgroundColor = 'white';">
        <div>Roadblock : {{roadblock.id}}</div>
        <div>Type : {{roadblock.type}}</div>
      </div>
    </div>
  </div>
  <!-- END: Roadblock Section -->

  <!-- START: Popup Warning Section -->
  <div *ngIf="this.showWarning" class="popupContainer"
    title="Warning: There are story boxes or investigation boxes or choice boxes that are orphan!">
    <div class="popupHeader">
      <h3>These are Orphan Boxes</h3>
      <button (click)="closeOrphanPopup()" class="btn btn-fill btn-danger">X</button>
    </div>
    <div *ngFor="let orphan of this.orphanStoryboxes" class="popupContent">
      <div (click)="this.navigateToOrphan(orphan.id)" class="popupCard"
        onmouseover="this.style.backgroundColor = 'gray';" onmouseleave="this.style.backgroundColor = 'white';">
        <div>Orphan : {{orphan.id}}</div>
      </div>
    </div>

  </div>
  <!-- END: Popup Warning Section -->