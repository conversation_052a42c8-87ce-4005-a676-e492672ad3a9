.main-menu-efect {
    padding-right: 15px;
    padding-left: 15px; 
    min-height: calc(100% - 210px);
    margin-top: 30px;
  }
  
  .card-header-content {
  display: block;
  margin-left: 20px;
  margin-right: 15px;
  width: 30%;
  }
  
  .card {  
     padding-top: 17px !important;
   }
  
   .card-header-content {
     display: block;
     margin-left: 30px;
     margin-right: 15px;
     width: 30%;
   }
  
   //tabela
   .card-container 
  {
    display: flex;
    flex-direction: column;
    align-items: center;
  
    .card 
    {
      border: 1px solid #ccc;
      padding: 15px;
      margin: 5px;
      width: 50vw;
      opacity: 1;
      transition: opacity 0.5s ease-in-out;
    } 
  }
  
  
  h3{
      font-size: 28px;
      margin: 10px 0 10px;
  }

.table-bordered {
    width: 100%;
   margin-bottom: 50px;
  }
  
  .table-bordered th, .table-bordered td {
    padding: 8px;
    text-align: center;
  }
  
  .table-bordered thead th {
    background-color: #2E2E2E;
    color: white;
    font-weight: normal !important;
  }  
  
  .table-bordered thead th[colspan="4"] {
    background-color: #555555;
    color: white;
  }
  
.gray {
background-color:#ddd;
}

.btn-action {
width: 70px;
}

.btn-remove {  
  width: 64px;
  height: 55px;
  display: flex;
  justify-content: center;
}




 
