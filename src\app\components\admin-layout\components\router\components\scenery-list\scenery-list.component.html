<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons [cardTitle]="listName" [cardDescription]="cardDescription"
          [rightButtonTemplates]="[addButtonTemplate]">
        </app-header-with-buttons>
        <app-header-search (inputKeyup)="lstOnChangeFilter($event)"
          (searchOptions)="lstOnChangeFilterOptions($event)"></app-header-search>
      </div>
    </div>
    <!--List-->
    <div class="card">
      <table class="table table-list">
        <thead class="sticky">
          <tr>
            <th>Index</th>
            <th class="th-clickable" (click)="sortListByParameter('id')">ID</th>
            <th>Label</th>
            <th class="th-clickable" (click)="sortListByParameter('assigned')">Assigned</th>
            <th class="th-clickable" (click)="sortListByParameter('name')">Name</th>
            <th>Notes</th>
            <th class="th-clickable" (click)="sortListByParameter('area')">Area
              <select class="dropdown filter-dropdown limited center" name="areaIdFilter"
                [(ngModel)]="lstFilterValue['areaId']" (change)="lstOnChangeFilter()">
                <option value="ALL">All</option>
                <option *ngFor="let area of preloadedAreas" value="{{ area.id }}">
                  {{ area.hierarchyCode }}: {{ area.name }}
                </option>
              </select>
            </th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="
                let scenery of lstIds | sceneries;
                let i = index;
                trackBy: trackByIndex
              ">
            <tr id="{{ scenery.id }}">
              <td class="td-sort">{{ i+1 }}</td>
              <td class="td-id">{{ scenery.id }}</td>
              <td #colorLabel class="label-color-td" [ngStyle]="{ background: (scenery | information)?.hex }"
                (click)="color.click()">
                <input #color type="color" value="{{ (scenery | information)?.hex || '' }}" style="visibility: hidden"
                  (change)="updateColor(scenery, color.value, colorLabel)" />
              </td>
              <td>
                <ng-container *ngIf="(scenery | review).assignedAt.length > 0; else notAssigned">
                  <i *ngIf="(scenery | review).assignedToBindArea; else notAssignedOnBidArea" style="position: relative"
                    placement='top' delay='250' ttWidth="max-content" ttAlign="left" ttPadding="10px"
                    tooltip="Assigned at {{ (scenery | review).assignedAt | location | enumerateList}}"
                    class="pe-7s-check success"></i>
                  <ng-template #notAssignedOnBidArea>
                    <i style="position: relative" placement='top' delay='250' ttWidth="max-content" ttAlign="left"
                      ttPadding="10px"
                      tooltip="Not assigned to the binded Area: {{ (scenery | review).assignedAt | location | enumerateList}}"
                      placement='top' delay='250' ttAlign="left" style="font-size: 36px;"
                      class="pe-7s-attention attention"></i>
                  </ng-template>
                </ng-container>
                <ng-template #notAssigned>
                  <i style="position: relative" placement='top' delay='250' ttWidth="auto" ttAlign="center"
                    ttPadding="10px" tooltip="Not assigned" placement='top' delay='250'
                    *ngIf="!(scenery | review).assignedToBindArea" style="font-size: 36px; "
                    class="pe-7s-close-circle error"></i>
                </ng-template>
                <div>{{(scenery | review).assignedAt.length}}</div>
              </td>
              <td class="td-70">
                <input class="form-control form-title form-short" type="text"
                  value="{{ (scenery | translation : lstLanguage : scenery.id : 'name') }}" #name
                  (change)="changeName(scenery, 'name', name.value)" />
              </td>
              <td class="td-relative">
                <textarea class="form-control borderless" #authorNotes
                  value="{{ (scenery | information)?.authorNotes || '' }}" (change)="
                      updateInformation(
                        scenery,
                        'authorNotes',
                        authorNotes.value
                      )
                    "></textarea>
              </td>
              <td>
                <button [ngClass]="(scenery.areaId | area) ? 'btn btn-info btn-fill' : 'btn'"
                  (click)="toPromptSelectBindArea(scenery)">
                  {{ (scenery.areaId | area)?.name || 'undefined' }}
                </button>
              </td>
              <td class="td-actions">
                <button class="btn btn-danger btn-fill btn-remove" (click)="lstPromptRemove(scenery)">
                  <i class="pe-7s-close"></i>
                </button>
                <button class="btn btn-gray btn-fill translation-button" (click)="downloadSceneryOrtography(scenery)">
                  <div class="mat-translate"></div>
                </button>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>