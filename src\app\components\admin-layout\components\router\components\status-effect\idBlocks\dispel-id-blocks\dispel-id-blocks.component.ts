import { Component, EventEmitter, Output } from '@angular/core';
import { Alert } from '../../../../../../../../../lib/darkcloud';
import { DispelIdBlocks } from '../../../../../../../../lib/@bus-tier/models';
import { Button } from '../../../../../../../../lib/@pres-tier/data';
import { DispelIdBlockservice } from '../../../../../../../../services';

@Component({
  selector: 'app-dispel-id-blocks',
  templateUrl: './dispel-id-blocks.component.html',
  styleUrls: ['./dispel-id-blocks.component.scss']
})
export class DispelIdBlocksComponent {
  titles = [1, 2, 3, 4, 5, 6];
  listDispel: DispelIdBlocks[] = [];
  activeLanguage = 'PTBR';
  @Output() activeTab2 = new EventEmitter<string>();

  public readonly excelButtonTemplate: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };
  constructor(
    private _dispelIdBlockservice: DispelIdBlockservice
  ){}


  async ngOnInit(): Promise<void>{

      this.removeEmptyItems();
      this.listDispel = this._dispelIdBlockservice.models;
    }

    removeEmptyItems() {
      this._dispelIdBlockservice.toFinishLoading(); 

      this._dispelIdBlockservice.models = this._dispelIdBlockservice.models.filter(boostItem => {
        return this.titles.some((_, index) => boostItem.positionNameDispel[index] !== "");
      });     
      this._dispelIdBlockservice.toSave();    
    }
    
    async onExcelPaste() {
      const text = await navigator.clipboard.readText();
      const lines = text.split(/\r?\n/).filter(line => line);    
      const processedData: string[][] = [];
    
      if (lines.length > 0) {
        lines.forEach(line => {
          // Divide cada linha em colunas e remove a primeira coluna
          const values = line.split("\t").map(value => value.trim()).slice(1);
    
          processedData.push(values);
        });
    
        // Verifica se o número de colunas em cada linha corresponde ao número de colunas em this.titles
        const isColumnCountValid = processedData.every(row => row.length === this.titles.length);
    
        if (!isColumnCountValid) {
          Alert.showError('Invalid number of columns');
          return;
        }
  
        this._dispelIdBlockservice.models = [];
        this._dispelIdBlockservice.toSave();
    
        for (let index = 0; index < processedData.length; index++) {
          this._dispelIdBlockservice.createNewDispelIdBlocks(processedData[index]);
        }    

        Alert.ShowSuccess('Dispel imported successfully!');
        this.activeTab2.emit('dispel');
        this.ngOnInit();
      }
    }
    
 
    changeDispel(rowIndex: number, colIndex: number, newValue: string){ 

      if (this.listDispel[rowIndex]) {
        this.listDispel[rowIndex].positionNameDispel[colIndex] = newValue;
        this._dispelIdBlockservice.svcToModify(this.listDispel[rowIndex]);
      }
    }
}
