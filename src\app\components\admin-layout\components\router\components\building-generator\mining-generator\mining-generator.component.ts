import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ItemClass } from 'src/app/lib/@bus-tier/models/ItemClass';
import { ItemService, PopupService, ReviewService, UserSettingsService } from 'src/app/services';
import { ItemClassService } from 'src/app/services/item-class.service';
import { LanguageService } from 'src/app/services/language.service';
import { TabService } from 'src/app/services/tab.service';
import { TranslationService } from 'src/app/services/translation.service';
import { FILTER_SUFFIX_PATH } from 'src/lib/darkcloud/angular/dsadmin/constants/others';


@Component({
  selector: 'app-mining-generator',
  templateUrl: './mining-generator.component.html',
})
export class MiningGeneratorComponent implements OnInit
{

  constructor(
    private _reviewService: ReviewService,
    private _itemClassService: ItemClassService,
    private _itemService: ItemService,
    private _tabService: TabService,
    private _router: Router,
    private _popupService: PopupService,
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    protected _translationService: TranslationService,
    protected _languageService: LanguageService
  ) {

  }

  public itemClasses: ItemClass[] = [];
  public reviewOrderAscending: boolean = false;
  public activeTab: string;
  public activeTab2: string;

  ngOnInit(): void {
    const tab = localStorage.getItem(
      `tab-MiningGeneratorComponent${FILTER_SUFFIX_PATH}`
    );
    this.activeTab = tab === 'null' || !tab ? 'titanium' : tab;  


    const tab2 = localStorage.getItem(
      `tab-MiningGeneratorComponent2${FILTER_SUFFIX_PATH}`
    );
    this.activeTab2 = tab2 === 'null' || !tab2 ? 'mining' : tab2; 
   
  }

  public switchToTab(tab: string) {
    this.activeTab = tab;
    localStorage.setItem(
      `tab-MiningGeneratorComponent${FILTER_SUFFIX_PATH}`,
      this.activeTab      
      );
  }

  public switchToTab2(tab: string) {
    this.activeTab2 = tab;
    localStorage.setItem(
      `tab-MiningGeneratorComponent2${FILTER_SUFFIX_PATH}`,
      this.activeTab2      
      );
  }

}
