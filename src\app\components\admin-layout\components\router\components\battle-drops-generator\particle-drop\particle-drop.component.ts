import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { ParticleDrop, WeaponUpgrade } from 'src/app/lib/@bus-tier/models';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';
import { ChestService, UserSettingsService } from 'src/app/services';
import { Button } from 'src/app/lib/@pres-tier/data';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc';
import { ReviewService } from 'src/app/services/review.service';
import { TranslationService } from 'src/app/services/translation.service';
import { ParticleDropService } from 'src/app/services/particle-drop.service';
import { SpinnerService } from '../../../../../../../spinner/spinner.service';
import { Alert } from 'src/lib/darkcloud';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-particle-drop',
  templateUrl: './particle-drop.component.html',
  styleUrls: ['./particle-drop.component.scss'],
})

export class ParticleDropComponent extends SortableListComponent<ParticleDrop> implements OnInit
{
  @Input() probability = true;
  particlesList:ParticleDrop[] = [];
  description:string = '';
  sortNameOrder = -1;
  custom: Custom;
  weaponUpgrades: WeaponUpgrade[];
  activeTab:string = 'particleDrop';

  constructor(
    private spinnerService: SpinnerService,
    _activatedRoute: ActivatedRoute,
    protected _particleDropService: ParticleDropService,
    _userSettingsService: UserSettingsService,
    private _router: Router,
    private _reviewService: ReviewService,
    protected _translationService: TranslationService,
    private _chestService: ChestService,
    private ref: ChangeDetectorRef
  ) 
  {
    super(_particleDropService, _activatedRoute, _userSettingsService, 'name');
  }

  public override async ngOnInit(): Promise<void>
  {
    await this._particleDropService.toFinishLoading();
    await this._chestService.toFinishLoading();
    this.generateFirstParticleDrops();

    return null;
  }
  
  async generateFirstParticleDrops()
  {
    let particleLength: number = this._particleDropService.models.length;
    if(particleLength > 0)
    for(let i = 0; i < this._chestService.models.length; i++)
    {
      for(let j = 0; j < particleLength; j++)
      {
        if(this._chestService.models[i].acronym != undefined &&
          this._chestService.models[i].acronym != '' &&
          this._chestService.models[i].acronym == this._particleDropService.models[j].type)
        {
          this.particlesList.push(this._particleDropService.models[j]);
          break;
        }
        if(j == particleLength-1 && this._chestService.models[i].acronym != undefined &&
          this._chestService.models[i].acronym != '') 
        {
          let particleDrop: ParticleDrop = 
          await this._particleDropService.createNewParticleDrop(this._chestService.models[i].acronym);
          this.particlesList.push(particleDrop);
        }
      }
    }
    else
    for(let i = 0; i < this._chestService.models.length; i++)
    {
      if(this._chestService.models[i].acronym != undefined &&
        this._chestService.models[i].acronym != '') 
      {
        let particleDrop: ParticleDrop = 
         await this._particleDropService.createNewParticleDrop(this._chestService.models[i].acronym);
        this.particlesList.push(particleDrop);
      }
    }
    this.lineupOrderParticlesList();
    this.description = `Showing ${this.particlesList.length} results`;
  }


   lineupOrderParticlesList() 
  {
  this.sortNameOrder *= -1;
    this.particlesList.sort((a, b) => 
    {  
      return this.sortNameOrder * a.type.localeCompare(b.type);
    });

  }

  public readonly excelButtonTemplate: Button.Templateable = 
  {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  goBack()
  {
    this._router.navigate(['battleDropsGenerator/']);	
  }

  async onExcelPaste(): Promise<void> {
    this.spinnerService.setState(true);
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter(line => line);

    const errors: string[] = [];
    const processedTypes = new Set<string>();

    // Verifica cada linha copiada
    for (let l = 0; l < lines.length; l++) {
        let line = lines[l];
        let cols = line.split(/\t/).map(col => col.trim());

        // Verifica se a linha contém exatamente 2 colunas
        if (cols.length !== 2) {            
              Alert.showError(`Number of columns copies wrong.`);
              continue;
          }
     
        const particleType = cols[0];
        const amountStr = cols[1];

        // Verifica se a primeira coluna está presente no particlesList
        let particleDrop = this._particleDropService.models.find(x => x.type === particleType);
        if (!particleDrop) {
            errors.push(`Particle type names not found in the system: "${particleType}"`);
            continue;
        }

        // Verifica se há duplicidades
        if (processedTypes.has(particleType)) {
            errors.push(`Duplicate particle type names found:"${particleType}".`);
            continue;
        }
        processedTypes.add(particleType);

        // Processa o valor de amount
        if (amountStr?.trim()) {
            particleDrop.amount = parseFloat(amountStr.replace(',', '.'));
            if (isNaN(particleDrop.amount)) {
                errors.push(`Invalid amount value "${amountStr}".`);
                continue;
            }
        } else {
            particleDrop.amount = undefined;
        }

        await this._particleDropService.svcToModify(particleDrop);
        await this._particleDropService.toSave();
        Alert.ShowSuccess('Particle Drop copied successfully!');
    }
  
    if (this.displayErrors(errors)) {
        return;
    }

    // Atualiza a lista se não houver erros
    this.particlesList = this._particleDropService.models.filter(par => par.type !== undefined);
    this.sortNameOrder *= +1;
    this.particlesList.sort((a, b) => this.sortNameOrder * a.type.localeCompare(b.type));

    this.lstFetchLists();
    this.spinnerService.setState(false);
  }

  displayErrors(errors: string[]): boolean {
      if (errors.length > 0) {
          this.spinnerService.setState(false);
          Alert.showError(errors.join('\n'));
          return true;
      }
      return false;
  }

  async changeParticle(particle:ParticleDrop, value:string)
  {
    let par = this._particleDropService.svcFindById(particle.id);
    par.amount = value == '' ? undefined : +value;
    await this._particleDropService.svcToModify(par);
    await this._particleDropService.toSave();
  }
}
