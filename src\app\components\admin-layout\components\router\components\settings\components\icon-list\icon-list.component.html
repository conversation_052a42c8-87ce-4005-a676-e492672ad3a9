<div class="card">
  <div class="header">
    <h4 class="title">Icons</h4>
    <a class="btn btn-fill btn-info btn-sm" href="https://themes-pixeden.com/font-demos/7-stroke/" target="_blank">All icons</a>
    <button class="btn btn-simple btn-success btn-fill add-button width-btn" (click)="toPromptAddIcon()">
      <i class="pe-7s-plus icon-width icon-size"></i>
    </button>
  </div>
  <div class="content">
    <div class="row">
      <table class="table table-list">
        <tbody>
          <ng-container *ngFor="let icon of icons; let i = index; trackBy: trackByIndex">
            <tr>
              <td class="td-auto">
                <i [ngClass]="icons[i].id + ' icon'" class="icon-size"></i>
              </td>
              <td>
                <input class="form-control form-title form-short" type="text" value="{{icons[i].name}}" #name
                  (change)="onChange(icons[i], 'name', name.value)" />
              </td>
              <td class="td-actions">
                <button class="btn btn-fill btn-neutral" (click)="toPromptChangeIcon(icons[i])">
                  <i class="pe-7s-refresh-2 icon icon-size" ></i>
                </button>
              </td>
              <td class="td-actions">
                <button class="btn btn-danger btn-fill btn-remove width-btn" (click)="toPromptRemoveIcon(icons[i])">
                  <i class="pe-7s-close icon-width icon-size"></i>
                </button>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>
