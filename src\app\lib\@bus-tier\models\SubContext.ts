import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';

export class SubContext extends Base<Data.Hard.ISubContext, Data.Result.ISubContext> implements Required<Data.Hard.ISubContext>
{
  public static generateId(index: number): string {
    return IdPrefixes.SUBCONTEXT + index;
  }

  constructor( index: number, dataAccess: SubContext['TDataAccess']) 
  {
    super(
    {
      hard: 
      {
        id: SubContext.generateId(index),     
      },
    },
    dataAccess
    );
  }
  protected getInternalFetch() 
  {
    return {};
  }
  public get atributte(): string
  {
    return this.hard.atributte;
  }
  public set atributte(value: string) 
  {
    this.hard.atributte = value;
  }
  public get subContext(): string[]
  {
    return this.hard.subContext;
  }
  public set subContext(value: string[]) 
  {
    this.hard.subContext = value;
  }
  public get description(): string[]
  {
    return this.hard.description;
  }
  public set description(value: string[]) 
  {
    this.hard.description = value;
  }

}
