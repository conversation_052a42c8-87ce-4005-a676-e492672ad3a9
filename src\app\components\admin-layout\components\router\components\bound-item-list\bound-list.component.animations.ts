import { animate, state, style, transition, trigger } from "@angular/animations";


export let fadeIn = trigger('fadeIn', [
    state('void', style({
        transform: 'translateX(1920px)'
    })),
    transition('void => *', [animate('500ms 0ms ease-out')]),
    transition('* => void', [animate('500ms 0ms ease-in')])
])

export let fadeOut = trigger('fadeIn', [
    state('void', style({
        transform: 'translateX(1920px)'
    })),
    transition('* => void', [animate('500ms 0ms ease-in')])
])

export let popup = trigger('popup', [
    state('void', style({
        transform: 'scale(0,0)'
    })),
    transition('void => *', [animate('200ms 0ms ease-out')]),
    transition('* => void', [animate('200ms 0ms ease-out')])
]);