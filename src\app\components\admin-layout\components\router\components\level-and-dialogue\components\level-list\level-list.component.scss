.root-button
{
    display: flex;
    justify-content: center;
    margin-left: 500%;
    margin-right: 500%;
}

.translation-check
{
    height: 20px;
    width: 80px;

    align-items: center;
    text-align: center;

    display: flex;

    pointer-events: none;
}

.translation-check-icon
{
    height: 10px;
    width: 10px;

    margin-left: 15px;
    margin-bottom: 2px;

    color: green;
}

.sticky th {
  top: 200px;
}
.shuffle-btn	
{	
    display: flex;	
    justify-content: center;	
    align-items: center;	
    height: 4vh;	
    width: 4vh;	
    margin: 0px;	
    margin-left: 20px;	
    cursor: pointer;	
    font-size: 50px;	
}	
th	
{	
    position: sticky;	
    top: -100px;	
}

.noLveles {
    display: grid;
    justify-content: center;
    text-align: center;
    margin-top: 10%;
    padding-bottom: 10%;
}

.ball-circle {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #2196F3;
    border: 2px solid #FFFFFF;
    float: right;
  }
  
  card-header {
    position: relative;
    z-index: 9999;
  }

  .card-table {
    position: relative;
    z-index: 1;
  }
  