import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, AfterViewInit } from '@angular/core';
import { Location, PopStateEvent} from '@angular/common';
import { filter } from 'rxjs/operators';
import { PopupService } from 'src/app/services/popup.service';
import { Subscription } from 'rxjs';
import { NavigationEnd, NavigationStart, Router } from '@angular/router';

/**
 * <AUTHOR>
 * @fileoverview This file contains the Admin Layout Angular Component".
 */

@Component({
  selector: 'app-root',
  templateUrl: './admin-layout.component.html',
})

/**
 * @component This component uses the <app-route> Angular selector,
 * involving all other components, thus being the main component.
 *
 * @view Wraps the app main panel and sidebar.
 */
export class AdminLayoutComponent implements OnInit, OnDestroy, AfterViewInit {
  
  constructor(
    public location: Location,
    private router: Router,
    private _popupService: PopupService
  ) {
    
  }
  private _router: Subscription;
  private lastPoppedUrl: string;
  private yScrollStack: number[] = [];
  private _poppingSubcription: Subscription;
  public popping: boolean;
  public isReady: boolean;

  ngOnDestroy(): void {
    this._poppingSubcription.unsubscribe();
  }
  ngOnInit() {
    this._poppingSubcription = this._popupService.popping.subscribe((value) => {
      this.popping = value;
    });

    const elemMainPanel = document.querySelector('.main-panel') as HTMLElement;
    const elemSidebar = document.querySelector(
      '.sidebar .sidebar-wrapper'
    ) as HTMLElement;

    this.location.subscribe((ev: PopStateEvent) => {
      this.lastPoppedUrl = ev.url;
    });

    this.router.events.subscribe((event: any) => {
      if (event instanceof NavigationStart) {
        if (event.url !== this.lastPoppedUrl) {
          this.yScrollStack.push(window.scrollY);
        }
      } else if (event instanceof NavigationEnd) {
        if (event.url === this.lastPoppedUrl) {
          this.lastPoppedUrl = undefined;
          window.scrollTo(0, this.yScrollStack.pop());
        } else {
          window.scrollTo(0, 0);
        }
      }
    });

    this._router = this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe((event: NavigationEnd) => {
        elemMainPanel.scrollTop = 0;
        elemSidebar.scrollTop = 0;
      });
    /* if (window.matchMedia(`(min-width: 960px)`).matches && !this.isMac()) {
      let ps = new PerfectScrollbar(elemMainPanel);
      ps = new PerfectScrollbar(elemSidebar);
    } */
  }

  ngAfterViewInit(): void {
    this.runOnRouteChange();
  }

  isMap(path): boolean {
    let titlee = this.location.prepareExternalUrl(this.location.path());
    titlee = titlee.slice(1);
    if (path === titlee) {
      return false;
    } else {
      return true;
    }
  }
  runOnRouteChange(): void {
    if (window.matchMedia(`(min-width: 960px)`).matches && !this.isMac()) {
      const elemMainPanel = document.querySelector(
        '.main-panel'
      ) as HTMLElement;
      /* const ps = new PerfectScrollbar(elemMainPanel);
      ps.update();*/
    }
  }
  isMac(): boolean {
    let bool = false;
    if (
      navigator.platform.toUpperCase().indexOf('MAC') >= 0 ||
      navigator.platform.toUpperCase().indexOf('IPAD') >= 0
    ) {
      bool = true;
    }
    return bool;
  }
}
