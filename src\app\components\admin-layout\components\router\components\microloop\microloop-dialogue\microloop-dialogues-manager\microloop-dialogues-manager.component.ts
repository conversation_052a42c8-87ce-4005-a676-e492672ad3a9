import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Area, Character, Dialogue, Level } from 'src/app/lib/@bus-tier/models';
import { AreaService, CharacterService, DialogueService, LevelService, PopupService, UserSettingsService } from 'src/app/services';
import { MicroloopContainerService } from 'src/app/services/microloop-container.service';
import { MicroloopService } from 'src/app/services/microloop.service';
import { Popup } from 'src/lib/darkcloud';
import { PreloadComponent } from 'src/lib/darkcloud/angular/easy-mvc';
import { LevelType, DialogueType, CharacterType, GameTypes } from 'src/lib/darkcloud/dialogue-system';

interface Preloadable 
{
  dialogues: Dialogue[];
  level: Level;
}

@Component({
  selector: 'app-microloop-dialogues-manager',
  templateUrl: './microloop-dialogues-manager.component.html',
  styleUrls: ['./microloop-dialogues-manager.component.scss']
})
export class MicroloopDialoguesManagerComponent extends PreloadComponent<Preloadable> implements OnInit 
{

  constructor(
    private _microloopService: MicroloopService,
    private _popupService: PopupService,
    private _areaService: AreaService,
    private _characterService: CharacterService,
    private _levelService: LevelService,
    private _router: Router,
    private _dialoguesService: DialogueService,
    private _microloopContainerService: MicroloopContainerService,
    public override readonly activatedRoute: ActivatedRoute,

    readonly userSettingsService: UserSettingsService
  )
  {
    super(activatedRoute);
  }

  public readonly LevelType = LevelType;
  public readonly DialogueType = DialogueType;

  public readonly headerDescription = 'List of Dialogs';

  public get level(): Level
  {
    return this._microloopService.svcFindById(this._router.url.split('/')[2].split('.')[0]+"."+this._router.url.split('/')[2].split('.')[1]);
  }
  public get dialogues(): Dialogue[] 
  {
    return this.preloadedData.dialogues;
  }

  public onCardBackButtonClick(): void 
  {
    let id = this.activatedRoute.snapshot.url[1].path;
    let processedId = id.split('.');
    
    this._microloopService.lastModifiedId = this.level.id;
    this._router.navigate(['microloopList', processedId[0], 'microloops']);
  }

  public trackByIndex(index: number, dialogue: Dialogue): any 
  {
    return index;
  }

  public ngOnInit(): void 
  {
    this._microloopService.lastModifiedId = this.level.id;
  }

  public redirectToDialogue(dialogue: Dialogue) 
  {
    this._router.navigate([
      'microloops/' + this.level.id + '/dialogues/' + dialogue.id,
    ]);
  }

  public async onChangeLevel() 
  {
    await this._microloopService.svcToModify(this.level);
  }

  public async toPromptAddBattleCharacterToLevel() 
  {
    const selectedCharacterButton = await this._popupService.fire<
      Area,
      Character
    >(
      new Popup.Interface(
        {
          title: 'Select Area',
          actionsClass: 'column',
        },
        Popup.toButtonList(
          this._areaService.models,
          {
            parameters: ['hierarchyCode', 'name'],
            mapping: '[<hierarchyCode>]: <name>',
          },
          { undefinedTitle: 'No Area' }
        ),
        {
          inputButton: {value: this._areaService.svcFindById(Area.getSubIdFrom(this.level.id)),},
          next: (selectedAreaButton) => 
          {
            if (!selectedAreaButton) return null;
            
            let characters =
              +this.level.type === LevelType.BOSS ? this._characterService.filterByType
              (
                CharacterType.BOSS,
                CharacterType.SUBBOSS
              )
              : this._characterService.filterByType
              (
                CharacterType.MINION,
                CharacterType.NPC
              );

            characters = characters.filter((character) => character.areaId === selectedAreaButton.value?.id);
            return new Popup.Interface<Character, Character>
            (
              {
                title: 'Select Character',
                actionsClass: 'column',
              },
              Popup.toButtonList(characters, 'name', 
              {
                classEnum: GameTypes.characterTypeName,
                classParameter: 'type',
              })
            );
          },
        }
      )
    );

    if (!selectedCharacterButton) return;
    

    this._microloopService.addBattleCharacter(this.level, selectedCharacterButton.value.id);
    this._characterService.svcReviewById(selectedCharacterButton.value.id);
  }

}
