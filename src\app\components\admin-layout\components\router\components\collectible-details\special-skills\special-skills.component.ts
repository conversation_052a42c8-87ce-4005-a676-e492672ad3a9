import { ChangeDetectorRef, Component, HostListener, Input } from '@angular/core';
import { AilmentTable, BoostTable, Category, ChaosTable, Character, ComboTable, DefensiveTable, DispelTable, HealingTable, NegativeTable, SpecialSkills } from 'src/app/lib/@bus-tier/models';
import { HybridTable } from 'src/app/lib/@bus-tier/models/HybridTable';
import { AilmentIdBlockservice, AilmentTableService, BattleUpgradeService, BoostIdBlockservice, BoostTableService, CategoryStatusEffectService, ChaosIdBlockservice, ChaosTableService, CharacterService, ClassService, DefensiveIdBlockservice, DefensiveTableService, DispelIdBlockservice, DispelTableService, HealingIdBlockservice, HealingTableService, HybridTableService, NegativeIdBlockservice, RepetitionStatusEffectService, SpecialSkillsService, StatusInfoService, UserSettingsService } from 'src/app/services';
import { NegativeTableService } from '../../../../../../../services/negative-table.service';
import { ActivatedRoute, Router } from '@angular/router';
import { ComboTableService } from 'src/app/services/combo-table.service';
import { GameTypes } from 'src/lib/darkcloud/dialogue-system';
import { BattleUpgrade } from 'src/app/lib/@bus-tier/models/BattleUpgrade';


/**
 * Interface que define a estrutura de um status skill (habilidade de status)
 * Representa os dados básicos de uma habilidade que pode ser aplicada a um personagem
 */
interface StatusSkill {
  id: string;              // Identificador único da habilidade
  name: string;            // Nome da habilidade
  description: string;     // Descrição detalhada da habilidade
  positionID: string;      // Posição/slot onde a habilidade pode ser equipada
  skillUser?: string;      // Usuário ou tipo de skill (opcional)
  idBoost?: string;        // ID do boost associado (para habilidades híbridas)
  idNegative?: string;     // ID do efeito negativo associado (para habilidades híbridas)
}

/**
 * Interface que define a estrutura de uma skill (habilidade especial)
 * Representa uma habilidade completa com todas as informações de categoria e efeito
 */
interface Skill {
  category?: string,                    // Categoria da habilidade (Boost, Healing, etc.)
  idCategory?: string,                  // ID da categoria
  relationCategory?: string,            // Relação da categoria (Resist/Weak)
  idStatusEffect?: string,              // ID do efeito de status
  nameStatusEffect?: string,            // Nome do efeito de status
  descriptionStatusEffect?: string,     // Descrição do efeito de status
  positionIDStatusEffect?: string,      // Posição do efeito de status
  positionCategory?: string,            // Posição da categoria
  indexStatusEffect?: number,           // Índice do efeito de status
  skillUserStatusEffect?: string        // Usuário do efeito de status
}


/**
 * Componente responsável por gerenciar as habilidades especiais de um personagem
 * Permite visualizar, adicionar e remover habilidades baseadas em categorias e afinidades elementais
 */
@Component({
  selector: 'app-special-skills',
  templateUrl: './special-skills.component.html',
  styleUrls: ['./special-skills.component.scss']
})
export class SpecialSkillsComponent {

  // Propriedades de entrada e dados do personagem
  @Input() character = '';                              // ID do personagem selecionado
  itemCharacter: Character;                             // Dados completos do personagem

  // Listas de dados e configurações
  listRepetitions = [];                                 // Lista de repetições por raridade
  listCountRarity = "";                                 // String formatada com contagem de raridade
  position = [];                                        // Posições baseadas na raridade do personagem
  getPosition = [];                                     // Posições formatadas para exibição
  listCategory: Category[] = [];                        // Lista de categorias disponíveis
  descriptionCatergory: string;                         // Descrição da categoria selecionada
  itemSelectedCategory: string;                         // Nome da categoria selecionada
  listStringElementalAffinities = [];                   // Lista de afinidades elementais do personagem
  valueBossLevel: string;
  nameClass: string;
  type: string;
  nameRarity: string;
  currentCharacter: BattleUpgrade;

  // Listas de IDs por categoria
  listIds = [];                                         // Lista geral de IDs
  listIdBlocks = [];                                    // IDs de boost blocks
  listIdHealing = [];                                   // IDs de healing
  listIdDefensive = [];                                 // IDs de defensive
  listIdNegative = [];                                  // IDs de negative
  listIdAilment = [];                                   // IDs de ailment
  listIdDispel = [];                                    // IDs de dispel
  listIdHybrid = [];                                    // IDs de hybrid
  listIdCombo = [];                                     // IDs de combo
  listIdChaos = [];                                    // IDs de chaos
  idsListHibrids = [];                                  // Lista específica para híbridos
  listDescriptionStatusEffect = [];                     // Lista de efeitos de status disponíveis

  // Tabelas de dados por categoria
  boostTable: BoostTable[] = []                         // Tabela de efeitos boost
  healingTable: HealingTable[] = []                     // Tabela de efeitos healing
  defensiveTable: DefensiveTable[] = [];                // Tabela de efeitos defensive
  negativeTable: NegativeTable[] = [];                  // Tabela de efeitos negative
  ailmentTable: AilmentTable[] = [];                    // Tabela de efeitos ailment
  dispelTable: DispelTable[] = [];                      // Tabela de efeitos dispel
  hybridTable: HybridTable[] = [];                      // Tabela de efeitos hybrid
  comboTable: ComboTable[] = [];                        // Tabela de efeitos combo
  chaosTable: ChaosTable[] = [];                        // Tabela de efeitos chaos

  // Controles de interface e estado
  indexPosition: number;                                // Índice da posição atual
  isModalInfo = false;                                  // Controla exibição do modal de informações
  listRemainingUnits: { position: number; amount: number }[] = []; // Unidades restantes por posição
  dropdownOpen = false;                                 // Controla abertura do dropdown
  selectedStatus: StatusSkill;                          // Status/habilidade selecionada
  listSpecialSkills: SpecialSkills;                     // Lista de habilidades especiais do personagem
  selectedCategory: Category;                           // Categoria selecionada
  isStatusSelected = false;                             // Indica se um status foi selecionado
  IsMaxCatgeory = false;                                // Indica se atingiu limite máximo de categoria
  indexCategory: string;                                // Índice da categoria selecionada
  categoryCount: number;                                // Contador de categorias
  isPulsing = false;                                    // Controla animação de pulsação

  // Contadores calculados
  listCalculatedHybrid: number;                         // Quantidade de híbridos disponíveis
  listCalculatedPhysical: number;                       // Quantidade de físicos disponíveis
  listCalculatedCombo: number;                          // Quantidade de combos disponíveis


  /**
   * Construtor do componente - injeta todos os serviços necessários para gerenciar habilidades especiais
   * @param _characterService - Serviço para gerenciar dados de personagens
   * @param _categoryStatusEffectService - Serviço para categorias de efeitos de status
   * @param _repetitionStatusEffectService - Serviço para repetições de efeitos
   * @param _elementalAffinities - Serviço para afinidades elementais
   * @param _specialSkillsService - Serviço principal para habilidades especiais
   * @param ref - Referência para detecção de mudanças
   * @param _router - Roteador para navegação
   * @param _userSettingsService - Serviço de configurações do usuário
   * @param _activatedRoute - Rota ativa
   * @param _boostIdBlockservice - Serviço para IDs de boost
   * @param _healingtIdBlockservice - Serviço para IDs de healing
   * @param _defensiveIdBlockservice - Serviço para IDs de defensive
   * @param _negativeIdBlockservice - Serviço para IDs de negative
   * @param _ailmentIdBlockservice - Serviço para IDs de ailment
   * @param _dispelIdBlockservice - Serviço para IDs de dispel
   * @param _boostTableService - Serviço para tabela de boost
   * @param _healingTableService - Serviço para tabela de healing
   * @param _defensiveTableService - Serviço para tabela de defensive
   * @param _negativeTableService - Serviço para tabela de negative
   * @param _ailmentTableService - Serviço para tabela de ailment
   * @param _dispelTableService - Serviço para tabela de dispel
   * @param _hybridTableService - Serviço para tabela de hybrid
   * @param _comboTableService - Serviço para tabela de combo
   */
  constructor(
    private _characterService: CharacterService,
    private _categoryStatusEffectService: CategoryStatusEffectService,
    private _repetitionStatusEffectService: RepetitionStatusEffectService,
    private _elementalAffinities: StatusInfoService,
    private _specialSkillsService: SpecialSkillsService,
    private ref: ChangeDetectorRef,
    private _classService: ClassService,
    protected _router: Router,
    _userSettingsService: UserSettingsService,
    _activatedRoute: ActivatedRoute,

    //Id Blocks - Serviços para gerenciar blocos de IDs por categoria
    private _boostIdBlockservice: BoostIdBlockservice,
    private _healingtIdBlockservice: HealingIdBlockservice,
    private _defensiveIdBlockservice: DefensiveIdBlockservice,
    private _negativeIdBlockservice: NegativeIdBlockservice,
    private _ailmentIdBlockservice: AilmentIdBlockservice,
    private _dispelIdBlockservice: DispelIdBlockservice,
    private _chaosIdBlockservice: ChaosIdBlockservice,

    // Status Effect Tables - Serviços para tabelas de efeitos de status
    private _boostTableService: BoostTableService,
    private _healingTableService: HealingTableService,
    private _defensiveTableService: DefensiveTableService,
    private _negativeTableService: NegativeTableService,
    private _ailmentTableService: AilmentTableService,
    private _dispelTableService: DispelTableService,
    private _hybridTableService: HybridTableService,
    private _comboTableService: ComboTableService,
    private _battleUpgradeService: BattleUpgradeService, 
    private _chaosTableService: ChaosTableService

  ) { }

  /**
   * Método de inicialização do componente
   * Carrega todos os dados necessários e configura o estado inicial
   * Executa cálculos de unidades restantes e organiza as habilidades especiais
   */
  async ngOnInit(): Promise<void> {

    // Carrega dados do personagem selecionado
    this._characterService.toFinishLoading();
    this.itemCharacter = this._characterService.models.find((character) => character.id === this.character);

    // Carrega dados de repetições e força atualização da view
    this._repetitionStatusEffectService.toFinishLoading();
    this.listRepetitions = this._repetitionStatusEffectService.models;
    this.ref.detectChanges();

   let valueCharacter = this.currentCharacter === undefined ? this.character : this.currentCharacter.character;
    this.currentCharacter = this._battleUpgradeService.models.find(modifier => modifier.character == this.character);

    // Timeout para garantir que todos os serviços estejam carregados
    setTimeout(async () => {
      // Calcula posições baseadas na raridade do personagem
      this.getRarityCount();

      // Carrega categorias e habilidades especiais
      this._categoryStatusEffectService.toFinishLoading();
      this.listCategory = this._categoryStatusEffectService.models;

      if (this.currentCharacter !== undefined) {
        const op = this.currentCharacter.bl != undefined ? this.currentCharacter.bl : 0;
        this.valueBossLevel = `BL: ${op}`;
      } else {
        this.valueBossLevel = 'BL: 0';
      }

      const character = this._characterService.models.find(x => x.id === valueCharacter); 
      this.nameClass = this._classService.models.find(x => x.id === character?.classId)?.name;
      this.nameRarity = character?.rarity;
      this.type = GameTypes.characterTypeName[character.type];


      this._specialSkillsService.toFinishLoading();
      this.listSpecialSkills = this._specialSkillsService.models.find(skill => skill.idChacracter === this.itemCharacter.id);

      // Executa cálculos de disponibilidade
      this.calculateRemainingUnits();    // Calcula unidades restantes por posição
      this.calculateHybrid();            // Calcula híbridos disponíveis
      this.calculateCombo();             // Calcula combos disponíveis
      this.calculatePhysical();          // Calcula físicos disponíveis

      // Cria nova lista de habilidades se não existir
      if (this.listSpecialSkills == undefined) {
        this.listSpecialSkills = await this._specialSkillsService.createNewSpecialSkills(this.itemCharacter);
        this.listSpecialSkills.listSpecialSkills = [] as Skill[];
      }
      else {
        // Se já existem habilidades, verifica limites e ordena
        if (this.listSpecialSkills.listSpecialSkills.length > 0) {
          this.checkCategoryLimit();
          this.sortSpecialSkillsByCategory();
        }
      }
      // Remove unidades já utilizadas dos contadores
      this.removeRemainingUnits();        // Remove unidades gerais utilizadas
      this.removePhisicalRemainingUnits(); // Remove físicos utilizados
      this.removeComboRemainingUnits();   // Remove combos utilizados      
    }, 500);

  }

  /**
   * Remove unidades já utilizadas do contador de unidades restantes
   * Percorre todas as habilidades especiais e decrementa o contador para cada habilidade não-híbrida
   */
  removeRemainingUnits() {
    this._specialSkillsService.models.forEach((x) => {
      x.listSpecialSkills.forEach((y) => {
        // Só conta habilidades que não são híbridas
        if (!y.idStatusEffect.includes('HYBRID')) {
          const position = parseInt(y.positionIDStatusEffect) - 1;
          this.listRemainingUnits[position].amount -= 1;
          this.ref.detectChanges();
        }
      });
    });
  }

  /**
   * Remove habilidades físicas já utilizadas do contador de físicos disponíveis
   * Percorre todas as habilidades especiais e decrementa o contador para cada habilidade física
   */
  removePhisicalRemainingUnits() {
    this._specialSkillsService.models.forEach((x) => {
      if (x.listSpecialSkills.length > 0) {
        x.listSpecialSkills.forEach((y) => {
          // Verifica se a habilidade é do tipo físico com validações de segurança
          if (y?.skillUserStatusEffect &&
              typeof y.skillUserStatusEffect === 'string' &&
              y.skillUserStatusEffect.toLowerCase().includes('físico')) {
            this.listCalculatedPhysical -= 1;
            this.ref.detectChanges();
          }
        });
      }
    });
  }

  /**
   * Remove combos já utilizados do contador de combos disponíveis
   * Percorre todas as habilidades especiais e decrementa o contador para cada combo
   */
  removeComboRemainingUnits() {
    this._specialSkillsService.models.forEach((x) => {
      if (x.listSpecialSkills.length > 0) {
        x.listSpecialSkills.forEach((y) => {
          // Verifica se a habilidade é do tipo combo com validações de segurança
          if (y?.skillUserStatusEffect &&
              typeof y.skillUserStatusEffect === 'string' &&
              y.skillUserStatusEffect.toLowerCase().includes('combo')) {
            this.listCalculatedCombo -= 1;
            this.ref.detectChanges();
          }
        });
      }
    });
  }

  /**
   * Calcula e formata as posições baseadas na raridade do personagem
   * Busca nas repetições quais posições correspondem à raridade do personagem atual
   */
  getRarityCount() {
    this.position = []

    // Validações de segurança
    if (!this.listRepetitions || this.listRepetitions.length === 0) {
      console.error("Repetitions list is empty or undefined.");
      return;
    }

    if (!this.itemCharacter || !this.itemCharacter.rarity) {
      console.error("ItemCharacter or rarity is undefined.");
      return;
    }

    // Busca as posições que correspondem à raridade do personagem
    this.listRepetitions.forEach(repetition => {
      repetition.positionNameRarity.forEach((rarity: any, index: any) => {
        if (rarity === this.itemCharacter.rarity) {
          this.position.push(index);
        }
      });
    });

    // Formata as posições encontradas para exibição
    this.getPosition = [];
    this.position.forEach((x) => {
      this.getPosition.push(this.getPositionRarity(x));
    });

    // Cria string formatada com as posições separadas por hífen
    this.listCountRarity = this.getPosition.join(' - ');
  }

  /**
   * Converte índice numérico em posição legível (1-6)
   * @param index - Índice da posição (0-5)
   * @returns String com a posição formatada (1-6)
   */
  getPositionRarity(index: number) {
    let position = '';

    if (index === 0) {
      position = '1';
    }
    else if (index === 1) {
      position = '2';
    }
    else if (index === 2) {
      position = '3';
    }
    else if (index === 3) {
      position = '4';
    }
    else if (index === 4) {
      position = '5';
    } else {
      position = '6';
    }
    return position;
  }
  /**
   * Calcula as unidades restantes por posição baseado em todas as categorias de habilidades
   * Soma a quantidade de habilidades disponíveis em cada uma das 6 posições
   * Considera: Boost, Healing, Defensive, Negative, Ailment e Dispel
   */
  calculateRemainingUnits() {
    this.listRemainingUnits = [];

    // Carrega todos os serviços de ID blocks
    this._boostIdBlockservice.toFinishLoading();
    const boostID = this._boostIdBlockservice.models;
    this._healingtIdBlockservice.toFinishLoading();
    const healingID = this._healingtIdBlockservice.models;
    this._defensiveIdBlockservice.toFinishLoading();
    const defensiveID = this._defensiveIdBlockservice.models;
    this._negativeIdBlockservice.toFinishLoading();
    const negativeID = this._negativeIdBlockservice.models;
    this._ailmentIdBlockservice.toFinishLoading();
    const ailmentID = this._ailmentIdBlockservice.models;
    this._dispelIdBlockservice.toFinishLoading();
    const dispelID = this._dispelIdBlockservice.models;
    this._comboTableService.toFinishLoading();
   

    // CALCULA BOOST ID
    // Inicializa um array para acumular as somas das 6 posições
    const totals = Array(6).fill(0);
    boostID.forEach((boost) => {
      boost.positionNameBoosts.forEach((item, index) => {
        if (item !== "") {
          totals[index]++;
        }
      });
    });

    // Transforma os totais calculados no formato desejado
    this.listRemainingUnits = totals.map((amount, index) => ({
      position: index + 1, // Posições começam em 1 (não 0)
      amount,
    }));

    // CALCULA HEALING ID
    // Conta quantas habilidades de healing existem em cada posição
    const totalsHealing = Array(6).fill(0);
    healingID.forEach((healing) => {
      healing.positionNameHealing.forEach((item, index) => {
        if (item !== "") {
          totalsHealing[index]++;
        }
      });
    });

    // Soma as habilidades de healing ao total de unidades restantes
    this.listRemainingUnits.filter((unit, index) => {
      const total = unit.amount + totalsHealing[index];
      this.listRemainingUnits[index].amount = null;
      this.listRemainingUnits[index].amount = total;
    });

    // CALCULA DEFENSIVE ID
    // Conta quantas habilidades defensivas existem em cada posição
    const totalsDefensive = Array(6).fill(0);
    defensiveID.forEach((defensive) => {
      defensive.positionNameDefensive.forEach((item, index) => {
        if (item !== "") {
          totalsDefensive[index]++;
        }
      });
    });

    // Soma as habilidades defensivas ao total de unidades restantes
    this.listRemainingUnits.filter((unit, index) => {
      const total = unit.amount + totalsDefensive[index];
      this.listRemainingUnits[index].amount = null;
      this.listRemainingUnits[index].amount = total;
    });

    // CALCULA NEGATIVE ID
    // Conta quantas habilidades negativas existem em cada posição
    const totalsNegative = Array(6).fill(0);
    negativeID.forEach((negative) => {
      negative.positionNameNegative.forEach((item, index) => {
        if (item !== "") {
          totalsNegative[index]++;
        }
      });
    });

    // Soma as habilidades negativas ao total de unidades restantes
    this.listRemainingUnits.filter((unit, index) => {
      const total = unit.amount + totalsNegative[index];
      this.listRemainingUnits[index].amount = null;
      this.listRemainingUnits[index].amount = total;
    });

    // CALCULA AILMENT ID
    // Conta quantas habilidades de ailment existem em cada posição
    const totalsAilment = Array(6).fill(0);
    ailmentID.forEach((ailment) => {
      ailment.positionNameAiment.forEach((item, index) => {
        if (item !== "") {
          totalsAilment[index]++;
        }
      });
    });

    // Soma as habilidades de ailment ao total de unidades restantes
    this.listRemainingUnits.filter((unit, index) => {
      const total = unit.amount + totalsAilment[index];
      this.listRemainingUnits[index].amount = null;
      this.listRemainingUnits[index].amount = total;
    });

    // CALCULA DISPEL ID
    // Conta quantas habilidades de dispel existem em cada posição
    const totalsDispel = Array(6).fill(0);
    dispelID.forEach((dispel) => {
      dispel.positionNameDispel.forEach((item, index) => {
        if (item !== "") {
          totalsDispel[index]++;
        }
      });
    });

    // Soma as habilidades de dispel ao total de unidades restantes
    this.listRemainingUnits.filter((unit, index) => {
      const total = unit.amount + totalsDispel[index];
      this.listRemainingUnits[index].amount = null;
      this.listRemainingUnits[index].amount = total;
    });
  }


  /**
   * Calcula quantas habilidades híbridas estão disponíveis (não utilizadas)
   * Filtra a tabela de híbridos removendo os que já estão em uso por algum personagem
   */
  calculateHybrid() {
    this._hybridTableService.toFinishLoading();
    this.hybridTable = this._hybridTableService.models;

    // Filtra híbridos que ainda não foram utilizados por nenhum personagem
    const filteredHybrid = this.hybridTable.filter(hybridItem => {
      // Verifica se o híbrido NÃO está presente em nenhuma lista de habilidades especiais
      return !this._specialSkillsService.models.some(specialSkill =>
        specialSkill.listSpecialSkills.some(specialSkillItem =>
          specialSkillItem.idStatusEffect === hybridItem.idHybrid
        )
      );
    });

    // Conta apenas híbridos com ID válido (não vazio)
    this.listCalculatedHybrid = filteredHybrid.filter(hybridItem => hybridItem.idHybrid !== "").length;
  }

  /**
   * Calcula quantos combos estão disponíveis (não utilizados)
   * Filtra a tabela de combos removendo os que já estão em uso por algum personagem
   */
  calculateCombo() {
    this._comboTableService.toFinishLoading();
    this.comboTable = this._comboTableService.models;

    // Filtra combos que ainda não foram utilizados por nenhum personagem
    const filteredCombo = this.comboTable.filter(comboItem => {
      // Verifica se o combo NÃO está presente em nenhuma lista de habilidades especiais
      return !this._specialSkillsService.models.some(specialSkill =>
        specialSkill.listSpecialSkills.some(specialSkillItem =>
          specialSkillItem.idStatusEffect === comboItem.idCombo
        )
      );
    });

    // Conta apenas combos com ID válido (não vazio)
    this.listCalculatedCombo = filteredCombo.filter(comboItem => comboItem.idCombo !== "").length;
  }

  /**
   * Calcula quantas habilidades físicas estão disponíveis em todas as tabelas
   * Percorre todas as tabelas de habilidades procurando por habilidades que contenham 'físico'
   */
  calculatePhysical() {
    // Inicializa o contador
    this.listCalculatedPhysical = 0;

    // Conta habilidades físicas na tabela de boost
    this._boostTableService.models.forEach((x) => {
      if (x.skillResistUser && x.skillResistUser.toLowerCase().includes('físico')) {
        this.listCalculatedPhysical += 1;
      }
    });

    // Conta habilidades físicas na tabela de healing
    this._healingTableService.models.forEach((x) => {
      if (x.skillResistUser && x.skillResistUser.toLowerCase().includes('físico')) {
        this.listCalculatedPhysical += 1;
      }
    });

    // Conta habilidades físicas na tabela de defensive (usa skillWeakUser)
    this._defensiveTableService.models.forEach((x) => {
      if (x.skillWeakUser && x.skillWeakUser.toLowerCase().includes('físico')) {
        this.listCalculatedPhysical += 1;
      }
    });

    // Conta habilidades físicas na tabela de negative
    this._negativeTableService.models.forEach((x) => {
      if (x.skillResistUser && x.skillResistUser.toLowerCase().includes('físico')) {
        this.listCalculatedPhysical += 1;
      }
    });

    // Conta habilidades físicas na tabela de ailment (usa statusEffectName)
    this._ailmentTableService.models.forEach((x) => {
      if (x.statusEffectName && x.statusEffectName.toLowerCase().includes('físico')) {
        this.listCalculatedPhysical += 1;
      }
    });

    // Conta habilidades físicas na tabela de dispel
    this._dispelTableService.models.forEach((x) => {
      if (x.skillResistUser && x.skillResistUser.toLowerCase().includes('físico')) {
        this.listCalculatedPhysical += 1;
      }
    });

    // Conta habilidades físicas na tabela de hybrid
    this._hybridTableService.models.forEach((x) => {
      if (x.skillResistUser && x.skillResistUser.toLowerCase().includes('físico')) {
        this.listCalculatedPhysical += 1;
      }
    });
  }

  /**
   * Reseta o componente para um novo personagem
   * Limpa todas as listas e reinicializa o componente
   * @param character - ID do novo personagem
   */
  reset(character: any) {
    this.character = character;
    this.listCategory = [];
    this.itemSelectedCategory = null;
    this.descriptionCatergory = null;
    this.ref.detectChanges();
    this.ngOnInit();
  }
  /**
   * Fecha o modal de informações de special skills
   * Método chamado pelo componente filho modal-info-special-skills
   */
  closeModalSpecialSkills() {
    this.isModalInfo = false;
  }

  /**
   * Alterna a exibição do modal de informações
   */
  onModalClick(): void {
    this.isModalInfo = !this.isModalInfo;
  }

  /**
   * Manipula a seleção de uma categoria no dropdown
   * Carrega as afinidades elementais do personagem e filtra as habilidades disponíveis
   * @param event - Evento de mudança do select
   */
  selectCategoryItem(event: Event) {

    // Limpa listas anteriores
    this.listStringElementalAffinities = [];
    this.indexCategory = null;

    const selectElement = event.target as HTMLSelectElement;
    this.indexCategory = selectElement.value;

    // Se selecionou a opção padrão, limpa a descrição e retorna
    if (this.indexCategory === 'default') {
      this.descriptionCatergory = undefined;
      return;
    }

    // Define a categoria selecionada e suas informações
    this.itemSelectedCategory = this.listCategory[selectElement.value].category;
    this.descriptionCatergory = this.listCategory[selectElement.value].description;
    this.selectedCategory = this.listCategory[selectElement.value]

    // Carrega afinidades elementais baseadas na relação da categoria (Resist ou Weak)
    if (this.listCategory[selectElement.value].relation === "Resist") {
      // Para categorias de resistência, pega elementos com ascensionOrder definido
      this._elementalAffinities.models.forEach(elemental => {
        if (elemental.character === this.character && elemental.ascensionOrder != null) { 
          this.listStringElementalAffinities.push(elemental.status);
        }
      });
    }
    else {
      // Para categorias de fraqueza, pega elementos com weakness definido
      this._elementalAffinities.models.forEach(elemental => {
        if (elemental.character === this.character && elemental.weakeness != null) {
          this.listStringElementalAffinities.push(elemental.status);
        }
      });
    }

    // Sempre adiciona 'físico' às afinidades disponíveis
    this.listStringElementalAffinities.push('físico');

    // Verifica se a categoria está dentro do limite permitido (máximo 2 por categoria)
    if (!this.checkCategoryLimit()) {
      return;
    }
    else {
      // Carrega os itens da categoria selecionada
      this.getCatergoryItem(this.listCategory[selectElement.value].category);
      this.isStatusSelected = false;
    }
  }

  /**
   * Direciona para o método específico de carregamento baseado na categoria selecionada
   * @param category - Nome da categoria selecionada
   */
  getCatergoryItem(category: string) {

    if (category === 'Boost') {
      this.getIdBlocksDescription();
    }
    else if (category === 'Healing') {
      this.getIdHealingDescription();
    }
    else if (category === 'Defensive') {
      this.getIdDefensiveDescription();
    }
    else if (category === 'Negative') {
      this.getIdNegativeDescription();
    }
    else if (category === 'Ailment') {
      this.getIdAilmentDescription();
    }
    else if (category === 'Dispel') {
      this.getIdDispelDescription();
    }
    else if (category === 'Boost - Negative (Hybrid)') {
      this.getIdHybridDescription();
    }
    else if (category === 'Combo') {
      this.getIdComboDescription();
    }
    else {
      // Para categoria 'Chaos' ou outras não especificadas
      console.log('Categoria Chaos: ', category);      
      this.getIdChaosDescription();
    }

  }

  /**
   * Carrega descrições e dados das habilidades de Boost
   * Busca IDs de boost baseados nas posições do personagem e filtra por afinidades elementais
   */
  getIdBlocksDescription() {
    // Limpa listas anteriores
    this.listDescriptionStatusEffect = [];
    this.listIdBlocks = [];
    this.listIds = [];

    // Carrega IDs de boost baseados nas posições do personagem
    this._boostIdBlockservice.toFinishLoading();
    this._boostIdBlockservice.models.forEach(boost => {
      for (let index = 0; index < this.position.length; index++) {
        if (boost.positionNameBoosts[this.position[index]]) {
          this.listIdBlocks.push({
            id: boost.positionNameBoosts[this.position[index]],
            position: this.position[index]
          });
        }
      }
    });
    this.listIds = this.listIdBlocks;

    // Remove IDs duplicados (mesmo ID na mesma posição)
    this.listIdBlocks = this.listIdBlocks.filter((value, index, self) =>
      index === self.findIndex(item => item.id === value.id && item.position === value.position)
    );

    // Converte posições de índice para números legíveis (1-6)
    this.listIdBlocks.forEach(idBlock => {
      idBlock.position = this.getPositionRarity(idBlock.position);
    });

    // Carrega dados da tabela de boost
    this._boostTableService.toFinishLoading();
    this.boostTable = this._boostTableService.models;

    // Cruza dados dos IDs com a tabela de boost para obter descrições
    this.listIdBlocks.forEach((idBlock) => {
      this.boostTable.forEach((boost) => {
        if (boost.idBoost === idBlock.id) {
          // Verifica se a habilidade é compatível com as afinidades elementais do personagem
          const skillResistUserLowerCase = (boost.skillResistUser || '').toLowerCase();
          const listElementalAffinitiesLowerCase = this.listStringElementalAffinities.map(affinity => affinity.toLowerCase());

          if (listElementalAffinitiesLowerCase.includes(skillResistUserLowerCase)) {
            // Adiciona a habilidade à lista de efeitos disponíveis
            this.listDescriptionStatusEffect.push({
              id: boost.idBoost,
              name: boost.statusEffectName,
              description: boost.description,
              positionID: idBlock.position,
              skillUser: boost.skillResistUser
            });
          }
        }
      });
    });

    // Remove habilidades já selecionadas/utilizadas
    this.removeStatusSelected();
  }

  getIdHealingDescription() {

    this.listDescriptionStatusEffect = [];
    this.listIdHealing = [];
    this.listIds = [];

    //Pega os Ids da tabela ID Healing
    this._healingtIdBlockservice.toFinishLoading();
    this._healingtIdBlockservice.models.forEach(healing => {
      for (let index = 0; index < this.position.length; index++) {
        if (healing.positionNameHealing[this.position[index]]) {
          this.listIdHealing.push({
            id: healing.positionNameHealing[this.position[index]],
            position: this.position[index]
          });
        }
      }
    });

    this.listIds = this.listIdHealing;

    // Remove ids duplicados
    this.listIdHealing = this.listIdHealing.filter((value, index, self) =>
      index === self.findIndex(item => item.id === value.id && item.position === value.position)
    );

    //Transforma as posições em números inteiros
    this.listIdHealing.forEach(idHealing => {
      idHealing.position = this.getPositionRarity(idHealing.position);
    });

    this._healingTableService.toFinishLoading();
    this.healingTable = this._healingTableService.models;

    // Iterar pela lista de IDs de blocos //Pega a descrição da tabela Status Effect Table/ Healing Table
    this.listIdHealing.forEach((idHealing) => {
      // Procurar por correspondência no array healingTable   
      this.healingTable.forEach((healing) => {
        if (healing.idHealing === idHealing.id) {
          // Transformar ambas as strings para letras minúsculas antes de comparar
          const skillResistUserLowerCase = (healing.skillResistUser || '').toLowerCase();
          const listElementalAffinitiesLowerCase = this.listStringElementalAffinities.map(affinity => affinity.toLowerCase());

          if (listElementalAffinitiesLowerCase.includes(skillResistUserLowerCase)) {
            // Adicionar os campos no array listDescriptionStatusEffect
            this.listDescriptionStatusEffect.push({
              id: healing.idHealing,
              name: healing.statusEffectName,
              description: healing.description,
              positionID: idHealing.position,
              skillUser: healing.skillResistUser
            });
          }
        }
      });
    });
    this.removeStatusSelected();
  }

  getIdDefensiveDescription() {

    this.listDescriptionStatusEffect = [];
    this.listIdDefensive = [];
    this.listIds = [];

    //Pega os Ids da tabela ID Defensive
    this._defensiveIdBlockservice.toFinishLoading();
    this._defensiveIdBlockservice.models.forEach(defensive => {
      for (let index = 0; index < this.position.length; index++) {
        if (defensive.positionNameDefensive[this.position[index]]) {
          this.listIdDefensive.push({
            id: defensive.positionNameDefensive[this.position[index]],
            position: this.position[index]
          });
        }
      }
    });
    this.listIds = this.listIdDefensive;

    this.listIdDefensive = this.listIdDefensive.filter((value, index, self) =>
      index === self.findIndex(item => item.id === value.id && item.position === value.position)
    );

    this.listIdDefensive.forEach(idDefensive => {
      idDefensive.position = this.getPositionRarity(idDefensive.position);
    });

    this._defensiveTableService.toFinishLoading();
    this.defensiveTable = this._defensiveTableService.models;

    this.listIdDefensive.forEach((idDefensive) => {

      this.defensiveTable.forEach((defensive) => {
        if (defensive.idDefensive === idDefensive.id) {
          const skillResistUserLowerCase = (defensive.skillWeakUser || '').toLowerCase();
          const listElementalAffinitiesLowerCase = this.listStringElementalAffinities.map(affinity => affinity.toLowerCase());

          if (listElementalAffinitiesLowerCase.includes(skillResistUserLowerCase)) {
            this.listDescriptionStatusEffect.push({
              id: defensive.idDefensive,
              name: defensive.statusEffectName,
              description: defensive.description,
              positionID: idDefensive.position,
              skillUser: defensive.skillWeakUser
            });
          }
        }
      });
    });
    this.removeStatusSelected();
  }

  getIdNegativeDescription() {
    this.listDescriptionStatusEffect = [];
    this.listIdNegative = [];
    this.listIds = [];

    //Pega os Ids da tabela ID Defensive
    this._negativeIdBlockservice.toFinishLoading();
    this._negativeIdBlockservice.models.forEach(defensive => {
      for (let index = 0; index < this.position.length; index++) {
        if (defensive.positionNameNegative[this.position[index]]) {
          this.listIdNegative.push({
            id: defensive.positionNameNegative[this.position[index]],
            position: this.position[index]
          });
        }
      }
    });

    this.listIds = this.listIdNegative;

    //Remove elementos duplicados
    this.listIdNegative = this.listIdNegative.filter((value, index, self) =>
      index === self.findIndex(item => item.id === value.id && item.position === value.position)
    );

    this.listIdNegative.forEach(idNegative => {
      idNegative.position = this.getPositionRarity(idNegative.position);
    });

    this._negativeTableService.toFinishLoading();
    this.negativeTable = this._negativeTableService.models;

    this.listIdNegative.forEach((idNegative) => {

      this.negativeTable.forEach((negative) => {
        if (negative.idNegative === idNegative.id) {
          const skillResistUserLowerCase = (negative.skillResistUser || '').toLowerCase();
          const listElementalAffinitiesLowerCase = this.listStringElementalAffinities.map(affinity => affinity.toLowerCase());

          if (listElementalAffinitiesLowerCase.includes(skillResistUserLowerCase)) {
            this.listDescriptionStatusEffect.push({
              id: negative.idNegative,
              name: negative.statusEffectName,
              description: negative.description,
              positionID: idNegative.position,
              skillUser: negative.skillResistUser
            });
          }
        }
      });
    });
    this.removeStatusSelected();
  }

  getIdAilmentDescription() {

    this.listDescriptionStatusEffect = [];
    this.listIdAilment = [];
    this.listIds = [];

    //Pega os Ids da tabela ID Ailemnt
    this._ailmentIdBlockservice.toFinishLoading();
    this._ailmentIdBlockservice.models.forEach(ailment => {
      for (let index = 0; index < this.position.length; index++) {
        if (ailment.positionNameAiment[this.position[index]]) {
          this.listIdAilment.push({
            id: ailment.positionNameAiment[this.position[index]],
            position: this.position[index]
          });
        }
      }
    });
    this.listIds = this.listIdAilment;
    this.listIdAilment = this.listIdAilment.filter((value, index, self) =>
      index === self.findIndex(item => item.id === value.id && item.position === value.position)
    );

    this.listIdAilment.forEach(idAilment => {
      idAilment.position = this.getPositionRarity(idAilment.position);
    });

    this._ailmentTableService.toFinishLoading();
    this.ailmentTable = this._ailmentTableService.models;

    this.listIdAilment.forEach((idAilment) => {

      this.ailmentTable.forEach((ailment) => {
        if (ailment.idAilment === idAilment.id) {
          this.listDescriptionStatusEffect.push({
            id: ailment.idAilment,
            name: ailment.statusEffectName,
            description: ailment.description,
            positionID: idAilment.position,
            skillUser: ailment.statusEffectName
          });
        }
      });
    });
    this.removeStatusSelected();
  }

  getIdDispelDescription() {
    this.listDescriptionStatusEffect = [];
    this.listIdDispel = [];
    this.listIds = [];

    //Pega os Ids da tabela ID Dispel
    this._dispelIdBlockservice.toFinishLoading();
    this._dispelIdBlockservice.models.forEach(dispel => {
      for (let index = 0; index < this.position.length; index++) {
        if (dispel.positionNameDispel[this.position[index]]) {
          this.listIdDispel.push({
            id: dispel.positionNameDispel[this.position[index]],
            position: this.position[index]
          });
        }
      }
    });

    this.listIds = this.listIdDispel;

    this.listIdDispel = this.listIdDispel.filter((value, index, self) =>
      index === self.findIndex(item => item.id === value.id && item.position === value.position)
    );

    this.listIdDispel.forEach(idDispel => {
      idDispel.position = this.getPositionRarity(idDispel.position);
    });

    this._dispelTableService.toFinishLoading();
    this.dispelTable = this._dispelTableService.models;

    this.listIdDispel.forEach((idDispel) => {

      this.dispelTable.forEach((dispel) => {
        if (dispel.idDispel === idDispel.id) {
          const skillResistUserLowerCase = (dispel.skillResistUser || '').toLowerCase();
          const listElementalAffinitiesLowerCase = this.listStringElementalAffinities.map(affinity => affinity.toLowerCase());

          if (listElementalAffinitiesLowerCase.includes(skillResistUserLowerCase)) {
            this.listDescriptionStatusEffect.push({
              id: dispel.idDispel,
              name: dispel.statusEffectName,
              description: dispel.description,
              positionID: idDispel.position,
              skillUser: dispel.skillResistUser
            });
          }
        }
      });
    });
    this.removeStatusSelected();
  }

  getIdHybridDescription() {

    this.listDescriptionStatusEffect = [];
    this.listIdHybrid = [];

    this.hybridTable.forEach((hybrid) => {
      const skillResistUserLowerCase = (hybrid.skillResistUser || '').toLowerCase();
      const listElementalAffinitiesLowerCase = this.listStringElementalAffinities.map(affinity => affinity.toLowerCase());

      if (listElementalAffinitiesLowerCase.includes(skillResistUserLowerCase)) {
        this.listDescriptionStatusEffect.push({
          id: hybrid.idHybrid,
          name: hybrid.statusEffectName,
          description: hybrid.description,
          positionID: 1,
          skillUser: hybrid.skillResistUser,
          idBoost: hybrid.idBoost,
          idNegative: hybrid.idNegative
        });
      }
    }); 
  }
  /**
   * Remove habilidades já selecionadas das listas disponíveis
   * Aplica regras específicas baseadas na tipologia da categoria:
   * - Combo: Apenas remove já utilizados, sem regras de repetição ou híbrido
   * - Hybrid: Aplica regras específicas de híbrido
   * - Outras: Aplica regras de repetição e híbrido
   */
  removeStatusSelected() {

    // Para categoria Combo, aplica regras simplificadas
    if (this.selectedCategory.category === "Combo") {
      // Remove apenas os combos já utilizados por qualquer personagem
      this.listSpecialSkills.listSpecialSkills.forEach((specialSkill) => {
        this.listDescriptionStatusEffect = this.listDescriptionStatusEffect.filter((statusEffect) => {
          return specialSkill.idStatusEffect !== statusEffect.id;
        });
      });

      // Remove combos já utilizados por outros personagens
      this._specialSkillsService.models.forEach((model) => {
        model.listSpecialSkills.forEach((specialSkill) => {
          this.listDescriptionStatusEffect = this.listDescriptionStatusEffect.filter((statusEffect) => {
            return specialSkill.idStatusEffect !== statusEffect.id;
          });
        });
      });
    }
    else if (this.selectedCategory.typology !== "Hybrid") {
      // Para categorias não-híbridas e não-combo, aplica regras normais
      this.listSpecialSkills.listSpecialSkills.forEach((specialSkill) => {
        this.listDescriptionStatusEffect = this.listDescriptionStatusEffect.filter((statusEffect) => {
          return specialSkill.idStatusEffect !== statusEffect.id;
        });
      });

      // Remove os itens que já foram usados de acordo a regra de repetição
      this.listDescriptionStatusEffect = this.listDescriptionStatusEffect.filter((statusEffect) => {
        const qtdIds = this.listIds.filter(id => id.id === statusEffect.id).length;

        let usesSkill = 0;

        this._specialSkillsService.models.forEach((model) => {
          if (model.listSpecialSkills.length > 0) {
            usesSkill += model.listSpecialSkills.filter(skill => skill.idStatusEffect === statusEffect.id).length;
          }
        });

        return (qtdIds - usesSkill) !== 0;
      });

      //Se o Special Skill selecionado for do tipo Boost ou Negative deve remover da seleção no Status Effect o id do Hybrid
      //para o personagem atual
      if (this.selectedCategory.category === "Boost" || this.selectedCategory.category === "Negative") {
        this.hybridTable.forEach((hybrid) => {
          this.listSpecialSkills.listSpecialSkills.forEach((specialSkill) => {
            if (specialSkill.idStatusEffect === hybrid.idHybrid) {
              this.listDescriptionStatusEffect = this.listDescriptionStatusEffect.filter((list) =>
                !(list.id === hybrid.idBoost || list.id === hybrid.idNegative)
              )
            }
          });
        });
      }
    }
    else {
      // Para categorias híbridas, aplica regras específicas de híbrido
      //Se o Special Skill selecionado for do tipo Boost ou Negative deve remover da seleção no Status Effect o id do Híbrido
      //para o personagem atual
      this._specialSkillsService.models.forEach((model) => {
        model.listSpecialSkills.forEach((specialSkill) => {
          this.listDescriptionStatusEffect = this.listDescriptionStatusEffect.filter(
            (x) => !(x.id === specialSkill.idStatusEffect ||
              x.idBoost === specialSkill.idStatusEffect ||
              x.idNegative === specialSkill.idStatusEffect)
          );
        });
      });
    }
  }
  
  /**
   * Carrega descrições e dados das habilidades de Combo
   * Busca combos baseados nas afinidades elementais do personagem
   * Combos não dependem de repetições ou ID blocks, apenas de afinidades elementais
   */
  getIdComboDescription() {

    this.listDescriptionStatusEffect = [];
    this.listIdCombo = [];

    this.comboTable.forEach((combo) => {
      // Verifica se o combo tem ID válido antes de processar
      if (!combo.idCombo || combo.idCombo.trim() === '') {      
        return;
      }

      const skillResistUserLowerCase = combo.skillResistUser.filter(cmb => cmb !== "").map(cmb => cmb.toLowerCase());
      const listElementalAffinitiesLowerCase = this.listStringElementalAffinities.map(affinity => affinity.toLowerCase());
   

      // Verifica se TODAS as habilidades do combo estão nas afinidades do personagem
      const allSkillsMatch = skillResistUserLowerCase.every(skill =>
        listElementalAffinitiesLowerCase.includes(skill)
      );

      if (allSkillsMatch && skillResistUserLowerCase.length > 0) {
        // Verifica se o combo já foi adicionado para evitar duplicatas
        const alreadyAdded = this.listDescriptionStatusEffect.some(item => item.id === combo.idCombo);

        if (!alreadyAdded) {
          this.listDescriptionStatusEffect.push({
            id: combo.idCombo,
            name: combo.statusEffectName,
            description: combo.description,
            positionID: 1,
            skillUser: combo.skillResistUser
          });
        }
      }

    });
    this.removeStatusSelected();;
  }

  getIdChaosDescription() {

    this.listDescriptionStatusEffect = [];
    this.listIdChaos = [];
    this.listIds = [];

    //Pega os Ids da tabela ID Chaos
    this._chaosIdBlockservice.toFinishLoading();
    this._chaosIdBlockservice.models.forEach(chaos => {
      for (let index = 0; index < this.position.length; index++) {
        if (chaos.positionNameChaos[this.position[index]]) {
          this.listIdChaos.push({
            id: chaos.positionNameChaos[this.position[index]],
            position: this.position[index]
          });
        }
      }
    });
    this.listIds = this.listIdChaos;
    this.listIdChaos = this.listIdChaos.filter((value, index, self) =>
      index === self.findIndex(item => item.id === value.id && item.position === value.position)
    );

    this.listIdChaos.forEach(idAilment => {
      idAilment.position = this.getPositionRarity(idAilment.position);
    });

    this._chaosTableService.toFinishLoading();
    this.chaosTable = this._chaosTableService.models;

    this.listIdChaos.forEach((idChaos) => {

      this.chaosTable.forEach((chaos) => {
        if (chaos.idChaosTable === idChaos.id) {
          this.listDescriptionStatusEffect.push({
            id: chaos.idChaosTable,
            name: chaos.statusEffectName,
            description: chaos.description,
            positionID: idChaos.position,
            skillUser: chaos.statusEffectName
          });
        }
      });
    });
    this.removeStatusSelected();
  }

  /**
   * Alterna o estado de abertura/fechamento do dropdown de habilidades
   */
  toggleDropdown() {
    this.dropdownOpen = !this.dropdownOpen;
  }

  /**
   * Fecha o dropdown quando o usuário clica fora dele
   * @param _event - Evento de clique (não utilizado, mas necessário para o HostListener)
   */
  @HostListener('document:click', ['$event'])
  closeDropdown(_event: Event) {
    this.dropdownOpen = false; // Fecha sempre que houver clique fora
  }

  /**
   * Evita que o dropdown feche quando o usuário clica dentro dele
   * @param event - Evento de clique
   */
  stopPropagation(event: Event) {
    event.stopPropagation();
  }

  /**
   * Seleciona e adiciona uma habilidade especial à lista do personagem
   * Verifica se há espaço disponível (máximo 5 habilidades) antes de adicionar
   * @param index - Índice da habilidade selecionada na lista de efeitos disponíveis
   */
  async selectStatusEffect(index: number) {
    // Obtém a habilidade selecionada
    this.selectedStatus = this.listDescriptionStatusEffect[index];

    // Verifica se ainda há espaço para mais habilidades (máximo 5)
    if (this.listSpecialSkills.listSpecialSkills.length < 5) {

      // Adiciona a nova habilidade à lista do personagem
      this.listSpecialSkills.listSpecialSkills.push({
        category: this.itemSelectedCategory,
        idCategory: this.selectedCategory.id,
        indexStatusEffect: index,
        relationCategory: this.selectedCategory.relation,
        idStatusEffect: this.selectedStatus.id,
        nameStatusEffect: this.selectedStatus.name,
        descriptionStatusEffect: this.selectedStatus.description,
        positionIDStatusEffect: this.selectedStatus.positionID,
        positionCategory: this.indexCategory + 1,
        skillUserStatusEffect: this.selectedStatus.skillUser
      });

      // Remove a habilidade da lista de disponíveis
      this.removeSkillList(index);

      // Salva as alterações no serviço
      await this._specialSkillsService.svcToModify(this.listSpecialSkills);
      await this._specialSkillsService.toSave();
      this.ref.detectChanges();

      // Fecha o dropdown após a seleção
      this.dropdownOpen = false;

    }
    else {
      // Se a lista estiver cheia, exibe aviso e marca flags de controle
      console.warn('Lista de skills cheia. Não é possível adicionar mais itens.');
      this.isStatusSelected = true;
      this.IsMaxCatgeory = true;
      return; // Não realiza mais nenhuma ação
    }

    // Reinicializa o componente para atualizar contadores e listas
    this.ngOnInit();
  }

  /**
   * Calcula o total de vezes que uma habilidade aparece nas posições disponíveis
   * Para combos, retorna 1 (sempre disponível)
   * Para outras categorias, conta ocorrências na lista de IDs
   * @param status - Objeto da habilidade
   * @returns Número total de ocorrências da habilidade
   */
  getTotalPosition(status: any): number | string {
    // Para combos, sempre retorna 1 (não dependem de repetições)
    if (this.selectedCategory.category === "Combo") {
      return 1;
    }

    // Para outras categorias, conta quantas vezes o ID da habilidade aparece na lista de IDs disponíveis
    const total = this.listIds.filter(id => id.id === status.id).length;
    return total;
  }

  /**
   * Calcula quantas vezes uma habilidade ainda pode ser usada (total - já utilizadas)
   * Para combos, verifica apenas se já foi utilizado (retorna 1 ou 0)
   * Para outras categorias, calcula diferença entre disponível e utilizado
   * @param status - Objeto da habilidade
   * @returns Quantidade disponível da habilidade
   */
  getAmountPosition(status: any): number | string {

    // Para combos, verifica apenas se já foi utilizado
    if (this.selectedCategory.category === "Combo") {
      let usesSkill = 0;

      this._specialSkillsService.models.forEach((model) => {
        if (model.listSpecialSkills.length > 0) {
          usesSkill += model.listSpecialSkills.filter(skill => skill.idStatusEffect === status.id).length;
        }
      });

      // Retorna 1 se não foi usado, 0 se já foi usado
      return usesSkill > 0 ? 0 : 1;
    }

    // Para outras categorias, aplica lógica normal
    // Conta quantas vezes o ID aparece na lista de IDs disponíveis
    const qtdIds = this.listIds.filter(id => id.id === status.id).length;

    // Calcula quantas vezes a habilidade já foi utilizada por todos os personagens
    let usesSkill = 0;

    this._specialSkillsService.models.forEach((model) => {
      if (model.listSpecialSkills.length > 0) {
        usesSkill += model.listSpecialSkills.filter(skill => skill.idStatusEffect === status.id).length;
      }
    });


    if (this.selectedCategory.category === "Ailment" || this.selectedCategory.category === "Chaos") {
      // Retorna a diferença (disponível - utilizado) ou a total se não foi usado
     return usesSkill > 0 ? qtdIds - usesSkill : qtdIds;
    } 
    else {
    // Retorna a diferença (disponível - utilizado) ou a posição se não foi usado
        return usesSkill > 0 ? qtdIds - usesSkill : status.positionID;
    }    
  }

  /**
   * Verifica se a categoria selecionada ainda pode ser adicionada (limite de 2 por categoria)
   * @returns true se pode adicionar, false se atingiu o limite
   */
  checkCategoryLimit(): boolean {
    // Validação de segurança
    if (!this.listSpecialSkills || !this.listSpecialSkills.listSpecialSkills) {
      console.error('Lista de skills não encontrada.');
      return false;
    }

    // Conta quantas habilidades da categoria selecionada já foram adicionadas
    const idCategoryCount = this.listSpecialSkills.listSpecialSkills.filter((skill) => skill.idCategory === this.selectedCategory?.id).length;

    // Verifica se excedeu o limite de 2 habilidades por categoria
    if (idCategoryCount >= 2) {
      console.error(`A categoria '${this.itemSelectedCategory}' já foi adicionada mais de 2 vezes.`);

      // Ativa animação de pulsação para feedback visual
      this.isPulsing = true;
      setTimeout(() => {
        this.isPulsing = false;
      }, 400);

      this.isStatusSelected = true;
      this.ref.detectChanges();
      return false;
    }

    // Se estiver dentro do limite, permite adicionar
    return true;
  }

  /**
   * Remove uma habilidade da lista de efeitos disponíveis
   * @param index - Índice da habilidade a ser removida
   */
  removeSkillList(index: number) {
    this.listDescriptionStatusEffect.splice(index, 1);
    this.ref.detectChanges();
  }

  /**
   * Remove uma habilidade da lista de habilidades especiais do personagem
   * Retorna a habilidade para a lista de disponíveis
   * @param index - Índice da habilidade a ser removida
   */
  returnSkillList(index: number) {
    // Remove a habilidade da lista do personagem
    this.listSpecialSkills.listSpecialSkills.splice(index, 1);
    this._specialSkillsService.svcToModify(this.listSpecialSkills);
    this._specialSkillsService.toSave();

    // Feedback visual com animação de pulsação
    this.isPulsing = true;
    setTimeout(() => {
      this.isPulsing = false;
    }, 400);

    // Limpa categorias e reinicializa o componente
    this.listCategory = [];
    this.ref.detectChanges();
    this.ngOnInit();
  }

  /**
   * Conta quantas vezes uma categoria aparece na lista até um determinado índice
   * @param category - Nome da categoria
   * @param index - Índice limite para contagem
   * @returns Número de ocorrências da categoria
   */
  getCategoryCount(category: string, index: number): number {
    const occurrences = this.listSpecialSkills.listSpecialSkills
      .slice(0, index + 1) // Considera apenas os elementos até o índice atual
      .filter(item => item.category === category).length; // Filtra os itens com a mesma categoria
    return occurrences;
  }

  /**
   * Navega de volta para a página anterior (others)
   */
  public onBack() {
    this._router.navigate(['others']);
  }

  /**
   * Ordena a lista de habilidades especiais por categoria em ordem alfabética
   */
  sortSpecialSkillsByCategory(): void {
    this.listSpecialSkills.listSpecialSkills.sort((a, b) => {
      const categoryA = (a.category || '').toLowerCase();
      const categoryB = (b.category || '').toLowerCase();
      if (categoryA < categoryB) {
        return -1;
      }
      if (categoryA > categoryB) {
        return 1;
      }
      return 0;
    });
  }

  /*
   * MÉTODOS ADICIONAIS NÃO DOCUMENTADOS INDIVIDUALMENTE:
   *
   * - getIdHealingDescription(): Carrega habilidades de healing (similar ao getIdBlocksDescription)
   * - getIdDefensiveDescription(): Carrega habilidades defensivas
   * - getIdNegativeDescription(): Carrega habilidades negativas
   * - getIdAilmentDescription(): Carrega habilidades de ailment
   * - getIdDispelDescription(): Carrega habilidades de dispel
   * - getIdHybridDescription(): Carrega habilidades híbridas (boost + negative)
   * - getIdComboDescription(): Carrega habilidades de combo
   * - removeStatusSelected(): Remove habilidades já selecionadas das listas disponíveis
   *
   * Todos seguem padrões similares aos métodos já documentados, adaptados para suas respectivas categorias.
   */

}