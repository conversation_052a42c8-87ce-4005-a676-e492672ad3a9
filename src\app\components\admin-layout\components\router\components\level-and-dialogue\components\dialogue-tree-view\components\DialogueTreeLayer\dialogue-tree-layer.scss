/* ========================================
   DIALOGUE TREE LAYER COMPONENT STYLES
   ======================================== */

/* Wrapper for option/dilemma leaf elements in horizontal layout */
.tree-leaf-wrapper {
  display: flex;
}

/* Individual leaf column styling with flexible width */
.tree-leaf-column {
  flex-grow: 1;
}

/* Roadblock-specific styling for proper spacing */
.tree-leaf-column.has-roadblocks {
   min-width: 815px !important;
}

