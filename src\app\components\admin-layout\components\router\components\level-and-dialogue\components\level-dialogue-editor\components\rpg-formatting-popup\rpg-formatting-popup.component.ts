import { RPGFormatting } from 'src/lib/darkcloud';
import { Component, EventEmitter, Output } from '@angular/core';

@Component({
  selector: 'app-rpg-formatting-popup',
  templateUrl: './rpg-formatting-popup.component.html',
})
export class RpgFormattingPopupComponent 
{
  RPGFormattingType = RPGFormatting.RPGType;
  @Output() applyformat: EventEmitter<RPGFormatting.RPGType> = new EventEmitter();
  
  onClick(typeClicked: RPGFormatting.RPGType) 
  {
    this.applyformat.emit(typeClicked);
  }
}
