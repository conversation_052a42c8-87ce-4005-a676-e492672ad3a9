import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { Base } from './Base';

export class StoryExpansionPkg extends Base<Data.Hard.IStoryExpansionPkg, Data.Result.IStoryExpansionPkg> implements Required<Data.Hard.IStoryExpansionPkg>
{
    private static generateId(index: number): string 
    {
        return IdPrefixes.STORY_EXPANSION_PKG + index;
    }

    constructor(
        index: number,
        level: string,
        dataAccess: StoryExpansionPkg['TDataAccess']
    ) {
        super(
        {
            hard: {
            id: StoryExpansionPkg.generateId(index),
            level,
            },
        },
        dataAccess
        );
    }
    
    protected getInternalFetch()
    {
        return {};
    }

    public get level(): string
    {
        return this.hard.level;
    }
    public set level(value: string)
    {
        this.hard.level = value;
    }

    public get name(): string
    {
        return this.hard.name;
    }
    public set name(value: string)
    {
        this.hard.name = value;
    }

    public get description(): string
    {
        return this.hard.description;
    }
    public set description(value: string)
    {
        this.hard.description = value;
    }

    public get note(): string
    {
        return this.hard.note;
    }
    public set note(value: string)
    {
        this.hard.note = value;
    }

    public get type(): number
    {
        return this.hard.type;
    }
    public set type(value: number)
    {
        this.hard.type = value;
    }

    public get area(): string
    {
        return this.hard.area;
    }
    public set area(value: string)
    {
        this.hard.area = value;
    }
    public get revisionCounterNameAI(): number 
    {
      return this.hard.revisionCounterNameAI;
    }
    public set revisionCounterNameAI(value: number) 
    {
      this.hard.revisionCounterNameAI = value;
    }
    public get revisionCounterDescriptionAI(): number 
    {
      return this.hard.revisionCounterDescriptionAI;
    }
    public set revisionCounterDescriptionAI(value: number) 
    {
      this.hard.revisionCounterDescriptionAI = value;
    } 
    public get isReviewedName(): boolean 
    {
      return this.hard.isReviewedName;
    }
    public set isReviewedName(value: boolean) 
    {
      this.hard.isReviewedName = value;
    } 
    public get isReviewedDescription(): boolean 
    {
      return this.hard.isReviewedDescription;
    }
    public set isReviewedDescription(value: boolean) 
    {
      this.hard.isReviewedDescription = value;
    } 
}