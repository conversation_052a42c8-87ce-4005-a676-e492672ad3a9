import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';

export class Option
  extends Base<Data.Hard.IOption, Data.Result.IOption>
  implements Required<Data.Hard.IOption> {
  public static generateId(optionBoxId: string, index?: number): string {
    return (
      optionBoxId +
      '.' +
      IdPrefixes.OPTION +
      (index !== undefined ? index : '_CANCEL')
    );
  }
  constructor(
    args: { index: number; optionBoxId: string; clonee?: Option },
    dataAccess: Option['TDataAccess']
  ) {
    const id = Option.generateId(args.optionBoxId, args.index);
    if (args.clonee != null) {
      super({ hard: args.clonee.hard, newId: id }, dataAccess);
    } else {
      super({ hard: { id } }, dataAccess);
    }
  }
  protected getInternalFetch() {
    return {};
  }
  public get answerBoxId(): string {
    return this.hard.answerBoxId;
  }
  public set answerBoxId(value: string) {
    this.hard.answerBoxId = value;
  }
  public get answerBoxNegativeId(): string {
    return this.hard.answerBoxNegativeId;
  }
  public set answerBoxNegativeId(value: string) {
    this.hard.answerBoxNegativeId = value;
  }
  public get isOmitted(): boolean {
    return this.hard.isOmitted;
  }
  public set isOmitted(value: boolean) {
    this.hard.isOmitted = value;
  }
  public get message(): string {
    return this.hard.message;
  }
  public set message(value: string) {
    this.hard.message = value;
  }
  public get weight(): number {
    return this.hard.weight ?? 0;
  }
  public set weight(value: number) {
    this.hard.weight = value;
  }
  public get label(): string {
    return this.hard.label;
  }
  public set label(value: string) {
    this.hard.label = value;
  }

  public get AndOrCondition(): string {
    return this.hard.AndOrCondition;
  }
  public set AndOrCondition(value: string) {
    this.hard.AndOrCondition = value;
  }
  public get choiceSpeech(): string {
    return this.hard.choiceSpeech;
  }
  public set choiceSpeech(value: string) {
    this.hard.choiceSpeech = value;
  }
  public get choiceAtributte(): string {
    return this.hard.choiceAtributte;
  }
  public set choiceAtributte(value: string) {
    this.hard.choiceAtributte = value;
  }
  public get choicePositive(): string {
    return this.hard.choicePositive;
  }
  public set choicePositive(value: string) {
    this.hard.choicePositive = value;
  }
  public get choiceNegative(): string {
    return this.hard.choiceNegative;
  }
  public set choiceNegative(value: string) {
    this.hard.choiceNegative = value;
  }
  public get choiceDifficulty(): string {
    return this.hard.choiceDifficulty;
  }
  public set choiceDifficulty(value: string) {
    this.hard.choiceDifficulty = value;
  }
  public get difficultClassValue(): string {
    return this.hard.difficultClassValue;
  }
  public set difficultClassValue(value: string) {
    this.hard.difficultClassValue = value;
  }
  public get classeNameOpponet(): string {
    return this.hard.classeNameOpponet;
  }
  public set classeNameOpponet(value: string) {
    this.hard.classeNameOpponet = value;
  }


  public get classModifierValue(): number {
    return this.hard.classModifierValue;
  }
  public set classModifierValue(value: number) {
    this.hard.classModifierValue = value;
  }
  public get resultDC(): number {
    return this.hard.resultDC;
  }
  public set resultDC(value: number) {
    this.hard.resultDC = value;
  }
  public get bl(): number {
    return this.hard.bl;
  }
  public set bl(value: number) {
    this.hard.bl = value;
  }

  public get labelAnswerNegative(): string {
    return this.hard.labelAnswerNegative;
  }
  public set labelAnswerNegative(value: string) {
    this.hard.labelAnswerNegative = value;
  }
  public get labelAnswerPositive(): string {
    return this.hard.labelAnswerPositive;
  }
  public set labelAnswerPositive(value: string) {
    this.hard.labelAnswerPositive = value;
  }
  public get nameKnowledge(): string {
    return this.hard.nameKnowledge;
  }
  public set nameKnowledge(value: string) {
    this.hard.nameKnowledge = value;
  }
  public get fatorSituationModifier(): string {
    return this.hard.fatorSituationModifier;
  }
  public set fatorSituationModifier(value: string) {
    this.hard.fatorSituationModifier = value;
  }
  public get valueSituationModifier(): number {
    return this.hard.valueSituationModifier;
  }
  public set valueSituationModifier(value: number) {
    this.hard.valueSituationModifier = value;
  }
  public get descriptionDCGuide(): string {
    return this.hard.descriptionDCGuide;
  }
  public set descriptionDCGuide(value: string) {
    this.hard.descriptionDCGuide = value;
  }
  public get subcontext(): string {
    return this.hard.subcontext;
  }
  public set subcontext(value: string) {
    this.hard.subcontext = value;
  }
  public get subContextDescription(): string {
    return this.hard.subContextDescription;
  }
  public set subContextDescription(value: string) {
    this.hard.subContextDescription = value;
  }
  public get investigaDifficulty(): string {
    return this.hard.investigaDifficulty;
  }
  public set investigaDifficulty(value: string) {
    this.hard.investigaDifficulty = value;
  }
  public get investigationPositive(): string {
    return this.hard.investigationPositive;
  }
  public set investigationPositive(value: string) {
    this.hard.investigationPositive = value;
  }  
  public get investigationNegative(): string {
    return this.hard.investigationNegative;
  }
  public set investigationNegative(value: string) {
    this.hard.investigationNegative = value;
  }
  public get descriptionInvestigation(): string {
    return this.hard.descriptionInvestigation;
  }
  public set descriptionInvestigation(value: string) {
    this.hard.descriptionInvestigation = value;
  }

  public get type(): number {
    return this.hard.type;
  }
  public set type(value: number) {
    this.hard.type = value;
  }
  public get descriptionSituationalModifier(): string {
    return this.hard.descriptionSituationalModifier;
  }
  public set descriptionSituationalModifier(value: string) {
    this.hard.descriptionSituationalModifier = value;
  }
  public get valueModifierITD(): number {
    return this.hard.valueModifierITD;
  }
  public set valueModifierITD(value: number) {
    this.hard.valueModifierITD = value;
  }
  public get isModMoon(): boolean {
    return this.hard.isModMoon;
  }
  public set isModMoon(value: boolean) {
    this.hard.isModMoon = value;
  }

}
