
<div class="card list-header" style="height: 70px; margin: 30px; margin-bottom: 0px;">
    <div class="header" style="display:flex; flex-direction:row; justify-content: space-between;">
        <div style="display:flex; flex-direction:row; justify-content: space-between; gap: 5px;">
            <button routerLink="/charactersSelector" class="{{!this.inThisPage ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                > Characters Selection </button>
            <button routerLink="/animationsSelector" class="{{this.inThisPage ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                >  Animations Selection </button>
            <button routerLink="/itemsSelector" class="{{!this.inThisPage ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
            >  Items Selection </button>
        </div>
    </div>
</div>


    <div class="main-content" >
        
        <div  style="margin-top:70px;" class="card">
            <app-header-with-buttons 
                [cardTitle]="'Selection Section'"
                [cardDescription]="description"
                [rightButtonTemplates]="[excelButtonTemplate, exportExcelButtonTemplate]"
                [isBackButtonEnabled]="false">
            </app-header-with-buttons>
           <app-header-search (inputKeyup)="search($event)" (searchOptions)="searchConditions($event)"></app-header-search>
          </div>

      
        <div class="card horizontal-scroll">
            <table class="table table-list card">
                <thead>
                <tr>
                    <th>ID</th>
                    <th class="th-clickable" (click)="sortByName('character')">Name</th>
                    <th class="th-clickable" (click)="sortByName('description')">Notes</th>
                    <th class="th-clickable" (click)="sortElements('finish')">Finish</th>
                    <th class="th-clickable" (click)="sortElements('finish')">REV1</th>
                    <th class="th-clickable" (click)="sortElements('finish')">REV2</th>
                </tr>
                </thead>
                <tbody>
                    <ng-container *ngFor="let animation of animationsSelectors; let i = index">
                    <tr *ngIf="animationsSelectors">
                    <td>
                        {{i}}
                    </td>
                    <td>
                        {{animation.character}}
                    </td>
                    <td>
                        <textarea placeholder=" " class="form-control"
                            type="text" #notes (change)="onChangeNotes(animation, notes.value)"
                            value="{{ animation.description }}"
                        ></textarea>
                    </td>
                    <td>
                        <input type="checkbox" [checked]="animation.finish" (click)="onChangeCheckbox(animation, 'finish')">
                    </td>
                    <td>
                        <input type="checkbox" class="inputt" [checked]="animation?.rev1" (click)="onChangeCheckbox(animation, 'rev1')">
                    </td>
                    <td>
                        <input type="checkbox" class="inputt"[checked]="animation?.rev2" (click)="onChangeCheckbox(animation, 'rev2')">
                    </td>
                    
                </tr>
            </ng-container>
         </tbody>
        </table>
        </div>   
    <ng-container *ngIf="loadingSpinner">
        <app-loading-spinner></app-loading-spinner>
    </ng-container>
<!--
       <ng-container *ngIf="!animationsSelectors.length">
        <div class="noAnimations">
          <p style="font-weight: 800;">
            <i class="pe-7s-attention"></i>
            No Animations Selection</p>
          <p>Check if I import the database correctly.</p>
        </div>
      </ng-container> 
-->


    </div>
    

