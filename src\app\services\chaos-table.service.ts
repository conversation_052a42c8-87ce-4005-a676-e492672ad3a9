import { Injectable } from '@angular/core';
import { IndexStorageService, ReviewService } from 'src/app/services';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { ModelService } from 'src/lib/darkcloud/angular/dsadmin';
import { ChaosTable } from '../lib/@bus-tier/models';


@Injectable({
  providedIn: 'root',
})
export class ChaosTableService extends  ModelService<ChaosTable> 
{

  public override svcPromptCreateNew(...args: any): Promise<ChaosTable> {
    throw new Error('Method not implemented.');
  }

  constructor(
    indexStorageService: IndexStorageService,
    readonly userSettingsService: UserSettingsService,
    reviewService: ReviewService,
  ) 
  {
    super(
      {
        defaultConstructor: () =>
          new ChaosTable(0, this.userSettingsService),
      },
      'ChaosTable',
      indexStorageService,
      reviewService,
    );
  }
  
  public async createNewnChaosTable(value: string[]) {

    if (value[0] != "") {
      let newChaosTable = new ChaosTable(this.svcNextIndex(), this.userSettingsService);
      newChaosTable.idChaosTable = value[0];
      newChaosTable.category = value[1];
      newChaosTable.statusEffectName = value[2];
      newChaosTable.description = value[3];  
      newChaosTable.target = value[4];
      newChaosTable.powerPoints = value[5];
      newChaosTable.allChaosTable = value[6];
      newChaosTable.duration = value[7];

      this.srvAdd(newChaosTable);
      return newChaosTable;
    } else {
      return null;
    }

  }
}
