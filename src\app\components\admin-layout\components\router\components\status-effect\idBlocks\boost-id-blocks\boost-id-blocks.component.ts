import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { Alert } from '../../../../../../../../../lib/darkcloud';
import { BoostIdBlocks } from '../../../../../../../../lib/@bus-tier/models';
import { Button } from '../../../../../../../../lib/@pres-tier/data';
import { BoostIdBlockservice } from '../../../../../../../../services';

@Component({
  selector: 'app-boost-id-blocks',
  templateUrl: './boost-id-blocks.component.html',
  styleUrls: ['./boost-id-blocks.component.scss']
})
export class BoostIdBlocksComponent  implements OnInit {

  titles = [1, 2, 3, 4, 5, 6];
  listBoost: BoostIdBlocks[] = [];
  activeLanguage = 'PTBR';
  @Output() activeTab2 = new EventEmitter<string>();

  public readonly excelButtonTemplate: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  constructor(
    private _boostIdBlockservice: BoostIdBlockservice
  ){}

    async ngOnInit(): Promise<void>{

      this.removeEmptyItems();
      this.listBoost = this._boostIdBlockservice.models;
    }
    removeEmptyItems() {
      this._boostIdBlockservice.toFinishLoading();    
      // Filtrar os modelos para remover aqueles onde todas as posições são ""
      this._boostIdBlockservice.models = this._boostIdBlockservice.models.filter(boostItem => {
        // Verificar se ao menos uma posição do item não é uma string vazia
        return this.titles.some((_, index) => boostItem.positionNameBoosts[index] !== "");
      });    

      this._boostIdBlockservice.toSave();      
    }

    async onExcelPaste() {
      const text = await navigator.clipboard.readText();
      const lines = text.split(/\r?\n/).filter(line => line);    
      const processedData: string[][] = [];
    
      if (lines.length > 0) {
        lines.forEach(line => {
          // Divide cada linha em colunas e remove a primeira coluna
          const values = line.split("\t").map(value => value.trim()).slice(1);
    
          processedData.push(values);
        });
    
        // Verifica se o número de colunas em cada linha corresponde ao número de colunas em this.titles
        const isColumnCountValid = processedData.every(row => row.length === this.titles.length);
    
        if (!isColumnCountValid) {
          Alert.showError('Invalid number of columns');
          return;
        }
  
        this._boostIdBlockservice.models = [];
        this._boostIdBlockservice.toSave();
    
        for (let index = 0; index < processedData.length; index++) {
          this._boostIdBlockservice.createNewBoostIdBlocks(processedData[index]);
        }    

        Alert.ShowSuccess('Boost imported successfully!');
        this.activeTab2.emit('boost');
        this.ngOnInit();
      }
    }
    
 
    changeBoost(rowIndex: number, colIndex: number, newValue: string){
      if (this.listBoost[rowIndex]) {
        this.listBoost[rowIndex].positionNameBoosts[colIndex] = newValue;
        this._boostIdBlockservice.svcToModify(this.listBoost[rowIndex]);
      }
    }
    
 
}
