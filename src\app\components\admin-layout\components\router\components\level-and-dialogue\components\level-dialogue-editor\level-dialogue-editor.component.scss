#leftbox {
  float:left; 
}
#middlebox{
  float:left; 
}
#rightbox{
  float:right;
}

.popup-tree
{
  position: fixed;
  height: 100%;
  width: 100%;
  background-color: hsla(0, 0%, 15%, 0.9);
  z-index: 149;

  margin: auto;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin-left: auto;
  margin-right: auto;
  margin-top: auto;
  margin-bottom: auto;
  padding-left: 50px;
  padding-right: 50px;

  text-align: center;
}

.popup-simulator
{
    position: fixed;
    height: 100%;
    width: 100%;
    background-color: hsla(0, 0%, 15%, 0.9);
    z-index: 150;

    margin: auto;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin-left: auto;
    margin-right: auto;
    margin-top: auto;
    margin-bottom: auto;

    text-align: center;
}

.bg {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1;
  background: url('https://images.unsplash.com/photo-1451186859696-371d9477be93?crop=entropy&fit=crop&fm=jpg&h=975&ixjsv=2.1.0&ixlib=rb-0.3.5&q=80&w=1925');
  background-size:     cover;                      /* <------ */
  background-repeat:   no-repeat;
  background-position: center center;              /* optional, center the image */
  filter: blur(50px) grayscale(60%);
  transform: scale(1.2);
}
input[type=text]	
{	
    border: none;	
    height: 35px;	
    width: 550px;	
    margin-left: 5px;	
}	
.input-custom-box	
{	
    display: flex;	
    flex-direction: row;	
    justify-content: space-around;	
    align-items: center;	
    border: 1px solid #999;	
    border-radius: 5px;	
    height: 40px;	
    width: 600px;	
    margin-left: 15px;	
}	
.input-custom-box:hover	
{	
    border: 0.5px solid #3472f7;	
    height: 40px;	
    width: 600px;	
}	
/* .i-btn{	
    display: flex;	
    font-family: inherit;	
    font-size: inherit;	
    line-height: inherit;	
    border-radius: 50px;	
    width: 30px;	
    height: 30px;	
    align-content: center;	
    justify-content: center;	
    align-items: center;	
    flex-direction: row;	
    flex-wrap: nowrap;	
    background-color: transparent;	
    border-color: transparent;	
    outline: none;	
}	
		
.i-btn:active, .i-btn:hover, .i-btn:focus-visible	
{	
    outline: none;	
    background-color: #d6d6d6;	
} */