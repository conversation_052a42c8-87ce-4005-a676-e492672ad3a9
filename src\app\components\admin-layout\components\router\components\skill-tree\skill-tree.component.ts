import { Component } from '@angular/core';
import { Button } from 'src/app/lib/@pres-tier/data';
import { CastsGoldenService, CastsSigilosService, CastsSoulsService } from 'src/app/services';
import { FILTER_SUFFIX_PATH } from 'src/lib/darkcloud/angular/dsadmin/constants/others';

@Component({
  selector: 'app-skill-tree',
  templateUrl: './skill-tree.component.html',
  styleUrls: ['./skill-tree.component.scss']
})
export class SkillTreeComponent {

  public activeTab: string;
  public activeTab2: string;
  activeLanguage = 'PTBR';
  listExcel: string[] = [];
  description = '';
  title = '';

    public readonly excelButtonTemplate: Button.Templateable = {
      title: 'Paste content from excel',
      onClick: this.onExcelPaste.bind(this),
      iconClass: 'excel-icon',
      btnClass: Button.Klasses.FILL_ORANGE,
    };
  
  constructor(
    private _castsGoldenService: CastsGoldenService,
     private _castsSoulsService: CastsSoulsService,
     private _castsSigilosService: CastsSigilosService
  ) {}

    ngOnInit(): void 
    {
     //  const tab = localStorage.getItem(`tab-SkillTreeComponent${FILTER_SUFFIX_PATH}`);
     // this.activeTab = tab === 'null' || !tab ? 'costs' : tab;    
      this.activeTab = 'costs';       
      this.activeTab2 = 'golden';
      this.getContextGolden();
      localStorage.setItem(`tab-SkillTreeComponent${FILTER_SUFFIX_PATH}`, this.activeTab2)
    }
  
    public switchToTab(tab: string) 
    {
      this.activeTab = tab;
      localStorage.setItem(`tab-SkillTreeComponent${FILTER_SUFFIX_PATH}`, this.activeTab);
    }

    public switchToTab2(tab: string) {  
      this.activeTab2 = tab;

      if (tab === 'golden') {
        this.getContextGolden();
      } 
     else if (tab === 'souls') {
        this.title = 'Souls';
        this.description = `Showing ${this._castsSoulsService.models.length} results`;
      } 
      else {
        this.title = 'Sigilos';
        this.description = `Showing ${this._castsSigilosService.models.length} results`;
      }

      localStorage.setItem(`tab-SkillTreeComponent${FILTER_SUFFIX_PATH}`, this.activeTab2);
    }

    getContextGolden() {
      this.title = 'Golden';
      this.description = `Showing ${this._castsGoldenService.models.length} results`;
    }

    async onExcelPaste() {
      const text = await navigator.clipboard.readText();
      const lines = text.split(/\r?\n/).filter((line) => line);
      this.listExcel = lines;
    }

}
