<div class="main-content fixed-height">
  <div class="container-fluid">
    <button class="btn btn-primary btn-sm {{isViewingIgnored ? 'btn-fill' : ''}}"
      (click)="isViewingIgnored = !isViewingIgnored">
      <i class="pe-7s-moon"></i>
      {{ isViewingIgnored ? 'Viewing ignored' : 'View ignored'}}
    </button>

    <div class="content">
      <div class="card">
        <ng-container *ngIf="
            reviewWithErrors.length === 0 &&
            levelIdsWithErrors.length === 0 &&
            missionIdsWithErrors.length === 0
          ">
          <i class="no-error-check pe-7s-check"></i>
        </ng-container>

        <div class="row">

          <!-- START: Events Section -->
          <div class="col-md-4">
            <h3>Dialogue</h3>
            <table class="table report-table table-clickable" style="height: 85vh;">
              <thead class="sticky">
                <tr>
                  <th>
                    Type ({{ reviewWithErrors.length }})
                  </th>
                  <th>Level</th>
                  <th>Missing</th>
                  <th class="center">Ignore</th>
                </tr>
              </thead>
              <tbody>
                <ng-container *ngFor="let dialog of reviewWithErrors; let i = index">
                  <tr [title]="dialog.id">
                    <!-- TYPE -->
                    <td (click)="accessSPO(dialog.id)" class="td-40px" [ngStyle]="{color: dialog.typeColor}">
                      {{dialog.typeName}}
                    </td>
                    <!-- LEVEL -->
                    <td (click)="accessSPO(dialog.id)" [ngStyle]="dialog.typeColor === 'red'? {color: 'red'} : {}">{{
                      dialog.namePosition }}</td>
                    <!-- MISSING -->
                    <td (click)="accessSPO(dialog.id)" class="td-min">
                      <ng-container *ngFor="let param of dialog.parametersWithErrors">
                        <i style="position: relative" placement='top' delay='250' ttWidth="auto" ttAlign="center"
                          ttPadding="10px" tooltip="Empty {{ param }}" class="pe-7s-close-circle error pull-right"></i>
                      </ng-container>
                    </td>
                    <!-- IGNORE -->
                    <td class="td-small">
                      <button [class]="isViewingIgnored | reviewIgnoreButtonClass"
                        (click)="ignoreReview(dialog.id, !isViewingIgnored)">
                        <i [class]="isViewingIgnored | reviewIgnoreButtonIcon"></i>
                      </button>
                    </td>
                  </tr>
                </ng-container>
              </tbody>
            </table>
          </div>
          <!-- END: Events Section -->

          <!-- START: Mission Dialogue Section -->
          <div class="col-md-4">
            <h3>Levels</h3>
            <table class="table report-table table-clickable" style="height: 85vh;">
              <thead class="sticky" style="top: 0px;">
                <tr>
                  <th></th>
                  <th>Level ({{ levelIdsWithErrors.length }})</th>
                  <th>Mission Dialogue</th>
                  <th>Links</th>
                  <th>Battle</th>
                  <th class="center">Ignore</th>
                </tr>
              </thead>
              <tbody>
                <ng-container *ngFor="let levelId of levelIdsWithErrors">
                  <tr [title]="levelId">
                    <td (click)="accessLevel(levelId)" class="td-20px">
                      <div class="circle" [ngStyle]="{
                          'background-color': levelId | level | typeColor
                        }"></div>
                    </td>
                    <td (click)="accessLevel(levelId)">{{([levelId] | location).length > 0 ? ([levelId] | location) : 'Ophan'}}<i *ngIf="
                      +(levelId | level)?.type !== 3 &&
                      !(levelId | level)?.sceneryId" class="pe-7s-photo warning pull-right" style="position: relative"
                        placement='top' delay='250' ttWidth="auto" ttAlign="center" ttPadding="10px"
                        tooltip="No scenery"></i>
                    </td>
                    <td (click)="accessLevel(levelId)" class="td-min">
                      <i style="position: relative" placement='top' delay='250' ttWidth="auto" ttAlign="center"
                        ttPadding="10px" tooltip="Mission dialogue not unlocked" *ngIf="
                          reviewResults[levelId]?.missionDialogueNotYetUnlocked
                        " class="pe-7s-close-circle error pull-right"></i>
                    </td>
                    <td (click)="accessLevel(levelId)" class="td-min">
                      <i *ngIf="!reviewResults[levelId]?.linkListInOrder" style="position: relative" placement='top'
                        delay='250' ttWidth="auto" ttAlign="center" ttPadding="10px" tooltip="Exit link: NOT EXPECTED"
                        class="pe-7s-close-circle error pull-right"></i>
                      <i *ngIf="
                          reviewResults[levelId]?.isTransition_noLinksToOtherArea
                        " style="position: relative" placement='top' delay='250' ttWidth="auto" ttAlign="center"
                        ttPadding="10px" tooltip="Transition: DOES NOT LINK TO OTHER AREA"
                        class="pe-7s-close-circle error pull-right"></i>
                      <i *ngIf="
                          reviewResults[levelId]?.notTransition_linksToOtherArea
                        " style="position: relative" placement='top' delay='250' ttWidth="auto" ttAlign="center"
                        ttPadding="10px" tooltip="Not Transition: LINKS TO OTHER AREA"
                        class="pe-7s-close-circle error pull-right"></i>
                    </td>
                    <td (click)="accessLevel(levelId)" class="td-min">
                      <i style="position: relative" placement='top' delay='250' ttWidth="auto" ttAlign="center"
                        ttPadding="10px" tooltip="No battle characters"
                        *ngIf="reviewResults[levelId]?.noBattleCharacters"
                        class="pe-7s-attention warning pull-right"></i>
                    </td>
                    <td class="td-small">
                      <button [class]="isViewingIgnored | reviewIgnoreButtonClass"
                        (click)="ignoreReview(levelId, !isViewingIgnored)">
                        <i [class]="isViewingIgnored | reviewIgnoreButtonIcon"></i>
                      </button>
                    </td>
                  </tr>
                </ng-container>
              </tbody>
            </table>
          </div>
          <!-- END: Mission Dialogue Section -->

          <!-- START: Mission Section -->
          <div class="col-md-4">
            <h3>Missions</h3>
            <table class="table report-table table-clickable" style="height: 85vh;">
              <thead class="sticky" style="top: 0px;">
                <tr>
                  <th>Mission ({{ missionIdsWithErrors.length }})</th>
                  <th>Notes</th>
                  <th>Errors</th>
                  <th class="center">Ignore</th>
                </tr>
              </thead>
              <tbody>
                <ng-container *ngFor="let missionId of missionIdsWithErrors">
                  <tr [title]="missionId">
                    <td (click)="accessMission(missionId)">{{ (missionId | mission)?.name }}</td>
                    <td (click)="accessMission(missionId)" class="td-auto category">
                      {{ (missionId | mission | information)?.authorNotes }}
                    </td>
                    <td (click)="accessMission(missionId)" class="td-min">
                      <i style="position: relative" placement='top' delay='250' ttWidth="auto" ttAlign="center"
                        ttPadding="10px" tooltip="Not assigned"
                        *ngIf="reviewResults[missionId]?.assignedAt.length === 0"
                        class="pe-7s-close-circle error pull-right"></i>
                      <i style="position: relative" placement='top' delay='250' ttWidth="auto" ttAlign="center"
                        ttPadding="10px" tooltip="Assigned multiple times"
                        *ngIf="reviewResults[missionId]?.assignedAt.length > 1"
                        class="pe-7s-attention warning pull-right"></i>
                      <i style="position: relative" placement='top' delay='250' ttWidth="auto" ttAlign="center"
                        ttPadding="10px" tooltip="No objectives"
                        *ngIf="(missionId | mission)?.objectiveIds.length === 0"
                        class="pe-7s-close-circle warning pull-right"></i>
                      <i style="position: relative" placement='top' delay='250' ttWidth="auto" ttAlign="center"
                        ttPadding="10px" tooltip="Problems with Objectives"
                        *ngIf="!reviewResults[missionId]?.objectivesCompleted"
                        class="pe-7s-close-circle error pull-right"></i>
                    </td>
                    <td class="td-small">
                      <button [class]="isViewingIgnored | reviewIgnoreButtonClass"
                        (click)="ignoreReview(missionId, !isViewingIgnored)">
                        <i [class]="isViewingIgnored | reviewIgnoreButtonIcon"></i>
                      </button>
                    </td>
                  </tr>
                </ng-container>
              </tbody>
            </table>
          </div>
          <!-- END: Mission Section -->
        </div>
      </div>
    </div>
  </div>
</div>

<ng-container *ngIf="microloopIdsWithErrors.length > 0">
  <div class="col-md-4">
    <h3>Microloops</h3>
    <table class="table report-table table-clickable" style="height: 85vh;">
      <thead class="sticky" style="top: 0px;">
        <tr>
          <th>ID</th>
          <th>Loop ({{ microloopIdsWithErrors.length }})</th>
          <th>Battle</th>
          <th>Speakers</th>
          <th class="center">Ignore</th>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngFor="let levelId of microloopIdsWithErrors">
          <tr [title]="levelId">
            <td (click)="accessMicroloop(levelId)" class="td-20px">
              <div class="circle" [ngStyle]="{'background-color': levelId | level | typeColor}"></div>
            </td>
            <td (click)="accessMicroloop(levelId)">
              {{ (levelId | microloop)?.name }}
            </td>
            <td (click)="accessMicroloop(levelId)">
              <i style="position: relative" placement='top' delay='250' ttWidth="auto" ttAlign="center" ttPadding="10px"
                tooltip="No battle characters" *ngIf="reviewResults[levelId]?.noBattleCharacters"
                class="pe-7s-close-circle error"></i>
            </td>
            <td (click)="accessMicroloop(levelId)">
              <i style="position: relative" placement='top' delay='250' ttWidth="auto" ttAlign="center" ttPadding="10px"
                tooltip="No speakers" *ngIf="reviewResults[levelId]?.asSpeaker?.length == 0"
                class="pe-7s-close-circle error"></i>
            </td>
            <td class="td-small">
              <button [class]="isViewingIgnored | reviewIgnoreButtonClass"
                (click)="ignoreReview(levelId, !isViewingIgnored)">
                <i [class]="isViewingIgnored | reviewIgnoreButtonIcon"></i>
              </button>
          </tr>
        </ng-container>
      </tbody>
    </table>
  </div>
</ng-container>