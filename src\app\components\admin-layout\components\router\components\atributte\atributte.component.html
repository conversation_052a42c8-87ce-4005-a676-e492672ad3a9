<ng-container *ngIf="activeTab != 'Subcontext'">
  <div class="main-menu-efect">
    <div class="container-fluid">
      <!--Header-->
      <div class="list-header-row update">
        <div class="card" style="padding-bottom: 5px;">
          <app-header-with-buttons [cardTitle]="listName" [cardDescription]="description"
            [rightButtonTemplates]="[statusTemplate]" [btnSubContext]="true" (clickBtn)="clickBtn($event)"
            [buttonLabel]="'Subcontext'">
          </app-header-with-buttons>
        </div>
      </div>
      <!--List-->
      <div class="card">
        <table class="table table-list">
          <thead class="sticky">
            <tr>
              <th class="th-clickable">Index</th>
              <th class="th-clickable" (click)="sortBySkill()">Atributte
                <div class="ball-circle"></div>
              </th>
              <th class="th-clickable" (click)="sortByAcronym()" style="width: 3%;">Acronym</th>
              <th>Description
                <div class="ball-circle"></div>
              </th>
              <th>Negative(-)</th>
              <th>Positivo(+)</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor=" let atr of this.atributtoClasses; let i = index; trackBy: trackById">
              <tr id="{{ atr.id }}">
                <td class="td-sort">{{ i + 1 }}</td>
                <!--Atributte-->
                <td class="td-notes" style="width:25%">
                  <input class="form-control form-short " type="text"
                    value="{{ (atr | translation : lstLanguage : atr.id : 'atributte') }}" #atributte
                    [ngStyle]="{'background-color': +atributte.value <= 0 ? '#404040' : '#ffffff'}"
                    (change)="atributteChange(atr, 'atributte', atributte.value)" />
                </td>
                <!--ACRONYM-->
                <td class="td-notes" style="width: 5%;">
                  <input class="form-control form-short" type="text" value="{{ atr.acronym }}" #acronym
                    [ngStyle]="{'background-color': +acronym.value <= 0 ? '#404040' : '#ffffff'}"
                    (change)="lstOnChange(atr, 'acronym', acronym.value)" />
                </td>
                <!--DESCRIPTION-->
                <td class="td-notes" style="text-align:right; width:25%">
                  <textarea class="form-control" style="height:50px" type="text"
                    value="{{ (atr | translation : lstLanguage : atr.id : 'description') }}" #description
                    [ngStyle]="{'background-color': +description.value <= 0 ? '#404040' : '#ffffff'}"
                    (change)="descriptionChange(atr, 'description', description.value)">
                    </textarea>
                </td>
                <!--Negative(-)-->
                <td class="td-notes" style="text-align:right; width:20%">
                  <input class="form-control form-short" type="text" value="{{ atr.negative }}" #negative
                    [ngStyle]="{'background-color': +negative.value <= 0 ? '#404040' : '#ffffff'}"
                    (change)="lstOnChange(atr, 'negative', negative.value)" />
                </td>
                <!--Positivo(+)-->
                <td class="td-notes" style="text-align:right; width:20%">
                  <input class="form-control form-short" type="text" value="{{ atr.positive }}" #positive
                    [ngStyle]="{'background-color': +positive.value <= 0 ? '#404040' : '#ffffff'}"
                    (change)="lstOnChange(atr, 'positive', positive.value)" />
                </td>
                <!--ACTIONS-->
                <td class="td-actions">
                  <button class="btn btn-danger btn-fill btn-remove" (click)="removeElement(atr)">
                    <i class="pe-7s-close"></i>
                  </button><br>
                  <button class="btn btn-gray btn-fill translation-button" (click)="getModifierOrtography(atr)">
                    <div class="mat-translate"></div>
                  </button>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</ng-container>


<ng-container *ngIf="activeTab === 'Subcontext'">
  <app-sub-context (clickBtnAtributte)="clickBtnAtributteHandler($event)"></app-sub-context>
</ng-container>