import { Component, OnInit } from '@angular/core';
import { RoadBlock } from 'src/app/lib/@bus-tier/models/RoadBlock';
import { AreaService, OptionService, StoryBoxService } from 'src/app/services';
import { MicroloopService } from 'src/app/services/microloop.service';
import { RoadBlockService } from 'src/app/services/road-block.service';
import { EasyMVC } from 'src/lib/darkcloud/angular';
import { SortingService } from 'src/app/services/sorting.service';
import { RoadBlockType } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard';
import { ActivatedRoute, Router } from '@angular/router';

interface Preloadable 
{
  pinDialogueMarkers: RoadBlock[];
}

interface TempProgressCondition
{
  color: Color,
  roadblock: RoadBlock
}

interface Color
{
  r: number,
  g: number,
  b: number
}

@Component({
  selector: 'app-progress-condition-report',
  templateUrl: './progress-condition-report.component.html',
  styleUrls: ['./progress-condition-report.component.scss']
})
export class ProgressConditionReportComponent extends EasyMVC.PreloadComponent<Preloadable> implements OnInit 
{

  public progressConditions: RoadBlock[] = [];
  auxProgressCondition: string[] = [];//This is to filter the progress condition array
  tempProgressCondition: TempProgressCondition[] = [];//This is to display the sorted RB. Sorted by place.
  auxColorArray: Color[] = [];
  reverseCondition: boolean = true;	
  reverseLocation: boolean = false;	
  reverseType: boolean = true;
  roadblockType = 5;//SPOKE_IN
  constructor(
    private _activatedRoute: ActivatedRoute,
    public readonly router: Router,
    public readonly areaService: AreaService,
    private _roadblockService: RoadBlockService,
    private _microloopService: MicroloopService,	
    private _sorting : SortingService,

    private _optionService: OptionService,
    private _storyBoxService: StoryBoxService,
  )
  {
    super(_activatedRoute, (data) => data.preloadedRoadblocks)
  }


  ngOnInit(): void
  {
   // Alert.showLoadingPleaseWiat(1000);
    this.progressConditions = this.fixData(this._roadblockService.models);  
    this._roadblockService.getDialogueId(this.progressConditions[0]?.StoryBoxId);   
    this.updateLabelOption();
    this.sortPlacesOfRoadblocks();
    this.sortArrayByLocation(this.tempProgressCondition);

  }

updateLabelOption() {
  const optionIndex = {};
  this._optionService.models.forEach((option) => {
    optionIndex[option.answerBoxId] = option;
  });

  this._storyBoxService.models.forEach((storyBox) => {
    const option = optionIndex[storyBox.id];
    if (option && option.label !== undefined && storyBox.labelOption === undefined) {
      storyBox.labelOption = option?.labelAnswerPositive || option?.label;
      this._storyBoxService.svcToModify(storyBox);
    }
  });
}

  //Group the RB that are in the same BOX.
sortPlacesOfRoadblocks() {
  let canPutFirstIElement = true;

  for (const progressCondition of this.progressConditions) {
    const color = this.pickNonRepeatedColor();
    const isRepeated = this.auxProgressCondition.includes(progressCondition.StoryBoxId);

    if (isRepeated) {
      canPutFirstIElement = true;
    }

    for (const otherProgressCondition of this.progressConditions) {
      if (otherProgressCondition.StoryBoxId === progressCondition.StoryBoxId && otherProgressCondition !== progressCondition) {
        this.auxProgressCondition.push(otherProgressCondition.StoryBoxId);
        this.tempProgressCondition.push({ color, roadblock: otherProgressCondition });

        if (canPutFirstIElement) {
          this.auxProgressCondition.push(progressCondition.StoryBoxId);
          this.tempProgressCondition.push({ color, roadblock: progressCondition });
        }
        canPutFirstIElement = false;
      }
    }

    if (!this.auxProgressCondition.includes(progressCondition.StoryBoxId)) {
      this.auxProgressCondition.push(progressCondition.StoryBoxId);
      this.tempProgressCondition.push({ color: undefined, roadblock: progressCondition });
    }
  }

  //Remove duplicados
  this.tempProgressCondition = this.tempProgressCondition.filter((tempProgressCondition, index, self) => {
    return self.findIndex((t) => t.roadblock.id === tempProgressCondition.roadblock.id) === index;
  });

}


  CheckConditionType(block: RoadBlock){
    if (block.Type == +RoadBlockType.SPOKE_IN && block.spokeElementId === undefined) {
      return true;
    } else {
      return false;
    }
  }

  rgb(color: Color):string
  {
    return 'rgb(' + color.r + ',' + color.g + ',' + color.b + ')';
  }

  generateRandomColor(): Color
  {
    let color: Color = 
    {
      r: Math.round(Math.random() * 255),
      g: Math.round(Math.random() * 255),
      b: Math.round(Math.random() * 255)
    }
    return color;
  }

  pickNonRepeatedColor() : Color
  {
    let color: Color = this.generateRandomColor();
    
    if(this.auxColorArray.includes(color)) this.pickNonRepeatedColor();
    this.auxColorArray.push(color);

    return color;
  }

  fixData(models: RoadBlock[]): RoadBlock[] 
  {
    return models.filter(rb => 
    {
      if(rb.id.includes('ML'))
      {
        const splits = rb.id.split('.');
        const containerId = splits[0];
        const loop = this._microloopService.svcFindById(splits[0]+"."+splits[1]);
        if(!loop)
        {
          this._microloopService.svcToRemove(rb.id);
          return null;
        }
      }
      return rb;
    });
  }

  access(id: string)
  {
    let levelId = this._roadblockService.getLevelId(id);
    let dialogueId = this._roadblockService.getDialogueId(id);

    if(id.split(".")[0].includes("ML"))
    {
      this.router.navigate([
        'microloops/' + levelId +
        '/dialogues/' + dialogueId
      ],
      {fragment: id});
    }
    else
    {
      this.router.navigate([
        'levels/' + levelId +
        '/dialogues/' + dialogueId
      ],
      {fragment: id});
    }
  }

  	
  sortArrayByCondition(array: TempProgressCondition[]) {	
    array.sort((a, b) => {	
        return this._sorting.sortArrayByAlphanumericValue(this.reverseCondition, a.roadblock.hard.itemId, b.roadblock.hard.itemId)	
    });	
    this.reverseCondition = !this.reverseCondition;	
  }	
  	
  sortArrayByLocation(array: TempProgressCondition[]) {	
    array.sort((a, b) => {	
      return this._sorting.sortArrayByLevelId(this.reverseLocation, a.roadblock.ID, b.roadblock.ID)	
    });	
    this.reverseLocation = !this.reverseLocation;	
  }	
  	
  sortArrayByType(array: TempProgressCondition[]) {	
    array.sort((a, b) => {	
      return this.reverseType! ? b.roadblock.hard.type - a.roadblock.hard.type : a.roadblock.hard.type - b.roadblock.hard.type;	
    });	
    this.reverseType = !this.reverseType;	
  }	
}
