<div class="row">
  <div class="col-md-12">
    <div class="report-table-wrapper">
      <table class="table report-table table-striped">
        <thead class="sticky"
               style="top: 0px;">
          <tr>
            <th></th>
            <th class="th-clickable center"
                (click)="sortsByParameter('hierarchyCode')">Level</th>
            <th class="th-clickable center"
                (click)="sortsByParameter('removeSpecialItem')">On Grind</th>
            <th class="th-clickable center special-item"
                (click)="sortsByParameter('specialItemOnGrindMarker')">Remove Special Item On Grind Marker</th>
            <th class="th-clickable center special-item"
                (click)="sortsByParameter('specialItemMarker')">Remove Special Item Marker</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let level of levels; let i = index">
            <tr *ngIf="level.removeSpecialItemBeforeGrind || specialItemRemovalMarkersByLevels.length > 0">
              <td class="td-20px">
                <div class="circle"
                     [ngStyle]="{
                  'background-color': level | typeColor
                }"></div>
              </td>
              <td class="td-50 td-clickable"
                (click)="accessLevel(level)">
                {{ [level.id] | location }}
                {{([level?.id] | location).length > 0 ? ([level.id] | location) : 'Ophan'}}
              </td>
              <td class="td-auto center  td-clickable">
              <i  
                  *ngIf="levelEventIds.includes(level.id) && levelEventIds.length > 0"
                  (click)="accessDialogues(level)"
                  class="btn-icon pe-7s-scissors"></i>
              </td>
              <td class="special-item">
                  <ng-container *ngFor="let event of specialItemRemovalMarkersByLevels;">
                    <tr class="tr-clickable" style="width: 100%;"
                        *ngIf="event.id.split('.')[0]+'.'+event.id.split('.')[1] == level.id && specialItemRemovalMarkersByLevels.length > 0"
                        (click)="accessDialogue(event)"> 
                        {{ event.id }}
                    </tr>
                  </ng-container>
              </td>
              <td class="special-item">
                  <ng-container *ngFor="let event of specialItemRemovalMarkers;">
                    <tr class="tr-clickable" style="width: 100%;"
                        *ngIf="event.id.split('.')[0]+'.'+event.id.split('.')[1] == level.id && specialItemRemovalMarkers.length > 0"
                        (click)="accessDialogue(event)"> 
                        {{ event.id }}
                    </tr>
                  </ng-container>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>
