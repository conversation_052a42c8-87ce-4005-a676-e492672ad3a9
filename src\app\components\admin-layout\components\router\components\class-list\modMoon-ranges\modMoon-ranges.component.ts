import { ChangeDetectorRef, Component } from '@angular/core';
import { Knowledge, ModMoonRanges } from 'src/app/lib/@bus-tier/models';
import { Atributte } from 'src/app/lib/@bus-tier/models/Atributte';
import { Button } from 'src/app/lib/@pres-tier/data';
import { KnowledgeService, ModMoonRangesService } from 'src/app/services';
import { AtributteService } from 'src/app/services/atributte.service';
import { Alert } from 'src/lib/darkcloud';

// Importa as interfaces dos tipos
type KnowledgeValue = { id: string; value: string; };
type AttributeValue = { id: string; value: string; };

@Component({
  selector: 'app-modmoon-ranges',
  templateUrl: './modMoon-ranges.component.html',
  styleUrls: ['./modMoon-ranges.component.scss']
})
export class ModMoonRangesComponent {

  description: string;
  activeLanguage = 'PTBR';
  listModMoonRanges: ModMoonRanges[] = [];
  listAttributte: Atributte[] = [];
  listKnowledge: Knowledge[] = [];
  titlesSuperior = ['Fase da Lua', 'Nomenclatura Técnica', 'DC KNOWLEDGE', 'Dano', 'DC ATTRIBUTE'];



  listMoonPhases = [
    {moon: '🌑 Nova', momenclatura: 'LUA_NOVA'},
    {moon: '🌒 Crescente Iluminante', momenclatura: 'LUA_CRESCENTE_ILUMINANTE'},
    {moon: '🌓 Quarto Crescente', momenclatura: 'LUA_QUARTO_CRESCENTE'},
    {moon: '🌔 Gibosa Crescente', momenclatura: 'LUA_GIBOSA_CRESCENTE'},
    {moon: '🌕 Lua Cheia', momenclatura: 'LUA_CHEIA'},
    {moon: '🌖 Gibosa Minguante', momenclatura: 'LUA_GIBOSA_MINGUANTE'},
    {moon: '🌗 Quarto Minguante', momenclatura: 'LUA_QUARTO_MINGUANTE'},
    {moon: '🌘 Minguante Iluminada', momenclatura: 'LUA_MINGUANTE_ILUMINADA'}
  ];
  subTitles = [];


  constructor(
     private _atributteService: AtributteService,
     private _knowledgeService: KnowledgeService,
     private change: ChangeDetectorRef,
     private _modMoonRangesService: ModMoonRangesService,

 ) { }

   public readonly excelButtonTemplate: Button.Templateable = {
     title: 'Paste content from excel',
     onClick: this.onExcelPaste.bind(this),
     iconClass: 'excel-icon',
     btnClass: Button.Klasses.FILL_ORANGE,
   }; 

 ngOnInit(): void {
    this._atributteService.toFinishLoading();
    this._knowledgeService.toFinishLoading();
    this._modMoonRangesService.toFinishLoading(); 
    this.listAttributte = this._atributteService.models;
    this.listKnowledge = this._knowledgeService.models;   

    setTimeout(() => {
      this.listModMoonRanges = this._modMoonRangesService.models;
      this.description = `Showing ${this.listModMoonRanges.length} results`; 
    }, 100);
 }


 createdOrderTitle() {
    this.subTitles = [];

    this.listKnowledge.forEach(knowledge => {
      this.subTitles.push(knowledge.knowledge);
    });
    this.subTitles.push('MOD Dano Party', 'MOD Dano Oponente');

    this.listAttributte.forEach(atributte => {
      this.subTitles.push(atributte.atributte);
    });
 }

  async onExcelPaste() {
     try {
       const text = await navigator.clipboard.readText();
       const lines = text.split(/\r?\n/).filter((line) => line);

       this._atributteService.toFinishLoading();
       this._knowledgeService.toFinishLoading();

       if (lines.length === 0) {
         Alert.showError('No data found in clipboard.');
         return;
       }

       // Limpa a lista atual
       this._modMoonRangesService.models = [];
       this.listModMoonRanges = [];
       this._modMoonRangesService.toSave();

       // VALIDAÇÃO DA PRIMEIRA LINHA (CABEÇALHO)
       const headerValues = lines[0].split('\t');

       // 1) Validação do número de colunas do cabeçalho
       const expectedColumns = this.listKnowledge.length + 2 + this.listAttributte.length; // Knowledge + 2 Danos + Attributes

       if (headerValues.length !== expectedColumns) {
         Alert.showError(`Error: Number of columns in header does not match expected (${expectedColumns}). Found: ${headerValues.length}`);
         return;
       }

       // Armazena a ordem dos cabeçalhos para usar no processamento dos dados
       const knowledgeHeaders: string[] = [];
       const attributeHeaders: string[] = [];

       let columnIndex = 0;

       // 2) Validação e armazenamento das colunas de Knowledge (primeiras colunas)
       for (let i = 0; i < this.listKnowledge.length; i++) {
         const headerKnowledgeName = headerValues[columnIndex];

         // Verifica se o nome do Knowledge existe no array this.listKnowledge
         const knowledgeExists = this.listKnowledge.find(k => k.knowledge === headerKnowledgeName);

         if (!knowledgeExists) {
           Alert.showError(`Error: Knowledge "${headerKnowledgeName}" in column ${columnIndex + 1} does not exist in the knowledge list. Please verify the knowledge name.`);
           return;
         }

         // Armazena a ordem do cabeçalho
         knowledgeHeaders.push(headerKnowledgeName);
         columnIndex++;
       }

       // Pula as 2 colunas de dano (MOD Dano Party, MOD Dano Oponente)
       columnIndex += 2;

       // 3) Validação e armazenamento das colunas de Attribute (últimas colunas)
       for (let i = 0; i < this.listAttributte.length; i++) {
         const headerAttributeName = headerValues[columnIndex];

         // Verifica se o nome do Attribute existe no array this.listAttributte
         const attributeExists = this.listAttributte.find(a => a.atributte === headerAttributeName);

         if (!attributeExists) {
           Alert.showError(`Error: Attribute "${headerAttributeName}" in column ${columnIndex + 1} does not exist in the attribute list. Please verify the attribute name.`);
           return;
         }

         // Armazena a ordem do cabeçalho
         attributeHeaders.push(headerAttributeName);
         columnIndex++;
       }

       // PROCESSAMENTO DAS DEMAIS LINHAS (DADOS)
       for (let index = 1; index < lines.length; index++) { // Começa da linha 1 (pula o cabeçalho)
         const values = lines[index].split('\t');

         // Validação do número de colunas dos dados
         if (values.length !== expectedColumns) {
           Alert.showError(`Error: Number of columns in line ${index + 1} does not match header columns (${expectedColumns}). Found: ${values.length}`);
           return;
         }

         const moonPhaseIndex = index - 1; // Ajusta índice para listMoonPhases
         if (moonPhaseIndex >= this.listMoonPhases.length) {
           Alert.showError(`Error: More data lines than available moon phases. Maximum: ${this.listMoonPhases.length}`);
           return;
         }

         const moonPhase = this.listMoonPhases[moonPhaseIndex];

         // Cria novo ModMoonRange
         const newMoonRange = await this._modMoonRangesService.createNewModMoonRanges();

         // Atribuições conforme as regras dinâmicas
         newMoonRange.moonPhase = moonPhase.moon;
         newMoonRange.technicalNomenclature = moonPhase.momenclatura;

         // Inicializa arrays de objetos
         newMoonRange.knowledge = [];
         newMoonRange.attribute = [];

         columnIndex = 0;

         // Processa colunas de Knowledge seguindo a ordem do cabeçalho
         for (let i = 0; i < knowledgeHeaders.length; i++) {
           const headerKnowledgeName = knowledgeHeaders[i]; // Nome do knowledge na ordem do cabeçalho
           const value = values[columnIndex];

           // Busca o knowledge correspondente no array
           const knowledgeItem = this.listKnowledge.find(k => k.knowledge === headerKnowledgeName);

           if (knowledgeItem) {
             const knowledgeObj: KnowledgeValue = { id: knowledgeItem.id, value: value };
             newMoonRange.knowledge.push(knowledgeObj);   
           }

           columnIndex++;
         }

         // Processa as 2 colunas de dano
         newMoonRange.modDanoParty = values[columnIndex];
         columnIndex++;
         newMoonRange.modDanoOponente = values[columnIndex];
         columnIndex++;

         // Processa colunas de Attributes seguindo a ordem do cabeçalho
         for (let i = 0; i < attributeHeaders.length; i++) {
           const headerAttributeName = attributeHeaders[i]; // Nome do attribute na ordem do cabeçalho
           const value = values[columnIndex];

           // Busca o attribute correspondente no array
           const attributeItem = this.listAttributte.find(a => a.atributte === headerAttributeName);

           if (attributeItem) {
             const attributeObj: AttributeValue = { id: attributeItem.id, value: value };
             newMoonRange.attribute.push(attributeObj);
           }

           columnIndex++;
         }

         this._modMoonRangesService.svcToModify(newMoonRange);
       }

       this._modMoonRangesService.toSave();
       this.change.detectChanges();
       Alert.ShowSuccess('MODMoon Ranges list copied successfully!');
       this.ngOnInit();
     } catch (error) {
       Alert.showError('Error importing data from Excel.');
       console.error(error);
     }
   }


   /**
    * ========== OBTER VALOR DE KNOWLEDGE ==========
    * Obtém o valor de um knowledge específico do moonRange
    * @param moonRange - O objeto ModMoonRange
    * @param knowledgeName - Nome do knowledge
    * @returns Valor do knowledge ou string vazia
    */
   getKnowledgeValue(moonRange: ModMoonRanges, knowledgeName: string): string {
     if (!moonRange.knowledge) return '';

     const knowledgeItem = this.listKnowledge.find(k => k.knowledge === knowledgeName);
     if (!knowledgeItem) return '';

     // Procura diretamente no array de objetos
     const entry = moonRange.knowledge.find(k => k.id === knowledgeItem.id);
     return entry ? entry.value : '';
   }

   /**
    * ========== OBTER VALOR DE ATTRIBUTE ==========
    * Obtém o valor de um attribute específico do moonRange
    * @param moonRange - O objeto ModMoonRange
    * @param attributeName - Nome do atributo
    * @returns Valor do atributo ou string vazia
    */
   getAttributeValue(moonRange: ModMoonRanges, attributeName: string): string {
     if (!moonRange.attribute) return '';

     const attributeItem = this.listAttributte.find(a => a.atributte === attributeName);
     if (!attributeItem) return '';

     // Procura diretamente no array de objetos
     const entry = moonRange.attribute.find(a => a.id === attributeItem.id);
     return entry ? entry.value : '';
   }

   /**
    * ========== OBTER NOME DO KNOWLEDGE POR ID ==========
    * Obtém o nome do knowledge baseado no ID
    * @param knowledgeId - ID do knowledge
    * @returns Nome do knowledge ou string vazia
    */
   getKnowledgeNameById(knowledgeId: string): string {
     if (!knowledgeId) return '';

     const knowledgeItem = this.listKnowledge.find(k => k.id === knowledgeId);
     return knowledgeItem ? knowledgeItem.knowledge : '';
   }

   /**
    * ========== OBTER NOME DO ATTRIBUTE POR ID ==========
    * Obtém o nome do attribute baseado no ID
    * @param attributeId - ID do attribute
    * @returns Nome do attribute ou string vazia
    */
   getAttributeNameById(attributeId: string): string {
     if (!attributeId) return '';

     const attributeItem = this.listAttributte.find(a => a.id === attributeId);
     return attributeItem ? attributeItem.atributte : '';
   }
}
