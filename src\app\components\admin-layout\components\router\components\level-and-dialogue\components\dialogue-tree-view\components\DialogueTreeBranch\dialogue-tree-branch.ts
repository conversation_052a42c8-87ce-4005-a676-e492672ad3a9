import { Component, OnInit, Input } from '@angular/core';
import { Router } from '@angular/router';
import { RoadBlockService } from 'src/app/services/road-block.service';
import { OptionService, DilemmaService, StoryBoxService } from 'src/app/services';
import { LevelHelperService } from 'src/app/services';
import { LabelColorService } from 'src/app/services/label-color.service';
import { OptionBoxService } from 'src/app/services/option-box.service';
import { DilemmaBoxService } from 'src/app/services/dilemmaBox.service';
import { RoadBlockType } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard';
// Branch calculation service
import { DialogueTreeBranchCalculationService } from 'src/app/services/dialogue-tree-branch-calculation.service';
import { DialogueTreeUtilityService } from 'src/app/services/dialogue-tree-utility.service';

@Component({
  selector: 'dialogue-tree-branch',
  templateUrl: './dialogue-tree-branch.html',
  styleUrls: ['./dialogue-tree-branch.scss'],
})
/**
 * Dialogue tree branch component that renders connecting lines between dialogue elements.
 * Handles complex branching logic including roadblock spacing, selection-based hiding,
 * and different branch types (top, middle, bottom) with dynamic positioning.
 */
export class DialogueTreeBranchComponent implements OnInit
{
    constructor(
        private _roadBlockService: RoadBlockService,
        private _optionService: OptionService,
        private _dilemmaService: DilemmaService,
        private _storyBoxService: StoryBoxService,
        private _levelHelperService: LevelHelperService,
        private _labelColorService: LabelColorService,
        private _optionBoxService: OptionBoxService,
        private _dilemmaBoxService: DilemmaBoxService,
        private _router: Router,
        // Branch calculation service
        private _branchCalculationService: DialogueTreeBranchCalculationService,
        private _dialogueTreeUtility: DialogueTreeUtilityService
    ) {}
    // Path configuration
    @Input() inPaths: boolean[];
    @Input() outPaths: boolean[];
    @Input() drawAllLinesInput: boolean = false;
    @Input() drawAllLinesOutput: boolean = false;

    // Roadblock configuration
    @Input() hasRoadblocks: boolean = false;
    @Input() roadblockCount: number = 0;
    @Input() hasOptionRoadblocks: boolean = false;

    // Selection state for branch visibility
    @Input() selectedChoiceOptions: Map<string, string> = new Map();
    @Input() selectedDilemmaOptions: Map<string, string> = new Map();
    @Input() parentOptionBoxId: string | undefined;
    @Input() optionIds: string[] = [];
    @Input() isInvestigationBox: boolean = false;
    @Input() isFromOptionLayer: boolean = false;

    // Roadblock evaluation for branch hiding
    @Input() roadblockEvaluationEnabled: boolean = true;
    @Input() selectedChoiceOptionsForRoadblocks: Map<string, string> = new Map();
    @Input() selectedInvestigationOptions: Map<string, Set<string>> = new Map();
    @Input() selectedDilemmaOptionsForRoadblocks: Map<string, string> = new Map();

    // Branch line calculations
    public lineWidthTop: number = 0;
    public lineWidthBottom: number = 0;
    public lineTopSpacing: number = 0;
    public lineBottomSpacing: number = 0;
    public dynamicLineHeight: number = 40;
    public usePixelMode: boolean = false;
    public middleBranchLines: {position: number, width: number, marginLeft: number}[] = [];

  /**
   * Initialize the component by calculating all branch dimensions and positions.
   */
  public ngOnInit(): void
  {
    this.calculateDynamicLineHeight();
    this.calculateLineWidthsAndSpacing();
    this.calculateMiddleBranchLines();
  }

  /**
   * Calculate line widths and spacing based on roadblock configuration.
   * Uses pixel mode for complex layouts, percentage mode for simple ones.
   */
  private calculateLineWidthsAndSpacing(): void {
    const result = this._branchCalculationService.calculateLineWidthsAndSpacing(
      this.inPaths,
      this.outPaths,
      this.hasRoadblocks,
      this.hasOptionRoadblocks,
      this.drawAllLinesInput,
      this.drawAllLinesOutput
    );

    this.lineWidthTop = result.lineWidthTop;
    this.lineWidthBottom = result.lineWidthBottom;
    this.lineTopSpacing = result.lineTopSpacing;
    this.lineBottomSpacing = result.lineBottomSpacing;
    this.usePixelMode = result.usePixelMode;
  }

  /** Calculate dynamic line height based on roadblock count */
  private calculateDynamicLineHeight(): void {
    this.dynamicLineHeight = this._branchCalculationService.calculateDynamicLineHeight(
      this.hasRoadblocks,
      this.roadblockCount
    );
  }

  /** Calculate middle branch lines: 2 options = 2 lines, 3 options = 2 lines (skip middle), 4+ = options - 1 */
  private calculateMiddleBranchLines(): void {
    this.middleBranchLines = this._branchCalculationService.calculateMiddleBranchLines(
      this.inPaths,
      this.outPaths
    );
  }

  /** Get dynamic line height for templates */
  public getDynamicLineHeight(): number {
    return this.dynamicLineHeight;
  }

  /** Check if branch should be hidden based on selection state or roadblock evaluation */
  public shouldHideBranchBasedOnSelection(branchIndex: number): boolean {

    // Investigation boxes: only hide due to roadblocks, not selections
    if (this.isInvestigationBox) {
      if (this.roadblockEvaluationEnabled && branchIndex < this.optionIds.length) {
        const optionId = this.optionIds[branchIndex];
        return this.shouldHideOptionDueToRoadblocks(optionId);
      }
      return false;
    }

    if (!this.parentOptionBoxId || !this.optionIds || this.optionIds.length === 0) {
      return false;
    }

    let selectedOptionId: string | undefined;

    // Check choice options first (includes dice failures)
    if (this.selectedChoiceOptions) {
      selectedOptionId = this.selectedChoiceOptions.get(this.parentOptionBoxId);
    }

    // Check dilemma options if no choice selection
    if (!selectedOptionId && this.selectedDilemmaOptions) {
      for (const [, dilemmaId] of this.selectedDilemmaOptions.entries()) {
        if (this.optionIds.includes(dilemmaId)) {
          selectedOptionId = dilemmaId;
          break;
        }
      }
    }

    if (!selectedOptionId) {
      // No selection - check roadblock hiding
      if (this.roadblockEvaluationEnabled && branchIndex < this.optionIds.length) {
        const optionId = this.optionIds[branchIndex];
        return this.shouldHideOptionDueToRoadblocks(optionId);
      }
      return false;
    }

    // Check if branch corresponds to selected option
    if (branchIndex < this.optionIds.length) {
      const optionId = this.optionIds[branchIndex];

      // Check roadblocks first
      if (this.roadblockEvaluationEnabled && this.shouldHideOptionDueToRoadblocks(optionId)) {
        return true;
      }

      const shouldHide = optionId !== selectedOptionId;
      return shouldHide;
    }

    return false;
  }

  /** Check if top branch should be hidden for Layer 2 (options to next elements) */
  public shouldHideTopBranchFromOptionLayer(branchIndex: number): boolean {
    if (!this.isFromOptionLayer) {
      return false;
    }
    return this.shouldHideBranchBasedOnSelection(branchIndex);
  }



  /** Get style for middle branch line with pixel/flex positioning and hiding logic */
  public getMiddleBranchLineStyle(lineIndex: number, ): any {
    if (!this.middleBranchLines || lineIndex >= this.middleBranchLines.length) {
      return { 'display': 'none' };
    }

    const inCount = this.inPaths?.length || 0;
    const outCount = this.outPaths?.length || 0;
    const totalOptions = Math.max(inCount, outCount);

    // Handle different option types using dedicated handlers

    // 1. Handle Investigation boxes (roadblock-only logic)
    const investigationResult = this.handleInvestigationBoxLogic(lineIndex, totalOptions);
    if (investigationResult !== null) {
      return investigationResult;
    }

    // 2. Handle Choice/Dilemma options (unified logic since they're identical)
    const choiceDilemmaResult = this.handleChoiceDilemmaLogic(lineIndex, totalOptions);
    if (choiceDilemmaResult !== null) {
      return choiceDilemmaResult;
    }

    // 3. Handle incoming path closure (when previous layer options have finish/restart markers)
    const pathClosureResult = this.handleIncomingPathClosure(lineIndex, totalOptions);
    if (pathClosureResult !== null) {
      return pathClosureResult;
    }

    // 4. Default case: return appropriate visible style based on scenario
    return this.createBranchStyle(lineIndex, totalOptions, false, this.hasOptionRoadblocks);
  }


  /**
   * Get the style for the main middle-branch container
   * Applies margins for 4+ options with NO roadblocks
   */
  public getMiddleBranchContainerStyle(): any {
    const inCount = this.inPaths?.length || 0;
    const outCount = this.outPaths?.length || 0;
    const totalOptions = Math.max(inCount, outCount);

    // Special case: 4+ options with NO option roadblocks - apply container margins
    if (totalOptions >= 4 && !this.hasOptionRoadblocks) {
      // Lookup table for margin values based on option count
      const marginValues: { [key: number]: { left: number, right: number } } = {
        4: { left: 304, right: 304 },
        5: { left: 243, right: 242 },
        6: { left: 202, right: 202 },
        7: { left: 173, right: 173 },
        8: { left: 151, right: 151 },
        9: { left: 134, right: 134 },
        10: { left: 120, right: 120 },
        11: { left: 109, right: 109 },
        12: { left: 100, right: 100 }
      };

      // Get margin values for current option count, fallback to 12 options values for higher counts
      const margins = marginValues[totalOptions] || marginValues[12];

      return {
        'margin-left': `${margins.left}px`,
        'margin-right': `${margins.right}px`
      };
    }

    // Default: no special margins
    return {};
  }

  private calculateFlexBranchWidth(totalOptions: number): number {
    // For flex branches, use empirical values based on actual rendered widths
    // These values are approximately what flex-grow: 1 produces for each scenario
    const branchWidthMap: { [key: number]: number } = {
      4: 612,  // Empirical value for 4 options
      5: 486,  // Empirical value for 5 options
      6: 404,  // Empirical value for 6 options
      7: 346,  // Empirical value for 7 options
      8: 302,  // Empirical value for 8 options
      9: 268,  // Empirical value for 9 options
      10: 240, // Empirical value for 10 options
      11: 218, // Empirical value for 11 options
      12: 200  // Empirical value for 12 options
    };

    return branchWidthMap[totalOptions] || 200; // Default to 200 for higher counts
  }

  // ===== OPTION TYPE HANDLERS =====
  // These methods handle the different option types (Investigation, Choice, Dilemma)

  /**
   * Handle investigation box logic - only roadblock-based hiding, no selection-based hiding
   * Investigation boxes allow multiple selections, so branches are only hidden due to roadblocks
   */
  private handleInvestigationBoxLogic(lineIndex: number, totalOptions: number): any | null {
    if (!this.isInvestigationBox || !this.roadblockEvaluationEnabled) {
      return null; // Not investigation box or roadblock evaluation disabled
    }

    // For 4+ options with roadblocks: apply path-to-center logic for all visible options
    if (totalOptions >= 4 && this.hasOptionRoadblocks) {
      return this.handlePathToCenterLogic(lineIndex, totalOptions);
    } else {
      // For 3 or fewer options, or no roadblocks: check individual option roadblocks
      return this.handleIndividualOptionRoadblocks(lineIndex, totalOptions);
    }
  }

  /**
   * Handle choice/dilemma option logic - unified handler since the logic is identical
   * Handles selection-based hiding and roadblock evaluation
   */
  private handleChoiceDilemmaLogic(lineIndex: number, totalOptions: number): any | null {
    if (this.isInvestigationBox || !this.parentOptionBoxId || !this.optionIds || this.optionIds.length === 0) {
      return null; // Not applicable for this scenario
    }

    // Get selected option ID (works for both choice and dilemma)
    const selectedOptionId = this.getSelectedOptionId();

    // Handle roadblock-based hiding when no selection is made
    if (this.roadblockEvaluationEnabled && !selectedOptionId) {
      return this.handleNoSelectionRoadblockLogic(lineIndex, totalOptions);
    }

    // Handle selection-based logic
    if (selectedOptionId) {
      return this.handleSelectionBasedLogic(lineIndex, totalOptions, selectedOptionId);
    }

    return null; // No special handling needed
  }

  /**
   * Handle incoming path closure logic - when previous layer options have finish/restart markers
   */
  private handleIncomingPathClosure(lineIndex: number, totalOptions: number): any | null {
    if (!this.inPaths || this.inPaths.length === 0) {
      return null;
    }

    // Use unified mapping system
    const optionIndex = this.mapLineIndexToOptionIndex(lineIndex, this.inPaths.length);

    if (optionIndex < this.inPaths.length) {
      const optionClosed = !this.inPaths[optionIndex];
      if (optionClosed) {
        // Keep the line's positioning but make it transparent
        return this.createBranchStyle(lineIndex, totalOptions, true, this.hasOptionRoadblocks, true);
      }
    }

    return null;
  }

  // ===== UNIFIED ROADBLOCK EVALUATION SYSTEM =====

  /**
   * Unified roadblock evaluation for any option type (Choice, Dilemma, StoryBox)
   */
  private evaluateOptionRoadblocks(optionId: string): boolean {
    if (!this.roadblockEvaluationEnabled) {
      return false; // Roadblock evaluation disabled
    }

    // CASCADING LOGIC: Check if parent should be hidden first
    if (this.shouldHideBasedOnParent(optionId)) {
      return true;
    }

    // Try to find the option and evaluate its roadblocks
    const evaluationResult = this.findAndEvaluateOption(optionId);
    return evaluationResult !== null ? evaluationResult : false;
  }

  /**
   * Find the option by ID and evaluate its roadblocks using unified logic
   */
  private findAndEvaluateOption(optionId: string): boolean | null {
    // Try choice option first
    const choiceResult = this.evaluateChoiceOptionRoadblocks(optionId);
    if (choiceResult !== null) return choiceResult;

    // Try dilemma option
    const dilemmaResult = this.evaluateDilemmaOptionRoadblocks(optionId);
    if (dilemmaResult !== null) return dilemmaResult;

    // Try dice failure story box
    const storyBoxResult = this.evaluateStoryBoxRoadblocks(optionId);
    if (storyBoxResult !== null) return storyBoxResult;

    return null; // Option not found
  }

  /**
   * Evaluate choice option roadblocks
   */
  private evaluateChoiceOptionRoadblocks(optionId: string): boolean | null {
    const option = this._optionService.svcFindById(optionId);
    if (!option) return null;

    const answerBoxId = option.answerBoxId;
    if (!answerBoxId || !this._dialogueTreeUtility.hasRoadblocks(answerBoxId)) {
      return false; // No roadblocks, don't hide
    }

    const roadblocks = this._roadBlockService.filterStoryboxForAllRoadblocks(answerBoxId);
    const andOrCondition = option.AndOrCondition || 'OR';

    return this.evaluateRoadblockConditions(roadblocks, andOrCondition);
  }

  /**
   * Evaluate dilemma option roadblocks
   */
  private evaluateDilemmaOptionRoadblocks(optionId: string): boolean | null {
    const dilemma = this._dilemmaService.svcFindById(optionId);
    if (!dilemma) return null;

    const answerBoxId = dilemma.idDilemmaBox;
    if (!answerBoxId || !this._dialogueTreeUtility.hasRoadblocks(answerBoxId)) {
      return false; // No roadblocks, don't hide
    }

    const roadblocks = this._roadBlockService.filterStoryboxForAllRoadblocks(answerBoxId);
    const andOrCondition = dilemma.AndOrCondition || 'OR';

    return this.evaluateRoadblockConditions(roadblocks, andOrCondition);
  }

  /**
   * Evaluate story box (dice failure) roadblocks
   */
  private evaluateStoryBoxRoadblocks(optionId: string): boolean | null {
    const storyBox = this._storyBoxService.svcFindById(optionId);
    if (!storyBox || !this._dialogueTreeUtility.hasRoadblocks(storyBox.id)) {
      return null; // Not a story box or no roadblocks
    }

    const roadblocks = this._roadBlockService.filterStoryboxForAllRoadblocks(storyBox.id);
    const andOrCondition = storyBox.AndOrCondition || 'OR';

    return this.evaluateRoadblockConditions(roadblocks, andOrCondition);
  }

  /**
   * Unified AND/OR condition evaluation for roadblocks
   */
  private evaluateRoadblockConditions(roadblocks: any[], andOrCondition: string): boolean {
    if (andOrCondition === 'AND') {
      // ALL roadblocks must be satisfied - hide if ANY roadblock is not satisfied
      return !roadblocks.every(roadblock => this.isRoadblockSatisfied(roadblock));
    } else {
      // OR condition (default): AT LEAST ONE roadblock must be satisfied - hide if NO roadblocks are satisfied
      return !roadblocks.some(roadblock => this.isRoadblockSatisfied(roadblock));
    }
  }

  // ===== HELPER METHODS FOR OPTION TYPE HANDLERS =====

  /**
   * Get the selected option ID for either choice or dilemma options
   */
  private getSelectedOptionId(): string | undefined {
    // Check choice options first
    if (this.selectedChoiceOptions) {
      const choiceSelection = this.selectedChoiceOptions.get(this.parentOptionBoxId!);
      if (choiceSelection) {
        return choiceSelection;
      }
    }

    // Check dilemma options
    if (this.selectedDilemmaOptions) {
      for (const [, dilemmaId] of this.selectedDilemmaOptions.entries()) {
        if (this.optionIds.includes(dilemmaId)) {
          return dilemmaId;
        }
      }
    }

    return undefined;
  }

  /**
   * Handle path-to-center logic for 4+ options with roadblocks
   */
  private handlePathToCenterLogic(lineIndex: number, totalOptions: number): any | null {
    // Use unified path calculation system
    const uniquePathBranches = this.calculateAllPathsToCenter(totalOptions);

    // Check if this line is part of any connection path
    if (uniquePathBranches.includes(lineIndex)) {
      return null; // This branch is needed for connectivity, don't hide it
    } else {
      // This branch is not needed for connectivity, hide it
      return this.createTransparentPixelStyle(lineIndex);
    }
  }

  /**
   * Handle individual option roadblocks for 3 or fewer options
   */
  private handleIndividualOptionRoadblocks(lineIndex: number, totalOptions: number): any | null {
    // Use unified mapping system
    const optionIndex = this.mapLineIndexToOptionIndex(lineIndex, totalOptions);

    if (optionIndex < this.optionIds.length) {
      const optionId = this.optionIds[optionIndex];
      if (this.shouldHideOptionDueToRoadblocks(optionId)) {
        // Hide this middle branch due to roadblocks
        return this.createBranchStyle(lineIndex, totalOptions, true, this.hasOptionRoadblocks);
      }
    }

    return null; // No hiding needed
  }

  /**
   * Handle roadblock logic when no selection is made
   */
  private handleNoSelectionRoadblockLogic(lineIndex: number, totalOptions: number): any | null {
    // For 4+ options with roadblocks: apply path-to-center logic for all visible options
    if (totalOptions >= 4 && this.hasOptionRoadblocks) {
      return this.handlePathToCenterLogic(lineIndex, totalOptions);
    } else {
      // For 3 or fewer options, or no roadblocks: use original logic
      return this.handleIndividualOptionRoadblocks(lineIndex, totalOptions);
    }
  }

  /**
   * Handle selection-based logic for choice/dilemma options
   */
  private handleSelectionBasedLogic(lineIndex: number, totalOptions: number, selectedOptionId: string): any | null {
    // Check if it's the center option in a 3-option scenario
    if (totalOptions === 3) {
      const centerOptionIndex = 1; // Middle option in a 3-option scenario
      const centerOptionId = this.optionIds[centerOptionIndex];

      if (selectedOptionId === centerOptionId) {
        // Center option is selected, hide ALL middle branches
        return { 'display': 'none' };
      }
    }

    // For other scenarios, map the line index to the corresponding option
    let optionIndex: number;
    if (totalOptions === 3) {
      // For 3 options: line 0 maps to option 0, line 1 maps to option 2
      optionIndex = lineIndex === 0 ? 0 : 2;
    } else if (totalOptions >= 4) {
      return this.handleComplexSelectionLogic(lineIndex, totalOptions, selectedOptionId);
    } else {
      // For other cases, direct mapping
      optionIndex = lineIndex;
    }

    // Check roadblocks first
    if (optionIndex >= 0 && optionIndex < this.optionIds.length) {
      const optionId = this.optionIds[optionIndex];
      if (this.roadblockEvaluationEnabled && this.shouldHideOptionDueToRoadblocks(optionId)) {
        return this.createBranchStyle(lineIndex, totalOptions, true, this.hasOptionRoadblocks);
      }

      // Hide if this line corresponds to an unselected option
      if (this.optionIds[optionIndex] !== selectedOptionId) {
        return this.createBranchStyle(lineIndex, totalOptions, true, this.hasOptionRoadblocks, true);
      }
    }

    return null; // No hiding needed
  }

  /**
   * Handle complex selection logic for 4+ options
   */
  private handleComplexSelectionLogic(lineIndex: number, totalOptions: number, selectedOptionId: string): any | null {
    const selectedOptionIndex = this.optionIds.findIndex(id => id === selectedOptionId);
    const isOddCount = totalOptions % 2 === 1;
    const centerIndex = this.calculateCenterIndex(totalOptions, this.hasOptionRoadblocks);

    // Handle center option selection
    if (this.hasOptionRoadblocks && selectedOptionIndex === centerIndex) {
      return this.createTransparentPixelStyle(lineIndex);
    } else if (isOddCount && selectedOptionIndex === centerIndex) {
      return this.createBranchStyle(lineIndex, totalOptions, true, this.hasOptionRoadblocks, true);
    }

    // Handle even-count scenarios
    if (!this.hasOptionRoadblocks && !isOddCount) {
      return this.handleEvenCountSelection(lineIndex, totalOptions, selectedOptionIndex, centerIndex);
    }

    // Handle odd-count edge options
    if (!this.hasOptionRoadblocks) {
      return this.handleOddCountEdgeSelection(lineIndex, totalOptions, selectedOptionIndex);
    }

    // Handle roadblock scenarios
    if (this.hasOptionRoadblocks) {
      return this.handleRoadblockSelection(lineIndex, selectedOptionIndex);
    }

    return null;
  }

  /**
   * Handle even-count selection scenarios
   */
  private handleEvenCountSelection(lineIndex: number, totalOptions: number, selectedOptionIndex: number, centerIndex: number): any | null {
    const centerBranchIndex = centerIndex - 1; // Branch that connects the two center options

    if (selectedOptionIndex === centerIndex - 1 || selectedOptionIndex === centerIndex) {
      // One of the center pair selected: show only the connecting branch between them
      if (lineIndex === centerBranchIndex) {
        const isLeftCenterSelected = selectedOptionIndex === centerIndex - 1;
        return this.createHalfWidthStyle(lineIndex, totalOptions, isLeftCenterSelected, this.hasOptionRoadblocks);
      } else {
        // Force hide by returning transparent styling immediately
        return this.createBranchStyle(lineIndex, totalOptions, true, this.hasOptionRoadblocks, true);
      }
    } else {
      // Edge option selected: show path to center INCLUDING the shared center branch
      const pathBranches = this.calculatePathToCenter(selectedOptionIndex, totalOptions);
      // Always include the center branch for even-count scenarios
      if (!pathBranches.includes(centerBranchIndex)) {
        pathBranches.push(centerBranchIndex);
      }

      if (pathBranches.includes(lineIndex)) {
        // Special styling for the shared center branch when edge options are selected
        if (lineIndex === centerBranchIndex) {
          const isLeftSideSelected = selectedOptionIndex < centerIndex;
          return this.createHalfWidthStyle(lineIndex, totalOptions, isLeftSideSelected, this.hasOptionRoadblocks);
        }
        return null; // Don't hide branches in the path
      } else {
        // Force hide by returning transparent styling immediately
        return this.createBranchStyle(lineIndex, totalOptions, true, this.hasOptionRoadblocks, true);
      }
    }
  }

  /**
   * Handle odd-count edge selection scenarios
   */
  private handleOddCountEdgeSelection(lineIndex: number, totalOptions: number, selectedOptionIndex: number): any | null {
    const pathBranches = this.calculatePathToCenter(selectedOptionIndex, totalOptions);
    if (pathBranches.includes(lineIndex)) {
      return null; // Don't hide branches in the path
    } else {
      // Force hide by returning transparent styling immediately
      return this.createBranchStyle(lineIndex, totalOptions, true, this.hasOptionRoadblocks, true);
    }
  }

  /**
   * Handle roadblock selection scenarios
   */
  private handleRoadblockSelection(lineIndex: number, selectedOptionIndex: number): any | null {
    const pathBranches = this.calculatePathToCenterRoadblocks(selectedOptionIndex);
    if (pathBranches.includes(lineIndex)) {
      return null; // Don't hide branches in the path
    } else {
      // Force hide by returning transparent styling immediately
      return this.createTransparentPixelStyle(lineIndex);
    }
  }

  // ===== UNIFIED PATH CALCULATION SYSTEM =====

  /**
   * Unified path calculation for any scenario (roadblock or non-roadblock)
   */
  private calculatePathBetweenIndices(fromIndex: number, toIndex: number): number[] {
    const pathBranches: number[] = [];

    if (fromIndex < toIndex) {
      // Going from left to right
      for (let i = fromIndex; i < toIndex; i++) {
        pathBranches.push(i);
      }
    } else if (fromIndex > toIndex) {
      // Going from right to left
      for (let i = toIndex; i < fromIndex; i++) {
        pathBranches.push(i);
      }
    }
    // If fromIndex === toIndex, return empty array (no path needed)

    return pathBranches;
  }

  /**
   * Calculate center index based on scenario (roadblock vs non-roadblock)
   */
  private calculateCenterIndex(totalOptions: number, hasRoadblocks: boolean): number {
    if (hasRoadblocks) {
      return 1; // Always element 1 for roadblocks
    } else {
      return Math.floor(totalOptions / 2); // Mathematical center for non-roadblocks
    }
  }

  /**
   * Find all visible (non-roadblocked) option indices
   */
  private getVisibleOptionIndices(): number[] {
    const visibleIndices: number[] = [];
    for (let i = 0; i < this.optionIds.length; i++) {
      const optionId = this.optionIds[i];
      if (!this.shouldHideOptionDueToRoadblocks(optionId)) {
        visibleIndices.push(i);
      }
    }
    return visibleIndices;
  }

  /**
   * Calculate paths from all visible options to center
   * Used for roadblock scenarios where multiple options need connectivity
   */
  private calculateAllPathsToCenter(totalOptions: number): number[] {
    const centerIndex = this.calculateCenterIndex(totalOptions, this.hasOptionRoadblocks);
    const visibleIndices = this.getVisibleOptionIndices();
    const allPathBranches: number[] = [];

    for (const visibleIndex of visibleIndices) {
      if (visibleIndex !== centerIndex) {
        const pathBranches = this.calculatePathBetweenIndices(visibleIndex, centerIndex);
        allPathBranches.push(...pathBranches);
      }
    }

    // Remove duplicates and return
    return [...new Set(allPathBranches)];
  }

  /**
   * Calculate path from a specific option to center
   * Unified method that works for both roadblock and non-roadblock scenarios
   */
  private calculatePathToCenter(selectedOptionIndex: number, totalOptions: number): number[] {
    const centerIndex = this.calculateCenterIndex(totalOptions, this.hasOptionRoadblocks);
    return this.calculatePathBetweenIndices(selectedOptionIndex, centerIndex);
  }

  /**
   * Map line index to option index based on total options
   * Handles the special case where 3 options have different mapping
   */
  private mapLineIndexToOptionIndex(lineIndex: number, totalOptions: number): number {
    if (totalOptions === 3) {
      // For 3 options: line 0 maps to option 0, line 1 maps to option 2
      return lineIndex === 0 ? 0 : 2;
    } else {
      // For other cases, direct mapping
      return lineIndex;
    }
  }

  // ===== STYLE GENERATION METHODS =====

  /**
   * Create a transparent flex-grow style for 4+ options without roadblocks
   * Used when hiding branches but maintaining flex layout
   */
  private createTransparentFlexStyle(includePosition: boolean = false): any {
    const style: any = {
      'flex-grow': '1',
      'width': 'auto',
      'margin-left': '0',
      'margin-right': '0',
      'background-color': 'transparent'
    };

    if (includePosition) {
      style['position'] = 'relative';
    }

    return style;
  }

  /**
   * Create a transparent pixel-based style with calculated positioning
   * Used for roadblock scenarios and 3 or fewer options
   */
  private createTransparentPixelStyle(lineIndex: number, totalOptions: number = 0): any {
    if (totalOptions === 2) {
      // Special case for 2 options
      const marginLeftValues = [610, 1220];
      const marginLeft = marginLeftValues[lineIndex] || 610;
      return {
        'width': '615px',
        'margin-left': `${marginLeft}px`,
        'background-color': 'transparent'
      };
    } else {
      // Standard case: 815px width with calculated margin
      const baseMarginLeft = 410;
      const branchWidth = 815;
      const marginLeft = baseMarginLeft + (lineIndex * branchWidth);
      return {
        'width': '815px',
        'margin-left': `${marginLeft}px`,
        'background-color': 'transparent'
      };
    }
  }

  /**
   * Create a visible flex-grow style for 4+ options without roadblocks
   * Used for normal branch display in flex layout
   */
  private createVisibleFlexStyle(): any {
    return {
      'flex-grow': '1',
      'width': 'auto',
      'margin-left': '0',
      'margin-right': '0',
      'position': 'relative'
    };
  }

  /**
   * Create a visible pixel-based style with calculated positioning
   * Used for normal branch display in pixel layout
   */
  private createVisiblePixelStyle(lineIndex: number, totalOptions: number = 0): any {
    if (totalOptions === 2) {
      // Special case for 2 options
      const marginLeftValues = [610, 1221];
      const marginLeft = marginLeftValues[lineIndex] || 610;
      return {
        'width': '615px',
        'margin-left': `${marginLeft}px`
      };
    } else {
      // Standard case: 815px width with calculated margin
      const baseMarginLeft = 410;
      const branchWidth = 815;
      const marginLeft = baseMarginLeft + (lineIndex * branchWidth);
      return {
        'width': '815px',
        'margin-left': `${marginLeft}px`
      };
    }
  }

  /**
   * Create a half-width style for center branch scenarios
   * Used when center options are selected and need partial branch display
   */
  private createHalfWidthStyle(
    lineIndex: number,
    totalOptions: number,
    isLeftSide: boolean,
    hasOptionRoadblocks: boolean
  ): any {
    if (totalOptions >= 4 && !hasOptionRoadblocks) {
      // Flex layout: use calculated half width
      const halfWidth = Math.floor(this.calculateFlexBranchWidth(totalOptions) / 2);
      return {
        'flex-grow': '1',
        'max-width': `${halfWidth}px`,
        'margin-left': isLeftSide ? '0' : `${halfWidth}px`,
        'margin-right': isLeftSide ? `${halfWidth}px` : '0',
        'position': 'relative'
      };
    } else {
      // Pixel layout: use half of 815px
      const halfWidth = Math.floor(815 / 2); // 407px
      const baseMarginLeft = 410;
      const branchWidth = 815;
      const originalMarginLeft = baseMarginLeft + (lineIndex * branchWidth);
      return {
        'width': `${halfWidth}px`,
        'margin-left': isLeftSide ? `${originalMarginLeft}px` : `${originalMarginLeft + halfWidth}px`,
        'margin-right': '0'
      };
    }
  }

  /**
   * Create appropriate style based on scenario (transparent vs visible, flex vs pixel)
   * This is the main style factory method that chooses the right style type
   */
  private createBranchStyle(
    lineIndex: number,
    totalOptions: number,
    isTransparent: boolean,
    hasOptionRoadblocks: boolean,
    includePosition: boolean = false
  ): any {
    const useFlexLayout = totalOptions >= 4 && !hasOptionRoadblocks;

    if (isTransparent) {
      return useFlexLayout
        ? this.createTransparentFlexStyle(includePosition)
        : this.createTransparentPixelStyle(lineIndex, totalOptions);
    } else {
      return useFlexLayout
        ? this.createVisibleFlexStyle()
        : this.createVisiblePixelStyle(lineIndex, totalOptions);
    }
  }

  private calculatePathToCenterRoadblocks(selectedIndex: number): number[] {
    // Use unified path calculation system
    const centerIndex = 1; // Always element 1 for roadblocks
    return this.calculatePathBetweenIndices(selectedIndex, centerIndex);
  }

  /**
   * Check if a specific option should be hidden due to roadblock evaluation
   * This implements the same roadblock logic as DialogueTreeLeaf for both choice and dilemma options
   * @param optionId The ID of the option to check (can be choice option or dilemma option)
   * @returns True if the option should be hidden due to roadblocks
   */
  private shouldHideOptionDueToRoadblocks(optionId: string): boolean {
    // Now uses the unified roadblock evaluation system to eliminate duplication
    return this.evaluateOptionRoadblocks(optionId);
  }

  /**
   * Check if a specific roadblock is satisfied by current selections
   * This implements the same logic as DialogueTreeLeaf
   */
  private isRoadblockSatisfied(roadblock: any): boolean {

    // Only evaluate "Spoke In" roadblocks that have matches in current dialogue
    if (+roadblock.Type !== +RoadBlockType.SPOKE_IN || !roadblock.spokeElementId) {
      return true; // Non-spoke roadblocks are always considered satisfied for now
    }

    // Get the spoke element referenced by this roadblock
    const spokeElement = this._levelHelperService.models.find((sp: any) =>
      sp.elementId === roadblock.spokeElementId
    );

    if (!spokeElement?.text) {
      return true; // If spoke element not found, consider satisfied
    }

    // Extract the label from the spoke text
    const referencedLabel = this._labelColorService.extractLabelFromSpokeText(spokeElement.text);

    if (!referencedLabel) {
      return true; // No label found, consider satisfied
    }

    // Check if this label exists in current dialogue
    const labelExistsInDialogue = this.checkLabelExistsInDialogue(referencedLabel);
    if (!labelExistsInDialogue) {
      return true; // If label not in current dialogue, don't evaluate (exterior roadblock - always satisfied)
    }

    // Check if this label is satisfied by current selections
    const isLabelSatisfied = this.checkLabelSatisfiedBySelections(referencedLabel);
    return isLabelSatisfied;
  }

  /**
   * Get the current dialogue ID from the router
   */
  private getCurrentDialogueId(): string {
    const url = this._router.url;
    const urlParts = url.split("/");
    const dialogueId = urlParts[4]; // URL structure: /levels/LEVEL_ID/dialogues/DIALOGUE_ID
    return dialogueId;
  }

  /**
   * Check if a label exists in the current dialogue (not whether it's satisfied)
   * This is used to determine if a roadblock is exterior (should be ignored)
   */
  private checkLabelExistsInDialogue(label: string): boolean {
    if (!label) return false;

    // Get current dialogue ID from router URL
    const currentDialogueId = this.getCurrentDialogueId();
    if (!currentDialogueId) return false;

    const dialoguePrefix = currentDialogueId.split('#')[0];
    const dialogueLabels = this._levelHelperService.models
      .filter((sp: any) => sp.elementId && sp.elementId.startsWith(dialoguePrefix + '.'))
      .map((sp: any) => this._labelColorService.extractLabelFromSpokeText(sp.text))
      .filter((extractedLabel: string) => extractedLabel && extractedLabel.trim() !== '');

    return dialogueLabels.includes(label);
  }

  /**
   * Check if a label is satisfied by current choice/investigation/dilemma selections
   */
  private checkLabelSatisfiedBySelections(label: string): boolean {
    // Use unified label checking system to eliminate triple duplication
    return this.checkChoiceSelections(label) ||
           this.checkInvestigationSelections(label) ||
           this.checkDilemmaSelections(label) ||
           this.checkStandaloneStoryBoxes(label);
  }

  /**
   * Check choice selections for label match
   */
  private checkChoiceSelections(label: string): boolean {
    if (!this.selectedChoiceOptionsForRoadblocks) return false;

    for (const [_optionBoxId, selectedOptionId] of this.selectedChoiceOptionsForRoadblocks.entries()) {
      if (this.checkOptionLabelMatch(selectedOptionId, label, 'choice')) {
        return true;
      }
    }
    return false;
  }

  /**
   * Check investigation selections for label match
   */
  private checkInvestigationSelections(label: string): boolean {
    if (!this.selectedInvestigationOptions) return false;

    for (const [_optionBoxId, selectedOptionIds] of this.selectedInvestigationOptions.entries()) {
      for (const selectedOptionId of selectedOptionIds) {
        if (this.checkOptionLabelMatch(selectedOptionId, label, 'choice')) {
          return true;
        }
      }
    }
    return false;
  }

  /**
   * Check dilemma selections for label match
   */
  private checkDilemmaSelections(label: string): boolean {
    if (!this.selectedDilemmaOptionsForRoadblocks) return false;

    for (const [_dilemmaBoxId, selectedDilemmaId] of this.selectedDilemmaOptionsForRoadblocks.entries()) {
      if (this.checkOptionLabelMatch(selectedDilemmaId, label, 'dilemma')) {
        return true;
      }
    }
    return false;
  }

  /**
   * Unified method to check if an option/dilemma matches a label
   */
  private checkOptionLabelMatch(optionId: string, targetLabel: string, type: 'choice' | 'dilemma'): boolean {
    if (!optionId) return false;

    if (type === 'choice') {
      const selectedOption = this._optionService.svcFindById(optionId);
      if (selectedOption?.answerBoxId) {
        const answerBox = this._storyBoxService?.svcFindById(selectedOption.answerBoxId);
        return answerBox?.labelOption === targetLabel;
      }
    } else if (type === 'dilemma') {
      const selectedDilemma = this._dilemmaService.svcFindById(optionId);
      if (selectedDilemma?.idDilemmaBox) {
        const answerBox = this._storyBoxService?.svcFindById(selectedDilemma.idDilemmaBox);
        return answerBox?.labelOption === targetLabel;
      }
    }

    return false;
  }

  /**
   * Check standalone story boxes for label matches
   */
  private checkStandaloneStoryBoxes(label: string): boolean {
    const currentDialogueId = this.getCurrentDialogueId();
    if (!currentDialogueId) return false;

    // Extract dialogue prefix for filtering (e.g., "A8.L1794.D0_PT-BR")
    const dialoguePrefix = currentDialogueId.split('#')[0];

    // Get all story boxes in the current dialogue, but exclude answer boxes
    const standaloneStoryBoxes = this._storyBoxService.models.filter(storyBox =>
      storyBox.id &&
      storyBox.id.startsWith(dialoguePrefix + '.') &&
      !storyBox.id.includes('.opt') // Exclude answer boxes (they contain ".opt" in their ID)
    );

    for (const storyBox of standaloneStoryBoxes) {
      // Check if this story box has the target label
      if (storyBox.label && storyBox.label.trim() === label.trim()) {
        // Check if this story box is actually visible in the current dialogue state
        if (this.isStoryBoxActuallyVisible(storyBox)) {
          return true;
        }
      }
    }

    return false; // Label not found in any visible story boxes
  }

  /**
   * Check if a story box is actually visible in the current dialogue state
   * Uses the same logic as the dialogue tree rendering to determine visibility
   */
  private isStoryBoxActuallyVisible(storyBox: any): boolean {
    // If roadblock evaluation is disabled, all story boxes are visible
    if (!this.roadblockEvaluationEnabled) {
      return true;
    }

    // Use the existing roadblock evaluation logic that's used by the dialogue tree
    // This is the same logic used in shouldHideElement() but specifically for story boxes
    if (!this._dialogueTreeUtility.hasRoadblocks(storyBox.id)) {
      return true; // No roadblocks, always visible
    }

    const storyBoxRoadblocks = this._roadBlockService.filterStoryboxForAllRoadblocks(storyBox.id);

    // Get the AND/OR condition for this story box
    const andOrCondition = storyBox.AndOrCondition || 'OR';

    // Use unified roadblock evaluation logic
    return !this.evaluateRoadblockConditions(storyBoxRoadblocks, andOrCondition);
  }

  /**
   * Check if an option should be hidden because its parent dialogue box is hidden
   * This implements cascading roadblock logic similar to DialogueTreeLeaf
   * @param optionId The ID of the option to check
   * @returns True if the option should be hidden due to parent being hidden
   */
  private shouldHideBasedOnParent(optionId: string): boolean {
    // Find the parent box by checking both choice options and dilemma options
    let parentBoxId: string | undefined;
    let parentBoxType: string | undefined;

    // First, try to find as choice option by searching through all OptionBoxes
    const parentOptionBox = this._optionBoxService.models.find(optionBox =>
      optionBox.optionIds && optionBox.optionIds.includes(optionId)
    );

    if (parentOptionBox) {
      parentBoxId = parentOptionBox.id;
      // Determine the type based on the OptionBox type property
      if (parentOptionBox.type === 0) { // CHOICE = 0
        parentBoxType = 'ChoiceBox';
      } else if (parentOptionBox.type === 1) { // INVESTIGATION = 1
        parentBoxType = 'InvestigationBox';
      } else if (parentOptionBox.type === 2) { // DILEMMA = 2 (assuming)
        parentBoxType = 'DilemmaBox';
      }
    }

    // If not found in OptionBoxes, check if it's a dilemma option
    if (!parentBoxId) {
      // Search through DilemmaBoxes to find which one contains this dilemma ID
      const parentDilemmaBox = this._dilemmaBoxService.models.find(dilemmaBox =>
        dilemmaBox.optionDilemmaIds && dilemmaBox.optionDilemmaIds.includes(optionId)
      );

      if (parentDilemmaBox) {
        parentBoxId = parentDilemmaBox.id;
        parentBoxType = 'DilemmaBox';
      } else {
      }
    }

    // If not found as regular option or dilemma, check if it's a dice failure (StoryBox)
    if (!parentBoxId) {
      // Check if this is a dice failure by finding an option that has this StoryBox as answerBoxNegativeId
      const parentOption = this._optionService.models.find(option =>
        option.answerBoxNegativeId === optionId
      );

      if (parentOption) {
        // Find the parent OptionBox that contains this option
        const parentOptionBox = this._optionBoxService.models.find(optionBox =>
          optionBox.optionIds && optionBox.optionIds.includes(parentOption.id)
        );

        if (parentOptionBox) {
          parentBoxId = parentOptionBox.id;
          // Determine the type based on the OptionBox type property
          if (parentOptionBox.type === 0) { // CHOICE = 0
            parentBoxType = 'ChoiceBox';
          } else if (parentOptionBox.type === 1) { // INVESTIGATION = 1
            parentBoxType = 'InvestigationBox';
          }
        }
      }
    }

    if (!parentBoxId || !parentBoxType) {
      return false; // No parent box found
    }

    // Check if the parent box should be hidden due to roadblocks
    const shouldHide = this.isParentBoxActuallyHidden(parentBoxId, parentBoxType);
    return shouldHide;
  }

  /**
   * Check if a parent box is actually hidden by simulating the same roadblock logic
   * that the parent box leaf component would use
   * @param parentBoxId The ID of the parent box to check
   * @param parentBoxType The type of the parent box (ChoiceBox, InvestigationBox, DilemmaBox)
   * @returns True if the parent box should be hidden
   */
  private isParentBoxActuallyHidden(parentBoxId: string, _parentBoxType: string): boolean {
    if (!parentBoxId) return false;

    // Check if roadblock evaluation is enabled
    if (!this.roadblockEvaluationEnabled) {
      return false; // Roadblock evaluation disabled
    }

    // Get roadblocks for the parent box
    if (!this._dialogueTreeUtility.hasRoadblocks(parentBoxId)) {
      return false; // No roadblocks on parent box, don't hide
    }

    const roadblocks = this._roadBlockService.filterStoryboxForAllRoadblocks(parentBoxId);

    // Get the parent box to check its AND/OR condition
    // All box types (Choice, Investigation, Dilemma) are OptionBox instances
    let parentBox: any;
    let andOrCondition = 'OR'; // Default

    parentBox = this._optionBoxService.svcFindById(parentBoxId);

    // If not found in OptionBoxService, try DilemmaBoxService
    if (!parentBox) {
      parentBox = this._dilemmaBoxService.svcFindById(parentBoxId);
    }

    andOrCondition = parentBox?.AndOrCondition || 'OR';

    if (!parentBox) {
      return false;
    }

    // Use unified roadblock evaluation logic
    return this.evaluateRoadblockConditions(roadblocks, andOrCondition);
  }
}
