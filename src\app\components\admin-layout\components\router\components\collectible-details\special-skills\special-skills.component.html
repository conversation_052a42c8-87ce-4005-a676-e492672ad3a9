<div style="margin-top: 20px; padding-bottom: 100px;" class="card">
      <div class="cardContent" *ngIf="itemCharacter?.rarity && nameClass && type">
        <h3>{{valueBossLevel}} {{'  -'}}&nbsp;</h3> 
          <h3>{{nameClass}} {{'  -'}}&nbsp;</h3>
          <div>
            <div class="titlecard" [style.background-color]="(itemCharacter?.rarity | tierColor: 'Character Rarity')">
                <h3>&nbsp;{{nameRarity}}</h3>
            </div> 
            <div style="display: flex; justify-content: center; padding-bottom: 20px;">
                 <h4>{{listCountRarity}}</h4>
             </div>
          </div>

          <h3>&nbsp;{{'- '}} {{type}}</h3>
    </div>


    <!--List-->
    <div class="container">
        <!-- CATEGORY-->
        <div style="width: 350px !important;;">
            <div class="titleCategory">
                <span>Category</span>
                <i ngClass="iconInter" (click)="onModalClick()" class="pe-7s-info batt"></i>
            </div>
            <!-- Dropdown para seleção -->
            <select class="dropdown filter-dropdown limited selectedCategory" (change)="selectCategoryItem($event)"
                [disabled]="IsMaxCatgeory || listSpecialSkills?.listSpecialSkills.length == 5"
                [ngStyle]="{'cursor': IsMaxCatgeory || listSpecialSkills?.listSpecialSkills.length == 5 ? 'none' : 'pointer'}"
                [class.pulse-red]="isPulsing">
                <option value="default">Select</option>
                <option *ngFor="let cat of listCategory; let i = index" [value]="i" localTemplateVar-i="i">
                    {{ i + 1 }} - {{ cat?.category }}
                </option>
            </select>
            <div style="margin-left: 13px; width: 300px;">
                <ng-container *ngIf="itemSelectedCategory">
                    <div style="display: flex; font-weight: 600; margin-top: 10px; margin-bottom: 6px;">
                        <span>Description</span>
                    </div>
                    <div>
                        <span style="font-weight: 600">{{itemSelectedCategory}}: </span>
                        {{descriptionCatergory}}
                    </div>
                </ng-container>
            </div>
        </div>

        <!-- STATUS EFFECT-->
        <ng-container *ngIf="descriptionCatergory || listSpecialSkills?.listSpecialSkills.length > 0">
            <div style="width: 600px !important;">
                <div class="titleEffect">
                    <span>Status Effect</span>
                    <span>Repetition/ Remaining units</span>
                </div>
                <div class="dropdown-wrapper" (click)="stopPropagation($event)">
                    <!-- Botão para abrir/fechar o dropdown -->
                    <button class="dropdown-toggle" (click)="toggleDropdown()"
                        [ngClass]="{'isDisabled-Status': isStatusSelected  || listSpecialSkills.listSpecialSkills.length == 5}"
                        [disabled]="isStatusSelected  || listSpecialSkills.listSpecialSkills.length == 5"> {{
                        'Select' }}
                    </button>
                    <!-- Menu do dropdown -->
                    <ul *ngIf="dropdownOpen" class="dropdown-menu">
                        <li *ngFor="let status of listDescriptionStatusEffect; let i = index"
                            (click)="selectStatusEffect(i)">
                            <!-- Texto da opção -->
                            <span style="width: 500px;">{{ i + 1 }} - {{ status.description }}</span>
                            <!-- Círculo com positionID -->
                            <span class="circle" *ngIf="selectedCategory.typology !== 'Hybrid' && selectedCategory.category !== 'Combo'">{{
                                getTotalPosition(status) }}</span>
                            <span class="circle">{{ getAmountPosition(status) }}</span>
                        </li>
                    </ul>
                </div>

                <div class="listRepetition">
                    <div style="display: flex; font-weight: 600; margin-top: 10px; margin-bottom: 6px;">
                        <span>Repetition</span>
                    </div>
                    <div style="display: flex; font-weight: 600; margin-top: 10px; margin-bottom: 6px;">
                        <span>Remaining units</span>
                    </div>
                </div>
                <div>
                    <table id="customers" style="width: 100%;  pointer-events: none;">
                        <tr style="background-color: white;"
                            *ngFor="let repetition of listRemainingUnits; let i = index">
                            <td class="repRemain">
                                <span class="spanTitle">{{ i + 1 }}</span>
                                <span class="remaing">{{ repetition.amount}}</span>
                            </td>
                        </tr>
                    </table>
                </div>
                <!-- HYBRID -->
                <div>
                    <table id="customers" style="width: 100%;  pointer-events: none; margin-top: 20px;">
                        <tr style="background-color: white;">
                            <td class="repRemain">
                                <span class="spanTitle fontWeight">Hybrid</span>
                                <span class="remaing">{{ listCalculatedHybrid}}</span>
                            </td>
                        </tr>
                    </table>
                    <table id="customers" style="width: 100%;  pointer-events: none; margin-top: 20px;">
                        <tr style="background-color: white;">
                            <td class="repRemain">
                                <span class="spanTitle fontWeight">Physical</span>
                                <span class="remaing">{{ listCalculatedPhysical}}</span>
                            </td>
                        </tr>
                    </table>
                     <table id="customers" style="width: 100%;  pointer-events: none; margin-top: 20px;">
                        <tr style="background-color: white;">
                            <td class="repRemain">
                                <span class="spanTitle fontWeight">Combo</span>
                                <span class="remaing">{{ listCalculatedCombo}}</span>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </ng-container>






        <!-- SPECIAL SKILL-->
        <ng-container *ngIf="listSpecialSkills?.listSpecialSkills.length > 0">
            <div style="width: 580px !important; margin-left: 40px;">
                <div class="titleCategory" style="margin-bottom: 10px;">
                    <span>Special Skill</span>
                </div>
                <table id="customers" style="width: 100%; cursor: pointer;">
                    <tr *ngFor="let select of listSpecialSkills.listSpecialSkills; let i = index"
                        (click)="returnSkillList(i)">
                        <td>
                            <span style="font-weight: 600; width: 80px;">
                                <ng-container>{{ select?.category }} {{ getCategoryCount(select?.category,
                                    i)}}:</ng-container>
                            </span>
                            {{select.nameStatusEffect}} 🠪 {{select?.descriptionStatusEffect}}
                        </td>
                    </tr>
                </table>
            </div>
        </ng-container>

    </div>

    <!-- Modal Info Special Skills Component -->
    <app-modal-info-special-skills
        [isModalVisible]="isModalInfo"
        (closeModal)="closeModalSpecialSkills()">
    </app-modal-info-special-skills>
</div>