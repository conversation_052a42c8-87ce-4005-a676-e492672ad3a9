<!-- Main container with scrollable content area -->
<div style="overflow-y: auto; height: calc(100% - 50px)">
  <!-- Interactive control panel for user actions -->
  <div class="interactive-controls">
    <!-- Button to clear all user selections -->
    <button (click)="clearSelections()"
            class="btn btn-secondary"
            [disabled]="!hasAnySelections()">
      Clear Selections ({{ getRelevantSelectionsInfo() }})
    </button>

    <!-- Button to toggle roadblock evaluation on/off -->
    <button (click)="toggleRoadblockEvaluation()"
            class="btn"
            [class.btn-warning]="!roadblockEvaluationEnabled"
            [class.btn-outline-secondary]="roadblockEvaluationEnabled">
      Show All: {{ !roadblockEvaluationEnabled ? 'ON' : 'OFF' }}
    </button>

    <!-- Help text explaining how to interact with the dialogue tree -->
    <span style="font-size: 12px; color: #666;">
      Click options (Choice/Investigation/Dilemma) to see the dialogue flow.
    </span>
  </div>

  <!--
    Main dialogue tree layers generated from the dialogue data.
    Each layer represents a dialogue box (StoryBox, OptionBox, DilemmaBox) or special markers (start/end).
    The dialogueTreeLayers pipe transforms the dialogue into a structured array of layers.
  -->
  <dialogue-tree-layer
    layerType="box"
    *ngFor="let box of dialogue | dialogueTreeLayers; let i = index;"
    [boxId]="box.id"
    [layerType]="box.layerType"
    [previousBoxId]="((dialogue | dialogueTreeLayers)[i-1])?.id"
    [isDeadLayer]="box.deadEnd"
    [inPaths]="box.inPaths"
    [outPaths]="box.outPaths"
    [selectedChoiceOptions]="selectedChoiceOptions"
    [selectedInvestigationOptions]="selectedInvestigationOptions"
    [selectedDilemmaOptions]="selectedDilemmaOptions"
    [roadblockEvaluationEnabled]="roadblockEvaluationEnabled"
    [hasPreviousLayerOptionRoadblocks]="hasPreviousLayerOptionRoadblocks(i)"
    (choiceOptionSelected)="onChoiceOptionSelected($event.optionBoxId, $event.optionId)"
    (investigationOptionSelected)="onInvestigationOptionSelected($event.optionBoxId, $event.optionId, $event.selected)"
    (dilemmaOptionSelected)="onDilemmaOptionSelected($event.dilemmaBoxId, $event.dilemmaId)"
    ></dialogue-tree-layer>
</div>
