<div class="content-weapon">
  <div class="card">
    <app-header-with-buttons 
      [cardTitle]="'Memory modules'"
      [cardDescription]="description"
      [rightButtonTemplates]="[excelButtonTemplate]"
      [isBackButtonEnabled]="false">
    </app-header-with-buttons>
  </div>

  <div style="overflow-x: auto;">
    <table class="table table-list">
      <thead class="sticky" style="margin-bottom: 20px;">
        <tr >
          <th style="background-color:rgba(247, 247, 248)!important; border:0px !important" colspan="5"></th>
          <th style="font-size:20px" colspan="3">Memory Creation</th>
          <th style="font-size:20px" colspan="3">MDC Installation</th>
          <th style="background-color:rgba(247, 247, 248)!important; border:0px !important"  [attr.colspan]="particlesNames?.length"></th>
        </tr>
        <tr>
          <th rowspan="3">Quantical Memory Module</th>
          <th rowspan="3">LAB Level UNLOCK (GATING.LAB)</th>
          <th rowspan="3">Available Slots</th>
          <th rowspan="3">Quibits</th>
          <th rowspan="3">Nomenclature</th>
          <th >Cost</th>
          <th >Creation Time</th>
          <th >Skip Price</th>
          <th >Cost</th>
          <th >Installation Time</th>
          <th >Skip Price</th>         
          <th [attr.colspan]="particlesNames?.length"
           >Subatomic Particles</th>
        </tr> 
        <tr>
          <th class="souls-color">Souls</th>
          <th class="time-color">Minutes</th>
          <th class="rubies-color">Rubies</th>
          <th class="gold-color">Gold</th>
          <th class="time-color">Minutes</th>
          <th class="rubies-color">Rubies</th>        
          <th rowspan="2" *ngFor="let particleId of particlesNames; let i = index">
            <!--  (change)="changeParticleOrder(pp.value, i)" -->
            <select class="dropdown filter-dropdown limited"
                style="display: inline-block; margin: 5px;"
                
                id="select" #selectedId (change)="changeElementsPosition(selectedId.value, particleId)"
                >
              <option *ngFor="let p of particlesNames"
              [selected]="p === particleId"
                      value="{{ p }}">{{ p }}</option>
            </select>
          </th>
        </tr>
        <tr >
          <th class="light-gray-color">Requied to create</th>
          <th class="light-gray-color">Time to create</th>
          <th class="light-gray-color">Gems to Skip</th>
          <th class="light-gray-color">Required to install</th>
          <th class="light-gray-color">Time to Install</th>
          <th class="light-gray-color">Gems to Skip</th>
        </tr>   
      </thead>
      <tbody>
        <ng-container *ngFor="let memoryModule of memoryModules; let i = index">
          <tr id="{{ memoryModule.id }}">
            <td class="td-id">{{ (memoryModule.itemId | item).name}}</td>
            <td class="td-id">
              <input
              class="background-input-table-color"
                placeholder=" "
                type="number"
                #InputLabLevel
                [value]="memoryModule.labLevel"
                (change)="changeMemoryValue(memoryModule, InputLabLevel.value, 'labLevel')" />
            </td>
            <td class="td-id">
              <input
              class="background-input-table-color"
                placeholder=" "
                type="number"
                #InputSlots
                [value]="memoryModule.slots"
                (change)="changeMemoryValue(memoryModule, InputSlots.value, 'slots')" />
            </td>
            <td class="td-id">
              <input
              class="background-input-table-color"
                placeholder=" "
                type="number"
                #InputQuibits
                [value]="memoryModule.qubits"
                (change)="changeMemoryValue(memoryModule, InputQuibits.value, 'qubits')"/>
            </td>
            <td class="td-id">
              <input
              class="background-input-table-color"
                placeholder=" "
                type="text"
                #InputNomenclature
                [value]="memoryModule.nomenclature ? memoryModule.nomenclature : ''"
                (change)="changeMemoryValue(memoryModule, InputNomenclature.value, 'nomenclature')" />
            </td>
            <td class="td-id">
              <input
              class="background-input-table-color"
                placeholder=" "
                type="number"
                #InputCreationSoulsCost
                [value]="memoryModule.creationSouls"
                (change)="changeMemoryValue(memoryModule, InputCreationSoulsCost.value, 'creationSouls')" />
            </td>
            <td class="td-id">
              <input
              class="background-input-table-color"
                placeholder=" "
                type="number"
                #InputCreationTimeToCreate
                [value]="memoryModule.creationTime"
                (change)="changeMemoryValue(memoryModule, InputCreationTimeToCreate.value, 'creationTime')" />
            </td>
            <td class="td-id">
              <input
              class="background-input-table-color"
                placeholder=" "
                type="number"
                #InputCreationSkipPrice
                [value]="memoryModule.creationRubies"
                (change)="changeMemoryValue(memoryModule, InputCreationSkipPrice.value, 'creationRubies')"  />
            </td>

            <td class="td-id">
              <input
              class="background-input-table-color"
                placeholder=" "
                type="number"
                #InputInstallationSoulsCost
                [value]="memoryModule.installationSouls"
                (change)="changeMemoryValue(memoryModule, InputInstallationSoulsCost.value, 'installationSouls')"  />
            </td>
            <td class="td-id">
              <input
              class="background-input-table-color"
                placeholder=" "
                type="number"
                #InputInstallationTimeToCreate
                [value]="memoryModule.installationTime"
                (change)="changeMemoryValue(memoryModule, InputInstallationTimeToCreate.value, 'installationTime')"/>
            </td>
            <td class="td-id">
              <input
              class="background-input-table-color"
                placeholder=" "
                type="number"
                #InputInstallationSkipPrice
                [value]="memoryModule.installationRubies"
                (change)="changeMemoryValue(memoryModule, InputInstallationSkipPrice.value, 'installationRubies')" />
            </td>
            <td class="td-id" *ngFor="let particleId of particlesNames; let i = index">
              <input
              class="background-input-table-color"
                placeholder=" "
                type="number"
                #InputParticle
                [value]="GetParticleValue(memoryModule, i)"
                (change)="changeParticleValue(memoryModule, +InputParticle.value, i)" />
            </td>
          </tr>
        </ng-container>
      </tbody>
    </table>

  </div>

</div>
