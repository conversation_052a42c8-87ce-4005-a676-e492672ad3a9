import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';
import { Tag } from './Tag';

export class PowerUpStat
  extends Base<Data.Hard.IPowerUpStat, Data.Result.IPowerUpStat>
  implements Required<Data.Hard.IPowerUpStat>
{
  protected static generateId(index: number): string {
    return IdPrefixes.POWER_UP_STAT + index;
  }

  constructor(
      index: number,
      itemId: string,
      dataAccess: PowerUpStat['TDataAccess']
  )
  {
      super({hard: {id: PowerUpStat.generateId(index), itemId}}, dataAccess);
  }

  public get itemId(): string
  {
    return this.hard.itemId;
  }
  public set itemId(value: string)
  {
    this.hard.itemId = value;
  }

  public get duration(): number
  {
    return this.hard.duration;
  }
  public set duration(value: number)
  {
    this.hard.duration = value;
  }

  public get partnerRegen(): number
  {
    return this.hard.partnerRegen;
  }
  public set partnerRegen(value: number)
  {
    this.hard.partnerRegen = value;
  }

  public get partnerHP(): number
  {
    return this.hard.partnerHP;
  }
  public set partnerHP(value: number)
  {
    this.hard.partnerHP = value;
  }

  public get partnerATK(): number
  {
    return this.hard.partnerATK;
  }
  public set partnerATK(value: number)
  {
    this.hard.partnerATK = value;
  }

  public get partnerDEF(): number
  {
    return this.hard.partnerDEF;
  }
  public set partnerDEF(value: number)
  {
    this.hard.partnerDEF = value;
  }

  public get partnerQI(): number
  {
    return this.hard.partnerQI;
  }
  public set partnerQI(value: number)
  {
    this.hard.partnerQI = value;
  }

  public get partnerLuck(): number
  {
    return this.hard.partnerLuck;
  }
  public set partnerLuck(value: number)
  {
    this.hard.partnerLuck = value;
  }

  public get enemyHP(): number
  {
    return this.hard.enemyHP;
  }
  public set enemyHP(value: number)
  {
    this.hard.enemyHP = value;
  }

  public get enemyATK(): number
  {
    return this.hard.enemyATK;
  }
  public set enemyATK(value: number)
  {
    this.hard.enemyATK = value;
  }

  public get enemyDEF(): number
  {
    return this.hard.enemyDEF;
  }
  public set enemyDEF(value: number)
  {
    this.hard.enemyDEF = value;
  }

  public get enemyQI(): number
  {
    return this.hard.enemyQI;
  }
  public set enemyQI(value: number)
  {
    this.hard.enemyQI = value;
  }

  public get enemyLuck(): number
  {
    return this.hard.enemyLuck;
  }
  public set enemyLuck(value: number)
  {
    this.hard.enemyLuck = value;
  }

  public get enemyIntimidate(): number
  {
    return this.hard.enemyIntimidate;
  }
  public set enemyIntimidate(value: number)
  {
    this.hard.enemyIntimidate = value;
  }

  public get enemySoothe(): number
  {
    return this.hard.enemySoothe;
  }
  public set enemySoothe(value: number)
  {
    this.hard.enemySoothe = value;
  }

  public get enemyInvisibility(): number
  {
    return this.hard.enemyInvisibility;
  }
  public set enemyInvisibility(value: number)
  {
    this.hard.enemyInvisibility = value;
  }

  public get enemyInvulnerability(): number
  {
    return this.hard.enemyInvulnerability;
  }
  public set enemyInvulnerability(value: number)
  {
    this.hard.enemyInvulnerability = value;
  }

  public get enemySlowDown(): number
  {
    return this.hard.enemySlowDown;
  }
  public set enemySlowDown(value: number)
  {
    this.hard.enemySlowDown = value;
  }

}
