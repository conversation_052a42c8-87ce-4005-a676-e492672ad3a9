import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/SortableListComponent';
import { Component } from '@angular/core';
import { Passive } from 'src/app/lib/@bus-tier/models';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { ReviewService } from 'src/app/services/review.service';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { PassiveService, WeaponService } from 'src/app/services';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-passive-generator',
  templateUrl: './passive-generator.component.html',
  styleUrls: ['./passive-generator.component.scss'],
})

export class PassiveGeneratorComponent extends SortableListComponent<Passive> 
{
  constructor(
    _activatedRoute: ActivatedRoute,
    protected _passiveService: PassiveService,
    _userSettingsService: UserSettingsService,
    private _router: Router,
    private _reviewService: ReviewService,
    protected _languageService: LanguageService,
    protected _translationService: TranslationService,
    private _weaponService: WeaponService
  ) 
  {
    super(_passiveService, _activatedRoute, _userSettingsService, 'name');
  }

  public downloadSceneryOrtography(passive: Passive) 
  {
    this._translationService.getPassiveOrtography(passive, true);
  }

  previewDescription(passive: Passive): string 
  {
    return passive.description
      ?.replace(
        /{bonustype}/gi,
        `<span class='pv1'>${passive.bonusType?.toString()}</span>`
      )
      ?.replace(
        /{bonusValue}/gi,
        `<span class='pv2'>${passive.bonusValue?.toString()}</span>`
      )
      ?.replace(
        /{triggerType}/gi,
        `<span class='pv3'>${passive.conditionType?.toString()}</span>`
      )
      ?.replace(
        /{triggerOperator}/gi,
        `<span class='pv4'>${passive.conditionComparator?.toString()}</span>`
      )
      ?.replace(
        /{triggerValue}/gi,
        `<span class='pv5'>${passive.conditionValue?.toString()}</span>`
      )
      ?.replace(
        /{duration}/gi,
        `<span class='pv6'>${passive.conditionDuration?.toString()}</span>`
      );
  }

  async removeFromPassive(passive: Passive) 
  {
    await this._passiveService.svcToRemove(passive.id);
    await this.removePassiveFromWeaponPassivesArray(passive);
    this.ngOnInit();
  }

  async removePassiveFromWeaponPassivesArray(passive: Passive)
  {
    for(let i = 0; i < this._weaponService.models.length; i++)
    {
      for(let j = 0; j < this._weaponService.models[i]?.passives?.length; j++)
      {
        if(this._weaponService.models[i].passives[j] == passive.id)
        {
          this._weaponService.models[i].passives.splice(j, 1);
          await this._weaponService.svcToModify(this._weaponService.models[i]);
          break;
        }
      }
    }
  }
}
