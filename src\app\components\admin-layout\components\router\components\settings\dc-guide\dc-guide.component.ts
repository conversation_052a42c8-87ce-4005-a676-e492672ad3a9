import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { DCGuide } from 'src/app/lib/@bus-tier/models/DCGuide';
import { Button } from 'src/app/lib/@pres-tier/data';
import { FILTER_SUFFIX_PATH } from 'src/lib/darkcloud/angular/dsadmin/constants/others';
@Component({
  selector: 'app-dcGuide',
  templateUrl: './dc-guide.component.html',
  styleUrls: ['./dc-guide.component.scss']
})
export class DCGuideComponent {

  title = "D&D DC Guide";
  activeLanguage = 'PTBR';
  listDCGuide: DCGuide[] = [];
  listDC: DCGuide;
  public activeTab: string;
  listExcel: string[] = [];


  public readonly excelButtonTemplate: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  constructor(      
    private _router: Router,
  ) {}


  public async ngOnInit() {
    const tab = localStorage.getItem(
      `tab-DCGuideComponent${FILTER_SUFFIX_PATH}`
    );
    this.activeTab = tab === 'null' || !tab ? 'attributeGuide' : tab;
  }

    public switchToTab(tab: string) {
      this.activeTab = tab;
      localStorage.setItem(
        `tab-DCGuideComponent${FILTER_SUFFIX_PATH}`,
        this.activeTab
      );
    }
  

  public redirectToSettings() {
    this._router.navigate(['settings']);
  }

  async onExcelPaste() {
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter((line) => line);
    this.listExcel = lines;
  }
  

}
