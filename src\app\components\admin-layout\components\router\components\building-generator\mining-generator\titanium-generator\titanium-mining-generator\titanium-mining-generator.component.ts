import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/SortableListComponent';
import { ChangeDetectorRef, Component } from '@angular/core';
import { Character, TitaniumMining } from 'src/app/lib/@bus-tier/models';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Button } from 'src/app/lib/@pres-tier/data';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { TitaniumMiningService } from 'src/app/services/titanium-mining.service';
import { Alert } from 'src/lib/darkcloud';
import { SpinnerService } from './../../../../../../../../../spinner/spinner.service';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-titanium-mining-generator',
  templateUrl: './titanium-mining-generator.component.html',
})
/**
 * Displays and edits emotion data as a list
 */
export class TitaniumMiningGeneratorComponent extends SortableListComponent<TitaniumMining> {
  constructor(
    private spinnerService: SpinnerService,
    _activatedRoute: ActivatedRoute,
    protected _titaniumMiningService: TitaniumMiningService,
    _userSettingsService: UserSettingsService,
    protected _languageService: LanguageService,
    protected _translationService: TranslationService,
    private ref: ChangeDetectorRef,

  ) {
    super(_titaniumMiningService, _activatedRoute, _userSettingsService, 'name');
  }

  public readonly excelButtonTemplate: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  public downloadSceneryOrtography(character: Character)
  {
    this._translationService.getCharacterOrtography(character, true);
  }

  protected override lstInit()
  {

  }
  description = ""
  protected override lstAfterFetchList()
  {
    this._titaniumMiningService.models;
    if(this._titaniumMiningService.models.length === 0)
    {
      for(let l = 1; l <= 20; l++)
      {
        this._titaniumMiningService.createNewLaboratory(l);
      }
      this._titaniumMiningService.toSave();
      this.lstFetchLists();
    }

        //remove empty element that just has lablevel == 0.
        this._titaniumMiningService.models.find(blueprint => 
          {
            if(blueprint.titaniumLevel === 0)          
            this._titaniumMiningService.svcToRemove(blueprint.id)
          })
        this.description = `Showing ${this._titaniumMiningService.models.length} results`;
  }

  async onExcelPaste(): Promise<void>
  {
    this.spinnerService.setState(true)
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter(line => line);
    
   if(this.DisplayErrors(lines)) return
   
    for(let l = 0; l < lines.length; l++)
    {
      let line = lines[l];
      let cols = line.split(/\t/);
           

      let titaniumMining = this._titaniumMiningService.models.find(tm => tm.titaniumLevel == +(cols[0].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.')));
      if(cols.length < 2) continue//copying from excel create an empty line with lenght is 1.
      if(!titaniumMining)
      {
        titaniumMining = this._titaniumMiningService.createNewLaboratory(+(cols[0].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.')));
      }
      
      if(cols[1]?.trim())
      {
        titaniumMining.souls = +(cols[1].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.'));
      }
      else
      {
        titaniumMining.souls = undefined;
      }
      if(cols[2]?.trim())
      {
        titaniumMining.time = +(cols[2].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.'));
      }
      else
      {
        titaniumMining.time = undefined;
      }
      if(cols[3]?.trim())
      {
        titaniumMining.rubies = +(cols[3].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.'));
      }
      else
      {
        titaniumMining.rubies = undefined;
      }
      if(cols[4]?.trim())
      {
        titaniumMining.storage = +(cols[4].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.'));
      }
      else
      {
        titaniumMining.storage = undefined;
      }
      if(cols[5]?.trim())
      {
        titaniumMining.production = +(cols[5].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.'));
      }
      else
      {
        titaniumMining.production = undefined;
      }

      await  this._titaniumMiningService.svcToModify(titaniumMining);
      await  this._titaniumMiningService.toSave();
      Alert.ShowSuccess('Titanium Mining imported successfully!');

    }
   
    this.lstFetchLists();
    this.ref.detectChanges();
    this.spinnerService.setState(false)
  }

  DisplayErrors(array)
  {
    let count = array[0].split(/\t/)
    if(count.length < 6)
    {
      Alert.showError("Copy the TITANIUM LEVEL column values too!")
      this.spinnerService.setState(false)
      return true
    }
    
    if(count[0] === "")
    {
      Alert.showError("You are probably copying a blank column!")
      this.spinnerService.setState(false)
      return true
    }

    return false
  }
}
