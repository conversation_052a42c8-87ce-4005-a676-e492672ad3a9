<div class="main-content">
    <div class="container-fluid">
        <!--Header-->
        <div class="list-header-row update">
            <div class="card">
                <div class="card-header-content" style="position: absolute; top: 10%;">
                    <h3 class="title">{{ cardTitle }}</h3>
                    <p style="width:60vw;" class="category">{{ description}}</p>
                </div>

                <div style="display: flex; align-items: end; justify-content: end;">
                    <div style="margin-right: 15px; margin-bottom: 16px;">
                        <div id="add"
                            style="display: flex; align-items: flex-end; justify-content: end; margin-right: 10px; position: relative;">
                            <button class="btn btn-success btn-fill ng-star-inserted" (click)="addMoonAttribute()">
                                <i class="pe-7s-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
  
        <table class="table-bordered">
          <thead>
            <tr>
              <th>Index</th>
              <th>Id</th>
              <th *ngFor="let attribute of atributtoClasses; let i = index;">
                {{attribute.atributte}}
              </th>
              <th class="btn-action">Actions</th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let moonAttribute of this.modMoonAttributes; let i = index;">
                <tr id="{{ moonAttribute.id }}">
                    <td class="td-sort gray">{{ i + 1 }}</td>
                    <td class="td-id">{{ moonAttribute.id }}</td>
                    <!-- Uma coluna para cada atributo da lista ordenada -->
                    <td class="td-notes" *ngFor="let attribute of atributtoClasses; let attrIndex = index;">
                        <ng-container *ngFor="let idAtributte of moonAttribute.idsAtributte; let e = index;">
                            <input *ngIf="idAtributte === attribute.id" class="form-control" type="text" [value]="moonAttribute.modMoonAtributte[e]" #modMoonAtributte
                                   [ngStyle]="{'background-color': +modMoonAtributte.value <= 0 ? '#404040' : '#ffffff'}"
                                   (change)="moonAtributteChange(i, modMoonAtributte.value, e)" />
                        </ng-container>                 
                    </td>
                    <!--ACTIONS-->
                    <td class="td-actions">
                        <button class="btn btn-danger btn-fill btn-remove"
                                (click)="removeElement(moonAttribute.id)">
                            <i class="pe-7s-close"></i>
                        </button>
                    </td>
                </tr>
            </ng-container>
          </tbody>

        </table>

            <ng-container *ngIf="modMoonAttributes.length === 0">
                <div class="card" style="text-align: center; padding: 20px;">
                    <h3>Empty list. Click to create the list.</h3>
                </div>
            </ng-container>

    </div>
</div>