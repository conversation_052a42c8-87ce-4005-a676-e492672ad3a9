import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { DiceChoicePopupService, DiceChoiceState, DiceChoiceOption } from '../../../services/dice-choice-popup.service';

/**
 * Dice choice popup component that presents users with three options before a dice roll:
 * - Positive: Guaranteed success outcome
 * - Negative: Guaranteed failure outcome
 * - Roll Dice: Proceed with actual dice roll to determine outcome
 *
 * This gives players control over risk vs. certainty in dialogue choices.
 */
@Component({
  selector: 'app-dice-choice-popup',
  templateUrl: './dice-choice-popup.component.html',
  styleUrls: ['./dice-choice-popup.component.scss']
})
export class DiceChoicePopupComponent implements OnInit, OnDestroy {
  // Current state of the choice popup
  public choiceState: DiceChoiceState = {
    isVisible: false
  };

  // Expose enum to template for use in HTML
  public DiceChoiceOption = DiceChoiceOption;

  private subscription: Subscription = new Subscription();

  constructor(private diceChoicePopupService: DiceChoicePopupService) { }

  /**
   * Initialize component and subscribe to choice state changes
   */
  ngOnInit(): void {
    // Subscribe to choice state updates from the service
    this.subscription = this.diceChoicePopupService.choiceState$.subscribe(
      state => {
        this.choiceState = state;
      }
    );
  }

  /**
   * Clean up subscriptions when component is destroyed
   */
  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  /**
   * Handle user's choice selection and pass it to the service
   * @param choice The choice made by the user (positive/negative/roll dice)
   */
  onChoiceSelected(choice: DiceChoiceOption): void {
    this.diceChoicePopupService.makeChoice(choice);
  }

  /**
   * Handle popup dismissal without making a choice
   */
  onClose(): void {
    this.diceChoicePopupService.hideChoicePopup();
  }
}
