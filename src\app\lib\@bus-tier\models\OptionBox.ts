import { IdPrefixes, OptionBoxType } from 'src/lib/darkcloud/dialogue-system';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { Box } from './Box';

export class OptionBox
  extends Box<Data.Hard.IOptionBox, Data.Result.IOptionBox>
  implements Required<Data.Hard.IOptionBox>
{
  public static generateId(parentId: string, index: number): string 
  {
    return parentId + '.' + IdPrefixes.OPTIONBOX + index;
  }

  constructor(
    index: number,
    parentId: string,
    type: OptionBoxType,
    dataAccess: OptionBox['TDataAccess']
  ) 
  {
    super(
      {
        hard: 
        {
          id: OptionBox.generateId(parentId, index),
          optionIds: [],
          type,
        },
      },
      dataAccess
    );
  }
  protected getInternalFetch() {
    return {};
  }
  public get type(): OptionBoxType {
    return this.hard.type;
  }
  public set type(value: OptionBoxType) {
    this.hard.type = value;
  }
  public get optionIds(): string[] {
    return this.hard.optionIds;
  }
  public set optionIds(value: string[]) {
    this.hard.optionIds = value;
  }
  public get label(): string {
    return this.hard.label;
  }
  public set label(value: string) {
    this.hard.label = value;
  }
  public get AndOrCondition(): string {
    return this.hard.AndOrCondition;
  }
  public set AndOrCondition(value: string) {
    this.hard.AndOrCondition = value;
  }
}
