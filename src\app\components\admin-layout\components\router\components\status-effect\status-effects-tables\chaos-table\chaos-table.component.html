<div class="div-container">
    <div class="width: 2221px;">
        <table class="table table-list borderList">
            <thead>    
                <tr>
                    <th [attr.colspan]="titles.length +1">
                        <h3>CHAOS</h3>
                    </th>
                    <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'" class="addButton"
                        [buttonTemplates]="[excelButtonTemplate]">
                    </app-button-group>
                </tr>
                <ng-container *ngIf="!isListChaosTableEmpty">
           
                    <tr>
                        <th class="default-color">Order</th>
                        <th class="default-color" *ngFor="let title of titles">{{title}}</th>
                      </tr>
                      
                </ng-container>
            </thead>
            <ng-container *ngIf="!isListChaosTableEmpty">
                <tbody>
                    <tr *ngFor="let item of listChaosTable; let i = index">
                        <td style="background-color: #ddd; width: 4%;">{{ i + 1 }}</td>               
                            <td class="td-id aligTitle" style="width: 11%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #idChaosTable [ngClass]="{'empty-input': !idChaosTable.value}"
                                    [value]="item.idChaosTable" (change)="changeChaos(i,'idChaosTable', idChaosTable.value)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 9%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #idCategory [ngClass]="{'empty-input': !idCategory.value}"
                                    [value]="item.category" (change)="changeChaos(i,'category', idCategory.value)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 11%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #statusEffectName [ngClass]="{'empty-input': !statusEffectName.value}"
                                [value]="item.statusEffectName" (change)="changeChaos(i, 'statusEffectName', statusEffectName.value)" />
                            </td>
                            <td class="td-id" style="width: 30%; word-break: break-word;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #description [ngClass]="{'empty-input': !description.value}"
                                    [value]="item.description" (change)="changeChaos(i, 'description', description.value)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 11%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #target [ngClass]="{'empty-input': !target.value}"
                                    [value]="item.target" (change)="changeChaos(i, 'target', target.value)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 11%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #powerPoints [ngClass]="{'empty-input': !powerPoints.value}"
                                    [value]="item.powerPoints" (change)="changeChaos(i, 'powerPoints', powerPoints.value)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 11%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #allChaosTable [ngClass]="{'empty-input': !allChaosTable.value}"
                                    [value]="item.allChaosTable" (change)="changeChaos(i, 'allChaosTable', allChaosTable.value)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 11%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #duration [ngClass]="{'empty-input': !duration.value}"
                                    [value]="item.duration" (change)="changeChaos(i, 'duration', duration.value)" />
                            </td>             
                    </tr>
                </tbody>
            </ng-container>
            <ng-container *ngIf="isListChaosTableEmpty">
                <div class="card" style="text-align: center; padding: 20px;">
                    <h3>The list has not yet been imported.</h3>
                </div>
            </ng-container>
        </table>
</div>
    
</div>

