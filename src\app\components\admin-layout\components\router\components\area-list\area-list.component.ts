import { Component } from '@angular/core';
import { Area } from 'src/app/lib/@bus-tier/models';
import { Button } from 'src/app/lib/@pres-tier/data';
import { CharacterService, LevelService, MinionStatsService, StoryExpansionPkgService } from 'src/app/services';
import { AreaService } from 'src/app/services/area.service';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationCheckService } from 'src/app/services/translation-check.service';
import { TranslationService } from 'src/app/services/translation.service';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Alert } from 'src/lib/darkcloud';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';
import { LevelType } from 'src/lib/darkcloud/dialogue-system';
import { language } from 'src/lib/darkcloud/dialogue-system/game-types';
import { fadeIn, popup } from '../bound-item-list/bound-list.component.animations';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-area-list',
  templateUrl: './area-list.component.html',
  styleUrls: ['./area-list.component.scss'],
  animations: [fadeIn, popup]
})

export class AreaListComponent extends TranslatableListComponent<Area> 
{
  
  popupStats: boolean = false;
  popupArea: Area;
   public language: language = 'PT-BR';
  public totalLevels = 0;
  public totalWords = 0;
  public wordsPerLevel = 0;
  public imNames = 0;
  public imNamesPercent = 0;
  public imDescription = 0;
  public imDescriptionPercent = 0;

  public levelsPerArea = '';
  public wordsPerArea = '';
  public imNamesPerArea = '';
  public imDescriptionPerArea = '';
  public currentAreaName = '';

  public totalLevelsArea = 0;
  public totalWordsArea = 0;
  public wordsPerLevelArea = 0;
  public imNamesArea = 0;
  public imNamesPercentArea = 0;
  public imDescriptionArea = 0;
  public imDescriptionPercentArea = 0;
  
  public currentAreaIndex = 0;	
  public countMinionLevel = 0;
  public countBossLevel = 0;
  public countSecondaryLevel = 0;
  public counTransitionLevel = 0;
  public counMinigameLevel = 0;

  public countMinionTotal = 0;
  public countBossTotal = 0;
  public countSecondaryTotal = 0;
  public countTransitionTotal = 0;
  public countMinigameTotal = 0;

  public percentageMinionLevel;
  public percentageBossLevel;
  public percentageSecondaryLevel;
  public percentageTransitionLevel;
  public percentageMinigameLevel;


  constructor(
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    public _minionStatsService: MinionStatsService,
    private _areaService: AreaService,
    private _levelService: LevelService,
    private _characterService: CharacterService,
    private _translationCheckService: TranslationCheckService,
    protected override _translationService: TranslationService,
    protected override _languageService: LanguageService,
    private _router: Router,
    private _storyExpansionService: StoryExpansionPkgService
  ) 
  {
    super(_areaService, _activatedRoute, _userSettingsService, 'name', _translationService, _languageService);
  }

  public readonly addAreaTemplate: Button.Templateable = 
  {
    title: 'Add a new instance to the list',
    onClick: this.addArea.bind(this),
    iconClass: 'pe-7s-plus',
    btnClass: Button.Klasses.FILL_GREEN,
  };

  async addArea()
  {
    const name = await Alert.showPrompt('Area Name');
    if(this.isAreaAlreadyCreated(name))
    {
      Alert.showError(`The area: ${name} already exists!`);
      return;
    }
    else if(name == '')
    {
      Alert.showError('You need to give a name to the area!');
      return;
    }
    else if(name == undefined) return;
    let newArea = await this._areaService.svcPromptCreateNew(name);
    this.lstToAdd(newArea, this._areaService.svcNextIndex());
    let newLevel = await this._levelService.svcPromptCreateNew((newArea).id);
    this._areaService.addLevel(newArea, newLevel.id);
    this._levelService.srvAdd(newLevel, this._levelService.svcNextIndex());
  }

  isAreaAlreadyCreated(areaName: string)
  {
    for(let i = 0; i < this._areaService.models.length; i++)
    {
      if(this._areaService.models[i]?.name == areaName)
      {
        return true;
      }
    }
    return false;
  }

  public getAreaOrtography(area: Area)
  {
    this._translationService.getAreaOrtography(area, true);
  }

  override lstInit()
  {
    this._translationCheckService.checkAll();

  this._areaService.models.forEach((area) => {
    area.isReviewedName = !!area.isReviewedName;
    area.isReviewedDescription = !!area.isReviewedDescription;
    this._areaService.svcToModify(area);
  });
    console.log('Area list initialized');
  }

  public checkAreaTranslation(area: Area)
  {
    try
    {
      return this._translationCheckService.checkHierarchicalTranslation(area.id);
    }
    catch
    {
      this._translationCheckService.checkAll();
    }
    return null;
  }


  areaStatsPopup(area: Area, index: number)	
  {
    this.currentAreaName = area.name;	
    this.currentAreaIndex = index;
    if(this.popupStats) return    

    this.countMinionLevel = 0;
    this.countBossLevel = 0;
    this.countSecondaryLevel = 0;
    this.counTransitionLevel = 0;
    this.counMinigameLevel = 0;
    this.countMinionTotal = 0;
    this.counTransitionLevel = 0;
    this.countMinigameTotal = 0;
    this.countBossTotal = 0;
    this.countSecondaryTotal = 0
    this.countMinigameTotal = 0


    let globalStats = this._areaService.svcGlobalStats();
    let areaStats = this._areaService.svcAreaStats(area);

    this.totalLevels = globalStats.totalLevels;
    this.totalWords = globalStats.totalWords;
    this.wordsPerLevel = Math.round(globalStats.wordsPerLevel);
    this.imNames = globalStats.imNames;
    this.imNamesPercent = globalStats.imNamesPercent;
    this.imDescription = globalStats.imDescription;
    this.imDescriptionPercent = globalStats.imDescriptionPercent;

    this.levelsPerArea = (Math.round(globalStats.levelsPerArea * 100) / 100).toString().replace(',','-').replace('.',',').replace('-','.');
    this.wordsPerArea = (Math.round(globalStats.wordsPerArea * 100) / 100).toString().replace(',','-').replace('.',',').replace('-','.');
    this.imNamesPerArea = (Math.round(globalStats.imNamesPerArea * 100) / 100).toString().replace(',','-').replace('.',',').replace('-','.');
    this.imDescriptionPerArea = (Math.round(globalStats.imDescriptionPerArea * 100) / 100).toString().replace(',','-').replace('.',',').replace('-','.');

    this.totalLevelsArea = areaStats.totalLevelsArea;
    this.totalWordsArea = areaStats.totalWordsArea;
    this.wordsPerLevelArea = Math.round(areaStats.wordsPerLevelArea);
    this.imNamesArea = areaStats.imNamesArea;
    this.imNamesPercentArea = areaStats.imNamesPercentArea;
    this.imDescriptionArea = areaStats.imDescriptionArea;
    this.imDescriptionPercentArea = areaStats.imDescriptionPercentArea;

    this._levelService.models.forEach(level => 
    {
      if(level.type == LevelType.BOSS)
      {
        this.countBossTotal++;
      }
      else if(level.type == LevelType.MINION)
      {
        this.countMinionTotal++;
      }
      else if(level.type == LevelType.DEFAULT)
      {
        this.countSecondaryTotal++;
      }
      else if(level.type == LevelType.TRANSITION)
      {
        this.countTransitionTotal++;
      }
      else if(level.type == LevelType.MINIGAME)
      {
        this.countMinigameTotal++;
      }
    });

    const levels = this._levelService.svcFilterByIds(area.levelIds);

    levels.forEach(level => 
    {

      if(level.type == LevelType.BOSS)
      {
        this.countBossLevel++;
      }
      else if(level.type == LevelType.MINION)
      {
        this.countMinionLevel++;
      }
      else if(level.type == LevelType.DEFAULT)
      {
        this.countSecondaryLevel++;
      }
      else if(level.type == LevelType.TRANSITION)
      {
        this.counTransitionLevel++;
      }
      else if(level.type == LevelType.MINIGAME)
      {
        this.counMinigameLevel++;
      }
    });

    if(levels.length == 0)
    {
      this.percentageMinionLevel = 0;
      this.percentageBossLevel = 0;
      this.percentageSecondaryLevel = 0;
      this.percentageTransitionLevel = 0;
      this.percentageMinigameLevel = 0;
    }
    else
    {
      this.percentageBossLevel = (Math.round((this.countBossLevel / levels.length) * 100)).toString().replace(',','-').replace('.',',').replace('-','.');
      this.percentageSecondaryLevel = (Math.round((this.countSecondaryLevel / levels.length) * 100)).toString().replace(',','-').replace('.',',').replace('-','.');
      this.percentageTransitionLevel = (Math.round((this.counTransitionLevel / levels.length) * 100)).toString().replace(',','-').replace('.',',').replace('-','.');
      this.percentageMinigameLevel = (Math.round((this.counMinigameLevel / levels.length) * 100)).toString().replace(',','-').replace('.',',').replace('-','.');
      this.percentageMinionLevel = (Math.round((this.countMinionLevel / levels.length) * 100)).toString().replace(',','-').replace('.',',').replace('-','.');
    }
 
    this.popupArea = area;
    this.popupStats = true;
  }
  	
  moveNext(next: boolean) 
  {	
    const areas = this._areaService.svcCloneByIds(this.lstIds);	
    	
    if (next) { this.currentAreaIndex = (this.currentAreaIndex + 1) % areas.length; } 	
    else { this.currentAreaIndex = (this.currentAreaIndex - 1) % areas.length; }

    const nextArea: Area = areas[this.currentAreaIndex];	
    this.closeAreaStatsPopup();	
    this.areaStatsPopup(nextArea, this.currentAreaIndex);	
  }	

  handleOutsideMouseClick(event: MouseEvent)	
  {	
    if(!this.popupStats) return;	
    const myDiv = document.getElementById("modal-close");

    // Get the position relative to the viewport
    const rect = myDiv.getBoundingClientRect();
    const top = rect.top;
    const left = rect.left;
    //Check the x axis
    if(event.clientX < left || event.clientX > left + myDiv.offsetWidth)
    {
      this.closeAreaStatsPopup();
    }
    else if(event.clientY > top + myDiv.offsetHeight || event.clientY < top)
    {
      this.closeAreaStatsPopup();
    }
  }

  closeAreaStatsPopup()
  {
    this.popupStats = false;
  }

  changeAreaName(area:Area, fieldName, value:string)
  {
    if(this.isAreaAlreadyCreated(value))
    {
      Alert.showError(`The area: ${value} already exists!`);
      this.updatePage();
      return;
    }
    area.isReviewedName = false;
    area.revisionCounterNameAI = 0;
    this.lstOnChange(area, fieldName, value);
  }

  changeDescription(area:Area, fieldName, value:string)
  {
    area.isReviewedDescription = false;
    area.revisionCounterDescriptionAI = 0;
    this.lstOnChange(area, fieldName, value);
  }
  
  updatePage()	
  {	
    this._router.navigate(['levels']);	
    setTimeout(()=>	
    {	
      this._router.navigate(['areas']);	
    },200)	
  }	

  
  async changeMinionStatsHC(area:Area, value:string)
  {
    for(let i = 0; i < this._minionStatsService.models.length; i++)
    {
      if(area.id == this._minionStatsService.models[i].area)
      {
        this._minionStatsService.models[i].hc = value == '' ? undefined : +value;
        await this._minionStatsService.svcToModify(this._minionStatsService.models[i]);
        await this._minionStatsService.toSave();
      }
    }
    this.lstOnChange(area, 'order', value);
  }

  async removeArea(area:Area)
  {
    await this.removeStoryexpansion(area);
    await this.removeMinionStatsHC(area);
    this.lstPromptRemove(area);  
  }

  async removeStoryexpansion(area:Area)
  {
    for(let i = 0; i < this._storyExpansionService.models.length; i++)
    {
      if(area.id == this._storyExpansionService.models[i].area)
      {
        await this._storyExpansionService.svcToRemove(this._storyExpansionService.models[i].id);
      }
    }
  }
  
  async removeMinionStatsHC(area:Area)
  {
    for(let i = 0; i < this._minionStatsService.models.length; i++)
    {
      if(this._minionStatsService.models[i].area == area.id)
      {
        this._minionStatsService.models[i].hc = undefined;
        await this._minionStatsService.svcToModify(this._minionStatsService.models[i]);
        await this._minionStatsService.toSave();
      }
    }
  }
}
