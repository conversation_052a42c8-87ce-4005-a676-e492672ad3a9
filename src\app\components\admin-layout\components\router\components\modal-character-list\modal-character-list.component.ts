import { Component, EventEmitter,Output, Input, OnInit } from '@angular/core';
import { fadeIn, popup } from '../bound-item-list/bound-list.component.animations';
import { Character } from 'src/app/lib/@bus-tier/models';
import { Result } from 'src/lib/darkcloud/review/result';

@Component({
  selector: 'app-modal-character-list',
  templateUrl: './modal-character-list.component.html',
  styleUrls: ['./modal-character-list.component.scss'],
  animations: [fadeIn, popup]
  
})
export class ModalCharacterListComponent implements OnInit  {

  popupStats: boolean;
  typeBattle: string;
  character: Character;
  asBattleCharacter: Result;

  @Input('mCharacter') public listBattle: any;
  @Output() onAlert = new EventEmitter();

  ngOnInit(): void {
    this.popupStats = true;    
  }

  closeAreaStatsPopup()
  {
    this.popupStats = !this.popupStats;
  }

  public alertEvent(e: boolean) {
    this.onAlert.emit(e);
  }

  handleOutsideMouseClick(event: MouseEvent)	
  {	
    if(!this.popupStats) return;	
    const myDiv = document.getElementById("modal-close");

    // Get the position relative to the viewport
    const rect = myDiv.getBoundingClientRect();
    const top = rect.top;
    const left = rect.left;
    //Check the x axis
    if(event.clientX < left || event.clientX > left + myDiv.offsetWidth)
    {
      this.closeAreaStatsPopup();
    }
    else if(event.clientY > top + myDiv.offsetHeight || event.clientY < top)
    {
      this.closeAreaStatsPopup();
    }
  }

}
