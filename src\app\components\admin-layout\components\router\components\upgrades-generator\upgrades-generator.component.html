
<div class="card list-header"
style="height: 70px; margin: 15px 30px 0;">
  <div class="header">
    <button class="{{activeTab === 'class-selection' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
            (click)="switchToTab('class-selection')">
      1 - Item Class
    </button>
    <button class="{{activeTab === 'upgrades' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
            (click)="switchToTab('upgrades')" style="margin-left: 5px;">
      2 - Upgrades
    </button>
  </div>
</div>

<app-upgrades-class-selection *ngIf="activeTab === 'class-selection'"> </app-upgrades-class-selection>
<app-upgrades-information *ngIf="activeTab === 'upgrades'"> </app-upgrades-information>
