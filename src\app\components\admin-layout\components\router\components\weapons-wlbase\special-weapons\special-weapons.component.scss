// Professional info note styling
.info-note-professional {
    margin: 0px 10px 20px 10px;
    padding: 0;
    width: fit-content;

    .info-note-content {
        background: linear-gradient(135deg, #e3f2fd 0%, #f8f9fa 100%);
        border: 1px solid #17a2b8;
        border-left: 4px solid #17a2b8;
        border-radius: 6px;
        padding: 12px 16px;
        box-shadow: 0 2px 4px rgba(23, 162, 184, 0.1);
        display: flex;
        align-items: center;
        transition: all 0.3s ease;

        &:hover {
            box-shadow: 0 4px 8px rgba(23, 162, 184, 0.15);
            transform: translateY(-1px);
        }

        .info-icon {
            color: #17a2b8;
            font-size: 18px;
            margin-right: 12px;
            flex-shrink: 0;
        }

        .info-text {
            color: #2c3e50;
            font-size: 15px;
            font-weight: 500;
            line-height: 1.4;
            margin: 0;
        }
    }
}
.content-weapon
{
  margin: 25px;
}

.content
{
  overflow-x: auto;
  bottom: 0px;
  position: fixed;
  overflow-y: auto;
  top: 340px;
  right: 20px;
  left: 220px;
  margin-left: 15px;
  margin-right: 15px;
}

.m-cammon {
 margin-left: 15px;
 margin-right: 15px;
}

table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
}

th, td {
    
    text-align: center;
    border: 1px solid #2f2f2f;
}
th {
    background-color: #2f2f2f;
    color: white;
    padding: 10px;
    font-weight: 400 !important;
}

hr {
    margin-top: 0px; 
    margin-bottom: 0px;
    border: 0;
    border-top: 1px solid #2f2f2f;  
}

p {
    font-size: 14px !important;
}

.light-blue-bg {
    background-color: #00d8ff !important;
   // border-color: #00d8ff !important;
    color: white !important;
    font-weight: bold;
}
.white-bg {
    background-color: white;
    color: black;
}

.green-bg {
    background: rgb(0, 176, 80);
    color: white;
}

.primary-bg {
    background-color: #0070c0; 
    color: white;
}

.purple-bg {
    background: rgb(112, 48, 160);
    color: white;
}

.yellow-bg {
    background: rgb(255, 192, 0);
    color: white;
}

.light-blue-bg {
    background-color: lightblue;
}

.gray-bg {
    background-color: #e6e6e6;
}

