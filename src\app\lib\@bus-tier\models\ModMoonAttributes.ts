import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { Base } from './Base';

export class ModMoonAttributes  extends Base<Data.Hard.IModMoonAttributes, Data.Result.IModMoonAttributes> implements Required<Data.Hard.IModMoonAttributes>
{
  public static generateId(index: number): string 
  {
    return IdPrefixes.MODMOONATTRIBUTES+ index;
  }

  constructor( index: number, dataAccess: ModMoonAttributes['TDataAccess']) 
  {
    super(
      {
        hard: 
        {
          id: ModMoonAttributes.generateId(index),    
        },
      },
      dataAccess
    );
  }

  protected getInternalFetch() 
  {
    return {};
  }

  public get idsAtributte(): string[]
  {
    return this.hard.idsAtributte;
  }
  public set idsAtributte(value: string[]) 
  {
    this.hard.idsAtributte = value;
  }

  public get modMoonAtributte(): string[]
  {
    return this.hard.modMoonAtributte;
  }
  public set modMoonAtributte(value: string[]) 
  {
    this.hard.modMoonAtributte = value;
  }


}
