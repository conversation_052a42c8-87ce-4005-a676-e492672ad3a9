import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { Item, Upgrades } from 'src/app/lib/@bus-tier/models';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';
import { ItemService, ParticleService, UpgradesService } from 'src/app/services';
import { CustomService } from 'src/app/services/custom.service';
import { ItemClassService } from 'src/app/services/item-class.service';
import { Button } from 'src/app/lib/@pres-tier/data';
import { Alert } from 'src/lib/darkcloud';
import { SpinnerService } from './../../../../../../../spinner/spinner.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-upgrades-information',
  templateUrl: './upgrades-information.component.html',
  styleUrls: ['./upgrades-information.component.scss'],
})
export class UpgradesInformationComponent implements OnInit, OnD<PERSON>roy
{
  custom: Custom;
  item: Item;
  upgrades: Upgrades[];
  subatomicParticles: Item[];
  description: string;
  ids = [];
  selectedClasses = [];
  private refreshInterval: any;

  constructor(
    private spinnerService: SpinnerService,
    protected _customService: CustomService,
    protected _itemService: ItemService,
    private _particleService: ParticleService,
    private _upgradesService: UpgradesService,
    private _itemClassService: ItemClassService,
    private _router: Router
  ) {}

  public readonly excelButtonTemplate: Button.Templateable = 
  {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  async ngOnInit(): Promise<void> 
  {
    await this._customService.toFinishLoading();
    await this._particleService.toFinishLoading();
    await this._itemService.toFinishLoading();
    await this._itemClassService.toFinishLoading();
    await this._upgradesService.toFinishLoading();

    this.custom = await this._customService.svcGetInstance();

    if (!this.custom.particlesOrder) this.custom.particlesOrder = [];
    
    this.requireSubparticles();
    this.selectClasses();
    this.ingredientsOrderInitialization()
    this.description = `Showing ${this.upgrades.length} results`;

    // Set up periodic refresh to detect changes in item class selection
    this.refreshInterval = setInterval(async () => {
      await this.refreshData();
    }, 1000);
  }

  ngOnDestroy(): void {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
    }
  }

  async refreshData(): Promise<void> {
    const updatedCustom = await this._customService.svcGetInstance();
    if (JSON.stringify(this.custom.upgradesClassItem) !== JSON.stringify(updatedCustom.upgradesClassItem)) {
      this.custom = updatedCustom;
      this.selectClasses();
      this.description = `Showing ${this.upgrades.length} results`;
    }
  }

  requireSubparticles()
  {    
    this.subatomicParticles = [];
    //item class pkg has a sub atomics particles array by id = IC0
    this._itemClassService.svcFindById('IC0')?.itemIds?.forEach((itemId) => 
    {
      this.subatomicParticles.push(this._itemService.svcFindById(itemId));
    });
  }
   
  selectClasses()
  {
    this.selectedClasses = this._itemClassService.models.filter((klass) =>
    {
      return this.custom.upgradesClassItem && this.custom.upgradesClassItem.includes(klass.id);
    });
    let itemIds = [];    
    
    this.selectedClasses.forEach((itemClass) => 
    {
      itemClass.itemIds.forEach((itemId) => 
      {
        itemIds.push(this._itemService.svcFindById(itemId));
      });
    });
    
    this.ids = [];
    itemIds.forEach((id) => 
    {
      this.ids.push(id?.id)
    });

    this.upgrades = [];

    this._upgradesService.models.forEach((sw) =>
    {
      if(this.ids.includes(sw.itemId))
      {
        this.upgrades.push(sw)
        this.ids = this.ids.filter(id => id !== sw.itemId)
      }     
    });
    
    this.ids.forEach(id => 
    {      
      this.upgrades.push(this._upgradesService.createNewUpgrades(id))
    })

    this._upgradesService.toSave();
    this.upgrades.concat(this._upgradesService.models)
  }

  async ingredientsOrderInitialization()
  {
    if(this.upgrades[0]?.ingredientsOrder?.length !== this.subatomicParticles.length || !this.upgrades[0]?.ingredientsOrder)
    {
      let ids : string[] = [];
      this.subatomicParticles.forEach((item) => ids.push(item.id));

      this.upgrades.forEach(sw=> sw.ingredientsOrder = ids);
      await this._upgradesService.toSave();
    }
    else
    {
      let ids = this.upgrades[0].ingredientsOrder;
      this.subatomicParticles.sort((a,b)=>
      {
        return ids.indexOf(a.id) - ids.indexOf(b.id);
      })
      await this._upgradesService.toSave();
    }
  }

  async changeElementsPosition( newElementName, oldElementName)
  {
    Alert.showError('','There is another value with the same name. Change it to avoid repetition');

    let newElementHolder;
    let oldElementHolder;

    let ids = this.upgrades[0].ingredientsOrder;
    newElementName = this.subatomicParticles.filter(i=> 
      this.simplifyString(i.name) == this.simplifyString(newElementName));
    newElementName = newElementName[0].id;

    for(let l = 0; l < ids.length; l++)
    {
      if(ids[l] === newElementName) newElementHolder = l;
      if(ids[l] === oldElementName) oldElementHolder = l;      
    }
    
    let oldElement = ids[oldElementHolder];
    let newElement = ids[newElementHolder];

    ids.splice(newElementHolder, 1, oldElement);
    ids.splice(oldElementHolder, 1, newElement);
    
    this.upgrades.forEach(sw=> sw.ingredientsOrder = ids);
    
    this.subatomicParticles.sort((a,b)=>
    {
      return ids.indexOf(a.id) - ids.indexOf(b.id);
    });
    
    await this._upgradesService.toSave();
  }

  GetSpecialWeaponName(memoryModuleId: string): string 
  {
    return this._itemService.svcFindById(this._upgradesService.svcFindById(memoryModuleId).itemId)?.name;
  }

  changeField(upgrades:Upgrades, value:string, fieldName:string)
  {
    upgrades[fieldName] = +value;
    this._upgradesService.svcToModify(upgrades);
    this._upgradesService.toSave();
  }

  GetAmountValue(upgrades: Upgrades, index: number) 
  {
    if (!upgrades.ingredientsAmount) upgrades.ingredientsAmount = [];
    return upgrades.ingredientsAmount[index];
  }

  changeIngredientAmount(upgrades: Upgrades, amount: number, index: number) 
  {
    upgrades.ingredientsAmount[index] = amount;
    this._upgradesService.svcToModify(upgrades);
    this._upgradesService.toSave();
  }

  async onExcelPaste(): Promise<void> 
  {
    this.spinnerService.setState(true);

    const text = await navigator.clipboard.readText();
  
    const lines = text.split(/\r?\n/).filter(line => line);
    let lastHellCircle:number = 0;
    let upgradesFields: string[] = 
      ['hellCicle', 'buildingEfficiency', '', 'workshopLevelUnlock', 'newEfficiency', 'efficiencyBoost', 'souls', 'minutes', 'rubies'];
    //if(this.displayErrors(lines)) return

    for (let l = 0; l < lines.length; l++) 
    {
      let line = lines[l];
      let cols = line.split(/\t/);

      let item = this._itemService.models.find((i) => this.simplifyString(i.name) == this.simplifyString(cols[2]));

      if (!item) continue;
      let upgrades = this.upgrades.find((i) => i.itemId == item.id);
      
      if (!upgrades) continue;

      for(let i = 0; i < upgradesFields.length; i++)
      {
        if(upgradesFields[i] != '')//Avoid upgrades column
        {
          if(i == 0 && upgrades[upgradesFields[i]] == undefined)//repeat hell circle value
          {
            upgrades[upgradesFields[i]] = lastHellCircle;
          }
          else if (cols[i]?.trim()) 
          {
            if(i == 0) lastHellCircle = +cols[i];
            upgrades[upgradesFields[i]] = +cols[i]
            .split(' ')
            .join('')
            .split('.')
            .join('')
            .replace(',','.')
          } 
          else upgrades[upgradesFields[i]] = undefined;
        }
      }
      //Fill the subatomic particles
      for (let i = 9; i < cols.length; i++) 
      {
        if (cols[i]?.trim()) 
        {
          upgrades.ingredientsAmount[i - 9] = +cols[i]
          .split(' ')
          .join('')
          .split('.')
          .join('')
          .replace(',','.')
        } 
        else upgrades.ingredientsAmount[i - 9] = undefined;         
      }

      await  this._upgradesService.svcToModify(upgrades);
      await  this._upgradesService.toSave();
      Alert.ShowSuccess('Upgrades imported successfully!');
      this.spinnerService.setState(false)
      this.description = `Showing ${this.upgrades.length} results`;
    }
  }

  displayErrors(array)
  {
    let count = array[0].split(/\t/)
     if(Number.isNaN(+count[0]))
    {
      Alert.showError("Copy the BLUEPRINT LEVEL column values too!")
      this.spinnerService.setState(false)
      return true
    }
    
    if(count[0] === "")
    {
      Alert.showError("You are probably copying a blank column!")
      this.spinnerService.setState(false)
      return true
    }
    if(array[1].split(/\t/)[0] === "0")
    {
      Alert.showError("The BLUEPRINT LEVEL column has less lines then the rest of lines you are copying from the table!")
      this.spinnerService.setState(false)
      return true
    }

    return false
  }


  simplifyString(str: string): string 
  {
    return (str?.normalize('NFD')?.replace(/[\u0300-\u036f]/g, '')?.toLocaleUpperCase())?.trim();
  }

  sortingUpgradesNumberOrder = -1;
  sortingUpgradesNumber(fieldName:string)
  {
    this.sortingUpgradesNumberOrder *= -1;
    this.upgrades.sort((a, b) => 
    {
      if (!a[fieldName] && b[fieldName]) return 1;
      if (a[fieldName] && !b[fieldName]) return -1;
      if (!a[fieldName] && !b[fieldName]) return 0;
      return this.sortingUpgradesNumberOrder * (a[fieldName] - b[fieldName]);
    });
  }

  sortComonIngredientsOrder = -1;
  sortComonIngredients(index: number) {
    this.sortComonIngredientsOrder *= -1;
    this.upgrades.sort((a, b) => {
      if (!a.ingredientsAmount[index] && b.ingredientsAmount[index]) return 1;
      if (a.ingredientsAmount[index] && !b.ingredientsAmount[index]) return -1;
      if (!a.ingredientsAmount[index] && !b.ingredientsAmount[index]) return 0;
      return (
        this.sortComonIngredientsOrder *
        (a.ingredientsAmount[index] - b.ingredientsAmount[index])
      );
    });
  }

  sortingUpgradesStringOrder = -1;
  sortingUpgradesString()
  {
    this.sortingUpgradesStringOrder *= -1;
    this.upgrades.sort((a, b) => {
      if (!a.itemId && b.itemId) return 1;
      if (a.itemId && !b.itemId) return -1;
      if (!a.itemId && !b.itemId) return 0;
      let nameA = this._itemService.svcFindById(a.itemId).name;
      let nameB = this._itemService.svcFindById(b.itemId).name;
      return this.sortingUpgradesStringOrder * nameA.localeCompare(nameB);
    });
  }
}
