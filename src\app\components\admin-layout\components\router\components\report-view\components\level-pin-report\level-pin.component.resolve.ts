import { Injectable } from '@angular/core';
import { MarkerService } from 'src/app/services';
import { EasyMVC } from 'src/lib/darkcloud/angular';
import { ItemType, MarkerType } from 'src/lib/darkcloud/dialogue-system';
import { Preloadable } from './level-pin-report.component';

@Injectable({
  providedIn: 'root',
})
export class LevelPinReportResolve extends EasyMVC.Resolve<Preloadable> {
  constructor(private _markerService: MarkerService) {
    super(_markerService);
  }
  async rsvOnResolve(): Promise<Preloadable> {
    await this._markerService.toFinishLoading();
    return {
      pinDialogueMarkers: this._markerService.models.filter(
        (marker) => +marker.type === +MarkerType.PIN
      ),
    };
  }
}
