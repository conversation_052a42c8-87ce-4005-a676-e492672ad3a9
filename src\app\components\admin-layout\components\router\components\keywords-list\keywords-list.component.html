<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons [cardTitle]="listName"
                                [cardDescription]="description"
                                [rightButtonTemplates]="[tagsButton, addButtonTemplate]"></app-header-with-buttons>
        <app-header-search (inputKeyup)="lstOnChangeFilter($event)" (searchOptions)="lstOnChangeFilterOptions($event)"></app-header-search>
      </div>
    </div>
    <!--List-->
    <div class="card">
      <table class="table table-list">
        <thead class="sticky">
          <tr>
            <th>Index</th>
            <th class="th-clickable"
            (click)="sortListByName(keywordsToSearch,'id')">ID</th>
            <th class="th-clickable" (click)="sortByTags()">Tags</th>
            <th class="th-clickable"
            (click)="sortListByName(keywordsToSearch,'key')">key</th>
            <th class="th-clickable"
            (click)="sortListByName(keywordsToSearch,'word')">word
            <div class="ball-circle"></div>
          </th>
            <th class="th-clickable"
            (click)="sortListByName(keywordsToSearch,'notes')">notes</th>
            <th>Actions</th>          
        </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="
                let keyword of keywordsToSearch;
                let i = index;
                trackBy: trackById
              ">
            <tr id="{{ keyword.id }}">
              <td class="td-sort">{{ i + 1 }}</td>
              <td class="td-id">{{ keyword.id }}</td>

              <td class="td-auto">
                <button class="btn btn-primary btn-fill" *ngFor="let tag of keyword?.keywordTags; let i = index"
                (click)="changeTag(keyword, '', i)" 
                tooltip="Create a new tag"
                [ngStyle]="{'background-color': (((tag | keywordTag) | async) | information: {hex: '#828282'})?.hex,
                  'border-color': (((tag | keywordTag) | async) | information: {hex: '#828282'})?.hex
                  }">{{ ((tag | keywordTag) | async)?.name }}</button>

                <button class="btn btn-success btn-fill" (click)="addTagWithPopup(keyword)">
                  <i class="pe-7s-plus"></i>
                </button>
              </td>

              <td class="td-id">{{ keyword.key }}</td>
              <td class="td-notes">
                <!-- <textarea style="overflow: hidden;" appTextareaAutoresize class="form-control form-short"
                       type="text"
                       value="{{ keyword.word }}"
                       #name
                       (change)="lstOnChange(keyword, 'word', name.value)"
                       *ngIf="lstLanguage == 'PT-BR'"></textarea> -->
                <textarea style="overflow: hidden;"  appTextareaAutoresize class="form-control form-short"
                       type="text"
                       value="{{ (keyword | translation : lstLanguage : keyword.id : 'word') }}"
                       #name
                       (change)="wordChange(keyword, 'word', name.value)"
                        ></textarea>
              </td>
              <td class="td-notes">
                <textarea class="form-control borderless"
                          
                          value="{{ (keyword | translation : lstLanguage : keyword.id : 'notes') }}"
                          #notes
                          (change)="lstOnChange(keyword, 'notes', notes.value)"></textarea>
              </td>
                <td class="td-actions" >
                  <button  class="btn btn-gray btn-fill translation-button"
                        (click)="getKeywordsOrtography(keyword)">                        
                        <div class="mat-translate"></div>
                    </button>
                    <br>
                    <button class="btn btn-danger btn-fill btn-remove"
                    (click)="removeKeyword(keyword)">                        
                    <i class="pe-7s-close"></i>
                  </button>
                </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>
