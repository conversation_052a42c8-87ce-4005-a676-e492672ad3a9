.tree-leaf
{
  /* background-color: aquamarine;
  border: 2px dashed black; */
  width: 100%;
  min-height: 60px; // Default minimum, will be overridden by ngStyle
  height: auto;
  position: relative;
  overflow: visible; // Allow roadblock branches to extend outside
  display: flex;
  flex-direction: column;
  justify-content: center; // Center content vertically

  // Smooth transition for visibility changes
  transition: opacity 0.3s ease;
}

.leaf-container
{
  position: relative;
  width: 100%;
  height: 100%;
}

.leaf-box
{
  width: 200px;
  padding: 5px;
  margin: 5px auto;
  margin-bottom: 15px;
  border: 2px solid black;
  border-radius: 5px;
  color: rgb(86, 86, 86);
  position: relative;
  box-sizing: border-box; // Ensure borders don't affect layout
}

.leaf-content
{
  position: relative;
  width: 100%;
  height: 100%;
}

.leaf-text
{
  font-size: 18px;
}

.leaf-subtext
{
  margin-top: 4px;
  font-size: 10px;
}

// Key icon styling for dialogue boxes with progress labels
.leaf-key-container
{
  position: absolute;
  top: 35%;
  transform: translateY(-50%);
  width: 40px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 6;
  box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

// Matched labels (with roadblocks) - positioned on the left
.leaf-key-container.matched
{
  left: -26px;
  border-radius: 0 15px 15px 0;
}

// Unmatched labels (without roadblocks) - positioned on the right
.leaf-key-container.unmatched
{
  right: -26px;
  border-radius: 15px 0 0 15px;
}

.leaf-key-icon
{
  font-size: 20px;
  font-weight: bold;
}

// Dice icon styling for options with dice system
.leaf-dice-container
{
  position: absolute;
  bottom: -27px; // Bottom position to distinguish from keys
  left: -27px; // Positioned to show full circle
  width: 35px;
  height: 35px;
  border-radius: 50%; // Full circle
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #333;
  z-index: 6;
  box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.leaf-dice-icon
{
  width: 25px;
  height: 25px;
  // The filter in HTML makes the icon white
}

// Check icon styling for selected options
.leaf-check-container
{
  position: absolute;
  bottom: -19px; // Bottom right position
  right: -19px; // Positioned to show full circle
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: white;
  border: 2px solid #4CAF50;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 7; // Higher than dice to ensure visibility
  box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.leaf-check-icon
{
  font-size: 24px;
  font-weight: bold;
  color: #4CAF50;
}

// Optional: Add a subtle background highlight for boxes with labels
.leaf-has-label
{
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);
}

// Interactive choice option styles
.leaf-interactive
{
  transition: box-shadow 0.2s ease, transform 0.2s ease;
  position: relative; // Ensure proper stacking context

  &:hover:not(.leaf-selected) {
    transform: scale(1.02); // Smaller scale to reduce layout impact
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    z-index: 8;
  }

  // Prevent hover effects on selected items to avoid conflicts
  &.leaf-selected {
    transform: none !important; // Ensure no transform is applied to selected items

    &:hover {
      transform: none !important;
    }
  }
}

.leaf-selected
{
  // Subtle styling since we now use check icon for selection indication
  box-shadow: 0 0 8px rgba(76, 175, 80, 0.3) !important;

  position: relative;
  z-index: 5;
}

.leaf-unselected
{
  opacity: 0.5;
  filter: grayscale(50%);
}

$line-width: 4px;

.leaf-line
{
  height: calc(100% - 60px);
  width: $line-width;
  margin-left: auto;
  margin-right: auto;
  background-color: white;
}



.leaf-story-box
{
  background-color: #BFCEDB;
}
.leaf-choice-box 
{
  background-color: #C19EE5;
}

.leaf-choice-option
{
  background-color: #C9B0DC;
}
.leaf-investigation-box
{
  background-color: #86CAB5;
}
.leaf-investigation-option
{
  background-color: #A1C9BC;
}

// Dice failure outcome styles - identical to regular options
.leaf-choice-failure
{
  background-color: #C9B0DC; // Same as choice option
}

.leaf-investigation-failure
{
  background-color: #A1C9BC; // Same as investigation option
}

.leaf-dice-failure
{
  background-color: #C9B0DC; // Default to choice option color for generic dice failures
}
.leaf-dilemma-box
{
  background-color: black;
  color: white;
}

.leaf-dilemma-option
{
  background-color: black;
  color: white;
}

.leaf-start
{
  background-color: #87cb16;
  color: white;
}
.leaf-end
{
  background-color: #ff4a55;
  color: white;
}

.leaf-dead
{
  opacity: 0.5;
}

// Restart Dialogue Styling
.restart-dialogue-section
{
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 10px 0;
}

.restart-dialogue-box
{
  background-color: #FFA726; // Orange color for restart
  color: white;
  font-weight: bold;
  border: 3px solid #FF8F00;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.restart-separator
{
  display: flex;
  align-items: center;
  width: 100%;
  margin: 30px 0;
  justify-content: center;
  gap: 14px;
}

.separator-line
{
  flex: 1;
  height: 2px;
  background: #ccc;
  max-width: 120px;
}

.separator-line:first-child
{
  background: linear-gradient(to right, transparent, #ccc);
}

.separator-line:last-child
{
  background: linear-gradient(to left, transparent, #ccc);
}

.restart-separator .diamonds
{
  color: #ccc;
  font-size: 12px;
  letter-spacing: 2px;
}

.continue-box
{
  background-color: #66BB6A; // Green color for continue
  color: white;
  font-weight: bold;
  border: 3px solid #4CAF50;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}



// Multiple roadblocks container
.roadblocks-container
{
  position: absolute;
  top: 0; // Start from top of dialogue box
  left: 0; // Start from dialogue box position
  width: 100%; // Full width to accommodate left and right positioning
  height: 100%; // Full height of dialogue box
  z-index: 5;
  pointer-events: none; // Prevent interference with other elements
  overflow: visible; // Allow roadblocks to extend beyond container
}

// Individual roadblock branch styling
.roadblock-branch-container
{
  position: absolute;
  display: flex;
  align-items: center;
  pointer-events: auto; // Allow interaction with roadblock info
  width: auto; // Auto width for proper positioning
}

// Left-side roadblock positioning (matched or mixed roadblocks)
.roadblock-branch-container.roadblock-left {
  left: 22%;
  top: 50%; // Center vertically relative to dialogue box
  transform: translateY(-50%); // Center the roadblock branch itself
  margin-right: 10px; // Gap between roadblock and dialogue box
  flex-direction: row-reverse; // Reverse order: info box, then arrow
  justify-content: flex-start;
}

// Right-side roadblock positioning (only unmatched roadblocks)
.roadblock-branch-container.roadblock-right {
  right: 22%;
  top: 50%; // Center vertically relative to dialogue box
  transform: translateY(-50%); // Center the roadblock branch itself
  margin-left: 10px; // Gap between roadblock and dialogue box
  flex-direction: row; // Normal order: arrow, then info box
  justify-content: flex-start;
}

.roadblock-horizontal-branch
{
    width: 320px;
    height: 3px;
    position: relative;
    margin: 0 10px;
    border-top: 2px dotted white;
    border-bottom: 2px dotted white;
}

// Right-pointing arrow (regular roadblocks on left side) - pointing towards dialogue box
.roadblock-horizontal-branch.arrow-left::after {
  content: '';
  position: absolute;
  right: -10px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 10px solid white;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
}

// Left-pointing arrow (regular roadblocks on right side) - pointing towards dialogue box
.roadblock-horizontal-branch.arrow-right::after {
  content: '';
  position: absolute;
  left: -10px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-right: 10px solid white;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
}

// Option-specific horizontal branch styling
.roadblock-horizontal-branch-option
{
  width: 60px; // Branch line length (same as regular)
  height: 3px; // Thinner line for cleaner look (same as regular)
  position: relative;
  margin: 0 10px; // Add margin for spacing from dialogue box and roadblock
  border-top: 2px dotted white;
  border-bottom: 2px dotted white;
}

// Right-pointing arrow (Option-type roadblocks on left side) - pointing towards dialogue box
.roadblock-horizontal-branch-option.arrow-left-option::after {
  content: '';
  position: absolute;
  right: -10px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 10px solid white; // Can be customized for Options
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
}

// Left-pointing arrow (Option-type roadblocks on right side) - pointing towards dialogue box
.roadblock-horizontal-branch-option.arrow-right-option::after {
  content: '';
  position: absolute;
  left: -10px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-right: 10px solid white; // Can be customized for Options
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
}

// ===== OPTION-TYPE ROADBLOCK POSITIONING =====
// Dedicated positioning classes for roadblocks associated with ChoiceOption, InvestigationOption, and DilemmaOption

// Option-type left-side roadblock positioning (matched or mixed Option roadblocks)
.roadblock-branch-container.roadblock-option-left {
  left: -2%;
  top: 50%; // Center vertically relative to dialogue box
  transform: translateY(-50%); // Adjust for roadblock box height
  margin-right: 10px; // Gap between roadblock and dialogue box
  flex-direction: row-reverse; // Reverse order: info box, then arrow
  justify-content: flex-start;
}

// Option-type right-side roadblock positioning (only unmatched Option roadblocks)
.roadblock-branch-container.roadblock-option-right {
  right: -2%;
  top: 50%; // Center vertically relative to dialogue box
  transform: translateY(-50%); // Adjust for roadblock box height
  margin-left: 10px; // Gap between roadblock and dialogue box
  flex-direction: row; // Normal order: arrow, then info box
  justify-content: flex-start;
}




