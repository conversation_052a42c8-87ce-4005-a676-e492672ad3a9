import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';

export class RelicUses  extends Base<Data.Hard.IRelicUses, Data.Result.IRelicUses> implements Required<Data.Hard.IRelicUses>
{
  public static generateId(index: number): string {
    return IdPrefixes.RELICUSES + index;
  }

  constructor( index: number, dataAccess: RelicUses['TDataAccess']) 
  {
    super(
    {
      hard: 
      {
        id: RelicUses.generateId(index),     
      },
    },
    dataAccess
    );
  }
  public get nameRarity(): string 
  {
    return this.hard.nameRarity;
  }
  public set nameRarity(value: string) 
  {
    this.hard.nameRarity = value;
  }

  public get idRarity(): string 
  {
    return this.hard.idRarity;
  }
  public set idRarity(value: string) 
  {
    this.hard.idRarity = value;
  }

  public get numberRelicUse(): number 
  {
    return this.hard.numberRelicUse;
  }
  public set numberRelicUse(value: number) 
  {
    this.hard.numberRelicUse = value;
  }

}
