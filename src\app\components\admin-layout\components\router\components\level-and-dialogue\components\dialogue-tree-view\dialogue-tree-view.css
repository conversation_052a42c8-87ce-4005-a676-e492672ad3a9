/* ========================================
   DIALOGUE TREE VIEW COMPONENT STYLES
   ======================================== */

/*
 * Interactive control panel that stays at the top of the view.
 * Contains buttons for clearing selections and toggling roadblock evaluation.
 */
.interactive-controls {
  padding: 12px 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 12px;
  margin: 10px 0 20px 10px;
  display: flex;
  align-items: center;
  gap: 12px;
  position: sticky;
  top: 10px;
  left: 10px;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  width: fit-content;
  max-width: 90%;
  backdrop-filter: blur(10px);
}

/* Base button styling for interactive controls */
.interactive-controls .btn {
  padding: 8px 16px;
  font-size: 13px;
  font-weight: 500;
  border-radius: 8px;
  border: 1px solid #ccc;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  white-space: nowrap;
}

/* Button hover effects (only for enabled buttons) */
.interactive-controls .btn:hover:not(:disabled) {
  background-color: #e9ecef;
  border-color: #adb5bd;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* Disabled button styling */
.interactive-controls .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Secondary button styling (Clear Selections button) */
.interactive-controls .btn-secondary {
  background-color: #6c757d;
  border-color: #6c757d;
  color: white;
}

.interactive-controls .btn-secondary:hover:not(:disabled) {
  background-color: #5a6268;
  border-color: #545b62;
}

/* Success button styling (currently unused but kept for future use) */
.interactive-controls .btn-success {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  border-color: #28a745;
  color: white;
  box-shadow: 0 2px 6px rgba(40, 167, 69, 0.3);
}

.interactive-controls .btn-success:hover:not(:disabled) {
  background: linear-gradient(135deg, #218838 0%, #1abc9c 100%);
  border-color: #1e7e34;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
}

/* Outline secondary button styling (Show All toggle button) */
.interactive-controls .btn-outline-secondary {
  background-color: transparent;
  border-color: #6c757d;
  color: #6c757d;
}

.interactive-controls .btn-outline-secondary:hover:not(:disabled) {
  background-color: #6c757d;
  border-color: #6c757d;
  color: white;
}

/* Help text styling for user instructions */
.interactive-controls span {
  font-size: 12px;
  color: #6c757d;
  font-style: italic;
  background-color: rgba(255, 255, 255, 0.7);
  padding: 4px 8px;
  border-radius: 6px;
  border: 1px solid rgba(108, 117, 125, 0.2);
}