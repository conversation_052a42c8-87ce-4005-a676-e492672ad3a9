import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/SortableListComponent';
import { Component} from '@angular/core';
import { Character, Mastery} from 'src/app/lib/@bus-tier/models';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Button } from 'src/app/lib/@pres-tier/data';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { Alert } from 'src/lib/darkcloud';
import { MasteryService } from 'src/app/services/mastery.service';
import { TierService } from 'src/app/services';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-mastery',
  templateUrl: './mastery.component.html',
  styleUrls: ['./mastery.component.scss'],

})

export class MasteryComponent extends SortableListComponent<Mastery> 
{
  timerOne:any;
   masteryList : Mastery[] = [];
  tierList:string[] = [];
  description:string = '';

  masteryFields: string[] = [
    'typeMasteryRarity', 
    'slotA.skillDominated', 
    'slotA.multiplier', 
    'slotB.skillDominated', 
    'slotB.multiplier', 
    'slotC.skillDominated', 
    'slotC.multiplier', 
    'extremes'
  ];

  constructor(
    _activatedRoute: ActivatedRoute,
    protected _masteryService: MasteryService,
    _userSettingsService: UserSettingsService,
    protected _languageService: LanguageService,
    private _tierListService: TierService,
    protected _translationService: TranslationService
  ) 
  {
    super(_masteryService, _activatedRoute, _userSettingsService, 'name');
  }

  public override async ngOnInit() 
  {
    await this._masteryService.toFinishLoading();
    await this._tierListService.toFinishLoading();
    this.tierList = this._tierListService.fillRarityArrayDynimically('Weapon Rarity', this.tierList);
    this.masteryList = this._masteryService.models.filter((x) => x.id.includes('MAS'));
    this.sortMasteryListBasedOnTierList();  
    this.description = `Showing ${this.masteryList.length} results`;
  }

  sortMasteryListBasedOnTierList()
  {
    this.masteryList = this.masteryList.sort((a,b)=> this.tierList.indexOf(a.typeMasteryRarity) - this.tierList.indexOf(b.typeMasteryRarity));
  }

  public readonly excelButtonTemplate: Button.Templateable = 
  {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  public downloadSceneryOrtography(character: Character) 
  {
    this._translationService.getCharacterOrtography(character, true);
  }

  async changeSlotASkillDominated(mastery: Mastery, value: string, fieldName:string)
  {  
    if(fieldName === 'slotA.skillDominated') mastery.slotA.skillDominated = value; 
    if(fieldName === 'slotA.multiplier') mastery.slotA.multiplier = value;
    if(fieldName === 'slotB.skillDominated') mastery.slotB.skillDominated = value; 
    if(fieldName === 'slotB.multiplier') mastery.slotB.multiplier = value;
    if(fieldName === 'slotC.skillDominated') mastery.slotC.skillDominated = value; 
    if(fieldName === 'slotC.multiplier') mastery.slotC.multiplier = value;
    if(fieldName === 'extremes') mastery.extremes = value;
        
    await this._masteryService.svcToModify(mastery);
    await this._masteryService.toSave();
  }

  async onExcelPaste(): Promise<void> {
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter(line => line);
  
    // Verifica se há erros no conteúdo copiado
    if (this.displayErrors(lines)) return;

    if(this.masteryList.length === 0)  {
      for (let l = 0; l < lines.length; l++) {
        const line = lines[l];
        const cols = line.split(/\t/);
    
        // Verifica se a primeira coluna é válida
        const tierName = cols[0].trim();
        if (!this.tierList.includes(tierName)) {
          return Alert.showError('', `The tier: ${tierName}, does NOT exist in the Tier list!`);
        }  
        // Cria uma nova Mastery na base de dados
         const newMastery = await this._masteryService.createNewMastery(tierName);
    
        newMastery.slotA = newMastery.slotA || {}; // Garante que o objeto exista
        newMastery.slotB = newMastery.slotB || {}; // Garante que o objeto exista
        newMastery.slotC = newMastery.slotC || {}; // Garante que o objeto exista
    
        newMastery.slotA.skillDominated = cols[1];
        newMastery.slotA.multiplier = cols[2];
        newMastery.slotB.skillDominated = cols[3];
        newMastery.slotB.multiplier = cols[4];
        newMastery.slotC.skillDominated = cols[5];
        newMastery.slotC.multiplier = cols[6];
        newMastery.extremes = cols[7];
    
        this.masteryList.push(newMastery);        
        await this._masteryService.svcToModify(newMastery);
     }
    }
    else {
      for (let l = 0; l < lines.length; l++) {
        const line = lines[l];
        const cols = line.split(/\t/);

        const tierName = cols[0].trim();
        if (!this.tierList.includes(tierName)) {
          return Alert.showError('', `The tier: ${tierName}, does NOT exist in the Tier list!`);
        }

        this.masteryList[l].typeMasteryRarity = tierName;
        this.masteryList[l].slotA.skillDominated = cols[1];
        this.masteryList[l].slotA.multiplier = cols[2];
        this.masteryList[l].slotB.skillDominated = cols[3];
        this.masteryList[l].slotB.multiplier = cols[4];
        this.masteryList[l].slotC.skillDominated = cols[5];
        this.masteryList[l].slotC.multiplier = cols[6];
        this.masteryList[l].extremes = cols[7];

        await this._masteryService.svcToModify(this.masteryList[l]);
      }
    }
  
    await this._masteryService.toSave();
    Alert.ShowSuccess('Mastery imported successfully!');
    this.lstFetchLists();
    this.ngOnInit();
  }
  
  displayErrors(array)
  {
    let count = array[0].split(/\t/)
    if(count.length < this.masteryFields.length)
    {
      Alert.showError("Copy the ORDER or MASTERY EXTREMES column values too!");
      return true;
    }
    
    if(count[0] === "")
    {
      Alert.showError("You are probably copying a blank column!");
      return true;
    }

    return false;
  }

}
