import { Component, EventEmitter, Input, Output } from '@angular/core';

// Interface para os itens enviados para revisão
export interface IViewSend {
  id?: string;
  name?: string;
  lote?: string;
  textValue?: string[];
  textValueString?: string;
}

@Component({
  selector: 'app-view-send-modal',
  templateUrl: './view-send-modal.component.html',
  styleUrls: ['./view-send-modal.component.scss']
})
export class ViewSendModalComponent {

   /**
     * Controla se o modal está visível ou não
     */
    @Input() isModalInfo: boolean = false;

    /**
     * Array com os itens enviados para revisão
     */
    @Input() listViewSend: IViewSend[] = [];

    /**
     * Evento emitido quando o modal deve ser fechado
     */
    @Output() modalClose = new EventEmitter<void>();
  
    /**
     * Fecha o modal e emite o evento para o componente pai
     */
    closeViewSendPopup(): void {
      this.modalClose.emit();
    }
  
    /**
     * Fecha o modal quando o mouse sai da área (mouseleave)
     */
    onMouseLeave(): void {
      this.modalClose.emit();
    }
}
