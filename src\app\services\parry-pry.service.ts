import { Injectable } from '@angular/core';
import { IndexStorageService, ReviewService } from 'src/app/services';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { ModelService } from 'src/lib/darkcloud/angular/dsadmin';
import { ParryPry } from '../lib/@bus-tier/models/ParryPry';

@Injectable({
  providedIn: 'root',
})
export class ParryPryService extends  ModelService<ParryPry> 
{

  public override svcPromptCreateNew(): Promise<ParryPry> {
    throw new Error('Method not implemented.');
  }

  constructor(
    indexStorageService: IndexStorageService,
    readonly userSettingsService: UserSettingsService,
    reviewService: ReviewService,
  ) 
  {
    super(
      {
        defaultConstructor: () =>
          new ParryPry(0, this.userSettingsService),
      },
      'ParryPry',
      indexStorageService,
      reviewService,
    );
  }
  
  public async createNewParryPry() {
    let parryPry = new ParryPry(this.svcNextIndex(), this.userSettingsService);
    this.srvAdd(parryPry);
    return parryPry;
  }
}
