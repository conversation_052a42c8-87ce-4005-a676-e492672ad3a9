<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons [cardTitle]="listName" [cardDescription]="description"
          [rightButtonTemplates]="[tagsButton,addMissionTemplate]">
        </app-header-with-buttons>
        <app-header-search (inputKeyup)="lstOnChangeFilter($event)"
          (searchOptions)="searchFilterOptions($event)"></app-header-search>
      </div>
    </div>
    <!--List-->
    <div class="card">
      <table class="table table-list">
        <thead class="sticky">
          <tr>
            <th>Index</th>
            <th class="th-clickable" (click)="sortMissionListById()">
              ID
            </th>
            <th class="th-clickable" (click)="sortMissionMissions()">
              Missions
            </th>
            <th class="th-clickable" (click)="sortMissionObjectives()">
              Objectives
            </th>
            <th style="width: 200px;">XP</th>
            <th class="th-clickable" (click)="sortListByParameter('name')">
              Name & Description
              <div class="ball-circle"></div>
            </th>
            <th>Notes</th>
            <th style="width: 150px;">Objectives
              <div class="ball-circle"></div>
            </th>
            <th class="th-clickable" (click)="sortListMissionByArea()">Area   
              <select class="dropdown filter-dropdown limited center" name="areaIdFilter"
                [(ngModel)]="lstFilterValue['areaId']" (change)="lstOnChangeFilter()">
                <option value="ALL">All</option>
                <option *ngFor="let area of preloadedAreas" value="{{ area.id }}">
                  {{ area.hierarchyCode }}: {{ area.name }}
                </option>
              </select>
            </th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let mission of lstIds | missions; let i = index; trackBy: trackByIndex">
            <tr id="{{ mission.id }}" class="anchor">
              <td class="td-sort">{{ i + 1 }}</td>
              <td class="category">{{ mission.id }}</td>

              <!--START: Mission Section -->
              <td class="td-small">
                <ng-container *ngIf=" !(mission | reviewMission : 'hasAssignedMission').type; else hasAssignedMission">
                  <i style="position: relative" placement='top' delay='250' ttWidth="150px" ttAlign="center"
                    ttPadding="10px" tooltip="NOT ASSIGNED" class="pe-7s-attention attention iMission"> </i>

                </ng-container>

                <ng-template #hasAssignedMission>
                  <ng-container *ngIf=" (mission | reviewMission : 'hasSingleMission').type; else hasNoSingleMission">
                    <i style="position: relative" placement='top' delay='250' ttWidth="max-content" ttAlign="left"
                      ttPadding="10px"
                      tooltip="ASSIGNED: {{((mission | reviewMission : 'hasSingleMission').missionsIds | location).toString()}}"
                      class="pe-7s-check success iMission">
                    </i>
                  </ng-container>

                  <ng-template #hasNoSingleMission>
                    <ng-container *ngIf=" (mission | reviewMission : 'hasMultipleAssignments').type">
                      <i style="position: relative; z-index: 1;" placement='top' delay='250' ttWidth="max-content" ttAlign="left"
                        ttPadding="10px"
                        tooltip="ASSIGNED ALERT: {{((mission | reviewMission : 'hasMultipleAssignments').missionsIds | location | enumerateList).toString()}}"
                        class="pe-7s-check success iMission">
                        <i class="pe-7s-attention warning icon-notification" style="margin-left: 15px;"></i>
                      </i>
                    </ng-container>
                  </ng-template>
                </ng-template>
              </td>
              <!--END: Mission Section -->

              <!--START: Objectives Section -->
              <td style="width: 10px">
                <ng-container *ngIf="!(mission | reviewObjective : 'hasObjectives').type; else hasNoObjectives">
                  <i style="position: relative" tooltip="NO OBJECTIVES" placement='top' delay='250' ttWidth="150px"
                    ttPadding="10px" ttAlign="center" class="pe-7s-close-circle warning iMission">
                  </i>
                </ng-container>

                <ng-template #hasNoObjectives>
                  <ng-container *ngIf="(mission | reviewObjective : 'hasNotAssigned').type; else hasAssigned">
                    <i style="position: relative; z-index: 1;" placement='top' delay='250' ttWidth="150px" ttAlign="center"
                      ttPadding="10px" tooltip="NOT ASSIGNED" class="pe-7s-attention attention">
                    </i>
                  </ng-container>
                  <ng-template #hasAssigned>
                    <ng-container
                      *ngIf="!(mission | reviewObjective : 'hasSinleAssigment').type; else hasNoSinleAssigment">
                      <i style="position: relative; z-index: 1;" placement='top' delay='250' ttWidth="max-content" ttAlign="left"
                        ttPadding="10px"
                        tooltip="ASSIGNED: {{((mission | reviewObjective : 'hasSinleAssigment').missionsIds | location | enumerateList).toString()}}"
                        class="pe-7s-check success">
                      </i>
                    </ng-container>
                    <ng-template #hasNoSinleAssigment>
                      <i style="position: relative; z-index: 1;" placement='top' delay='250' ttWidth="max-content" ttAlign="left"
                        ttPadding="10px"
                        tooltip="ASSIGNED ALERT: {{((mission | reviewObjective : 'hasSinleAssigment').missionsIds | location | enumerateList).toString()}}"
                        class="pe-7s-check success">
                        <i class="pe-7s-attention warning icon-notification"></i>
                      </i>
                    </ng-template>
                  </ng-template>
                </ng-template>
              </td>
              <!--END: Objectives Section -->

              <td>
                <button class="btn btn-lg btn-fill btn-neutral">
                  {{ mission.xp }}
                </button>
              </td>
              <td>
                <input class="form-control form-short" type="text"
                  value="{{ (mission | translation : lstLanguage : mission.id : 'name') }}" #name
                  (change)="changeName(mission, 'name', name.value)" />
                <textarea class="form-control" type="text"
                  value="{{ (mission | translation : lstLanguage : mission.id : 'description') }}" #description
                  (change)="changeDescription(mission, 'description', description.value)">
                  </textarea>
              </td>
              <td>
                <textarea class="form-control borderless" placeholder="Notes..." type="text"
                  value="{{ (mission | information)?.authorNotes || '' }}" #notes (change)="
                      updateInformation(mission, 'authorNotes', notes.value)
                    "></textarea>
              </td>
              <td class="td-small hover-hand">
                <button class="btn btn-primary" type="button"
                  (click)=" missionIdToggle = missionIdToggle === mission.id ? null : mission.id">
                  <i class="pe-7s-look" style="font-size: 32px"></i>
                </button>
              </td>
              <td class="td-small">
                <button [ngClass]="(mission.areaId | area) ? 'btn btn-info btn-fill' : 'btn'" (click)="toPromptChangeMissionArea(mission)">
                  {{ (mission.areaId | area)?.name || "undefined" }}
                </button>
              </td>
              <td class="td-actions td-auto">
                <button class="btn btn-fill btn-danger btn-remove" (click)="lstPromptRemove(mission)">
                  <i class="pe-7s-close"></i>
                </button>
                <button class="btn btn-fill btn-gray translation-button" (click)="downloadMissionOrtography(mission)">
                  <div class="mat-translate"></div>
                </button>
              </td>
            </tr>
            <tr *ngIf="missionIdToggle === mission.id">
              <td colspan="9">
                <app-objective-sublist [missionId]="mission.id" (xpChanged)="xpChanged(mission, $event)">
                </app-objective-sublist>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>