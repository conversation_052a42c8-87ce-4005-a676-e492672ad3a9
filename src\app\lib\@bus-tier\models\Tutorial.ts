import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { Base } from './Base';

export class Tutorial
  extends Base<Data.Hard.ITutorial, Data.Result.ITutorial>
  implements Required<Data.Hard.ITutorial>
{
  public static generateId(index: number): string {
    return IdPrefixes.TUTORIAL + index;
  }
  public generateId(index: number): string {
    return Tutorial.generateId(index);
  }
  constructor(
    index: number,
    name: string,
    dataAccess: Tutorial['TDataAccess']
  ) {
    super(
      {
        hard: {
          id: Tutorial.generateId(index),
          name,
        },
      },
      dataAccess
    );
  }
  public get name(): string {
    return this.hard.name;
  }
  public set name(value: string) {
    this.hard.name = value;
  }
  protected getInternalFetch() {
    return {};
  }
  public get description(): string {
    return this.hard.description;
  }
  public set description(value: string) {
    this.hard.description = value;
  }
  public get notes(): string {
    return this.hard.notes;
  }
  public set notes(value: string) {
    this.hard.notes = value;
  }
}
