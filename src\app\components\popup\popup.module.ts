import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { LoadingComponent } from './loading/loading.component';
import { MajorModule } from 'src/app/major.module';
import { PopupComponent } from './popup.component';
import { RouterModule } from '@angular/router';
import { AudioListenerModule } from '../audio-listener/audio-list.module';

@NgModule({
  imports: [
    CommonModule,
    BrowserModule,
    FormsModule,
    RouterModule,
    MajorModule,
    AudioListenerModule,
  ],
  declarations: [PopupComponent, LoadingComponent],
  exports: [PopupComponent, LoadingComponent],
})
export class PopupModule {}
