<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons [cardTitle]="'Titanium Storage C'" [cardDescription]="description"
          [rightButtonTemplates]="[excelButtonTemplate]">
        </app-header-with-buttons>
        <app-header-search (inputKeyup)="lstOnChangeFilter($event)"
          (searchOptions)="lstOnChangeFilterOptions($event)"></app-header-search>
      </div>
    </div>
    <!--List-->
    <div class="card">
      <table class="table table-list">
        <thead class="sticky">
          <tr>
            <th rowspan="3" class="th-clickable" (click)="sortListByParameter('titaniumLevel')">
              Titanium Storage - Level
            </th>
            <th class="th-clickable" (click)="sortListByParameter('souls')">
              Cost
            </th>
            <th class="th-clickable" (click)="sortListByParameter('time')">
              Building Time
            </th>
            <th class="th-clickable" (click)="sortListByParameter('improveRubies')">
              Skip Cost
            </th>
            <th rowspan="3" class="th-clickable" (click)="sortListByParameter('storage')">
              Store Capacity C
            </th>
          </tr>
          <tr>
            <th class="th-clickable silver-color" (click)="sortListByParameter('souls')">
              TITANIUM
            </th>
            <th class="th-clickable time-color" (click)="sortListByParameter('time')">
              MINUTES
            </th>
            <th class="th-clickable rubies-color" (click)="sortListByParameter('improveRubies')">
              RUBIES
            </th>
          </tr>
          <tr>
            <th class="th-clickable light-gray" (click)="sortListByParameter('souls')">
              Required to get to this level
            </th>
            <th class="th-clickable light-gray" (click)="sortListByParameter('time')">
              Time to upgrade to this level
            </th>
            <th class="th-clickable light-gray" (click)="sortListByParameter('improveRubies')">
              Gem to skip the wait (build instantly)
            </th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="
                  let titaniumStorage of lstIds | titaniumStorages;
                  let i = index;
                  trackBy: trackById
                ">
            <tr *ngIf="titaniumStorage.type === 'C'" id="{{ titaniumStorage.id}}">
              <td>{{ titaniumStorage.titaniumLevel}}</td>
              <td class="td-id">
                <input class="background-input-table-color" placeholder=" " type="number" #Inputsouls
                  [value]="titaniumStorage.souls" (change)="lstOnChange(titaniumStorage, 'souls', Inputsouls.value)" />
              </td>
              <td class="td-id">
                <input class="background-input-table-color" placeholder=" " type="number" #Inputtime
                  [value]="titaniumStorage.time" (change)="lstOnChange(titaniumStorage, 'time', Inputtime.value)" />
              </td>
              <td class="td-id">
                <input class="background-input-table-color" placeholder=" " type="number" #Inputrubies
                  [value]="titaniumStorage.rubies"
                  (change)="lstOnChange(titaniumStorage, 'rubies', Inputrubies.value)" />
              </td>
              <td class="td-id">
                <input class="background-input-table-color" placeholder=" " type="number" #Inputstorage
                  [value]="titaniumStorage.storage"
                  (change)="lstOnChange(titaniumStorage, 'storage', Inputstorage.value)" />
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>