import { Component, OnInit } from '@angular/core';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Alert } from 'src/lib/darkcloud';
import { readMultiple } from 'src/lib/others';
import { LevelService } from 'src/app/services/level.service';
import { ILevel } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard';

@Component({
  selector: 'app-icon-list',
  templateUrl: './icon-list.component.html',
  styleUrls: ['./icon-list.component.scss']
})
/**
 * Displays and edits icon data as a list
 */
export class IconListComponent implements OnInit {

  constructor(
    private _userSettingsService: UserSettingsService,
    private _levelService: LevelService
  ) {}
  /**
   * Array that stores icons
   */
  public icons: { id: string; name: string }[] = [];
  public valeuLeves: ILevel[] = [];

  /*
   ** ng tracker by index
   */
  trackByIndex(index: number, icon: any): any {
    return index;
  }

  /*
   ** Renders the list
   */
  ngOnInit(): void {
    this.renderList();
  }

  /*
   ** Renders the list
   */
  renderList() {
    this.icons = this._userSettingsService.data?.icons;
  }

  /*
   ** Removes the icon after displaying a confirmation alert
   */
  async toPromptRemoveIcon(icon: { id: string; name: string }) {
    const iconIndex = this.icons.indexOf(icon);

    const removeLevel = this._levelService.models.filter((level) => level.iconIndex === iconIndex);
    
     const names: string[] = readMultiple( removeLevel);
    if (
      await Alert.showConfirm(
        'Are you sure you want to remove \n"' + icon.id + '"?',
        'This will remove this icon from ' + names.length + ' levels',
        'Review'
      )
    ) {
      if (await Alert.showReviewUnlink('These will be removed:', names)) {
        //remove icon
        this._userSettingsService.RemoveIcon(icon.id);     
    
        removeLevel.flatMap((el) => {
                delete el.hard.iconIndex            
                delete el.iconIndex
        });

        for (let index = 0; index < removeLevel.length; index++) {            
         delete removeLevel[index].iconIndex;     
         }       
  
        removeLevel.forEach ((idInformation) => {     
          this._userSettingsService.RemoveIconInformation(idInformation.id);
        })
         

        this._userSettingsService.modify();
        this.renderList();        
      }     
    }
    
  }

  /*
   ** Prompts an option to change the icon
   */
  toPromptChangeIcon(icon: { id: string; name: string }) {
    this._userSettingsService.changeIconId(icon);
    this.renderList();
  }

  /*
   ** Adds a new icon
   */
  toPromptAddIcon() {
    this._userSettingsService.addIcon();
    this.renderList();
  }

  /*
   ** Modifies the icon
   */
  public onChange(icon: any, property?: string, value?: string) {
    if (property) {
      icon[property] = value;
    }
    this._userSettingsService.modify();
  }
}
