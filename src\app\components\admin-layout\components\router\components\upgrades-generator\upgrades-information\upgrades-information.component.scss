
.content-weapon
{
  margin: 25px;
}
select option{
  background-color: #fff;
}

.sticky th
{
  top:0 !important;
}
.sticky{
  display:table-header-group !important;
  position:relative !important;
  z-index: 200 !important;
  border-color: #c9c9c9 !important;
}

.c-container {
  position: fixed;
  top: 225px;
  left: 220px;
  right: 20px;
  bottom: 0px;
  overflow-x: auto;
  overflow-y: auto;
  margin-left: 15px;
  margin-right: 15px;
}

.red-color
{
    background-color: rgb(255,0,0) !important;
    color: rgb(255, 255, 255)!important;
}

.blue-color
{
    background-color: rgb(0,176,240)!important;
    color: rgb(255, 255, 255)!important;

}

.light-blue-color
{
    background-color: rgb(221,235,247)!important;
    color: rgb(0, 0, 0)!important;

}

.light-gray-color
{
    background-color: rgb(242,242,242)!important;
    color: rgb(0, 0, 0)!important;

}

.gray-color 
{
    background-color: rgb(89,89,89)!important;
    color: #fff!important;

}

.green-color 
{
    background-color: rgb(27, 149, 57)!important;
    color: #fff!important;

}


