import { Injectable } from '@angular/core';
import { RoadBlock } from 'src/app/lib/@bus-tier/models/RoadBlock';
import { RoadBlockType } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard';
import { RoadBlockService } from './road-block.service';
import { LabelColorService } from './label-color.service';
import { LevelHelperService } from './level-helper.service';
import { OptionService } from './option.service';
import { DilemmaService } from './dilemma.service';
import { StoryBoxService } from './story-box.service';
import { AnswerDilemmaBoxService } from './answer-dilemmaBox.service';
import { Router } from '@angular/router';

/**
 * Service for handling roadblock evaluation logic in dialogue trees.
 * Manages roadblock satisfaction checking, label matching, and cascading visibility logic.
 */
@Injectable({
  providedIn: 'root'
})
export class DialogueTreeRoadblockEvaluationService {

  constructor(
    private _roadBlockService: RoadBlockService,
    private _labelColorService: LabelColorService,
    private _levelHelperService: LevelHelperService,
    private _optionService: OptionService,
    private _dilemmaService: DilemmaService,
    private _storyBoxService: StoryBoxService,
    private _answerDilemmaBoxService: AnswerDilemmaBoxService,
    private _router: Router
  ) {}

  /**
   * Check if any roadblocks in a dialogue box have matches in the current dialogue
   */
  hasAnyMatchedRoadblocks(roadBlocks: RoadBlock[]): boolean {
    return roadBlocks.some(roadblock => {
      if (+roadblock.Type !== +RoadBlockType.SPOKE_IN || !roadblock.spokeElementId) {
        return false; // Non-spoke roadblocks don't have matches
      }

      const spokeElement = this._levelHelperService.models.find((sp: any) => sp.elementId === roadblock.spokeElementId);
      if (!spokeElement?.text) {
        return false;
      }

      const referencedLabel = this._labelColorService.extractLabelFromSpokeText(spokeElement.text);
      return this.checkLabelMatchInDialogue(referencedLabel);
    });
  }

  /**
   * Check if any SPOKE_IN roadblocks in a dialogue box have no matches in the current dialogue
   * Non-spoke roadblocks are ignored for positioning logic
   */
  hasAnyUnmatchedRoadblocks(roadBlocks: RoadBlock[]): boolean {
    return roadBlocks.some(roadblock => {
      // Only consider SPOKE_IN roadblocks for matching logic
      if (+roadblock.Type !== +RoadBlockType.SPOKE_IN || !roadblock.spokeElementId) {
        return false; // Non-spoke roadblocks don't affect positioning
      }

      const spokeElement = this._levelHelperService.models.find((sp: any) => sp.elementId === roadblock.spokeElementId);
      if (!spokeElement?.text) {
        return true;
      }

      const referencedLabel = this._labelColorService.extractLabelFromSpokeText(spokeElement.text);
      return !this.checkLabelMatchInDialogue(referencedLabel);
    });
  }

  /**
   * Check if an element should be hidden based on roadblock dependencies
   * This evaluates whether roadblocks are satisfied by current choice selections
   * AND checks if parent elements are hidden (cascading logic)
   */
  shouldHideBasedOnRoadblocks(
    roadblockEvaluationEnabled: boolean,
    hasRoadBlock: boolean,
    roadBlocks: RoadBlock[],
    andOrCondition: string,
    selectedChoiceOptions: Map<string, string>,
    selectedInvestigationOptions: Map<string, Set<string>>,
    selectedDilemmaOptions: Map<string, string>,
    shouldHideBasedOnParent: () => boolean
  ): boolean {
    // Check if roadblock evaluation is enabled
    if (!roadblockEvaluationEnabled) {
      return false; // Roadblock evaluation disabled
    }

    // CASCADING LOGIC: Check if parent should be hidden first
    if (shouldHideBasedOnParent()) {
      return true;
    }

    if (!hasRoadBlock || roadBlocks.length === 0) {
      return false; // No roadblocks, don't hide
    }

    // INVERTED LOGIC: Check if any roadblocks have matching labels in current dialogue
    const hasMatchingLabelsInDialogue = roadBlocks.some(roadblock => {
      if (+roadblock.Type !== +RoadBlockType.SPOKE_IN || !roadblock.spokeElementId) {
        return false; // Non-spoke roadblocks are always satisfied
      }

      const spokeElement = this._levelHelperService.models.find((sp: any) =>
        sp.elementId === roadblock.spokeElementId
      );

      if (!spokeElement?.text) {
        return false;
      }

      const referencedLabel = this._labelColorService.extractLabelFromSpokeText(spokeElement.text);
      if (!referencedLabel) {
        return false;
      }

      // Check if this label exists in current dialogue
      return this.checkLabelMatchInDialogue(referencedLabel);
    });

    // If no roadblocks have matching labels in current dialogue, don't hide (exterior roadblocks are always satisfied)
    if (!hasMatchingLabelsInDialogue) {
      return false;
    }

    // INVERTED LOGIC: Evaluate roadblocks - hide if they are NOT satisfied
    // Evaluate roadblocks based on AND/OR logic
    let shouldHide: boolean;
    if (andOrCondition === 'AND') {
      // ALL roadblocks must be satisfied - hide if ANY roadblock is not satisfied
      shouldHide = !roadBlocks.every(roadblock => 
        this.isRoadblockSatisfied(roadblock, selectedChoiceOptions, selectedInvestigationOptions, selectedDilemmaOptions)
      );
    } else {
      // OR condition (default): AT LEAST ONE roadblock must be satisfied - hide if NO roadblocks are satisfied
      shouldHide = !roadBlocks.some(roadblock => 
        this.isRoadblockSatisfied(roadblock, selectedChoiceOptions, selectedInvestigationOptions, selectedDilemmaOptions)
      );
    }

    return shouldHide;
  }

  /**
   * Check if a specific roadblock is satisfied by current selections
   */
  private isRoadblockSatisfied(
    roadblock: RoadBlock,
    selectedChoiceOptions: Map<string, string>,
    selectedInvestigationOptions: Map<string, Set<string>>,
    selectedDilemmaOptions: Map<string, string>
  ): boolean {
    // Only evaluate "Spoke In" roadblocks that have matches in current dialogue
    if (+roadblock.Type !== +RoadBlockType.SPOKE_IN || !roadblock.spokeElementId) {
      return true; // Non-spoke roadblocks are always considered satisfied for now
    }

    // Get the spoke element referenced by this roadblock
    const spokeElement = this._levelHelperService.models.find((sp: any) =>
      sp.elementId === roadblock.spokeElementId
    );

    if (!spokeElement?.text) {
      return true; // If spoke element not found, consider satisfied
    }

    // Extract the label from the spoke element text
    const referencedLabel = this._labelColorService.extractLabelFromSpokeText(spokeElement.text);
    if (!referencedLabel) {
      return true; // If no label found, consider satisfied
    }

    // Check if this label exists in current dialogue
    if (!this.checkLabelMatchInDialogue(referencedLabel)) {
      return true; // If label not in current dialogue, don't evaluate (leave visible)
    }

    // Check if any selected choice option OR visible story box has this label
    const isSatisfied = this.isLabelSatisfiedBySelections(
      referencedLabel, 
      selectedChoiceOptions, 
      selectedInvestigationOptions, 
      selectedDilemmaOptions
    );
    return isSatisfied;
  }

  /**
   * Helper method to check if a label has a match in the current dialogue
   */
  private checkLabelMatchInDialogue(label: string): boolean {
    if (!label) return false;

    const currentDialogueId = this.getCurrentDialogueId();
    if (!currentDialogueId) return false;

    const dialoguePrefix = currentDialogueId.split('#')[0];
    const dialogueLabels = this._levelHelperService.models
      .filter((sp: any) => sp.elementId && sp.elementId.startsWith(dialoguePrefix + '.'))
      .map((sp: any) => this._labelColorService.extractLabelFromSpokeText(sp.text))
      .filter((extractedLabel: string) => extractedLabel && extractedLabel.trim() !== '');

    return dialogueLabels.includes(label);
  }

  /**
   * Get the current dialogue ID from the router
   */
  private getCurrentDialogueId(): string {
    const url = this._router.url;
    const urlParts = url.split("/");
    const dialogueId = urlParts[4]; // URL structure: /levels/LEVEL_ID/dialogues/DIALOGUE_ID
    return dialogueId;
  }

  /**
   * Check if a label is satisfied by current choice selections, investigation selections, OR visible standalone story boxes
   */
  private isLabelSatisfiedBySelections(
    targetLabel: string,
    selectedChoiceOptions: Map<string, string>,
    selectedInvestigationOptions: Map<string, Set<string>>,
    selectedDilemmaOptions: Map<string, string>
  ): boolean {
    // Check selected choice options first
    if (selectedChoiceOptions && selectedChoiceOptions.size > 0) {
      // Get all selected choice options and check their labels
      for (const [, selectedOptionId] of selectedChoiceOptions.entries()) {
        const selectedOption = this._optionService.svcFindById(selectedOptionId);
        if (!selectedOption) continue;

        // Check if this option has the target label
        // Labels can be on the option itself or on its answer box
        if (this.optionHasLabel(selectedOption, targetLabel)) {
          return true;
        }
      }
    }

    // Check selected investigation options
    if (selectedInvestigationOptions && selectedInvestigationOptions.size > 0) {
      // Get all selected investigation options and check their labels
      for (const [, selectedOptionIds] of selectedInvestigationOptions.entries()) {
        for (const selectedOptionId of selectedOptionIds) {
          const selectedOption = this._optionService.svcFindById(selectedOptionId);
          if (!selectedOption) continue;

          // Check if this option has the target label
          // Labels can be on the option itself or on its answer box
          if (this.optionHasLabel(selectedOption, targetLabel)) {
            return true;
          }
        }
      }
    }

    // Check selected dilemma options
    if (selectedDilemmaOptions && selectedDilemmaOptions.size > 0) {
      // Get all selected dilemma options and check their labels
      for (const [, selectedDilemmaId] of selectedDilemmaOptions.entries()) {
        const selectedDilemma = this._dilemmaService.svcFindById(selectedDilemmaId);
        if (!selectedDilemma) continue;

        // Check if this dilemma has the target label
        // Labels can be on the dilemma itself or on its answer box
        if (this.dilemmaHasLabel(selectedDilemma, targetLabel)) {
          return true;
        }
      }
    }

    // FIXED: Also check visible standalone story boxes with progress condition labels
    // These are always reachable if they don't have unsatisfied roadblocks
    if (this.checkStandaloneStoryBoxesForLabel(targetLabel)) {
      return true;
    }

    return false;
  }

  /**
   * Check if an option has a specific label (either on option or answer box)
   */
  private optionHasLabel(option: any, targetLabel: string): boolean {
    // Check option's direct label
    if (option.label && option.label.trim() === targetLabel.trim()) {
      return true;
    }

    // Check answer box label
    if (option.answerBoxId) {
      const answerBox = this._storyBoxService.svcFindById(option.answerBoxId);
      if (answerBox?.labelOption && answerBox.labelOption.trim() === targetLabel.trim()) {
        return true;
      }
    }

    // Check negative answer box label (for dice systems)
    if (option.answerBoxNegativeId) {
      const negativeAnswerBox = this._storyBoxService.svcFindById(option.answerBoxNegativeId);
      if (negativeAnswerBox?.labelOption && negativeAnswerBox.labelOption.trim() === targetLabel.trim()) {
        return true;
      }
    }

    return false;
  }

  /**
   * Check if a dilemma has a specific label (either on dilemma or answer box)
   */
  private dilemmaHasLabel(dilemma: any, targetLabel: string): boolean {
    // Check dilemma's direct label
    if (dilemma.label && dilemma.label.trim() === targetLabel.trim()) {
      return true;
    }

    // Check answer box label
    if (dilemma.idDilemmaBox) {
      const answerDilemmaBox = this._answerDilemmaBoxService.svcFindById(dilemma.idDilemmaBox);
      if (answerDilemmaBox?.label && answerDilemmaBox.label.trim() === targetLabel.trim()) {
        return true;
      }
    }

    return false;
  }

  /**
   * Check if any standalone story boxes in the current dialogue have the target label
   * This excludes answer boxes (which contain "opt" in their ID) and only checks
   * actual standalone story boxes with progress condition labels
   */
  private checkStandaloneStoryBoxesForLabel(targetLabel: string): boolean {
    const currentDialogueId = this.getCurrentDialogueId();
    if (!currentDialogueId) {
      return false;
    }

    // Extract dialogue prefix for filtering (e.g., "A8.L1794.D0_PT-BR")
    const dialoguePrefix = currentDialogueId.split('#')[0];

    // Get all story boxes in the current dialogue, but exclude answer boxes
    const standaloneStoryBoxes = this._storyBoxService.models.filter(storyBox =>
      storyBox.id &&
      storyBox.id.startsWith(dialoguePrefix + '.') &&
      !storyBox.id.includes('.opt') // Exclude answer boxes (they contain ".opt" in their ID)
    );

    for (const storyBox of standaloneStoryBoxes) {
      // Check if this story box has the target label
      if (storyBox.label && storyBox.label.trim() === targetLabel.trim()) {
        // Check if this story box is actually visible in the current dialogue state
        if (this.isStoryBoxActuallyVisible(storyBox)) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * Check if a story box is actually visible in the current dialogue state
   * Uses the same logic as the dialogue tree rendering to determine visibility
   */
  private isStoryBoxActuallyVisible(storyBox: any): boolean {
    // Use the existing roadblock evaluation logic that's used by the dialogue tree
    // This is the same logic used in shouldHideElement() but specifically for story boxes
    const storyBoxRoadblocks = this._roadBlockService.filterStoryboxForAllRoadblocks(storyBox.id);

    if (!storyBoxRoadblocks || storyBoxRoadblocks.length === 0) {
      return true; // No roadblocks, always visible
    }

    // Get the AND/OR condition for this story box
    const andOrCondition = storyBox.AndOrCondition || 'OR';

    // Evaluate roadblocks using the same logic as the main roadblock evaluation
    const roadblockResults = storyBoxRoadblocks.map(roadblock =>
      this.isRoadblockSatisfied(roadblock, new Map(), new Map(), new Map())
    );

    if (andOrCondition === 'AND') {
      // ALL roadblocks must be satisfied
      return roadblockResults.every(result => result);
    } else {
      // OR condition: AT LEAST ONE roadblock must be satisfied
      return roadblockResults.some(result => result);
    }
  }

  /**
   * Check if the current label has a matching roadblock in the dialogue
   */
  hasRoadblockMatchInDialogue(currentLabel: string): boolean {
    if (!currentLabel) return false;

    const currentDialogueId = this.getCurrentDialogueId();
    if (!currentDialogueId) return false;

    // Extract the dialogue prefix (before the #) for roadblock filtering
    const dialoguePrefix = currentDialogueId.split('#')[0];

    // Get all roadblocks
    const allRoadblocks = this._roadBlockService.models;

    // Filter roadblocks for current dialogue using the prefix
    const dialogueRoadblocks = allRoadblocks.filter(roadblock =>
      roadblock.ID && roadblock.ID.startsWith(dialoguePrefix + '.')
    );

    // Check if any roadblock in this dialogue references the current label
    const hasMatch = dialogueRoadblocks.some(roadblock => {
      if (+roadblock.Type !== +RoadBlockType.SPOKE_IN || !roadblock.spokeElementId) {
        return false;
      }

      const spokeElement = this._levelHelperService.models.find((sp: any) => sp.elementId === roadblock.spokeElementId);
      if (!spokeElement?.text) {
        return false;
      }

      const referencedLabel = this._labelColorService.extractLabelFromSpokeText(spokeElement.text);
      return referencedLabel === currentLabel;
    });

    return hasMatch;
  }
}
