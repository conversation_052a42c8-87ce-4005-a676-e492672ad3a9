import { Component, OnInit, HostListener, AfterViewInit, OnD<PERSON>roy } from '@angular/core';
import { Subject } from 'rxjs';
import { PopupService } from 'src/app/services/popup.service';
import { Popup } from 'src/lib/darkcloud';
import { Button } from 'src/lib/darkcloud/popup/Button';
import { Index } from 'src/lib/others';

@Component({
  selector: 'app-popup',
  templateUrl: './popup.component.html',
  styleUrls: ['./popup.component.scss'],
})
export class PopupComponent implements OnInit, AfterViewInit, OnDestroy {

  constructor(
  protected _popupService: PopupService) {}
  public popupInterfaces: Popup.Interface<any, any>[];
  public popupElement: HTMLElement;
  public selectedButton: Index<Popup.Button<any>> = {};
  public btnSelected: any;
  public btnSelectedClose: any;
  timeout:any;
  refreshBtn: boolean = false;
  btnClose: boolean = false;
  nullValue: Index<Popup.Button<{}>> = {};
  
  @HostListener('document:click', ['$event'])
  onClick(event) 
  {
    if (this.popupElement && event.target.className.includes('close-on-click'))
    {
      this._popupService.close();
      return;
    }
  }

  ngOnInit() 
  {
    this.popupInterfaces = [];
    this.popupInterfaces.push(this._popupService.popupInterface);
    this.refreshBtn = this._popupService.refreshBtn;
     if (this._popupService.popupInterface?.inputSettings?.inputButton) 
    {
      const buttonSelected = this._popupService.popupInterface.buttons.find(
        (button) => button.value === this._popupService.popupInterface.inputSettings.inputButton.value
      );
      this.btnSelected = buttonSelected;     
      if (buttonSelected) 
      {
        if (this.popupInterfaces[0]?.inputSettings?.next) 
        {
          this.selectedButton[0] = buttonSelected;
          const a = this.popupInterfaces[0].inputSettings.next(buttonSelected);
          this.popupInterfaces.push(a);
        }
      }
    } 
  }

  ngAfterViewInit(): void 
  {
    this.timeout = setTimeout(() => 
    {
        this.popupElement = document.getElementById('popup');
    }, 100);
  }

  public ClickButton(interfaceIndex: number, buttonIndex: number) 
  {  
   
    if (this.popupInterfaces[interfaceIndex]?.inputSettings?.next) 
    {
      if (this.popupInterfaces.length > interfaceIndex + 1) 
      {
        this.popupInterfaces.splice(interfaceIndex + 1);
      }
      const button = this.popupInterfaces[interfaceIndex].buttons[buttonIndex];
      //Get selected element
      this.btnSelectedClose = button;  
     
      this.selectedButton[interfaceIndex] = button;
      const a = this.popupInterfaces[interfaceIndex].inputSettings.next(button);
      this.popupInterfaces.push(a);
    } 
    else 
    {   
      this._popupService.close(this.popupInterfaces[interfaceIndex].buttons[buttonIndex]);
    }
  }

  closeReferencePopup(interfaceIndex: number) {

  
    if (this.popupInterfaces.length > 1) 
      {
        this.popupInterfaces.pop().buttons[-1];        
        
      } else {
        this._popupService.close();
      }
  }

  ngOnDestroy() 
  {
    clearInterval(this.timeout);   
  }

}
