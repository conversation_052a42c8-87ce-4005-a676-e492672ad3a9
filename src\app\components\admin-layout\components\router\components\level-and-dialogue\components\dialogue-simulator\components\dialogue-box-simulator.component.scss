/*--------------------
SCSS Mixins for Reusable Styles
--------------------*/

/* Mixin for perfect centering using transform */
@mixin center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* Mixin for animated loading dots in typing animation */
@mixin ball {
  @include center;
  content: '';
  display: block;
  width: 3px;
  height: 3px;
  border-radius: 50%;
  background: rgba(255, 255, 255, .5);
  z-index: 2;
  margin-top: 4px;
  animation: ball .45s cubic-bezier(0, 0, 0.15, 1) alternate infinite;
}

/*--------------------
Main <PERSON><PERSON>er (Legacy - Not Used in Current Implementation)
--------------------*/
.chat {
  @include center;
  width: 300px;
  height: 80vh;
  max-height: 500px;
  z-index: 2;
  overflow: hidden;
  box-shadow: 0 5px 30px rgba(0, 0, 0, .2);
  background: rgba(0, 0, 0, .5);
  border-radius: 20px;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}

/*--------------------
Chat Header Section (Legacy)
--------------------*/
.chat-title {
  flex: 0 1 45px;
  position: relative;
  z-index: 2;
  background: rgba(0, 0, 0, 0.2);
  color: #fff;
  text-transform: uppercase;
  text-align: left;
  padding: 10px 10px 10px 50px;

  h1, h2 {
    font-weight: normal;
    font-size: 10px;
    margin: 0;
    padding: 0;
  }

  h2 {
    color: rgba(255, 255, 255, .5);
    font-size: 8px;
    letter-spacing: 1px;
  }

  .avatar {
    position: absolute;
    z-index: 1;
    top: 8px;
    left: 9px;
    border-radius: 30px;
    width: 30px;
    height: 30px;
    overflow: hidden;
    margin: 0;
    padding: 0;
    border: 2px solid rgba(255, 255, 255, 0.24);

    img {
      width: 100%;
      height: auto;
    }
  }
}

/*--------------------
Messages
--------------------*/
.messages-content {
  /* position: absolute;
  top: 0;
  left: 0; */
  height: 101%;
  width: 100%;
}

.message.clickable:hover
{
  background: rgba(80, 80, 80, 0.3);
  cursor: pointer;
}

.message.left>span
{
  font-size: 12px;
  display: block;
  text-align: left;
  margin-bottom: 6px;
}
.message.right>span
{
  font-size: 12px;
  display: block;
  margin-bottom: 6px;
}

.message.option.disabled {
  opacity: 0.5;
  pointer-events: none;
  text-decoration: line-through;
}

.message {
  clear: both;
  float: left;
  padding: 9px 15px 11px;
  border-radius: 10px 10px 10px 0;
  background: rgba(0, 0, 0, .3);
  margin: 8px 0;
  font-size: 18px;
  line-height: 1.4;
  margin-left: 35px;
  margin-right: 35px;
  margin-bottom: 12px;
  position: relative;
  text-shadow: 0 1px 1px rgba(0, 0, 0, .2);
  color: rgb(204, 204, 204);
  text-align: left;
  word-break: break-word;

  &.selected {
    background-color: rgba(128, 86, 9, 0.2);
    opacity: 1 !important;
    text-decoration: auto !important;
  }

  &.right {
    border-radius: 10px 10px 0 10px;
    float: right;
    text-align: right;
  }

  .timestamp {
    position: absolute;
    bottom: -15px;
    font-size: 9px;
    color: rgba(255, 255, 255, .3);
  }

  &.left::before {
    content: '';
    position: absolute;
    bottom: -6px;
    border-top: 6px solid rgba(0, 0, 0, .3);
    left: 0;
    border-right: 7px solid transparent;
  }

  &.right::before {
    content: '';
    position: absolute;
    bottom: -6px;
    border-top: 6px solid rgba(0, 0, 0, .3);
    right: 0;
    border-left: 7px solid transparent;
  }

  .avatar {
    position: absolute;
    z-index: 1;
    bottom: -15px;
    left: -35px;
    border-radius: 30px;
    width: 30px;
    height: 30px;
    overflow: hidden;
    margin: 0;
    padding: 0;
    border: 2px solid rgba(255, 255, 255, 0.24);

    img {
      width: 100%;
      height: auto;
    }
  }

  &.message-personal {
    float: right;
    color: #fff;
    text-align: right;
    background: linear-gradient(120deg, #248A52, #257287);
    border-radius: 10px 10px 0 10px;

    &::before {
      left: auto;
      right: 0;
      border-right: none;
      border-left: 5px solid transparent;
      border-top: 4px solid #257287;
      bottom: -4px;
    }
  }

  &:last-child {
    margin-bottom: 30px;
  }

  &.new {
    transform: scale(0);
    transform-origin: 0 0;
    animation: bounce 400ms linear both;
  }

  &.loading {
    color: rgb(204, 204, 204);
    width: 70px;
    padding-left: 15px;
    padding-right: 3px;
    height: 40px;

    /* &::before {
      @include ball;
      border: none;
      animation-delay: .15s;
    } */

    & span {
      display: inline-block;
      font-size: 0;
      width: 15px;
      height: 10px;
      position: relative;

      &::before {
        @include ball;
        margin-left: -7px;
      }

      &.b2::before {
        @include ball;
        margin-left: -7px;
        animation-delay: .2s;
      }

      &.b3::before {
        @include ball;
        margin-left: -7px;
        animation-delay: .4s;
      }
/*
      &::after {
        @include ball;
        margin-left: 7px;
        animation-delay: .3s;
      } */
    }
  }
  .icon
  {
    display: inline-block;
    font-size: 16px;
    margin-right: 8px;
  }

  &.option
  {
    width: calc(100% - 70px);
    float: left;
    text-align: left;
  }
}


.tableOptions
{
  float: right;
  table-layout: fixed;
  clear: both;

  tr
  {
    width: 100%;

    td
    {
      width: 100%;
    }
  }
}

.material-icons
{
    display: inline-block;
    font-size: 20px !important;
    margin-right: 8px;
    vertical-align: middle;

}

.sms-icon
{
  transform: scaleX(-1);
}

.announcement-icon
{
  transform: scaleX(-1);
}

.icon-png
{
  width: 20px;
  height: 20px;
  vertical-align: middle;
  margin-right: 8px;
  transform: scaleX(-1);
}





/*--------------------
Message Box
--------------------*/
.message-box {
  flex: 0 1 40px;
  width: 100%;
  background: rgba(0, 0, 0, 0.3);
  padding: 10px;
  position: relative;

  textarea:focus:-webkit-placeholder{
      color: transparent;
  }

  & .message-submit {
    position: absolute;
    z-index: 1;
    top: 9px;
    right: 10px;
    color: #fff;
    border: none;
    background: #248A52;
    font-size: 18px;
    text-transform: uppercase;
    line-height: 1;
    padding: 6px 10px;
    border-radius: 10px;
    outline: none!important;
    transition: background .2s ease;

    &:hover {
      background: #1D7745;
    }
  }
}

/* Roadblock bracket system */
.dialogue-box-container {
  position: relative;
  width: 100%;
  /* Ensure container properly contains floated elements */
  overflow: hidden;
  min-height: 1px;
}

.dialogue-content {
  position: relative;
  width: 100%;
  /* Add clearfix to contain floated messages */
  &::after {
    content: "";
    display: table;
    clear: both;
  }
}

.roadblock-bracket {
  position: absolute;
  left: 10px;
  top: 5px;
  bottom: 5px;
  width: 25px;
  pointer-events: none; /* Allow clicks to pass through to content */
  min-height: 50px; /* Ensure minimum height for proper bracket display */
}

/* Individual lines that make up the roadblock bracket */
.roadblock-bracket-line {
  position: absolute;
  left: 0;
  background-color: #ff66cc; /* Pink color for roadblock indicators */
  border-radius: 1px;
}

/* Top horizontal line of the bracket */
.roadblock-bracket-top {
  top: 0;
  width: 20px;
  height: 3px;
}

/* Vertical line of the bracket with centered lock icon */
.roadblock-bracket-middle {
  top: 0;
  width: 3px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* Bottom horizontal line of the bracket */
.roadblock-bracket-bottom {
  bottom: 0;
  width: 20px;
  height: 3px;
}

/* Lock icon in the center of the roadblock bracket */
.roadblock-lock-icon {
  color: #fff;
  font-size: 14px;
  background-color: #ff66cc;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: -8px; /* Offset to center on the vertical line */
  border: 1px solid #ff66cc;
}

/*--------------------
Custom Scrollbar Styling (Legacy)
--------------------*/
.mCSB_scrollTools {
  margin: 1px -3px 1px 0;
  opacity: 0; /* Hide scrollbar by default */
}

.mCSB_inside > .mCSB_container {
  margin-right: 0px;
  padding: 0 10px;
}

.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  background-color: rgba(0, 0, 0, 0.5)!important;
}

/*--------------------
Animation Keyframes
--------------------*/

/* Bounce animation for message appearance using 3D matrix transforms */
@keyframes bounce {
  0% { transform: matrix3d(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1); }
  4.7% { transform: matrix3d(0.45, 0, 0, 0, 0, 0.45, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1); }
  9.41% { transform: matrix3d(0.883, 0, 0, 0, 0, 0.883, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1); }
  14.11% { transform: matrix3d(1.141, 0, 0, 0, 0, 1.141, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1); }
  18.72% { transform: matrix3d(1.212, 0, 0, 0, 0, 1.212, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1); }
  24.32% { transform: matrix3d(1.151, 0, 0, 0, 0, 1.151, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1); }
  29.93% { transform: matrix3d(1.048, 0, 0, 0, 0, 1.048, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1); }
  35.54% { transform: matrix3d(0.979, 0, 0, 0, 0, 0.979, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1); }
  41.04% { transform: matrix3d(0.961, 0, 0, 0, 0, 0.961, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1); }
  52.15% { transform: matrix3d(0.991, 0, 0, 0, 0, 0.991, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1); }
  63.26% { transform: matrix3d(1.007, 0, 0, 0, 0, 1.007, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1); }
  85.49% { transform: matrix3d(0.999, 0, 0, 0, 0, 0.999, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1); }
  100% { transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1); }
}

/* Ball animation for typing indicator dots */
@keyframes ball {
  from {
    transform: translateY(0) scaleY(.8); /* Start position with slight vertical compression */
  }
  to {
    transform: translateY(-10px); /* Move up 10px */
  }
}


