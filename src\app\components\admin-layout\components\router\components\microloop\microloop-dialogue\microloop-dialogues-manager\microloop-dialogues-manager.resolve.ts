import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Injectable } from '@angular/core';
import { DialogueService } from 'src/app/services/dialogue.service';
import { Dialogue, Level } from 'src/app/lib/@bus-tier/models';
import { Preloadable } from '../../../level-and-dialogue/components/level-dialogues-manager/level-dialogues-manager.component';
import { MicroloopService } from 'src/app/services/microloop.service';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';

@Injectable({
  providedIn: 'root',
})
export class MicroloopDialoguesManagerResolve implements Resolve<Preloadable> {
  constructor(
    private _dialogueService: DialogueService,
    private _microloopService: MicroloopService,
    private _userSettingsService: UserSettingsService
  ) {}

  async resolve(snapshot: ActivatedRouteSnapshot): Promise<Preloadable> {
    const levelId = snapshot.paramMap.get('levelId');

    await this._microloopService.toFinishLoading();
    await this._dialogueService.toFinishLoading();
    await this._userSettingsService.toFinishLoading();

    const level = this._microloopService.svcCloneById(levelId);
    let dialogues = await this.toTryCreateRemainingDialogues(level);

    dialogues = dialogues.filter((v,i,a) => 
      a.findIndex(t => (t.id === v.id))===i
    );

    return {
      dialogues,
      level,
    };
  }

  async toTryCreateRemainingDialogues(level: Level): Promise<Dialogue[]> {
    const availableDialogues = this._dialogueService.svcFilterByIds(
      level.dialogueIds
    );

    if (availableDialogues.length < 5) {
      await this._microloopService.toCreateRemainingDialogues(level);
    }

    await this._microloopService.toOrderDialogues(level);

    return this._dialogueService.svcCloneByIds(level.dialogueIds, true);
  }
}