import { AfterViewInit, Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Area, Level, Scenery } from 'src/app/lib/@bus-tier/models';
import { Button } from 'src/app/lib/@pres-tier/data';
import { Interpreter } from 'src/app/lib/Interpreter';
import { AreaService, CharacterService, ConditionService, DialogueService, ItemService, LevelService, PopupService, ReviewService, SceneryService, SpeechService, UserSettingsService } from 'src/app/services';
import { LanguageService } from 'src/app/services/language.service';
import { MicroloopContainerService } from 'src/app/services/microloop-container.service';
import { MicroloopService } from 'src/app/services/microloop.service';
import { TranslationService } from 'src/app/services/translation.service';
import { Alert, Popup } from 'src/lib/darkcloud';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';
import { ConditionType, LevelType } from 'src/lib/darkcloud/dialogue-system';
import { language } from 'src/lib/darkcloud/dialogue-system/game-types';
import { HighlightElement } from 'src/lib/others';

@Component({
  selector: 'app-microloop',
  templateUrl: './microloop.component.html',
  styleUrls: ['./microloop.component.scss']
})
export class MicroloopComponent extends TranslatableListComponent<Level> implements AfterViewInit 
{
  public container: Area;
  public override listName: string = '';
  language: language = 'PT-BR';
  LevelType = LevelType;
  loops: Level[] = [];


  constructor(
    _activatedRoute: ActivatedRoute,
    private _microloopService: MicroloopService,
    _userSettingsService: UserSettingsService,
    private _router: Router,
    private _reviewService: ReviewService,
    private _dialogueService: DialogueService,
    private _sceneryService: SceneryService,
    private _areaService: AreaService,
    private _popupService: PopupService,
    private _microloopContainerService: MicroloopContainerService,
    private _conditionService: ConditionService,
    private _characterService: CharacterService,
    private _itemService: ItemService,
    private _speechService: SpeechService,
    protected override _translationService: TranslationService,
    protected override _languageService: LanguageService,
    private _levelService: LevelService
  )
  {
    super(_microloopService, _activatedRoute, _userSettingsService, 'name', _translationService, _languageService);
  }


  override lstInit()
  {
    this._microloopService.svcReviewAll();
  }
  
  override lstAfterFetchList()
  {
    this.container = this._microloopContainerService.svcFindById(this._activatedRoute.snapshot.url[1]?.path);
    this.listName = this.container.name + ' Loop List';
    
    this.lstIds = this.container.levelIds;
    this.loops = this._microloopService.svcCloneByIds(this.lstIds);

    this._activatedRoute.fragment.subscribe(fragment => 
    {
      HighlightElement(fragment, 200, true);
    });
  }

  public redirectToDialogueManager(loop: Level)
  {
    this.updateInformation(loop, 'enablesDialogue', true);
    this._router.navigate(['microloops/' + loop.id + '/dialogues']);
  }
  
  public async toPromptChangeScenery(loop: Level)
  {
    const selectedSceneryButton = await this._popupService.fire<Area, Scenery>(
      new Popup.Interface(
        {
          title: 'Select Area',
          actionsClass: 'column'
        },
        Popup.toButtonList(
          this._areaService.models,
          {
            parameters: ['hierarchyCode', 'name'],
            mapping: '[<hierarchyCode>]: <name>'
          },
          { undefinedTitle: 'No Area' }
        ),
        {
          inputButton: 
          {
            value: this._areaService.svcFindById(Area.getSubIdFrom(loop.id)),
          },
          next: (selectedAreaButton) => 
          {
            if(!selectedAreaButton) return null;

              return new Popup.Interface
              ({
                title: 'Scenery',
                actionsClass: 'column'
              },
              Popup.toButtonList(
                this._sceneryService.models.filter(scenery => scenery.areaId === selectedAreaButton.value?.id),
                'name',
                {
                  undefinedTitle: 'No Scenery'
                }
              ),
              {
                inputButton: 
                {
                  value: this._sceneryService.svcFindById(loop.sceneryId)
                }
              })
          }
        }
      )
    );
    loop.sceneryId =  selectedSceneryButton.value ? selectedSceneryButton.value.id : '';
    await this._microloopService.svcToModify(loop);
    await this._sceneryService.svcReviewAll();
  }

  public override readonly addButtonTemplate: Button.Templateable = 
  {
    title: 'Add a new instance to the list',
    onClick: this.createNewLoop.bind(this),
    iconClass: 'pe-7s-plus',
    btnClass: Button.Klasses.FILL_GREEN,
  };

  async createNewLoop()
  {
    const name = await Alert.showPrompt('Loop Name');
    if(this.isMicroloopAlreadyCreated(name))
    {
      Alert.showError(`The area: ${name} already exists!`);
      return;
    }
    else if(name == '')
    {
      Alert.showError('You need to give a name to the loop!');
      return;
    }
    else if(name == undefined) return;

    let newLoop = await this._microloopService.svcPromptCreateNew(this.container.id, name);

    this.lstIds = this.lstIds.concat(newLoop.id);
    this.container.levelIds.push(newLoop.id);

    await this._microloopService.srvAdd(newLoop);
    await this._microloopContainerService.svcToModify(this.container);
    await this.updatePage(newLoop);
    //this.ngOnInit();
  }
  
  updatePage(newLoop)	
  {	
    this._router.navigate(['microloopList/']);	
    setTimeout(()=>	
    {	
      this._router.navigate(['microloopList/' + newLoop.id.split('.')[0] + '/microloops']);	
      this.lstResetHighlights();
      this.HighlightElement(newLoop.id, 100, true);
    },1)	
  }

  isMicroloopAlreadyCreated(areaName: string)
  {
    for(let i = 0; i < this._microloopService.models.length; i++)
    {
      if(this._microloopService.models[i]?.name == areaName)
      {
        return true;
      }
    }
    return false;
  }

  async removeLoop(loop: Level)
  {
    const confirm = await Alert.showConfirm('Area you sere you want to delete ' + loop.name + '?', '', 'Yes');
    if(!confirm) return;

    this.lstIds = this.lstIds.filter(x => x != loop.id);
    this.container.levelIds = this.container.levelIds.filter(x => x != loop.id);

    this._microloopContainerService.svcToModify(this.container);
    this._microloopService.svcToRemove(loop.id);
    this.ngOnInit();
  }
  
  redirectToMicroloopList()
  {
    this._router.navigate(['microloopList']);
  }

  markAsUnique(loop: Level)
  {
    if(loop.isUnique != undefined)
      loop.isUnique = !loop.isUnique;
    else
      loop.isUnique = true;

    this._microloopService.svcToModify(loop);
  }

  async markAsFirst(loop: Level)
  {
    if(loop.isFirst != undefined)
      loop.isFirst = !loop.isFirst;
    else
      loop.isFirst = true;
    
    await this._microloopService.svcToModify(loop);

    for(let i = 0; i < this._microloopService.models.length; i++)
    {
      if(this._microloopService.models[i].isFirst == undefined) this._microloopService.models[i].isFirst = false;
      if(this._microloopService.models[i].id != loop.id && this._microloopService.models[i].isFirst)
      {
        this._microloopService.models[i].isFirst = false;
        await this._microloopService.svcToModify(this._microloopService.models[i]);
        break;
      }
    }
    this.container = this._microloopContainerService.svcFindById(this._activatedRoute.snapshot.url[1]?.path);
    this.lstIds = this.container.levelIds;
    this.loops = this._microloopService.svcCloneByIds(this.lstIds);
    this.ngOnInit();
  }


  toPromptAddCondition(loop: Level)
  {
    const interpreter = new Interpreter
    ({
      characters: this._characterService.models,
      items: this._itemService.models
    });

    let conditionsToString = '\n';
    loop.conditionIds?.forEach((id, index) => 
    {
      const condition = this._conditionService.svcFindById(id);
      conditionsToString +=
      index +
      1 +
      ': ' +
      interpreter.interpretInterface(condition, 'Condition') +
      '\n';
    });
    Alert.showOptions(
      'Certain death if the player...',
      'Condition',
      conditionsToString,
      'Select'
    ).then(async result => 
      {
      if(!result) return;
      
      switch(result)
      {

        case Alert.AlertOptions.POP:
          if(loop.conditionIds.length > 0){
            await this._conditionService.svcToRemove(
              loop.conditionIds[loop.conditionIds.length - 1]
            );
            this._microloopService.popCondition(loop);
          }
          break;
        case Alert.AlertOptions.CLEAR:
          await this._conditionService.svcToRemove(loop.conditionIds);
          this._microloopService.clearConditions(loop);
          break;
        case Alert.AlertOptions.ADD:
          const type = await Alert.showRadioEnum('Select Condition Type',
          {
            [+ConditionType.LOADSOUT_BOSS]: 'Loads out Boss',
            [+ConditionType.LOADSOUT_ITEM]: 'Loads out Item'
          });
          if(type === undefined)
            return;
          const conditionInstance = await this._conditionService.svcPromptCreateNew(type, loop);
          if(conditionInstance == undefined)
          {
            return;
          }
          await this._conditionService.srvAdd(conditionInstance);
          this._microloopService.addCondition(loop, conditionInstance);
          break;
      }
      this.toPromptAddCondition(loop);
    });
  }

  downloadMicroloopOrtography(loop: Level)
  {
    this._translationService.getLevelOrtography(loop, true);
  }

}
