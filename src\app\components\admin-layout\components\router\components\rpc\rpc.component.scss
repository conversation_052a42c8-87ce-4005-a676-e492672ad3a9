.main-menu-efect {
  padding-right: 15px;
  padding-left: 15px; 
  min-height: calc(100% - 210px);
}

.update {
    margin: -2px -2px 0;
    padding-bottom: 5px;
    background-color: #f7f7f6;
    margin-top: 15px;
}

.card-header-content {
display: block;
margin-left: 20px;
margin-right: 15px;
width: 30%;
}

.card {
  // padding-top: 8px !important;
   padding-top: 17px !important;
   padding-bottom: 10px;
 }

 .card-header-content {
   display: block;
   margin-left: 30px;
   margin-right: 15px;
   width: 30%;
 }

 //tabela
 .card-container 
{
  display: flex;
  flex-direction: column;
  align-items: center;

  .card 
  {
    border: 1px solid #ccc;
    padding: 15px;
    margin: 5px;
    width: 50vw;
    opacity: 1;
    transition: opacity 0.5s ease-in-out;
  } 
}


.title {
  padding-top: 5px;
}

h3{
    font-size: 28px;
    margin: 10px 0 10px;
}

.borderList {
    border: 1px solid #ddd !important;
}

.addButton
{  
 position: absolute;
    top: -1px;
    right: 13px;
}

  .default-color {
    background-color: #AEAAAA !important;
  }

  .aligTitle {
    background-color: white !important;
    color: #565656 !important;
    text-align-last: center !important;
    width: 8%;
  }

  .noCursor {
    cursor: default !important;
  }
  
  .text-center {
    text-align: center !important;
  }
  .width-buscontext {
    width: 400px !important;
  }
  .paddingTop {
    padding-top: 0px !important;
  }

  // Estilos das tabelas
  .rpc-table-container {
    overflow-x: auto;
  }

  .rpc-table {
    width: 100%;
    border-collapse: collapse;
    font-family: Arial, sans-serif;
    font-size: 14px;
    background-color: white;

    th, td {
      border: 1px solid #666;
      text-align: center;
      padding: 8px 12px;
      vertical-align: middle !important;
      font-weight: normal !important;
    }

    thead {
      background-color: #666;
      color: white;

    }

    tbody {
      .ailment-row {
        &:nth-child(even) {
          background-color: #f9f9f9;
        }
      }
    }
  }
  
h3{
    font-size: 25px;
    margin: 10px 0 10px;
}

span {
      color: black;
      font-size: 25px;
      font-weight: 600;
    }

.addButton
{  
 position: absolute;
    top: -3.5px;
    right: 13px;

}
.btn-excel {
  top: 330px;
  position: absolute;
  right: 50px;
}

.btn-excel-boos {
  position: absolute;
    display: flex;
    right: 48px;
    margin-top: -57px;
}

// Estilos para as tabelas RPC baseado na imagem de referência
.table-header {
  background-color: #666;
  color: white;
  padding: 30px 0;
  position: relative;

  h3 {
    margin: 0;
    font-size: 28px;
    color: white;
    text-align: center;
    letter-spacing: 1px;
  }

  .addButton {
    position: absolute;
    top: 47%;
    right: 20px;
    transform: translateY(-50%);
  }
}

.description-cell {
  background-color: #ddd !important
}

.text-left {
  text-align: left !important;
}
.empty-td {
  background-color: gray;
}

