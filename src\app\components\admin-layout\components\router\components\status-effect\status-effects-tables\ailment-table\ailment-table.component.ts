import { Component, EventEmitter, Output } from '@angular/core';
import { AilmentTable } from 'src/app/lib/@bus-tier/models';
import { Button } from 'src/app/lib/@pres-tier/data';
import { AilmentTableService } from 'src/app/services';
import { Alert } from 'src/lib/darkcloud';

@Component({
  selector: 'app-ailment-table',
  templateUrl: './ailment-table.component.html',
  styleUrls: ['./ailment-table.component.scss']
})
export class AilmentTableComponent {

  
 titles = ['ID', 'CATEGORY', 'STATUS EFFECT NAME', 'DESCRIPTION', 'POWER POINTS (PP)', 'ALL'];
 listAilmentTable: AilmentTable[] = [];
 activeLanguage = 'PTBR';
 @Output() activeTab2 = new EventEmitter<string>();
 isListAilmentEmpty: boolean;

 public readonly excelButtonTemplate: Button.Templateable = {
   title: 'Paste content from excel',
   onClick: this.onExcelPaste.bind(this),
   iconClass: 'excel-icon',
   btnClass: Button.Klasses.FILL_ORANGE,
 };
 constructor(
   private _ailmentTableService: AilmentTableService
 ){}


 async ngOnInit(): Promise<void>{
   
     this.removeEmptyItems();
      this.listAilmentTable = this._ailmentTableService.models;
      this.isListAilmentEmpty = this.listAilmentTable.length === 0;  

   }

   removeEmptyItems() {
     this._ailmentTableService.toFinishLoading();
     this._ailmentTableService.models = this._ailmentTableService.models.filter(ailmentItem => ailmentItem.idAilment !== "");
     this._ailmentTableService.toSave();
   }

   async onExcelPaste() {
     const text = await navigator.clipboard.readText();
     const lines = text.split(/\r?\n/).filter(line => line);    
     const processedData: string[][] = [];
   
     if (lines.length > 0) {
       lines.forEach(line => {
         // Divide cada linha em colunas e remove a primeira coluna
         const values = line.split("\t").map(value => value.trim()).slice(1);
   
         processedData.push(values);
       });
   
       // Verifica se o número de colunas em cada linha corresponde ao número de colunas em this.titles
       const isColumnCountValid = processedData.every(row => row.length === this.titles.length);
   
       if (!isColumnCountValid) {
         Alert.showError('Invalid number of columns');
         return;
       }
 
       this._ailmentTableService.models = [];
       this._ailmentTableService.toSave();
   
       for (let index = 0; index < processedData.length; index++) {
         this._ailmentTableService.createNewnAilmentTable(processedData[index]);
       }    

       Alert.ShowSuccess('Ailment Table imported successfully!');
       this.activeTab2.emit('ailmentTable');
       this.ngOnInit();
     }
   }
   

   changeAilment(rowIndex: number, name: string, newValue: string){

     if (name === 'idAilment') {
      this.listAilmentTable[rowIndex].idAilment = newValue;        
     }
     else if (name === 'category') {
       this.listAilmentTable[rowIndex].category = newValue;        
      }
      else if (name === 'statusEffectName') {
       this.listAilmentTable[rowIndex].statusEffectName = newValue;        
      }
      else if (name === 'description') {
       this.listAilmentTable[rowIndex].description = newValue;        
      }
      else if (name === 'powerPoints') {
       this.listAilmentTable[rowIndex].powerPoints = newValue;        
      }
      else if (name === 'allAilment') {
       this.listAilmentTable[rowIndex].allAilment = newValue;        
      }

     this._ailmentTableService.svcToModify(this.listAilmentTable[rowIndex]);
   }    


}
