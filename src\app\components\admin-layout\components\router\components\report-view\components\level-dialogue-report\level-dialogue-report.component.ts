import { Component, OnInit } from '@angular/core';
import { EasyMVC } from 'src/lib/darkcloud/angular';
import { Marker } from 'src/app/lib/@bus-tier/models';
import { AreaService } from 'src/app/services';
import { Accessment, Sortment } from 'src/app/lib/@pres-tier';
import { RoadBlockService } from 'src/app/services/road-block.service';
import { ActivatedRoute, Router } from '@angular/router';

export interface Preloadable {
  finishDialogueMarkers: Marker[];
  releaseDialogueMarkers: Marker[];
}

@Component({
  selector: 'app-level-dialogue-report',
  templateUrl: './level-dialogue-report.component.html',
})
export class LevelDialogueReportComponent extends EasyMVC.PreloadComponent<Preloadable> implements OnInit
{
  sorting: Sorting = new Sorting(this);
  accessing: Accessing = new Accessing(this);
  constructor(
    private _activatedRoute: ActivatedRoute,
    public readonly router: Router, 
    private _roadblockService: RoadBlockService,
    public readonly areaService: AreaService
  ) 
  {
    super(_activatedRoute, (data) => data.preloadedLevelDialogueReportData);
  }

  ngOnInit(): void {}

  public redirectToDialoguesManager(id: string) 
  {
    let levelId = this._roadblockService.getLevelId(id);
    let dialogueId = this._roadblockService.getDialogueId(id);

    if(id.split(".")[0].includes("ML"))
    {
      this.router.navigate(['microloops/' + levelId + '/dialogues/' + dialogueId],
      {fragment: id});
    }
    else
    {
      this.router.navigate(['levels/' + levelId + '/dialogues/' + dialogueId],
      {fragment: id});
    }
  }
}

class Sorting extends Sortment.Sorting 
{
  byLocation = Sortment.byLocation(this._component.areaService);
  byLevelLocation = Sortment.byLevelLocation(this._component.areaService);
  byPin = (marker: Marker) => marker.pin;
  byType = (marker: Marker) => marker.type;

  constructor(private readonly _component: LevelDialogueReportComponent) 
  {
    super();
  }

  public sortFinishDialogueMarkers(by: Sortment.Sortion<Marker>) 
  {
    this.execute(this._component.preloadedData.finishDialogueMarkers, by);
  }
  public sortReleaseDialogueMarkers(by: Sortment.Sortion<Marker>) 
  {
    this.execute(this._component.preloadedData.releaseDialogueMarkers, by);
  }
}

class Accessing extends Accessment.Accessing 
{
  towardsLevelList = Accessment.towardsLevelList<Marker>((m) => m.levelId);
  towardsDialogueEditor = Accessment.towardsDialogueEditor<Marker>((m) => m.id);
  public accessMarker(marker: Marker, towards: Accessment.Accession<Marker>) 
  {
    this.execute(marker, towards);
    
  }
}
