import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';
import { Tag } from './Tag';

export class MemoryModule
  extends Base<Data.Hard.IMemoryModule, Data.Result.IMemoryModule>
  implements Required<Data.Hard.IMemoryModule>
{
  protected static generateId(index: number): string {
    return IdPrefixes.MEMORY_MODULE + index;
  }

  constructor(
      index: number,
      itemId: string,
      dataAccess: MemoryModule['TDataAccess']
  )
  {
      super({hard: {id: MemoryModule.generateId(index), itemId}}, dataAccess);
  }

  public get itemId(): string
  {
    return this.hard.itemId;
  }
  public set itemId(value: string)
  {
    this.hard.itemId = value;
  }

  public get memoryModule(): string[]
  {
    return this.hard.memoryModule;
  }
  public set memoryModule(value: string[])
  {
    this.hard.memoryModule = value;
  }

  public get labLevel(): number
  {
    return this.hard.labLevel;
  }
  public set labLevel(value: number)
  {
    this.hard.labLevel = value;
  }

  public get slots(): number
  {
    return this.hard.slots;
  }
  public set slots(value: number)
  {
    this.hard.slots = value;
  }

  public get qubits(): number
  {
    return this.hard.qubits;
  }
  public set qubits(value: number)
  {
    this.hard.qubits = value;
  }

  public get nomenclature(): string
  {
    return this.hard.nomenclature;
  }
  public set nomenclature(value: string)
  {
    this.hard.nomenclature = value;
  }

  public get creationSouls(): number
  {
    return this.hard.creationSouls;
  }
  public set creationSouls(value: number)
  {
    this.hard.creationSouls = value;
  }

  public get creationTime(): number
  {
    return this.hard.creationTime;
  }
  public set creationTime(value: number)
  {
    this.hard.creationTime = value;
  }

  public get creationRubies(): number
  {
    return this.hard.creationRubies;
  }
  public set creationRubies(value: number)
  {
    this.hard.creationRubies = value;
  }

  public get installationSouls(): number
  {
    return this.hard.installationSouls;
  }
  public set installationSouls(value: number)
  {
    this.hard.installationSouls = value;
  }

  public get installationTime(): number
  {
    return this.hard.installationTime;
  }
  public set installationTime(value: number)
  {
    this.hard.installationTime = value;
  }

  public get installationRubies(): number
  {
    return this.hard.installationRubies;
  }
  public set installationRubies(value: number)
  {
    this.hard.installationRubies = value;
  }

  public get particlesValue(): number[]
  {
    return this.hard.particlesValue;
  }
  public set particlesValue(value: number[])
  {
    this.hard.particlesValue = value;
  }

  public get particlesNames(): string[]
  {
    return this.hard.particlesNames;
  }
  public set particlesNames(value: string[])
  {
    this.hard.particlesNames = value;
  }

}
