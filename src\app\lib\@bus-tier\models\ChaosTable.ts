import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';

export class ChaosTable extends Base<Data.Hard.IChaosTable, Data.Result.IChaosTable> implements Required<Data.Hard.IChaosTable>
{
  public static generateId(index: number): string {
    return IdPrefixes.CHAOSTABLE + index;
  }

  constructor( index: number, dataAccess: ChaosTable['TDataAccess']) 
  {
    super(
    {
      hard: 
      {
        id: ChaosTable.generateId(index),     
      },
    },
    dataAccess
    );
  }
  protected getInternalFetch()
  {
    return {};
  }
  public get idChaosTable (): string
  {
    return this.hard.idChaosTable;
  }
  public set idChaosTable(value: string) 
  {
    this.hard.idChaosTable = value;
  }
  public get category(): string
  {
    return this.hard.category;
  }
  public set category(value: string) 
  {
    this.hard.category = value;
  } 
  public get statusEffectName(): string
  {
    return this.hard.statusEffectName;
  }
  public set statusEffectName(value: string) 
  {
    this.hard.statusEffectName = value;
  }
  public get description(): string
  {
    return this.hard.description;
  }
  public set description(value: string) 
  {
    this.hard.description = value;
  }
  public get powerPoints(): string
  {
    return this.hard.powerPoints;
  }
  public set powerPoints(value: string) 
  {
    this.hard.powerPoints = value;
  }
  public get allChaosTable(): string
  {
    return this.hard.allChaosTable;
  }
  public set allChaosTable(value: string) 
  {
    this.hard.allChaosTable = value;
  }
  public get duration(): string
  {
    return this.hard.duration;
  }
  public set duration(value: string) 
  {
    this.hard.duration = value;
  }
  public get target(): string
  {
    return this.hard.target;
  }
  public set target(value: string) 
  {
    this.hard.target = value;
  }

}
