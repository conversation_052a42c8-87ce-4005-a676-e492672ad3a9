

.card {
  // padding-top: 8px !important;
   padding-top: 15px !important;
   display: flex; justify-content: space-between;
 }

 .card-header-content {
   display: block;
   margin-left: 30px;  
   width: 28%;
 }
 
 .card-header-tooltip {
  display: flex;
  width: 30%;
 // margin-left: 35%;
  margin-bottom: 15px;
  color:white !important;
  background-color: black;
  padding:10px;
  z-index: 999;
  border-radius: 10px;
}

 .sticky th
{
  background-color: rgba(255, 255, 255, 0);
  top: 0px;
}
th 
{
  width: 200px !important; /* Adjust the width value as needed */
  height: 35px !important;
}

input
{
  width: 100px !important;
}
.col_index {
  padding-left: 20px;
  padding-right: 20px
}
.titleIngredients {
  display: flex;
  align-items: center;
  padding-left: 35%;
  width: 220px !important;
}
.contentIngredients {
 // display: flex; 
  width: 220px;
  justify-content: center;
 // padding: 0 !important;
}
.iconInter {
  text-align: center; 
  font-size: 30px !important; 
  margin-top: 8px;
}
i:hover{
  color: blueviolet;
  cursor: pointer;
}


/*Modal do sistema */
.item-overlay
{
    position: absolute;
    //border: 15px solid black;
    height: 100%;
    width: 100%;
    background-color: rgb(230, 230, 230);
    z-index: 150;
}

.popup-report
{ 
  border-radius: 8px;
  width: 400px;
  position: fixed;
 // left: 35%;
  padding: 24px;
  top: 23%;
 // transform: translate(-60%, -50%);
 transform: translate(-10%, -40%);
  z-index: 1000;
  opacity: 1;  

}
.modal-header {
  color: white !important;
  padding: 0px !important;

  .modal-title {
    text-align: left !important;
    margin-bottom: 7px;
    .close {
      opacity: 0px !important;
      margin-top: 0px !important;
    }
  }

   button {
    span {
      color: white !important;
    }
   }
}

.close {
  opacity: 1 !important;
 }

 .contextInfo {
  color: white;
  text-align: left;
  overflow-y: auto;
  white-space: pre-wrap;
  height: auto;
  max-height: 700px;
  scrollbar-width: thin;
  scroll-behavior: auto;  
  scrollbar-color: white black;
  padding-top: 10px;
}
.background-div {	
  position: relative;
  display: flex;
  justify-content: center;
  z-index: 9999;
}	

.background-div.popup-open:before 	
{	
  content: "";	
  position: fixed;	
  top: 0;	
  left: 0;	
  width: 100%;	
  height: 100%;	
  background-color: rgba(0, 0, 0, 0.5);	
  z-index: 9998;	
  pointer-events: none;	
}	

// FIM DO MODAL
