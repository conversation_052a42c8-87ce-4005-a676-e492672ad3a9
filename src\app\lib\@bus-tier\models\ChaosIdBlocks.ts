import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';

export class ChaosIdBlocks extends Base<Data.Hard.IChaosIdBlocks, Data.Result.IChaosIdBlocks> implements Required<Data.Hard.IChaosIdBlocks>
{
  public static generateId(index: number): string {
    return IdPrefixes.CHAOSIDBLOCKS + index;
  }

  constructor( index: number, dataAccess: ChaosIdBlocks['TDataAccess']) 
  {
    super(
    {
      hard: 
      {
        id: ChaosIdBlocks.generateId(index),     
      },
    },
    dataAccess
    );
  }
  protected getInternalFetch() 
  {
    return {};
  }
  public get positionName<PERSON>haos(): string[]
  {
    return this.hard.positionNameChaos;
  }
  public set positionName<PERSON>haos(value: string[]) 
  {
    this.hard.positionNameChaos = value;
  }

}
