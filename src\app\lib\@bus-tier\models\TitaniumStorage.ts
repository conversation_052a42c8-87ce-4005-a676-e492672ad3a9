import {
  CharacterType,
  Gender,
  IdPrefixes,
} from 'src/lib/darkcloud/dialogue-system';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { Base } from './Base';

export class TitaniumStorage
  extends Base<Data.Hard.ITitaniumStorage, Data.Result.ITitaniumStorage>
  implements Required<Data.Hard.ITitaniumStorage>
{
  private static generateId(index: number): string {
    return IdPrefixes.TITANIUM_STORAGE + index;
  }
  constructor(
    index: number,
    titaniumLevel: number,
    dataAccess: TitaniumStorage['TDataAccess']
  ) {
    super(
      {
        hard: {
          id: TitaniumStorage.generateId(index),
          titaniumLevel,
        },
      },
      dataAccess
    );
  }
  protected getInternalFetch() {
    return {};
  }

  public get titaniumLevel(): number
  {
    return this.hard.titaniumLevel;
  }
  public set titaniumLevel(value: number)
  {
    this.hard.titaniumLevel = value;
  }
  public get souls(): number
  {
    return this.hard.souls;
  }
  public set souls(value: number)
  {
    this.hard.souls = value;
  }
  public get time(): number
  {
    return this.hard.time;
  }
  public set time(value: number)
  {
    this.hard.time = value;
  }
  public get rubies(): number
  {
    return this.hard.rubies;
  }
  public set rubies(value: number)
  {
    this.hard.rubies = value;
  }
  public get storage(): number
  {
    return this.hard.storage;
  }
  public set storage(value: number)
  {
    this.hard.storage = value;
  }
  public get type(): string
  {
    return this.hard.type;
  }
  public set type(value: string)
  {
    this.hard.type = value;
  }

}
