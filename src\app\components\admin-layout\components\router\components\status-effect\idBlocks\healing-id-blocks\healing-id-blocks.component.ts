import { Component, EventEmitter, Output } from '@angular/core';
import { Alert } from '../../../../../../../../../lib/darkcloud';
import { HealingIdBlocks } from '../../../../../../../../lib/@bus-tier/models';
import { Button } from '../../../../../../../../lib/@pres-tier/data';
import { HealingIdBlockservice } from '../../../../../../../../services';

@Component({
  selector: 'app-healing-id-blocks',
  templateUrl: './healing-id-blocks.component.html',
  styleUrls: ['./healing-id-blocks.component.scss']
})
export class HealingIdBlocksComponent {

  titles = [1, 2, 3, 4, 5, 6];
  listHealing: HealingIdBlocks[] = [];
  activeLanguage = 'PTBR';
  @Output() activeTab2 = new EventEmitter<string>();

  public readonly excelButtonTemplate: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };
  constructor(
    private _healingtIdBlockservice: HealingIdBlockservice
  ){}


  async ngOnInit(): Promise<void>{
      this.removeEmptyItems();
      this.listHealing = this._healingtIdBlockservice.models; 
    }

    removeEmptyItems() {
      this._healingtIdBlockservice.toFinishLoading(); 

      this._healingtIdBlockservice.models = this._healingtIdBlockservice.models.filter(boostItem => {
        return this.titles.some((_, index) => boostItem.positionNameHealing[index] !== "");
      });     
      this._healingtIdBlockservice.toSave();    
    }
    
    async onExcelPaste() {
      const text = await navigator.clipboard.readText();
      const lines = text.split(/\r?\n/).filter(line => line);    
      const processedData: string[][] = [];
    
      if (lines.length > 0) {
        lines.forEach(line => {
          // Divide cada linha em colunas e remove a primeira coluna
          const values = line.split("\t").map(value => value.trim()).slice(1);
    
          processedData.push(values);
        });
    
        // Verifica se o número de colunas em cada linha corresponde ao número de colunas em this.titles
        const isColumnCountValid = processedData.every(row => row.length === this.titles.length);
    
        if (!isColumnCountValid) {
          Alert.showError('Invalid number of columns');
          return;
        }
  
        this._healingtIdBlockservice.models = [];
        this._healingtIdBlockservice.toSave();
    
        for (let index = 0; index < processedData.length; index++) {
          this._healingtIdBlockservice.createNewHealingIdBlocks(processedData[index]);
        }    

        Alert.ShowSuccess('Healing imported successfully!');
        this.activeTab2.emit('healing');
        this.ngOnInit();
      }
    }
    
 
    changeHealing(rowIndex: number, colIndex: number, newValue: string){    

      if (this.listHealing[rowIndex]) {
        this.listHealing[rowIndex].positionNameHealing[colIndex] = newValue;
        this._healingtIdBlockservice.svcToModify(this.listHealing[rowIndex]);
      }
    }
    
 

}
