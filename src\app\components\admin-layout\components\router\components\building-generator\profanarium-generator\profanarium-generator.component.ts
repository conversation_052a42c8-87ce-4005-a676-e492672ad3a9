import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/SortableListComponent';
import { Component } from '@angular/core';
import { Character, Profanarium} from 'src/app/lib/@bus-tier/models';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Button } from 'src/app/lib/@pres-tier/data';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { ProfanariumService} from 'src/app/services';
import { Alert } from 'src/lib/darkcloud';
import { SpinnerService } from './../../../../../../../spinner/spinner.service';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-profanarium-generator',
  templateUrl: './profanarium-generator.component.html',
  styleUrls: ['./profanarium-generator.component.scss'],
})

export class ProfanariumGeneratorComponent extends SortableListComponent<Profanarium> 
{
  description:string = "";
  override listName: string = 'WorkShop List';

  constructor(
    private spinnerService:SpinnerService,
    _activatedRoute: ActivatedRoute,
    protected _profanariumService: ProfanariumService,
    _userSettingsService: UserSettingsService,
    protected _languageService: LanguageService,
    protected _translationService: TranslationService
  ) 
  {
    super(_profanariumService, _activatedRoute, _userSettingsService, 'name');
  }
  public readonly excelButtonTemplate: Button.Templateable = 
  {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  public downloadSceneryOrtography(character: Character) 
  {
    this._translationService.getCharacterOrtography(character, true);
  }

  protected override lstInit() {}

  protected override lstAfterFetchList() 
  {
    if (this._profanariumService.models.length == 0) 
    {
      for (let l = 1; l <= 20; l++)       
        this._profanariumService.createNewProfanarium(l);
      
      this._profanariumService.toSave();
      this.lstFetchLists();
    }

      //remove empty element that just has lablevel == 0.
      this._profanariumService.models.find(blueprint => 
      {
        if(blueprint.profanariumLevel === 0)          
          this._profanariumService.svcToRemove(blueprint.id);
      })
    this.description = `Showing ${ this._profanariumService.models.length} results`;
  }

  async onExcelPaste(): Promise<void> 
  {
    this.spinnerService.setState(true);
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter(line => line);

    if(this.DisplayErrors(lines)) return;
    
    for (let l = 0; l < lines.length; l++) 
    {
      let line = lines[l];
      let cols = line.split(/\t/);
      let profanariumFields:string[] = ['improveTitanium', 'improveTime', 'improveRubies', 'researchSouls', 'researchTime', 'researchRubies'];

      let profanarium = this._profanariumService.models.find(
        (laboratory) =>
          laboratory.profanariumLevel ==
          +cols[0].split(' ')
          .join('')
          .split('.')
          .join('')
          .replace(',','.')
      );
      if (!profanarium) 
      {
        let labLevel = +cols[0].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.')
        profanarium = this._profanariumService.createNewProfanarium(labLevel);        
      }

      for(let j = 1; j < profanariumFields.length; j++)
      {
        if (cols[j]?.trim()) 
        {
          profanarium[profanariumFields[j]] = +cols[j]
          .split(' ')
          .join('')
          .split('.')
          .join('')
          .replace(',','.')
        } 
        else 
        {
          profanarium[profanariumFields[j]] = undefined;
        }
      }

      await  this._profanariumService.svcToModify(profanarium);
      await  this._profanariumService.toSave();
      Alert.ShowSuccess('WorkShop List imported successfully!');

      this.lstFetchLists();
    }
    this.spinnerService.setState(false);

  }

  DisplayErrors(array)
  {
    let count = array[0].split(/\t/)
    if(count.length < 7)
    {
      this.spinnerService.setState(false);
      Alert.showError("Copy the WORKSHOP LEVEL column values too!");
      return true;
    }
    
    if(count[0] === "")
    {
      this.spinnerService.setState(false);
      Alert.showError("You are probably copying a blank column!");
      return true;
    }
    return false;
  }
}
