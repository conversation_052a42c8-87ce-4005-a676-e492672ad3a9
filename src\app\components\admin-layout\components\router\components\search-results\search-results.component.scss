.search-results-table {
  > thead th {
    text-align: center;
  }
  //make the table header sticky
  > thead {
    position: sticky !important;
    //top: 250px;
    background-color: #e6e6e6;
    z-index: 40;
  }
  //table row height
  > tbody > tr {
    height: auto !important;
  }
  > tbody > tr > td {
    height: 100% !important;
  }
  .td-sort {
    background-color: #e6e6e6;
  }
  //no paddng for any type of table element
  //most squished as possible
  td {
    padding: 0 !important;
    padding-left: 5px !important;
    padding-right: 5px !important;
  }
  .td-relative {
    position: relative;
    height: 100%;
    width: 50%;
  }
  .td-auto {
    //to replace while compiling
    //width: auto;
    //to replace when building
    width: 1%;
  }
}

.result-accuracy-4 {
  background-color: #bcb0ff !important;
}
.result-accuracy-3 {
  background-color: #d4ccff !important;
}
.result-accuracy-2 {
  background-color: #e9e6ff !important;
}

.tr-clickable:hover {
  .result-accuracy-3,
  .result-accuracy-2,
  .result-accuracy-1 {
    background-color: rgb(194, 194, 194) !important;
  }
}

.search-bar-wrapper {
  display: flex;
  padding: 20px;
  i,
  input,
  div,
  p {
    margin: 20px 20px;
  }
  i {
    font-size: 50px;
  }
  .toggle {
    height: 50px;
    width: 50px;
  }
}

.cancel-button
{
  height: 50px;
  width: 50px;
}

.cancel-icon
{
  color: #bcb0ff00;
}

.centered-controls
{
  margin: auto;
  width: 50%;
  text-align: center;
}