import { Component } from '@angular/core';
import { Amplifiers } from 'src/app/lib/@bus-tier/models';
import { AmplifiersService } from 'src/app/services/amplifiers';

@Component({
  selector: 'app-amplifiers',
  templateUrl: './amplifiers.component.html',
  styleUrls: ['./amplifiers.component.scss'],
})
export class AmplifiersComponent {
  amplifiersList: Amplifiers[] = [];
  description: string = '';
  columnHeaders = [
    'HP_Amplifier',
    'ATK_Amplifier',
    'DEF_Amplifier',
    'EA_Amplifier1',
    'EA_Amplifier2',
    'EA_Amplifier3',
  ];

  constructor(
    private _amplifiersService: AmplifiersService
  ) {}

  async ngOnInit(): Promise<void> {

    this.columnHeaders.forEach(async (header) => {
      this.amplifiersList.push(
        await this._amplifiersService.createNewAmplifiers(header)
      );
    });
    await this._amplifiersService.toFinishLoading();

    setTimeout(() => {
      this.amplifiersList = this._amplifiersService.models;
      this.description = `Showing ${this.amplifiersList.length} results`;
    }, 100);
 
  }

  async changeAplifier(index: number, value: string) {
    this.amplifiersList[index].valueAmplifier = +value;
    this._amplifiersService.svcToModify(this.amplifiersList[index]);
    this._amplifiersService.toSave();
  }
}
