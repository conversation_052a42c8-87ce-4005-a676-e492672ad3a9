import { ChangeDetectorRef, Component, Input } from '@angular/core';
import { Area } from 'src/app/lib/@bus-tier/models';
import { Drop } from 'src/app/lib/@bus-tier/models/Drop';
import { Button } from 'src/app/lib/@pres-tier/data';
import { AreaService, ChestService, UserSettingsService } from 'src/app/services';
import { DropService } from 'src/app/services/drop.service';
import { ReviewService } from 'src/app/services/review.service';
import { TranslationService } from 'src/app/services/translation.service';
import { Alert } from 'src/lib/darkcloud';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc';
import { FILTER_SUFFIX_PATH } from '../../../../../../../../lib/darkcloud/angular/dsadmin/constants/others';
import { SpinnerService } from './../../../../../../../spinner/spinner.service';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-drop-varianceA',
  templateUrl: './drop-varianceA.component.html',
  styleUrls: ['./drop-varianceA.component.scss'],
})
export class DropVarianceAComponent extends SortableListComponent<Drop>
{

  @Input() slot0: boolean = false;
  description:string = '';
  public elements = ['Code Block', 'Gold', 'Ichor', 'Ruby'];
  public selectedElement;
  dropsList: Drop[] = [];
  sortTypeOrder = +1;
  areas: Area[];
  areasOrderValue : Area[] = [];
  public activeTab: string;
  activeLanguage = 'PTBR';
  variance:string = "A";
  subText: string;

  constructor(
    private spinnerService : SpinnerService,
    _activatedRoute: ActivatedRoute,
    protected _dropService: DropService,
    private _areaService: AreaService,
    _userSettingsService: UserSettingsService,
    private _router: Router,
    private _reviewService: ReviewService,
    protected _translationService: TranslationService,
    private _chestService: ChestService,
    private ref: ChangeDetectorRef,
  ) {
    super(_dropService, _activatedRoute, _userSettingsService, 'name');
  }

  public readonly excelButtonTemplate: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

/*
  protected override lstAfterFetchList()
  {
    if(this.slot0) this.elements = ['Gold', 'Ruby'];

    this.selectedElement = this.elements[0];
    this._dropService.toFinishLoading();
    this._areaService.toFinishLoading();
    this.checkElement();
    this.ngOnInit();  
  }
*/
  public override async ngOnInit() {

   // this.activeTab = 'Code Block';
   // if(this.slot0) this.elements = ['Gold', 'Ruby'];

    this.selectedElement = this.elements[0];
    this.checkElement();
    this._areaService.toFinishLoading();
    this.areas = this._areaService.models.filter(a => !!a.order).sort((a, b) => a.order - b.order);
    this.areas.forEach(area => 
    {
      if(!this.areasOrderValue.map(m => m.order).includes(area.order)) {
        this.areasOrderValue?.push(area)
      }
    })
   // this.subText = this.selectedElement;
    this.areas = this.areasOrderValue;
    this.sortListByType();
    this.ref.detectChanges();
  }

  public async checkElement() 
  {
    this.dropsList = [];
    this._dropService.toFinishLoading();
    for(let i = 0; i < this._dropService.models.length; i++)
    {
      for(let j = 0; j < this._chestService.models.length; j++)
      {
        if(this._dropService.models[i].element == this.selectedElement &&
          this._dropService.models[i].slot0 == this.slot0 &&
          this._dropService.models[i].variance == this.variance &&
          this._chestService.models[j].acronym != undefined &&
          this._chestService.models[j].acronym != '' &&
          this._chestService.models[j].acronym == this._dropService.models[i].type
          )
        {
          this.dropsList.push(this._dropService.models[i]);
          break;
        }
      }
    }

    if (this.dropsList.length == 0) 
    {
      for (let i = 0; i < this._chestService.models.length; i++) 
      {
        if(this._chestService.models[i].acronym != undefined && 
          this._chestService.models[i].acronym != '')
          {
            let cbd = new Drop(
              this._dropService.svcNextIndex(),
              this._chestService.models[i].acronym,
              this.selectedElement,
              this.slot0,
              this.variance,
              this._userSettingsService
            );
            this._dropService.srvAdd(cbd);
            this.dropsList.push(cbd);
          }
      }
    }
    else
    {
      for(let i = 0; i < this._chestService.models.length; i++)
      {
        for(let j = 0; j < this.dropsList.length; j++)
        {
          if(this._chestService.models[i].acronym != undefined &&
            this._chestService.models[i].acronym != '' &&
            this.dropsList[j].type == this._chestService.models[i].acronym) break;          
          if(j == this.dropsList.length-1 &&
            this._chestService.models[i].acronym != undefined &&
            this._chestService.models[i].acronym != '')
          {
            let cbd = new Drop(
              this._dropService.svcNextIndex(),
              this._chestService.models[i].acronym,
              this.selectedElement,
              this.slot0,
              this.variance,
              this._userSettingsService
            );
            this._dropService.srvAdd(cbd);
            this.dropsList.push(cbd);
            this._dropService.toSave();
          }        
        }
      }
    }

    this. lineupOrderTypeDrops();
    this.description = `Showing ${this.dropsList.length} results`;    
  }

  sortNameOrder = +1; 
  lineupOrderTypeDrops() 
  {
  this.sortNameOrder *=+1;
    this.dropsList.sort((a, b) => 
    {  
      return this.sortNameOrder * a.type.localeCompare(b.type);
    });

  }

  sortListByType() {
    this.sortTypeOrder *=+1;
    this.dropsList.sort((a, b) => {
      return this.sortTypeOrder *  a.type.localeCompare(b.type);
    });
  }

  public switchToTab(tab: string) {
    this.activeTab = tab;
    this.subText =  tab;     

    localStorage.setItem(
      `tab-DropVarianceAComponent${FILTER_SUFFIX_PATH}`,
      this.activeTab
    );
    
    this.selectedElement = tab;
    this.checkElement();
    this.ref.detectChanges();
  }

  changeDropAmount(drop: Drop, amount: string, index: number)
  {
    drop.amount[index] = amount === '' ? null : +amount;
    this._dropService.svcToModify(drop);
    this._dropService.toSave();
    //this.checkElement();
  }

  GetAmountValue(drop: Drop, index: number) 
  {   
    if (!drop.amount) drop.amount = [];    
    return drop.amount[index];
  }

  changeElement(element: string)
  {
    this.selectedElement = element;
    this.checkElement();
    this.ref.detectChanges();
  }

  async onExcelPaste(): Promise<void> {
    this.spinnerService.setState(true);
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter(element => element);

    const dropTypeErrors: string[] = [];
    const duplicateDropTypes: string[] = [];
    const processedDropTypes = new Set<string>();

    for (let l = 0; l < lines.length; l++) {
        let line = lines[l];
        let cols = line.split(/\t/).map(col => col.trim());

         const excelHeaders = lines[0].split(/\t/).map(header => header.trim());
         const expectedColumnsCount = this.areas.length + 1; 
      // Verifica se o número de colunas no Excel é o mesmo que o esperado
          if (excelHeaders.length !== expectedColumnsCount) {
              this.displayErrors([`The copied data has ${excelHeaders.length} columns, but ${expectedColumnsCount} were expected.`]);
              this.spinnerService.setState(false);
              return;
          }

        let drop = this._dropService.models.find(x => x.type === cols[0] && x.slot0 === this.slot0 && x.element === this.selectedElement && x.variance === this.variance);
        if (!drop) {
            dropTypeErrors.push(cols[0]);
            continue;
        }

        if (processedDropTypes.has(cols[0])) {
            duplicateDropTypes.push(cols[0]);
        } else {
            processedDropTypes.add(cols[0]);
        }

        for (let i = 1; i < cols.length; i++) {
            let amount = parseInt(cols[i]
                .split(' ').join('')
                .split('.').join('')
                .replace(',', '.').replace('%', ''));

            if (!drop.amount) drop.amount = [];
            drop.amount[i - 1] = isNaN(amount) ? undefined : amount;
        }

        await this._dropService.svcToModify(drop);
        await this._dropService.toSave();
    }

    if (dropTypeErrors.length > 0) {
        this.displayErrors([`Drop type names not found in the system: ${dropTypeErrors.join(', ')}`]);
        return;
    }
    if (duplicateDropTypes.length > 0) {
        this.displayErrors([`Duplicate drop type names found: ${duplicateDropTypes.join(', ')}`]);
        return;
    }
 
    if (dropTypeErrors.length === 0 && duplicateDropTypes.length === 0) {
        this.dropsList = this._dropService.models;
        this.lstFetchLists();
        this.ref.detectChanges();
        Alert.ShowSuccess('Drop list copied successfully!');
        this.lstInit();
        this.lineupOrderTypeDrops();
    }

    this.spinnerService.setState(false);
}

displayErrors(errors: string[]): boolean {
  if (errors.length) {
      this.spinnerService.setState(false);
      Alert.showError(`${errors.join('\n')}`);
      return true;
  }
  return false;
}


}
