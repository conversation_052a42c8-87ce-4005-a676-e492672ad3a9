import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

/**
 * Interface for dice roll modifiers (bonuses/penalties)
 */
export interface DiceModifier {
  label: string;
  value: number;
}

/**
 * Interface for dice roll results
 */
export interface DiceResult {
  roll: number;
  dc: number;
  success: boolean;
  total?: number; // roll + modifiers
}

/**
 * Interface for the complete dice overlay state
 */
export interface DiceOverlayState {
  isVisible: boolean;
  result: DiceResult | null;
  dc: number;
  modifiers: DiceModifier[];
}

/**
 * Service managing the dice roll overlay system.
 * Handles showing/hiding the dice overlay, executing rolls, and managing results.
 */
@Injectable({
  providedIn: 'root'
})
export class DiceOverlayService {
  // State management using BehaviorSubject for reactive updates
  private overlayState = new BehaviorSubject<DiceOverlayState>({
    isVisible: false,
    result: null,
    dc: 10,
    modifiers: []
  });

  public overlayState$ = this.overlayState.asObservable();

  constructor() { }

  /**
   * Show the dice roll overlay in BG3 style (user clicks dice to roll)
   * @param dc The difficulty class for the roll
   * @param modifiers Array of modifiers to apply to the roll
   */
  showDiceRoll(dc: number, modifiers: DiceModifier[] = []): void {
    console.log('DiceOverlayService: Showing dice roll overlay:', { dc, modifiers });

    // Display overlay without result initially - user must click dice to roll
    this.overlayState.next({
      isVisible: true,
      result: null,
      dc: dc,
      modifiers: modifiers
    });
  }

  /**
   * Execute the actual dice roll and calculate results
   * Called when user clicks the dice in the overlay
   */
  executeDiceRoll(): void {
    const currentState = this.overlayState.value;
    if (!currentState.isVisible) return;

    // Generate random d20 roll (1-20)
    const roll = Math.floor(Math.random() * 20) + 1;

    // Apply any modifiers to the roll
    const modifierTotal = currentState.modifiers.reduce((sum, mod) => sum + mod.value, 0);
    const total = roll + modifierTotal;

    // Determine success based on difficulty class
    const success = total >= currentState.dc;

    const result: DiceResult = {
      roll,
      dc: currentState.dc,
      success,
      total
    };

    console.log('DiceOverlayService: Dice roll executed:', result);

    // Update overlay state with the roll result
    this.overlayState.next({
      ...currentState,
      result: result
    });
  }

  /**
   * Legacy method for backward compatibility - shows dice result immediately
   * @param roll The dice roll result (1-20)
   * @param dc The difficulty class
   * @param success Whether the roll was successful
   * @param waitForClick Whether to wait for user click to dismiss
   */
  showDiceResult(roll: number, dc: number, success: boolean, waitForClick: boolean = true): void {
    console.log('DiceOverlayService: Showing dice result (legacy):', { roll, dc, success, waitForClick });

    const result: DiceResult = { roll, dc, success, total: roll };

    // Display overlay with immediate result (no user interaction needed)
    this.overlayState.next({
      isVisible: true,
      result: result,
      dc: dc,
      modifiers: []
    });

    // Auto-hide after delay if not waiting for user interaction
    if (!waitForClick) {
      setTimeout(() => {
        this.hideOverlay();
      }, 2000);
    }
  }

  /**
   * Hide the dice overlay and clean up state
   * Uses a two-phase approach to ensure dialogue components can process results
   */
  hideOverlay(): void {
    console.log('DiceOverlayService: Hiding dice overlay');

    const currentState = this.overlayState.value;

    // Phase 1: Hide overlay but keep result for dialogue processing
    this.overlayState.next({
      isVisible: false,
      result: currentState.result,
      dc: currentState.dc,
      modifiers: currentState.modifiers
    });

    // Phase 2: Clear result after dialogue has had time to process it
    setTimeout(() => {
      this.overlayState.next({
        isVisible: false,
        result: null,
        dc: currentState.dc,
        modifiers: currentState.modifiers
      });
    }, 0);
  }

  /**
   * Get current overlay state (for components that need immediate access)
   */
  getCurrentState(): DiceOverlayState {
    return this.overlayState.value;
  }

  /**
   * Check if overlay is currently visible
   */
  isVisible(): boolean {
    return this.overlayState.value.isVisible;
  }

  /**
   * Reset the overlay state completely (for starting fresh)
   */
  resetOverlay(): void {
    console.log('DiceOverlayService: Resetting dice overlay');

    this.overlayState.next({
      isVisible: false,
      result: null,
      dc: 10,
      modifiers: []
    });
  }
}
