<div class="main-content">
    <div class="container-fluid">
        <!--Header-->
        <div class="list-header-row update">
            <div class="card">
                <app-header-with-buttons [cardTitle]="'REPORTS'" [cardDescription]="description"
                    [rightButtonTemplates]="[exportExcelButtonTemplate]">
                </app-header-with-buttons>
            </div>
        </div>

        <div style="position: sticky;">
            <!--TOTAL UNIQUE CHARACTERES-->
            <div style="overflow-x: auto;">
                <div style="width: 100%;">
                    <table class="table table-list">
                        <thead>
                            <tr>
                                <th class="dark-gray" [attr.colspan]="orderAreaList.length + 1">
                                    <h5>TOTAL UNIQUE CHARACTERES</h5>
                                </th>
                            </tr>
                            <tr>
                                <th class="thBC">Caracter Rarity</th>
                                <th class="thBC" style="width: 70px;"
                                    *ngFor=" let area of orderAreaList; let i = index;">
                                    {{area.order}}
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <ng-container *ngFor="let inspira of ListTotalUnique; let e = index;">
                                <tr>
                                    <td class="other-td" style="width: 8%;"
                                        [ngStyle]="{'background-color': inspira.nameCharacterRarity | tierColor : 'Character Rarity', 'color':'#fff'}">
                                        {{inspira.nameCharacterRarity}}
                                    </td>
                                    <ng-container *ngFor="let item of inspira.listCicleLevel; let e = index;">
                                        <td class="other-td" style="width: 30px;">{{item
                                            .totalUniqueCharacteres}}
                                        </td>
                                    </ng-container>
                                </tr>
                            </ng-container>

                        </tbody>
                    </table>
                    <ng-container *ngIf="">
                        <p class="noWeapons">No Inspiration Point Rarity</p>
                    </ng-container>
                </div>
            </div>


            <!--TOTAL DE LEVEL POINTS (LP)-->
            <div style="overflow-x: auto;">
                <div style="width: 100%;">
                    <table class="table table-list">
                        <thead>
                            <tr>
                                <th class="dark-gray" [attr.colspan]="orderAreaList.length + 1">
                                    <h5>TOTAL DE LEVEL POINTS (LP)</h5>
                                </th>
                            </tr>
                            <tr>
                                <th style="width: 100px;" class="thBC"></th>
                                <th class="thBC" style="width: 70px;"
                                    *ngFor=" let area of orderAreaList; let i = index;">
                                    {{area.order}}
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <ng-container *ngFor=" let level of listLevelPoints; let i = index;">
                                <tr>
                                    <td class="other-td" style="font-weight: 800;">{{level.nameTypeLevel}}</td>
                                    <td class="other-td" style="width: 40px;"
                                        *ngFor=" let point of level.listCicleLevel; let i = index;">
                                        {{point.totalLevelPoint}}
                                    </td>
                                </tr>
                            </ng-container>

                        </tbody>
                    </table>
                    <ng-container *ngIf="">
                        <p class="noWeapons">No Inspiration Point Rarity</p>
                    </ng-container>
                </div>
            </div>


            <!--UNIQUE CHARACTERES BY HC AND BL-->
            <ng-container *ngIf="listUniqueByHCAndBL.length > 0">
                <div style="width: 100%; margin-top: 30px; overflow: auto;">
                    <table class="table table-list">
                        <thead>
                            <tr>
                                <th class="dark-gray" [attr.colspan]="multiplyArea.length + 1">
                                    <h5>UNIQUE CHARACTERES BY HC AND BL</h5>
                                </th>
                            </tr>
                            <tr>
                                <th class="thBC">HC</th>
                                <th class="thBC" style="width: 50px;" *ngFor="let area of multiplyArea; let i = index;">
                                    {{area.orderArea}}
                                </th>
                            </tr>
                            <tr>
                                <th style="border: 1px #595959 solid !important;">BL</th>
                                <th style="width: 50px; border: 1px #595959 solid !important;"
                                    *ngFor=" let bl of multiplyArea; let i = index;">
                                    {{i+0}}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <ng-container *ngFor="let hcBl of listUniqueByHCAndBL; let e = index;">
                                <tr>
                                    <td class="other-td" style="width: 8%;"
                                        [ngStyle]="{'background-color': hcBl.nameRarity| tierColor : 'Character Rarity', 'color':'#fff'}">
                                        {{hcBl.nameRarity}}
                                    </td>
                                    <ng-container *ngFor="let item of multiplyArea; let e = index;">
                                        <td class="other-td" style="width: 30px;">
                                            <ng-container *ngFor="let hc of hcBl?.hcList">
                                                <ng-container *ngIf="hc?.indexBL === e">
                                                    {{hc?.totalHCBL}}
                                                </ng-container>
                                            </ng-container>
                                        </td>
                                    </ng-container>
                                </tr>
                            </ng-container>
                        </tbody>
                    </table>
                    <ng-container *ngIf="">
                        <p class="noWeapons">No Inspiration Point Rarity</p>
                    </ng-container>
                </div>


                <!--NO COMPATIBLE-->
                <div style="width: 100%; display: flex; justify-content: center; margin-top: 10px;">
                    <table class="table table-list" style="width: 550px;">
                        <thead>
                            <tr>
                                <th class="thBC" colspan="6">Not Compatible</th>
                            </tr>
                            <tr>
                                <th class="thBC">Ordem</th>
                                <th class="thBC">Name</th>
                                <th class="thBC">HC</th>
                                <th class="thBC">BL</th>
                                <th class="thBC">Area</th>
                                <th class="thBC">Rarity</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let noExist of nonExistentItems; let i = index;">
                                <td class="other-td">{{i}}</td>
                                <td class="other-td" style="text-align: left;">{{noExist.nameCharacter}}</td>
                                <td class="other-td">{{noExist.orderArea}}</td>
                                <td class="other-td">{{noExist.bl}}</td>
                                <td class="other-td">{{noExist.nameArea}}</td>
                                <td class="other-td" [ngStyle]="{'background-color': noExist.rarity| tierColor : 'Character Rarity', 'color':'#fff'}">{{noExist.rarity}}</td>
                            </tr>

                        </tbody>
                    </table>
                </div>       
            </ng-container>
                  <app-report-archetype></app-report-archetype>       
        </div>

    </div>
</div>