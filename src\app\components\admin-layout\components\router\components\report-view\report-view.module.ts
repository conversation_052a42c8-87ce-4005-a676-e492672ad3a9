import { SpecialItemReportComponent } from './components/special-item-report/special-item-report.component';
import { ReportViewComponent } from './report-view.component';
import { WeightReportComponent } from './components/weight-report/weight-report.component';
import { MessageReportComponent } from './components/message-report/message-report.component';
import { LevelReportComponent } from './components/level-report/level-report.component';
import { ClassReportComponent } from './components/class-report/class-report.component';
import { BattleReportComponent } from './components/battle-report/battle-report.component';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BrowserModule } from '@angular/platform-browser';
import { FormsModule } from '@angular/forms';
import { ContentInputModule } from 'src/app/components/content-input/content-input.module';
import { PopupModule } from 'src/app/components/popup/popup.module';
import { MajorModule } from 'src/app/major.module';
import { BattleCharactersTableModule } from 'src/app/components/battle-characters-table/battle-characters-table.module';
import { CurrencyReportComponent } from './components/currency-report/currency-report.component';
import { GeneralItemsComponent } from './components/general-items/general-items.component';
import { LevelDialogueReportComponent } from './components/level-dialogue-report/level-dialogue-report.component';
import { LevelPinReportComponent } from './components/level-pin-report/level-pin-report.component';
import { RestartDialogueReportComponent } from './components/restart-dialogue-report/restart-dialogue-report.component';
import { ProgressConditionReportComponent } from './components/progress-condition-report/progress-condition-report.component';
import { MarkedCollectibleComponent } from './components/marked-collectible/marked-collectible.component';
import { AngularStickyThingsModule } from '@w11k/angular-sticky-things';
import { ChoiceCadenceReportComponent } from './components/choice-cadence-report/choice-cadence-report.component';
import { DisplayConditionPipe } from 'src/app/pipes/display-condition.pipe';
import { DilemmaReportComponent } from './components/dilemma-report/dilemma-report.component';
import { DifficultyClassReportsComponent } from './components/difficulty-class-reports/difficulty-class-reports.component';
import { KnowledgeDifficultClassComponent } from './components/knowledge-difficult-class/knowledge-difficult-class.component';
import { RouterModule } from '@angular/router';

@NgModule({
  imports: [
    CommonModule,
    BrowserModule,
    FormsModule,
    RouterModule,
    MajorModule,
    ContentInputModule,
    PopupModule,
    BattleCharactersTableModule,
    AngularStickyThingsModule
  ],
  providers: [DisplayConditionPipe],
  declarations: [
    ReportViewComponent,
    BattleReportComponent,
    ClassReportComponent,
    LevelReportComponent,
    MessageReportComponent,
    WeightReportComponent,
    SpecialItemReportComponent,
    CurrencyReportComponent,
    GeneralItemsComponent,
    LevelDialogueReportComponent,
    LevelPinReportComponent,
    RestartDialogueReportComponent,
    ProgressConditionReportComponent,
    MarkedCollectibleComponent,
    ChoiceCadenceReportComponent,
    DilemmaReportComponent,
    DifficultyClassReportsComponent,
    KnowledgeDifficultClassComponent
  ],
  exports: [ReportViewComponent],
})
export class ReportViewModule {}
