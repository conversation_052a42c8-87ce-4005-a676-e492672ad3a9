
<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">

        <app-header-with-buttons 
                  [cardTitle]="'Particle List'"
                  [cardDescription]="listDescription"
                  [rightButtonTemplates]="[addAreaTemplate]"
                  [isBackButtonEnabled]="false"></app-header-with-buttons>
        <app-header-search (inputKeyup)="lstOnChangeFilter($event)" (searchOptions)="lstOnChangeFilterOptions($event)"></app-header-search>
      </div>
    </div>
    <!--List-->
    <div class="card">
      <table class="table table-list">
        <thead class="sticky">
          <tr>
            <th>Index</th>
            <th class="th-clickable"
            (click)="sortListById()">ID</th>
            <th style="min-width: 150px;"  class="th-clickable"
            (click)="sortListByName()">Name & Description</th>
            <th class="th-clickable"
            (click)="sortListByWeapon()">Particle Record</th>
            <th >Actions</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let item of itemList; let i = index; trackBy: trackByIndex">
            <tr id="{{ item?.id }}" >
              <td class="td-sort">{{ i+1 }}</td>
              <td class="td-id">{{ item?.id }}</td>
              <td class="td-notes">
                <!-- <span class="form-control form-short"
                       type="text"
                       style="background-color: rgb(189, 189, 189)"
                       *ngIf="lstLanguage == 'PT-BR'">{{ item?.name }}</span> -->
                <span style="background-color:rgb(189,189,189)" class="form-control form-short"
                       type="text"
                       >{{ (item | translation : lstLanguage : item?.id  : 'name') }}</span>
                <!-- <span class="form-control"
                          type="text"
                          style="background-color: rgb(189, 189, 189)"
                          *ngIf="lstLanguage == 'PT-BR'">{{ item?.description }}
                  </span> -->
                <span style="background-color:rgb(189,189,189)" class="form-control"
                          type="text"
                          >{{ (item | translation : lstLanguage : item?.id  : 'description') }}
                  </span>
              </td>
              <td class="td-actions">
                <div class="row middle"
                style="display: flex; width: 18px; margin-bottom: 2px;">
                    <span
                      style="display: inline-block; margin: 2px;"
                      class="notification-circle small"
                      [style.background-color]="checkIfParticleIsCompleted(item, 1)"></span>
              </div>
                <button class="btn btn-primary btn-fill btn-remove"
                        (click)="selectWeapon(item, lstLanguage, item.id)">
                  <i class="pe-7s-angle-right-circle"></i>
                </button>
              </td>
              <td class="td-actions">
                <button class="btn btn-gray btn-fill translation-button"
                (click)="downloadMapsOrtography(item)"
                        >
                        <div class="mat-translate"></div>
                </button>
              </td>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
  <div *ngIf="isParticleListOpen">
  <app-particle-list (isParticleListOpen)="changeIsParticleListOpen()" [itemList]="itemList" [particleList]="particleList"></app-particle-list>
  </div>
</div>
