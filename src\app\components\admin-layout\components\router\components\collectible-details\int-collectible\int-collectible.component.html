<div style="margin-top: 20px; padding-bottom: 70px;" class="card">

    <ng-container >
        <div style="padding: 20px;">
            <table class="table table-list" style="width: 320px;">
                <thead>
                  <tr>
                    <th class="dark-gray" colspan="3">
                      <h4>INT</h4>
                    </th>
                  </tr>
                  <tr>
                    <th class="one-Column">Qi.new</th>
                    <th class="ev_green" style="width: 50%;">EV(DEFQI) (%)</th>
                    <th class="ev_orange" style="width: 50%;">PR(atkqi) (%)</th>
                  </tr>
                </thead>
                <tbody>
                  <ng-container *ngFor="let int of listIntCollectibes.valuesInt; let i = index;">
                    <tr>
                      <td>
                          {{int.qiNew}}
                      </td>
                      <td class="ev_green">
                          {{int.ev_defluk ? int.ev_defluk + '%' : ''}}
                      </td>
                      <td class="ev_orange">
                        {{int.pr_atkluk ? int.pr_atkluk + '%' : ''}}
                    </td>
                    </tr>
                  </ng-container>
                </tbody>
              </table>
              <div *ngIf="listIntCollectibes"> 
                <span><b>INT (Inteligência):</b> {{int ? int : 'Sem valor no Primal Modifier'}}</span><br>       
                <span><b>EV_BASE (Evasão):</b> {{ev_base ? ev_base : 'Sem valor no Primal Modifier'}}</span><br>                
                <span><b>PR_BASE (Precisão):</b> {{pr_base ? pr_base : 'Sem valor no Primal Modifier'}}</span><br>
                <span><b>EV_MAX:</b> {{ev_max ? ev_max : 'Sem valor no EV-MAX'}}</span>
              </div>   
        </div>    
    </ng-container>

</div>





