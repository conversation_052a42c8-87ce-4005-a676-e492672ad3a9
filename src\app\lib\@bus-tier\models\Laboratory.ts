import {
  CharacterType,
  Gender,
  IdPrefixes,
} from 'src/lib/darkcloud/dialogue-system';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { Base } from './Base';

export class Laboratory
  extends Base<Data.Hard.ILaboratory, Data.Result.ILaboratory>
  implements Required<Data.Hard.ILaboratory>
{
  private static generateId(index: number): string {
    return IdPrefixes.LABORATORY + index;
  }
  constructor(
    index: number,
    labLevel: number,
    dataAccess: Laboratory['TDataAccess']
  ) {
    super(
      {
        hard: {
          id: Laboratory.generateId(index),
          labLevel,
        },
      },
      dataAccess
    );
  }
  protected getInternalFetch() {
    return {};
  }

  public get labLevel(): number
  {
    return this.hard.labLevel;
  }
  public set labLevel(value: number)
  {
    this.hard.labLevel = value;
  }

  public get improveTitanium(): number
  {
    return this.hard.improveTitanium;
  }
  public set improveTitanium(value: number)
  {
    this.hard.improveTitanium = value;
  }

  public get improveTime(): number
  {
    return this.hard.improveTime;
  }
  public set improveTime(value: number)
  {
    this.hard.improveTime = value;
  }

  public get improveRubies(): number
  {
    return this.hard.improveRubies;
  }
  public set improveRubies(value: number)
  {
    this.hard.improveRubies = value;
  }

  public get researchSouls(): number
  {
    return this.hard.researchSouls;
  }
  public set researchSouls(value: number)
  {
    this.hard.researchSouls = value;
  }

  public get researchTime(): number
  {
    return this.hard.researchTime;
  }
  public set researchTime(value: number)
  {
    this.hard.researchTime = value;
  }

  public get researchRubies(): number
  {
    return this.hard.researchRubies;
  }
  public set researchRubies(value: number)
  {
    this.hard.researchRubies = value;
  }

}
