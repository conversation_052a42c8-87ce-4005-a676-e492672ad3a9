<div class="popup" [ngClass]="{'hide': !popupOpen}" (click)="togglePopup($event)">
  <div class="wrapper">
    <div class="card">
      <table class="table table-list table-popup" style="overflow: scroll;">
        <thead class="sticky">
          <tr>
            <th class="th-clickable">
              ID
            </th>
            <th class="th-clickable">
              Name
            </th>
            <th>Description</th>
            <th>Selected</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let c of classes">
            <tr id="{{ c.id }}">
              <td class="td-id">{{ c.id }}</td>
              <td>
                <span>{{ c.name }}</span>
              </td>
              <td>
                <span>{{ c.description }}</span>
              </td>
              <td>
                <input type="checkbox"
                  [checked]="selectedClasses.includes(c)"
                  (change)="changeValue($event, c)"/>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>
<h5 [ngClass]="{'character-group': true, 'selected-background': selectedClasses?.length === 0}" (click)="togglePopup(undefined)">
  <span class="character" *ngFor="let clas of selectedClasses">{{ clas.name }}</span>
</h5>
