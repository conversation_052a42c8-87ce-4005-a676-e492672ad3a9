.card-header {
    height: 110px; 
    position: sticky; 
    //width: 100%;
  }
  
  .content-holder {
  display: flex;   
  margin-right: 50px;
  margin-left: 50px;   
}

.card-inter {
  margin-top: 70px;
  display: grid;
  gap: 15px;
  width: 800px;
  padding-top: 20px;
  padding-bottom: 20px;
  padding-left: 20px;
  margin-left: 25px;
  border: 1px solid #ccc;
  border-radius: 5px;
  box-shadow: 0 1px 3px #0000004d;
  background-color: white;
}

///MODAL
/* Fundo escurecido (backdrop) */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5); /* Ajuste a opacidade aqui */
  z-index: 998; /* Deve ser menor que o modal */
}
.popup-report
{
position: fixed;
top: 50%;
left: 50%;
transform: translate(-50%, -50%);
height: 70%;
background-color: white;
z-index: 999; /* <PERSON><PERSON> que o backdrop */
}

.card-modal {
  width: 100%; 
 /* display: flex; 
  justify-content: center;
  */
}

.titleModal {
  display: flex; 
  justify-content: center;
}

h4 {
  margin: 0;
  padding: 0;
  font-weight: 700;
}

.closeModal {
  width: 50px; 
  display: flex; 
  justify-self: end; 
  position: absolute;
}

.swal-icon-close {
  position: relative;
  z-index: 2;
  top: -20px;
  right: 0;
  align-items: center;
  justify-content: center;
  width: 1.2em;
  height: 1.2em;
  padding: 0;
  overflow: hidden;
  transition: color .1s ease-out;
  border: none;
  border-radius: 0;
  background: 0 0;
  color: #ccc;
  font-family: serif;
  font-size: 2.5em;
  line-height: 1.2;
  cursor: pointer;
  float: inline-end;
  float: right;;

}
.swal-icon-close:hover {
 color: red;;
}

.invalid-field {
  border: 1px solid red !important;
}

.th-general {
  background-color: gray !important;
}

.div-name {
  width: 100%; 
  display: flex; 
  align-items: center;
}

.span-name {
  margin-right: 10px; 
  width: 70px;
}

input {
  padding-left: 5px;
}

.input-nameKey {
  width: 680px; 
  border-radius: 5px; 
  height: 30px;
}

.div-key {
  display: flex; 
  align-items: center;
}

.span-key {
  margin-right: 10px; 
  width: 70px;
}

.db-itens {
  display: flex;
  align-content: center;
  flex-direction: column;
  flex-wrap: wrap;
  padding: 10px;
}

select {
  height: 30px;
  border-radius: 5px;
  width: 300px;
}

.div-between {
  display: flex; 
  justify-content: space-between;
}

.div-model {
  display: flex; 
  align-items: center;
}

.span-model {
  margin-right: 10px;
  width: 70px;
}

.p-size{
  font-size: 13px !important;
}

.error401 {
  width: 700px;
  color: red;
  text-align: center;
  margin-bottom: 20px;
  margin-top: 10px;
}

.input-temperature {
  width: 200px; 
  border-radius: 5px; 
  height: 30px; 
  margin-right: 20px;
}

.input-limit {
  width: 275px; 
  border-radius: 5px; 
  height: 30px;
}

.div-created {
  display: flex; 
  justify-content: center; 
  margin-top: 10px;
}

.btn-ConfirmForm {
  width: 100%;
  background-color: #3472f7; 
  color: white; 
  margin-right: 10px; 
}

.btn-ConfirmForm:hover {
  color: black;
}

.table-bordered {
  width: 100%;
  margin-bottom: 20px;
}

.table-bordered th, .table-bordered td {
  padding: 8px;
  text-align: center;
}

.table-bordered thead th {
  background-color: #2E2E2E;
  color: white;
}

.table-bordered thead th[colspan="3"] {
  background-color: #2E2E2E;
  color: white;
}

.table-bordered thead th[colspan="4"] {
  background-color: #555555;
  color: white;
}


.gray-color-th, th {
  background-color: #595959 !important;
}

.trBC {
 // background-color: #595959 !important;
  text-align: center;
  font-weight: 400 !important;
}

.gray{
background-color:#ddd;
}
