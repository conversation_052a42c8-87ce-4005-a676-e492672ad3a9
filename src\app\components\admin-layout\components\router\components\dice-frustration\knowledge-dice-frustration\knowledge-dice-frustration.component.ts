import { ChangeDetectorRef, Component } from '@angular/core';
import { KnowledgeDiceFrustration } from 'src/app/lib/@bus-tier/models';
import { Button } from 'src/app/lib/@pres-tier/data';
import { KnowledgeDiceFrustrationService } from 'src/app/services';
import { TranslationService } from 'src/app/services/translation.service';
import { Alert } from 'src/lib/darkcloud';


@Component({
  selector: 'app-knowledge-dice-frustration',
  templateUrl: './knowledge-dice-frustration.component.html',
  styleUrls: ['./knowledge-dice-frustration.component.scss']
})
export class KnowledgeDiceFrustrationComponent {

  titles = ['LIGHT', 'MODERATE', 'CRITICAL'];
  description: string;
  activeLanguage = 'PTBR';
  listFrustrationLevels: KnowledgeDiceFrustration[] = [];

  public readonly excelButtonTemplate: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  constructor(
    public ref: ChangeDetectorRef,
    public _knowledgeDiceFrustrationService: KnowledgeDiceFrustrationService,
    protected _translationService: TranslationService,

  ) { }


  async ngOnInit(): Promise<void> {

    this._knowledgeDiceFrustrationService.toFinishLoading();
    setTimeout(() => {
      this.listFrustrationLevels = this._knowledgeDiceFrustrationService.models;
      this.description = `Showing ${this.listFrustrationLevels.length} results`;
    }, 60);

  }

  async onExcelPaste() {
    try {
      const text = await navigator.clipboard.readText();
      const lines = text.split(/\r?\n/).filter((line) => line);

      this._knowledgeDiceFrustrationService.models = [];
      this._knowledgeDiceFrustrationService.toSave();

      for (const line of lines) {
        const values = line.split('\t');
        const nameSubContext = values[0];

        if (values.length !== this.titles.length) {
          Alert.showError(
            `Error: Number of columns in the imported data does not match the expected (${this.titles.length}).`
          );
          return;
        }

        if (nameSubContext) {
          this.listFrustrationLevels = [];
          this.listFrustrationLevels.push(
            await this._knowledgeDiceFrustrationService.createNewDiceFrustrationLevels()
          );
        }

        for (let index = 0; index < this.listFrustrationLevels.length; index++) {
          if (nameSubContext || nameSubContext !== "") {
            this.listFrustrationLevels[index].subContext = nameSubContext;
            this.listFrustrationLevels[index].light.push(values[1]);
            this.listFrustrationLevels[index].moderate.push(values[2]);
            this.listFrustrationLevels[index].critical.push(values[3]);
          }
          else {
            this.listFrustrationLevels[index].light.push(values[1]);
            this.listFrustrationLevels[index].moderate.push(values[2]);
            this.listFrustrationLevels[index].critical.push(values[3]);
          }

          this._knowledgeDiceFrustrationService.svcToModify(this.listFrustrationLevels[index]);
          this._knowledgeDiceFrustrationService.toSave();
        }
      }

      this.ref.detectChanges();
      Alert.ShowSuccess("Knowledge Dice Frustration list copied successfully!");
      this.ngOnInit();
    }
    catch (error) {
      Alert.showError("Error importing data from Excel.");
      console.error(error);
    }
  }

  changeFrustrationValue(rowIndex: number, rowSub: number, name: string, newValue: string) {
    if (name === 'light') {
      this.listFrustrationLevels[rowIndex].light[rowSub] = newValue == '' ? null : newValue;
      this.listFrustrationLevels[rowIndex].isReviewedLight[rowSub] = false;
      this.listFrustrationLevels[rowIndex].revisionCounterLightAI[rowSub] = 0;
    } else if (name === 'moderate') {
      this.listFrustrationLevels[rowIndex].moderate[rowSub] = newValue == '' ? null : newValue;
      this.listFrustrationLevels[rowIndex].isReviewedModerate[rowSub] = false;
      this.listFrustrationLevels[rowIndex].revisionCounterModerateAI[rowSub] = 0;
    } else if (name === 'critical') {
      this.listFrustrationLevels[rowIndex].critical[rowSub] = newValue == '' ? null : newValue;
      this.listFrustrationLevels[rowIndex].isReviewedCritical[rowSub] = false;
      this.listFrustrationLevels[rowIndex].revisionCounterCriticalAI[rowSub] = 0;
    }
    this._knowledgeDiceFrustrationService.svcToModify(this.listFrustrationLevels[rowIndex]);
  }


  downloadKnowledgeDiceOrtography(knowledge: KnowledgeDiceFrustration, index: number) {
    this._translationService.getKnowledgeDiceFrustrationOrtography(knowledge, true, index);
  }

}
