import { Component, OnInit } from '@angular/core';
import { AreaService } from 'src/app/services/area.service';
import { LevelService } from 'src/app/services/level.service';
import { MarkerService } from 'src/app/services/marker.service';
import { Dialogue, Level, Marker } from 'src/app/lib/@bus-tier/models';
import { byAreaAndLevelByExtraction } from 'src/app/lib/@bus-tier/sorting';
import { Sorting } from 'src/lib/darkcloud/angular/easy-mvc/Sorting';
import { EventService } from 'src/app/services';
import { Router } from '@angular/router';

@Component({
  selector: 'app-special-item-report',
  templateUrl: './special-item-report.component.html',
  styleUrls: ['./special-item-report.component.scss'],
})
export class SpecialItemReportComponent implements OnInit 
{
  currentLevel:Level = undefined;
  protected sortingParameter: Sorting.Parameter;
  protected sortingOrder: Sorting.Order = 'ascending';
  levels: Level[] = [];
  specialItemRemovalMarkersByLevels = [];
  levelEventIds = []
  specialItemRemovalMarkers = [];

  constructor(
    private _router: Router,
    private markerService: MarkerService,
    private _eventService: EventService,
    private _levelService: LevelService,
    private _areaService: AreaService
  ) {}

  
  ngOnInit(): void 
  {
    this.levelEventIds = [];
    this.levels = [];
    //Get all events of type special Item Remove on Grind.
    for(let i = 0; i < this._eventService.models.length; i++)
    {
      if(+this._eventService.models[i]?.type == 12)
      {
        this.specialItemRemovalMarkersByLevels.push(this._eventService.models[i]);
      }
      if(+this._eventService.models[i]?.type == 8)	
      {	
          this.specialItemRemovalMarkers.push(this._eventService.models[i]);	
      }
    }

    for(let i = 0; i < this.specialItemRemovalMarkersByLevels.length; i++)
    {
      let level = this.specialItemRemovalMarkersByLevels[i].id.split('.')[0]+'.'+this.specialItemRemovalMarkersByLevels[i].id.split('.')[1];
      for(let j = 0; j < this._levelService.models.length; j++)
      {
        //Add the levels that have event
        if(this._levelService.models[j].id == level && !this.levels.includes(this._levelService.models[j]))
        {
          this.levels.push(this._levelService.models[j]);
        }
      }
    }
 
    //Fill the scissors array
    for(let j = 0; j < this._levelService.models.length; j++)
    {
      if(this._levelService.models[j].removeSpecialItemBeforeGrind && !this.levels.includes(this._levelService.models[j]))
      {
        this.levelEventIds.push(this._levelService.models[j].id);//Use to show the scissors
        this.levels.push(this._levelService.models[j]);
      }
    }

    for(let i = 0; i < this.specialItemRemovalMarkers.length; i++)	
    {	
      let level = this.specialItemRemovalMarkers[i].id.split('.')[0]+'.'+this.specialItemRemovalMarkers[i].id.split('.')[1];	
      for(let j = 0; j < this._levelService.models.length; j++)	
      {	
        //Add the levels that have event	
        if(this._levelService.models[j].id == level && !this.levels.includes(this._levelService.models[j]))	
        {	
          this.levels.push(this._levelService.models[j]);	
        }	
      }	
    }
  }


  accessLevel(level: Level) 
  {
    this._router.navigate(['levels'], { fragment: level.id });
  }

  public sortsByParameter(param: Sorting.Parameter) 
  {
    this.sortingOrder = Sorting.nextOrder(this.sortingOrder, this.sortingParameter, param);
    this.sortingParameter = param;
    this.sortsLevels();
  }

  accessDialogue(marker: Marker) 
  {
    this._router.navigate(
      [
        'levels/' +
          Level.getSubIdFrom(marker.id) +
          '/dialogues/' +
          Dialogue.getSubIdFrom(marker.id, 'PT-BR'),
      ],
      { fragment: marker.id }
    );
  }

  accessDialogues(level: Level) 
  {
    this._router.navigate(['levels/' + level.id + '/dialogues']);
  }

  sortsLevels() 
  {
    switch (this.sortingParameter) 
    {
      case 'hierarchyCode':
        this.levels.sort((a, b) =>
          this.sortingOrder === 'descending' ? byAreaAndLevelByExtraction(a, b, this._areaService, this._levelService)
            : byAreaAndLevelByExtraction(b, a, this._areaService, this._levelService));
        break;
      case 'removeSpecialItem':
        this.levels.sort((a, b) => this.sortingOrder === 'ascending' ? 
          a.removeSpecialItemBeforeGrind && !b.removeSpecialItemBeforeGrind ? 1 : -1
            : !a.removeSpecialItemBeforeGrind && b.removeSpecialItemBeforeGrind ? 1 : -1);
        break;
      case 'specialItemOnGrindMarker':	
        this.levels.sort((a, b) => this.sortingOrder === 'ascending' ?	
          this.specialItemRemovalMarkersByLevels[a.id]?.length > this.specialItemRemovalMarkersByLevels[b.id]?.length ? 1 : -1	
            : this.specialItemRemovalMarkersByLevels[a.id]?.length < this.specialItemRemovalMarkersByLevels[b.id]?.length ? 1 : -1);	
        break;
      case 'specialItemMarker':
        this.levels.sort((a, b) => this.sortingOrder === 'ascending' ? 
        this.specialItemRemovalMarkers[a.id]?.length > this.specialItemRemovalMarkers[b.id]?.length ? 1 : -1	
        : this.specialItemRemovalMarkers[a.id]?.length < this.specialItemRemovalMarkers[b.id]?.length ? 1 : -1);
            
        break;
    }
  }
}
