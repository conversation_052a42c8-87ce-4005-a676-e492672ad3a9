import { Component, Input } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { BattleUpgrade } from 'src/app/lib/@bus-tier/models/BattleUpgrade';
import { Button } from 'src/app/lib/@pres-tier/data';
import { AreaService, CharacterService, ClassService } from 'src/app/services';
import { BattleUpgradeService } from 'src/app/services/battle-upgrade.service';
import { CustomService } from 'src/app/services/custom.service';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Alert } from 'src/lib/darkcloud';
import { ICollectibleDetails } from 'src/lib/darkcloud/angular/dsadmin/v9/data/result';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/SortableListComponent';
import { GameTypes } from 'src/lib/darkcloud/dialogue-system';

@Component({
  selector: 'app-battle-upgrade',
  templateUrl: './battle-upgrade.component.html',
  styleUrls: ['./battle-upgrade.component.scss'],
})
export class BattleUpgradeComponent extends SortableListComponent<BattleUpgrade> implements ICollectibleDetails {
  @Input() character: string = '';
  currentCharacter: BattleUpgrade;
  hierachyCode: string = '';
  valueBossLevel: string;
  nameClass: string;
  nameRarity: string;
  type: string;

  constructor(
    protected _customService: CustomService,
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    protected _battleUpgradeService: BattleUpgradeService,
    private _characterService: CharacterService,
    private _areaService: AreaService,
    private _classService: ClassService,

  ) {
    super(_battleUpgradeService, _activatedRoute, _userSettingsService, 'name');
  }

  public readonly excelButtonTemplate: Button.Templateable =
    {
      title: 'Paste content from excel',
      onClick: this.onExcelPaste.bind(this),
      iconClass: 'excel-icon',
      btnClass: Button.Klasses.FILL_ORANGE,
    };


  override async ngOnInit(): Promise<void> {
    await this._battleUpgradeService.toFinishLoading();
    this.currentCharacter = this._battleUpgradeService.models.find(modifier => modifier.character == this.character);

    if (this.currentCharacter?.character != this.character || !this.currentCharacter) {
      this.currentCharacter = await this._battleUpgradeService.createNewBattleUpgrade(this.character);
      this.currentCharacter.apMin = [];
      this.currentCharacter.baseLevel = [];
      this.currentCharacter.hp = [];
      this.currentCharacter.atk = [];
      this.currentCharacter.def = [];
      this.currentCharacter.atkLim = [];
      for (let i = 0; i <= 10; i++) {
        this.currentCharacter.baseLevel[i] = i;
      }
    }

    this.checkBosslevel(this.currentCharacter);
    await this._battleUpgradeService.svcToModify(this.currentCharacter);
    await this._battleUpgradeService.toSave();
    this.valueBossLevel = `BL: ${this.currentCharacter.bl}`;

    const character = this._characterService.models.find(x => x.id === this.currentCharacter.character);
    this.nameRarity = character.rarity;
    this.nameClass = this._classService.models.find(x => x.id === character.classId)?.name;
    this.type = GameTypes.characterTypeName[character.type];

    this.takeCharacterHierachyCode();
    this.addNewField('apMin');

  }

  checkBosslevel(currentCharacter: BattleUpgrade) {
    for (let i = 0; i < currentCharacter?.hp.length; i++) {
      if (currentCharacter?.hp[i] !== null) {
        this.currentCharacter.bl = i;
        return this.currentCharacter;
      }
    }
    return this.currentCharacter.bl = 0;
  }

  //This is to add a new field when it is inserted after creation
  async addNewField(fieldToBeAdded: string) {
    if (this.currentCharacter[fieldToBeAdded] == undefined) this.currentCharacter[fieldToBeAdded] = [];
    await this._battleUpgradeService.svcToModify(this.currentCharacter);
    await this._battleUpgradeService.toSave();
  }

  reset(character) {
    this.character = character;
    this.ngOnInit();
  }

  async onExcelPaste() {
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter(line => line); // Divide as linhas e filtra as linhas vazias

    if (this.DisplayErrors(lines)) return;

    this.currentCharacter.apMin = [];
    this.currentCharacter.baseLevel = [];
    this.currentCharacter.hp = [];
    this.currentCharacter.atk = [];
    this.currentCharacter.def = [];
    this.currentCharacter.atkLim = [];
    this._battleUpgradeService.svcToModify(this.currentCharacter);

    for (let l = 0; l < lines.length; l++) {
      let line = lines[l];
      let cols = line.split(/\t/);
      // Pega o índice da segunda coluna (BL)
      let index = +cols[1]
        .split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',', '.');

      // Encontra o battle upgrade correspondente ao personagem
      let battle = this._battleUpgradeService.models.find((b) => b.character === this.character);
      let battleFields: string[] = ['apMin', '', 'hp', 'atk', 'def', 'atkLim'];

      // Garante que a baseLevel tenha a quantidade correta de níveis
      if (l >= battle.baseLevel.length) {
        battle.baseLevel.push(l);
      }

      // Processa cada campo, pulando a segunda coluna
      for (let i = 0; i < battleFields.length; i++) {
        // Ignora a segunda coluna (BL)
        if (battleFields[i] !== '') {
          // Verifica se a coluna não está vazia
          if (cols[i]?.trim()) {
            // Converte o valor da célula para número, removendo formatações indesejadas
            battle[battleFields[i]][index] = +cols[i]
              .split(' ')
              .join('')
              .split('.')
              .join('')
              .replace(',', '.');
          } else {
            // Se o valor da célula estiver vazio, define como undefined
            battle[battleFields[i]][index] = undefined;
          }
        }
      }
      // Atualiza o serviço com o objeto modificado
      this._battleUpgradeService.svcToModify(battle);
    }

    this._battleUpgradeService.toSave();
    Alert.ShowSuccess('Battle Upgrade List imported successfully!');
    this.ngOnInit();
  }


  DisplayErrors(array) {
    let count = array[0].split(/\t/)
    if (count.length < 5) {
      Alert.showError("Copy the BL column values too!")
      return true
    }

    if (count[0] === "") {
      Alert.showError("You are probably copying a blank column!")
      return true
    }

    return false
  }

  takeCharacterHierachyCode() {
    let areaId = this._characterService.svcFindById(this.character).areaId
    this.hierachyCode = this._areaService.svcFindById(areaId)?.hierarchyCode
  }

  async changeBattleupgradeValue(battle: BattleUpgrade, value: string, index: number, field: string) {
    battle[field][index] = value == '' ? undefined : +value;
    /*if(field === 'hp' && value != ''){
      battle.hp = parseInt(value.toString());
    }
    */
    await this._battleUpgradeService.svcToModify(battle);
    await this._battleUpgradeService.toSave();
    this.ngOnInit();
  }
}
