import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Speech } from 'src/app/lib/@bus-tier/models';
import { CharacterService, DialogueService, OptionBoxService, ReviewService, SpeechService, StoryBoxService, UserSettingsService } from 'src/app/services';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc';
import { OptionBoxType } from 'src/lib/darkcloud/dialogue-system';
import { comparable } from 'src/lib/others';

@Component({
  selector: 'app-choice-cadence-report',
  templateUrl: './choice-cadence-report.component.html',
  styleUrls: ['./choice-cadence-report.component.scss'],
})
export class ChoiceCadenceReportComponent extends SortableListComponent<Speech> implements OnInit{

  constructor(
    private _choiceBoxService: OptionBoxService,
    private _dialogueService: DialogueService,
    private _speechService: SpeechService,
    private _storyBoxService: StoryBoxService,
    private _reviewService: ReviewService,
    private _router: Router,
    private _characterService: CharacterService,
    private _optionboxService: OptionBoxService,
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
  ) {
    super(_speechService, _activatedRoute, _userSettingsService, 'name');
  }

  public speechData: Speech[] = [];

  regSpeech: Speech[] = [];
  questionSpeech: Speech[] = [];
  otherSpeech: Speech[] = [];

  public static dataCache: Speech[] = [];

  override async ngOnInit() {
    this._dialogueService.models.forEach((y) => {
      y.boxIds.forEach((x, i) => {
        if (x.includes('OB')) {
          if (
            this._optionboxService.svcFindById(x).type == OptionBoxType.CHOICE
          ) {
            let storyBox = this._storyBoxService.svcFindById(y.boxIds[i - 1]);
            if (storyBox) {
              let speechId =
                storyBox.storyProgressIds[storyBox.storyProgressIds.length - 1];
              let speech = this._speechService.svcFindById(speechId);
              if (!speech) return;

              if (speech.message?.slice(speech.message.length - 1) == '.')
                this.regSpeech.push(speech);
              else if (speech.message?.slice(speech.message.length - 1) == '?')
                this.questionSpeech.push(speech);
              else this.otherSpeech.push(speech);
            }
          }
        }
      });
    });

  }

  access(id: string) {
    this._router.navigate(
      [
        'levels/' +
          this._reviewService.reviewResults[id].levelId +
          '/dialogues/' +
          this._reviewService.reviewResults[id].dialogueId,
      ],
      { fragment: id }
    );
  }

  private sortByName: boolean = true;

  sortListByCharacterName(data: Speech[]) {
    data.sort((a, b) => {
      let characterNameA = comparable(
        this._characterService.svcFindById(a.speakerId)?.name
      );
      let characterNameB = comparable(
        this._characterService.svcFindById(b.speakerId)?.name
      );

      if (!characterNameA || !characterNameB) return 0;

      if (characterNameA > characterNameB) return this.sortByName ? 1 : -1;
      else if (characterNameB > characterNameA) return this.sortByName ? -1 : 1;

      return 0;
    });

    this.sortByName = !this.sortByName;
  }

  private isSortedByMessage: boolean = false;

  sortListByMessage(data: Speech[]) {
    data.sort((a, b) => {
      let messageA = comparable(a.message);
      let messageB = comparable(b.message);

      if (!messageA || !messageB) return 0;

      if (messageA > messageB) return this.isSortedByMessage ? 1 : -1;
      else if (messageB > messageA) return this.isSortedByMessage ? -1 : 1;

      return 0;
    });

    this.isSortedByMessage = !this.isSortedByMessage;
  }
}
