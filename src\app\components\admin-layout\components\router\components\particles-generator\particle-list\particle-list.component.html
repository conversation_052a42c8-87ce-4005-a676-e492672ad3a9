<div @popup class="popup-report" style="overflow:hidden;overflow-y: scroll;height: 70vh;">
    <button (click)="closeListStatsPopup()"><i class="pe-7s-close"></i></button>
    <table class="popup-area" >
      <thead>
        <tr>         
          <th style="text-align:center" class="th-clickable" (click)="sortByName()" >Partícula</th>
          <th  style="text-align:center" class="th-clickable" (click)="sortHCLevel()" >HC Level</th>
          <th style="text-align:center"  class="th-clickable" (click)="sortATK()" >ATK</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let item of particlesList"> 
          <td>{{item.name}}</td>
          <td>{{item.hcLevel}}</td>
          <td>{{item.atk}}%</td>
        </tr>          
      </tbody>
    </table>
  </div>