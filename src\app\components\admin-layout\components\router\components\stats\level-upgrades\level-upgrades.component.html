<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="card list-header-row">
      <app-header-with-buttons 
        [cardTitle]="'Main Character Upgrade'"
        [cardDescription]="cardDescription"
        [rightButtonTemplates]="[excelButtonTemplate]"
        [isBackButtonEnabled]="false">
      </app-header-with-buttons>
      <app-header-search (inputKeyup)="lstOnChangeFilter($event)"></app-header-search>
    </div>
    <!--List-->
    <div class="card" >
      <table class="table table-list" style="align-content: center !important;">
        <thead style="top: 115px">
          <tr >
            <th style="background-color:transparent !important; border: none !important;" colspan="3"></th>
            <th class="gray-color" style="font-size:16px">Cost</th>
            <th class="gray-color" style="font-size:16px">Meditation Time</th>
            <th class="gray-color" style="font-size:16px">Skip Price</th>
          </tr>
          <tr>
            <th rowspan="2" class="th-clickable gray-color"
            (click)="sortListByParameter('areaId')" style="font-size:16px">Circle of Hell (HC)</th>
            <th rowspan="2" class="th-clickable gray-color"
            (click)="sortListByParameter('playerLevel')" style="font-size:16px">
              Player Level (PLL)</th>
            <th rowspan="2" class="th-clickable gray-color"
            (click)="sortListByParameter('astralPlan')" style="font-size:16px">Astral Plan (AP)</th>

            <th class="pink-color">Accumulated XP</th>
            <th class="blue-color">Minutes</th>
            <th class="red-color">Rubies</th>
          </tr>
          <tr>
          <th class="th-clickable light-gray-color" 
            (click)="sortListByParameter('totalXP')">Required to this level</th>
            <th class="th-clickable light-gray-color"
            (click)="sortListByParameter('timeToUpgrade')">Time to upgrade to this level</th>
            <th class="th-clickable light-gray-color"
            (click)="sortListByParameter('rubies')">Gem to skip the wait (build instantly)</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let levelupgrade of this.levelUpgradeList; let i = index; trackBy: trackById">
            <tr id="{{ levelupgrade.id }}">
              <td class="td-id"><input 
                readonly
                class="form-control form-short " 
                type="text"
                value="{{+levelupgrade.hc}}"/>
              </td>
              <td class="td-id">
                <input 
                  placeholder=" "  
                  class="form-control form-short background-input-table-color" 
                  type="number"
                  value="{{+levelupgrade.playerLevel <= 40 ? +levelupgrade.playerLevel : ''}}"
                  #playerLevel
                  (change)="changeValue(levelupgrade, playerLevel.value, 'playerLevel')"  /></td>
              <td class="td-id">
                <input 
                  placeholder=" "  
                  class="form-control form-short background-input-table-color" 
                  type="number"
                  value="{{ +levelupgrade.astralPlan }}"
                  #astralPlan
                  (change)="changeValue(levelupgrade, astralPlan.value, 'astralPlan')" /></td>
              <td class="td-id">
                <input 
                  placeholder=" " 
                  class="form-control form-short background-input-table-color" 
                  type="number"
                  value="{{ +levelupgrade.totalXP }}"
                  #totalXP
                  (change)="changeValue(levelupgrade, totalXP.value, 'totalXP')" /></td>
              <td class="td-id">
                <input 
                  placeholder=" "  
                  class="form-control form-short background-input-table-color" 
                  type="number"
                  value="{{ +levelupgrade.timeToUpgrade }}"
                  #timeToUpgrade
                  (change)="changeValue(levelupgrade, timeToUpgrade.value, 'timeToUpgrade')" /></td>
              <td class="td-id">
                <input 
                  placeholder=" "  
                  class="form-control form-short background-input-table-color" 
                  type="number"
                  value="{{ +levelupgrade.rubies }}"
                  #rubies
                  (change)="changeValue(levelupgrade, rubies.value, 'rubies')" />
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>
