<!--Modal Info Special Skills-->
<ng-container *ngIf="isModalVisible">
    <div class="background-div handleOut" aria-hidden="true">
        <div class="modal-backdrop" *ngIf="isModalVisible"></div>
        <div id="modal-close" class="popup-report" (mouseleave)="onMouseLeave()"
            style="background-color: black;">
            
        <div style="overflow-y: auto; overflow-x: hidden; max-height: 700px;">
              <div class="modal-header">
                <div style="display: flex; justify-content: space-between;">
                    <p style="color:azure !important; text-align: center;" class="modal-title">GENERAL RULES</p>
                    <button type="button" class="close handleOut" (click)="closeModalSpecialSkills()"
                        data-dismiss="background-div" aria-label="Fechar">
                        <span class="xClose"  aria-hidden="true">&times;</span>
                    </button>
                </div>
            </div>
            <div class="contextInfo">
                <p class="modal-title title p-text">
                    1. Each category can be entered a maximun of two (2) times.
                </p>
                <p class="modal-title title p-text">
                    2. The <span class="span-text">DEFENSIVE</span> category is limited to <span class="span-text">WEAK SKILL</span> or <span class="span-text">Negative Affinity Skills</span>
                </p>
                <p class="modal-title title p-text">
                    3. Up to five (5) <span class="span-text">Special Skill</span> may be added.
                </p>
                <p class="modal-title title p-text">
                    4. <span class="span-text">Physical Special Skills</span> operate independently of a character's <span class="span-text">Elemental Affinities.</span>
                </p>
            </div>
            <br>
            <!--Hybrid Rules-->
            <div class="modal-header">
                <div style="display: flex; justify-content: space-between; margin-top: 15px;">
                    <p style="color:azure !important; text-align: center;" class="modal-title">Hybrid Rules</p>
                </div>
            </div>
            <div class="contextInfo">
                <p class="modal-title title p-text">
                    These rules do <span class="span-text">not</span> depend on the following tables:
                </p>
                <ul>
                    <li>Repetitions</li>
                    <li>ID Blocks</li>
                </ul>
                <p class="modal-title title p-text">
                    These rules <span class="span-text">do</span> depend on:
                </p>
                <ul>
                    <li>Elemental Affinities</li>
                    <li>Status Effects Tables</li>
                </ul>
                <p class="modal-title title p-text">
                    As a result, the selection pool is entirely open and determined by individual units associated with available 
                    <span class="span-text">SKILLs.</span>
                </p>
                 <ul>
                    <li>If an <span class="span-text">ID BOOST</span> or a <span class="span-text">NEGATIVE</span> has already been selected, the <span class="span-text">HYBRID</span> option becomes blocked. </li>
                    <li> Conversely, if the <span class="span-text">ID</span> is part of a HYBRID configuration, the system blocks the option to choose an <span class="span-text">ID BOOST</span> or <span class="span-text">NEGATIVE.</span>  </li>              
                </ul>       
            </div>
  <br>
            <!--Ailment Rules-->
            <div class="modal-header">
                <div style="display: flex; justify-content: space-between; margin-top: 15px;">
                    <p style="color:azure !important; text-align: center;" class="modal-title">Ailment Rules</p>
                </div>
            </div>
            <div class="contextInfo">
                <p class="modal-title title p-text">
                    These rules do <span class="span-text">not</span> depend on the following tables:
                </p>
                <ul>
                    <li>Elemental Affinities</li>           
                </ul>
                <p class="modal-title title p-text">
                    These rules <span class="span-text">do</span> depend on:
                </p>
                <ul>
                    <li>Repetition</li>
                    <li>ID Blocks</li>
                </ul>
                <p class="modal-title title p-text">
                    The selection pool remains completely open, provided repetitions are still available.             
                </p>       
            </div>
  <br>
         <!--CHAOS Rules-->
            <div class="modal-header">
                <div style="display: flex; justify-content: space-between; margin-top: 15px;">
                    <p style="color:azure !important; text-align: center;" class="modal-title">CHAOS Rules</p>
                </div>
            </div>
            <div class="contextInfo">
                <p class="modal-title title p-text">
                    These rules do <span class="span-text">not</span> depend on the following tables:
                </p>
                <ul>
                    <li>Elemental Affinities</li>           
                </ul>
                <p class="modal-title title p-text">
                    These rules <span class="span-text">do</span> depend on:
                </p>
                <ul>
                    <li>Repetition</li>
                    <li>ID Blocks</li>
                </ul>
                <p class="modal-title title p-text">
                    The selection pool remains completely open, provided repetitions are still available.             
                </p>       
            </div>
  <br>
            <!--COMBO Rules-->
            <div class="modal-header">
                <div style="display: flex; justify-content: space-between; margin-top: 15px;">
                    <p style="color:azure !important; text-align: center;" class="modal-title">Combo</p>
                </div>
            </div>
            <div class="contextInfo">
                <p class="modal-title title p-text">
                    These rules do <span class="span-text">not</span> depend on the following tables:
                </p>
                <ul>
                    <li>Repetition</li>
                    <li>ID Blocks</li>    
                </ul>
                <p class="modal-title title p-text">
                    These rules <span class="span-text">do</span> depend on:
                </p>
                <ul>
                    <li>Elemental Affinities</li>
                    <li>Status Effects Tables</li>   
                </ul>
                <p class="modal-title title p-text">
                   Thus, the selection pool remains entirely open and determined by individual <span class="span-text">SKILL COMBO.</span>            
                </p>       
            </div>
            
            </div>      

            
        </div>
    </div>
</ng-container>
<!--Fim do Modal-->
