.mission-event-table
{
    background-color: #ed7d31 !important;
    width: auto !important;
    th,
    td,
    label {
      color: white !important;
    }
}

.color-grey-light {
  color: #d3d3d3;
  opacity: 90% !important;
}

.item-event-table
{
    background-color: #ffc000 !important;
    width: auto !important;
    th,
    td,
    label {
      color: white !important;
    }
}

.cinematic-event-table
{
    background-color: #7b7b7b !important;
    width: auto !important;
    th,
    td,
    label {
      color: white !important;
    }
}

.loop-event-table
{
    background-color: #2c2c2c !important;
    width: auto !important;
    th,
    td,
    label {
      color: white !important;
    }
}

.percentage-class{
  text-align: center;
  border-style: solid; 
  border-color:rgb(189, 189, 189);
   border-width:1px; 
   max-width: 30px;
}

.percentage-class:hover{
  text-align: center;
  border-style: solid; 
  border-color:rgb(255, 205, 205);
  color:rgb(255, 205, 205);
   border-width:1px; 
   max-width: 30px;
  cursor: pointer;
}

.isPercentage-class{
  color:green;
  font-weight: bold;
  background-color: #d7ffd7;
  width:80px;
}

.isNOTPercentage-class{
  color:rgb(66, 66, 66);
  cursor: pointer;
  width:80px;

}

.divider {
  background: #c9c9c9; 
  font-size: 0.1px;
}

.itemClass {
  background-color: rgb(46, 241, 127) !important;
  margin-bottom: 6px;
  opacity: 1;  
}

.borderRadius{
  border-radius: 50%;
  color: green; 
  border: 3px solid green;
  padding: 3px 3px; 
  text-align: right;  
}
