<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons 
          [cardTitle]="listName"
          [cardDescription]="''"
          [rightButtonTemplates]="[excelButtonTemplate]">
        </app-header-with-buttons>
      </div>
    </div>
    <!--List-->

    <div class="card">
      <table class="table table-list">
          <thead>
            <tr>
              <th>Weapon Rarity</th>
              <th>Slots Available for Silicates</th>
              <th>Stars (Total)</th>
            </tr>         
          </thead>
          <tbody>           
            <ng-container *ngFor="let weapon of this.weaponList">
              <tr>
                <td [ngStyle]="{'background-color': weapon.name | tierColor : 'Weapon Rarity', 'color':'#fff'}">
                  {{weapon.name}}
                </td>  
                <td >
                  <input  placeholder=" " class="form-control form-short background-input-table-color"
                    type="number"
                    #availablesSilicatesSlots
                    [value]="weapon.availablesSilicatesSlots"
                    (change)="onChangeAvailableRarity(weapon, availablesSilicatesSlots.value)"/>
                </td>
                <td >
                  <input  placeholder=" " class="form-control form-short background-input-table-color"
                    type="number"
                    #stars
                    [value]="weapon.stars"
                    (change)="onChangeStars(weapon, stars.value)"/>
                </td>
            </ng-container>        

          </tbody>             
      </table> 
         <ng-container *ngIf="this.weaponList.length == 0">
            <p class="noWeapons">No Weapons Rarity</p>
        </ng-container>      
    </div>
  </div>
</div>

