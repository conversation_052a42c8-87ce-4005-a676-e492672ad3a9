import { Component, OnInit } from '@angular/core';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';
import { ItemClass } from 'src/app/lib/@bus-tier/models/ItemClass';
import { ItemService } from 'src/app/services';
import { CustomService } from 'src/app/services/custom.service';
import { ItemClassService } from 'src/app/services/item-class.service';
import { FILTER_SUFFIX_PATH } from 'src/lib/darkcloud/angular/dsadmin/constants/others';

@Component({
  selector: 'app-silicate-class-selection',
  templateUrl: './silicate-class-selection.component.html',
  styleUrls: ['./silicate-class-selection.component.scss'],
})
export class SilicateClassSelectionComponent implements OnInit {

  itemClasses: ItemClass[];
  custom: Custom;

  constructor(
    private _itemClassService: ItemClassService,
    private _itemService: ItemService,
    private _customService: CustomService,
  ) {

  }

  async ngOnInit(): Promise<void>
  {
    await this._itemClassService.toFinishLoading()
    await this._customService.toFinishLoading()

    this.itemClasses = this._itemClassService.models;
    this.custom = await this._customService.svcGetInstance();

    if(!this.custom.silicatesClassItem)
    {
      this.custom.silicatesClassItem = [];
    }

  }

  async onItemClassSelected(itemClass: ItemClass)
  {
    this._customService.toggleItemClass(itemClass, 'silicatesClassItem');
    this.custom = await this._customService.svcGetInstance();
  }

}
