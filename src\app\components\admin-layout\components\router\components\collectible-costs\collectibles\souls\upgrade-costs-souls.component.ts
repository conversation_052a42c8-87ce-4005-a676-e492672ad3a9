import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/SortableListComponent';
import { Component} from '@angular/core';
import { Character, CollectibleRaritySouls } from 'src/app/lib/@bus-tier/models';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Button } from 'src/app/lib/@pres-tier/data';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';

import { Alert } from 'src/lib/darkcloud';
import { CollectibleRaritySoulsService } from 'src/app/services/collectibleRaritySouls.service';
import { TierService } from 'src/app/services';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-upgrade-costs-souls',
  templateUrl: './upgrade-costs-souls.component.html',
  styleUrls: ['./upgrade-costs-souls.component.scss']
})

export class UpgradeCostsSoulsComponent extends SortableListComponent<CollectibleRaritySouls> 
{
  tableHeaderNames:string[] = ['baseSouls', 'ascensionSouls', 'costSouls'];
  tierList:string[] = [];
  soulsList : CollectibleRaritySouls[] = [];

  constructor(
    _activatedRoute: ActivatedRoute,
    protected _collectibleRaritySoulsService: CollectibleRaritySoulsService,
    _userSettingsService: UserSettingsService,
    private _tierListService: TierService,
    protected _languageService: LanguageService,
    protected _translationService: TranslationService
  ) 
  {
    super(_collectibleRaritySoulsService, _activatedRoute, _userSettingsService, 'name');
  }

  protected override async lstInit() 
  {
    await this._collectibleRaritySoulsService.toFinishLoading();
    await this._tierListService.toFinishLoading();
    this.tierList = this._tierListService.fillRarityArrayDynimically('Code Block Rarity', this.tierList);
    this.soulsList = this._collectibleRaritySoulsService.models;
    this.sortSoulsListBasedOnTierList();
    this.lstFetchLists();
  }

  public readonly excelButtonTemplate: Button.Templateable = 
  {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  public downloadSceneryOrtography(character: Character) 
  {
    this._translationService.getCharacterOrtography(character, true);
  }

  sortSoulsListBasedOnTierList()
  {
    this.soulsList.sort((a,b)=> this.tierList.indexOf(a.typeSouls) - this.tierList.indexOf(b.typeSouls));
  }
  
  async changeBlockDrops(codeBlockDrop: CollectibleRaritySouls, value: string, fieldName:string)
  {
    codeBlockDrop.hard[fieldName] = value == ''? undefined : +value;
    await this._collectibleRaritySoulsService.svcToModify(codeBlockDrop);
    await this._collectibleRaritySoulsService.toSave();
  }

  async onExcelPaste(): Promise<void> 
  {
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter(line => line);
    let bossAscensionFields: string[] = ['typeSouls', 'collectibleLevel', 'baseSouls', 'ascensionSouls', 'costSouls'];    
    if(this.displayErrors(lines)) return


    for (let l = 0; l < lines.length; l++) 
    {
      let line = lines[l];
      let cols = line.split(/\t/);

      let souls = this._collectibleRaritySoulsService.models.find((soul) =>
      {
        return soul.collectibleLevel == +cols[1]
        .split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.');
      });

      if(!this.tierList.includes(cols[0].trim()))
      {
        return Alert.showError('',`The tier: ${cols[0]}, does NOT exist in the Tier list!`);
      }
  
      if (!souls) 
      {
        if(!this.tierList.includes(cols[0].trim()))
        {
          return Alert.showError('',`The tier: ${cols[0]}, does NOT exist in the Tier list!`);
        }
        let baseLevel = +cols[1]
        .split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.');
        
        souls = this._collectibleRaritySoulsService.createNewCollectibleRaritySouls(baseLevel,cols[0].toLowerCase());
      }

      for(let i = 0; i < bossAscensionFields.length; i++)
      {
        if (cols[i]?.trim()) 
        {
          souls[bossAscensionFields[i]] = i == 0 ? cols[i] : +cols[i]
          .split(' ')
          .join('')
          .split('.')
          .join('')
          .replace(',','.')
        } 
        else souls[bossAscensionFields[i]] = undefined;
      }
    
      this._collectibleRaritySoulsService.svcToModify(souls);
      this._collectibleRaritySoulsService.toSave();
      Alert.ShowSuccess('Collectible Rarity Souls  imported successfully!');

      this.lstFetchLists();
    }
  }

  displayErrors(array)
  {
    let count = array[0].split(/\t/)
    if(count.length < this.tableHeaderNames.length+2)
    {
      Alert.showError("Copy the ORDER column values too!")
      return true
    }
    
    if(count[0] === "")
    {
      Alert.showError("You are probably copying a blank column!")
      return true
    }

    return false
  }


}
