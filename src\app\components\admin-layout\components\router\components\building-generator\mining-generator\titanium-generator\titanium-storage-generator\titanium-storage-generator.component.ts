import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/SortableListComponent';
import { Component } from '@angular/core';
import { Character,TitaniumStorage } from 'src/app/lib/@bus-tier/models';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Button } from 'src/app/lib/@pres-tier/data';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { TitaniumStorageService } from 'src/app/services/titanium-storage.service';
import { Alert } from 'src/lib/darkcloud';
import { SpinnerService } from './../../../../../../../../../spinner/spinner.service';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-titanium-storage-generator',
  templateUrl: './titanium-storage-generator.component.html',
})
/**
 * Displays and edits emotion data as a list
 */
export class TitaniumStorageGeneratorComponent extends SortableListComponent<TitaniumStorage> {
  constructor(
    private spinnerService: SpinnerService,
    _activatedRoute: ActivatedRoute,
    protected _titaniumStorageService: TitaniumStorageService,
    _userSettingsService: UserSettingsService,
    protected _languageService: LanguageService,
    protected _translationService: TranslationService,
  ) {
    super(_titaniumStorageService, _activatedRoute, _userSettingsService, 'name');
  }

  public readonly excelButtonTemplate: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  public downloadSceneryOrtography(character: Character)
  {
    this._translationService.getCharacterOrtography(character, true);
  }

  protected override lstInit()
  {

  }
  description = ""
  protected override lstAfterFetchList()
  {
    let soulsTypeCAmount = []
    this._titaniumStorageService.models;
    if(this._titaniumStorageService.models.filter(model => model.type === "A").length === 0)
    {
      for(let l = 1; l <= 20; l++)
      {
        this._titaniumStorageService.createNewTitaniumStorage(l, "A");
      }
      this._titaniumStorageService.toSave();
      this.lstFetchLists();
    }

     //remove empty element that just has lablevel == 0.
     this._titaniumStorageService.models.find(blueprint => 
      {
        if(blueprint.titaniumLevel === 0)          
        this._titaniumStorageService.svcToRemove(blueprint.id)
      })
    soulsTypeCAmount = this._titaniumStorageService.models.filter(model => model.type === "A")
    this.description = `Showing ${soulsTypeCAmount.length} results`;
  }

  async onExcelPaste(): Promise<void>
  {
    this.spinnerService.setState(true)
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter(line => line);

    if(this.DisplayErrors(lines)) return
    
    for(let l = 0; l < lines.length; l++)
    {
      let line = lines[l];
      let cols = line.split(/\t/);
          

      let titaniumStorage = this._titaniumStorageService.models.find(ts => ts.titaniumLevel === +(cols[0].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.'))&& ts.type === "A");
      if(!titaniumStorage)
      {
        titaniumStorage = this._titaniumStorageService.createNewTitaniumStorage(+(cols[0].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.')), "A");
      }
      

      if(cols[1]?.trim())
      {
        titaniumStorage.souls = +(cols[1].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.'));
      }
      else
      {
        titaniumStorage.souls = undefined;
      }
      if(cols[2]?.trim())
      {
        titaniumStorage.time = +(cols[2].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.'));
      }
      else
      {
        titaniumStorage.time = undefined;
      }
      if(cols[3]?.trim())
      {
        titaniumStorage.rubies = +(cols[3].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.'));
      }
      else
      {
        titaniumStorage.rubies = undefined;
      }
      if(cols[4]?.trim())
      {
        titaniumStorage.storage = +(cols[4].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.'));
      }
      else
      {
        titaniumStorage.storage = undefined;
      }

      await  this._titaniumStorageService.svcToModify(titaniumStorage);
      await  this._titaniumStorageService.toSave();

      this.lstFetchLists();
    }
    this.spinnerService.setState(false)
  }

  DisplayErrors(array)
  {
    let count = array[0].split(/\t/)
    if(count.length < 5)
    {
      Alert.showError("Copy the TITANIUM LEVEL column values too!")
      this.spinnerService.setState(false)
      return true
    }
    
    if(count[0] === "")
    {
      Alert.showError("You are probably copying a blank column!")
      this.spinnerService.setState(false)
      return true
    }

    return false
  }

}
