.table-container {
    width: 100%;
    max-height: 1200px; /* Define a altura máxima para a rolagem vertical */
    overflow: auto; /* Adiciona a barra de rolagem quando necessário */

  }

  table { 
    width: 100%;
    min-width: 800px; /* Define a largura mínima para a rolagem horizontal */
  }

  th, td {
    text-align: left;
  }

  th {
    background-color: #f4f4f4;
    position: sticky;
    top: 0; /* Mantém o cabeçalho fixo ao rolar */
    z-index: 1;
  }

  tr:nth-child(even) {
    background-color: #f9f9f9;
  }

  
h3{
  font-size: 28px;
  margin: 10px 0 10px;
}
.borderList {
  border: 1px solid #ddd !important;
}

.addButton
{  
position: absolute;
  top: -1px;
  right: 13px;
}

.default-color {
  background-color: #AEAAAA !important;
}

.aligTitle {
  text-align-last: center !important;
  width: 4%;
}

.noCursor {
  cursor: default !important;
}

