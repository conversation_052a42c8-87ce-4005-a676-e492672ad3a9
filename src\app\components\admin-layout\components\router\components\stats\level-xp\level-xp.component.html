<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="card list-header-row">
      <app-header-with-buttons [cardTitle]="'Level XP'"
                               [cardDescription]="cardDescription"
                               [rightButtonTemplates]="[excelButtonTemplate]"
                               [isBackButtonEnabled]="false">
      </app-header-with-buttons>
      <app-header-search (inputKeyup)="lstOnChangeFilter($event)"></app-header-search>
      <select class="dropdown filter-dropdown limited"	
            (change)="SelectArea($event)" >	
            <option	
            *ngFor="let area of mainAreas"	
            [value]="area?.id"	
            [selected]="selectedArea?.id == area?.id">{{ area?.name}}</option>	
        </select>
    </div>
    <!--List-->
    <div class="card">
      <table class="table table-list">
        <thead style="top: 115px">
          <tr>
            <th>Index</th>
            <th class="th-clickable">
              ID
            </th>
            <th class="th-clickable">Name</th>
            <th class="th-clickable">XP</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="
                let level of filteredLevels;
                let i = index;
                trackBy: trackById
              ">
            <tr id="{{ level?.id }}">
              <td class="td-sort">{{ i + 1 }}</td>
              <td class="td-id">{{ level?.id }}</td>
              <td class="td-id">{{ level?.name }}</td>
              <td class="td-id">
                <input placeholder=" " class="form-control form-short background-input-table-color" type="number"
                   [ngClass]="{'empty-input': !level?.xp}" value="{{ level?.xp != undefined ? level?.xp : undefined}}"
                   #xp 
                   (change)="changeLevelXP(level, xp.value)" /></td>
              <td class="td-id">
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>
