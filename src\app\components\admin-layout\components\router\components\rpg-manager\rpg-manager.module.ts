import { RpgManagerComponent } from './rpg-manager.component';
import { RpgWordingSuggestionManagerComponent } from './components/rpg-wording-suggestion-manager/rpg-wording-suggestion-manager.component';
import { RpgWordingDetectionManagerComponent } from './components/rpg-wording-detection-manager/rpg-wording-detection-manager.component';
import { NgModule } from '@angular/core';
import { MajorModule } from 'src/app/major.module';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { BrowserModule } from '@angular/platform-browser';
import { ContentInputModule } from 'src/app/components/content-input/content-input.module';
import { PopupModule } from 'src/app/components/popup/popup.module';
import { RouterModule } from '@angular/router';

@NgModule({
  imports: [
    CommonModule,
    BrowserModule,
    FormsModule,
    RouterModule,
    MajorModule,
    ContentInputModule,
    PopupModule,
  ],
  declarations: [
    RpgManagerComponent,
    RpgWordingDetectionManagerComponent,
    RpgWordingSuggestionManagerComponent,
  ],
  exports: [RpgManagerComponent],
})
export class RpgManagerModule {}
