import {ChangeDetectorRef,Component,EventEmitter,Input,OnDestroy,Output} from '@angular/core';
import { Subscription } from 'rxjs/internal/Subscription';
import {Area,Character,DCKnowledgeGuide,Dialogue,Item,Knowledge,Level,Mission,Option,OptionBox,SituationalModifier,SpokePlace,StoryBox,SubContextKnowledge} from 'src/app/lib/@bus-tier/models';
import { BattleUpgrade } from 'src/app/lib/@bus-tier/models/BattleUpgrade';
import { DCGuide } from 'src/app/lib/@bus-tier/models/DCGuide';
import { RoadBlock } from 'src/app/lib/@bus-tier/models/RoadBlock';
import { Typing } from 'src/app/lib/@pres-tier';
import {
  AreaService,
  DCGuideService,
  DCKnowledgeGuideService,
  KnowledgeService,
  LevelService,
  ReviewService,
  SubContextKnowledgeService,
} from 'src/app/services';
import { DialogueService } from 'src/app/services/dialogue.service';
import { EventHandlerService } from 'src/app/services/eventHandler.service';
import { OptionBoxService } from 'src/app/services/option-box.service';
import { OptionService } from 'src/app/services/option.service';
import { RoadBlockService } from 'src/app/services/road-block.service';
import { StoryBoxService } from 'src/app/services/story-box.service';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Alert } from 'src/lib/darkcloud';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { RoadBlockType } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard/IRoadBlock';
import { GameTypes, OptionBoxType } from 'src/lib/darkcloud/dialogue-system';
import { HighlightElement } from 'src/lib/others';
import { LevelHelperService } from '../../../../../../../../../../services/level-helper.service';
import { SituationalModifierService } from 'src/app/services/situational-modifier.service';
import { EventService } from 'src/app/services/event.service';
import { MarkerService } from 'src/app/services/marker.service';
import { SpeechService } from 'src/app/services/speech.service';
import { SettingsComponent } from '../../../../../settings/settings.component';
import { Router } from '@angular/router';


interface SpokePlaceHelper {
  elementId: string;
  text: string;
  label: string;
  component: string;
}

@Component({
  selector: 'app-investigation',
  templateUrl: './investigation.component.html',
  styleUrls: ['./investigation.component.scss'],
})
export class InvestigationComponent implements OnDestroy {
  @Input() index: number;
  @Input() dialogue: Dialogue;
  @Input() optionBox: OptionBox;
  @Input() preloadedSpeakers: Character[]; //A informação do Speakers é adicionada nos levels
  @Input() preloadedMissionsOfArea: Mission[];
  @Input() language: string;
  @Output() refresh: EventEmitter<void> = new EventEmitter();
  public HighlightElement = HighlightElement;
  public OptionBoxType = OptionBoxType;
  private rpgSubscription: Subscription;
  public roadblockId: string;
  existOptionBox: Option;
  isReset = false;
  optionReset: Option;

  toRemoveProcessConditionFunc: (roadblock: any) => void;

  popupStats = false;
  openAttributeBox = false;
  openITDBox = false;
  openSubcontext = false;
  openSubcontextKnowledge = false;
  openDCBox = false;
  openSituationalModifier = false;
  isConfirm = false;
  selectedFactorSM: any;
  selecteKnowledge: any;
  selectSubcontext: any;
  selectedITD: any;
  selectedITDKnowledge: any;
  selectSubcontextKnowledge: any;
  resultDC: number;
  valueModifierITD: number;
  descriptionDCGuide: string;
  descriptionInvestigation: string;
  descriptionSubcontext: string;
  descriptionSubcontextKnowledge: string;
  investigationPositive: string;
  investigationNegative: string;
  valueSituationalModifier: number;
  descriptionSituationalModifier: string;

  optionId: string;
  classeName: string;
  situationalModifier: SituationalModifier;
  listSubcontextKnowledge: SubContextKnowledge;
  listSituationalModifier: SituationalModifier[] = [];
  listDCKnowledgeGuide: DCKnowledgeGuide[] = [];
  listKnowledge: Knowledge[] = [];
  optionDifficultyClass: Option;
  difficulty: any;
  difficultyKnowledge: any;
  subcontext: string;
  subcontextKnowledge: string;
  hasLabel: boolean = false;
  timeout: any;
  timeout2: any;
  optionBoxes;
  answerBoxPositive: StoryBox;
  getOption;
  usedRoadBlocks = [];
  usedOnLevels = [];
  itemList: Item[] = [];
  roadBlocksUseds: RoadBlock[] = [];
  listDCGuide: DCGuide[] = [];
  isTextValid = true;
  listSpokePlace: SpokePlace[] = [];

  constructor(
    private _userSettingsService: UserSettingsService,
    private _dialogueService: DialogueService,
    private _optionBoxService: OptionBoxService,
    private _router: Router,
    private _speechService: SpeechService,
    private _eventService: EventService,
    private _markerService: MarkerService,
    private _storyBoxService: StoryBoxService,
    private _optionService: OptionService,
    private _roadblockService: RoadBlockService,
    private _eventsHandlerService: EventHandlerService,
    private _change: ChangeDetectorRef,
    private _levelHelperService: LevelHelperService,
    private _settingsComponent :SettingsComponent,
    private _areaService: AreaService,
    private _levelService: LevelService,
    private _reviewService: ReviewService,
    private _dcGuideService: DCGuideService,
    private _knowledgeService: KnowledgeService,
    private _situationalModifierService: SituationalModifierService,
    private _subContextKnowledgeService: SubContextKnowledgeService,
    private _dCKnowledgeGuideService: DCKnowledgeGuideService
  ) {
    this.toRemoveProcessConditionFunc = this.toRemoveRoadblock.bind(this);
  }

  async ngOnInit() {
    this._dCKnowledgeGuideService.toFinishLoading();
    this.rpgSubscription = this._eventsHandlerService
      .OnRpgWordAdded()
      .subscribe((object) => {
        this.optionBox = this._optionBoxService.svcCloneById(this.optionBox.id);
      });

    this.timeout = setTimeout(() => {
      this._change.detectChanges();
    }, 500);

    if (this.optionBox) {
      if (this.optionBox.AndOrCondition == undefined) {
        this.optionBox['AndOrCondition'] = 'OR';
      }
  
      this._dcGuideService.toFinishLoading();
      this.listDCGuide = this._dcGuideService.models;
      this._levelHelperService.toFinishLoading();
      this.listSpokePlace = this._levelHelperService.models;
      await this.getRoadBlocks();
  
      this.listKnowledge = this._knowledgeService.models;
      this.listDCKnowledgeGuide = this._dCKnowledgeGuideService.models;
  
      this.getDescriptionKnowledge();
      this.optionBoxes = this._optionService.svcFilterByIds(
        this.optionBox.optionIds,
        true
      );   
      this.addTypeOPtions();
      this.roadblocksForKeyInformation();
    }
  }

  addTypeOPtions() {
    this.optionBoxes.forEach((option) => {
      if (option.type === undefined) {
        option.typ = this.optionBox.type;
      }
      this._optionService.svcToModify(option);
    });
  }

  async getDescriptionKnowledge() {
    const optionListBoxes = this._optionService.svcFilterByIds(
      this.optionBox.optionIds,
      true
    );

    optionListBoxes.forEach((option) => {
      if (option?.descriptionInvestigation) {
        setTimeout(() => {
          const matchingGuide = this.listDCKnowledgeGuide.find(
            (x) => option.valueModifierITD === x.dcMin || option.valueModifierITD === x.dcMax
          );
          if (matchingGuide) {
            option.descriptionInvestigation = this.transaleteKnowledge(
              option.nameKnowledge,
              matchingGuide
            );
          }
          this._optionService.svcToModify(option);
          return option;
        }, 100);
      }
    });
  }

  async roadblocksForKeyInformation() {
    this._roadblockService.toFinishLoading();
    const roadblocks = this._roadblockService.models.filter((roadblock) => {
      return roadblock.spokeElementId === this.optionBox?.id
    });
    
    roadblocks.forEach((roadblock) => {
      const dialogueIds = this._dialogueService.models
        .filter((dialogue) => {
          return (
            roadblock.StoryBoxId &&
            !dialogue.id.includes('ML') &&
            roadblock.StoryBoxId.includes(dialogue.id)
          );
        })
        .map((dialogue) => dialogue.id);

      dialogueIds.forEach((dialogueId) => {
        const areaId = Area.getSubIdFrom(dialogueId);
        const area = this._areaService.svcFindById(areaId);
        if (!area) return;

        const levelId = Level.getSubIdFrom(dialogueId);
        const level = this._levelService.svcFindById(levelId);
        const dialogue = this._dialogueService.svcFindById(
          Dialogue.getSubIdFrom(dialogueId, 'PT-BR')
        );
        const hierarchyCode = area.hierarchyCode;
        const levelIndex = this._reviewService.reviewResults[levelId]?.index;
        const type = GameTypes.dialogueTypeDisplay[+dialogue.type];

        this.usedOnLevels.push(
          `[${hierarchyCode}] ${levelIndex} "${level.name}" (${type})`
        );
      });

      this.usedRoadBlocks.push(roadblock);
    });
  }

  async getRoadBlocks() {
    this._optionBoxService.toFinishLoading();
    const idsToRemove = this._optionBoxService.models.map(
      (option) => option.id
    );

    this._roadblockService.toFinishLoading();
    this._roadblockService.models.filter(
      (roadblock) => !idsToRemove.includes(roadblock?.StoryBoxId)
    );
    let rb = await this._roadblockService.models.find(
      (rb) => rb.spokeElementId == this.optionBox.id
    );
    if (rb) this.hasLabel = true;
  }

  ngOnDestroy() {
    // prevent memory leak when component is destroyed
    this.rpgSubscription.unsubscribe();

    clearInterval(this.timeout);
    clearInterval(this.timeout2);
  }

  public getInformation(id: string): Data.Internal.Base {
    return this._userSettingsService.getInformation(id);
  }

  public updateInformation<TKey extends keyof Data.Internal.Base>(
    id: string,
    key: TKey,
    value: Data.Internal.Base[TKey]
  ) {
    if (id) this._userSettingsService.updateInformation(id, key, value);
  }

  // ng trackers (for list optimization)
  trackByIndex(index: number, option: Option): any {
    return index;
  }

  public async toMove(transpose: number) {
    if (this.dialogue.boxIds.length <= 1) return;
    const oldIndex = this.dialogue.boxIds.indexOf(this.optionBox.id) + 0;
    const newIndex = oldIndex + transpose;
    if (!this.dialogue.boxIds[newIndex]) return;

    this.dialogue.boxIds[oldIndex] = this.dialogue.boxIds[newIndex];
    this.dialogue.boxIds[newIndex] = this.optionBox.id;
    await this._dialogueService.svcToModify(this.dialogue);

    this._change.detectChanges();
    this.refresh.emit();
  }

  async toRemove(box: OptionBox) { //Remove todo o componente

    if (await Alert.showRemoveAlert(box.id)) {
      await this._dialogueService.removeBox(this.dialogue, box.id);
      this._optionBoxService.svcToRemove(box.id);
      box?.optionIds.forEach((optionId) => {
        this._optionService.svcToRemove(optionId);
      });
      await this.removePostiveAndNegativeComponent(box.id);
      this._userSettingsService.removeIdObjectInformations(box.id);
      this.removePlacesLabels(box.id);
      this._change.detectChanges();
      this.ngOnInit();
      this.refresh.emit();
      this.uploadRedirect(box.id);
  }     

   this._change.detectChanges();
   this.refresh.emit();     
  }

  uploadRedirect(id: string) {
    const levelId = id.split('.').slice(0, 2).join('.');
    const dialogueId = id.split('.').slice(0, 3).join('.');

    this._router.navigate(['levels/' + levelId + '/dialogues']);
    setTimeout(() => {      
      this._router.navigate(['levels/' + levelId + '/dialogues/' + dialogueId]);
    }, 75)

  }

removePlacesLabels(id: string) {
    this._roadblockService.models.forEach((roadblock) => {
      if (roadblock.spokeElementId === id) {
        roadblock.spokeElementId = undefined;
        this._roadblockService.svcToModify(roadblock);
      }
    });   

    this._levelHelperService.models.forEach((places) => {
      if (places.elementId.includes(id)) {
        this._levelHelperService.svcToRemove(places.id);
      }
    }); 
  }

  public async onChange(
    optionBox: OptionBox,
    property?: string,
    value?: string
  ) {
    if (property) optionBox[property] = value;

    await this._optionBoxService.svcToModify(this.optionBox);
    this._change.detectChanges();
  }

  public async onChangeOption(option: Option) {
    await this._optionService.svcToModify(option);
    this._change.detectChanges();
  }

  public async onChangeOptionValue(
    option: Option,
    property: string,
    value: any
  ) {
    option[property] = value;
    await this._optionService.svcToModify(option);
    this._change.detectChanges();
    this.refresh.emit();
  }

  //Look the method on the documentation to understand better.
  async updateOptionFromAnswerBox(option: Option) {
    for (let i = 0; i < this.optionBoxes.length; i++) {
      if (this.optionBoxes[i].id == option.id) {
        this.optionBoxes[i].label = option.label;
        break;
      }
    }

    await this._optionService.svcToModify(option);
    this._change.detectChanges();
    this.refresh.emit();
  }

  public async selectWeight(option: Option) {
    await Alert.showNumberField('Choose Weight').then(async (e) => {
      if (e.isDismissed) return;

      option.weight = e.value;
      if (option.weight === 0) {
        option.weight = undefined;
      }
      await this._optionService.svcToModify(option);
    });
    this._change.detectChanges();
    this.refresh.emit();
  }

  // methods that can be called within the html (add, remove, move listItems)
  public async toMoveOption(option: Option, transpose: number) {
    if (this.optionBox.optionIds.length <= 1) return;

    const oldIndex = this.optionBox.optionIds.indexOf(option.id) + 0;
    const newIndex = oldIndex + transpose;
    if (!this.optionBox.optionIds[newIndex]) return;

    this.optionBox.optionIds[oldIndex] = this.optionBox.optionIds[newIndex];
    this.optionBox.optionIds[newIndex] = option.id;
    await this._optionBoxService.svcToModify(this.optionBox);
    this._change.detectChanges();
    this.ngOnInit();
    this.refresh.emit();
  }

  listDit = [
    { name: 'Muito fácil: 5', levelValeu: 5 },
    { name: 'Fácil: 10', levelValeu: 10 },
    { name: 'Moderado: 15', levelValeu: 15 },
    { name: 'Difícil: 20', levelValeu: 20 },
    { name: 'Muito difícil: 25', levelValeu: 25 },
    { name: 'Quase impossível: 30', levelValeu: 30 },
  ];

  // Difficulty class
  public async openModalDificultyClass(option: Option) {
    this.optionDifficultyClass = option;
    this.isConfirm = false; // Habilita o botão de confirmação após alteração

    // Verifica se há valores já definidos
    if (option.investigaDifficulty !== undefined) {
      this.existOptionBox = option;
      this.isReset = true;
      this.optionReset = option;
      this.openModalInvestigation(option);
    } else {
      // Se não houver valores definidos, abre o modal sem preenchimento

      this.difficulty = null;
      this.resultDC = null;
      this.classeName = null;
      this.selecteKnowledge = null;
      this.selectedFactorSM = null;
      this.valueSituationalModifier = null;
      this.selectedITD = null;
      this.selectSubcontext = null;
      this.isReset = false;

      // Inicializa os valores
      this.openSubcontextKnowledge = false;
      this.openSituationalModifier = false;
      this.openAttributeBox = false;
      this.openSubcontext = false;
      this.openITDBox = false;
      this.openDCBox = false;
    }
    this.popupStats = true;
  }

  openModalInvestigation(option: Option) {
    //Investigation

    this.selecteKnowledge = option?.nameKnowledge;
    this.selectedFactorSM = option?.fatorSituationModifier;
    this.valueSituationalModifier = option?.valueSituationModifier;
    this.selectSubcontextKnowledge = option?.subcontext;
    this.descriptionSubcontextKnowledge = option?.subContextDescription;
    this.descriptionInvestigation = option?.descriptionInvestigation;
    this.descriptionDCGuide = option?.descriptionDCGuide;

    if (option?.descriptionSituationalModifier) {
      this.descriptionSituationalModifier = option?.descriptionSituationalModifier;
    }
    else {
      this.descriptionSituationalModifier = this._situationalModifierService.models.find((x) => x.factor === this.selectedFactorSM)?.description[0];
    }

    this._subContextKnowledgeService.toFinishLoading();
    this.listSubcontextKnowledge = this._subContextKnowledgeService.models.find(
      (x) => x.knowledge === this.selecteKnowledge
    );

    if (!this.listSubcontextKnowledge.subContext.includes('None (Default)')) {
      this.listSubcontextKnowledge.subContext.unshift('None (Default)');
    }

    this.selectSubcontext = 'None (Default)';

    this.situationalModifier = this._situationalModifierService.models.find(
      (x) => x.knowledge === this.selecteKnowledge
    );

    // Preenche ITD
    this.difficulty = this.listDit.find(
      (dif) => dif.name === option?.investigaDifficulty
    );
    this.selectedITD = this.difficulty;

    // Preenche o DC
    this.resultDC = option.resultDC;
    this.valueModifierITD = option.valueModifierITD;

    // Abre os campos relevantes
    this.openSituationalModifier = true;
    this.openSubcontextKnowledge = true;
    this.openITDBox = true;
    this.openDCBox = true;
  }

  transaleteKnowledge(knowledge: string, cdKnowledge: DCKnowledgeGuide) {
    const knowledgeMap = {
      Arcanismo: cdKnowledge?.arcana,
      Engenharia: cdKnowledge?.engineering,
      Investigação: cdKnowledge?.investigation,
      Furtividade: cdKnowledge?.stealth,
    };
    return knowledgeMap[knowledge];
  }

  getDCGuide(option: Option) {
    for (let index = 0; index < this.listDCGuide.length; index++) {
      if (
        this.listDCGuide[index].dcMin === option.valueModifierITD ||
        this.listDCGuide[index].dcMax === option.valueModifierITD
      ) {
        option.descriptionDCGuide = this.listDCGuide[index].description;
        this.descriptionDCGuide = this.listDCGuide[index].description;
        this._optionService.svcToModify(option);
        this._optionService.toSave();
        this._change.detectChanges();
      }
    }
  }

  //MODAL
  getKnowledge(nome: string, index: number) {
    this.selecteKnowledge = nome;

    //Atribui valores positivo eou negativo
    this.investigationPositive = this.listKnowledge[index].positive;
    this.investigationNegative = this.listKnowledge[index].negative;

    //Verif se o valor selecionado é igual ao valor do Subcontext
    this.listSubcontextKnowledge = this._subContextKnowledgeService.models.find(
      (x) => x.knowledge === this.selecteKnowledge
    );

    if (!this.listSubcontextKnowledge.subContext.includes('None (Default)')) {
      this.listSubcontextKnowledge.subContext.unshift('None (Default)');
    }

    this.selectSubcontextKnowledge = 'None (Default)';
    //this.situationalModifier = this._situationalModifierService.models[index];
    this.situationalModifier = this._situationalModifierService.models.filter(
      (x) => x.knowledge === this.selecteKnowledge
    )[0];
    this.openSituationalModifier = true;

    // Resetar caixas subsequentes
    this.openITDBox = false;
    this.selectedITD = null;
    this.selectedFactorSM = null;
    this.openDCBox = false;
    this.resultDC = null;
    this.valueModifierITD = null;
    this.openSubcontextKnowledge = false;
    this.isConfirm = false; // Habilita o botão de confirmação após alteração
  }

  getTaskSubContextKnowledge(sub: any, index: number) {
    this.subcontextKnowledge = sub;
    this.selectSubcontextKnowledge = sub;

    if (this.selectSubcontextKnowledge !== 'None (Default)') {
      index = index - 1;
      this.descriptionSubcontextKnowledge = this.listSubcontextKnowledge.description[index];
    } else {
      this.descriptionSubcontextKnowledge = '';
    }

    this.openITDBox = true;
    this.selectedITD = null;
    this.isConfirm = false;
  }

  getSituationalModifier(factor: string, index: number) {
    this.selectedFactorSM = factor;
    this.valueSituationalModifier = parseInt(
      this.situationalModifier.situationalmodifier[index]
    );
    this.descriptionSituationalModifier = this.situationalModifier.description[index];
    
    this.openITDBox = false;
    this.openDCBox = false;
    this.openSubcontext = false;
    this.openSubcontextKnowledge = true;

    // Resetar caixa subsequente
    this.selectedITD = null;
    this.openDCBox = false;
    this.resultDC = null;
    this.valueModifierITD = null;
    this.valueModifierITD = null;
    this.isConfirm = false;
    this.selectedITD = null;
    this.selectSubcontextKnowledge = null;
  }

  getTaskDifficultyClass(dif: any) {
    this.difficultyKnowledge = dif;
    this.selectedITD = dif;
    this.resultDC = this.difficultyKnowledge.levelValeu;

    this.valueModifierITD = this.calculateKnowledgeDC(this.resultDC, this.valueSituationalModifier);

    //Pega o texto do DC Knowledge Guide de acordo com o resultado total
    const matchingGuide = this.listDCKnowledgeGuide.find(
      (x) => x.dcMin === this.valueModifierITD || x.dcMax === this.valueModifierITD
    );
    if (matchingGuide) {
      this.descriptionInvestigation = this.transaleteKnowledge(
        this.selecteKnowledge,
        matchingGuide
      );
      this.descriptionDCGuide = matchingGuide.description;
    }

    this.isConfirm = true;

    this.openDCBox = true;
  }

  async resetDice(resetOption: Option) { 

    try {
      if (await Alert.showRemoveAlert('DICE')) {    
        const answerPostive =  this._storyBoxService.svcFindById(resetOption.answerBoxId);   
        answerPostive.choicePositive = undefined;
        answerPostive.descriptionDCGuideOption = undefined;
        answerPostive.resultDCOption = undefined;
        answerPostive.nameKnowledge = undefined;
        answerPostive.resultDCOption = undefined;
        answerPostive.subcontext = undefined;
        answerPostive.type = answerPostive.type;
        answerPostive.messageOption = answerPostive?.messageOption;
        this._storyBoxService.svcToModify(answerPostive);      

        this.resetIdNegative(resetOption.answerBoxNegativeId);

        resetOption.answerBoxNegativeId = undefined;
        resetOption.bl = undefined;
        resetOption.choiceAtributte = undefined;
        resetOption.choiceDifficulty = undefined;
        resetOption.choiceNegative = undefined;
        resetOption.choicePositive = undefined;
        resetOption.choiceSpeech = undefined;
        resetOption.classModifierValue = undefined;
        resetOption.classeNameOpponet = undefined;
        resetOption.descriptionDCGuide = undefined;
        resetOption.difficultClassValue = undefined;
        resetOption.fatorSituationModifier = undefined;
        resetOption.investigaDifficulty = undefined;
        resetOption.investigationPositive = undefined;
        resetOption.investigationNegative = undefined;          
        resetOption.nameKnowledge = undefined;
        resetOption.resultDC = undefined;
        resetOption.valueModifierITD = undefined;
        resetOption.subContextDescription = undefined;
        resetOption.subcontext = undefined;
        resetOption.valueSituationModifier = undefined;
        this.existOptionBox = undefined;
        resetOption.labelAnswerNegative = undefined;
        resetOption.labelAnswerPositive = resetOption?.labelAnswerPositive 
        this._optionService.svcToModify(resetOption);          
      }
    } catch (error) {
      Alert.showError(error);
    }   

    this.popupStats = false;
    this._change.detectChanges();
   // this.refresh.emit();
    this.ngOnInit();
  }

resetIdNegative(id: string) {
    this._storyBoxService.svcToRemove(id);
    this._speechService.svcToRemove(id);
    this._eventService.svcToRemove(id);
    this._markerService.svcToRemove(id);
    this._roadblockService.svcToRemove(id);
    const place = this._levelHelperService.models.find((x) => x.elementId === id);
    this._levelHelperService.svcToRemove(place?.id);
  }


  confirmDifficultyClass() {
    this.optionDifficultyClass.nameKnowledge = this.selecteKnowledge;
    this.optionDifficultyClass.fatorSituationModifier = this.selectedFactorSM;
    this.optionDifficultyClass.valueSituationModifier = this.valueSituationalModifier;
    this.optionDifficultyClass.subcontext = this.selectSubcontextKnowledge;
    this.optionDifficultyClass.subContextDescription = this.descriptionSubcontextKnowledge;
    this.optionDifficultyClass.descriptionDCGuide = this.descriptionDCGuide;
    this.optionDifficultyClass.descriptionInvestigation = this.descriptionInvestigation;
    this.optionDifficultyClass.difficultClassValue = this.difficultyKnowledge;
    this.optionDifficultyClass.investigaDifficulty = this.difficultyKnowledge.name;
    this.optionDifficultyClass.resultDC = this.resultDC;
    this.optionDifficultyClass.valueModifierITD = this.valueModifierITD;
    this.optionDifficultyClass.descriptionDCGuide = this.descriptionDCGuide;
    this.optionDifficultyClass.investigationPositive = this.investigationPositive;
    this.optionDifficultyClass.investigationNegative = this.investigationNegative;
    this.optionDifficultyClass.labelAnswerPositive = this.optionBoxes?.labelAnswerPositive;
    this.optionDifficultyClass.descriptionSituationalModifier = this.descriptionSituationalModifier;
    this.optionDifficultyClass.type = 1;

    if (this.existOptionBox) {
      this.getKnowledgePositiveAndNegative();
      this._optionService.svcToModify(this.optionDifficultyClass);
      this._optionService.toSave();
    } else {
      this.getKnowledgePositive();
      this.toPromptAddAnswerBox(this.optionDifficultyClass);
    }

    this._change.detectChanges();
    this.isConfirm = false;
    this.popupStats = false;
    this.refresh.emit();
    this.ngOnInit();
  }

  getKnowledgePositiveAndNegative() {
    this.listKnowledge.forEach((x) => {
      if (x.knowledge == this.optionDifficultyClass.nameKnowledge) {
        this.optionDifficultyClass.investigationPositive = x.positive;
        this.optionDifficultyClass.investigationNegative = x.negative;
      }
    });
  }

  closeAreaStatsPopup() {
    this.popupStats = false;
    this.existOptionBox = undefined;
  }

  handleOutsideMouseClick(event: MouseEvent) {
    if (!this.popupStats) return;
    const myDiv = document.getElementById('modal-close');
    // Get the position relative to the viewport
    const rect = myDiv.getBoundingClientRect();
    const top = rect.top;
    const left = rect.left;
    //Check the x axis
    if (event.clientX < left || event.clientX > left + myDiv.offsetWidth) {
      this.closeAreaStatsPopup();
    } else if (
      event.clientY > top + myDiv.offsetHeight ||
      event.clientY < top
    ) {
      this.closeAreaStatsPopup();
    }
  }

  closeLevelReferencePopup() {
    this.popupStats = false;
    this.ngOnInit();
  }

  getKnowledgePositive() {
    this._storyBoxService.models.forEach((x) => {
      for (let index = 0; index < this.optionBoxes.length; index++) {
        if (this.optionBoxes[index].investigationPositive != undefined) {
          if (x.id.includes(this.optionBoxes[index].answerBoxId)) {
            x.investigationPositive = this.investigationPositive;
          }
        }
      }
    });
    this._storyBoxService.toSave();
  }

  checkBosslevel(currentCharacter: BattleUpgrade) {
    for (let i = 0; i < currentCharacter?.hp.length; i++) {
      if (currentCharacter?.hp[i] !== null) {
        return currentCharacter.baseLevel[i];
      }
    }
    return 0; // Caso todos os valores de hp sejam nulos
  }

  calculateCD(ITD: number, BL: number, c: number, MC: number): number {
    // Calcula a parte interna da fórmula
    if (BL == undefined) {
      BL = 0;
    }
    const internalCalculation = ITD + (BL / 100) * ((30 - ITD) / c) + MC;

    // Retorna o valor mínimo entre 30 e o cálculo interno
    return Math.min(30, internalCalculation);
  }

  calculateKnowledgeDC(ITD: number, valueSM: number): number {
    const dcValue = ITD - valueSM;

    return dcValue > 30 ? 30 : dcValue;
  }

  public async toPromptAddAnswerBox(option: Option) {
    try {
      const answerBox2 = await this._storyBoxService.svcPromptCreateNew(
        option.id
      );
      option.answerBoxNegativeId = answerBox2.id;
      option.labelAnswerPositive = this._levelHelperService.models.find((tl) => tl.elementId === option.answerBoxId)?.originalLabel; 
      this._optionService.svcToModify(option);
      this._optionService.toSave();
      this.existOptionBox = undefined;
      // this._change.detectChanges();
    } catch (error) {
      Alert.showError(error);
    }
  }

  public async toAddRoadblock() {
    let roadblock = await this._roadblockService.svcPromptCreateNew(
      this.optionBox.id,
      RoadBlockType.OBTAINED_ITEM
    );
    this.roadblockId = roadblock.id;
    this._roadblockService.srvAdd(roadblock);
    this._change.detectChanges();
    await this.roadBlocksUseds.push(roadblock);
    this.refresh.emit();
  }

  async ngAfterViewInit() {
    await this._roadblockService.toFinishLoading();
    this.roadBlocksUseds =
      this._roadblockService.filterStoryboxForAllRoadblocks(this.optionBox?.id);
    this.refresh.emit();
  }

  public async toPromptAddOption() {
    try {
      let option = await this._optionBoxService.addOption(this.optionBox);
      this.optionBoxes.push(option);
      this.optionId = option.id;
      const answerBox = await this._storyBoxService.svcPromptCreateNew(
        option.id
      );
      this.answerBoxPositive = answerBox;
      await this._storyBoxService.srvAdd(answerBox);
      option.answerBoxId = answerBox.id;
      await this._optionService.svcToModify(option);
      this._change.detectChanges();

      this.timeout2 = setTimeout(() => {
        this._change.detectChanges();
        this.refresh.emit();
      }, 300);
    // Scroll para o novo componente com efeito suave
    setTimeout(() => {
        HighlightElement(option.id, 110, true, 'transparent');
    }, 100);
    
    } catch (error) {
      Alert.showError(error);
    }
    this._change.detectChanges();
    this.refresh.emit();
  }

  public async RemoveOption(option: Option) {
    try {
      if (await Alert.showRemoveAlert('Option: ' + option.message)) {
        this._optionService.svcToRemove(option.id);
        this._optionBoxService.RemoveOption(this.optionBox, option.id);
        this._levelHelperService.svcToRemove(option.answerBoxId);
        this.removePostiveAndNegativeComponent(option.id);

        this._levelHelperService.models.forEach((places) => {
          if (places.elementId === option?.answerBoxId || places.elementId === option?.answerBoxNegativeId) {
            this._levelHelperService.svcToRemove(places.id);
          }
        });

        this._roadblockService.models.forEach((roadblock) => {
          if (roadblock.spokeElementId === option?.answerBoxId || roadblock.spokeElementId === option?.answerBoxNegativeId) {
            roadblock.spokeElementId = undefined;
            this._roadblockService.svcToModify(roadblock);
          }
        });   
        this._userSettingsService.removeIdObjectInformations(option.id);  
        this._settingsComponent.loadPlaces();
        this._storyBoxService.checkSpeechInStorybox(); //Atualiza os storyProgressIds
        this.ngOnInit();
        this.uploadRedirect(option.id);  
      }
    } catch (error) {
      Alert.showError(error);
    }
    this.ngOnInit();
    this._change.detectChanges();
    this.refresh.emit();
  }  
  

  async removePostiveAndNegativeComponent(id: string) {
    
    for (const storyBox of this._storyBoxService.models) {
      if (storyBox.id.includes(id)) {
        this._storyBoxService.svcToRemove(storyBox.id);
      }
    }

    this._speechService.models.forEach((speech) => {
      if (speech.id.includes(id)) {
        this._speechService.svcToRemove(speech.id);
      }
    });
    this._eventService.models.forEach((event) => {
      if (event.id.includes(id)) {
        this._eventService.svcToRemove(event.id);
      }
    });
    this._markerService.models.forEach((marker) => {
      if (marker.id.includes(id)) {
        this._markerService.svcToRemove(marker.id);
      }
    });
    this._roadblockService.models.forEach((roadblock) => {
      if (roadblock.id.includes(id)) {
        this._roadblockService.svcToRemove(roadblock.id);
      }
    });   

  }

  async toRemoveRoadblock(roadblock: RoadBlock) {
    if (await Alert.showRemoveAlert(Typing.typeName(roadblock))) {
      this.roadblockId = undefined;
      this._roadblockService.svcToRemove(roadblock.id);
      this.roadBlocksUseds = this.roadBlocksUseds.filter(
        (rb) => rb.id != roadblock.id
      );
      this._change.detectChanges();
      this.refresh.emit();
      this.ngOnInit();
    }
  }

  async changeLabel(event: Event) {
    const inputElement = event.target as HTMLInputElement;
    const text = inputElement.value.trim();

    const existingLabels = {};
    this.listSpokePlace.forEach((spoke) => {
      existingLabels[spoke.originalLabel] = true;
    });

    const spoke = this.listSpokePlace.find(
      (spoke) => spoke.elementId === this.optionBox.id
    );

    if (text === '') {
      inputElement.value = '<<Label for progress condition>>';
      this.optionBox.label = undefined;
      this._optionBoxService.svcToModify(this.optionBox);
      this._optionBoxService.toSave();

      if (spoke) {  
        this.removeBDLabelPlaces(this.optionBox.id, spoke.id);
       }

       this.refresh.emit();   
    } 
    else {
      if (existingLabels[text]) {
        // Label já existe na base de dados do Places 
        Alert.showError('This Label ALREADY Exists!!', '');

       if(this.optionBox.label === undefined) {
        inputElement.value = '<<Label for progress condition>>';
        this._change.detectChanges();
      } else {
        inputElement.value =this.optionBox.label;
      }
       this.refresh.emit();    
      } 
      else {
        // Atualiza label
        if (spoke) {
          spoke.originalLabel = text;
          spoke.text = '[InvestigationBox] ' + text;
          this._levelHelperService.svcToModify(spoke);
        } 
        else {
          // Adiciona label
          const helper: SpokePlaceHelper = {
            elementId: this.optionBox.id,
            label: text,
            component: '[InvestigationBox]',
            text: '[InvestigationBox] ' + text,
          };
          this._levelHelperService.createNewLevelHelper(helper);
        }
        inputElement.value = text;
        this.optionBox.label = text;
        this._optionBoxService.svcToModify(this.optionBox);           
      }        
    }
  }


  async removeBDLabelPlaces(idAnswerBox: string, idPlaces: string) {    

    await this._roadblockService.models.forEach((roadblock) => {
      if (roadblock.spokeElementId === idAnswerBox) {
        roadblock.spokeElementId = undefined;
        this._roadblockService.svcToModify(roadblock);
      }
    });

    await this._levelHelperService.svcToRemove(idPlaces);     
  }

  async chooseAndOrCondition(event) {
    //AND = FALSE, OR = TRUE.
    if (event.target.checked == false) this.optionBox['AndOrCondition'] = 'AND';
    else this.optionBox['AndOrCondition'] = 'OR';

    await this._optionBoxService.svcToModify(this.optionBox);
    await this._optionBoxService.toSave();
  }
}
