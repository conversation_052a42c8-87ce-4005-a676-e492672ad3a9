import { ChangeDetectorRef, Component } from '@angular/core';
import { Area, Mission } from 'src/app/lib/@bus-tier/models';
import { AreaService } from 'src/app/services/area.service';
import { MissionService } from 'src/app/services/mission.service';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { PopupService } from 'src/app/services/popup.service';
import { Alert, Popup } from 'src/lib/darkcloud';
import { Index } from 'src/lib/others';
import { ReviewService } from 'src/app/services/review.service';
import { EasyMVC } from 'src/lib/darkcloud/angular';
import { TranslationService } from 'src/app/services/translation.service';
import { LanguageService } from 'src/app/services/language.service';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';
import { ObjectiveService } from 'src/app/services/objective.service';
import { EventService } from 'src/app/services';
import { Button } from 'src/app/lib/@pres-tier/data';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-mission-list',
  templateUrl: './mission-list.component.html',
  styleUrls: ['./mission-list.component.scss']
})
export class MissionListComponent extends TranslatableListComponent<Mission> {
  protected override lstFilterParameters: EasyMVC.Filter[] = [{ name: 'areaId' }];
  public missionIdToggle: string;
  public preloadedAreas: Area[] = [];
  public areaNames: Index<string>;
  public missions: Mission[] = [];

  noObjectives: Mission[] = [];
  noAssigned: Mission[] = [];
  multipleAssigned: Mission[] = [];
  singleAssigned: Mission[] = [];

  noMissionAssigned: Mission[] = [];
  multipleMissionAssigned: Mission[] = [];
  singleMissionAssigned: Mission[] = [];
  invertObjectives: number = 1;
  invertMissions: number = 1;
  description: string = '';
  caseSensitive: boolean = false;
  accentSensitive: boolean = false;

  constructor(
    _userSettingsService: UserSettingsService,
    private _areaService: AreaService,
    _activatedRoute: ActivatedRoute,
    private _missionService: MissionService,
    private _objectiveService: ObjectiveService,
    private _popupService: PopupService,
    private _reviewService: ReviewService,
    private _change: ChangeDetectorRef,
    protected override _translationService: TranslationService,
    protected override _languageService: LanguageService,
    private _eventService: EventService,
    private _router: Router,  


  ) {
    super(_missionService, _activatedRoute, _userSettingsService, 'id', _translationService, _languageService);
  }

  public readonly addMissionTemplate: Button.Templateable = {
    title: 'Add a new instance to the list',
    onClick: this.addMission.bind(this),
    iconClass: 'pe-7s-plus',
    btnClass: Button.Klasses.FILL_GREEN,
  };

    public readonly tagsButton: Button.Templateable =
    {
      title: 'Go to the Mission Notes',
      onClick: this.goToMissionNotes.bind(this),
      iconClass: 'pe-7s-file',
      btnClass: Button.Klasses.FILL_BRIGHTBLUE,
    };
  

  override lstInit() {
    this._missionService.toFinishLoading();
   //this.inicializedFieldsReviewed();
    this.preloadAreas();
    this.missionIdToggle = this._activatedRoute.snapshot.fragment;
    this.missions = this._missionService.models;

    this.description = `Showing ${this.missions.length} results`;
  }

  inicializedFieldsReviewed() {
    this._missionService.models.forEach((character) => {
      character.isReviewedName = !!character.isReviewedName;
      character.isReviewedDescription = !!character.isReviewedDescription;
      this._missionService.svcToModify(character);
    });
  }
  

  preloadAreas(): void {
    const areaIds: string[] = [];
    this.areaNames = {};
    this._missionService.models.forEach((mission) => {
      const areaId = mission.areaId;
      if (!areaIds.includes(areaId)) {
        areaIds.push(areaId);
      }
    });
    this.preloadedAreas = this._areaService.svcFilterByIds(areaIds);
  }

  protected override filterItem(mission: Mission) {
    return (
      (this.lstFilterValue['areaId'] as string) === 'ALL' ||
      mission.areaId === (this.lstFilterValue['areaId'] as string)
    );
  }

  async addMission() {
    let addMmission;

    try {
      addMmission = await this._missionService.svcPromptCreateNew();
    }
    catch (e) {
      Alert.showError("This Atributte already exists!");
      return
    }
    if (!addMmission) return;

   if (this.missions.includes(addMmission)) return;
   else await this.lstToAdd(addMmission);

    this.lstResetHighlights();
    this.HighlightElement(addMmission.id, 110, true);
  }

  override lstAfterInitFetchList() {
    this.areaNames = {};
    this._areaService.models.forEach((area) => {
      this.areaNames[area.id] = area.name;
    });
  }

  public xpChanged(mission: Mission, value: string) {
    let amount = 0;
    mission.objectiveIds.forEach(objectiveId => {
      let objective = this._objectiveService.svcFindById(objectiveId);
      if (objective.xp) {
        amount += +objective.xp;
      }
    })
    mission.xp = amount;
    this._missionService.svcToModify(mission);
  }


  // Sobrescrever o método getAreaName para usar os nomes das áreas
  protected override getAreaName(areaId: string): string {
    return this.areaNames[areaId] || '';
  }

  public async toPromptChangeMissionArea(mission: Mission) {
    const selectedAreaButton = await this._popupService.fire<Area, Area>(
      new Popup.Interface(
        {
          title: 'Select Area',
          actionsClass: 'column',
        },
        Popup.toButtonList(
          this._areaService.models,
          {
            parameters: ['hierarchyCode', 'name'],
            mapping: '[<hierarchyCode>]: <name>',
          },
          { undefinedTitle: 'No Area' }
        ),
        {
          hideButton:
          {
            value: this._areaService.svcFindById(Area.getSubIdFrom(mission.id)),
          },
        }
      )
    );
    if (!selectedAreaButton) return;

    mission.areaId = selectedAreaButton.value?.id;
    await this._missionService.svcToModify(mission);
  }

  public async selectXP(mission: Mission) {
    await Alert.showNumberField('Choose xp amount').then(async (e) => {
      if (e.isDismissed) return;

      mission.xp = e.value;
      if (mission.xp <= 0) mission.xp = undefined;

      await this._missionService.svcToModify(mission);
    });
  }

  public downloadMissionOrtography(mission: Mission) {
    this._translationService.getMissionOrtography(mission, true);
  }

  public isMissionTranslated(mission: Mission) {
    return this._translationService.checkTranslation(mission.id, 'EN-US');
  }

  // Implementation of abstract methods for objective sorting capability
  protected override hasObjectiveSortingCapability(): boolean {
    return true; // This component supports objective sorting
  }

  protected override getReferenceService(): any {
    return this._eventService; // Return the event service as reference
  }

  protected override getObjectiveSortingArrays(): {
    noReferences: Mission[],
    noAssigned: Mission[],
    singleAssigned: Mission[],
    multipleAssigned: Mission[],
    result: Mission[]
  } {
    return {
      noReferences: this.noObjectives,
      noAssigned: this.noAssigned,
      singleAssigned: this.singleAssigned,
      multipleAssigned: this.multipleAssigned,
      result: this.missions
    };
  }

  private invertObjectivesCounter = { value: 0 };

  protected override getInvertCounter(): { value: number } {
    // Initialize the counter with current value if not set
    if (this.invertObjectivesCounter.value === 0) {
      this.invertObjectivesCounter.value = this.invertObjectives;
    }
    return this.invertObjectivesCounter;
  }

  protected override updateSortedResults(sortedResults: Mission[]): void {
    this.missions = sortedResults;
    // Update the component's invertObjectives with the new counter value
    this.invertObjectives = this.invertObjectivesCounter.value;
  }

  // Implementation of abstract methods for mission sorting capability
  private invertMissionsCounter = { value: 0 };

  protected override hasMissionSortingCapability(): boolean {
    return true; // This component supports mission sorting
  }

  protected override getMissionSortingArrays(): {
    noAssigned: Mission[],
    singleAssigned: Mission[],
    multipleAssigned: Mission[],
    result: Mission[]
  } {
    return {
      noAssigned: this.noMissionAssigned,
      singleAssigned: this.singleMissionAssigned,
      multipleAssigned: this.multipleMissionAssigned,
      result: this.missions
    };
  }

  protected override getMissionInvertCounter(): { value: number } {
    // Initialize the counter with current value if not set
    if (this.invertMissionsCounter.value === 0) {
      this.invertMissionsCounter.value = this.invertMissions;
    }
    return this.invertMissionsCounter;
  }

  protected override updateMissionSortedResults(sortedResults: Mission[]): void {
    this.missions = sortedResults;
    // Update the component's invertMissions with the new counter value
    this.invertMissions = this.invertMissionsCounter.value;
  }

  // Implementation of abstract methods for mission ID sorting capability
  private sortAscendingFlag = { value: true };

  protected override hasMissionIdSortingCapability(): boolean {
    return true; // This component supports mission ID sorting
  }

  protected override getMissionIdSortAscending(): { value: boolean } {
    return this.sortAscendingFlag;
  }

  protected override getMissionArray(): Mission[] {
    return this.missions;
  }

  protected override updateMissionIdSortedResults(sortedResults: Mission[]): void {
    this.missions = sortedResults;
    // The sortAscending flag is already updated by reference in the generic method
  }


  search(term: string): void {
    this.missions = [];
    this.lstSearchTerm = term;

    for (const model of this._missionService.models)
      if (this.itemMatchesQuery(model, term)) this.missions.push(model);

    if (!term) this.missions = this._missionService.models;

    this.description = `Showing ${this.missions.length} results`;
  }

  itemMatchesQuery(model: Mission, query: string): boolean {
    let nameMatch: boolean = false;
    let descriptionMatch: boolean = false;

    const normalizedQuery = this.accentSensitive ? query.normalize('NFKD').replace(/[\u0300-\u036f]/g, '') : query;
    const name = this.caseSensitive ? model.name : model.name;

    if (this.accentSensitive) {
      if (this.caseSensitive) {
        let localName = name.normalize('NFKD').replace(/[\u0300-\u036f]/g, '');
        nameMatch = localName.includes(normalizedQuery);
      }
      else {
        let localName = name.normalize('NFKD').replace(/[\u0300-\u036f]/g, '');
        nameMatch = localName.toLowerCase().includes(normalizedQuery.toLowerCase());
      }
    }
    else if (this.caseSensitive) {
      let localName = name.normalize('NFKD').replace(/[\u0300-\u036f]/g, '');
      nameMatch = localName.includes(normalizedQuery);
    }
    else nameMatch = name.toLowerCase().trim().includes(normalizedQuery.toLowerCase().trim());

    const description = this.caseSensitive ? model?.description : model?.description?.toLowerCase();
    if (this.accentSensitive) {
      if (this.caseSensitive) {
        let localDescription = description.normalize('NFKD').replace(/[\u0300-\u036f]/g, '');
        descriptionMatch = localDescription.includes(normalizedQuery);
      }
      else {
        let localDescription = description.normalize('NFKD').replace(/[\u0300-\u036f]/g, '');
        descriptionMatch = localDescription.toLowerCase().includes(normalizedQuery.toLowerCase());
      }
    }
    else if (this.caseSensitive) {
      let localDescription = description.normalize('NFKD').replace(/[\u0300-\u036f]/g, '');
      descriptionMatch = localDescription.includes(normalizedQuery);
    }
    else descriptionMatch = description?.includes(normalizedQuery);

    return nameMatch || descriptionMatch;
  }


  async removeMision(mission: Mission) {
    const confirm: boolean = await Alert.showRemoveAlert(mission.name);

    if (confirm) {
      this._missionService.svcToRemove(mission.id);
      this._change.detectChanges();
      return this.lstInit();
    }
    return false;
  }

  searchFilterOptions(event) {
    this.caseSensitive = event.caseSensitive;
    this.accentSensitive = event.accentSensitive;
  }

  changeName(mission: Mission, fieldName, value:string) {
    mission.isReviewedName = false;
    mission.revisionCounterNameAI = 0;
    this.lstOnChange(mission, fieldName, value);
  }

  changeDescription(mission: Mission, fieldName, value:string) {
    mission.isReviewedDescription = false;
    mission.revisionCounterDescriptionAI = 0;
    this.lstOnChange(mission, fieldName, value);
  }

    goToMissionNotes()
  {
    console.log('goToMissionNotes');
    this._router.navigate(['/missionNotes']);
  }

}
