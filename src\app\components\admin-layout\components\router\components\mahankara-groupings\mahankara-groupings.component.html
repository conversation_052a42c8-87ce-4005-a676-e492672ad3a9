<ng-container *ngIf="listGroupings.length > 0">
    <div style="display: flex; flex-direction: row; overflow-x: auto; width: 100%;">
        <table class="table table-list borderList">
            <thead>
                <tr>            
                    <th *ngFor="let title of titles"> {{title}}</th>
                    <th colspan="4">Speech Category</th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let item of listGroupings; let i = index">                                
                        <td class="td-id aligTitle">
                            <input class="background-input-table-color form-control form-short noCursor" placeholder=" " disabled
                                type="text" #index [ngClass]="{'empty-input': !index.value}"
                                [value]="item.indexGrouping" (change)="changeMahankaraValue(i,'indexCategory', index.value)" />
                        </td>
                        <td class="td-id"style="width: 20%;">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #groupings [ngClass]="{'empty-input': !groupings.value}"
                                [value]="item.groupings" (change)="changeMahankaraValue(i,'groupings', groupings.value)" />
                        </td>
                        <td class="td-id" style="width: 45%;">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #description [ngClass]="{'empty-input': !description.value}"
                                [value]="item.description" (change)="changeMahankaraValue(i, 'description', description.value)" />
                        </td> 
                        <td class="td-id" *ngFor="let speech of item.speechCategory; let e = index" style="width: 60px; text-align-last: center;">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #speechCategory [ngClass]="{'empty-input': !speechCategory.value || speech === ''}"
                                [value]="speech" (change)="changeMahankaraValue(i,'speechCategory', speechCategory.value, e)" />
                        </td>
                </tr>
            </tbody>
        </table>
    </div>
</ng-container>


<ng-container *ngIf="listGroupings.length === 0">
    <div class="card" style="text-align: center; padding: 20px;">
        <h3>Empty list. Click to create the list.</h3>
    </div>
</ng-container>
