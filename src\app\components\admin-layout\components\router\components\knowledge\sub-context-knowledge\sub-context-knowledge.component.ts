import { ChangeDetectorRef, Component, EventEmitter, Output } from '@angular/core';
import { SubContextKnowledge } from 'src/app/lib/@bus-tier/models';
import { Button } from 'src/app/lib/@pres-tier/data';
import { KnowledgeService, SubContextKnowledgeService } from 'src/app/services';
import { Alert } from 'src/lib/darkcloud';

@Component({
  selector: 'app-sub-context-knowledge',
  templateUrl: './sub-context-knowledge.component.html',
  styleUrls: ['./sub-context-knowledge.component.scss']
})
export class SubContextKnowledgeComponent {


    @Output() clickBtnKnowledge = new EventEmitter<boolean>();
    titles = ['Knowledge', 'Subcontext', 'Description'];
    listSubContext: SubContextKnowledge[] = [];
    newSubContext: SubContextKnowledge;
    description: string;
    activeLanguage = 'PTBR';
    activeTab: string;
    isSubContext: boolean;
  
    constructor(
      private _subContextKnowledgeService: SubContextKnowledgeService,
      private _knowledgeService: KnowledgeService,
      private ref: ChangeDetectorRef
    ) { }
  
    public readonly excelButtonTemplate: Button.Templateable = {
      title: 'Paste content from excel',
      onClick: this.onExcelPaste.bind(this),
      iconClass: 'excel-icon',
      btnClass: Button.Klasses.FILL_ORANGE,
    };
  
  
    async ngOnInit(): Promise<void> {
      this._subContextKnowledgeService.toFinishLoading();
  
      setTimeout(() => {
        this.listSubContext = this._subContextKnowledgeService.models;
  
        // Itera sobre a lista e remove apenas o valor 'None (Default)' de subContext[0]
        this.listSubContext.forEach((x) => {
          if (x.subContext[0] === 'None (Default)') {
            x.subContext.shift(); // Remove apenas o valor 'None (Default)'
          }
        });
  
        this.description = `Showing ${this.listSubContext.length} results`;
        this.isSubContext = this.listSubContext.length > 0;
      }, 60);
  
    }
  
    async onExcelPaste() {
      try {
        const text = await navigator.clipboard.readText();
        const lines = text.split(/\r?\n/).filter((line) => line);
  
        this._subContextKnowledgeService.models = [];
        this._subContextKnowledgeService.toSave();
  
        for (const line of lines) {
          const values = line.split('\t');
          const knowledge = values[0];
  
          if (values.length !== this.titles.length) {
            Alert.showError(`Error: Number of columns in the imported data does not match the expected (${this.titles.length}).`);
            return;
          }
  
          // Verifica se o atributo existe em this._atributteService.models
          if (knowledge || knowledge !== '') {
            const exists = this._knowledgeService.models.some((model) => model?.knowledge === knowledge);
            if (!exists) {
              Alert.showError(`Error: Knowledge "${knowledge}" not found in the system.`);
              return; // Interrompe o fluxo
            }
  
            this.newSubContext = await this._subContextKnowledgeService.createNewSubContextKnowledge();
            this.newSubContext.subContext = [];
            this.newSubContext.description = [];
  
            this.newSubContext.knowledge = knowledge;
            this.newSubContext.subContext.push(values[1]);
            this.newSubContext.description.push(values[2]);
  
          } else {
            this.newSubContext.subContext.push(values[1]);
            this.newSubContext.description.push(values[2]);
          }
        }
  
        this.listSubContext.push(this.newSubContext);
        const index = this.listSubContext.indexOf(this.newSubContext);
        this._subContextKnowledgeService.svcToModify(this.listSubContext[index]);
  
        this._subContextKnowledgeService.toSave();
        this.ref.detectChanges();
        Alert.ShowSuccess('Subcontext list copied successfully!');
        this.ngOnInit();
      } catch (error) {
        Alert.showError('Error importing data from Excel.');
        console.error(error);
      }
    }
  
  
  
    changeSubContextValue(rowIndex: number, rowSub: number, name: string, newValue: string) {
  
      if (name === 'knowledge') {
        this.listSubContext[rowIndex].knowledge = newValue;
      } else if (name === 'subContext') {
        this.listSubContext[rowIndex].subContext[rowSub] = newValue;
      } else if (name === 'description') {
        this.listSubContext[rowIndex].description[rowSub] = newValue;
      }
      this._subContextKnowledgeService.svcToModify(this.listSubContext[rowIndex]);
    }
  
    btnClickContext() {
      this.clickBtnKnowledge.emit(true);
    }
  

}
