import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { Base } from './Base';

export class ParticleDrop extends Base<Data.Hard.IParticleDrop, Data.Result.IParticleDrop> implements Required<Data.Hard.IParticleDrop>
{
  private static generateId(index: number): string 
  {
    return IdPrefixes.PARTICLE_DROP + index;
  }
  
  constructor(index: number, dataAccess: ParticleDrop['TDataAccess'])
  {
    super(
      {
        hard: 
        {
          id: ParticleDrop.generateId(index),
          amount: undefined
        },
      },
      dataAccess
    );
  }
  protected getInternalFetch() 
  {
    return {};
  }
  public get amount(): number 
  {
    return this.hard.amount;
  }
  public set amount(value: number) 
  {
    this.hard.amount = value;
  }  
  public get type(): string 
  {
    return this.hard.type;
  }
  public set type(value: string) 
  {
    this.hard.type = value;
  }  
}
