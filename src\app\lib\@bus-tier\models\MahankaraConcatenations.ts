import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';

export class MahankaraConcatenations extends Base<Data.Hard.IMahankaraConcatenations, Data.Result.IMahankaraConcatenations> implements Required<Data.Hard.IMahankaraConcatenations>
{
  public static generateId(index: number): string {
    return IdPrefixes.MAHANKARACONCATENATIONS + index;
  }

  constructor( index: number, dataAccess: MahankaraConcatenations['TDataAccess']) 
  {
    super(
    {
      hard: 
      {
        id: MahankaraConcatenations.generateId(index),     
      },
    },
    dataAccess
    );
  }
  protected getInternalFetch() 
  {
    return {};
  }
  public get order(): string
  {
    return this.hard.order;
  }
  public set order(value: string) 
  {
    this.hard.order = value;
  }
  public get concatenations(): string[]
  {
    return this.hard.concatenations;
  }
  public set concatenations(value: string[]) 
  {
    this.hard.concatenations = value;
  }

}
