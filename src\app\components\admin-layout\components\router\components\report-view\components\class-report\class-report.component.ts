import { Component, OnInit } from '@angular/core';
import { Area, Character } from 'src/app/lib/@bus-tier/models';
import { Index, comparable } from 'src/lib/others';
import { LevelService } from 'src/app/services/level.service';
import { ClassService } from 'src/app/services/class.service';
import { AreaService } from 'src/app/services/area.service';
import { CharacterService } from 'src/app/services/character.service';
import { Sorting } from 'src/lib/darkcloud/angular/easy-mvc/Sorting';
import { LevelType } from 'src/lib/darkcloud/dialogue-system';

@Component({
  selector: 'app-class-report',
  templateUrl: './class-report.component.html',
})
/**
 * A component that displays a reports of the classes assigned to
 * MINION and BOSS character types that appears in a level
 */
export class ClassReportComponent implements OnInit {
  LevelType = LevelType;

  /**
   * Order in which the list is sorted
   */
  protected sortingOrder: Sorting.Order = 'ascending';
  /**
   * Parameter by which the list is sorted
   */
  protected sortingParameter: Sorting.Parameter = 'count';

  /**
   * Stores area data
   */
  preloadedAreas: Area[] = [];

  /**
   * Stores information of the classes that appears on a certain type of level
   */
  levelClasses: {
    [levelType: number]: { id: string; count: number }[];
  };

  /**
   * Stores information of the classes that appears on a certain type of level
   * indexed by area id
   */
  levelClassesByArea: Index<{
    [levelType: number]: { id: string; count: number }[];
  }> = {};

  constructor(
    private _levelService: LevelService,
    private _characterService: CharacterService,
    private _classService: ClassService,
    private _areaService: AreaService
  ) {}

  /**
   * Loads area data, fetches the information of the classes of minions and bosses
   * and sorts the list by parameter
   */
  ngOnInit(): void {
    this.preloadedAreas = this._areaService.models;
    this.levelClasses = {
      1: [], // minion level Type
      2: [], // boss level Type
    };

    this.preloadedAreas.forEach((area) => {
      this.levelClassesByArea[area.id] = {
        1: [], // minion level Type
        2: [], // boss level Type
      };
    });

    const indexedCharactersWithClass: Index<Character> = {};
    this._characterService.models
      .filter((character) => character.classId)
      .forEach((character) => {
        indexedCharactersWithClass[character.id] = character;
      });

    this._levelService.models
      .filter(
        (level) =>
          +level.type === LevelType.BOSS || +level.type === LevelType.MINION
      )
      .forEach((level) => {
        level.battleCharacterIds.forEach((battleCharacterId) => {
          /*  if (!indexedCharactersWithClass[battleCharacterId].classId) {
            return;
          } */
          let levelClass = this.levelClasses[+level.type].find(
            (klass) =>
              klass.id === indexedCharactersWithClass[battleCharacterId].classId
          );
          if (levelClass) {
            levelClass.count++;
          } else {
            levelClass = {
              id: indexedCharactersWithClass[battleCharacterId].classId,
              count: 1,
            };
            this.levelClasses[+level.type].push(levelClass);
          }
          let levelClassByArea = this.levelClassesByArea[
            Area.getSubIdFrom(level.id)
          ][+level.type].find(
            (klass) =>
              klass.id === indexedCharactersWithClass[battleCharacterId].classId
          );
          if (levelClassByArea) {
            levelClassByArea.count++;
          } else {
            levelClassByArea = {
              id: indexedCharactersWithClass[battleCharacterId].classId,
              count: 1,
            };
            this.levelClassesByArea[Area.getSubIdFrom(level.id)][
              +level.type
            ].push(levelClassByArea);
          }
        });
      });

    this.sortClassesByParameter(this.sortingParameter);
  }

  /**
   * Sorts the classes information by a parameter and resets the sortingOrder if necessary
   * @param parameter Parameter to sort the list by
   */
  public sortClassesByParameter(parameter: Sorting.Parameter) {
    this.sortingOrder =
      this.sortingParameter === parameter
        ? this.sortingOrder === 'ascending'
          ? 'descending'
          : 'ascending'
        : 'descending';

    this.sortingParameter = parameter;

    this.preloadedAreas.forEach((area) => {
      if (this.levelClassesByArea[area.id][+LevelType.BOSS]) {
        this.sortsClasses(this.levelClassesByArea[area.id][+LevelType.BOSS]);
      }
      if (this.levelClassesByArea[area.id][+LevelType.MINION]) {
        this.sortsClasses(this.levelClassesByArea[area.id][+LevelType.MINION]);
      }
    });

    this.sortsClasses(this.levelClasses[+LevelType.BOSS]);
    this.sortsClasses(this.levelClasses[+LevelType.MINION]);
  }

  /**
   * Sorts the classes information by parameter depending on which parameter it is
   * @param p Parameter to sort the list by
   */
  private sortsClasses(data: { id: string; count: number }[]) {
    switch (this.sortingParameter) {
      case 'name':
        data.sort((a, b) => {
          const ca = this._classService.svcFindById(a.id).name;
          const cb = this._classService.svcFindById(b.id).name;
          return this.sortingOrder === 'descending'
            ? comparable(ca) > comparable(cb) || !ca
              ? 1
              : -1
            : comparable(ca) < comparable(cb) || !cb
            ? 1
            : -1;
        });
        break;
      case 'count':
        data.sort((a, b) => {
          return this.sortingOrder === 'descending'
            ? a.count > b.count
              ? 1
              : -1
            : a.count < b.count
            ? 1
            : -1;
        });
        break;
    }
  }
}
