import { Component, Input, SimpleChanges } from '@angular/core';
import { CastsSigilos, TierList } from 'src/app/lib/@bus-tier/models';
import { CastsSigilosService, TierService } from 'src/app/services';
import { Alert } from 'src/lib/darkcloud';

@Component({
  selector: 'app-sigilos',
  templateUrl: './sigilos.component.html',
  styleUrls: ['./sigilos.component.scss']
})
export class SigilosComponent {


  rarityList: TierList[] = [];
   characterRarityList: string[] = [];
   @Input() copySkillTree: string[];
   isFirstChange = true;
   ListSigilos: CastsSigilos[] = [];
 
   
 
   constructor(
     private _tierListService: TierService,
     private _castsSigilosService: CastsSigilosService
   ) { }
 
   ngOnChanges(changes: SimpleChanges) {
     if (changes['copySkillTree']) {
       if (this.isFirstChange) {
         // Ignorar a primeira alteração no ciclo de vida
         this.isFirstChange = false;
       } else if (this.copySkillTree && this.copySkillTree.length > 0) {
         this.onExcelPaste();
       }
     }
   }
 
   public async ngOnInit() {
     this._tierListService.toFinishLoading();
       if (this.characterRarityList.length === 0) {
         this.characterRarityList = this._tierListService
           .fillRarityArrayDynimically(
             'Character Rarity',
             this.characterRarityList
           )
           .filter((x) => x != 'Inferior');
         this.getListChacterRarity();     
       }
 
       this.ListSigilos = this._castsSigilosService.models;
   }
 
   getListChacterRarity() {
     // Array auxiliar para definir na ordem
     const orderDesired = ['Elementar', 'Comum', 'Raro', 'Épico', 'Lendário'];
     this.characterRarityList.sort(
       (a, b) => orderDesired.indexOf(a) - orderDesired.indexOf(b)
     );
   }

   async onExcelPaste() {
     this._castsSigilosService.models = [];
     this._castsSigilosService.toSave();
     this.ListSigilos = [];
 

     // Verifica se `this.copySkillTree` contém dados
     if (!this.copySkillTree || this.copySkillTree.length === 0) {
       Alert.showError('No data found in the copied Excel content.');
       return this.ngOnInit();
     }
     this.copySkillTree = this.copySkillTree.filter(
       (line) => line.trim() !== ''
     );

     // Separa os headers (1ª linha) e os dados restantes
     const [headers, ...data] = this.copySkillTree;
     const columns = headers.split('\t').map((col) => col.toLowerCase().trim());
 
     // 2ª Validação: Garante que existem pelo menos 3 colunas
     if (columns.length < this.characterRarityList.length) {
       Alert.showError('Excel copied missing columns.');
       return this.ngOnInit();
     }
 
     // **Transforma os valores do cabeçalho esperado em minúsculas**
     const normalizedListHeader = this.characterRarityList.map((header) =>
       header.toLowerCase().trim()
     );
 
     // 3ª Validação: Verifica se os títulos copiados correspondem aos esperados (independentemente da ordem)
     if (!columns.every((col) => normalizedListHeader.includes(col))) {
       Alert.showError(
         `The first line "${columns.join(
           ', '
         )}" does not match the expected header`
       );
       return this.ngOnInit();
     }
 
     // **Criar um mapeamento para a posição de cada coluna com base no cabeçalho copiado**
     const columnIndexMap: { [key: string]: number } = {};
     columns.forEach((col, index) => {
       columnIndexMap[col] = index;
     });
     // cria um novo item vazio para poder gerar a primeira linha da tabela
     this._castsSigilosService.createNewSouls();
 
     // 4ª Validação: Processa as linhas copiadas (a partir da segunda linha)
     for (const line of data) { 
       const values = line.split('\t');
 
       // Garantir que a linha tenha o mesmo número de colunas que o cabeçalho esperado
       if (values.length < normalizedListHeader.length) {
         console.warn('Linha com dados incompletos encontrada:', line);
         continue;
       }
 
       const newItem = await this._castsSigilosService.createNewSouls();

       // Atribuir valores com base na posição correta dos campos copiados
       newItem.elementar = columnIndexMap['elementar'] !== undefined ? values[columnIndexMap['elementar']] : null;
       newItem.comum = columnIndexMap['comum'] !== undefined ? values[columnIndexMap['comum']] : null;
       newItem.raro = columnIndexMap['raro'] !== undefined ? values[columnIndexMap['raro']] : null;
       newItem.epico = columnIndexMap['épico'] !== undefined ? values[columnIndexMap['épico']] : null;
       newItem.lendario = columnIndexMap['lendário'] !== undefined ? values[columnIndexMap['lendário']] : null;
       this._castsSigilosService.svcToModify(newItem);
     }
 
     await this._castsSigilosService.toSave();
     Alert.ShowSuccess('Sigilos imported successfully!');
     this.ngOnInit();
   }
 
   changeGoldenValue(index: number, field: string, value: string) {
     // Altera o campo do item baseado no index
     if (field === 'elementar') {
       this.ListSigilos[index].elementar = value;
     } else if (field === 'comum') {
       this.ListSigilos[index].comum = value;
     } else if (field === 'raro') {
       this.ListSigilos[index].raro = value;
     } else if (field === 'epico') {
       this.ListSigilos[index].epico = value;
     } else if (field === 'lendario') {
       this.ListSigilos[index].lendario = value;
     }
     this._castsSigilosService.svcToModify(this.ListSigilos[index]);
     this._castsSigilosService.toSave();
   }


}
