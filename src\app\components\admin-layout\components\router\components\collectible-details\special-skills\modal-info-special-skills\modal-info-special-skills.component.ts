import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-modal-info-special-skills',
  templateUrl: './modal-info-special-skills.component.html',
  styleUrls: ['./modal-info-special-skills.component.scss']
})
export class ModalInfoSpecialSkillsComponent {

  /**
   * Controla se o modal está visível ou não
   */
  @Input() isModalVisible: boolean = false;

  /**
   * Evento emitido quando o modal deve ser fechado
   */
  @Output() closeModal = new EventEmitter<void>();

  /**
   * Fecha o modal emitindo o evento para o componente pai
   */
  closeModalSpecialSkills(): void {
    this.closeModal.emit();
  }

  /**
   * Fecha o modal quando o usuário sai com o mouse (mouseleave)
   */
  onMouseLeave(): void {
    this.closeModalSpecialSkills();
  }

}
