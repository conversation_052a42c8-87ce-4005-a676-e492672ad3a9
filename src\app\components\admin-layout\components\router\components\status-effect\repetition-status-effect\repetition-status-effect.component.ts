import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { Alert } from '../../../../../../../../lib/darkcloud';
import { Repetition } from '../../../../../../../lib/@bus-tier/models';
import { RepetitionStatusEffectService, TierService } from '../../../../../../../services';


@Component({
  selector: 'app-repetition-status-effect',
  templateUrl: './repetition-status-effect.component.html',
  styleUrls: ['./repetition-status-effect.component.scss']
})
export class RepetitionStatusEffectComponent implements OnChanges, OnInit {
  
  listRepetiton: Repetition[] = [];
  titles = [1, 2, 3, 4, 5, 6];
  characterRarityList: string[] = [];
  @Output() descriptionOutput = new EventEmitter<string>();
  @Input() copyExcelRepetition: string[];
  listExcelRepetition: string[];
  
  // Flag para controle de inicialização do copyExcelRepetition
  private isFirstChange = true;

  constructor(
    private _repetitionStatusEffectService: RepetitionStatusEffectService,
    private _tierListService: TierService,
  ) { }

  async ngOnInit(): Promise<void> {
    this._repetitionStatusEffectService.toFinishLoading();
    this.listRepetiton = this._repetitionStatusEffectService.models;
    this.descriptionOutput.emit(`Showing ${this.listRepetiton.length} results`);

    this._tierListService.toFinishLoading();
    this.characterRarityList = this._tierListService.fillRarityArrayDynimically('Character Rarity', this.characterRarityList).filter(x => x != 'Inferior');
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['copyExcelRepetition']) {
      if (this.isFirstChange) {
        // Ignorar a primeira alteração no ciclo de vida
        this.isFirstChange = false;
      } else if (this.copyExcelRepetition && this.copyExcelRepetition.length > 0) {
        this.onExcelPaste();
      }
    }
  }

  async onExcelPaste() {  

    const invalidValues: string[] = [];
    const processedData: string[][] = [];
  
    if (this.copyExcelRepetition.length > 0) {
      this.copyExcelRepetition.forEach(line => {
        const values = line.split("\t").map(value => value.trim());
     
        this._repetitionStatusEffectService.models = [];
        this._repetitionStatusEffectService.toSave();

        processedData.push(values);

        values.forEach(value => {
          if (value && !this.characterRarityList.includes(value)) {
            invalidValues.push(value);
          }
        });
      });
      
      if (invalidValues.length > 0 && this.copyExcelRepetition.length > 0) {
        Alert.showError(`Invalid values found: ${invalidValues.join(", ")}`);
        return;
      }

      for (let index = 0; index < processedData.length; index++) {
        this._repetitionStatusEffectService.createNewRepetition(processedData[index]);
      }  

      this.copyExcelRepetition = []; 
      Alert.ShowSuccess('Repetition imported successfully!');
      this.ngOnInit();

    }
  }
  
  changeRarityValue(rowIndex: number, colIndex: number, newValue: string) {
    if (!this.characterRarityList.includes(newValue)) {
      Alert.showError("Invalid rarity value!");
      return;
    }

    if (this.listRepetiton[rowIndex]) {
      this.listRepetiton[rowIndex].positionNameRarity[colIndex] = newValue;
      this._repetitionStatusEffectService.svcToModify(this.listRepetiton[rowIndex]);
    }
  }
}
