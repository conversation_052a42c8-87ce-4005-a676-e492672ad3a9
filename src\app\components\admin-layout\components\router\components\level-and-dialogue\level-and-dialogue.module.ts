import { PiecesModule } from './../../../../../pieces/pieces.module';
import { LevelDialogueEditorModule } from './components/level-dialogue-editor/level-dialogue-editor.module';
import { LevelListComponent } from './components/level-list/level-list.component';
import { LevelDialoguesManagerComponent } from './components/level-dialogues-manager/level-dialogues-manager.component';
import { LevelDialogueEditorComponent } from './components/level-dialogue-editor/level-dialogue-editor.component';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { ContentInputModule } from 'src/app/components/content-input/content-input.module';
import { PopupModule } from 'src/app/components/popup/popup.module';
import { BattleCharactersTableModule } from 'src/app/components/battle-characters-table/battle-characters-table.module';
import { LocationPipe } from 'src/app/pipes/location.pipe';
import { RouterModule } from '@angular/router';
import { MajorModule } from 'src/app/major.module';

@NgModule({
  imports: [
    RouterModule,
    CommonModule,
    BrowserModule,
    FormsModule,
    MajorModule,
    LevelDialogueEditorModule,
    ContentInputModule,
    PopupModule,
    BattleCharactersTableModule,
    PiecesModule
  ],
  declarations: [
    LevelListComponent,
    LevelDialoguesManagerComponent
  ],
  exports: [
    LevelListComponent,
    LevelDialogueEditorComponent,
    LevelDialoguesManagerComponent,
  ],
  providers: [
    LocationPipe
  ],
})
export class LevelAndDialogueModule {}
