import { Component, EventEmitter, Output } from '@angular/core';
import { Alert } from '../../../../../../../../../lib/darkcloud';
import { AilmentIdBlocks } from '../../../../../../../../lib/@bus-tier/models/AilmentIdBlocks';
import { Button } from '../../../../../../../../lib/@pres-tier/data';
import { AilmentIdBlockservice } from '../../../../../../../../services';

@Component({
  selector: 'app-ailment-id-blocks',
  templateUrl: './ailment-id-blocks.component.html',
  styleUrls: ['./ailment-id-blocks.component.scss']
})
export class AilmentIdBlocksComponent {
  titles = [1, 2, 3, 4, 5, 6];
  listAilment: AilmentIdBlocks[] = [];
  activeLanguage = 'PTBR';
  @Output() activeTab2 = new EventEmitter<string>();

  public readonly excelButtonTemplate: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };
  constructor(
    private _ailmentIdBlockservice: AilmentIdBlockservice
  ){}


  async ngOnInit(): Promise<void>{

      this._ailmentIdBlockservice.toFinishLoading();
       this._ailmentIdBlockservice.models = this._ailmentIdBlockservice.models.filter(negativeItem => {
        return negativeItem.positionNameAiment.some(value => value !== "" && value !== undefined && value !== null);
      });
      this._ailmentIdBlockservice.toSave();
      this.listAilment = this._ailmentIdBlockservice.models;
    }

    async onExcelPaste() {
      const text = await navigator.clipboard.readText();
      const lines = text.split(/\r?\n/).filter(line => line);    
      const processedData: string[][] = [];
    
      if (lines.length > 0) {
        lines.forEach(line => {
          // Divide cada linha em colunas e remove a primeira coluna
          const values = line.split("\t").map(value => value.trim()).slice(1);
    
          processedData.push(values);
        });
    
        // Verifica se o número de colunas em cada linha corresponde ao número de colunas em this.titles
        const isColumnCountValid = processedData.every(row => row.length === this.titles.length);
    
        if (!isColumnCountValid) {
          Alert.showError('Invalid number of columns');
          return;
        }
  
        this._ailmentIdBlockservice.models = [];
        this._ailmentIdBlockservice.toSave();
    
        for (let index = 0; index < processedData.length; index++) {
          this._ailmentIdBlockservice.createNewAilmentIdBlocks(processedData[index]);
        }    

        Alert.ShowSuccess('Ailment imported successfully!');
        this.activeTab2.emit('ailment');
        this.ngOnInit();
      }
    }
    
 
    changeAilment(rowIndex: number, colIndex: number, newValue: string){

      if (this.listAilment[rowIndex]) {
        this.listAilment[rowIndex].positionNameAiment[colIndex] = newValue;
        this._ailmentIdBlockservice.svcToModify(this.listAilment[rowIndex]);
      }
    }
    
 
}
