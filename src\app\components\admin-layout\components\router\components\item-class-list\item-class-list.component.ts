import { Component} from '@angular/core';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';
import { ItemClass } from 'src/app/lib/@bus-tier/models/ItemClass';
import { Tab } from 'src/app/lib/@bus-tier/models/Tab';
import { Button } from 'src/app/lib/@pres-tier/data';
import { ChestService, CodeBlockDropService, IngredientDropService, ItemService, ParticleDropService, PopupService, ReviewMessage, ReviewService, UserSettingsService } from 'src/app/services';
import { CustomService } from 'src/app/services/custom.service';
import { ItemClassService } from 'src/app/services/item-class.service';
import { LanguageService } from 'src/app/services/language.service';
import { TabService } from 'src/app/services/tab.service';
import { TranslationService } from 'src/app/services/translation.service';
import { Alert, Popup } from 'src/lib/darkcloud';
import { FILTER_SUFFIX_PATH } from 'src/lib/darkcloud/angular/dsadmin/constants/others';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';
import { ItemsSelectorService } from './../../../../../../services/items-selector.service';
import { DropService } from 'src/app/services/drop.service';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-item-class-list',
  templateUrl: './item-class-list.component.html',
  styleUrls: ['./item-class-list.component.scss']
})
export class ItemClassListComponent extends TranslatableListComponent<ItemClass>
{
  invertItemsCountSorting: boolean = false;
  indexTabSorting = 0;
  public itemClasses: ItemClass[] = [];
  public reviewOrderAscending: boolean = false;
  public activeTab: string;
  public custom: Custom;
  public queryValue: string;


  constructor(
    private _itemsSelectorService: ItemsSelectorService,
    public customService: CustomService,
    private _reviewService: ReviewService,
    private _itemClassService: ItemClassService,
    private _itemService: ItemService,
    private _tabService: TabService,
    private _router: Router,
    private _popupService: PopupService,
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    private _chestListService: ChestService,
    protected override _translationService: TranslationService,
    protected override _languageService: LanguageService,
    private _dropService: DropService,
    private _codeBlockDropService: CodeBlockDropService,
    private _ingredientDropService: IngredientDropService,
    private _particleDropService: ParticleDropService
  ) 
  {
    super(_itemClassService, _activatedRoute, _userSettingsService, 'name', _translationService, _languageService);
  }

  override async lstInit()
  {
    await this._itemClassService.toFinishLoading();
   // this.inicializedFieldsReviewed();
    this.custom = await this.customService.svcGetInstance();

    const tab = localStorage.getItem(`tab-ItemClassComponent${FILTER_SUFFIX_PATH}`);
    this.activeTab = tab === 'null' || !tab ? 'itemClass' : tab;

    this.itemClasses = this._itemClassService.models;
    this._itemService.svcReviewAll();
    this.lstIds = this.itemClasses.map(x => x.id);
  }

  inicializedFieldsReviewed() {
    this._itemClassService.models.forEach((character) => {
      character.isReviewedName = !!character.isReviewedName;
      character.isReviewedDescription = !!character.isReviewedDescription;
      this._itemClassService.svcToModify(character);
    });
  }

  public switchToTab(tab: string) 
  {
    this.activeTab = tab;
    localStorage.setItem(`tab-ItemClassComponent${FILTER_SUFFIX_PATH}`, this.activeTab);
  }
  async removeItemClass(itemClass: ItemClass)
  {      
    const confirm = await Alert.showConfirm('Are you sure you want to delete ' + itemClass.name + '?', '', 'Yes');
    if(!confirm) return;

    for(let i = 0; i < this._itemsSelectorService.models.length; i++)
    {
      if(this._itemsSelectorService.models[i].itemsSection.trim() == itemClass.name.trim())
      {
        await this._itemsSelectorService.svcToRemove(this._itemsSelectorService.models[i].id);
        i = i-1;
      }
    }
    this.deleteDrops(itemClass.name, this._dropService);
    this.deleteDrops(itemClass.name, this._ingredientDropService);
    this.deleteDrops(itemClass.name, this._particleDropService);
    this.deleteDrops(itemClass.name, this._codeBlockDropService);

    await this._itemClassService.svcToRemove(itemClass.id);

    let index = this.itemClasses.indexOf(itemClass);
    this.itemClasses.splice(index, 1);
  }

  async deleteDrops(itemClassName, service)
  {
    for(let i = 0; i < this._chestListService.models.length; i++)
    {
      if(this._chestListService.models[i].chestType == itemClassName &&
        this._chestListService.models[i].acronym != undefined &&
        this._chestListService.models[i].acronym != '') 
      {
        for(let j = 0; j < service.models.length; j++)
        {
          if(service.models[j].type == this._chestListService.models[i].acronym)
          {
            await service.svcToRemove(service.models[j].id);
            await service.toSave();
            j = j-1;
          }
        }
      }
    }
  }

  public GateXPItemClassChanged(value: any) 
  {
    this.custom.gateXPclassItem = value.target.value;
    this.customService.svcToModify(this.custom);
  }
  
  public async addTabWithPopup(itemClass: ItemClass)
  {
    let buttons: Popup.Button<Tab>[] = [];
    this._tabService.models.forEach(x => 
    {
      buttons.push(new Popup.Button<Tab>(x.name, x, 'btn btn-fill'));
    });

    const selectedTag = await this._popupService.fire<Tab, Tab>(
      new Popup.Interface({ title: 'Select Tab', actionsClass: 'column'}, buttons));

    if(itemClass.tagIds) itemClass.tagIds.push(selectedTag.value.id);
    else
    {
      itemClass.tagIds = [];
      itemClass.tagIds.push(selectedTag.value.id);
    }

    this._itemClassService.svcToModify(itemClass);
  }

 
  changeTab(itemClass: ItemClass, tabId: string, index: number)
  {
    if(tabId == "") itemClass.tagIds.splice(index, 1);    
    else itemClass.tagIds[index] = tabId;

    this._itemClassService.svcToModify(itemClass);
  }

  public routeToBoundItems(itemClass: ItemClass)
  {
    this._router.navigate(['/boundItems', {id: itemClass.id}]);
  }

  public override readonly addButtonTemplate: Button.Templateable = 
  {
    title: 'Add a new instance to the list',
    onClick: this.addClass.bind(this),
    iconClass: 'pe-7s-plus',
    btnClass: Button.Klasses.FILL_GREEN,
  };

  async addClass()
  {
    let itemClass;
    try 
    {
      itemClass = await this._itemClassService.svcPromptCreateNew();
    } 
    catch (e) 
    {
      Alert.showError("Classe de Item já existe!");
    }
    if(!itemClass) return;

    await this._itemClassService.srvAdd(itemClass);

    if(this.itemClasses.includes(itemClass)) return;    
    else this.itemClasses.push(itemClass);
  }

  public async downloadItemClassOrtography(itemClass: ItemClass)
  {
    this._translationService.getItemClassOrtography(itemClass, true);
  }

  public readonly tagsButton: Button.Templateable = 
  {
    title: 'Go to the Tags List',
    onClick: this.goToTags.bind(this),
    iconClass: 'pe-7s-ticket',
    btnClass: Button.Klasses.FILL_BRIGHTBLUE,
  };

  goToTags()
  {
    this._router.navigate(['/tags']);
  }

  sortByBoundItemsCount()
  {
    this.itemClasses.sort((a,b) => 
    {
      let aLength = a.itemIds.length;
      let bLength = b.itemIds.length;

      return this.invertItemsCountSorting? aLength - bLength : bLength - aLength;
    });

    this.invertItemsCountSorting = !this.invertItemsCountSorting;
  }


   public sortListByReviews() 
   {
    this.reviewOrderAscending = !this.reviewOrderAscending;
    let itemClasses = this._itemClassService.models;

    itemClasses.forEach((itemClass) =>
    {
      itemClass['reviewCount'] = this._reviewService.objsReviews.filter(review => itemClass.itemIds.includes(review.modelId)).length;
    })
    this.itemClasses = itemClasses.sort((a, b) => 
    {
      if (a['reviewCount'] < b['reviewCount']) {return this.reviewOrderAscending? 1:-1;}
      if (a['reviewCount'] > b['reviewCount']) {return this.reviewOrderAscending? -1:1;}
      return 0;
    });
  }

  sortByTabs()
  {
    let tabsIds = this._itemClassService.svcGetTabsUsed(this.itemClasses);

    if(tabsIds.length != 0)
    {
      let searchTabIds = [];
      for(let i = 0; i < tabsIds.length; i++)
      {
        let index = i + this.indexTabSorting;
        if (index >= tabsIds.length) index -= tabsIds.length;
        searchTabIds.push(tabsIds[index]);
      }
      this.itemClasses = this.itemClasses.map(item => 
      {
        let firstTab = 99999;
        if (item.tagIds)
        {
          for (let tabIndex = 0; tabIndex < searchTabIds.length; tabIndex++)
          {
            if(item.tagIds.includes(searchTabIds[tabIndex]))
            {
              firstTab = tabIndex;
              tabIndex = searchTabIds.length;
            }
          }
        }
        item["firsTag"] = firstTab;
        return item;
      }).sort((a, b) => 
      {
        if(a["firsTag"] > b["firsTag"]) return 1;
        else if(a["firsTag"] < b["firsTag"]) return -1;
        return 0;
      })

      this.indexTabSorting++;
      if(this.indexTabSorting >= tabsIds.length) 
      {
        this.indexTabSorting = 0;
      }
    }
  }

  filterList(event: string)
  {
    this.queryValue = event;
  }

  constructToolTipMessage(itemClass: ItemClass, reviewMessages: ReviewMessage[]): string
  {
    if(!reviewMessages) return "Reviews found!";
    
    if(reviewMessages.length == 0) return "No reviews found!";

    let message = "Not Assigned (" + reviewMessages.length + "):\n\n";
    let longMessage = reviewMessages.length > 10;
    if(longMessage) reviewMessages = reviewMessages.slice(0, 10);
    
    for(let i = 0; i < reviewMessages.length; i++)    
      message += this._itemService.svcFindById(reviewMessages[i].modelId).name + "\n";
    
    if(longMessage) message += "...";    
    return message;
  }

  async changeItemclassName(itemClass:ItemClass, itemClassName:string)
  {
    if(this.isItemclassAlreadyExists(itemClassName))
    {
      Alert.showError(`The item class: ${itemClass.name} already exists!`);
      return;
    }
    else
    {
      await this.changeItemsectionOnItemsSelector(itemClass.name, itemClassName);
      itemClass.name = itemClassName;
      itemClass.isReviewedName = false;
      itemClass.revisionCounterNameAI = 0;
      await this._itemClassService.svcToModify(itemClass);
      this.changeItemClassName(itemClass.name, itemClassName);
      this.changeChestChesttype(itemClass.name, itemClassName);
    }
  }

  async changeItemClassName(oldName:string, newName:string)
  {
    for(let i = 0; i < this._itemClassService.models.length; i++)
    {
      if(this._itemClassService.models[i].name == oldName)
      {
        this._itemClassService.models[i].name = newName;
        await this._itemClassService.svcToModify(this._itemClassService.models[i]);
        break;
      }
    }
  }

  changeDescription(itemClass:ItemClass, itemClassDescription, value:string) {
    itemClass.isReviewedDescription = false;
    itemClass.revisionCounterDescriptionAI = 0;
    this.lstOnChange(itemClass, itemClassDescription, value);
  }

  async changeChestChesttype(oldName:string, newName:string)
  {
    for(let i = 0; i < this._chestListService.models.length; i++)
    {
      if(this._chestListService.models[i].chestType == oldName)
      {
        this._chestListService.models[i].chestType = newName;
        await this._chestListService.svcToModify(this._chestListService.models[i]);
      }
    }
  }
 
  isItemclassAlreadyExists(itemClassName:string):boolean
  {
    for(let i = 0; i < this._itemClassService.models.length; i++)
    {
      if(this._itemClassService.models[i].name == itemClassName) return true;      
    }
    return false;
  }

  async changeItemsectionOnItemsSelector(itemClassName:string, newItemClassName:string)
  {
    for(let i = 0; i < this._itemsSelectorService.models.length; i++)
    {
      if(this._itemsSelectorService.models[i].itemsSection.trim() == itemClassName.trim())
      {
        this._itemsSelectorService.models[i].itemsSection = newItemClassName;
        await this._itemsSelectorService.svcToModify(this._itemsSelectorService.models[i]);
      }
    }
  }

}
