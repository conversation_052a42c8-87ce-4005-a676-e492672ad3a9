.key-icon
{
  font-weight: bold !important;
  font-size: 150% !important;
   float: right !important; 
   margin-right: 70px !important;
   margin-bottom: 120px !important;
  display: flex !important;
  align-items:flex-end !important;
   align-content:flex-end !important;

}
.p_endInvest {
  height: 39px;
}

.card_choice {
  background-color: #cdafdb;
}

//Modal
/* Fundo escurecido (backdrop) */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5); /* Ajuste a opacidade aqui */
  z-index: 998; /* <PERSON>e ser menor que o modal */
}

.background-div {	
  position: relative !important;	
}	
.item-overlay
{
    position: absolute;
    //border: 15px solid black;
    height: 100%;
    width: 100%;
    background-color: white;
    z-index: 150;
}

.popup-report
{
    position: fixed;
    height: 70%;
    background-color: white;
    transform: translate(-0%, 20%);
    z-index: 999; /* <PERSON><PERSON> que o backdrop */
    top: 7%;
    margin: auto;
    left: 0;
    right: 0;
    margin-left: auto;
    margin-right: auto;
    margin-top: auto;
    margin-bottom: auto;
}

.c-title {
    display: flex; 
    align-items: center; 
    width: 100%;
    padding: 10px;
}

.btn-close {
  width: 100%;
  display: flex;
  justify-content: end;
}

.btn-reset {
  width: 100%;
  background-color: red; 
  color: white; 
  margin-right: 10px;
  margin-top: 50px;
}

.comp-container {
  display: flex; 
  justify-content: center; 
  margin-bottom: 10px;
}

.title-element {
  margin-top: 5px;
  padding: 10px; 
  text-align: center;
}

.width-container {
  width: 180px; 
  margin-right: 7.5%; 
}

.width-SM {
  width: 240px; 
  margin-right: 7.5%; 
}

.height-scroll {
  width: 250px; 
  height: 300px;
  overflow-x: hidden;
  overflow-y: auto;
}

.li-SM {
  text-align: start; 
  display: flex;
  justify-content: space-between;
}
.ptextItem {
    text-decoration: underline;
    font-weight: 900;
    margin-top: 30px;
}

.height-cap
{
    max-height: 1000px;
}

.submit-button
{
    text-align: center;
    margin-left: auto;
    margin-right: auto;
    width: 60%;
    margin-top: 250px;
}

.total-modal {
    background-color: white; 
    border-radius: 5px; 
    border: 2px solid #555; 
    padding-bottom: 10px;
    padding-left: 20px;
    padding-right: 20px;
}

.total-content {
    overflow-y: auto;
    height: auto;
    scrollbar-width: thin;
    scroll-behavior: auto;    
    max-height: 690px;
}


.c-content-item {
    margin-top: 20px; 
    margin-bottom: 20px; 
    max-height: 230px; 
    display: contents;
}
.scrollable-div
{
    overflow-y: auto;
    height: auto;
    scrollbar-width: thin;
    scroll-behavior: auto;
    max-height: 50%;
 }

.list-header-row
{
  margin-bottom: -20px;
}
.requested-not-assigned	
{	
    vertical-align: middle;	
    width: 35px;	
    height: 35px;	
}

.box {
  width: 180px;
  margin: 20px 0;
  border: 1px solid #ccc; 
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  height: fit-content;
}

.box h3, .box h4 {
  margin: 0;
  padding: 0;
  font-weight: 700;
}

ul {
  list-style-type: none;
  padding: 0;
}

ul li {
  padding: 8px;
  cursor: pointer;
  background-color: white;
  margin-bottom: 5px;
}

ul li:hover {
  background-color: #e6e6e6;
}

.buttons {
  margin-top: 20px;
}

#dcValue {
  font-weight: bold;
  text-align: center;
  padding-bottom: 50px;
}

.ative {
  background-color: #e6e6e6 !important;
}

h1 {
  font-size: 60px;
}

.colorSvg {
  filter: brightness(0) invert(1); /* Deixa o SVG branco */
  width: 50px;
  height: 50px;
}

.margIcon {
  margin: auto;
}

.extrut-card {
  display:flex; 
  flex-direction: row; 
  justify-content: flex-start; 
  gap: 10px; 
  align-content: center;
  align-items: center; 
  align-self: center; 
  margin-left: 10px;
}
.c-modifier {
  display: ruby; 
  text-align: start;
}
.btn-difficultyClass {
  width: 100%;
  background-color: #3472f7; 
  color: white; 
  margin-right: 10px;
}

.cond-card {
  font-weight: 900; 
  font-size: 20px
}
.sub-header {
  display:flex; 
  flex-direction: row; 
  justify-content:space-between; 
  align-items:center
}

.class-hasLabel {
   font-weight: bold;
   font-size: 150%;
   float: right; 
   margin-right: 70px;
   display: flex;
   align-items:flex-end;
   align-content:flex-end;
}

.c-message {
  color:#8c8b8b; 
  font-weight: 100; 
  margin-bottom: 5px;
}

.btn-remove {
  font-size: 2px; 
  margin: 0; 
  padding: 3px 5px
}
.style-icon {
  font-size: 24px; 
  margin: 0; padding: 0
}

hr {
  border-top: 1px solid #FFFFFF;
  margin-right: 300px;
}

.inv-box {
  display: flex;   
  padding-left: 20px; 
  align-items: center; 
}
.inv-input {
  width: 60%;
  margin-left: 60px;
  margin-right: 20px;
}

.toggle-switch {
  display: flex;
  position: relative;
  gap: 7px;
  margin-top: 15px;
}

.toggle-switch input[type="checkbox"] {
  display: none;
}

.switch-label {
  display: block;
  width: 55px;
  height: 25px;
   background-color: #cccccc; /* Cor quando OFF (cinza) */
  border-radius: 15px;
  position: relative;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.switch-label:hover {
   background-color: #cccccc; /* Cor quando OFF (cinza) */
}

.switch-text {
  position: absolute;
  left: 8px;
  top: 55%;
  transform: translateY(-50%);
  color: white;
  font-size: 11px;
  font-weight: bold;
  transition: opacity 0.3s ease;
}

.switch-slider {
  position: absolute;
  right: 0px;
  width: 24px;
  height: 24px;
  background-color: white;
  border-radius: 50%;
  transition: transform 0.3s ease;
}

/* Estilos quando o toggle está ON (checked) */
input[type="checkbox"]:checked + .switch-label {
  background-color: #87cb16; /* Cor azul/verde quando ON */
}

input[type="checkbox"]:checked + .switch-label:hover {
  background-color: #87cb16; /* Hover quando ON */
}

input[type="checkbox"]:checked + .switch-label .switch-slider {
  transform: translateX(-30px);
}

input[type="checkbox"]:checked + .switch-label .switch-text {
  left: auto;
  right: 8px;
}

#explain:hover {
  color: red;
    cursor: pointer;
}

/* Developer Note Styles */
.developer-note {
  background-color: #2c3e50;
  color: #ecf0f1;
  border-radius: 8px;
  padding: 15px;
  margin: 15px 20px;
  border-left: 4px solid #e74c3c;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: fit-content;
  max-width: 600px;
}

.note-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-size: 16px;
  color: #ecf0f1;
}

.note-icon {
  margin-right: 8px;
  color: #e74c3c;
  font-size: 18px;
}

.note-content {
  font-size: 14px;
  line-height: 1.4;
}

.note-content p {
  margin: 5px 0;
  color: #bdc3c7;
}

.note-content p strong {
  color: #ecf0f1;
}