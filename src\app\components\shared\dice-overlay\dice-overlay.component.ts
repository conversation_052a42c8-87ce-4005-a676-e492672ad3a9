import { Component, On<PERSON>ni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectorRef } from '@angular/core';
import { Subscription } from 'rxjs';
import { DiceOverlayService, DiceOverlayState } from '../../../services/dice-overlay.service';

/**
 * Dice overlay component that displays the 3D dice rolling interface.
 * Implements a Baldur's Gate 3 style dice rolling experience with:
 * - 3D animated d20 dice with clickable interaction
 * - Success/failure result display with critical hit/miss detection
 * - Ornate fantasy-themed UI frame with decorative borders
 * - Smooth animations and visual feedback
 */
@Component({
  selector: 'app-dice-overlay',
  templateUrl: './dice-overlay.component.html',
  styleUrls: ['./dice-overlay.component.scss']
})
export class DiceOverlayComponent implements OnInit, OnDestroy {
  // Current state of the dice overlay received from service
  public overlayState: DiceOverlayState = {
    isVisible: false,
    result: null,
    dc: 10,
    modifiers: []
  };

  // Animation state tracking for dice behavior
  public isRolling: boolean = false;  // True during dice roll animation
  public hasRolled: boolean = false;  // True after dice has been rolled
  public diceTransform: string = 'rotateX(0deg) rotateY(0deg) rotateZ(0deg)'; // 3D transform for dice

  // Subscription management
  private subscription: Subscription = new Subscription();
  private rollAnimationTimeout?: number; // Timeout for roll animation completion

  constructor(
    private diceOverlayService: DiceOverlayService,
    private cdr: ChangeDetectorRef
  ) { }

  /**
   * Initialize component and subscribe to dice overlay state changes
   */
  ngOnInit(): void {
    // Subscribe to overlay state changes from the service
    this.subscription = this.diceOverlayService.overlayState$.subscribe(
      state => {
        const wasVisible = this.overlayState.isVisible;
        console.log('Dice overlay component received state update:', {
          state,
          wasVisible,
          currentHasRolled: this.hasRolled,
          currentIsRolling: this.isRolling
        });

        this.overlayState = state;

        // Reset animation states when starting a new dice roll
        if (state.isVisible && !wasVisible) {
          console.log('New dice roll started - resetting dice state');
          this.resetDiceState();
        }

        // Setup dynamic animation when roll result is received during rolling
        if (state.result && this.isRolling && !this.hasRolled) {
          console.log('Received dice result while rolling - setting up target rotation for face:', state.result.roll);
          this.setupDynamicAnimation(state.result.roll);
        }

        // Force change detection to update the DOM
        this.cdr.detectChanges();
      }
    );
  }

  /**
   * Clean up subscriptions and timeouts when component is destroyed
   */
  ngOnDestroy(): void {
    this.subscription.unsubscribe();
    if (this.rollAnimationTimeout) {
      clearTimeout(this.rollAnimationTimeout);
    }
  }

  /**
   * Handle clicks on the overlay background to dismiss after roll completion
   * @param event Click event on the overlay background
   */
  onOverlayClick(event: Event): void {
    console.log('Dice overlay clicked:', { hasRolled: this.hasRolled, result: this.overlayState.result, isRolling: this.isRolling });

    // Only allow dismissal after the roll is complete and result is shown
    if (this.hasRolled && this.overlayState.result) {
      console.log('Hiding dice overlay...');
      this.diceOverlayService.hideOverlay();
    }
  }

  /**
   * Handle clicks on the dice to initiate rolling
   * @param event Click event on the dice element
   */
  onDiceClick(event: Event): void {
    event.stopPropagation(); // Prevent triggering overlay click
    console.log('Dice clicked:', { isRolling: this.isRolling, hasRolled: this.hasRolled });

    // Only allow rolling if dice hasn't been rolled yet
    if (!this.isRolling && !this.hasRolled) {
      console.log('Starting dice roll...');
      this.startDiceRoll();
    }
    // Allow dismissal by clicking dice after roll is complete
    else if (this.hasRolled && this.overlayState.result) {
      console.log('Hiding dice overlay from dice click...');
      this.diceOverlayService.hideOverlay();
    }
  }

  /**
   * Reset dice to initial state for a new roll
   */
  private resetDiceState(): void {
    this.isRolling = false;
    this.hasRolled = false;
    this.diceTransform = 'rotateX(0deg) rotateY(0deg) rotateZ(0deg)';

    // Clear any existing animation timeout
    if (this.rollAnimationTimeout) {
      clearTimeout(this.rollAnimationTimeout);
      this.rollAnimationTimeout = undefined;
    }
  }

  /**
   * Initiate the dice rolling process
   * Triggers the service to calculate the roll result
   */
  private startDiceRoll(): void {
    this.isRolling = true;

    // Execute the actual dice roll calculation in the service
    // The animation setup will happen when the result is received in the subscription
    this.diceOverlayService.executeDiceRoll();

    // Set a timeout to finish the roll after the animation completes (like CodePen)
    // This ensures the rolling animation plays for the full duration before showing result
    this.rollAnimationTimeout = window.setTimeout(() => {
      console.log('Roll animation timeout completed - finishing dice roll');
      this.finishDiceRoll();
      // Force change detection to update the DOM immediately
      this.cdr.detectChanges();
    }, 1200); // Match the CSS animation duration
  }

  /**
   * Setup the dynamic animation CSS variables to end on the correct face
   */
  private setupDynamicAnimation(targetFace: number): void {
    const targetRotation = this.getTargetRotationForFace(targetFace);

    // Generate natural, varied rotation values for tumbling effect
    // Each axis rotates at different speeds and directions for realistic motion
    const generateRandomRotation = (base: number, variance: number) =>
      base + (Math.random() - 0.5) * variance;

    // Create 15 keyframes with varied, natural rotations for optimized performance
    const rotations = {
      x1: generateRandomRotation(80, 50),    y1: generateRandomRotation(120, 60),   z1: generateRandomRotation(60, 40),
      x2: generateRandomRotation(180, 70),   y2: generateRandomRotation(240, 80),   z2: generateRandomRotation(140, 60),
      x3: generateRandomRotation(140, 60),   y3: generateRandomRotation(320, 90),   z3: generateRandomRotation(200, 70),
      x4: generateRandomRotation(280, 80),   y4: generateRandomRotation(180, 70),   z4: generateRandomRotation(120, 50),
      x5: generateRandomRotation(220, 70),   y5: generateRandomRotation(400, 100),  z5: generateRandomRotation(280, 80),
      x6: generateRandomRotation(360, 90),   y6: generateRandomRotation(280, 80),   z6: generateRandomRotation(180, 60),
      x7: generateRandomRotation(320, 80),   y7: generateRandomRotation(480, 110),  z7: generateRandomRotation(340, 90),
      x8: generateRandomRotation(460, 100),  y8: generateRandomRotation(360, 90),   z8: generateRandomRotation(240, 70),
      x9: generateRandomRotation(400, 90),   y9: generateRandomRotation(560, 120),  z9: generateRandomRotation(400, 100),
      x10: generateRandomRotation(540, 110), y10: generateRandomRotation(440, 100), z10: generateRandomRotation(300, 80),
      x11: generateRandomRotation(480, 100), y11: generateRandomRotation(640, 130), z11: generateRandomRotation(460, 110),
      x12: generateRandomRotation(620, 120), y12: generateRandomRotation(520, 110), z12: generateRandomRotation(360, 90),
      x13: generateRandomRotation(560, 110), y13: generateRandomRotation(720, 140), z13: generateRandomRotation(520, 120),
      x14: generateRandomRotation(700, 130), y14: generateRandomRotation(600, 120), z14: generateRandomRotation(420, 100),
      x15: generateRandomRotation(640, 120), y15: generateRandomRotation(800, 150), z15: generateRandomRotation(580, 130)
    };

    // Set CSS custom properties on the dice element
    const diceElement = document.querySelector('.dice-3d') as HTMLElement;
    if (diceElement) {
      // Set all 15 keyframe rotations for optimized, natural tumbling
      for (let i = 1; i <= 15; i++) {
        const xKey = `x${i}` as keyof typeof rotations;
        const yKey = `y${i}` as keyof typeof rotations;
        const zKey = `z${i}` as keyof typeof rotations;

        diceElement.style.setProperty(`--spin-x-${i}`, `${rotations[xKey]}deg`);
        diceElement.style.setProperty(`--spin-y-${i}`, `${rotations[yKey]}deg`);
        diceElement.style.setProperty(`--spin-z-${i}`, `${rotations[zKey]}deg`);
      }

      // Set final target rotation
      diceElement.style.setProperty('--target-x', `${targetRotation.rotateX}deg`);
      diceElement.style.setProperty('--target-y', `${targetRotation.rotateY}deg`);
      diceElement.style.setProperty('--target-z', `${targetRotation.rotateZ}deg`);

      console.log('Natural tumbling animation setup for face', targetFace, ':', {
        targetRotation,
        sampleRotations: { x1: rotations.x1, y1: rotations.y1, z1: rotations.z1 },
        finalTarget: `rotateX(${targetRotation.rotateX}deg) rotateY(${targetRotation.rotateY}deg) rotateZ(${targetRotation.rotateZ}deg)`
      });
    }
  }

  /**
   * Get the face number to display on the dice
   * Returns the roll result when rolling is complete (not during rolling animation)
   */
  public getFaceNumber(): string | null {
    // Show face number when rolling animation is complete (not during rolling)
    const faceNumber = !this.isRolling && this.overlayState.result?.roll ? this.overlayState.result.roll.toString() : null;
    console.log('getFaceNumber called:', {
      isRolling: this.isRolling,
      hasRolled: this.hasRolled,
      result: this.overlayState.result,
      roll: this.overlayState.result?.roll,
      faceNumber
    });
    return faceNumber;
  }

  /**
   * Calculate the target rotation for a specific dice face
   * Based on the CSS face positioning logic
   */
  private getTargetRotationForFace(faceNumber: number): { rotateX: number, rotateY: number, rotateZ: number } {
    // SCSS variables converted to degrees
    const angle = 53; // $angle
    const ringAngle = -11; // $ringAngle
    const sideAngle = 72; // $sideAngle (360deg / 5)

    // Face positioning logic matching the CSS
    if (faceNumber >= 1 && faceNumber <= 5) {
      // Top pentagon ring (faces 1-5)
      return {
        rotateX: -angle,
        rotateY: sideAngle * (faceNumber - 1),
        rotateZ: 0
      };
    } else if (faceNumber >= 6 && faceNumber <= 10) {
      // Upper middle ring (faces 6-10)
      return {
        rotateX: ringAngle,
        rotateY: sideAngle * (faceNumber - 6),
        rotateZ: 180
      };
    } else if (faceNumber >= 11 && faceNumber <= 15) {
      // Lower middle ring (faces 11-15)
      return {
        rotateX: ringAngle,
        rotateY: -sideAngle * (faceNumber - 11) - sideAngle/2,
        rotateZ: 0
      };
    } else if (faceNumber >= 16 && faceNumber <= 20) {
      // Bottom pentagon ring (faces 16-20)
      const index = faceNumber === 20 ? 0 : faceNumber - 15; // 20 maps to 0, others to 1-4
      return {
        rotateX: -angle + 180,
        rotateY: -sideAngle * index,
        rotateZ: 0
      };
    }

    // Default fallback
    return { rotateX: 0, rotateY: 0, rotateZ: 0 };
  }

  /**
   * Complete the dice roll animation and display the result
   */
  private finishDiceRoll(): void {
    this.isRolling = false;
    this.hasRolled = true;

    console.log('Dice roll finished:', {
      result: this.overlayState.result,
      hasRolled: this.hasRolled,
      isRolling: this.isRolling,
      rollNumber: this.overlayState.result?.roll,
      overlayState: this.overlayState
    });

    // Force change detection and check DOM
    setTimeout(() => {
      const diceElement = document.querySelector('.dice-3d');
      const dataFace = diceElement?.getAttribute('data-face');
      console.log('DOM check after finish:', {
        diceElement,
        dataFace,
        hasRolled: this.hasRolled,
        resultRoll: this.overlayState.result?.roll
      });
    }, 100);
  }

  /**
   * Get 3D rotation transform for displaying a specific dice face
   * Maps dice roll numbers (1-20) to 3D CSS transforms for proper face orientation
   * @param number The dice roll result (1-20)
   * @returns CSS transform string for 3D rotation
   */
  private getDiceRotationForNumber(number: number): string {
    // Rotation mapping for each face of the d20 dice
    // Each rotation positions the dice to show the corresponding number face-up
    const rotations: { [key: number]: string } = {
      1: 'rotateX(0deg) rotateY(0deg) rotateZ(0deg)',        // Default position
      2: 'rotateX(90deg) rotateY(0deg) rotateZ(0deg)',       // Rotate around X-axis
      3: 'rotateX(180deg) rotateY(0deg) rotateZ(0deg)',      // 180° X rotation
      4: 'rotateX(270deg) rotateY(0deg) rotateZ(0deg)',      // 270° X rotation
      5: 'rotateX(0deg) rotateY(90deg) rotateZ(0deg)',       // Rotate around Y-axis
      6: 'rotateX(0deg) rotateY(180deg) rotateZ(0deg)',      // 180° Y rotation
      7: 'rotateX(0deg) rotateY(270deg) rotateZ(0deg)',      // 270° Y rotation
      8: 'rotateX(90deg) rotateY(90deg) rotateZ(0deg)',      // Combined X+Y rotation
      9: 'rotateX(90deg) rotateY(180deg) rotateZ(0deg)',     // X+Y rotation variant
      10: 'rotateX(90deg) rotateY(270deg) rotateZ(0deg)',    // X+Y rotation variant
      11: 'rotateX(180deg) rotateY(90deg) rotateZ(0deg)',    // X+Y rotation variant
      12: 'rotateX(180deg) rotateY(180deg) rotateZ(0deg)',   // X+Y rotation variant
      13: 'rotateX(180deg) rotateY(270deg) rotateZ(0deg)',   // X+Y rotation variant
      14: 'rotateX(270deg) rotateY(90deg) rotateZ(0deg)',    // X+Y rotation variant
      15: 'rotateX(270deg) rotateY(180deg) rotateZ(0deg)',   // X+Y rotation variant
      16: 'rotateX(270deg) rotateY(270deg) rotateZ(0deg)',   // X+Y rotation variant
      17: 'rotateX(45deg) rotateY(45deg) rotateZ(45deg)',    // Diagonal faces
      18: 'rotateX(135deg) rotateY(45deg) rotateZ(45deg)',   // Diagonal faces
      19: 'rotateX(225deg) rotateY(45deg) rotateZ(45deg)',   // Diagonal faces
      20: 'rotateX(315deg) rotateY(45deg) rotateZ(45deg)'    // Diagonal faces
    };

    // Return the rotation for the number, or default position if not found
    return rotations[number] || 'rotateX(0deg) rotateY(0deg) rotateZ(0deg)';
  }
}
