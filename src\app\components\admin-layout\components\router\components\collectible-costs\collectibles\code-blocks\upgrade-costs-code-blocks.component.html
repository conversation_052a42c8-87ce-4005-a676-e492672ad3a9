<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons [cardTitle]="'Code Blocks'" [cardDescription]="cardDescription"
          [rightButtonTemplates]="[excelButtonTemplate]">
        </app-header-with-buttons>
      </div>
    </div>
    <!--List-->
    <div>
      <ng-container *ngIf="collectibles.length > 0 && tierList.length > 0">
        <table class="table-bordered">
          <thead>
            <tr>
              <th rowspan="3" class="th-clickable" style="color:aliceblue; font-weight: 400;height: 100px;"
                (click)="sortListByParameter('bossLevel')">
                Boss Level (BL)
              </th>
              <th [attr.colspan]="characterRarityList.length" style="text-align: center; color: white;">
                TOTAL CODE BLOCKS / COLLECTIBLE RARITY
              </th>
            </tr>

            <tr>
              <th class="my-td" *ngFor="let header of characterRarityList"
                [style.background-color]="(header | tierColor: 'Character Rarity')+ '!important'"
                style="color:aliceblue; font-weight: 500;border: 1px solid #545353;">
                {{ header }}
              </th>
            </tr>

            <ng-container *ngFor="let title of tierList">
              <th colspan="2" *ngIf="title == 'Bloco de Código Comum'" (click)="sortListByParameter(title)"
                [style.background-color]="(title | tierColor: 'Code Block Rarity')+ '!important'" class="my-td"
                style="color:aliceblue; font-weight: 500;border: 1px solid #545353;">
                {{ title }}
              </th>
              <th *ngIf="title != 'Bloco de Código Comum'" (click)="sortListByParameter(title)"
                [style.background-color]="(title | tierColor: 'Code Block Rarity')+ '!important'" class="my-td"
                style="color:aliceblue; font-weight: 500;border: 1px solid #545353;">
                {{ title }}
              </th>
            </ng-container>

            <tr>

            </tr>
          </thead>

          <tbody>
            <ng-container *ngFor="let codeDrop of collectibles">
              <tr id="{{ codeDrop.id }}">
                <td class="td-id other-td">{{ codeDrop.bossLevel }}</td>
                <td *ngFor="let header of characterRarityList" class="other-td">
                  <input placeholder=" " style="border-style:solid; width: 95%" type="number"
                    [value]="codeDrop.hard[header.toLowerCase() +'CodeBlocksAmount']" #inputField
                    [ngClass]="{'empty-input': !inputField.value}"
                    (change)="changeBlockDrops(codeDrop, inputField.value, header.toLowerCase() +'CodeBlocksAmount')" />
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>

      </ng-container>

      <ng-container *ngIf="collectibles.length === 0 || tierList.length === 0">
        <div class="card padList" style="text-align: center;">
          <h4 *ngIf="collectibles.length === 0">Empty Code Blocks List.</h4>
          <h4 *ngIf="tierList.length === 0">List of Code Block Rarity is empty.</h4>
        </div>
      </ng-container>

    </div>
  </div>
</div>