<div class="main-content">
    <div class="container-fluid">
        <!--Header-->
        <div class="list-header-row update">
            <div class="card">
                <div class="card-header-content"
                     style="position: absolute; top: 10%;">
                    <h3 class="title">Class Modifier List</h3>
                    <p style="width:60vw;" class="category">{{ description}}</p>
                </div>
                <div style="display: contents;">
                    <div
                    style="width: max-content; margin-right: 105px; float: right;">
                   <p class="category" style="align-self:center">Relic Uses – Item Class</p>
                   <select (change)="GateXPItemClassChanged($event)">
                       <option *ngFor="let itemClass of itemClasses"
                               [value]="itemClass?.id"
                               [selected]="itemClass?.id == 'IC25'">
                           {{ itemClass?.name }}
                       </option>
                   </select>
               </div>
               <div style="position: absolute; margin-top: 57px; right: 5px">
                   <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'"
                                     class="add-buttons"
                                     [buttonTemplates]="[excelButtonTemplate]">
                   </app-button-group>
               </div>
                </div>
            </div>
        </div>

        <!--list modifier-->
        <div>
            <table class="table-bordered">
                <thead>
                    <tr>
                        <th rowspan="2" class="order">
                            Ordem
                        </th>
                        <th rowspan="2"
                            style="color:aliceblue; font-weight: 400;height: 100px; width: 10%; border: 2px gray solid !important;">
                            Classe
                        </th>
                        <th style="background-color: #595959 !important; border-left: 3px #555 solid !important;"
                            [attr.colspan]="atributteList.length">
                            <h5>CLASS MODIFIER (CM)</h5>
                        </th>
                        <th style="background-color: #595959 !important;"
                            [attr.colspan]="conditionFields.length">
                            <h5>RELIC BONUS</h5>
                        </th>
                    </tr>
                    <tr>
                        <ng-container *ngIf="atributteList.length > 0">
                            <th class="my-td"
                                style="color:aliceblue; font-weight: 500;border: 2px solid #545353; border-left: 3px #555 solid !important;"
                                *ngFor="let header of atributteList; let i = index;">
                                {{header.atributte}}
                            </th>
                        </ng-container>
                        <th style="color:aliceblue; font-weight: 500;border: 2px solid #545353;"
                            *ngFor="let point of conditionFields; let i = index;">
                            {{point}}
                        </th>

                        <ng-container *ngIf="atributteList.length == 0">
                            <th>
                                <h3>No attributes</h3>
                            </th>
                        </ng-container>
                    </tr>

                </thead>
                <ng-container *ngIf="classes.length > 0">
                    <tbody>
                        <ng-container
                                      *ngFor="let classList of classes; let i = index; trackBy: trackById">
                            <tr id="{{ classList.id }}">
                                <td class="other-td td-sort">{{ i+1 }}</td>
                                <td class="other-td">{{(classList.name)}}</td>
                                <ng-container *ngIf="atributteList.length > 0">
                                    <td class="other-td" style="width: 10%;"
                                        *ngFor="let Atrib of atributteList; let i = index;">
                                        <input type="number"
                                               class="inputModif background-input-table-color"
                                               #InputClassModifier
                                               placeholder=" "
                                               (change)="changeClassAmount(InputClassModifier.value, classList, i)"
                                               [value]="classList.amountClass ? classList.amountClass[i] : ''">
                                    </td>
                                </ng-container>
                                <td class="other-td textAlig">
                                    {{classList.nameItem}}</td>
                                <td class="other-td textAlig">
                                    {{classList.descriptionItem}}</td>

                            </tr>
                        </ng-container>
                    </tbody>
                </ng-container>

            </table>
        </div>
    </div>

</div>