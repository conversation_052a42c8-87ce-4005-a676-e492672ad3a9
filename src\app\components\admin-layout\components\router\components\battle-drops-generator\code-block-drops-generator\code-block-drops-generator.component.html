<div style="margin-top: 20px">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons [cardTitle]="listName" [cardDescription]="''"
          [rightButtonTemplates]="[excelButtonTemplate]">
        </app-header-with-buttons>
      </div>
    </div>
    <div>
      <ng-container *ngIf="codeBlocksList.length > 0 && tierList.length > 0">
        <table class="table-list">
          <thead class="sticky">
            <tr>
              <th class="th-clickable" (click)="sortListByParameter('type')">Type</th>
              <td class="my-td" (click)="sortListByParameter(header)"
                [style.background-color]="(header | tierColor: 'Code Block Rarity')"
                *ngFor="let header of tierList; let i = index;">
                {{ header}}
              </td>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let codeDrop of codeBlocksList">
              <tr id="{{ codeDrop.id }}">
                <td class="td-id other-td">{{ codeDrop.type }}</td>
                <td *ngFor="let header of tierList; let i = index" class="other-td">
                  <input placeholder=" " style="border-style:solid; padding-left: 8px; width: 90%;" type="number"
                    [value]="codeDrop.hard[probability ? header?.toLowerCase() +'Probability' : header?.toLowerCase() +'Numeric']"
                    #inputField [ngClass]="{'empty-input': !inputField.value}"
                    (change)="changeBlockDrops(codeDrop, inputField.value, probability ? header?.toLowerCase()+'Probability' : header?.toLowerCase()+'Numeric')" />
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </ng-container>


      <ng-container *ngIf="codeBlocksList.length === 0 && tierList.length > 0">
        <div style="text-align: center;">
          <h4>Empty List</h4>
        </div>
      </ng-container>

      <ng-container *ngIf="codeBlocksList.length > 0 && tierList.length === 0">
        <div style="text-align: center;">
          <h4>Empty Code Block Rarity list.</h4>
        </div>
      </ng-container>
    </div>
  </div>
