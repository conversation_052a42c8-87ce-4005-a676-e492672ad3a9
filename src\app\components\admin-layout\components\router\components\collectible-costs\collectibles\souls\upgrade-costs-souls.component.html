<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons 
          [cardTitle]="'Ascension Cost Souls'"
          [cardDescription]="cardDescription"
          [rightButtonTemplates]="[excelButtonTemplate]">
        </app-header-with-buttons>
      </div>
    </div>
    <!--List-->
    <div class="card">
      <table class="table table-list">
          <thead>
            <tr>
              <th colspan="5" style="font-size: 20px" class="dark-gray">Boss Ascension</th>
            </tr>
            <tr>
              <th rowspan="2" class="dark-gray">Collectible Rarity</th>
              <th class="dark-gray"></th>
              <th colspan="2" class="dark-gray">Talents</th>
              <th class="dark-gray">Souls</th>
            </tr>
            <tr>
              <th (click)="sortListByParameter('collectibleLevel')" id="base" class="light-gray th-clickable">Order</th>
              <th (click)="sortListByParameter('baseSouls')" id="base" class="light-gray th-clickable">Base</th>
              <th (click)="sortListByParameter('ascensionSouls')" id="base" class="light-gray th-clickable">Ascension</th>
              <th (click)="sortListByParameter('costSouls')" class="light-gray th-clickable">Cost</th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let codeDrop of this.soulsList">
              <tr id="{{ codeDrop.id }}">
                <td [ngStyle]="{'background-color': codeDrop.typeSouls | tierColor: 'Code Block Rarity', 'color':'white', 'font-weight': '500'}">
                    {{ codeDrop.typeSouls }}
                </td>
                <td class="td-id">{{ codeDrop.collectibleLevel }}</td>
                <td *ngFor="let header of tableHeaderNames;">
                  <input
                    placeholder=" "
                    style="border-style:solid;"
                    type="number"
                    [value]="codeDrop.hard[header]"
                    #inputField
                    [ngClass]="{'empty-input': !inputField.value}"
                    (change)="changeBlockDrops(codeDrop, inputField.value, header)" />
                </td>
              </tr>
            </ng-container>
          </tbody>
      </table>
    </div>
  </div>
</div>
