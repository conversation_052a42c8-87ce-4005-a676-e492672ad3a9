import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Knowledge } from 'src/app/lib/@bus-tier/models';
import { Button } from 'src/app/lib/@pres-tier/data';
import { KnowledgeService, UserSettingsService } from 'src/app/services';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { Alert } from 'src/lib/darkcloud';
import { FILTER_SUFFIX_PATH } from 'src/lib/darkcloud/angular/dsadmin/constants/others';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';
import { language } from 'src/lib/darkcloud/dialogue-system/game-types';

@Component({
  selector: 'app-knowledge',
  templateUrl: './knowledge.component.html',
  styleUrls: ['./knowledge.component.scss']
})
export class KnowledgeComponent extends TranslatableListComponent<Knowledge>  {

  constructor(
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    private _knowledgeService: KnowledgeService,
    protected override _translationService: TranslationService,
    protected override _languageService: LanguageService,
  ) {
    super(_knowledgeService, _activatedRoute, _userSettingsService, 'name', _translationService, _languageService);
  }

  language: language = 'PT-BR';
  knowledgeClasses: Knowledge[] = [];
  activeTab: string;
  description: string;
  btnSubContext: string = FILTER_SUFFIX_PATH;
  isTab = false;
  listExcelSubContext: string[] = [];

  public readonly statusTemplate: Button.Templateable =
    {
      title: 'Add a new instance to the list',
      onClick: this.addStatus.bind(this),
      iconClass: 'pe-7s-plus',
      btnClass: Button.Klasses.FILL_GREEN,
    };

  async addStatus() {
    let knowledge;

    try {
      knowledge = await this._knowledgeService.svcPromptCreateNewKnowledge();
    }
    catch (e) {
      Alert.showError("This Knowledge already exists!");
      return
    }
    if (!knowledge) return;

    await this._knowledgeService.srvAdd(knowledge);
    this._knowledgeService.toSave();

    if (this.knowledgeClasses.includes(knowledge)) return;
    else this.knowledgeClasses.push(knowledge);
  }

  override async lstInit() {
    this._knowledgeService.toFinishLoading();
    this.knowledgeClasses = this._knowledgeService.models;
    this._knowledgeService.svcReviewAll();
    this.lstIds = this.knowledgeClasses.map(x => x.id);

    setTimeout(() => this.description = `Showing ${this.knowledgeClasses.length} results`, 50);

    const tab = localStorage.getItem(
      `tab-OthersComponent${FILTER_SUFFIX_PATH}`
    );
    this.activeTab = tab === 'null' || !tab ? 'keywords' : tab;
    this.isTab = false;
  }

  async removeElement(status: Knowledge) {
    const confirm = await Alert.showRemoveAlert(status.knowledge + ' ' + 'Knolodge');
    if (!confirm) return;

    this._knowledgeService.models = await this._knowledgeService.models.filter(s => s.id !== status.id);
    await this._knowledgeService.toSave();
    this.knowledgeClasses = this.knowledgeClasses.filter(s => s !== status);
  }

  public getModifierOrtography(status: Knowledge) {
    this._translationService.getKnowledgeOrtography(status, true);
  }

  //The two below sort methods are here because it dosent work with the old code but the others parameters did worked.
  sortBySkillOrder = -1;
  sortBySkill() {
    this.sortBySkillOrder *= -1;
    this._knowledgeService.models.sort((a, b) => {
      return this.sortBySkillOrder * a.knowledge.localeCompare(b.knowledge);
    });
    this.lstFetchLists();
  }
  sortByAcronymOrder = -1;
  sortByAcronym() {
    this.sortByAcronymOrder *= -1;
    this._knowledgeService.models.sort((a, b) => {
      return this.sortByAcronymOrder * a.acronym.localeCompare(b.acronym);
    });
    this.lstFetchLists();
  }

  clickBtn(isActive: boolean) {
    if (isActive) {
      this.activeTab = 'Subcontext';
      this.isTab = true;
    }
    localStorage.setItem(`tab-KnowledgeComponent${FILTER_SUFFIX_PATH}`, this.activeTab);
  }

  clickBtnKnoledgeHandler(event: boolean): void {
    if (event) {
      this.activeTab = 'keywords';
      this.isTab = false;
      // Atualizar o armazenamento local para persistir o estado
      localStorage.setItem(`tab-KnowledgeComponent${FILTER_SUFFIX_PATH}`, this.activeTab);
    }
  }

  knowledgeChange(knowledge: Knowledge, fieldknowledge: string, value: string) {
    knowledge.isReviewedknowledge = false;
    knowledge.revisionCounterknowledgeAI = 0;
    this.lstOnChange(knowledge, 'knowledge', value);
  }

  descriptionChange(knowledge: Knowledge, description: string, value: string) {
    knowledge.isReviewedDescription = false;
    knowledge.revisionCounterDescriptionAI = 0;
    this.lstOnChange(knowledge, 'description', value);
  }
}
