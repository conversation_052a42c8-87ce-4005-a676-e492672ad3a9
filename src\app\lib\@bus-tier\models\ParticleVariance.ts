import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { Base } from './Base';

export class ParticleVariance extends Base<Data.Hard.IParticleVariance, Data.Result.IParticleVariance> implements Required<Data.Hard.IParticleVariance>
{
  private static generateId(index: number): string 
  {
    return IdPrefixes.PARTICLE_VARIANCE + index;
  }
  
  constructor(index: number, dataAccess: ParticleVariance['TDataAccess'])
  {
    super(
      {
        hard: 
        {
          id: ParticleVariance.generateId(index),
          amount: undefined
        },
      },
      dataAccess
    );
  }
  protected getInternalFetch() 
  {
    return {};
  }
  public get amount(): number[] 
  {
    return this.hard.amount;
  }
  public set amount(value: number[]) 
  {
    this.hard.amount = value;
  }  
  public get type(): string 
  {
    return this.hard.type;
  }
  public set type(value: string) 
  {
    this.hard.type = value;
  } 
  public get name(): string 
  {
    return this.hard.name;
  }
  public set name(value: string) 
  {
    this.hard.name = value;
  } 
  public get drop(): string 
  {
    return this.hard.drop;
  }
  public set drop(value: string) 
  {
    this.hard.drop = value;
  }
  public get order(): number[]
  {
    return this.hard.order;
  }
  public set order(value: number[]) 
  {
    this.hard.order = value;
  }  
  public get idItemClass(): string
  {
    return this.hard.idItemClass;
  }
  public set idItemClass(value: string) 
  {
    this.hard.idItemClass = value;
  }  
    

}
