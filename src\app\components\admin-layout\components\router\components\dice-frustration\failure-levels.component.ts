import { Component } from '@angular/core';
import { FILTER_SUFFIX_PATH } from '../../../../../../../lib/darkcloud/angular/dsadmin/constants/others';

@Component({
  selector: 'app-failure-levels',
  templateUrl: './failure-levels.component.html',
  styleUrls: ['./failure-levels.component.scss']
})
export class FailureLevelsComponent {

  activeTab: string;
  isTab = false;

  constructor() { }

  async ngOnInit() {  
    const tab = localStorage.getItem(`tab-OthersComponent${FILTER_SUFFIX_PATH}`);
    this.activeTab = tab === 'null' || !tab ? 'keywords' : tab;
    this.activeTab = 'att-check';
    this.isTab = false;

  }  
  public switchToTab(tab: string) {
    this.activeTab = tab;
    localStorage.setItem(`tab-FailureLevelsComponent${FILTER_SUFFIX_PATH}`, this.activeTab);

  }

}
