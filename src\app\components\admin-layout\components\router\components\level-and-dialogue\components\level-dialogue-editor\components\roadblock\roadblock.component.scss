tr td
{
   // text-align: center;
    //background-color: lightcoral;
    padding-left: 0;
    padding-right: 25px;
    background-color: rgb(12, 231, 48);
    border-radius: 7px;
    height: 100%;
    max-height: 250px;
}
tr.marker td
{
    background-color: transparent!important;
}

.title-category {
    font-size: 14px;
    font-weight: 400;
    color: #9a9a9a;
    margin-bottom: 10px;
    float: left;
    margin-left: 30px;
    width: max-content;
}

.table-width {
    max-width: 925px;
   //width: 925px;
} 

.btn-outline-light
{
    color: rgb(255, 255, 255);
    border: 0;
}

label
{
    color: white;
}

select, input
{
    color: #565656;
}

.justSearch {
    flex: 1; 
    margin-left: 15px; 
    align-content: center; 
    margin-top: 23px; 
}

.inputSearch {
    width: 770px; 
    border-radius: .25rem; 
    height: 4vh; 
    border: 1px solid #ced4da; 
    border-radius: .25rem; 
    font-weight: 400; 
    line-height: 1.5; 
    color: #495057; 
    padding-left: 10px;
}

.inputSearchInter {
    width: 770px; 
    border-radius: .25rem; 
    height: 4vh; 
    border: 1px solid #ced4da; 
    border-radius: .25rem; 
    font-weight: 400; 
    line-height: 1.5; 
    color: #495057; 
    padding-left: 10px;
}

.inputSearchInter[readonly] {
    background-color: #4df85f;
    cursor: not-allowed;
  }
  .inputSearchInter:not([readonly]) {
    background-color: white; /* Normal quando editável */
    cursor: text;
  }
  
.mg-top {
   margin-top: 10px;
   float: left;
}

.unlockStyle {
    width: 300px !important;
    margin-bottom: 12px;
}

