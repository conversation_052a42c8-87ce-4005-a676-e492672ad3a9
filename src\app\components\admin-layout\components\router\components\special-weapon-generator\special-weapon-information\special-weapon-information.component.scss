
.content-weapon
{
  margin: 25px;
}

.containerTable
{
  overflow-x: auto; 
  bottom: 0px; 
  position: fixed; 
  overflow-y: auto; 
  top: 225px; 
  right: 20px; 
  left: 220px;
  margin-left: 15px;
  margin-right: 15px;
}

select option{
  background-color: #fff;
}

.sticky th
{
  top:0 !important;
}
.sticky{
  display:table-header-group !important;
  position:relative !important;
  z-index: 200 !important;
  border-color: #c9c9c9 !important;
}

.red-color
{
    background-color: rgb(255,0,0) !important;
    color: rgb(0, 0, 0)!important;
}

.blue-color
{
    background-color: rgb(0,176,240)!important;
    color: rgb(0, 0, 0)!important;

}

.light-blue-color
{
    background-color: rgb(221,235,247)!important;
    color: rgb(0, 0, 0)!important;

}

.light-gray-color
{
    background-color: rgb(242,242,242)!important;
    color: rgb(0, 0, 0)!important;

}

.gray-color 
{
    background-color: rgb(89,89,89)!important;
    color: #fff!important;

}

.gray-bg {
  background-color: #e6e6e6;
}

