.content-weapon
{
  margin: 25px;
}

.content
{
  overflow-x: auto;
  bottom: 0px;
  position: fixed;
  overflow-y: auto;
  top: 340px;
  right: 20px;
  left: 220px;
  margin-left: 15px;
  margin-right: 15px;
}

.m-cammon {
 margin-left: 15px;
 margin-right: 15px;
}

table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
}

th, td {
    
    text-align: center;
    border: 1px solid #2f2f2f;
}
th {
    background-color: #2f2f2f;
    color: white;
    padding: 10px;
    font-weight: 400 !important;
}
  
hr {
    margin-top: 0px; 
    margin-bottom: 0px;
    border: 0;
    border-top: 1px solid #2f2f2f;  
}

p {
    font-size: 14px !important;
}

.light-blue-bg {
    background-color: #00d8ff !important;
   // border-color: #00d8ff !important;
    color: white !important;
    font-weight: bold;
}
.white-bg {
    background-color: white;
    color: black;
}

.green-bg {
    background: rgb(0, 176, 80);
    color: white;
}

.primary-bg {
    background-color: #0070c0; 
    color: white;
}

.purple-bg {
    background: rgb(112, 48, 160);
    color: white;
}

.yellow-bg {
    background: rgb(255, 192, 0);
    color: white;
}

.light-blue-bg {
    background-color: lightblue;
}

.gray-bg {
    background-color: #e6e6e6;
}

