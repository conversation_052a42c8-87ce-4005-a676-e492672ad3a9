import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';

export class RPCTableTributeAndSubmission extends Base<Data.Hard.IRPCTableTributeAndSubmission, Data.Result.IRPCTableTributeAndSubmission> implements Required<Data.Hard.IRPCTableTributeAndSubmission>
{
  public static generateId(index: number): string {
    return IdPrefixes.RPCTABLETRIBUTEANDSUBMISSION + index;
  }

  constructor( index: number, dataAccess: RPCTableTributeAndSubmission['TDataAccess']) 
  {
    super(
    {
      hard: 
      {
        id: RPCTableTributeAndSubmission.generateId(index),     
      },
    },
    dataAccess
    );
  }
    protected getInternalFetch() 
  {
    return {};
  }
  public get valueE(): string 
  {
    return this.hard.valueE;
  }
  public set valueE(value: string) 
  {
    this.hard.valueE = value;
  }
  public get tone(): string 
  {
    return this.hard.tone;
  }
  public set tone(value: string) 
  {
    this.hard.tone = value;
  }
  public get description(): string 
  {
    return this.hard.description;
  }
  public set description(value: string) 
  {
    this.hard.description = value;
  }
  public get phraseOptionsA(): string 
  {
    return this.hard.phraseOptionsA;
  }
  public set phraseOptionsA(value: string) 
  {
    this.hard.phraseOptionsA = value;
  }
  public get phraseOptionsB(): string 
  {
    return this.hard.phraseOptionsB;
  }
  public set phraseOptionsB(value: string) 
  {
    this.hard.phraseOptionsB = value;
  }
  public get phraseOptionsC(): string 
  {
    return this.hard.phraseOptionsC;
  }
  public set phraseOptionsC(value: string) 
  {
    this.hard.phraseOptionsC = value;
  }



}
