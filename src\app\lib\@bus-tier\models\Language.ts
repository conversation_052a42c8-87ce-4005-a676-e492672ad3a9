import { Data } from "src/lib/darkcloud/angular/dsadmin";
import { IdPrefixes } from "src/lib/darkcloud/dialogue-system";
import { Base } from "./Base";

export class Language
extends Base<Data.Hard.ILanguage, Data.Result.ILanguage>
implements Required<Data.Hard.ILanguage>
{
    static generateId(index: number): string
    {
        return IdPrefixes.LANGUAGE + index;
    }

    constructor(
        index: number,
        name: string,
        dataAccess: Language['TDataAccess']
    ) {
        super(
            {
                hard: {
                    id: Language.generateId(index),
                    name
                },
            },
            dataAccess
        );
    }

    get name()
    {
        return this.hard.name;
    }
    set name(value: string)
    {
        this.hard.name = value;
    }
    
}