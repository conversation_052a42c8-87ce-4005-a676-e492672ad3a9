<div class="row">
  <div class="col-md-6">
    <div class="report-table-wrapper">
      <p class="error">Negative Options ({{ negativeOptionsWeightTotal }})</p>
      <table class="table report-table table-striped">
        <thead class="sticky" style="top: 0px;">
        <tr>	
          <th class="th-clickable" (click)="sortArrayByWeight(negativeOptions)">Weight</th>	
          <th class="th-clickable" (click)="sortArrayByMessage(negativeOptions)">Message</th>	
          <th class="th-clickable" (click)="sortArrayByCode(negativeOptions)">Hierarchy Code</th>	
        </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let option of negativeOptions">
            <tr>
              <td class="td-auto center">
                {{option.weight}}
              </td>
              <td class="td-80 td-clickable"
                  (click)="access(option)"
                  [innerHTML]="option.message | rpgFormatting">
              </td>
              <td class="td-auto center">
                {{(option.id | area : true).hierarchyCode}}
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>

  <div class="col-md-6">
    <div class="report-table-wrapper">
      <p class="success">Positive Options ({{ positiveOptionsWeightTotal }})</p>
      <table class="table report-table table-striped">
        <thead class="sticky"
               style="top: 0px;">
        <tr>	
          <th class="th-clickable" (click)="sortArrayByWeight(positiveOptions)">Weight</th>	
          <th class="th-clickable" (click)="sortArrayByMessage(positiveOptions)">Message</th>	
          <th class="th-clickable" (click)="sortArrayByCode(positiveOptions)">Hierarchy Code</th>	
        </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let option of positiveOptions">
            <tr>
              <td class="td-auto center">
                {{option.weight}}
              </td>
              <td class="td-80 td-clickable"
                  (click)="access(option)"
                  [innerHTML]="option.message | rpgFormatting"></td>
              <td class="td-auto center">
                {{(option.id | area : true).hierarchyCode}}
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>
