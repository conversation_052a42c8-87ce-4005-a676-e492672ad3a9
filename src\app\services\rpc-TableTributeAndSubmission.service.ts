import { Injectable } from '@angular/core';
import { IndexStorageService, ReviewService } from 'src/app/services';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { ModelService } from 'src/lib/darkcloud/angular/dsadmin';
import { RPCTableTributeAndSubmission } from '../lib/@bus-tier/models';

@Injectable({
  providedIn: 'root',
})
export class RPCTableTributeAndSubmissionService extends  ModelService<RPCTableTributeAndSubmission> 
{

  public override svcPromptCreateNew(...args: any): Promise<RPCTableTributeAndSubmission> {
    throw new Error('Method not implemented.');
  }

  constructor(
    indexStorageService: IndexStorageService,
    readonly userSettingsService: UserSettingsService,
    reviewService: ReviewService,
  ) 
  {
    super(
      {
        defaultConstructor: () =>
          new RPCTableTributeAndSubmission(0, this.userSettingsService),
      },
      'RPCTableTributeAndSubmission',
      indexStorageService,
      reviewService,
    );
  }
  
  public createNewTributeAndSubmission(): RPCTableTributeAndSubmission {  

    let submission = new RPCTableTributeAndSubmission(this.svcNextIndex(), this.userSettingsService);   
    this.srvAdd(submission);
    return submission;    

  }
}
