<div class="row">
  <div class="col-md-12">
    <div class="report-table-wrapper">
      <table class="table report-table table-striped">
        <thead class="sticky"
               style="top: 0px;">
          <tr>
            <th class="th-clickable"
                (click)="sorting.sortFinishDialogueMarkers(sorting.byType)">Type</th>
            <th class="th-clickable"
                (click)="sorting.sortFinishDialogueMarkers(sorting.byLocation)">Location
            </th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let marker of preloadedData.finishDialogueMarkers">
            <tr [title]="marker.id"
                (click)="redirectToDialoguesManager(marker.id)"
                class="tr-clickable">
              <td class="td-auto">{{ marker.type | markerTypeName }}</td>
              <td>{{([marker.id] | location).length > 0 ? ([marker.id] | location) : '<PERSON><PERSON>'}}
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>
