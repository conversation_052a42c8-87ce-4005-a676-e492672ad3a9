import { Component, OnInit } from '@angular/core';
import { EasyMVC } from 'src/lib/darkcloud/angular';
import { Marker } from 'src/app/lib/@bus-tier/models';
import { AreaService, MarkerService, ReviewService } from 'src/app/services';
import { Accessment, Sortment } from 'src/app/lib/@pres-tier';
import { ActivatedRoute, Router } from '@angular/router';

export interface Preloadable {
  pinDialogueMarkers: Marker[];
}
@Component({
  selector: 'app-level-pin-report',
  templateUrl: './level-pin-report.component.html',
})
export class LevelPinReportComponent
  extends EasyMVC.PreloadComponent<Preloadable>
  implements OnInit
{
  sorting: Sorting = new Sorting(this);
  accessing: Accessing = new Accessing(this);
  constructor(
    private _activatedRoute: ActivatedRoute,
    public readonly router: Router,
    public readonly areaService: AreaService,
    private _markerService: MarkerService,
    private _reviewService: ReviewService
  ) {
    super(_activatedRoute, (data) => data.preloadedLevelPinReportData);
  }

  public pinnedMarkers: Marker[] = [];

  ngOnInit(): void {
    this.pinnedMarkers = this._markerService.models.filter(x => {
      return (x.pin == true);
    });
  }

  access(id:string)
  {
    this.router.navigate(
      [
        'levels/' + this._reviewService.reviewResults[id].levelId +
        '/dialogues/' + this._reviewService.reviewResults[id].dialogueId
      ],
      {fragment: id}
    );
  }
}

class Sorting extends Sortment.Sorting {
  byLocation = Sortment.byLocation(this._component.areaService);
  byLevelLocation = Sortment.byLevelLocation(this._component.areaService);
  byPin = (marker: Marker) => marker.pin;
  byType = (marker: Marker) => marker.type;
  constructor(private readonly _component: LevelPinReportComponent) {
    super();
  }
  public sortPinMarkers(by: Sortment.Sortion<Marker>) {
    this.execute(this._component.pinnedMarkers, by);
  }
}

class Accessing extends Accessment.Accessing {
  towardsLevelList = Accessment.towardsLevelList<Marker>((m) => m.levelId);
  towardsDialogueEditor = Accessment.towardsDialogueEditor<Marker>((m) => m.id);
  public accessMarker(marker: Marker, towards: Accessment.Accession<Marker>) {
    this.execute(marker, towards);
  }
}
