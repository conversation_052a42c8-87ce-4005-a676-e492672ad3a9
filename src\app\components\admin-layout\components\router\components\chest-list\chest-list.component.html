<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons 
          [cardTitle]="'Chest List'"
          [cardDescription]="description">
        </app-header-with-buttons>
        <app-header-search (inputKeyup)="search($event)" (searchOptions)="lstOnChangeFilterOptions($event)"></app-header-search>
      </div>
    </div>
    <!--List-->
    <div class="card">
      <table class="table table-list">
        <thead class="sticky">
          <tr>
            <th class="th-clickable">Index</th>
            <th class="th-clickable" (click)="sortByChest()">Chest 
              <select [(ngModel)]="choosedChest" #item (change)="onSelectNewChest(item.value)" class="dropdown filter-dropdown limited center">
                <option *ngIf="choosedChest == 'All'" value="All">All</option>
                <option *ngFor="let items of this._itemClassService.models" [selected]="item.name">{{items.name}}</option>
              </select>
            </th>
            <th class="th-clickable" (click)="sortByAcronym()">Acronym</th>
            <th>Unlock Time (Minutes)</th>
            <th (click)="sortByChestType()" class="th-clickable">Minion x Boss e Subboss</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor=" let chest of this.chestList; let i = index;">
            <tr id="{{ chest.id }}">
              <td class="td-sort">{{ i + 1 }}</td>
              <td class="td-notes">
                {{chest.chestName}}
              </td>
              <td class="td-notes" style="width: 20%;">
                <input placeholder=" " class="form-control form-short background-input-table-color"
                  (change)="onChangeChest(acronym.value, chest, 'acronym')"
                  type="text"
                  value="{{ chest.acronym }}"
                  #acronym
                  [ngStyle]="{'background-color': +acronym.value <= 0 ? '#404040' : '#ffffff'}"/>                
              </td>
              <td class="td-id" style="text-align:right; width:20%">
                <input placeholder=" " class="form-control form-short background-input-table-color"
                  (change)="onChangeChest(unlockTime.value, chest, 'unlockTime')"
                  type="number"
                  value="{{chest.unlockTime}}"
                  #unlockTime
                  [ngStyle]="{'background-color': +unlockTime.value <= 0 ? '#404040' : '#ffffff'}"/>
              </td>
              <td class="td-actions">
                <select (change)="onChangeChest(items.value, chest, 'characterType')" #items value="{{chest.chestType}}" class="dropdown filter-dropdown limited center">
                  <option default value="All">All</option>
                  <option *ngFor="let item of this.characters" [selected]="item == chest.characterType">{{item}}</option>
                </select>
              </td>
              <td class="td-actions">
                <button 
                  (click)="removeElement(chest)" 
                  class="btn btn-danger btn-fill btn-remove">
                  <i class="pe-7s-close"></i>
                </button>              
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>
