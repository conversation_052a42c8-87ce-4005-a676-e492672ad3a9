<ng-container *ngIf="listConcatenations.length > 0">
    <div style="display: flex; flex-direction: row; overflow-x: auto; width: 100%;">
        <table class="table table-list borderList">
            <thead>
                <tr>            
                    <th>Order</th>
                    <th colspan="4">Concatenations</th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let item of listConcatenations; let i = index">                                
                        <td class="td-id aligTitle">
                            <input class="background-input-table-color form-control form-short noCursor" placeholder=" " disabled
                                type="text" #order [ngClass]="{'empty-input': !order.value}"
                                [value]="item.order" (change)="changeMahankaraValue(i,'order', order.value)" />
                        </td>
                        <td class="td-id" *ngFor="let conca of item.concatenations; let e = index" style="width: 60px; text-align-last: center;">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #concatenations [ngClass]="{'empty-input': !concatenations.value || conca === ''}"
                                [value]="conca" (change)="changeMahankaraValue(i,'concatenations', concatenations.value, e)" />
                        </td>
                </tr>
            </tbody>
        </table>
    </div>
</ng-container>


<ng-container *ngIf="listConcatenations.length === 0">
    <div class="card" style="text-align: center; padding: 20px;">
        <h3>Empty list. Click to create the list.</h3>
    </div>
</ng-container>
