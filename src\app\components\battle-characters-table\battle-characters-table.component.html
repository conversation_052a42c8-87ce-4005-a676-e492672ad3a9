<!--Add Minion Button-->
<div class="collumn no-horizontal-margin">
  <div *ngIf="level.battleCharacterIds.length === 0; else battleCharacterTable">
    <button *ngIf="!editable; else warningIcon" class="btn-absolute td-btn-focus btn btn-simple center"
      (click)="toPromptAddBattleCharacterToLevel(level)">
      <i class="pe-7s-plus icon center success"></i> Add Character
    </button>
    <ng-template #warningIcon>
      <i class="pe-7s-attention warning icon-review icon-large"></i>
    </ng-template>
  </div>
</div>
<ng-template #battleCharacterTable>
  <div class="collumn no-horizontal-margin">
    <!--List of minions-->
    <div style="display: flex; align-items: flex-start; gap: 10px; position: relative;">
      <!-- Tabela principal -->
      <table ngClass="table {{ editable ? 'compact-' : '' }}battle-character-table">
        <thead>
          <tr>
            <th style="border-color:rgb(219, 219, 219) !important; color:black !important">{{ level.type | levelTypeName
              | titleConvention: "plural" }}</th>
            <th style="border-color:rgb(219, 219, 219) !important;" *ngIf="!editable">
              <button class="btn btn-simple btn-success pull-right" (click)="toPromptAddBattleCharacterToLevel(level)">
                <i class="pe-7s-plus"></i>
              </button>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr class="tr-focus" *ngFor="let battleCharacterId of level.battleCharacterIds; let i = index">
            <td>
              <button [ngStyle]="editable ? { margin: '3px' } : null" (click)="accessCharacter(battleCharacterId)"
                ngClass="btn btn-fill btn-{{
                  (battleCharacterId | character)?.type | characterTypeClass
                }}">
                {{ (battleCharacterId | character)?.name }}
              </button>
            </td>
            <td *ngIf="!editable">
              <button class="btn tr-btn-focus btn-simple btn-danger pull-right"
                (click)="toPromptRemoveOneBattleCharacterFromLevel(level,battleCharacterId)">
                <i class="pe-7s-less"></i>
              </button>
            </td>
          </tr>
        </tbody>
      </table>

      <ng-container *ngIf="(isCollectible || +level.type === LevelType.BOSS) && isDialogue">
      <!-- Grupo "] Invoke" para itens a partir da posição 1 -->
      <div *ngIf="level.battleCharacterIds.length > 1" style="position: absolute; right: -120px; top: 0;">
        <!-- Container posicionado exatamente na linha da posição 1 -->
        <div style="position: relative; display: flex; align-items: stretch; justify-content: flex-start; "
          [ngStyle]="{'margin-top.px': 48 + 44,'height.px': (level.battleCharacterIds.length - 1) * 55}">

          <!-- Símbolo de chave ] mais fino -->
          <div style="display: flex; align-items: center; justify-content: center; width: 20px;"
            [ngStyle]="{'height.px': (level.battleCharacterIds.length - 1) * 50 }">
            <!-- Bracket ] feito com CSS - bordas sempre finas -->
            <div style="position: relative; width: 20px;" [ngStyle]="{'height.px': (level.battleCharacterIds.length - 1) * 50 - 6 }">
              <!-- Linha vertical direita -->
              <div style="position: absolute; right: 0; top: 0; width: 2px; background-color: #ccc;" 
                  [ngStyle]="{'height.px': (level.battleCharacterIds.length - 1) * 50 - 6 }">
               </div>
              <!-- Linha horizontal superior -->
              <div style="position: absolute; right: 0; top: 0; width: 17px; height: 2px; background-color: #ccc;"></div>
              <!-- Linha horizontal inferior -->
              <div style=" position: absolute; right: 0; bottom: 0; width: 17px; height: 2px; background-color: #ccc;"></div>
            </div>
          </div>
          <!-- Texto "Invoke" centralizado na altura total -->
          <div
            style="display: flex; align-items: center; justify-content: center; margin-left: 15px; font-size: 26px; color: #999; font-weight: normal; white-space: nowrap;"
            [ngStyle]="{'height.px': (level.battleCharacterIds.length - 1) * 50 }">
            Invoke
          </div>
        </div>
      </div>
      </ng-container>
    </div>
  </div>
</ng-template>