
<div class="content-weapon" style="margin: 25px;" *ngIf="!custom?.selectedParticleId">
  <h2>No particle selected!</h2>
</div>
<div class="content-weapon" style="margin: 25px;" *ngIf="custom?.selectedParticleId">
  <div class="card list-header-row">
    <app-header-with-buttons class="card"
                             [isBackButtonEnabled]="true"
                             (cardBackButtonClick)="onBack()"
                             [cardTitle]="item.name"
                             [cardDescription]="item.description"></app-header-with-buttons>
  </div>
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-6">
        <h3>Particle Information</h3>
        <br />
        <h4>Battle Description:</h4>
       <!--  <textarea  [ngClass]="{'form-control area-text-empty-background-color': particle.description.length == 0, 'form-control area-text-background-color':particle.description.length > 0}"
          style="margin-top: 10px; margin-bottom: 10px;"
          type="text"
          value="{{ particle.description }}"
          #description
          (change)="changeDescription(description.value)"
          *ngIf="lstLanguage == 'PT-BR'">
        </textarea> -->
        <textarea class="form-control"
          style="margin-top: 10px; margin-bottom: 10px;"
          type="text"
          value="{{ (particle | translation : lstLanguage : particle.id : 'description') }}"
          #description
          (change)="changeDescription(description.value)"
          >
        </textarea>
        <br />
        <h4>Effect Option:</h4>
        <select required class="dropdown filter-dropdown limited select-background-color"
                  [(ngModel)]="particle.effectId"
                  style="display: inline-block; margin: 10px; margin-bottom: 15px"
                  #effectId
                  (change)="changeSelectedEffectId(effectId.value)">
            <option *ngFor="let effect of effects"
                    value="{{ effect.id }}">{{ effect.name }}</option>
          </select>
        <!-- <textarea class="form-control"
          type="text"
          value=""
          #effect
          *ngIf="lstLanguage == 'PT-BR'">
        </textarea>
        <textarea class="form-control"
          type="text"
          value=""
          #effect
          *ngIf="lstLanguage != 'PT-BR'">
        </textarea> -->
        <br />
        <h4>HC Level (Gating.HC):</h4>
        <input type="number"
        placeholder=" "
              class="toggle background-input-table-color"
              [(ngModel)]="particle.hcLevel"
              style="margin-left: 10px;"
              (change)="changeHcLevel(particle.hcLevel)"/>
        <br />
        <br />
        <h4>ATK:</h4>
        <input type="number"
        placeholder=" "
              class="toggle background-input-table-color"
              [(ngModel)]="particle.atk"
              style="margin-left: 10px;"
              (change)="changeAtk(particle.atk)"/>
        <br />
        <br />
        <h4>Shake:</h4>
        <input type="checkbox"
        placeholder=" "
              class="toggle center"
              [(ngModel)]="particle.shake"
              (change)="changeShake(particle.shake)"/>
        <br />
        <br />
        <h4>Hit:</h4>
        <input type="checkbox"
        placeholder=" "
              class="toggle center background-input-table-color"
              [(ngModel)]="particle.hit"
              (change)="changeHit(particle.hit)"/>
        <br />
        <br />
        <h4>Splits:</h4>
        <input type="number"
        placeholder=" "
              class="toggle background-input-table-color"
              [(ngModel)]="particle.split"
              style="margin-left: 10px;"
              (change)="changeSplit(particle.split)"/>
      </div>

      <div class="col-md-6">
        <h3>&nbsp;</h3>
        <br />
        <h4>Character Group:</h4>
        <character-selector
        [selectedCharactersId]="particle.charactersId"
        (selectedCharactersIdChange)="changeSelectedCharactersId($event)" ></character-selector>
        <br />
        <h4>Classes:</h4>
        <class-selector
        [selectedClassessId]="particle.classesId"
        (selectedClassessIdChange)="changeSelectedClassessId($event)" ></class-selector>
        <!-- <br />
        <h4>Weapon:</h4>
        <select class="dropdown filter-dropdown limited"
                [(ngModel)]="particle.weaponId"
                #weaponId
                (change)="changeWeaponId(weaponId.value)">
          <option *ngFor="let weapon of weapons"
                  value="{{ weapon.id }}">{{ GetItemName(weapon.itemId) }}</option>
        </select> -->

      </div>
    </div>
  </div>
</div>
