.item-overlay
{
    position: absolute;
    //border: 15px solid black;
    height: 100%;
    width: 100%;
    background-color: white;
    z-index: 999;
}

.popup-report
{
    position: fixed;
    height: 28%;
    width: 40%;
    background-color: white;
    z-index: 9999;
    margin: auto;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin-left: auto;
    margin-right: auto;
    margin-top: auto;
    margin-bottom: auto;

    text-align: center;
}

/* Fundo escurecido (backdrop) */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5); /* Ajuste a opacidade aqui */
    z-index: 998; /* Deve ser menor que o modal */
  }

.c-title {
    display: flex;
    /* gap: 50%; */
    align-items: center; 
    width: 100%;
    padding: 10px;
    border-bottom: 2px solid #555;
}

.ptextItem {
    text-decoration: underline;
    font-weight: 900;
    margin-top: 30px;
}

.height-cap
{
    max-height: 1000px;
}

.submit-button
{
    text-align: center;
    margin-left: auto;
    margin-right: auto;
    width: 60%;
    margin-top: 250px;
}

.total-modal {
    background-color: white; 
    border-radius: 5px; 
    border: 2px solid #555; 
    padding-bottom: 30px;
}

.total-content {
    overflow-y: auto;
    height: auto;
    scrollbar-width: thin;
    scroll-behavior: auto;    
    max-height: 690px;
}

.c-content-item {
    margin-top: 20px; 
    margin-bottom: 20px; 
    max-height: 230px; 
    display: contents;
}
.scrollable-div
{
    overflow-y: auto;
    height: auto;
    scrollbar-width: thin;
    scroll-behavior: auto;
    max-height: 50%;
 }

.list-header-row
{
  margin-bottom: -20px;
}
.requested-not-assigned	
{	
    vertical-align: middle;	
    width: 35px;	
    height: 35px;	
}

.padGating {
    padding-left: 23px;
    padding-right: 23px;
}

.ball-circle {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #2196F3;
    border: 2px solid #FFFFFF;
    float: right;
  }