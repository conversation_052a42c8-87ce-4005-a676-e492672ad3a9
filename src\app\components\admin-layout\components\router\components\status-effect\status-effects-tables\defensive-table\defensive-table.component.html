<div class="div-container">
    <div class="width: 2221px;">
        <table class="table table-list borderList">
            <thead>
                <tr>
                    <th [attr.colspan]="titles.length +1">
                        <h3>Defensive</h3>
                    </th>
                    <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'" class="addButton"
                        [buttonTemplates]="[excelButtonTemplate]">
                    </app-button-group>
                </tr>
                <ng-container *ngIf="!isListDefensiveEmpty">

                    <tr>
                        <th class="default-color">Order</th>
                        <th [ngClass]="(title === 'SKILL WEAK USER') ? 'skill-Weak' : 'default-color'"
                            *ngFor="let title of titles">
                            {{title}}
                        </th>
                    </tr>

                </ng-container>
            </thead>
            <ng-container *ngIf="!isListDefensiveEmpty">
                <tbody>
                    <tr *ngFor="let item of listDefensiveTable; let i = index">
                        <td style="background-color: #ddd; width: 2%;">{{ i + 1 }}</td>
                        <td class="td-id aligTitle" style="width: 8%;">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #idDefensive [ngClass]="{'empty-input': !idDefensive.value}"
                                [value]="item.idDefensive"
                                (change)="changeDefensive(i,'idDefensive', idDefensive.value)" />
                        </td>
                        <td class="td-id aligTitle" style="width: 110px;">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #idCategory [ngClass]="{'empty-input': !idCategory.value}"
                                [value]="item.category" (change)="changeDefensive(i,'category', idCategory.value)" />
                        </td>
                        <td class="td-id aligTitle" style="width: 110px;">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #status [ngClass]="{'empty-input': !status.value}" [value]="item.status"
                                (change)="changeDefensive(i, 'status', status.value)" />
                        </td>
                        <td class="td-id aligTitle" style="width: 7%;">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #skillWeakUser [ngClass]="{'empty-input': !skillWeakUser.value}"
                                [value]="item.skillWeakUser"
                                (change)="changeDefensive(i, 'skillWeakUser', skillWeakUser.value)" />
                        </td>
                        <td class="td-id aligTitle" style="width: 110px;">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #operator [ngClass]="{'empty-input': !operator.value}"
                                [value]="item.operator" (change)="changeDefensive(i, 'operator',operator.value)" />
                        </td>
                        <td class="td-id aligTitle" style="width: 110px;">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #value [ngClass]="{'empty-input': !value.value}" [value]="item.value"
                                (change)="changeDefensive(i, 'value', value.value)" />
                        </td>
                        <td class="td-id" style="width: 160px;">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #statusEffectName [ngClass]="{'empty-input': !statusEffectName.value}"
                                [value]="item.statusEffectName"
                                (change)="changeDefensive(i, 'statusEffectName', statusEffectName.value)" />
                        </td>
                        <td class="td-id" style="width: 600px; word-break: break-word;">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #description [ngClass]="{'empty-input': !description.value}"
                                [value]="item.description"
                                (change)="changeDefensive(i, 'description', description.value)" />
                        </td>
                        <td class="td-id aligTitle" style="width: 7%;">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #powerPoints [ngClass]="{'empty-input': !powerPoints.value}"
                                [value]="item.powerPoints"
                                (change)="changeDefensive(i, 'powerPoints', powerPoints.value)" />
                        </td>
                        <td class="td-id aligTitle">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #allDefensive [ngClass]="{'empty-input': !allDefensive.value}"
                                [value]="item.allDefensive"
                                (change)="changeDefensive(i, 'allDefensive', allDefensive.value)" />
                        </td>
                        <td class="td-id aligTitle" style="width: 7%;">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #duration [ngClass]="{'empty-input': !duration.value}"
                                [value]="item.duration" (change)="changeDefensive(i, 'duration', duration.value)" />
                        </td>
                    </tr>
                </tbody>
            </ng-container>
            <ng-container *ngIf="isListDefensiveEmpty">
                <div class="card" style="text-align: center; padding: 20px;">
                    <h3>The list has not yet been imported.</h3>
                </div>
            </ng-container>
        </table>
    </div>

</div>