<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons [cardTitle]="'Storage B'" [cardDescription]="description"
          [rightButtonTemplates]="[excelButtonTemplate]">
        </app-header-with-buttons>
        <app-header-search (inputKeyup)="lstOnChangeFilter($event)"
          (searchOptions)="lstOnChangeFilterOptions($event)"></app-header-search>
      </div>
    </div>
    <!--List-->
    <div class="card">
      <table class="table table-list">
        <thead class="sticky">
          <tr>
            <th rowspan="3" class="th-clickable" (click)="this.sortBySoulsLevel()">
              Souls Grinder Storage - Level
            </th>
            <th class="th-clickable" (click)="this.sortByTitaniumCost()">
              Cost
            </th>
            <th class="th-clickable" (click)="sortByBuildingTime()">
              Building Time
            </th>
            <th class="th-clickable" (click)="sortByRubiesCost()">
              Skip Cost
            </th>
            <th rowspan="3" class="th-clickable" (click)="sortByLocalStorage()">
              Storage Capacity B
            </th>
          </tr>
          <tr>
            <th class="th-clickable silver-color" (click)="this.sortByTitaniumCost()">
              TITANIUM
            </th>
            <th class="th-clickable time-color" (click)="sortByBuildingTime()">
              MINUTES
            </th>
            <th class="th-clickable rubies-color" (click)="sortByRubiesCost()">
              RUBIES
            </th>
          </tr>
          <tr>
            <th class="th-clickable light-gray" (click)="this.sortByTitaniumCost()">
              Required to get to this level
            </th>
            <th class="th-clickable light-gray" (click)="sortByBuildingTime()">
              Time to upgrade to this level
            </th>
            <th class="th-clickable light-gray" (click)="sortByRubiesCost()">
              Gem to skip the wait (build instantly)
            </th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="
                let soulsStorage  of lstIds | soulsGrinderStorages;
                let i = index;
                trackBy: trackById
              ">
            <tr *ngIf="soulsStorage.type === 'B'" id="{{ soulsStorage.id}}">

              <td>{{ soulsStorage.soulsLevel}}</td>

              <td class="td-id">
                <input class="background-input-table-color" placeholder=" " type="number" #Inputsouls
                  [value]="soulsStorage.titaniumCost" (change)="changeSoulLevel(soulsStorage, +Inputsouls.value)" />
              </td>

              <td class="td-id">
                <input class="background-input-table-color" placeholder=" " type="number" #Inputtime
                  [value]="soulsStorage.requiredTime" (change)="changeRequiredTime(soulsStorage, +Inputtime.value)" />
              </td>

              <td class="td-id">
                <input class="background-input-table-color" placeholder=" " type="number" #Inputrubies
                  [value]="soulsStorage.rubiesSkipCost" (change)="changeRubiesCost(soulsStorage, +Inputrubies.value)" />
              </td>

              <td class="td-id">
                <input class="background-input-table-color" placeholder=" " type="number" #Inputstorage
                  [value]="soulsStorage.localStorage"
                  (change)="changeLocalStorage(soulsStorage, +Inputstorage.value)" />
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>