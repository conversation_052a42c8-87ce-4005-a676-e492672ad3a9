import { ChangeDetectorRef, Component, Input} from '@angular/core';
import { CodeBlockDrop, WeaponUpgrade } from 'src/app/lib/@bus-tier/models';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';
import { ChestService, TierService, UserSettingsService } from 'src/app/services';
import { Button } from 'src/app/lib/@pres-tier/data';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc';
import { TranslationService } from 'src/app/services/translation.service';
import { CodeBlockDropService } from 'src/app/services/code-block-drop.service';
import { Alert } from 'src/lib/darkcloud';
import { SpinnerService } from './../../../../../../../spinner/spinner.service';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-code-block-drops-generator',
  templateUrl: './code-block-drops-generator.component.html',
  styleUrls: ['./code-block-drops-generator.component.scss'],
})
export class CodeBlockDropsGeneratorComponent extends SortableListComponent<CodeBlockDrop> 
{
  @Input() probability = true;
  codeBlocksList: CodeBlockDrop[] = []; 
  sortTypeOrder = -1;
  sortTiersOrder = -1;
  custom: Custom;
  weaponUpgrades: WeaponUpgrade[];
  tierList:string[] = [];
  override listName: string = 'Code Block (Drop Probability)';

  constructor(
    private spinnerService:SpinnerService,
    _activatedRoute: ActivatedRoute,
    protected _codeBlockDropService: CodeBlockDropService,
    _userSettingsService: UserSettingsService,
    protected _translationService: TranslationService,
    private ref: ChangeDetectorRef,
    private _chestService: ChestService,
    private _tierListService: TierService
  ) 
  {
    super(_codeBlockDropService, _activatedRoute, _userSettingsService, 'name');
  }

  public readonly excelButtonTemplate: Button.Templateable = 
  {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  public override async ngOnInit(): Promise<void> 
  {
    await this._codeBlockDropService.toFinishLoading();
    await this._chestService.toFinishLoading();
    await this._tierListService.toFinishLoading();
    this.tierList = this._tierListService.fillRarityArrayDynimically('Code Block Rarity', this.tierList);  
    
    this.filterCodeblockBasedOnChests();
    if(!this.probability) this.listName = 'Code Block (Garanteed Drop)';
    return null; 
  }

  sortListByType() {
    this.sortTypeOrder *= -1;
    this.codeBlocksList.sort((a, b) => 
    {  
      return this.sortTypeOrder * a.type.localeCompare(b.type);
    });
 }

 sortListByTiers() {
  this.sortTypeOrder *= +1;
  this.tierList.sort((a, b) => 
  {  
    return this.sortTypeOrder * a.localeCompare(b);
  });
 }


  filterCodeblockBasedOnChests()
  {
    let arrayLength:number = this._codeBlockDropService.models.length;
    if(arrayLength > 0)
    {
      for(let i = 0; i < this._chestService.models.length; i++)
      {
        for(let j = 0; j < arrayLength; j++)
        {
          if(this._chestService.models[i].acronym != undefined &&
            this._chestService.models[i].acronym != '' && 
            this._chestService.models[i].acronym == this._codeBlockDropService.models[j].type &&
            this._codeBlockDropService.models[j].type != undefined &&
            this._codeBlockDropService.models[j].type != '')
          {
            this.codeBlocksList.push(this._codeBlockDropService.models[j]);
            break;
          }
          if(j == arrayLength-1 &&               
            this._chestService.models[i].acronym != undefined &&
            this._chestService.models[i].acronym != '')
          {
            let cbd = this._codeBlockDropService.createNewDrop(this._chestService.models[i].acronym);
            this.codeBlocksList.push(cbd);
            break;
          }
        }
      }
    }
    else
    {
      for(let i = 0; i < this._chestService.models.length; i++)
      {
        let cbd = this._codeBlockDropService.createNewDrop(this._chestService.models[i].acronym);
        this.codeBlocksList.push(cbd);
      }      
    }
    this.sortListByType();  
  }

  async changeBlockDrops(codeBlockDrop: CodeBlockDrop, value: string, fieldName:string) 
  {
    codeBlockDrop.hard[fieldName] = value == '' ? undefined : +value;
    await this._codeBlockDropService.svcToModify(codeBlockDrop);
    await this._codeBlockDropService.toSave();
  }

  async onExcelPaste(): Promise<void> {
    this.spinnerService.setState(true);
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter(element => element);

    const excelHeaders = lines[0].split(/\t/).map(header => header.trim());
    const expectedColumnsCount = this.tierList.length + 1; // +1 para incluir a coluna de tipo
    const codeBlockErrors: string[] = [];
    const codeBlockNames: string[] = [];  
    const duplicateNames: string[] = [];  

    let fieldsProbabilityName: string[] = [];
    let noFieldsProbabilityName: string[] = [];

    // Inicializa arrays de nomes para os campos de probabilidade e valores numéricos
    for (let i = 0; i < this.tierList.length; i++) {
        fieldsProbabilityName.push(this.tierList[i].toLowerCase() + 'Probability');
        noFieldsProbabilityName.push(this.tierList[i].toLowerCase() + 'Numeric');
    }

    // Verifica se o número de colunas no Excel é o mesmo que o esperado
    if (excelHeaders.length !== expectedColumnsCount) {
        this.displayErrors([`The copied data has ${excelHeaders.length} columns, but ${expectedColumnsCount} were expected.`]);
        this.spinnerService.setState(false);
        return;
    }
 
    for (let l = 0; l < lines.length; l++) {
        let line = lines[l];
        let cols = line.split(/\t/).map(col => col.trim());

        const currentType = cols[0];

        // Verifica se o tipo já foi encontrado antes (duplicado)
        if (codeBlockNames.includes(currentType)) {
            duplicateNames.push(currentType);
        } else {
            codeBlockNames.push(currentType);
        }

        // Verifica se o tipo do bloco de código está presente na lista
        let codeBlockDrop = this._codeBlockDropService.models.find(x => x.type.trim() === currentType);

        if (!codeBlockDrop) {
            codeBlockErrors.push(currentType);  // Armazena o tipo que não foi encontrado
            continue;
        }

        if (this.probability) {
            for (let i = 0; i < fieldsProbabilityName.length; i++) {
                if (cols[i + 1]?.trim()) {
                    codeBlockDrop.hard[fieldsProbabilityName[i]] = +(cols[i + 1].split(' ').join('').split('.').join('').replace(',','.').replace('%',''));
                } else {
                    codeBlockDrop.hard[fieldsProbabilityName[i]] = undefined;
                }
            }
        } else {
            for (let i = 0; i < noFieldsProbabilityName.length; i++) {
                if (cols[i + 1]?.trim()) {
                    codeBlockDrop.hard[noFieldsProbabilityName[i]] = +(cols[i + 1].split(' ').join('').split('.').join('').replace(',','.').replace('%',''));
                } else {
                    codeBlockDrop.hard[noFieldsProbabilityName[i]] = undefined;
                }
            }
        }

        await this._codeBlockDropService.svcToModify(codeBlockDrop);
        await this._codeBlockDropService.toSave();
    }

    if (codeBlockErrors.length > 0) {
        this.displayErrors([`Type names not found in the system: ${codeBlockErrors.join(', ')}`]);
    }

    if (duplicateNames.length > 0) {
        this.displayErrors([`Duplicate column name: ${duplicateNames.join(', ')}`]);
    }

    this.codeBlocksList = []; 
    this.codeBlocksList = this._codeBlockDropService.models;

    this.lstFetchLists();
    this.ref.detectChanges();   
    this.spinnerService.setState(false);    
    Alert.ShowSuccess(`${this.listName} copied successfully!`);
}

displayErrors(errors: string[]): void { 
    if (errors.length > 0) {
        Alert.showError(errors.join('\n'));
    }
}


  sortListByParameterOrder:number = 1;
  public override sortListByParameter(parameter: string): void 
  {
    this.sortListByParameterOrder *= -1;
    this.codeBlocksList.sort((a,b)=>
    {
      if(!a.hard[this.probability ? parameter.toLowerCase() +'Probability' : parameter.toLowerCase() +'Numeric'] &&
       b.hard[this.probability ? parameter.toLowerCase() +'Probability' : parameter.toLowerCase() +'Numeric']) return 1;
      if(a.hard[this.probability ? parameter.toLowerCase() +'Probability' : parameter.toLowerCase() +'Numeric'] && 
        !b.hard[this.probability ? parameter.toLowerCase() +'Probability' : parameter.toLowerCase() +'Numeric']) return -1;
      if(!a.hard[this.probability ? parameter.toLowerCase() +'Probability' : parameter.toLowerCase() +'Numeric'] && 
        !b.hard[this.probability ? parameter.toLowerCase() +'Probability' : parameter.toLowerCase() +'Numeric']) return 0;
      return this.sortListByParameterOrder*+a.hard[this.probability ? parameter.toLowerCase() +'Probability' : parameter.toLowerCase() +'Numeric']-
        +b.hard[this.probability ? parameter.toLowerCase() +'Probability' : parameter.toLowerCase() +'Numeric'];
    });

  }

  sortByNameOrder:number = 1;
  sortByName(parameter:string) 
  {
    this.sortByNameOrder *= -1;
    this.codeBlocksList.sort((a, b) => 
    {
      if(!a[parameter]&& b[parameter]) return 1;
      if(a[parameter]&& !b[parameter]) return -1;
      if(!a[parameter]&& !b[parameter]) return 0;
      return this.sortByNameOrder * a[parameter]?.localeCompare(b[parameter]);
    });
  }

}
