import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';

// Interfaces para os objetos de Knowledge e Attribute
interface RarityValue {
  idRarity?: string;
  value?: string;
}

export class ParryPry extends Base<Data.Hard.IParryPry, Data.Result.IParryPry> implements Required<Data.Hard.IParryPry>
{
  public static generateId(index: number): string {
    return IdPrefixes.PARRYPRY + index;
  }

  constructor( index: number, dataAccess: ParryPry['TDataAccess']) 
  {
    super(
    {
      hard: 
      {
        id: ParryPry.generateId(index),     
      },
    },
    dataAccess
    );
  }
  protected getInternalFetch() 
  {
    return {};
  }
  public get className(): string 
  {
    return this.hard.className;
  }
  public set className(value: string) 
  {
    this.hard.className = value;
  }
  public get classId(): string 
  {
    return this.hard.classId;
  }
  public set classId(value: string) 
  {
    this.hard.classId = value;
  }
  public get rarityValue(): RarityValue[]
  {
    return this.hard.rarityValue;
  }
  public set rarityValue(value: RarityValue[])
  {
    this.hard.rarityValue = value;
  }


}
