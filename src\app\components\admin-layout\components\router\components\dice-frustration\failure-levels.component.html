    <div class="main-menu-efect">
        <div class="container-fluid">
            <div class="list-header-row update">
                <div class="card">
                    <div style="display: flex; justify-content: space-between;">
                        <div class="card-header-content">
                            <button class="{{activeTab === 'att-check' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                            (click)="switchToTab('att-check')">Attribute Check</button>
                            <button style="margin-left: 2px;" class="{{activeTab === 'Know-check' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                            (click)="switchToTab('Know-check')">Knowledge Check</button>
                        </div>
                        <div style="display: flex; align-items: end; justify-content: end; align-items: center;">
                            <div class="btn-atributte">
                                <button style="margin-left: 2px;" class="{{activeTab === 'att-dice' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                                    (click)="switchToTab('att-dice')">Attribute - Dice Frustration </button>
                                    <button style="margin-left: 2px;" class="{{activeTab === 'know-dice' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                                    (click)="switchToTab('know-dice')">Knowledge - Dice Frustration </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <ng-container *ngIf="activeTab === 'att-check'">
                <app-attribute-check></app-attribute-check>
            </ng-container>
            <ng-container *ngIf="activeTab === 'Know-check'">
                <app-knowledge-check></app-knowledge-check>
            </ng-container>
            
    
            <ng-container *ngIf="activeTab === 'att-dice'">
                <app-attributce-dice-frustration></app-attributce-dice-frustration>
            </ng-container>
            <ng-container *ngIf="activeTab === 'know-dice'">
                <app-knowledge-dice-frustration></app-knowledge-dice-frustration>
            </ng-container>

        </div>
    </div>

