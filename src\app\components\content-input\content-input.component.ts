import { RpgService } from './../../services/rpg.service';
import { ChangeDetectionStrategy, Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild, OnDestroy } from '@angular/core';
import { language } from 'src/lib/darkcloud/dialogue-system/game-types';
import { fromEvent } from 'rxjs';
import { debounceTime } from 'rxjs/internal/operators/debounceTime';
import { SearchOnPageService } from 'src/app/services/search-on-page.service';

@Component({
  selector: 'app-content-input',
  templateUrl: './content-input.component.html',
  styleUrls: ['./content-input.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ContentInputComponent implements OnInit, OnDestroy {
  changeTextTimeOut: number;
  @Input() class: string;
  @Input() initialContent: string;
  @Input() innerText: string;
  @Input() language: language = 'PT-BR';
  @Output() focusing: EventEmitter<boolean> = new EventEmitter();
  @Output() contentChange: EventEmitter<string> = new EventEmitter();
  @Output() contentFormat: EventEmitter<any> = new EventEmitter();
  isInputFocused: boolean = false;
  content: string;

  @ViewChild('inputBox', { static: false }) inputBox: ElementRef;

  constructor(
    private _rpgService: RpgService,
    private _searchOnPageService: SearchOnPageService,
  ) {}

  ngOnInit(): void 
  {
    this.content = this.initialContent + '';
    this.innerText = this.initialContent + '';
  }

  ngAfterViewInit(): void 
  {
    fromEvent(this.inputBox.nativeElement, 'keyup')
      .pipe(debounceTime(0))
      .subscribe((value) => this.onChangeContent(this.inputBox?.nativeElement?.innerText));

    this._searchOnPageService.filterContent();
  }

  onChangeContent(content: string) 
  {
    this.content = content;
    this.initialContent = content;
    clearTimeout(this.changeTextTimeOut);
    this._rpgService.pauseSuggestionsGeneration();
    this.changeTextTimeOut = setTimeout(() => {
      this.contentChange.emit(content);
    });
    this._rpgService.resumeSuggestionsGeneration();
  }

  onFocus(isFocusing: boolean) 
  {
    this.isInputFocused = isFocusing;

    if (isFocusing) 
    {
      this.innerText = this.initialContent + '';
    } 

    this._searchOnPageService.filterContent();
    this.focusing.emit(isFocusing);
  }

  ngOnDestroy() 
  {
    clearTimeout(this.changeTextTimeOut);
  }
}
