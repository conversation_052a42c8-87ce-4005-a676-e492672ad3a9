import { Component, OnInit } from '@angular/core';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';
import { ItemClass } from 'src/app/lib/@bus-tier/models/ItemClass';
import { ItemService } from 'src/app/services';
import { CustomService } from 'src/app/services/custom.service';
import { ItemClassService } from 'src/app/services/item-class.service';

@Component({
  selector: 'app-memorymodule-class-selection',
  templateUrl: './memorymodule-class-selection.component.html',
  styleUrls: ['./memorymodule-class-selection.component.scss'],
})
export class MemoryModuleClassSelectionComponent implements OnInit {

  itemClasses: ItemClass[];
  custom: Custom;

  constructor(
    private _itemClassService: ItemClassService,
    private _itemService: ItemService,
    private _customService: CustomService,
  ) {

  }

  async ngOnInit(): Promise<void>
  {
    this.itemClasses = this._itemClassService.models;
    this.custom = await this._customService.svcGetInstance();
    if(!this.custom.memoryModuleClassItem)
    {
      this.custom.memoryModuleClassItem = [];
    }
  }

  async onItemClassSelected(itemClass: ItemClass)
  {
    this._customService.toggleItemClass(itemClass, 'memoryModuleClassItem');
    this.custom = await this._customService.svcGetInstance();
  }

}
