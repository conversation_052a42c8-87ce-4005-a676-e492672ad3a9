import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { StoryProgress } from './StoryProgress';

export class Speech
  extends StoryProgress<Data.Hard.ISpeech, Data.Result.ISpeech>
  implements Required<Data.Hard.ISpeech>
{
  public static generateId(storyBoxId: string, index: number): string {
    return storyBoxId + '.' + IdPrefixes.SPEECH + index;
  }
  constructor(
    args: { index: number; storyBoxId: string; clonee?: Speech },
    dataAccess: Speech['TDataAccess']
  ) {
    const id = Speech.generateId(args.storyBoxId, args.index);
    if (args.clonee != undefined) {
      super({ hard: args.clonee.hard, newId: id }, dataAccess);
    } else {
      super({ hard: { id } }, dataAccess);
    }
  }
  protected getInternalFetch() {
    return {};
  }
  public get message(): string {
    return this.hard.message;
  }
  public set message(value: string) {
    this.hard.message = value;
  }
  public get isIntroduction(): boolean {
    return this.hard.isIntroduction;
  }
  public set isIntroduction(value: boolean) {
    this.hard.isIntroduction = value;
  }
  public get speakerId(): string {
    return this.hard.speakerId;
  }
  public set speakerId(value: string) {
    this.hard.speakerId = value;
  }
  public get emotionId(): string {
    return this.hard.emotionId;
  }
  public set emotionId(value: string) {
    this.hard.emotionId = value;
  }
  public get audioId(): string {
    return this.hard.audioId;
  }
  public set audioId(value: string) {
    this.hard.audioId = value;
  }
  public get isReviewedMessage(): boolean {
    return this.hard.isReviewedMessage;
  }
  public set isReviewedMessage(value: boolean) {
    this.hard.isReviewedMessage = value;
  }
  public get revisionCounterMessageAI(): number {
    return this.hard.revisionCounterMessageAI;
  }
  public set revisionCounterMessageAI(value: number) {
    this.hard.revisionCounterMessageAI = value;
  }
}
