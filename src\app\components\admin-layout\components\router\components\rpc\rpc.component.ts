import { ChangeDetectorRef, Component } from '@angular/core';
import { RPCEscapeTable, RPCTableTributeAndSubmission } from 'src/app/lib/@bus-tier/models';
import { Button } from 'src/app/lib/@pres-tier/data';
import { RPCEscapeTableService, RPCTableTributeAndSubmissionService } from 'src/app/services';
import { Alert } from 'src/lib/darkcloud';

@Component({
  selector: 'app-rpc',
  templateUrl: './rpc.component.html',
  styleUrls: ['./rpc.component.scss'],
})
export class RpcComponent {
  
  public readonly excelButtonSubmission: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onExcelPasteTableSubmission.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  public readonly excelButtonEscale: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onExcelPasteEscapeTable.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  description: string;
  activeLanguage = 'PTBR';
  activeTab: string;
  rpcTableTributeAndSubmission: RPCTableTributeAndSubmission[] = [];
  rpcCEscapeTable: RPCEscapeTable[] = [];

  constructor(
    private ref: ChangeDetectorRef,
    private _rpCTableTributeAndSubmissionService: RPCTableTributeAndSubmissionService,
    private _rpCEscapeTableService: RPCEscapeTableService,
  ) {}

 ngOnInit() {
    this._rpCTableTributeAndSubmissionService.toFinishLoading();
    this._rpCEscapeTableService.toFinishLoading();
    this.rpcTableTributeAndSubmission = this._rpCTableTributeAndSubmissionService.models;
    this.rpcCEscapeTable = this._rpCEscapeTableService.models;
  
    this.description = `Showing ${this.rpcTableTributeAndSubmission.length} results`;  
  }

  async onExcelPasteTableSubmission() {
    try {
      const text = await navigator.clipboard.readText();
      const lines = text.split(/\r?\n/).filter((line) => line);

      // Limpar dados existentes ANTES do loop
      this._rpCTableTributeAndSubmissionService.models = [];
      await this._rpCTableTributeAndSubmissionService.toSave();

      // Processar cada linha do Excel
      for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
        const line = lines[lineIndex];
        const cols = line.split(/\t/).map((col) => col.trim());

        // Validação 1: Verificar se tem exatamente 6 colunas
        if (cols.length !== 6) {
          Alert.showError(
            `Error at line ${
              lineIndex + 1
            }: Content must contain exactly 6 columns. ${
              cols.length
            } columns found.`
          );
          return;
        }

        const submission = await this._rpCTableTributeAndSubmissionService.createNewTributeAndSubmission();
    
        // Atribuir valores das colunas do Excel aos campos correspondentes
        // Coluna 1: valueE
        submission.valueE = cols[0] || '';
        submission.tone = cols[1] || '';
        submission.description = cols[2] || ''; 
        submission.phraseOptionsA = cols[3] || '';
        submission.phraseOptionsB = cols[4] || '';
        submission.phraseOptionsC = cols[5] || '';

 
        this._rpCTableTributeAndSubmissionService.svcToModify(submission);
      }

      this.rpcTableTributeAndSubmission = this._rpCTableTributeAndSubmissionService.models;
      this.description = `Showing ${this.rpcTableTributeAndSubmission.length} results`;

      Alert.ShowSuccess('Excel copied successfully!');
      this.ref.detectChanges();
    } catch (error) {
      Alert.showError('Error importing data from Excel.');
      console.error(error);
    }
  }

  async onExcelPasteEscapeTable() {
    try {
      const text = await navigator.clipboard.readText();
      const lines = text.split(/\r?\n/).filter((line) => line);

      // Limpar dados existentes ANTES do loop
      this._rpCEscapeTableService.models = [];
      await this._rpCEscapeTableService.toSave();

      // Processar cada linha do Excel
      for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
        const line = lines[lineIndex];
        const cols = line.split(/\t/).map((col) => col.trim());

        // Validação 1: Verificar se tem exatamente 6 colunas
        if (cols.length !== 6) {
          Alert.showError(
            `Error at line ${
              lineIndex + 1
            }: Content must contain exactly 6 columns. ${
              cols.length
            } columns found.`
          );
          return;
        }

        const escapeTable = await this._rpCEscapeTableService.createNewRPCEscapeTable();

        // Atribuir valores das colunas do Excel aos campos correspondentes
        // Coluna 1: valueE
        escapeTable.valueE = cols[0] || '';
        escapeTable.tone = cols[1] || '';
        escapeTable.description = cols[2] || '';
        escapeTable.phraseOptionsA = cols[3] || '';
        escapeTable.phraseOptionsB = cols[4] || '';
        escapeTable.phraseOptionsC = cols[5] || '';

       this._rpCEscapeTableService.svcToModify(escapeTable);
      }  

      this.rpcCEscapeTable = this._rpCEscapeTableService.models;
      this.description = `Showing ${this.rpcCEscapeTable.length} results`;

      Alert.ShowSuccess('Excel copied successfully!');
      this.ref.detectChanges();
    } catch (error) {
      Alert.showError('Error importing data from Excel.');
      console.error(error);
    }
  }
}
