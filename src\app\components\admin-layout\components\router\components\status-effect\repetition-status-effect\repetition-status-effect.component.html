<ng-container *ngIf="listRepetiton.length > 0">

    <div style="display: flex; flex-direction: row; overflow-x: auto; width: 100%; justify-content: center;">
        <table class="table table-list borderList">
            <thead>
                <tr>
                    <th colspan="10">
                        <h3>Repetition</h3>
                    </th>
                </tr>
                <tr>
                    <th>Index</th>
                    <th *ngFor="let title of titles"> {{title}}</th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let item of listRepetiton; let i = index">
                    <td style="background-color: #ddd;">{{ i + 1 }}</td>
                    <ng-container *ngFor="let rep of titles; let r = index">
                        <td class="td-id">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                [style.background-color]="(item.positionNameRarity?.[r] | tierColor: 'Character Rarity')+ '!important'"
                                type="text" #repetition [ngClass]="{'empty-input': !repetition.value}"
                                [value]="item.positionNameRarity?.[r] || ''"
                                (change)="changeRarityValue(i, r, repetition.value)" />
                        </td>
                    </ng-container>
                </tr>
            </tbody>
        </table>
    </div>
</ng-container>

<ng-container *ngIf="listRepetiton.length === 0">
    <div class="card" style="text-align: center; padding: 20px;">
        <h3>Empty list. Click to create the list.</h3>
    </div>
</ng-container>