<!-- Weapons WLBase Row 2: Weapon type selection -->
<div class="card list-header" style="height: 70px; margin-top: 10px; margin-bottom: 0px; margin-left: 30px; margin-right: 30px;">
  <div class="header" style="display: flex; flex-direction: row; justify-content: space-between !important;">
    <div>
      <button class="{{weaponType === 'common' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
          (click)="switchToWeaponType('common')">
          Common Weapons
      </button>
      <button class="{{weaponType === 'special' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
          (click)="switchToWeaponType('special')" style="margin-left: 5px;">
          Special Weapons
      </button>
    </div>
  </div>
</div>

<!-- Weapons WLBase Row 3: Dynamic content based on weapon type -->
<div class="card list-header" style="height: 70px; margin-top: 10px; margin-bottom: 0px; margin-left: 30px; margin-right: 30px;">
  <div class="header" style="display: flex; flex-direction: row; justify-content: space-between !important;">
    <div>
      <button class="{{subActiveTab === 'item-class' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
          (click)="switchToSubTab('item-class')">
          1 - Item Class
      </button>
      <button class="{{subActiveTab === 'weapon-selection' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
          (click)="switchToSubTab('weapon-selection')" style="margin-left: 5px;">
          2 - {{weaponType === 'common' ? 'Common Weapon Selection' : 'Special Weapon Selection'}}
      </button>
    </div>
  </div>
</div>

<!-- Content based on sub-tab selection -->
<app-weapons-wlbase-class-selection *ngIf="subActiveTab === 'item-class'"></app-weapons-wlbase-class-selection>
<app-common-weapons *ngIf="subActiveTab === 'weapon-selection' && weaponType === 'common'"></app-common-weapons>
<app-special-weapons *ngIf="subActiveTab === 'weapon-selection' && weaponType === 'special'"></app-special-weapons>
