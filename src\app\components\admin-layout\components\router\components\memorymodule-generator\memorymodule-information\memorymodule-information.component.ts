import { Component, OnInit } from '@angular/core';
import { Item, MemoryModule } from 'src/app/lib/@bus-tier/models';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';
import { ItemService, ParticleService } from 'src/app/services';
import { CustomService } from 'src/app/services/custom.service';
import { FILTER_SUFFIX_PATH } from 'src/lib/darkcloud/angular/dsadmin/constants/others';
import { MemoryModuleService } from 'src/app/services/memorymodule.service';
import { ItemClassService } from 'src/app/services/item-class.service';
import { Button } from 'src/app/lib/@pres-tier/data';
import { Alert } from 'src/lib/darkcloud';
import { SpinnerService } from 'src/app/spinner/spinner.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-memorymodule-information',
  templateUrl: './memorymodule-information.component.html',
  styleUrls: ['./memorymodule-information.component.scss'],
})
export class MemoryModuleInformationComponent implements OnInit 
{ 
  public readonly excelButtonTemplate: Button.Templateable = 
  {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  public activeTab: string;
  custom: Custom;
  item: Item;
  memoryModules: MemoryModule[];
  particles: Item[] = [];
  description: string;
  selectedClasses = [];
  ids = [];
  particlesIds: string[] = [];
  particlesToStore: Item[] = [];
  particlesNames: string[] = [];
  constructor(
    private spinnerService: SpinnerService,
    protected _customService: CustomService,
    protected _itemService: ItemService,
    private _particleService: ParticleService,
    private _memoryModuleService: MemoryModuleService,
    private _itemClassService: ItemClassService,
    private _router: Router
  ) {}

 

  async ngOnInit(): Promise<void> 
  {
    await this._customService.toFinishLoading();
    await this._particleService.toFinishLoading();
    await this._itemService.toFinishLoading();
    await this._itemClassService.toFinishLoading();
    await this._memoryModuleService.toFinishLoading();

    this.custom = await this._customService.svcGetInstance();

    if (!this.custom.particlesOrder) 
      this.custom.particlesOrder = [];

    if (this.custom.particlesOrder.length != this._particleService.models.length) 
    {
      this.custom.particlesOrder = [];
      this._particleService.models.forEach((particle) => 
      {
        this.custom.particlesOrder.push(particle.id);
      });
    }

    this.selectMemoryClasses();
    this.intializeTab();
    this.description = `Showing ${this.memoryModules.length} results`;

    //item class pkg has a sub atomics particles array by id = IC0
    this._itemClassService.svcFindById('IC0').itemIds.forEach((itemId) => 
    {
      this.particlesToStore.push(this._itemService.svcFindById(itemId));
    });
    this.removeSubatomicsValues();
    this.takeParticlesNames();
    this._memoryModuleService.toSave();
  }

  takeParticlesNames() 
  {
    let names: string[] = [];
    this.particlesToStore.forEach((p) => names.push(p.name));

    if (this.memoryModules[0].particlesNames?.length === 0) 
    {
      this.memoryModules.forEach((m) => (m.particlesNames = names));
      this._memoryModuleService.toSave();

      this.particlesNames = this.memoryModules[0].particlesNames;
    } 
    else 
    {
      this.removeDeletedParticleFromParticlesNames(names);
      this.particlesToStore.sort((a, b) => 
      {
        return (this.memoryModules[0].particlesNames?.indexOf(a.name) - this.memoryModules[0].particlesNames?.indexOf(b.name));
      });
      this.particlesToStore.forEach((p) => this.particlesNames.push(p.name));
    }
  }

  removeDeletedParticleFromParticlesNames(names) 
  {
    this.memoryModules[0].particlesNames = this.memoryModules[0].particlesNames?.filter((p) => names.includes(p));
  }


  //Remove value from particlesValue when a subatomic value is removed from the project
  removeSubatomicsValues() 
  {
    let l = this.memoryModules[0].particlesNames?.length;
    for (let i = 0; i < this.memoryModules?.length; i++) {
      let len = this.memoryModules[i].particlesValue?.length - l;
      for (let l = 1; l <= len; l++) 
        this.memoryModules[i]?.particlesValue.pop();
    }
  }

  selectMemoryClasses() 
  {
    this.selectedClasses = this._itemClassService.models.filter((klass) => 
    {
      return this.custom.memoryModuleClassItem.includes(klass.id);
    });
    let itemIds = [];

    this.selectedClasses.forEach((itemClass) => 
    {
      itemClass.itemIds.forEach((itemId) => 
      {
        itemIds.push(this._itemService.svcFindById(itemId));
      });
    });

    this.ids = [];
    itemIds.forEach((id) => 
    {
      this.ids.push(id.id);
    });

    this.memoryModules = [];

    this._memoryModuleService.models.forEach((sw) => 
    {
      if (this.ids.includes(sw.itemId)) 
      {
        this.memoryModules.push(sw);
        this.ids = this.ids.filter((id) => id !== sw.itemId);
      }
    });

    this.ids.forEach((id) => 
    {
      this.memoryModules.push(this._memoryModuleService.createNewMemoryModule(id));
    });

    this._memoryModuleService.toSave();
    this.memoryModules.concat(this._memoryModuleService.models);
  }

  intializeTab() 
  {
    const tab = localStorage.getItem(`tab-MemoryModuleInformationComponent${FILTER_SUFFIX_PATH}`);
    this.activeTab = tab === 'null' || !tab ? 'class-selection' : tab;
  }

  GetParticleName(particleId: string): string 
  {
    return this._itemService.svcFindById(this._particleService.svcFindById(particleId).itemId).name;
  }

  async changeElementsPosition(newElementName, oldElementName) 
  {
    Alert.showError('', 'There is another value with the same name. Change it to avoid repetition');
    let newElementHolder;
    let oldElementHolder;

    for (let l = 0; l < this.particlesNames.length; l++) 
    {
      if (this.particlesNames[l] === newElementName) 
        newElementHolder = l;
      
      if (this.particlesNames[l] === oldElementName) 
        oldElementHolder = l;
    }
    
    let oldElement = this.particlesNames[oldElementHolder];
    let newElement = this.particlesNames[newElementHolder];
    this.particlesNames.splice(newElementHolder, 1, oldElement);
    this.particlesNames.splice(oldElementHolder, 1, newElement);

    this._memoryModuleService.models.forEach((m) => (m.particlesNames = this.particlesNames));
    this._memoryModuleService.models.forEach((m) => this._memoryModuleService.svcToModify(m));
    await this._memoryModuleService.toSave();
  }

  public switchToTab(tab: string) 
  {
    this.activeTab = tab;
    localStorage.setItem(`tab-MemoryModuleInformationComponent${FILTER_SUFFIX_PATH}`, this.activeTab);
  }

  GetMemoryModuleName(memoryModuleId: string): string 
  {
    return this._itemService.svcFindById( this._memoryModuleService.svcFindById(memoryModuleId).itemId).name;
  }

  public onBack() 
  {
    this._router.navigate(['weaponRecords']);
  }

  changeMemoryValue(memoryModule: MemoryModule, value: string, fieldName:string)
  {
    memoryModule[fieldName] = value == '' ? undefined : +value;
    this._memoryModuleService.svcToModify(memoryModule);
    this._memoryModuleService.toSave();
  }

  GetParticleValue(memoryModule: MemoryModule, index: number) 
  {
    if (!memoryModule.particlesValue) 
      memoryModule.particlesValue = [];
    
    return memoryModule.particlesValue[index];
  }

  changeParticleValue( memoryModule: MemoryModule, value: number, index: number) 
  {
    memoryModule.particlesValue[index] = value;
    this._memoryModuleService.svcToModify(memoryModule);
    this._memoryModuleService.toSave();
  }

  async onExcelPaste() 
  {
    this.spinnerService.setState(true);
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter((line) => line);
    if(this.DisplayErrors(lines)) return;

    for (let l = 0; l < lines.length; l++) 
    {
      let line = lines[l];
      let rows = line.split(/\t/);

      if (rows.length < 5) continue;

      let searchName = this.simplifyString(rows[0]);
      this.memoryModules.forEach((memory) => 
      {
        let item = this._itemService.svcFindById(memory.itemId);
        if (this.simplifyString(item.name) === searchName) 
          this.PasteStatsLine(memory, rows);
      });
    }

    this.spinnerService.setState(false);
  }

 async PasteStatsLine(memory, rows: string[]) 
 {   
    let memoryFields:string[] = ['labLevel', 'slots', 'qubits', 'nomenclature', 'creationSouls', 'creationTime', 'creationRubies', 'installationSouls', 
    'installationTime', 'installationRubies'];

    for(let i = 0; i < memoryFields.length; i++)
    {
      if (rows[i+1]?.trim()) 
      {
        memory[memoryFields[i]] = +rows[i+1]
        .split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',', '.');
      }
      else 
        memory[memoryFields[i]] = undefined;
    }
    
    let len = memoryFields.length + 1;
    for (let i = len; i < rows.length; i++) 
    {
      if (rows[i]?.trim()) 
      {
        memory.particlesValue[i - len] = +rows[i]
          .split(' ')
          .join('')
          .split('.')
          .join('')
          .replace(',', '.');
      } 
      else 
        memory.particlesValue[i - len] = undefined;
    }

    await  this._memoryModuleService.svcToModify(memory);
    await   this._memoryModuleService.toSave();
    Alert.ShowSuccess('Memory modules imported successfully!');
  }

  simplifyString(str: string): string 
  {
    return str.normalize('NFD')?.replace(/[\u0300-\u036f]/g, '')?.toLocaleUpperCase()?.trim();
  }

  DisplayErrors(array) 
  {
    let count = array[0].split(/\t/);
    if(count.length < 11 + this.particlesNames.length)
    {
      Alert.showError('You nedd to copy ALL ' + (11 + this.particlesNames.length).toString() + ' Columns! Including QUANTICAL MEMORY MODULE Column!');
      this.spinnerService.setState(false);
      return true;
    }

    if (!count[0]) {
      Alert.showError('You are probably copying MORE COLUMNS then necessary!');
      this.spinnerService.setState(false);
      return true;
    }

    if (count[0] === '') {
      Alert.showError('You are probably copying a blank column!');
      this.spinnerService.setState(false);
      return true;
    }

    return false;
  }
}
