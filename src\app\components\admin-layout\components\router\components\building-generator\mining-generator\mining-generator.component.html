<div class="card list-header"
     style="height: 70px; margin: 30px; margin-bottom: 0px;">
    <div class="header">
        <button class="{{activeTab === 'titanium' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab('titanium')">
            Titanium
        </button>
        <button class="{{activeTab === 'adamantium' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab('adamantium')" style="margin-left: 5px;">
            Adamantium
        </button>
        <button class="{{activeTab === 'hellnium' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab('hellnium')" style="margin-left: 5px;">
            Hellnium
        </button>
        <button style="position:relative; float:right"
                class="{{activeTab2 === 'storageC' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab2('storageC')">
            {{activeTab | titlecase}} Storage C
        </button>
        <button style="position:relative; float:right; margin-right: 5px;"
                class="{{activeTab2 === 'storageB' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab2('storageB')">
            {{activeTab | titlecase}} Storage B
        </button>
        <button style="position:relative; float:right; margin-right: 5px;"
                class="{{activeTab2 === 'storageA' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab2('storageA')">
            {{activeTab | titlecase}} Storage A
        </button>
        <button style="position:relative; float:right; margin-right: 5px;"
                class="{{activeTab2 === 'mining' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab2('mining')">
            {{activeTab | titlecase}} Mining
        </button>
    </div>
</div>

<app-titanium-generator *ngIf="activeTab === 'titanium'" [active]="activeTab2"></app-titanium-generator>

<app-adamantium-generator *ngIf="activeTab === 'adamantium'" [activeTab]="activeTab2"></app-adamantium-generator>

<app-hellnium-generator *ngIf="activeTab === 'hellnium'" [activeTab]="activeTab2"></app-hellnium-generator>