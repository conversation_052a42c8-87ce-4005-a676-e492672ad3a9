<div class="card">
  <div class="header">
    <h4 class="title">Keywords</h4>
  </div>
  <div class="content">
    <div class="row">
      <table class="table table-list">
        <tbody>
          <ng-container *ngFor="
              let keyword of keywords;
              let i = index;
              trackBy: trackByIndex
            ">
            <tr>
              <td id="{{ keyword.id }}"
                  class="td-sort">
                {{ keyword.id }}
              </td>
              <td class="td-auto">
                {{ keyword.key }}
              </td>
              <td class="td-80"
                  style="text-align: left">
                <input class="form-control form-short"
                       [(ngModel)]="keyword.word"
                       (ngModelChange)="onChange() " />
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>
