import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';
import { Tag } from './Tag';

export class Particle
  extends Base<Data.Hard.IParticle, Data.Result.IParticle>
  implements Required<Data.Hard.IParticle>
{
  protected static generateId(index: number): string {
    return IdPrefixes.PARTICLE + index;
  }

  constructor(
      index: number,
      itemId: string,
      dataAccess: Particle['TDataAccess']
  )
  {
      super({hard: {id: Particle.generateId(index), itemId}}, dataAccess);
  }

  public get itemId(): string
  {
    return this.hard.itemId;
  }
  public set itemId(value: string)
  {
    this.hard.itemId = value;
  }

  public get weaponId(): string
  {
    return this.hard.weaponId;
  }
  public set weaponId(value: string)
  {
    this.hard.weaponId = value;
  }

  public get description(): string
  {
    return this.hard.description;
  }
  public set description(value: string)
  {
    this.hard.description = value;
  }

  public get atk(): number
  {
    return this.hard.atk;
  }
  public set atk(value: number)
  {
    this.hard.atk = value;
  }

  public get hcLevel(): number
  {
    return this.hard.hcLevel;
  }
  public set hcLevel(value: number)
  {
    this.hard.hcLevel = value;
  }

  public get shake(): boolean
  {
    return this.hard.shake;
  }
  public set shake(value: boolean)
  {
    this.hard.shake = value;
  }

  public get hit(): boolean
  {
    return this.hard.hit;
  }
  public set hit(value: boolean)
  {
    this.hard.hit = value;
  }

  public get split(): number
  {
    return this.hard.split;
  }
  public set split(value: number)
  {
    this.hard.split = value;
  }

  public get classesId(): string[]
  {
    return this.hard.classesId;
  }
  public set classesId(value: string[])
  {
    this.hard.classesId = value;
  }

  public get charactersId(): string[]
  {
    return this.hard.charactersId;
  }
  public set charactersId(value: string[])
  {
    this.hard.charactersId = value;
  }

  public get effectId(): string
  {
    return this.hard.effectId;
  }
  public set effectId(value: string)
  {
    this.hard.effectId = value;
  }

}
