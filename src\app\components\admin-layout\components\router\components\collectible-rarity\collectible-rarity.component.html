
<ng-container *ngIf="!isCtrEvMax">
  <div class="main-div">
    <div class="container-fluid">
      <!--Header-->
      <div class="list-header-row update" style="z-index: 9999;">
        <div class="card">
          <div style="width: 100%; display: flex;">
            <div style="width: 50%;">
              <app-header-with-buttons [cardTitle]="listName" [cardDescription]="description">
              </app-header-with-buttons>
            </div>
            <div style="width: 50%;">
              <button class="{{activeTab === 'ctr-ev-max' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
              (click)="switchToTab('ctr-ev-max')" style="float: right; height: 40px;width: 70px; margin-top: 20px; margin-right: 62px;">
              <i _ngcontent-mut-c250="" class="pe-7s-config" style="font-size: 30px;position: relative;top: -5px;"></i>
            </button>
            </div>
          </div>
          <div
            style="display: flex; flex-direction: row; justify-content: space-between; margin-left: 50px; margin-right: 50px;">
            <div>
              <h4 style="display: inline-block;">Area</h4>
              <select class="dropdown filter-dropdown limited"
                style="display: inline-block; margin: 10px; margin-bottom: 15px; " #InputArea [(ngModel)]="selectedArea"
                (change)="filterArea(InputArea.value)">
                <option value="ALL">All</option>
                <option *ngFor="let area of areas" value="{{ area.id }}">{{ area.hierarchyCode }}{{" : "}}{{ area.name }}
                </option>
              </select>
            </div>
            <div>
              <h4 style="display: inline-block;">Type</h4>
              <select class="dropdown filter-dropdown limited"
                style="display: inline-block; margin: 10px; margin-bottom: 15px" #InputType [(ngModel)]="selectedType"
                (change)="filterType(InputType.value)">
                <option value="ALL">All</option>
                <option *ngFor="let type of types" value="{{ type }}">{{ type }}</option>
              </select>
            </div>
            <div>
              <h4 style="display: inline-block;">Collectible Rarity</h4>
              <select class="dropdown filter-dropdown limited"
                style="display: inline-block; margin: 10px; margin-bottom: 15px" #InputRarity [(ngModel)]="selectedRarity"
                (change)="filterRarity(InputRarity.value)">
                <option value="ALL">All</option>
                <option *ngFor="let rarity of rarityList" value="{{ rarity.name }}">
                  {{ rarity.name }}
                </option>
              </select>
            </div>
            <div>
              <h4 style="display: inline-block;">Class</h4>
              <select class="dropdown filter-dropdown limited"
                style="display: inline-block; margin: 10px; margin-bottom: 15px" #InputClass [(ngModel)]="selectedClass"
                (change)="filterClass(InputClass.value)">
                <option value="ALL">All</option>
                <option *ngFor="let class of classes" value="{{ class }}">{{ class }}</option>
              </select>
            </div>
          </div>
          <!-- 
           <app-header-search 
            (inputKeyup)="lstOnChangeFilter($event)"
            (searchOptions)="lstOnChangeFilterOptions($event)">
          </app-header-search>       
         -->
          <app-header-search (inputKeyup)="search($event)"
            (searchOptions)="lstOnChangeFilterOptions($event)"></app-header-search>
        </div>
      </div>
  
      <!--List-->
      <div class="card">
        <table class="table table-list">
          <thead class="sticky">
            <tr>
              <th>Index</th>
              <th class="th-clickable" (click)="sortListByName(charactersList, 'id')">ID</th>
              <th class="th-clickable" (click)="sortByQuantitySpDef()">Skill Defenses</th>
              <th class="th-clickable" (click)="sortListByName(charactersList, 'areaId')">Area</th>
              <th class="th-clickable" (click)="sortListByName(charactersList, 'name')">Name</th>
              <th class="th-clickable" (click)="sortListByName(charactersList, 'rarity')">Rarity</th>
              <th class="th-clickable" (click)="sortCollectibleRecord()">Record</th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let character of charactersList; let i = index; trackBy: trackById">
              <tr id="{{ character.id }}">
                <td class="td-sort">{{ i + 1 }}</td>
                <td class="td-id">{{ character.id }}</td>
                <td class="td-id">{{ getSkillDefenses(character)}}</td>
                <td class="td-id">{{ character.areaId | areaById }}</td>
                <td class="td-id">
                  <span style="font-size: 16px; color: rgb(56, 56, 56);">{{character.name}}</span>
                </td>
                <td>
                  <select class="selectRarity"
                    [ngStyle]="{'background-color': character.rarity | tierColor: 'Character Rarity'}"
                    [(ngModel)]="character.rarity" #InputRarity
                    (change)="changeSelectedRarity(character, InputRarity.value)">
                    <option></option>
                    <option *ngFor="let rarity of rarityList; let i = index" value="{{ rarity.name }}" [ngStyle]="{'background-color': rarity.name| tierColor : 'Character Rarity', color:'#fff'}">
                      {{ rarity.name}}
                    </option>
                  </select>
                </td>
                <td *ngIf="+character.type == 2 || +character.type == 3 || +character.type == 4" class="td-actions">
                  <div class="row middle" style="display: flex; width: 60px; margin-bottom: 2px;">
                    <!--1- Battle-->
                    <ng-container *ngIf="character.rarity !== 'Inferior'">
                      <span style="display: inline-block; margin: 2px;" class="notification-circle small"
                        [style.background-color]="checkIfBattle(character) ? 'rgb(29, 199, 234)': 'rgb(128, 128, 128)'"></span>
                    </ng-container>
                    <!--1- Battle Inferior-->
                    <ng-container *ngIf="character.rarity === 'Inferior'">
                      <span style="display: inline-block; margin: 2px;" class="notification-circle small"
                        [style.background-color]="checkBattleInferior(character) ? 'rgb(29, 199, 234)': 'rgb(128, 128, 128)'"></span>
                    </ng-container>
                    <!--2 - Elemental-->
                    <span style="display: inline-block; margin: 2px;" class="notification-circle small"
                      [style.background-color]="checkElemental(character) ? 'rgb(29, 199, 234)': 'rgb(128, 128, 128)'"></span>
                    <!--3 - Aliment Defenses-->
                    <span style="display: inline-block; margin: 2px;" class="notification-circle small"
                      [style.background-color]="checkIfAilmentDefenses(character) ? 'rgb(29, 199, 234)': 'rgb(128, 128, 128)'"></span>
                    <!--4 - Primal-->
                    <span style="display: inline-block; margin: 2px;" class="notification-circle small"
                      [style.background-color]="checkIfPrimal(character) ? 'rgb(29, 199, 234)': 'rgb(128, 128, 128)'"></span>
                    <!--5 - Special Skills-->
                    <span style="display: inline-block; margin: 2px;" class="notification-circle small"
                      [style.background-color]="checkIfSpecialSkill(character) ? 'rgb(29, 199, 234)': 'rgb(128, 128, 128)'"></span>
                  </div>
                  <button *ngIf="+character.type == 2 || 
                    +character.type == 3 || +character.type == 4" class="btn btn-primary btn-fill btn-remove"
                    (click)="selectCollectible(character)">
                    <i class="pe-7s-angle-right-circle"></i>
                  </button>
                </td>
                <td *ngIf="!character.isCollectible"></td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</ng-container>



<app-ctr-ev-max *ngIf="activeTab === 'ctr-ev-max'" (clickBtnMax)="clickBtnKnoledgeHandler($event)"></app-ctr-ev-max>