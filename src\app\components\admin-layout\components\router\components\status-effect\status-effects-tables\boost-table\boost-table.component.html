<div class="div-container">
    <div class="width: 2221px;">
        <table class="table table-list borderList">
            <thead>
                <tr>
                    <th [attr.colspan]="titles.length +1">
                        <h3>BOOST</h3>
                    </th>
                    <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'" class="addButton"
                        [buttonTemplates]="[excelButtonTemplate]">
                    </app-button-group>
                </tr>
                <ng-container *ngIf="!isListBoostEmpty">

                    <tr>
                        <th class="default-color">Order</th>
                        <th [ngClass]="(title === 'SKILL RESIST USER') ? 'skill-resist' : 'default-color'"
                            *ngFor="let title of titles">
                            {{title}}
                        </th>
                    </tr>

                </ng-container>
            </thead>
            <ng-container *ngIf="!isListBoostEmpty">
                <tbody>
                    <tr *ngFor="let item of listBoostTable; let i = index">
                        <td style="background-color: #ddd;">{{ i + 1 }}</td>
                        <td class="td-id aligTitle" style="width: 6%;">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #idBoost [ngClass]="{'empty-input': !idBoost.value}" [value]="item.idBoost"
                                (change)="changeBoost(i,'idBoost', idBoost.value)" />
                        </td>
                        <td class="td-id aligTitle" style="width: 110px;">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #idCategory [ngClass]="{'empty-input': !idCategory.value}"
                                [value]="item.category" (change)="changeBoost(i,'category', idCategory.value)" />
                        </td>
                        <td class="td-id aligTitle">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #idAffliction [ngClass]="{'empty-input': !idAffliction.value}"
                                [value]="item.idAffliction"
                                (change)="changeBoost(i, 'idAffliction', idAffliction.value)" />
                        </td>
                        <td class="td-id aligTitle" style="width: 110px;">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #status [ngClass]="{'empty-input': !status.value}" [value]="item.status"
                                (change)="changeBoost(i, 'status', status.value)" />
                        </td>
                        <td class="td-id aligTitle">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #skillResistUser [ngClass]="{'empty-input': !skillResistUser.value}"
                                [value]="item.skillResistUser"
                                (change)="changeBoost(i, 'skillResistUser', skillResistUser.value)" />
                        </td>
                        <td class="td-id aligTitle" style="width: 110px;">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #operator [ngClass]="{'empty-input': !operator.value}"
                                [value]="item.operator" (change)="changeBoost(i, 'operator',operator.value)" />
                        </td>
                        <td class="td-id aligTitle" style="width: 110px;">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #value [ngClass]="{'empty-input': !value.value}" [value]="item.value"
                                (change)="changeBoost(i, 'value', value.value)" />
                        </td>
                        <td class="td-id" style="width: 200px;">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #statusEffectName [ngClass]="{'empty-input': !statusEffectName.value}"
                                [value]="item.statusEffectName"
                                (change)="changeBoost(i, 'statusEffectName', statusEffectName.value)" />
                        </td>
                        <td class="td-id" style="width: 34%; word-break: break-word;">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #description [ngClass]="{'empty-input': !description.value}"
                                [value]="item.description"
                                (change)="changeBoost(i, 'description', description.value)" />
                        </td>
                        <td class="td-id aligTitle">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #powerPoints [ngClass]="{'empty-input': !powerPoints.value}"
                                [value]="item.powerPoints"
                                (change)="changeBoost(i, 'powerPoints', powerPoints.value)" />
                        </td>
                        <td class="td-id aligTitle">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #allBoost [ngClass]="{'empty-input': !allBoost.value}"
                                [value]="item.allBoost" (change)="changeBoost(i, 'allBoost', allBoost.value)" />
                        </td>
                        <td class="td-id aligTitle">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #duration [ngClass]="{'empty-input': !duration.value}"
                                [value]="item.duration" (change)="changeBoost(i, 'duration', duration.value)" />
                        </td>
                    </tr>
                </tbody>
            </ng-container>
            <ng-container *ngIf="isListBoostEmpty">
                <div class="card" style="text-align: center; padding: 20px;">
                    <h3>The list has not yet been imported.</h3>
                </div>
            </ng-container>
        </table>
    </div>

</div>