import { AfterViewInit, Component, OnInit } from '@angular/core';
import { ItemsSelector } from 'src/app/lib/@bus-tier/models';
import { Button } from 'src/app/lib/@pres-tier/data';
import { AreaService, ItemService, UserSettingsService } from 'src/app/services';
import { ItemClassService } from 'src/app/services/item-class.service';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { Alert } from 'src/lib/darkcloud';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/SortableListComponent';
import * as XLSX from 'xlsx';
import { SpinnerService } from '../../../../../../../../spinner/spinner.service';
import { ItemsSelectorService } from './../../../../../../../../services/items-selector.service';
import { ActivatedRoute } from '@angular/router';

export interface IClassItensSelector {
  id?: string,
  name?: string
}

@Component({
  selector: 'app-items-selector',
  templateUrl: './items-selector.component.html',
  styleUrls: ['./items-selector.component.scss'],
})

export class ItemsSelectorComponent extends SortableListComponent<ItemsSelector> implements AfterViewInit, OnInit
{
  itemsSelector:any[] = [];
  selectorSection:any[] = [];
  inThisPage: boolean = false;
  loadingSpinner: Boolean = false;
  isSelectorItem: boolean = false;
  sortByNameOrder = -1;
  description;
  itemsTest:any[] = [];
  noExistClassItens = [];
  existItens = [];  
  existClassItens = [{id : '', name: '', class_name: ''}]; 
  ClassItens = [{id : '', name: ''}];      
  caseSentitive;
  accentSentitive;

  public readonly excelButtonTemplate: Button.Templateable = 
  {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  public readonly exportExcelButtonTemplate: Button.Templateable = 
  {
    btnClass: [...Button.Klasses.FILL_BLUE, 'Excel'],
    title: 'Export to Excel',
    onClick: this.downloadAsExcel.bind(this),
    iconClass: 'pe-7s-cloud-download',
  };
    
  constructor(
    private _spinnerService:SpinnerService,
    public _areaService:AreaService,
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    private _itemsSelectorService: ItemsSelectorService,
    public _itemClassService: ItemClassService,
    private _itemService:ItemService,
    protected _translationService: TranslationService,
    protected _languageService: LanguageService,
  ) 
  {
    super(_itemsSelectorService, _activatedRoute, _userSettingsService, 'name');
  }
 public  override async ngOnInit(): Promise<void>
 {
    await this.removeOrphanItems();  
    return null; 
 }
  public override async ngAfterViewInit(): Promise<void> 
  {
    
    this.loadingSpinner = true;
    this.inThisPage = true;
    setTimeout(()=>
    {
      if(this._itemsSelectorService.models.length == 0)
      {        
        this._itemClassService.models.forEach(itemsClass => 
        {
          itemsClass.itemIds.forEach(item => 
          {          
            let ite = this._itemsSelectorService.createNewItemsSelector();
            ite.description = undefined;
            ite.itemName = this._itemService.models.find((c) => c.id === item).name;
            ite.concept = false;
            ite.finish = false;
            ite.itemsSection = itemsClass.name;
            this.itemsSelector.push(ite);
          })
        })             
      }
      else
      {       
        this.CheckItemClass();            
      }
       
    },1000)   
    
    return null;
  }

  //Get the name of the class item
    CheckItemClass () {    
    const itemsMissions = this._itemClassService.models.filter((x) => x.itemIds);
     for (let index = 0; index < itemsMissions.length; index++) {
      this._itemService.models.filter(item => itemsMissions[index].itemIds.includes(item.id)).forEach((item) => 
        {              
          this.existClassItens.push({id: item?.id, name: item?.name, class_name: itemsMissions[index].name }); 
  
          //Add the name in the Selector
         return this._itemsSelectorService.models.filter((x) => {        
            if (x.itemName === item.name) {
               x.ic_id = item?.id;
               this.existItens.push(x);         
            }                   
          });
        });
       }
      //Remove empty fields
       this.ClassItens =  this.existClassItens.filter((ob) => ob.id != '' && ob.name != '')

       
       // localiza os itens ausênte
       if (this.existItens.length !== this.ClassItens.length) {
          this.noExistClassItens = this.ClassItens.filter((missionItem) => 
         !this._itemsSelectorService.models.some((modelItem) => modelItem.ic_id === missionItem.id));
          this.isSelectorItem =  true;
          this.addItemsClassMissing();
       } 

       if(!this.isSelectorItem) this.getItemsClass();      
    }

    getItemsClass() {
    this.removeOrphanItems();
    let select = [];
    this._itemsSelectorService.models.forEach(itemSel =>
        {        
          if(itemSel.ic_id) this.itemsSelector.push(itemSel);  
        }); 

        this.loadingSpinner = false
        this.ngOnInit();
        this.description = `Showing ${this.itemsSelector.length} results`;
       const valor = this._itemsSelectorService.models.filter((x) => x.itemsSection === "UPGRADES")      
    }

  async addItemsClassMissing() {
      
      this.noExistClassItens.forEach((item) => {
        let ite = this._itemsSelectorService.createNewItemsSelector();
        ite.description = undefined;       
        ite.itemName = item.name;
        ite.ic_id = item.id;
        ite.concept = false;
        ite.finish = false;
        ite.itemsSection = item.class_name;       
        this._itemsSelectorService.svcToModify(ite);                         
      });     
      this.noExistClassItens = [];
     // this.removeOrphanItems();      
      this.isSelectorItem = false;
      return;    
  }

  sortElements(value:string)
  {
    let notChecked = [];
    let checked = [];

    for(let i = 0; i < this.itemsSelector.length; i++)
    {
      if(this.itemsSelector[i][value] == true)
      {
        checked.push(this.itemsSelector[i]);
      }
      else
      {
        notChecked.push(this.itemsSelector[i]);
      }
    }

    if(this.itemsSelector[0][value] == false) this.itemsSelector = [].concat(checked).concat(notChecked);
    else this.itemsSelector = [].concat(notChecked).concat(checked);
  }

  sortByName(value) 
  {
    this.sortByNameOrder *= -1;
    this.itemsSelector.sort((a, b) => 
    {
      return this.sortByNameOrder *  a[value]?.localeCompare(b[value]);
    });
  }
   
   async onChangeCheckbox(value:any, key:string)
  {
    if(value[key] == undefined) value[key] = true;    
    else value[key] = !value[key];
    
    await this._itemsSelectorService.svcToModify(value);
    await this._itemsSelectorService.toSave();
  }

  async onChangeNotes(charSelector:any, description:any)
  {
    if(charSelector.description == undefined) charSelector.description = description;      
    else charSelector.description = description;
    
    await this._itemsSelectorService.svcToModify(charSelector);
    await this._itemsSelectorService.toSave();
  }

  onChangeArea(areaName)
  {
    this.itemsSelector = [];
         
    if(areaName == 'All')
    {
      this._itemsSelectorService.models.forEach(itemSel =>
      {
        if(itemSel.ic_id) this.itemsSelector.push(itemSel);  
      })
      this.description = `Showing ${this.itemsSelector.length} results`;
      return;
    }

    this._itemsSelectorService.models.forEach(itemSel =>
    { 
      if(areaName == itemSel.itemsSection && itemSel.ic_id) this.itemsSelector.push(itemSel);      
    })
    this.description = `Showing ${this.itemsSelector.length} results`;
}

  search(value)
  {
    if(value == '') 
    {
      this.itemsSelector = [];
      this._itemsSelectorService.models.forEach(itemSel =>
      {
        this.itemsSelector.push(itemSel);
      })
    } 

    if(!this.caseSentitive && !this.accentSentitive)
      this.itemsSelector = this._itemsSelectorService.models.filter(char=> 
        this.simplifyString(char.itemName)?.includes(this.simplifyString(value)) || 
        this.simplifyString(char.description)?.includes(this.simplifyString(value)));
        
    else if(this.caseSentitive && this.accentSentitive)
      this.itemsSelector = this._itemsSelectorService.models.filter(char=> 
        char.itemName?.includes(value) || char.description?.includes(value));

    else if(this.caseSentitive && !this.accentSentitive)
      this.itemsSelector = this._itemsSelectorService.models.filter(char=> 
        this.simplifyString(char.itemName)?.toUpperCase().includes(this.simplifyString(value).toUpperCase()) || 
        this.simplifyString(char.description)?.toUpperCase().includes(this.simplifyString(value).toUpperCase()));

    else if(!this.caseSentitive && this.accentSentitive)
      this.itemsSelector = this._itemsSelectorService.models.filter(char=> 
        char.itemName?.toUpperCase().includes(value.toUpperCase()) ||  
        char.description?.toUpperCase().includes(value.toUpperCase()));

      this.description = `Showing ${this.itemsSelector.length} results`;
    
  }

  searchConditions(value)
  {
    if(value.caseSensitive == true && value.accentSensitive == false)
    {
      this.caseSentitive = true;
      this.accentSentitive = false;
    }
    else if (value.accentSensitive == true && value.caseSensitive == false)
    {
      this.accentSentitive = true;
      this.caseSentitive = false;
    }
    else if(value.caseSensitive == true  && value.accentSensitive == true )
    {
      this.accentSentitive = true;
      this.caseSentitive = true;
    }
    else
    {
      this.accentSentitive = false;
      this.caseSentitive = false;
    }
  }

  async onExcelPaste(): Promise<void> 
  {
    this._spinnerService.setState(true);
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter(line => line);
    if(this.DisplayErrors(lines, 4)) return;

    for (let l = 0; l < lines.length; l++) 
    {
      let line = lines[l];
      let cols = line.split(/\t/);
      let item = this.itemsSelector.find((i) => this.simplifyString(i.itemName) == this.simplifyString(cols[0]));
      
      if (!item) continue;
      
      if (cols[1]?.trim()) 
      {
        item.description = cols[1]
      } 
      else 
      {
        item.description = undefined;
      }
    
      if (cols[2]?.trim()) 
      {
        item.concept = cols[2]
        .split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.') == 'X' ? true : false;
      } 
      else 
      {
        item.concept = undefined;
      }

      if (cols[3]?.trim()) 
      {
        item.finish = cols[3]
        .split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.') == 'X' ? true : false;
      } 
      else 
      {
        item.finish = undefined;
      }

      await this._itemsSelectorService.svcToModify(item);
      await this._itemsSelectorService.toSave();
    }
    this._spinnerService.setState(false);
    this.description = `Showing ${this.itemsSelector.length} results`;
    this.ngOnInit();
  }

  override simplifyString(str: string): string 
  {
    return (str?.normalize('NFD')?.replace(/[\u0300-\u036f]/g, '')?.toLocaleUpperCase())?.trim();
  }

  DisplayErrors(array, length:number) 
  {
    let count = array[0].split(/\t/);
    if (count.length < length) 
    {
      Alert.showError('Copy ALL '+ length +' columns!');
      this._spinnerService.setState(false);
      return true;
    }

    if (count[0] === '') 
    {
      Alert.showError('You are probably copying a blank column!');
      this._spinnerService.setState(false);
      return true; 
    }

    return false;
  }

  public downloadAsExcel() 
  {
    const characterTable = document.createElement('table');
    const tHead = characterTable.createTHead();
    const tHeadRow = tHead.insertRow();
    tHeadRow.insertCell().innerText = 'Name';
    tHeadRow.insertCell().innerText = 'Notes';
    tHeadRow.insertCell().innerText = 'Concept';
    tHeadRow.insertCell().innerText = 'Finish';
    tHeadRow.insertCell().innerText = 'Item Section';

    const tBody = characterTable.createTBody();

    this.itemsSelector.forEach(item =>
    {
      const tBodyRow = tBody.insertRow();
      tBodyRow.insertCell().innerText = item.itemName;
      tBodyRow.insertCell().innerText = item.description == undefined ? '' : item.description;
      tBodyRow.insertCell().innerText = item.concept ? 'X' : '';
      tBodyRow.insertCell().innerText = item.finish ? 'X' : '';
      tBodyRow.insertCell().innerText = item.itemsSection;
    })

    const ws: XLSX.WorkSheet = XLSX.utils.table_to_sheet(characterTable);
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Item Selector List');

    const now = new Date();
    XLSX.writeFile(wb, now.toLocaleDateString() + '_' + now.toLocaleTimeString() + ' DSAdmin Item Selector List.xlsx');
  }

  async removeOrphanItems()
  {
    for(let j = 0; j < this._itemsSelectorService.models.length; j++)
    {
      for(let i = 0; i < this._itemService.models.length; i++)
      {
        if(this.doesItemselectorExistsInItems(i, j)) break;        
        if(i == this._itemService.models.length-1)
        {
          await this._itemsSelectorService.svcToRemove(this._itemsSelectorService.models[j]?.id);
        }
      }
    }
  }

  doesItemselectorExistsInItems = (i:number, j:number):boolean => 
    this._itemService.models[i].name.toLowerCase() == this._itemsSelectorService.models[j].itemName.toLowerCase();
}