import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { Base } from './Base';

export class Spoke<PERSON>lace extends Base<Data.Hard.ISpokePlace, Data.Result.ISpokePlace> implements Required<Data.Hard.ISpokePlace>
{
  public static generateId(index: number): string 
  {
    return IdPrefixes.LEVELSPOKEPLACE + index;
  }
  constructor( index: number, dataAccess: SpokePlace['TDataAccess']) 
  {
    super(
    {
      hard: 
      {
        id: SpokePlace.generateId(index),     
      },
    },
    dataAccess
    );
  }
  protected getInternalFetch() 
  {
    return {};
  }

  public get elementId(): string 
  {
    return this.hard.elementId;
  }
  public set elementId(value: string) 
  {
    this.hard.elementId = value;
  }
  public get text(): string 
  {
    return this.hard.text;
  }
  public set text(value: string) 
  {
    this.hard.text = value;
  }
  public get originalLabel(): string 
  {
    return this.hard.originalLabel;
  }
  public set originalLabel(value: string) 
  {
    this.hard.originalLabel = value;
  }
  public get component(): string 
  {
    return this.hard.component;
  }
  public set component(value: string) 
  {
    this.hard.component = value;
  }
}
