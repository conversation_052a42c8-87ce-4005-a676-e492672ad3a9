
<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons [cardTitle]="'Memory Module List'"
        [cardDescription]="listDescription"></app-header-with-buttons>
        <app-header-search (inputKeyup)="lstOnChangeFilter($event)" (searchOptions)="lstOnChangeFilterOptions($event)"></app-header-search>
      </div>
    </div>
    <!--List-->
    <div class="card">
      <table class="table table-list">
        <thead class="sticky">
          <tr>
            <th>Index</th>
            <th class="th-clickable"
            (click)="sortListById()">ID</th>
            <th style="min-width: 150px;"  class="th-clickable"
            (click)="sortListByName()">Name & Description</th>
            <th class="th-clickable"
            (click)="sortListByWeapon()">Weapon Record</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let item of itemList; let i = index; trackBy: trackByIndex">
            <tr id="{{ item.id }}">
              <td class="td-sort">{{ i+1 }}</td>
              <td class="td-id">{{ item.id }}</td>
              <td class="td-notes">
                <!-- <span class="form-control form-short"
                       type="text"
                       *ngIf="lstLanguage == 'PT-BR'">{{ item.name }}</span> -->
                <span class="form-control form-short"
                       type="text"
                       >{{ (item | translation : lstLanguage : item.id : 'name') }}</span>
                <!-- <span class="form-control"
                          type="text"
                          *ngIf="lstLanguage == 'PT-BR'">{{ item.description }}
                  </span> -->
                <span class="form-control"
                          type="text"
                          >{{ (item | translation : lstLanguage : item.id : 'description') }}
                  </span>
              </td>
              <td class="td-actions">
                <div class="row middle"
                style="display: flex; width: 18px; margin-bottom: 2px;">
                    <span
                      style="display: inline-block; margin: 2px;"
                      class="notification-circle small"
                      [style.background-color]="checkIfParticleIsCompleted(item, 1)"></span>
                    <!-- <span
                      style="display: inline-block; margin: 2px;"
                      class="notification-circle small"
                      [style.background-color]="checkIfParticleIsCompleted(item, 2)"></span>
                    <span
                      style="display: inline-block; margin: 2px;"
                      class="notification-circle small"
                      [style.background-color]="checkIfParticleIsCompleted(item, 3)"></span> -->
              </div>
                <button class="btn btn-primary btn-fill btn-remove"
                        (click)="selectWeapon(item)">
                  <i class="pe-7s-angle-right-circle"></i>
                </button>
              </td>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>
