import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ItemClass } from 'src/app/lib/@bus-tier/models/ItemClass';
import { UserSettingsService } from 'src/app/services';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { FILTER_SUFFIX_PATH } from 'src/lib/darkcloud/angular/dsadmin/constants/others';
import { ItemClassService } from 'src/app/services/item-class.service';
import { CustomService } from 'src/app/services/custom.service';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';


@Component({
  selector: 'app-item-details-weapons',
  templateUrl: './item-details-weapons.component.html',
})
export class ItemDetailsComponent implements OnInit
{

  constructor(
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    protected _translationService: TranslationService,
    protected _languageService: LanguageService,
    private _itemClassService: ItemClassService,
    private _customService: CustomService
  ) {

  }

  public itemClasses: ItemClass[] = [];
  public reviewOrderAscending: boolean = false;
  public activeTab: string;
  public custom: Custom;

  async ngOnInit(): Promise<void> {
    const tab = localStorage.getItem(
      `tab-ItemDetailsComponent${FILTER_SUFFIX_PATH}`
    );
    this.activeTab = tab === 'null' || !tab ? 'weapons' : tab;

    await this._itemClassService.toFinishLoading();
    await this._customService.toFinishLoading();
    this.custom = await this._customService.svcGetInstance();

    // Auto-select item classes when weapons tab is active
    if (this.activeTab === 'weapons') {
      await this.autoSelectWeaponClasses();
    }
  }

  public async switchToTab(tab: string) {
    this.activeTab = tab;
    localStorage.setItem(
      `tab-ItemDetailsComponent${FILTER_SUFFIX_PATH}`,
      this.activeTab
    );

    // Auto-select item classes when switching to weapons tab
    if (tab === 'weapons') {
      await this.autoSelectWeaponClasses();
    }
  }

  private async autoSelectWeaponClasses() {
    if (!this.custom.weaponClassItem) {
      this.custom.weaponClassItem = [];
    }

    // Find "ARMAS COMUNS" and "ARMAS ESPECIAIS" classes
    const armasComunsClass = this._itemClassService.models.find((x) => x.name === "ARMAS COMUNS");
    const armasEspeciaisClass = this._itemClassService.models.find((x) => x.name === "ARMAS ESPECIAIS");

    let needsUpdate = false;

    // Auto-select "ARMAS COMUNS" if not already selected
    if (armasComunsClass && !this.custom.weaponClassItem.includes(armasComunsClass.id)) {
      this.custom.weaponClassItem.push(armasComunsClass.id);
      needsUpdate = true;
    }

    // Auto-select "ARMAS ESPECIAIS" if not already selected
    if (armasEspeciaisClass && !this.custom.weaponClassItem.includes(armasEspeciaisClass.id)) {
      this.custom.weaponClassItem.push(armasEspeciaisClass.id);
      needsUpdate = true;
    }

    if (needsUpdate) {
      this._customService.svcToModify(this.custom);
    }
  }

}
