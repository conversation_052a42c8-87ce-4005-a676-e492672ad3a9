<div class="card list-header" style="height: 70px; margin-top: 10px; margin-bottom: 0px; margin-left: 30px; margin-right: 30px;">
    <div class="header">
        <button class="{{activeTab === 'classList' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
        (click)="switchToTab('classList')">
          Class List
        </button>
        <button class="{{activeTab === 'classModifier' ? 'btn btn-fill selectedButton' : 'btn btn-fill' }}"
        (click)="switchToTab('classModifier')" style="margin-left: 5px;">
          Class Modifier (CM) 
        </button>
        <button class="{{activeTab === 'situationalModifier' ? 'btn btn-fill selectedButton' : 'btn btn-fill' }}"
        (click)="switchToTab('situationalModifier')" style="margin-right: 30px; margin-left: 5px;">
           Situational Modifier (SM)
        </button>
        <button class="{{activeTab === 'modMoonRanges' ? 'btn btn-fill selectedButton' : 'btn btn-fill' }}"
        (click)="switchToTab('modMoonRanges')" style="margin-right: 30px;">
          MODMoon Ranges
        </button>
        <button class="{{activeTab === 'parryPry' ? 'btn btn-fill selectedButton' : 'btn btn-fill' }}"
        (click)="switchToTab('parryPry')" style="margin-right: 30px;">
          Parry (Pry)
        </button>

          <button class="{{activeTab === 'relicUses' ? 'btn btn-fill selectedButton' : 'btn btn-fill' }}"
          (click)="switchToTab('relicUses')" style="float: right;">
          Relic Uses
          </button>
          <button style="float: right; position: relative; height: 40px; width: 70px; margin-right: 10px;" 
           class="btn btn-fill" (click)="switchToTab('modMoonAttributes')" title="MOD Moon Attributes">
          <i class="pe-7s-config" style="font-size: 30px; position: relative; top: -5px;"></i>
        </button>

    </div>
</div>

<app-class-list *ngIf="activeTab === 'classList'"></app-class-list>
<app-class-modifier *ngIf="activeTab === 'classModifier'"></app-class-modifier>
<app-situational-modifier *ngIf="activeTab === 'situationalModifier'"></app-situational-modifier>
<app-relic-Uses *ngIf="activeTab === 'relicUses'"></app-relic-Uses>
<app-modmoon-attributes *ngIf="activeTab === 'modMoonAttributes'"></app-modmoon-attributes>
<app-modmoon-ranges *ngIf="activeTab === 'modMoonRanges'"></app-modmoon-ranges>
<app-parry-pry *ngIf="activeTab === 'parryPry'"></app-parry-pry>
