import { Component, OnInit } from "@angular/core";
import { FILTER_SUFFIX_PATH } from "src/lib/darkcloud/angular/dsadmin/constants/others";

@Component({
    selector:'app-classes-view',
    templateUrl: './classes.component.html'
})

export class ClassesComponent implements OnInit {

    public activeTab: string;

    ngOnInit(): void {
        const tab = localStorage.getItem(`tab-ClassesComponent${FILTER_SUFFIX_PATH}`);
        this.activeTab = tab === 'null' || !tab ? 'classList' : tab;
    }

    public switchToTab(tab: string) {
        this.activeTab = tab;
        localStorage.setItem(`tab-ClassesComponent${FILTER_SUFFIX_PATH}`,
            this.activeTab
        );
    }
}