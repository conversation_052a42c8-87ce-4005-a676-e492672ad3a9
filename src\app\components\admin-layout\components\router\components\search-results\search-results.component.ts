import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { Dialogue, Level } from 'src/app/lib/@bus-tier/models';
import { byAreaAndLevelByExtraction } from 'src/app/lib/@bus-tier/sorting';
import { OptionService, SpeechService, StoryBoxService } from 'src/app/services';
import { AreaService } from 'src/app/services/area.service';
import { ItemClassService } from 'src/app/services/item-class.service';
import { LevelService } from 'src/app/services/level.service';
import { SearchService } from 'src/app/services/search.service';
import { links } from 'src/lib/darkcloud/angular/dsadmin/constants/url';
import { Sorting } from 'src/lib/darkcloud/angular/easy-mvc/Sorting';
import { Search } from 'src/lib/darkcloud/index';

@Component({
  selector: 'app-search-results',
  templateUrl: './search-results.component.html',
  styleUrls: ['./search-results.component.scss'],
})
export class SearchResultsComponent implements OnInit, OnDestroy 
{
  ResultType = Search.ResultType;
  public term = '';
  public results: Search.Result[] = [];
  public allResults: Search.Result[] = [];
  private _showAllResults: boolean;
  public subscription: Subscription;
  public preloadedResultTypes = Search.ResultTypes;
  public preloadedParams = [];
  public resultType: string | number = 'ALL';
  public resultParam = 'ALL';
  public filterTypeName: string = '';
  private srtLstParameter: string;
  protected srtLstOrder: Sorting.Order = 'ascending';

  constructor(
    private _searchService: SearchService,
    private _router: Router,
    private _areaService: AreaService,
    private _levelService: LevelService,
    private _itemClassService: ItemClassService,
    private _speechService: SpeechService,
    private _optionService: OptionService,
    private _storyboxService: StoryBoxService
  ) {}

  ngOnInit(): void 
  {
    this.subscription = this._searchService.subjectResults.subscribe(
    (results) => 
    {
      this.resultParam = this._searchService.resultParam || this.resultParam;
      this.allResults = results.sort((a, b) =>
        byAreaAndLevelByExtraction(this._searchService.getObj(a), this._searchService.getObj(b),
        this._areaService, this._levelService, this.srtLstOrder !== 'ascending'));
      this.allResults.forEach((t) => 
      {
        this.preloadedParams.push(t.field);
      });

      this.preloadedParams = this.preloadedParams.filter((t, i, s) => s.indexOf(t) === i);
      this.filterSearchResults();
    });

    this.subscription.add(this._searchService.subjectTerm.subscribe((value) => 
    {
      this.term = value;
    }));
    this.filterSearchResults();
    this._searchService.loadLastSearch();
  }

  public filterSearchResults()
  {
    const filteredResults = this.resultParam === 'ALL' ? this.allResults : this.allResults.filter((t) => t.field === this.resultParam);
    let aux = [];
    if (this.resultType === 'ALL')    
      aux = this._showAllResults ? filteredResults : filteredResults.slice(0, 50);
    
    else 
      aux = this._showAllResults ? filteredResults.filter((result) => +result.type === +this.resultType)
      : filteredResults.filter((result) => +result.type === +this.resultType).slice(0, 50);
    

    this.results = [];
    this.results = aux;  
  }

  public sortListByParameter(parameter: string, index: number = undefined) 
  {
    if (this.srtLstParameter === parameter) 
    {
      this.srtLstOrder = this.srtLstOrder === 'descending' ? 'ascending' : 'descending';
    } 
    else this.srtLstOrder = 'descending';
    this.sort(parameter);
    this.results = this._showAllResults ? this.allResults : this.allResults.slice(0, 50);
    this.srtLstParameter = parameter;
  }

  private sort(p: string) 
  {
    switch (p) 
    {
      case 'hierarchyCode':
        this.allResults.sort((a, b) =>
          byAreaAndLevelByExtraction(
            this._searchService.getObj(a),
            this._searchService.getObj(b),
            this._areaService,
            this._levelService,
            this.srtLstOrder !== 'ascending'));
        break;
      case 'accuracy':
        this.allResults.sort((a, b) => this.srtLstOrder === 'ascending' ? Search.Accuracy[a.type] > Search.Accuracy[b.type] ?
        -1 : 1 : Search.Accuracy[a.type] > Search.Accuracy[b.type] ? 1 : -1);
        break;
    }
  }

  trackByIndex(index: number, item: any): any 
  {
    return index;
  }

  public toggleShowAllResults() 
  {
    this._showAllResults = !this._showAllResults;
    this.filterSearchResults();
  }

  public stopSearch()
  {
    this._searchService.clearPendingResults();
  }

  public toggleDisplayRemovedResults() 
  {
    this._searchService.toggleDisplayRemovedResults();
  }

  public toggleSearcherFormattingSetting() 
  {
    this._searchService.toggleSearcherFormattingSetting();
  }

  public toggleSearcherCaseSetting() 
  {
    this._searchService.toggleSearcherCaseSetting();
  }

  public toggleSearcherAccentSetting() 
  {
    this._searchService.toggleSearcherAccentSetting();
  } 

  public search() 
  {
    this._searchService.searchFor(this.term);
  }

  public redirectTo(result: Search.Result) 
  {
    if (result.wasRemoved) return;  
    let link: string = links[result.typeName].path;
 
    if(result.id.includes('ML'))
      link = 'microloops/' + result.id.split('.')[0] + '.' + result.id.split('.')[1] + 
      '/dialogues/' + result.id.split('.')[0] + '.' + result.id.split('.')[1] + '.' + result.id.split('.')[2];

    const obj = this._searchService.getObj(result);
    if (obj && !result.id.includes('ML')) 
    {
      if (link.includes(':levelId'))      
        link = link.replace(':levelId', Level.getSubIdFrom(obj.id));
      
      if (link.includes(':dialogueId'))      
        link = link.replace(':dialogueId', Dialogue.getSubIdFrom(obj.id, 'PT-BR'));
      
      if (link.includes('boundItems')) 
      {
        let itemClassId = this._itemClassService.models.filter(itemClass => itemClass.itemIds.includes(obj.id))[0]?.id
        this._router.navigate([link, {id: itemClassId}], { fragment: obj?.id });
        return;
      }
    }
    this._router.navigate([link], { fragment: obj?.id });
  }

  ngOnDestroy() 
  {
    this.subscription.unsubscribe();
    this.stopSearch();
  }
}
