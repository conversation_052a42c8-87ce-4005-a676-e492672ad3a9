.center
{
    margin: auto;
}

$mynewcolor: #000000;

.card_answer_invest {
  background-color: #c9e2de;
}

.btn-mi
{
    background-color: #ed7d31;
    border-color: #ed7d31;
}
.btn-mi:hover, 
.btn-mi:focus, 
.btn-mi:active, 
.btn-mi.active, 
.open .dropdown-toggle.btn-mi { 
  color: #ffffff; 
  background-color: #F09846; 
  border-color: #FC9B40; 
} 
 
.btn-mi:active, 
.btn-mi.active, 
.open .dropdown-toggle.btn-mi { 
  background-image: none; 
} 
 
.btn-mi.disabled, 
.btn-mi[disabled], 
fieldset[disabled] .btn-mi, 
.btn-mi.disabled:hover, 
.btn-mi[disabled]:hover, 
fieldset[disabled] .btn-mi:hover, 
.btn-mi.disabled:focus, 
.btn-mi[disabled]:focus, 
fieldset[disabled] .btn-mi:focus, 
.btn-mi.disabled:active, 
.btn-mi[disabled]:active, 
fieldset[disabled] .btn-mi:active, 
.btn-mi.disabled.active, 
.btn-mi[disabled].active, 
fieldset[disabled] .btn-mi.active { 
  background-color: #FC9B40; 
  border-color: #FC9B40; 
} 
 
.btn-mi .badge { 
  color: #FC9B40; 
  background-color: #ffffff; 
}

.btn-boss
{
    background-color: #ff00bf;
    border-color: #ff00bf;
}
.btn-boss:hover, 
.btn-boss:focus, 
.btn-boss:active, 
.btn-boss.active, 
.open .dropdown-toggle.btn-boss { 
  color: #ffffff; 
  background-color: #e000a8; 
  border-color: #e000a8; 
}

.btn-item
{
    background-color: #ffc000;
    border-color: #ffc000;
}
.btn-item:hover, 
.btn-item:focus, 
.btn-item:active, 
.btn-item.active, 
.open .dropdown-toggle.btn-item { 
  color: #ffffff; 
  background-color: #F2C02C; 
  border-color: #FFC000; 
} 
 
.btn-item:active, 
.btn-item.active, 
.open .dropdown-toggle.btn-item { 
  background-image: none; 
} 
 
.btn-item.disabled, 
.btn-item[disabled], 
fieldset[disabled] .btn-item, 
.btn-item.disabled:hover, 
.btn-item[disabled]:hover, 
fieldset[disabled] .btn-item:hover, 
.btn-item.disabled:focus, 
.btn-item[disabled]:focus, 
fieldset[disabled] .btn-item:focus, 
.btn-item.disabled:active, 
.btn-item[disabled]:active, 
fieldset[disabled] .btn-item:active, 
.btn-item.disabled.active, 
.btn-item[disabled].active, 
fieldset[disabled] .btn-item.active { 
  background-color: #FFC000; 
  border-color: #FFC000; 
} 
 
.btn-item .badge { 
  color: #FFC000; 
  background-color: #ffffff; 
}

.btn-cinematic
{
    background-color: #7b7b7b;
    border-color: #7b7b7b;
}
.btn-cinematic:hover, 
.btn-cinematic:focus, 
.btn-cinematic:active, 
.btn-cinematic.active, 
.open .dropdown-toggle.btn-cinematic { 
  color: #ffffff; 
  background-color: #787878; 
  border-color: #7B7B7B; 
} 
 
.btn-cinematic:active, 
.btn-cinematic.active, 
.open .dropdown-toggle.btn-cinematic { 
  background-image: none; 
} 
 
.btn-cinematic.disabled, 
.btn-cinematic[disabled], 
fieldset[disabled] .btn-cinematic, 
.btn-cinematic.disabled:hover, 
.btn-cinematic[disabled]:hover, 
fieldset[disabled] .btn-cinematic:hover, 
.btn-cinematic.disabled:focus, 
.btn-cinematic[disabled]:focus, 
fieldset[disabled] .btn-cinematic:focus, 
.btn-cinematic.disabled:active, 
.btn-cinematic[disabled]:active, 
fieldset[disabled] .btn-cinematic:active, 
.btn-cinematic.disabled.active, 
.btn-cinematic[disabled].active, 
fieldset[disabled] .btn-cinematic.active { 
  background-color: #7B7B7B; 
  border-color: #7B7B7B; 
} 

.componente_AndOr {
  display:flex; 
  flex-direction: row; 
  justify-content: flex-start; 
  gap: 10px; 
  align-content: center;
  align-items: center; 
  align-self: center; 
  margin-left: 10px;
}

.component_label {
  display: flex; 
  align-items:flex-start; 
  align-content:flex-start; 
  margin-left: 30px;
}
 
.btn-cinematic .badge { 
  color: #7B7B7B; 
  background-color: #ffffff; 
}

#dcValue {
  font-weight: bold;
  color: white;
}

.spanValue {
  font-size: 25px; 
  margin-right: 8px; 
  margin-top: 10px;
}

.marker-b
{
    margin-left: 25px !important;
}

.btn-icon
{
  font-size: 35px;
  color: #804800;
}

.btn-loop
{
  color: #ffffff;
  background-color: #2c2c2c;
}

.nu-icon
{
  font-size: 31px;
  color: #8f8686;
  margin-left: 47px;
}

.key-icon
{
  font-weight: bold;
  font-size: 90px;
  display: flex;
   align-items:flex-end;
   align-content:flex-end;
   float:right;
   margin-right: 120px;
}
.positionCircle{
  display: flex;
  align-self: center;
}
