import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { Base } from './Base';

export class Scenery
  extends Base<Data.Hard.IScenery, Data.Result.IScenery>
  implements Required<Data.Hard.IScenery>
{
  public static generateId(index: number): string {
    return IdPrefixes.SCENERY + index;
  }
  constructor(index: number, name: string, dataAccess: Scenery['TDataAccess']) {
    super(
      {
        hard: {
          id: Scenery.generateId(index),
          name,
        },
      },
      dataAccess
    );
  }
  public get name(): string {
    return this.hard.name;
  }
  public set name(value: string) {
    this.hard.name = value;
  }
  protected getInternalFetch() {
    return {};
  }
  public get areaId(): string {
    return this.hard.areaId;
  }
  public set areaId(value: string) {
    this.hard.areaId = value;
  }
  public get isReviewedName(): boolean {
    return this.hard.isReviewedName;
  }
  public set isReviewedName(value: boolean) {
    this.hard.isReviewedName = value;
  }
}
