
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { RouterModule } from '@angular/router';
import { ContentInputComponent } from './content-input.component';
import { MajorModule } from 'src/app/major.module';

@NgModule({
  imports: [
    CommonModule,
    BrowserModule,
    FormsModule,
    RouterModule,
    MajorModule,
  ],
  declarations: [ContentInputComponent],
  exports: [ContentInputComponent],
})
export class ContentInputModule {}
