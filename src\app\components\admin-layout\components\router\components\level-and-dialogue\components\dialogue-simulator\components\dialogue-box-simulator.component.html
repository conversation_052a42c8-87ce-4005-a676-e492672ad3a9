<!-- Chat interface inspired by: https://codepen.io/supah/pen/jqOBqp -->

<!-- Main container for individual dialogue box with optional roadblock indicators -->
<div class="dialogue-box-container" [class.has-roadblock]="hasRoadBlock && isRoadBlockViewEnabled">

  <!-- Roadblock visual indicator: Square brackets with lock icon -->
  <!-- Shows when dialogue elements have unmet requirements -->
  <div class="roadblock-bracket" *ngIf="hasRoadBlock && isRoadBlockViewEnabled">
    <div class="roadblock-bracket-line roadblock-bracket-top"></div>
    <div class="roadblock-bracket-line roadblock-bracket-middle">
      <i class="pe-7s-lock roadblock-lock-icon"></i>
    </div>
    <div class="roadblock-bracket-line roadblock-bracket-bottom"></div>
  </div>

  <!-- Main dialogue content area -->
  <div class="dialogue-content">
    <!-- User choice options table -->
    <table class="tableOptions">
      <!-- Individual option rows -->
      <tr *ngFor="let option of options">
        <td>
          <div
            style="margin-bottom: 5px;"
            class="message option clickable new right"
            [class.disabled]="sessionService.usedOptionsPerBox.get(box.id)?.has(option.id)"
            [ngClass]="{'selected' : this.selectedOption !== undefined}"
            (click)="!sessionService.usedOptionsPerBox.get(box.id)?.has(option.id) &&  processedOption(option.id)">
              <!-- Option type icon (speech bubble or announcement) -->
              <div
                class="material-icons"
                [ngClass]="optionType === 1 ? 'sms-icon' : 'announcement-icon'">
                {{ optionType === 1 ? 'sms' : 'announcement' }}
              </div>
              <!-- Option text with RPG formatting -->
              <div style="display: inline" [innerHTML]="option.message | rpgFormatting"></div>
          </div>
        </td>
      </tr>
      <!-- Exit option for investigation type dialogues -->
      <tr>
        <td>
          <div class="message option clickable new right"
            *ngIf="options?.length > 0 && this.optionType === 1 && this.selectedOption === undefined"
            (click)="finishBoxProcessing(true)"
            [ngClass]="{'selected' : this.options?.length == 0}">
            <div class="material-icons">power_settings_new</div>
            <div style="display: inline-block" [innerHTML]="'Exit' | rpgFormatting"></div>
          </div>
        </td>
      </tr>
    </table>

    <!-- Dilemma choices table (when dilemmas are present) -->
    <table class="tableOptions" *ngIf="dilemmas?.length > 0">
      <tr *ngFor="let dilemma of dilemmas">
        <td>
          <div
            style="margin-bottom: 5px;"
            class="message option clickable new right"
            [ngClass]="{'selected' : selectedOption !== undefined}"
            (click)="processedOption(dilemma.id)">
              <!-- Dilemma icon -->
              <img src="assets/img/icon_dilemma.png" alt="icon" class="icon-png" />
              <!-- Dilemma text with RPG formatting -->
              <div style="display: inline" [innerHTML]="dilemma.message | rpgFormatting"></div>
          </div>
        </td>
      </tr>
    </table>

    <!-- Chat messages display area -->
    <div *ngFor="let message of messages">
      <!-- Loading animation for typing effect -->
      <div *ngIf="message.loading; else messageContent"
        class="message loading new"
        [ngClass]="{'left' : message.sideLeft, 'right' : !message.sideLeft}">
        <span class="b1"></span>
        <span class="b2"></span>
        <span class="b3"></span>
      </div>
      <!-- Actual message content -->
      <ng-template #messageContent>
        <div class="message new"
          [ngClass]="{'left' : message.sideLeft, 'right' : !message.sideLeft}">
          <!-- Character name with color coding -->
          <span
            *ngIf="message.name !== undefined"
            [ngStyle]="{'color': message.color? message.color : '#fff'}">{{ message.name }}</span>
          <!-- Message text with RPG formatting -->
          <div [innerHTML]="message.message | rpgFormatting"></div>
        </div>
      </ng-template>
    </div>
  </div>
</div>

<!-- Recursive component for investigation type dialogues that allow re-questioning -->
<dialogue-box-simulator
  *ngIf="questionAgainBox !== undefined"
  [roadBlockViewEnabled]="isRoadBlockViewEnabled"
  [dialogue]="dialogue"
  [box]="questionAgainBox"
  [messageTyppingDelay]="messageTyppingDelay"
  [messagesBetweenDelay]="messagesBetweenDelay"
  (proccessFinished)="proccessFinished.emit();"
  (updateScroll)="updateUI();"></dialogue-box-simulator>
