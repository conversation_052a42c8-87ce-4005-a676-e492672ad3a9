<div style="display: flex; flex-wrap: wrap; justify-content:  space-between; margin-left: -3.3vw;"  class="row"  *ngIf="nAnalyzedDetected === all.length">
    <!-- START: Character table -->
  <div class="col-md-2">
    <div class="report-table-wrapper">
      <table class="table rpg-table table-striped">
        <thead class="sticky"
               style="top: 0px">
          <tr>
            <th class="center">Link</th>
            <th>Character</th>
            <th></th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let detection of detections[+RPGType.CHARACTER]">
            <tr [class]="'rpg-color-' + +RPGType.CHARACTER">
              <ng-container *ngIf="detection.word | characterByName; else noCharacterIcon">
                <td class="td-small center td-clickable"
                    (click)="accessCharacter(detection.word)">
                  <i class="pe-7s-user"></i>
                </td>
              </ng-container>
              <ng-template #noCharacterIcon>
                <td></td>
              </ng-template>
              <td class="td-100 td-clickable"
                  (click)="searchFor(detection.word)">
                <p>
                  {{ detection.word }}
                </p>
              </td>
              <td class="td-small">
                <button 
                    class="btn btn-simple btn-remove btn-sm"
                    (click)="toPromptDetectionReview(detection)">
                    {{ detection.amount }}
                </button>
              </td>
              <td class="td-small">
                <button 
                    class="btn btn-danger btn-fill btn-remove btn-sm btn-hidden"
                    (click)="toPromptRemoveDetectedWord(detection, RPGType.CHARACTER)">
                  <i class="pe-7s-close"></i>
                </button>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
  <!-- END: Character table -->

  <!-- START: Items table -->
  <div class="col-md-2">
    <div class="report-table-wrapper">
      <table class="table rpg-table table-striped">
        <thead class="sticky"
               style="top: 0px">
          <tr>
            <th class="center">Link</th>
            <th>General Item</th>
            <th></th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let detection of detections[+RPGType.GENERAL_ITEM]">
            <tr [class]="'rpg-color-' + +RPGType.GENERAL_ITEM">
              <ng-container *ngIf="detection.word | itemByName; else noItemIcon">
                <td class="td-small center td-clickable"
                    (click)="accessItem(detection.word)">
                  <i class="pe-7s-gift"></i>
                </td>
              </ng-container>
              <ng-template #noItemIcon>
                <td></td>
              </ng-template>
              <td class="td-100 td-clickable"
                  (click)="searchFor(detection.word)">
                <p>
                  {{ detection.word }}
                </p>
              </td>
              <td class="td-small">
                <button class="btn btn-simple btn-remove btn-sm"
                        (click)="toPromptDetectionReview(detection)">
                  {{ detection.amount }}
                </button>
              </td>
              <td class="td-small">
                <button class="btn btn-danger btn-fill btn-remove btn-sm btn-hidden"
                        (click)="toPromptRemoveDetectedWord(detection, RPGType.GENERAL_ITEM)">
                  <i class="pe-7s-close"></i>
                </button>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
  <!-- END: Items table -->

  <!-- START: Places table -->
  <div class="col-md-2">
    <div class="report-table-wrapper">
      <table class="table rpg-table table-striped">
        <thead class="sticky"
               style="top: 0px">
          <tr>
            <th class="center">Link</th>
            <th>Place</th>
            <th></th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let detection of detections[+RPGType.PLACE]">
            <tr [class]="'rpg-color-' + +RPGType.PLACE">
              <ng-container *ngIf="detection.word | areaByName; else noAreaIcon">
                <td class="td-small center td-clickable"
                    (click)="accessArea(detection.word)">
                  <i class="pe-7s-way"></i>
                </td>
              </ng-container>
              <ng-template #noAreaIcon>
                <td></td>
              </ng-template>
              <td class="td-100 td-clickable"
                  (click)="searchFor(detection.word)">
                <p>
                  {{ detection.word }}
                </p>
              </td>
              <button class="btn btn-simple btn-remove btn-sm"
                      (click)="toPromptDetectionReview(detection)">
                {{ detection.amount }}
              </button>
              <td class="td-small">
                <button class="btn btn-danger btn-fill btn-remove btn-sm btn-hidden"
                        (click)="toPromptRemoveDetectedWord(detection, RPGType.PLACE)">
                  <i class="pe-7s-close"></i>
                </button>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
  <!-- END: Places table -->

  <!-- START: Currency table -->
  <div class="col-md-2">
    <div class="report-table-wrapper">
      <table class="table rpg-table table-striped">
        <thead class="sticky"
               style="top: 0px">
          <tr>
            <th class="center">Link</th>
            <th>Currency</th>
            <th></th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let detection of detections[+RPGType.CURRENCY]">
            <tr [class]="'rpg-color-' + +RPGType.CURRENCY">
              <ng-container *ngIf="detection.word | itemByName; else noCurrencyIcon">
                <td class="td-small center td-clickable"
                    (click)="accessItem(detection.word)">
                  <i class="pe-7s-diamond"></i>
                </td>
              </ng-container>
              <ng-template #noCurrencyIcon>
                <td></td>
              </ng-template>
              <td class="td-100 td-clickable"
                  (click)="searchFor(detection.word)">
                <p>
                  {{ detection.word }}
                </p>
              </td>
              <td class="td-small">
                <button class="btn btn-simple btn-remove btn-sm"
                        (click)="toPromptDetectionReview(detection)">
                  {{ detection.amount }}
                </button>
              </td>
              <td class="td-small">
                <button class="btn btn-danger btn-fill btn-remove btn-sm btn-hidden"
                        (click)="toPromptRemoveDetectedWord(detection, RPGType.CURRENCY)">
                  <i class="pe-7s-close"></i>
                </button>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
  <!-- END: Currency table -->

   <!-- START: Expressions table -->
   <div class="col-md-2">
    <div class="report-table-wrapper">
      <table class="table rpg-table table-striped">
        <thead class="sticky"
               style="top: 0px">
          <tr>
            <th>Expressions</th>
            <th></th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let detection of detections[+RPGType.EXPRESSIONS]">
            <tr [class]="'rpg-color-' + +RPGType.EXPRESSIONS">              
              <td class="td-100 td-clickable"
                  (click)="searchFor(detection.word)">
                <p>
                  {{ detection.word }}
                </p>
              </td>
              <td class="td-small">
                <button class="btn btn-simple btn-remove btn-sm"
                        (click)="toPromptDetectionReview(detection)">
                  {{ detection.amount }}
                </button>
              </td>
              <td class="td-small">
                <button class="btn btn-danger btn-fill btn-remove btn-sm btn-hidden"
                        (click)="toPromptRemoveDetectedWord(detection, RPGType.EXPRESSIONS)">
                  <i class="pe-7s-close"></i>
                </button>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
  <!-- END: Expressions table -->
</div>
