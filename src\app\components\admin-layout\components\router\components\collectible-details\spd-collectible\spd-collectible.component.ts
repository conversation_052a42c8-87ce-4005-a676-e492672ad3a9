import { Component, Input } from '@angular/core';
import { Character, PrimalModifier } from 'src/app/lib/@bus-tier/models';
import { SPDCollectible } from 'src/app/lib/@bus-tier/models/SPDCollectible';
import {
  CTREVMAXService,
  PrimalModifierService,
  SPDCollectibleService,
} from 'src/app/services';

@Component({
  selector: 'app-spd-collectible',
  templateUrl: './spd-collectible.component.html',
  styleUrls: ['./spd-collectible.component.scss'],
})
export class SpdCollectibleComponent {
  @Input() character: Character;
  primalModifier: PrimalModifier;
  ev_base: number;
  pr_base: number;
  ev_max: number;
  spd: number;

  listISPDCollectible: SPDCollectible;

  constructor(
    private _cTREVMAXService: CTREVMAXService,
    private _primalModifierService: PrimalModifierService,
    private _SPDCollectibleService: SPDCollectibleService,
  ) {}

  async ngOnInit(): Promise<void> {
    this._SPDCollectibleService.toFinishLoading();
    this._primalModifierService.toFinishLoading();

    this.listISPDCollectible =
      this._SPDCollectibleService.createNewSPDCollectible(this.character);
    // this._change.detectChanges();
    this.getDataValuesLuk();
  }

  getDataValuesLuk() {
    this.listISPDCollectible.valuesInt = [];
    this.ev_base = 0;
    this.spd = 0;
    this.ev_max = 0;

    setTimeout(() => {
      this.primalModifier = this._primalModifierService.models.find(
        (i) => i.character === this.character.id
      );
      this.ev_base = this.primalModifier.primalModifier.find(
        (i) => i.fieldName === 'Evasão'
      ).fieldValue;
      this.pr_base = this.primalModifier.primalModifier.find(
        (i) => i.fieldName === 'Precisão'
      ).fieldValue;
      this.spd = this.primalModifier.primalModifier.find(
        (i) => i.fieldName === 'Velocidade'
      ).fieldValue;

      if (this._cTREVMAXService.models.length > 0) {
        this.ev_max = this._cTREVMAXService.models[0].ev_max;
      }

      for (let newSPD = 5; newSPD <= 200; newSPD++) {
        if (this.ev_base) {
          this.listISPDCollectible.valuesInt.push({
            ev_defluk: this.calculateEV(newSPD).toFixed(2),
            pr_atkluk: this.calculatePR(newSPD).toFixed(2),
            qiNew: newSPD,
          });
        } else {
          this.listISPDCollectible.valuesInt.push({
            ev_defluk: '',
            pr_atkluk: '',
            qiNew: newSPD,
          });
        }
      }
      this._SPDCollectibleService.svcToModify(this.listISPDCollectible);
    }, 200);
  }

  private sign(value: number): number {
    return value > 0 ? 1 : value < 0 ? -1 : 0;
  }

  public calculateEV(newSPD: number): number {
    const controlPos = this.controlPositive(newSPD);
    const controlNeg = this.controlNegative(newSPD);

    const term1 = controlNeg * this.ev_base * (newSPD / this.spd);
    const term2 =
      controlPos *
      (((this.ev_max - this.ev_base) / (199.999999 - this.spd)) *
        (newSPD - this.spd) +
        this.ev_base);

    return term1 + term2;
  }

  public calculatePR(newSPD: number): number {
    const controlPos = this.controlPositive(newSPD);
    const controlNeg = this.controlNegative(newSPD);

    const prBaseFraction = this.pr_base / 100; // Convertendo PR para fração

    const term1 = controlNeg * prBaseFraction * (newSPD / this.spd);
    const term2 =
      controlPos *
      (((1 - prBaseFraction) / (199.999999 - this.spd)) * (newSPD - this.spd) +
        prBaseFraction);

    return (term1 + term2) * 100; // Convertendo de volta para porcentagem
  }

  private controlPositive(newLuck: number): number {
    return (this.sign(newLuck - this.spd) + 1) / 2;
  }

  private controlNegative(newLuck: number): number {
    return 1 - this.controlPositive(newLuck);
  }

  reset(character) {
    this.character = character;
    this.ngOnInit();
  }
}
