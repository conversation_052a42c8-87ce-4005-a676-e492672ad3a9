<ng-container *ngIf="listCategories.length > 0">
    <div style="display: flex; flex-direction: row; overflow-x: auto; width: 100%;">
        <table class="table table-list borderList">
            <thead>
                <tr>            
                    <th *ngFor="let title of titles"> {{title}}</th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let item of listCategories; let i = index">                                
                        <td class="td-id aligTitle">
                            <input class="background-input-table-color form-control form-short noCursor" placeholder=" " disabled
                                type="text" #index [ngClass]="{'empty-input': !index.value}"
                                [value]="item.indexCategory" (change)="changeMahankaraValue(i,'indexCategory', index.value)" />
                        </td>
                        <td class="td-id">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #speechCategory [ngClass]="{'empty-input': !speechCategory.value}"
                                [value]="item.speechCategory" (change)="changeMahankaraValue(i,'speechCategory', speechCategory.value)" />
                        </td>
                        <td class="td-id">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #description [ngClass]="{'empty-input': !description.value}"
                                [value]="item.description" (change)="changeMahankaraValue(i, 'description', description.value)" />
                        </td> 
                </tr>
            </tbody>
        </table>
    </div>
</ng-container>


<ng-container *ngIf="listCategories.length === 0">
    <div class="card" style="text-align: center; padding: 20px;">
        <h3>Empty list. Click to create the list.</h3>
    </div>
</ng-container>
