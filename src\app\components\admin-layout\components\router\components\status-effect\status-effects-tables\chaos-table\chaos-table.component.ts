import { Component, EventEmitter, Output } from '@angular/core';
import { ChaosTable } from 'src/app/lib/@bus-tier/models';
import { Button } from 'src/app/lib/@pres-tier/data';
import { ChaosTableService } from 'src/app/services';
import { Alert } from 'src/lib/darkcloud';

@Component({
  selector: 'app-chaos-table',
  templateUrl: './chaos-table.component.html',
  styleUrls: ['./chaos-table.component.scss']
})
export class ChaosTableComponent {

  
 titles = ['ID', 'CATEGORY', 'STATUS EFFECT NAME', 'DESCRIPTION', 'TARGET', 'POWER POINTS (PP)', 'ALL', 'DURATION (TURNS)'];
 listChaosTable: ChaosTable[] = [];
 activeLanguage = 'PTBR';
 @Output() activeTab2 = new EventEmitter<string>();
 isListChaosTableEmpty: boolean;

 public readonly excelButtonTemplate: Button.Templateable = {
   title: 'Paste content from excel',
   onClick: this.onExcelPaste.bind(this),
   iconClass: 'excel-icon',
   btnClass: Button.Klasses.FILL_ORANGE,
 };
 constructor(
   private _chaosTableService: ChaosTableService
 ){}


 async ngOnInit(): Promise<void>{
   
     this.removeEmptyItems();
      this.listChaosTable = this._chaosTableService.models;
      this.isListChaosTableEmpty = this.listChaosTable.length === 0;  

   }

   removeEmptyItems() {
     this._chaosTableService.toFinishLoading();
     this._chaosTableService.models = this._chaosTableService.models.filter(ailmentItem => ailmentItem.idChaosTable !== "");
     this._chaosTableService.toSave();
   }

   async onExcelPaste() {
     const text = await navigator.clipboard.readText();
     const lines = text.split(/\r?\n/).filter(line => line);    
     const processedData: string[][] = [];
   
     if (lines.length > 0) {
       lines.forEach(line => {
         // Divide cada linha em colunas e remove a primeira coluna
         const values = line.split("\t").map(value => value.trim()).slice(1);
   
         processedData.push(values);
       });
   
       // Verifica se o número de colunas em cada linha corresponde ao número de colunas em this.titles
       const isColumnCountValid = processedData.every(row => row.length === this.titles.length);
   
       if (!isColumnCountValid) {
         Alert.showError('Invalid number of columns');
         return;
       }
 
       this._chaosTableService.models = [];
       this._chaosTableService.toSave();
   
       for (let index = 0; index < processedData.length; index++) {
         this._chaosTableService.createNewnChaosTable(processedData[index]);
       }    

       Alert.ShowSuccess('Ailment Table imported successfully!');
       this.activeTab2.emit('ailmentTable');
       this.ngOnInit();
     }
   }
   

   changeChaos(rowIndex: number, name: string, newValue: string){

     if (name === 'idAilment') {
      this.listChaosTable[rowIndex].idChaosTable = newValue;        
     }
     else if (name === 'category') {
       this.listChaosTable[rowIndex].category = newValue;        
      }
      else if (name === 'statusEffectName') {
       this.listChaosTable[rowIndex].statusEffectName = newValue;        
      }
      else if (name === 'description') {
       this.listChaosTable[rowIndex].description = newValue;        
      }
      else if (name === 'target') {
       this.listChaosTable[rowIndex].target = newValue;        
      }
      else if (name === 'powerPoints') {
       this.listChaosTable[rowIndex].powerPoints = newValue;        
      }
      else if (name === 'allAilment') {
       this.listChaosTable[rowIndex].allChaosTable = newValue;        
      }
      else if (name === 'duration') {
        this.listChaosTable[rowIndex].duration = newValue;        
       }

     this._chaosTableService.svcToModify(this.listChaosTable[rowIndex]);
   }    
}
