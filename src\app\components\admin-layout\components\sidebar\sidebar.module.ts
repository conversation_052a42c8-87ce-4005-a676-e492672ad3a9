import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SidebarComponent } from './sidebar.component';
import { SearcherComponent } from './components/searcher/searcher.component';
import { BrowserModule } from '@angular/platform-browser';
import { FormsModule } from '@angular/forms';
import { MajorModule } from 'src/app/major.module';
import { PopupModule } from 'src/app/components/popup/popup.module';
import { RouterModule } from '@angular/router';
import { ContentInputModule } from 'src/app/components/content-input/content-input.module';
//import { AppRoutingModule } from 'dsadmin-win32-x64/resources/app/src/app/components/admin-layout/components/router/app-routing.module';

@NgModule({
  imports: [
    CommonModule,
    BrowserModule,
    FormsModule,
    //AppRoutingModule,
    MajorModule,
    RouterModule,
    ContentInputModule,
    PopupModule,
  ],
  declarations: [SidebarComponent, SearcherComponent],
  exports: [SidebarComponent],
})
export class SidebarModule {}
