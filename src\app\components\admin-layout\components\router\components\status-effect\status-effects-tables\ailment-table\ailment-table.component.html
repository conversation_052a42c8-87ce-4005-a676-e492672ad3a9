<div class="div-container">
    <div class="width: 2221px;">
        <table class="table table-list borderList">
            <thead>    
                <tr>
                    <th [attr.colspan]="titles.length +1">
                        <h3>AILMENT</h3>
                    </th>
                    <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'" class="addButton"
                        [buttonTemplates]="[excelButtonTemplate]">
                    </app-button-group>
                </tr>
                <ng-container *ngIf="!isListAilmentEmpty">
           
                    <tr>
                        <th class="default-color">Order</th>
                        <th class="default-color" *ngFor="let title of titles">{{title}}</th>
                      </tr>
                      
                </ng-container>
            </thead>
            <ng-container *ngIf="!isListAilmentEmpty">
                <tbody>
                    <tr *ngFor="let item of listAilmentTable; let i = index">
                        <td style="background-color: #ddd; width: 4%;">{{ i + 1 }}</td>               
                            <td class="td-id aligTitle" style="width: 11%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #idAilment [ngClass]="{'empty-input': !idAilment.value}"
                                    [value]="item.idAilment" (change)="changeAilment(i,'idAilment', idAilment.value)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 11%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #idCategory [ngClass]="{'empty-input': !idCategory.value}"
                                    [value]="item.category" (change)="changeAilment(i,'category', idCategory.value)" />
                            </td>
                            <td class="td-id" style="width: 15%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #statusEffectName [ngClass]="{'empty-input': !statusEffectName.value}"
                                [value]="item.statusEffectName" (change)="changeAilment(i, 'statusEffectName', statusEffectName.value)" />
                            </td>
                            <td class="td-id" style="width: 30%; word-break: break-word;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #description [ngClass]="{'empty-input': !description.value}"
                                    [value]="item.description" (change)="changeAilment(i, 'description', description.value)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 15%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #powerPoints [ngClass]="{'empty-input': !powerPoints.value}"
                                    [value]="item.powerPoints" (change)="changeAilment(i, 'powerPoints', powerPoints.value)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 15%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #allAilment [ngClass]="{'empty-input': !allAilment.value}"
                                    [value]="item.allAilment" (change)="changeAilment(i, 'allAilment', allAilment.value)" />
                            </td>              
                    </tr>
                </tbody>
            </ng-container>
            <ng-container *ngIf="isListAilmentEmpty">
                <div class="card" style="text-align: center; padding: 20px;">
                    <h3>The list has not yet been imported.</h3>
                </div>
            </ng-container>
        </table>
</div>
    
</div>

