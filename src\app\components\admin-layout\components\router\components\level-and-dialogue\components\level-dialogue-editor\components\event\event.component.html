<td colspan="6" id="{{event.id}}">
  <p style="opacity: 30%;" [ngClass]="event.id.includes('ADB') ? 'color-grey-light' : ''">{{event.id}}</p>
  <table class="table table-responsive"
    [class.cinematic-event-table]="getEventCategory(event) === +EventCategory.CINEMATIC"
    [class.item-event-table]="getEventCategory(event) === +EventCategory.ITEM"
    [class.mission-event-table]="getEventCategory(event) === +EventCategory.MISSION"
    [class.loop-event-table]="getEventCategory(event) === +EventCategory.LOOP">
    <tbody>
      <tr>
        <!--ORDER ARROWS-->
        <td class="td-sort">
          <div [title]="event.id">
            <button class="btn btn-success btn-simple btn-invert btn-sm" (click)="toMove(event, -1)">
              <i class="pe-7s-angle-up-circle"></i>
            </button>
            {{ index }}
            <button class="btn btn-danger btn-simple btn-invert btn-sm" (click)="toMove(event, 1)">
              <i class="pe-7s-angle-down-circle"></i>
            </button>
          </div>
        </td>

        <!--TYPE SELECTION-->
        <td>        

          <label>Event Type
            <ng-container *ngIf="getEventCategory(event) === +EventCategory.ITEM">
            <i id="explain" style="font-size: 20px !important; vertical-align: bottom;" placement='top' delay='250' ttPadding="10px" ttWidth="450px" 
            tooltip="
Ação	         Remove item do inventário?	Adiciona item no inventário?
Give Item 	✅ Sim	                                ✅ Sim	
Receive Item	❌ Não	                                ✅ Sim	
Trade Item	❌ Não	                                ❌ Não"
            class="pe-7s-help1"></i>
            </ng-container>
          </label><br />

          <select *ngIf="getEventCategory(event) !== +EventCategory.LOOP" class="filter-dropdown auto"
            [(ngModel)]="event.type" (change)="onChange(); loadRequirements();">
            <option *ngFor="let eventType of EventCategoryToType[+getEventCategory(event)]" value="{{ eventType }}">
              {{ eventType | eventTypeName }}
            </option>
          </select>
          <select *ngIf="getEventCategory(event) === +EventCategory.LOOP && isMicroloop" class="filter-dropdown auto"
            [(ngModel)]="event.type" (change)="onChange(); loadRequirements();">
            <option *ngFor="let eventType of EventCategoryToType[+getEventCategory(event)]" value="{{ eventType }}">
              {{ eventType | eventTypeName }}
            </option>
          </select>
          <select *ngIf="getEventCategory(event) === +EventCategory.LOOP && !isMicroloop" class="filter-dropdown auto">
            <option value="EventType.LINK_MICROLOOP">Link to Microloop</option>
          </select>
        </td>

        <td>
          <ng-container *ngIf="event.type !== undefined">
            <ng-container *ngIf="event.type === EventType.ASSIGN_MISSION; else objectiveContainer">
              <!--SELECT MISSION-->
              <ng-container *ngIf="isRequired['missionId']">
                <label>Mission</label><br />
                <select class="filter-dropdown auto" [(ngModel)]="event.missionId" (change)="onChange(); ">

                  <option *ngFor="let mission of preloadedMissionsOfArea" value="{{ mission.id }}">
                    {{ mission.name }}
                  </option>

                  <!-- TYPE Assign a Mission -->
                  <!--   <ng-container *ngIf="event.type == 3">
                    <option></option>
                    <option *ngFor="let mission of preloadedMissionsOfArea" value="{{ mission.id }}"
                      [ngClass]="validatesMissionInUse(mission).assignedAt.length > 0 ? 'itemClass': ''">
                          {{ mission.name }}                  
                    <span [ngClass]="validatesMissionInUse(mission)?.assignedAt.length > 0 ? 'borderRadius': ''" *ngIf="validatesMissionInUse(mission).assignedAt.length"> 
                      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;   =>&emsp; {{validatesMissionInUse(mission).assignedAt.length}}</span>
                    </option>
                </ng-container>-->

                </select>
              </ng-container>
            </ng-container>
            <ng-template #objectiveContainer>

              <!--SELECT MISSION-->
              <ng-container *ngIf="isRequired['missionId']">
                <label>Mission</label><br />
                <select class="filter-dropdown auto" [(ngModel)]="event.missionId"
                  (click)="onChange(); loadObjectives()">
                  <option></option>
                  <option *ngFor="let mission of displayPreloadedMissions" value="{{ mission.id }}">
                    {{ mission.name }}
                  </option>
                </select>
              </ng-container>

              <!--SELECT OBJECTIVE-->
              <ng-container *ngIf="event.missionId">
                <ng-container *ngIf="isRequired['objectiveId']">
                  <label>Objective</label><br />
                  <select class="filter-dropdown auto" [(ngModel)]="event.objectiveId" (change)="onChange()">
                    <!-- TYPE Assign a Mission -->
                    <ng-container *ngIf="event.type == 3">
                      <option></option>
                      <option *ngFor="let objective of missionObjectives | objectives : event; let i = index"
                        value="{{ objective?.id }}" #obj
                        [ngClass]="validatesMissionInUse(objective).assignedAt.length > 0 ? 'itemClass': ''">
                        {{ i }}: {{ objective.description }}
                        <span [ngClass]="validatesMissionInUse(objective)?.assignedAt.length > 0 ? 'borderRadius': ''"
                          *ngIf="validatesMissionInUse(objective).assignedAt.length">
                          &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; => &emsp;
                          {{validatesMissionInUse(objective).assignedAt.length}}</span>
                      </option>
                    </ng-container>

                    <!-- TYPE Complete Objective-->
                    <ng-container *ngIf="event.type == 6">
                      <option></option>
                      <option *ngFor="let objective of missionObjectives | objectives : event; let i = index"
                        value="{{ objective?.id }}" #obj
                        [ngClass]="validatesMissionInUse(objective).completesAt.length > 0 ? 'itemClass': ''">
                        {{ i }}: {{ objective.description }}
                        <span [ngClass]="validatesMissionInUse(objective)?.completesAt.length > 0 ? 'borderRadius': ''"
                          *ngIf="validatesMissionInUse(objective).completesAt.length">
                          &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; => &emsp;
                          {{validatesMissionInUse(objective).completesAt.length}}</span>
                      </option>
                    </ng-container>

                    <!-- TYPE Fail Objective-->
                    <ng-container *ngIf="event.type == 7">
                      <option></option>
                      <option *ngFor="let objective of missionObjectives | objectives : event; let i = index"
                        value="{{ objective?.id }}" #obj
                        [ngClass]="validatesMissionInUse(objective).failsAt.length > 0 ? 'itemClass': ''">
                        {{ i }}: {{ objective.description }}
                        <span [ngClass]="validatesMissionInUse(objective)?.failsAt.length > 0 ? 'borderRadius': ''"
                          *ngIf="validatesMissionInUse(objective).failsAt.length">
                          &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; => &emsp;
                          {{validatesMissionInUse(objective).failsAt.length}}</span>
                      </option>
                    </ng-container>

                  </select>
                </ng-container>
              </ng-container>
            </ng-template>


            <!--Select Add Items -->
            <ng-container *ngIf="isRequired['itemId'] && event.type != 8">
              <div>
                <div>
                  <label>Item</label><br />
                  <select class="filter-dropdown auto" [(ngModel)]="event.itemId" (change)="onChange();">
                    <option></option>
                    <ng-container *ngFor="let itemClass of itemGroups">
                      <option disabled></option>
                      <optgroup [label]="itemClass.name" default>
                        <!--RECEIVE_ITEM = 0-->
                        <ng-container *ngIf="event.type == 0">
                          <option *ngFor="let item of itemClass.itemIds" [value]="item.id"
                            [ngClass]="validatesItemInUse(item)?.receivedAt.length > 0 ? 'itemClass': ''">
                            {{ item.name }}
                            <span [ngClass]="validatesItemInUse(item)?.receivedAt.length > 0 ? 'borderRadius': ''"
                              *ngIf="validatesItemInUse(item)?.receivedAt.length">
                              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; => &emsp;
                              {{validatesItemInUse(item).receivedAt.length}}</span>
                          </option>
                        </ng-container>
                        <!-- GIVE_ITEM = 1-->
                        <ng-container *ngIf="event.type == 1 || event.type == 14">
                          <option *ngFor="let item of itemClass.itemIds" [value]="item.id"
                            [ngClass]="validatesItemInUse(item)?.givenAt.length > 0 ? 'itemClass': ''">
                            {{ item.name }}
                            <span [ngClass]="validatesItemInUse(item)?.givenAt.length > 0 ? 'borderRadius': ''"
                              *ngIf="validatesItemInUse(item)?.givenAt.length">
                              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; => &emsp;
                              {{validatesItemInUse(item).givenAt.length}}</span>
                          </option>
                        </ng-container>
                        <!--TRADE_ITEM = 2-->
                        <ng-container *ngIf="event.type == 2">
                          <option *ngFor="let item of itemClass.itemIds" [value]="item.id"
                            [ngClass]="validatesItemInUse(item)?.tradedAt.length > 0 ? 'itemClass': ''">
                            {{ item.name }}
                            <span [ngClass]="validatesItemInUse(item)?.tradedAt.length > 0 ? 'borderRadius': ''"
                              *ngIf="validatesItemInUse(item)?.tradedAt.length">
                              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; => &emsp;
                              {{validatesItemInUse(item).tradedAt.length}}</span>
                          </option>
                        </ng-container>
                      </optgroup>
                      <option disabled></option>
                      <option style="margin-top: 2rem; margin-bottom: 2rem;" disabled class="divider"></option>
                    </ng-container>
                  </select>

                </div>

                <div>

                  <div style="display:flex; align-items: center;">
                    <div>
                      <label>Amount</label> <br />
                      <input *ngIf="!this.isPercentage" type="number" class="form-control form-short" #amount min="0"
                        [value]="event.amount" [(ngModel)]="event.amount"
                        (change)="onChangeParameterText('amount', amount.value)" />

                      <input *ngIf="this.isPercentage" type="number" min="" placeholder="Amount"
                        class="form-control form-short" #amount [value]="event.amount" [(ngModel)]="amount.value"
                        (change)="onChangeParameterText('amount', amount.value)" />
                    </div>
                    <div style="margin-left:20px; margin-top: 20px;">
                      <button class="percentage-class"
                        [ngClass]="{'isPercentage-class': this.isPercentage , 'isNOTPercentage-class': !this.isPercentage}"
                        (click)="onChangeToPercentage()">%</button>
                    </div>
                  </div>
                </div>
              </div>
            </ng-container>

            <!--SELECT VIDEO-->
            <ng-container *ngIf="isRequired['videoId']">
              <label>Video</label><br />
              <select class="filter-dropdown auto" [(ngModel)]="event.videoId" (change)="onChange();">
                <option *ngFor="let video of preloadedVideos" value="{{ video.id }}"
                  [style.background-color]="video | videoColor">
                  <div>
                    <ng-container *ngIf="+video.type === VideoType.CUTSCENE; else isAnimatic">
                      {{ video.id + ': ' + (video | cutscene).name }}
                    </ng-container>
                    <ng-template #isAnimatic>
                      {{ video.id + ': ' + ((video | animatic).characterId |
                      character)?.name }}
                    </ng-template>
                  </div>
                </option>
              </select>
            </ng-container>

            <!--SELECT TUTORIAL-->
            <ng-container *ngIf="isRequired['tutorialId']">
              <label>Tutorial</label><br />
              <select class="filter-dropdown auto" [(ngModel)]="event.tutorialId" (change)="onChange();">
                <option *ngFor="let tutorial of preloadedTutorials" value="{{ tutorial.id }}">
                  {{ tutorial.name }}
                </option>
              </select>
            </ng-container>
          </ng-container>

          <ng-container *ngIf="event.type == +EventType.LINK_MICROLOOP">
            <label>Microloop</label>
            <select class="filter-dropdown auto" [(ngModel)]="event.microloopId" (change)="onChange();">
              <option value="{{ container.id }}" *ngFor="let container of preloadedMicroloopContainers">
                {{ container.name }}
              </option>
            </select>
          </ng-container>
        </td>

        <!--REMOVE BUTTON-->
        <td>
          <button class="btn btn-simple btn-fill btn-danger btn-remove" (click)="toRemove(event)">
            <i style="font-size: 24px; margin: 0; padding: 0" class="pe-7s-close"></i>
          </button>
        </td>
      </tr>
    </tbody>
  </table>
</td>