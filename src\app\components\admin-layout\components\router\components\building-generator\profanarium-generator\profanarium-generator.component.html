<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons 
          [cardTitle]="listName"
          [cardDescription]="description"
          [rightButtonTemplates]="[excelButtonTemplate]">
        </app-header-with-buttons>
        <app-header-search 
          (inputKeyup)="lstOnChangeFilter($event)"
          (searchOptions)="lstOnChangeFilterOptions($event)">
        </app-header-search>
      </div>
    </div>
    <!--List-->
    <div class="card">
      <table class="table table-list">
        <thead class="sticky">
          <tr>
            <th rowspan="4" class="th-clickable"
                (click)="sortListByParameter('profanariumLevel')">
                WORKSHOP - LEVEL
            </th>
            <th colspan="3">
              IMPROVEMENT (change WORKSHOP level)
            </th>
            <th colspan="3">
              RESEARCH (to create improvements)
            </th>            
          </tr>
          <tr>
            <th class="th-clickable"
                (click)="sortListByParameter('improveTitanium')">
                Cost
            </th>
            <th class="th-clickable"
                (click)="sortListByParameter('improveTime')">
                Building Time
            </th>
            <th class="th-clickable"
                (click)="sortListByParameter('improveRubies')">
                Skip Price
            </th>
            <th class="th-clickable"
                (click)="sortListByParameter('researchSouls')">
                Cost
            </th>
            <th class="th-clickable"
                (click)="sortListByParameter('researchTime')">
                Time to Finish
            </th>
            <th class="th-clickable" 
                (click)="sortListByParameter('researchRubies')">
                Skip Price
            </th>
          </tr>
          <tr>
            <th class="th-clickable silver-color"
                (click)="sortListByParameter('improveTitanium')">
                TITANIUM
            </th>
            <th class="th-clickable time-color"
                (click)="sortListByParameter('improveTime')">
                MINUTES
            </th>
            <th class="th-clickable rubies-color"
                (click)="sortListByParameter('improveRubies')">
                RUBIES
            </th>
            <th class="th-clickable common"
                (click)="sortListByParameter('researchSouls')">
                SOULS
            </th>
            <th class="th-clickable time-color"
                (click)="sortListByParameter('researchTime')">
                MINUTES
            </th>
            <th class="th-clickable rubies-color" 
                (click)="sortListByParameter('researchRubies')">
                RUBIES
            </th>
          </tr>
          <tr>
            <th class="th-clickable light-gray"
                (click)="sortListByParameter('improveTitanium')">
                Required to get to this level
            </th>
            <th class="th-clickable light-gray"
                (click)="sortListByParameter('improveTime')">
                Time to upgrade to this level
            </th>
            <th class="th-clickable light-gray"
                (click)="sortListByParameter('improveRubies')">
                Gem to skip the wait (build instantly)
            </th>
            <th class="th-clickable light-gray"
                (click)="sortListByParameter('researchSouls')">
                Required to Research
            </th>
            <th class="th-clickable light-gray"
                (click)="sortListByParameter('researchTime')">
                Research Time
            </th>
            <th class="th-clickable light-gray" 
                (click)="sortListByParameter('researchRubies')">
                Gems to skip the wait (research instantly)
            </th>
          </tr>        
        </thead>
        <tbody>
          <ng-container *ngFor="let profanarium of lstIds | profanariums; let i = index; trackBy: trackById">
            <tr id="{{ profanarium.id }}">
              <td>{{ profanarium.profanariumLevel }}</td>
              <td class="td-id">
                <input 
                  class="background-input-table-color" placeholder=" "
                  type="number" #InputimproveTitanium
                  [value]="profanarium.improveTitanium"
                  (change)="lstOnChange(profanarium, 'improveTitanium', InputimproveTitanium.value)" />
              </td>
              <td class="td-id">
                <input 
                  class="background-input-table-color" placeholder=" "
                  type="number" #InputimproveTime
                  [value]="profanarium.improveTime"
                  (change)="lstOnChange(profanarium, 'improveTime', InputimproveTime.value)" />
              </td>
              <td class="td-id">
                <input 
                  class="background-input-table-color" placeholder=" "
                  type="number" #InputimproveRubies
                  [value]="profanarium.improveRubies"
                  (change)="lstOnChange(profanarium, 'improveRubies', InputimproveRubies.value)" />
              </td>
              <td class="td-id">
                <input 
                  class="background-input-table-color" placeholder=" "
                  type="number" #InputresearchSouls
                  [value]="profanarium.researchSouls"
                  (change)="lstOnChange(profanarium, 'researchSouls', InputresearchSouls.value)" />
              </td>
              <td class="td-id">
                <input 
                  class="background-input-table-color" placeholder=" "
                  type="number" #InputreresearchTime
                  [value]="profanarium.researchTime"
                  (change)="lstOnChange(profanarium, 'researchTime', InputreresearchTime.value)" />
              </td>
              <td class="td-id">
                <input 
                  class="background-input-table-color" placeholder=" "
                  type="number" #InputresearchRubies
                  [value]="profanarium.researchRubies"
                  (change)="lstOnChange(profanarium, 'researchRubies', InputresearchRubies.value)" />
              </td>             
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>