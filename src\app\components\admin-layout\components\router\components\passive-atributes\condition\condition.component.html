
<div class="m-container">
    <table class="table-bordered">
        <thead>
            <tr>
                <th rowspan="2" class="trBC">INDEX</th>
                <th colspan="5" class="trBC">CONDITION (Trigger)</th>
            </tr>
            <tr>
                <th>ID</th>
                <th>Description</th>
                <th>Type</th>
                <th>Operator</th>
                <th>Value</th>
            </tr>
        </thead>
        <tbody>
            <!-- Exemplo de Linhas com dados -->
            <ng-container *ngIf="listCondition.length > 0">
                <tr *ngFor="let cond of listCondition; let i = index">
                    <td>{{ i + 1 }}</td>
                    <td class="bc">{{cond?.idValue}}</td>
                    <td class="bc">{{cond?.description}}</td>
                    <td class="bc">{{cond?.type}}</td>
                    <td class="bc">{{cond?.operator}}</td>
                    <td class="bc">{{cond?.value}}</td>
                </tr>
            </ng-container>
    
        </tbody>
    </table>
    
</div>

<ng-container *ngIf="listCondition.length === 0">
    <div style="text-align: center;">
        <h4>Empty List</h4>
    </div>
</ng-container>