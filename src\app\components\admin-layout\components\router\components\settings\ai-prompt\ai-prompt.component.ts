import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AIPrompt } from 'src/app/lib/@bus-tier/models/AIPrompt';
import { Button } from 'src/app/lib/@pres-tier/data';
import { OpenAIEnvironmentService, UserSettingsService } from 'src/app/services';
import { AIPromptService } from 'src/app/services/ai-prompt.service';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { IOpenAIEnvironment } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';


@Component({
  selector: 'app-ai-prompt',
  templateUrl: './ai-prompt.component.html',
  styleUrls: ['./ai-prompt.component.scss'],
})

export class AIComponent extends TranslatableListComponent<AIPrompt> implements OnInit
{
  prompts: AIPrompt[] = [];
types = ['AI Revisor', 'Creative Writing', 'Dilemma Box', 'Ghostwriter', 'Grammar', ];
  subGrammar = ['Correct Grammar', 'Reorder Syntax','Less Words'];
  subCreativeWriting = ['Improve Text', 'Easy Reading Text', 'Sophisticated Text', 'Casual Text',	];
  subTypes = [];
  listEnvironments: IOpenAIEnvironment[] = [];


  constructor(
    _activatedRoute: ActivatedRoute,
    private _aiPromptService: AIPromptService,
    private _openAIEnvironmentService: OpenAIEnvironmentService,
     private _router: Router,
    _userSettingsService: UserSettingsService,
     _translationService: TranslationService,
     _languageService: LanguageService
  ) 
  {
    super(_aiPromptService, _activatedRoute, _userSettingsService, 'name', _translationService, _languageService);
  }

  public readonly addAIPromptTemplate: Button.Templateable = 
  {
    title: 'Add a new instance to the list',
    onClick: this.addAIPrompt.bind(this),
    iconClass: 'pe-7s-plus',
    btnClass: Button.Klasses.FILL_GREEN,
  };

  public override ngOnInit(): Promise<void> 
  {
    this.getEmptyPrompt();
    this.prompts = this._aiPromptService.models;
    this.listEnvironments = this._openAIEnvironmentService.models;

    return null;
  }

  getEmptyPrompt() {
    this._aiPromptService.models.forEach(x => {
      if (x.nameEnvironmentAi === undefined) {
        x.nameEnvironmentAi = '';          
      } 
      if (x.selectType === 'Select type' || x.selectType === undefined) {
        x.selectType = '';        
      }

      this._aiPromptService.svcToModify(x);    
    });
  }

  async addAIPrompt()
  {
    let newPrompt: AIPrompt = this._aiPromptService.svcPromptCreateNewAIPrompt();
    newPrompt.selectType = 'Select type';
    await this._aiPromptService.srvAdd(newPrompt);
    this.prompts = this._aiPromptService.models;
     this.lstResetHighlights();
    this.HighlightElement(newPrompt.id, 110, true);
  }

  /**
   * Verifica se o campo promptName deve estar habilitado baseado no tipo selecionado
   */
  isPromptNameEnabled(selectType: string): boolean {
    return selectType === 'Creative Writing' || selectType === 'Grammar' || selectType === 'Ghostwriter';
  }

  /**
   * Método chamado quando um tipo é selecionado
   * Habilita o campo promptName para tipos específicos e salva a seleção
   */
  selectType(type: string, prompts: AIPrompt) {
    // Sempre salva o tipo selecionado
    prompts.selectType = type;

    // Se for um dos tipos especiais, limpa o promptName para permitir nova entrada
    if (this.isPromptNameEnabled(type)) {
      // Habilita o input para digitar o nome do prompt
      // O campo será habilitado automaticamente via isPromptNameEnabled()
      if (!prompts.promptName) {
        prompts.promptName = ''; // Garante que o campo esteja vazio para nova entrada
      }
    } 

    this._aiPromptService.svcToModify(prompts);
    this.ngOnInit();
  }

  selectEnvironment(nameEnvironmentAi : string, prompts: AIPrompt ) { 
    prompts.nameEnvironmentAi = nameEnvironmentAi;
    prompts.idEnvironmentAi = this.listEnvironments.find(x => x.name === nameEnvironmentAi).id;
   this._aiPromptService.svcToModify(prompts);
   this.ngOnInit();
 } 
  
  async changePrompt(prompt: AIPrompt, value: any, property: string)
  {
    (prompt as any)[property] = value;
    await this._aiPromptService.svcToModify(prompt);
    this.ngOnInit();
  }

  async deletePrompt(prompt: AIPrompt)
  {
    await this._aiPromptService.toPromptRemove(prompt, 'id');
    this.prompts = this._aiPromptService.models;
    this.ngOnInit();
  }

  public redirectToSettings() {
    this._router.navigate(['settings']);
  }
}
