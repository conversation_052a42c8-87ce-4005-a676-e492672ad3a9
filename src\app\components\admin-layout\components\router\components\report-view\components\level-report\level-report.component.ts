import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Marker } from 'src/app/lib/@bus-tier/models';
import { Accessment, Sortment } from 'src/app/lib/@pres-tier';
import { AreaService } from 'src/app/services/area.service';
import { EasyMVC } from 'src/lib/darkcloud/angular';

export interface Preloadable {
  unlockMarkers: Marker[];
  releaseMarkers: Marker[];
}

@Component({
  selector: 'app-level-report',
  templateUrl: './level-report.component.html',
})
/**
 * Component that reports unlocked levels and unlocked Mission Dialogues from levels
 */
export class LevelReportComponent extends EasyMVC.PreloadComponent<Preloadable> {
  sorting: Sorting = new Sorting(this);
  accessing: Accessing = new Accessing(this);
  constructor(
    public readonly areaService: AreaService,
    public readonly router: Router,
    private _activatedRoute: ActivatedRoute
  ) {
    super(_activatedRoute, (data) => data.preloadedLevelReportResolveData);
  }
}

class Sorting extends Sortment.Sorting {
  byLocation = Sortment.byLocation(this._component.areaService);
  byLevelLocation = Sortment.byLevelLocation(this._component.areaService);
  byPin = (marker: Marker) => marker.pin;
  constructor(private readonly _component: LevelReportComponent) {
    super();
  }
  public sortUnlockMarkers(by: Sortment.Sortion<Marker>) {
    this.execute(this._component.preloadedData.unlockMarkers, by);
  }
  public sortReleaseMarkers(by: Sortment.Sortion<Marker>) {
    this.execute(this._component.preloadedData.releaseMarkers, by);
  }
}

class Accessing extends Accessment.Accessing {
  towardsLevelList = Accessment.towardsLevelList<Marker>((m) => m.levelId);
  towardsDialogueEditor = Accessment.towardsDialogueEditor<Marker>((m) => m.id);
  public accessMarker(marker: Marker, towards: Accessment.Accession<Marker>) {
    this.execute(marker, towards);
  }
}
