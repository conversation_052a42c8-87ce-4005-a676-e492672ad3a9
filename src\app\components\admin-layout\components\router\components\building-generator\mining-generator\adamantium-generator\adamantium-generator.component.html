
<app-adamantium-mining-generator *ngIf="activeTab === 'mining'">
</app-adamantium-mining-generator>
<app-adamantium-storage-generator *ngIf="activeTab === 'storageA'">
</app-adamantium-storage-generator>
<app-adamantium-storageB-generator *ngIf="activeTab === 'storageB'">
</app-adamantium-storageB-generator>
<app-adamantium-storageC-generator *ngIf="activeTab === 'storageC'">
</app-adamantium-storageC-generator>