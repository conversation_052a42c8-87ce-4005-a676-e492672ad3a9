.main-menu-efect {
    padding-right: 15px;
    padding-left: 15px; 
    min-height: calc(100% - 210px);
    margin-top: 30px;
}

.card-header-content {
  display: block;
  margin-left: 20px;
  margin-right: 15px;
  width: 30%;
}

.card {
    // padding-top: 8px !important;
     padding-top: 17px !important;
     padding-bottom: 10px;
   }
 
   .card-header-content {
     display: block;
     margin-left: 30px;
     margin-right: 15px;
     width: 30%;
   }

   //tabela
   .card-container 
{
    display: flex;
    flex-direction: column;
    align-items: center;
  
    .card 
    {
      border: 1px solid #ccc;
      padding: 15px;
      margin: 5px;
      width: 50vw;
      opacity: 1;
      transition: opacity 0.5s ease-in-out;
    }
   
}


ul {
  list-style-type: none;
  margin: 0;
  padding: 0;
  overflow: hidden;
  //background-color: #333;
}

li {
  float: right;
}

li a {
  display: block;
  color: white;
  text-align: center;
  padding: 14px 16px;
  text-decoration: none;
}
