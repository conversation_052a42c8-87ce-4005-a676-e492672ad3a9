<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="card list-header-row" style="z-index: 9999;">
      <app-header-with-buttons [cardTitle]="'Mission Notes'" [cardDescription]="'Showing ' + filteredResultsCount + ' results'"
        [rightButtonTemplates]="[addMissionNotesButton]" [isBackButtonEnabled]="true"
        (cardBackButtonClick)="redirectToItemClasses()">
      </app-header-with-buttons>

      <!-- Custom Search Component -->
      <div class="custom-search-container">
        <div class="searchControl">
          <input class="form-control"
            type="text"
            placeholder="Search in name and description..."
            [(ngModel)]="queryValue"
            (input)="updateSearchTerm(queryValue)" />
        </div>
        <div class="searchOptions">
          <input type="checkbox"
            [(ngModel)]="accentSensitive"
            (change)="updateSearchOptions(caseSensitive, accentSensitive)"
            id="accentSensitive">
          <label for="accentSensitive">Accent Sensitive</label><br>
          <input type="checkbox"
            [(ngModel)]="caseSensitive"
            (change)="updateSearchOptions(caseSensitive, accentSensitive)"
            id="caseSensitive">
          <label for="caseSensitive">Case Sensitive</label>
        </div>
      </div>
    </div>

    <!--List-->
    <div>
    <div class="card">
      <table class="table table-list">
        <thead style="top: 115px">
          <tr>
            <th style="width: 40px;">Index</th>    
            <th class="th-clickable" (click)="sortListByParameter('name')">
              Name & Description
            </th>      
            <th class="th-clickable" (click)="sortListByParameter('notes')">Notes</th>
            <th class="th-clickable" (click)="sortListByParameter('createdDate')">
              Created At
            </th>
            <th>Action</th>       
          </tr>
        </thead>

        <tbody>
          <ng-container *ngFor="let missionNotes of filteredMissionNotes; let i = index; trackBy: trackById">
            <tr id="{{ missionNotes.id }}" class="anchor">
              <td class="td-sort">{{ i + 1 }}</td>   
              <td>
                <input class="form-control form-short" type="text"
                  value="{{ (missionNotes | translation : lstLanguage : missionNotes.id : 'name') }}" #name
                  (change)="lstOnChange(missionNotes, 'name', name.value)" />
                <textarea class="form-control" type="text"
                  value="{{ (missionNotes | translation : lstLanguage : missionNotes.id : 'description') }}" #description
                  (change)="lstOnChange(missionNotes, 'description', description.value)">
                  </textarea>
              </td>
              <td>
                <textarea class="form-control borderless" placeholder="Notes..." type="text"
                  value="{{ (missionNotes | information)?.authorNotes || '' }}" #notes (change)="
                      updateInformation(missionNotes, 'authorNotes', notes.value)">
                </textarea>
              </td>
              <td>
                {{ missionNotes.createdDate }}
              </td>        
     
              <td class="td-actions td-auto">
                <button class="btn btn-fill btn-danger btn-remove" (click)="removeMision(missionNotes)">
                  <i class="pe-7s-close"></i>
                </button>    
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>

      <ng-container *ngIf="filteredMissionNotes.length == 0 && listMissionNotes.length == 0">
        <div class="noMissions" style="font-weight: 800; text-align: center; padding: 20px;">
          <p>
            No Mission Notes</p>
          <p>Click me Add a new Mission Notes to the list.</p>
        </div>
      </ng-container>

      <ng-container *ngIf="filteredMissionNotes.length == 0 && listMissionNotes.length > 0">
        <div class="noMissions" style="font-weight: 800; text-align: center; padding: 20px;">
          <p>
            No Mission Notes found for "{{ queryValue }}"</p>
          <p>Try adjusting your search criteria.</p>
        </div>
      </ng-container>

    </div>
    </div>


    </div>
    </div>