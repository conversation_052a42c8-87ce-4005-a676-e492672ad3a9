import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/SortableListComponent';
import { Component } from '@angular/core';
import { AdamantiumStorage, Character,} from 'src/app/lib/@bus-tier/models';

import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Button } from 'src/app/lib/@pres-tier/data';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { AdamantiumStorageService } from 'src/app/services/adamantium-storage.service';
import { Alert } from 'src/lib/darkcloud';
import { SpinnerService } from './../../../../../../../../../spinner/spinner.service';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-adamantium-storageB-generator',
  templateUrl: './adamantium-storageB-generator.component.html',
})
/**
 * Displays and edits emotion data as a list
 */
export class AdamantiumStorageBGeneratorComponent extends SortableListComponent<AdamantiumStorage> {
  constructor(
    private spinnerService: SpinnerService,
    _activatedRoute: ActivatedRoute,
    protected _adamantiumStorageService: AdamantiumStorageService,
    _userSettingsService: UserSettingsService,
    protected _languageService: LanguageService,
    protected _translationService: TranslationService,
  ) {
    super(_adamantiumStorageService, _activatedRoute, _userSettingsService, 'name');
  }

  public readonly excelButtonTemplate: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  public downloadSceneryOrtography(character: Character)
  {
    this._translationService.getCharacterOrtography(character, true);
  }

  protected override lstInit()
  {

  }
  description = ""
  protected override lstAfterFetchList()
  {
    let soulsTypeCAmount = []
    this._adamantiumStorageService.models;
    if(this._adamantiumStorageService.models.filter(model => model.type === "B").length == 0)
    {
      for(let l = 1; l <= 20; l++)
      {
        this._adamantiumStorageService.createNewAdamantiumStorage(l, "B");
      }
      this._adamantiumStorageService.toSave();
      this.lstFetchLists();
    }

    //remove empty element that just has lablevel == 0.
    this._adamantiumStorageService.models.find(blueprint => 
      {
        if(blueprint.adamantiumLevel === 0)          
        this._adamantiumStorageService.svcToRemove(blueprint.id)
      })
    soulsTypeCAmount = this._adamantiumStorageService.models.filter(model => model.type === "B")
    this.description = `Showing ${soulsTypeCAmount.length} results`;
  }

  async onExcelPaste(): Promise<void>
  {
    this.spinnerService.setState(true)
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter(line => line);

   if(this.DisplayErrors(lines)) return

    for(let l = 0; l < lines.length; l++)
    {
      let line = lines[l];
      let cols = line.split(/\t/);
      
      
      let adamantiumStorage = this._adamantiumStorageService.models.find(ts => ts.adamantiumLevel === +(cols[0].split(' ')
      .join('')
      .split('.')
      .join('')
      .replace(',','.')) && ts.type === "B");
      if(!adamantiumStorage)
      {
        adamantiumStorage = this._adamantiumStorageService.createNewAdamantiumStorage(+(cols[0].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.')),"B");
      }
      
      if(cols[1]?.trim())
      {
        adamantiumStorage.souls = +(cols[1].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.'));
      }
      else
      {
        adamantiumStorage.souls = undefined;
      }
      if(cols[2]?.trim())
      {
        adamantiumStorage.time = +(cols[2].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.'));
      }
      else
      {
        adamantiumStorage.time = undefined;
      }
      if(cols[3]?.trim())
      {
        adamantiumStorage.rubies = +(cols[3].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.'));
      }
      else
      {
        adamantiumStorage.rubies = undefined;
      }
      if(cols[4]?.trim())
      {
        adamantiumStorage.storage = +(cols[4].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.'));
      }
      else
      {
        adamantiumStorage.storage = undefined;
      }

      await  this._adamantiumStorageService.svcToModify(adamantiumStorage);
      await  this._adamantiumStorageService.toSave();

      this.lstFetchLists();
    }

    this.spinnerService.setState(false)
  }

  DisplayErrors(array)
  {
    let count = array[0].split(/\t/)
    if(count.length < 5)
    {
      Alert.showError("Copy the ADAMANTIUM LEVEL column values too!")
      this.spinnerService.setState(false)
      return true
    }
    
    if(count[0] === "")
    {
      Alert.showError("You are probably copying a blank column!")
      this.spinnerService.setState(false)
      return true
    }

    return false
  }

}
