<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="card list-header-row">
      <app-header-with-buttons [cardTitle]="listName"
                               [cardDescription]="cardDescription"
                               [rightButtonTemplates]="[addButtonTemplate]"
                               [isBackButtonEnabled]="false"
                               (cardBackButtonClick)="redirectToItemClasses()">
      </app-header-with-buttons>
      <app-header-search (inputKeyup)="lstOnChangeFilter($event)"></app-header-search>
    </div>
    <!--List-->
    <div class="card">
      <table class="table table-list">
        <thead style="top: 115px">
          <tr>
            <th>Index</th>
            <th class="th-clickable"
                (click)="sortListByParameter('id')">
              ID
            </th>
            <th>Label</th>
            <th class="th-clickable"
                (click)="sortListByParameter('name')">
              Name
            </th>
            <th class="th-clickable"
            (click)="sortListByParameter('notes')">Notes</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="
                let tag of lstIds | tabs;
                let i = index;
                trackBy: trackById
              ">
            <tr id="{{ tag.id }}">
              <td class="td-sort">{{ i + 1 }}</td>
              <td class="td-id">{{ tag.id }}</td>
              <td #colorLabel class="label-color-td"
                  [ngStyle]="{ background: (tag | information)?.hex }"
                  (click)="color.click()">
                <input #color
                       type="color"
                       value="{{ (tag | information)?.hex || '' }}"
                       style="visibility: hidden"
                       (change)="updateColor(tag, color.value, colorLabel)" />
              </td>
              <td>
                  <input class="form-control form-short"
                       type="text"
                       value="{{ (tag | translation : lstLanguage : tag.id : 'name') }}"
                       #name
                       (change)="lstOnChange(tag, 'name', name.value)"
                      />
                  <textarea class="form-control"
                            type="text"
                            value="{{ (tag | translation : lstLanguage : tag.id : 'description') }}"
                            #description
                            (change)="lstOnChange(tag, 'description', description.value)"
                            >
                    </textarea>
              </td>
              <td class="td-notes">
                <textarea class="form-control borderless"
                          value="{{ (tag | translation : lstLanguage : tag.id : 'notes') }}"

                          #notes
                          (change)="lstOnChange(tag, 'notes', notes.value)"></textarea>
              </td>
              <td class="td-actions">
                <button class="btn btn-danger btn-fill btn-remove"
                        (click)="lstPromptRemove(tag)">
                  <i class="pe-7s-close"></i>
                </button>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>
