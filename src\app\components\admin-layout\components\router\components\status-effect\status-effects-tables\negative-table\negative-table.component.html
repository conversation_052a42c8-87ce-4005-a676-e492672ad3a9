<div class="div-container">
    <div class="width: 2221px;">
        <table class="table table-list borderList">
            <thead>    
                <tr>
                    <th [attr.colspan]="titles.length +1">
                        <h3>Negative</h3>
                    </th>
                    <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'" class="addButton"
                        [buttonTemplates]="[excelButtonTemplate]">
                    </app-button-group>
                </tr>
                <ng-container *ngIf="!isListNegativeEmpty">
           
                    <tr>
                        <th class="default-color">Order</th>
                        <th
                          [ngClass]="(title === 'SKILL RESIST USER' || title === 'SKILL RESIST ENEMY') ? 'skill-resist' : 'default-color'"
                          *ngFor="let title of titles">
                          {{title}}
                        </th>
                      </tr>
                      
                </ng-container>
            </thead>
            <ng-container *ngIf="!isListNegativeEmpty">
                <tbody>
                    <tr *ngFor="let item of listNegativeTable; let i = index">
                        <td style="background-color: #ddd; width: 2%;">{{ i + 1 }}</td>               
                            <td class="td-id aligTitle" style="width: 6%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #idNegative [ngClass]="{'empty-input': !idNegative.value}"
                                    [value]="item.idNegative" (change)="changeNegative(i,'idNegative', idNegative.value)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 110px;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #idCategory [ngClass]="{'empty-input': !idCategory.value}"
                                    [value]="item.category" (change)="changeNegative(i,'category', idCategory.value)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 120px;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #idAffliction [ngClass]="{'empty-input': !idAffliction.value}"
                                    [value]="item.idAffliction" (change)="changeNegative(i,'idAffliction', idAffliction.value)" />
                            </td>      
                            <td class="td-id aligTitle" style="width: 110px;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #status [ngClass]="{'empty-input': !status.value}"
                                    [value]="item.status" (change)="changeNegative(i, 'status', status.value)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 7%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #skillResistUser [ngClass]="{'empty-input': !skillResistUser.value}"
                                    [value]="item.skillResistUser" (change)="changeNegative(i, 'skillResistUser', skillResistUser.value)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 7%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #skillResistEnemy [ngClass]="{'empty-input': !skillResistEnemy.value}"
                                    [value]="item.skillResistEnemy" (change)="changeNegative(i, 'skillResistEnemy', skillResistEnemy.value)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 110px;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #operator [ngClass]="{'empty-input': !operator.value}"
                                    [value]="item.operator" (change)="changeNegative(i, 'operator',operator.value)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 110px;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #value [ngClass]="{'empty-input': !value.value}"
                                [value]="item.value" (change)="changeNegative(i, 'value', value.value)" />
                            </td>
                            <td class="td-id" style="width: 200px;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #statusEffectName [ngClass]="{'empty-input': !statusEffectName.value}"
                                [value]="item.statusEffectName" (change)="changeNegative(i, 'statusEffectName', statusEffectName.value)" />
                            </td>
                            <td class="td-id" style="width: 600px; word-break: break-word;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #description [ngClass]="{'empty-input': !description.value}"
                                    [value]="item.description" (change)="changeNegative(i, 'description', description.value)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 7%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #powerPoints [ngClass]="{'empty-input': !powerPoints.value}"
                                    [value]="item.powerPoints" (change)="changeNegative(i, 'powerPoints', powerPoints.value)" />
                            </td>
                            <td class="td-id aligTitle"style="width: 3%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #allNegative [ngClass]="{'empty-input': !allNegative.value}"
                                    [value]="item.allNegative" (change)="changeNegative(i, 'allNegative', allNegative.value)" />
                            </td>  
                            <td class="td-id aligTitle" style="width: 7%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #duration [ngClass]="{'empty-input': !duration.value}"
                                    [value]="item.duration" (change)="changeNegative(i, 'duration', duration.value)" />
                            </td>                
                    </tr>
                </tbody>
            </ng-container>
            <ng-container *ngIf="isListNegativeEmpty">
                <div class="card" style="text-align: center; padding: 20px;">
                    <h3>The list has not yet been imported.</h3>
                </div>
            </ng-container>
        </table>
</div>
    
</div>

