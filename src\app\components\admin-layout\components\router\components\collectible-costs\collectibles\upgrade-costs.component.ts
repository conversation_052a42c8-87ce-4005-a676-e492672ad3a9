import { Component, Input, OnInit} from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ItemClass } from 'src/app/lib/@bus-tier/models/ItemClass';
import { UserSettingsService } from 'src/app/services';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { FILTER_SUFFIX_PATH } from 'src/lib/darkcloud/angular/dsadmin/constants/others';


@Component({
  selector: 'app-upgrade-costs',
  templateUrl: './upgrade-costs.component.html',
})
export class UpgradeCostsComponent implements OnInit
{

  constructor(    
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    protected _translationService: TranslationService,
    protected _languageService: LanguageService
  ) {

  }

  public itemClasses: ItemClass[] = [];
  public reviewOrderAscending: boolean = false;
  @Input() active: string;
  @Input() active3: string;

  ngOnInit(): void {
   
    /*   const tab2 = localStorage.getItem(
        `tab-MiningGeneratorComponent2${FILTER_SUFFIX_PATH}`
      );
      this.active3 = tab2 === 'null' || !tab2 ? 'code' : tab2; */ 
     
    }
  
    public switchToTab2(tab: string) {
      this.active3 = tab

        localStorage.setItem(
          `tab-MiningGeneratorComponent3${FILTER_SUFFIX_PATH}`,
          this.active3      
          );
    }
  }

