import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { FILTER_SUFFIX_PATH } from 'src/lib/darkcloud/angular/dsadmin/constants/others';

@Component({
  selector: 'app-others',
  templateUrl: './others.component.html',
})

export class OthersComponent implements OnInit 
{
  constructor(private router : Router){}
  public activeTab: string;

  ngOnInit(): void 
  {
    const tab = localStorage.getItem(`tab-OthersComponent${FILTER_SUFFIX_PATH}`);
    this.activeTab = tab === 'null' || !tab ? 'keywords' : tab;
  }

  public switchToTab(tab: string) 
  {
    this.activeTab = tab;
    localStorage.setItem(`tab-OthersComponent${FILTER_SUFFIX_PATH}`, this.activeTab);
  }  
}
