<div style="margin-top: 20px" class="list-header-row update">
  <div style="padding-top: 10px;" class="card">
    <app-header-with-buttons [cardTitle]="'Primal Modifier'" [cardDescription]="''" [valueBossLevel]="valueBossLevel"
      [nameClass]="nameClass" [nameRarity]="nameRarity" [type]="type" [rightButtonTemplates]="[excelButtonTemplate]"
      [isBackButtonEnabled]="false">
    </app-header-with-buttons>
  </div>
</div>
<div class="card">
  <table class="table table-list">
    <thead>
      <tr>
        <th class="dark-gray" colspan="2">
          <h4>Primal Modifiers</h4>
        </th>
      </tr>
      <tr>
        <th class="gray">Modifiers</th>
        <th class="gray" style="width: 50%;">Value</th>
      </tr>
    </thead>
    <tbody>
      <ng-container *ngFor="let primal of currentModifier?.primalModifier; let i = index;">
        <tr>
          <td>
            <input class="background-input-table-color form-control form-short" type="string" readonly
              value="{{primal.fieldName}}" />
          </td>
          <td style="display: flex; flex-direction: row;"
            style="justify-content: space-between; display: flex; flex-direction: row;">
          <input class="background-input-table-color form-control form-short "
          placeholder=" " type="number" #primalValue value="{{this.currentModifier.primalModifier[i]?.fieldValue }}"
          (change)="changePrimalValue(primalValue.value, i, primal)"
          [ngClass]="{'inputDifferences': inputDifferences[i]}">
            <select #percentage (change)="changePercentage(this.currentModifier, percentage.value, i)"
              style="padding-left: 30px; padding-right: 20px;"
              [ngStyle]="{'background-color': this.currentModifier.primalModifier[i]?.percentage == '%' ? '#1dc7ea' : ''}">
              <option [value]="''"></option>
              <option [value]="'%'" [selected]="this.currentModifier.primalModifier[i]?.percentage == '%'">
                %
              </option>
            </select>
          </td>
        </tr>
      </ng-container>
    </tbody>
  </table>
</div>