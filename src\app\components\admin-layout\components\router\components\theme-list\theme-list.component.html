<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="card list-header-row">
      <app-header-with-buttons [isBackButtonEnabled]="true"
                               (cardBackButtonClick)="redirectToVideoList()"
                               [cardTitle]="listName"
                               [cardDescription]="cardDescription"
                               [rightButtonTemplates]="[addButtonTemplate]"></app-header-with-buttons>
      <app-header-search (inputKeyup)="lstOnChangeFilter($event)"></app-header-search>
    </div>
    <!--List-->
    <div class="container-fluid">
      <div class="col-md-12">
        <div class="card">
          <table class="table table-list">
            <thead style="top: 115px">
              <tr>
                <th>Index</th>
                <th class="th-clickable"
                    (click)="sortListByParameter('id')">
                  ID
                </th>
                <th class="th-clickable"
                    (click)="sortListByParameter('name')">
                  Name
                </th>
                <th class="th-clickable"
                    (click)="sortListByParameter('assigned')">
                  Assigned
                </th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <ng-container *ngFor="
                let theme of lstIds | themes;
                let i = index;
                trackBy: trackById
              ">
                <tr id="{{ theme.id }}">
                  <td class="td-sort">{{ i + 1 }}</td>
                  <td class="td-id">{{ theme.id }}</td>
                  <td>
                    <input class="form-control form-title form-short"
                           type="text"
                           value="{{ theme.name }}"
                           #name
                           (change)="lstOnChange(theme, 'name', name.value)" />
                  </td>
                  <td>
                    <ng-container *ngIf="
                      (theme | review).assignedAt.length > 0;
                      else notAssigned
                    ">
                      <i class="pe-7s-check success"></i>
                      <div>{{ (theme | review).assignedAt.length }}</div>
                    </ng-container>
                    <ng-template #notAssigned>
                      <i title="Theme not assigned"
                         class="pe-7s-close-circle error"></i>
                    </ng-template>
                  </td>
                  <td class="td-actions">
                    <button class="btn btn-danger btn-fill btn-remove"
                            (click)="lstPromptRemove(theme)">
                      <i class="pe-7s-close"></i>
                    </button>
                  </td>
                </tr>
              </ng-container>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
