<div class="main-menu-efect">
  <div class="container-fluid">
    <div class="list-header-row update">
      <div class="card">
        <div style="display: flex; justify-content: space-between; margin-top: 15px; margin-bottom: 15px;">
          <div class="card-header-content">
            <h3 class="title">Healing Change</h3>
            <p style="width:60vw;" class="category">{{ description}}</p>
          </div>
          <div style="display: flex; align-items: end; justify-content: end; align-items: center;">
            <div class="btn-ailmentChange">
              <button style="margin-left: 2px;" class="btn btn-fill" (click)="btnClickAilment()">Back Ailment</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Healing Chance Per Turn - PARTY -->
    <div class="card paddingTop"  *ngIf="ailmentList.length > 0 && healingChangeListParty.length > 0">
      <div class="healing-table-container">
        <table class="healing-table">
          <thead>
            <tr>
              <th class="ailment-header"  rowspan="3">
                Ailment
               </th>
              </tr>
            <tr>
              <th class="turn-header" colspan="11">
                <h3>{{ titleTurmParty }} 
                  <span>PARTY</span> 
                </h3>                
                <div class="addButton">
                 <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'" 
                      [buttonTemplates]="[excelButtonTemplateParty]">
                </app-button-group>
                </div>
              </th>   
            </tr>
            <tr>
              <th class="turn-number" *ngFor="let turn of getTurnNumbers()">{{ turn }}</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let ailment of ailmentList; let i = index" class="ailment-row">
              <td class="ailment-name">{{ ailment.ailment }}</td>
              <td class="percentage-cell" *ngFor="let turn of getTurnNumbers(); let j = index">
                <input class="background-input-table-color form-control form-short"
                       placeholder="0,0%"
                       type="text"
                       #percentageInput
                       [ngClass]="{'empty-input': !percentageInput.value}"
                       [value]="getHealingChangeForAilment(ailment.id)?.valuePosition?.[j] || '0,0%'"
                       (change)="changePercentageValue(ailment.id, j, percentageInput.value)" />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

        <ng-container *ngIf="ailmentList.length === 0 || healingChangeListParty.length === 0">
      <div class="card" style="text-align: center; padding: 20px;">
        <h3>Empty list of Heal Chance Per Turn - <span>PARTY</span>. Click to create list</h3>
           <div class="btn-excel">
                 <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'" 
                      [buttonTemplates]="[excelButtonTemplateParty]">
                </app-button-group>
           </div>
      </div>
    </ng-container>


        <!-- Healing Chance Per Turn - BOSS -->
    <div class="card paddingTop"  *ngIf="ailmentList.length > 0 && healingChangeListBoss.length > 0">
      <div class="healing-table-container">
        <table class="healing-table">
          <thead>
            <tr>
              <th class="ailment-header"  rowspan="3">
                Ailment
               </th>
              </tr>
            <tr>
              <th class="turn-header" colspan="11">
                <h3>{{ titleTurnBoss }} 
                  <span>BOSS</span> 
                </h3>                
                <div class="addButton">
                 <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'" 
                      [buttonTemplates]="[excelButtonTemplateBoss]">
                </app-button-group>
                </div>
              </th>   
            </tr>
            <tr>
              <th class="turn-number" *ngFor="let turn of getTurnNumbers()">{{ turn }}</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let ailment of ailmentList; let i = index" class="ailment-row">
              <td class="ailment-name">{{ ailment.ailment }}</td>
              <td class="percentage-cell" *ngFor="let turn of getTurnNumbers(); let j = index">
                <input class="background-input-table-color form-control form-short"
                       placeholder="0,0%"
                       type="text"
                       #percentageInput
                       [ngClass]="{'empty-input': !percentageInput.value}"
                       [value]="getHealingChangeForTurnBoss(ailment.id)?.valuePosition?.[j] || '0,0%'"
                       (change)="changePercentageValueTurnBoss(ailment.id, j, percentageInput.value)" />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

      <ng-container *ngIf="ailmentList.length === 0 || healingChangeListBoss.length === 0">
      <div class="card" style="text-align: center; padding: 20px;">
        <h3>Empty list of Heal Chance Per Turn - <span>Boss</span>. Click to create list</h3>
           <div class="btn-excel-boos">
                 <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'" 
                      [buttonTemplates]="[excelButtonTemplateBoss]">
                </app-button-group>
           </div>
      </div>
    </ng-container>

    </div>
  </div>
