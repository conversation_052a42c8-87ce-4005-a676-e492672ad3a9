import { Component, EventEmitter, OnInit, Output } from '@angular/core';

@Component({
  selector: 'app-back-button',
  template: `
    <button
      class="btn btn-fill pull-left back-button"
      (click)="buttonClick.emit()"
    >
      <i class="pe-7s-angle-left"></i>
    </button>
  `,
  styleUrls: ['./back-button.component.scss'],
})
export class BackButtonComponent implements OnInit {
  @Output() buttonClick = new EventEmitter();

  constructor() {}

  ngOnInit(): void {}
}
