<div class="row row-md-6">
  <div class="col-md-6">
    <div class="height-90pc">
      <p class="btn btn-fill label-Boss">
        TOTAL of Classes in Level type BOSS by Area
      </p>
      <table class="table report-table ">
        <thead class="sticky"
               style="top: 0px">
          <tr>
            <th>Area</th>
            <th class="th-clickable"
                (click)="sortClassesByParameter('name')">
              Class
            </th>
            <th class="th-clickable center"
                (click)="sortClassesByParameter('count')">
              Total
            </th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let area of preloadedAreas">
            <ng-container *ngIf="levelClassesByArea[area.id][+LevelType.BOSS].length > 0">
              <tr>
                <td [attr.rowspan]="
                    levelClassesByArea[area.id][+LevelType.BOSS].length
                  ">
                  {{ area.name }}
                </td>
                <td>
                  {{
                  (
                  levelClassesByArea[area.id][+LevelType.BOSS][0]?.id
                  | klass
                  ).name
                  }}
                </td>
                <td class="td-small">
                  {{ levelClassesByArea[area.id][+LevelType.BOSS][0]?.count }}
                </td>
              </tr>
              <ng-container *ngIf="levelClassesByArea[area.id][+LevelType.BOSS].length > 0">
                <tr *ngFor="
                    let o of levelClassesByArea[area.id][+LevelType.BOSS].slice(
                      1
                    );
                    let i = index
                  ">
                  <td>
                    {{ (o?.id | klass).name }}
                  </td>
                  <td class="td-small">{{ o.count }}</td>
                </tr>
              </ng-container>
            </ng-container>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
  <div class="col-md-6">
    <div class="height-90pc">
      <p class="btn btn-fill label-Minion">
        TOTAL of Classes in Level type MINION by Area
      </p>
      <table class="table report-table ">
        <thead class="sticky"
               style="top: 0px">
          <tr>
            <th>Area</th>
            <th class="th-clickable"
                (click)="sortClassesByParameter('name')">
              Class
            </th>
            <th class="th-clickable center"
                (click)="sortClassesByParameter('count')">
              Total
            </th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let area of preloadedAreas">
            <ng-container *ngIf="levelClassesByArea[area.id][+LevelType.MINION].length > 0">
              <tr>
                <td [attr.rowspan]="
                    levelClassesByArea[area.id][+LevelType.MINION].length
                  ">
                  {{ area.name }}
                </td>
                <td>
                  {{
                  (
                  levelClassesByArea[area.id][+LevelType.MINION][0]?.id
                  | klass
                  ).name
                  }}
                </td>
                <td class="td-small">
                  {{ levelClassesByArea[area.id][+LevelType.MINION][0]?.count }}
                </td>
              </tr>
              <ng-container *ngIf="
                  levelClassesByArea[area.id][+LevelType.MINION].length > 0
                ">
                <tr *ngFor="
                    let o of levelClassesByArea[area.id][
                      +LevelType.MINION
                    ].slice(1);
                    let i = index
                  ">
                  <td>{{ (o?.id | klass).name }}</td>
                  <td class="td-small">{{ o.count }}</td>
                </tr>
              </ng-container>
            </ng-container>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>

<div class="row row-md-6">
  <div class="col-md-6">
    <div class="height-90pc">
      <p class="btn btn-fill label-Boss">TOTAL of Classes in Level type BOSS</p>
      <table class="table report-table table-striped ">
        <thead class="sticky"
               style="top: 0px">
          <tr>
            <th class="th-clickable"
                (click)="sortClassesByParameter('name')">
              Class
            </th>
            <th class="th-clickable center"
                (click)="sortClassesByParameter('count')">
              Total
            </th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let o of levelClasses[+LevelType.BOSS]">
            <tr>
              <td class="td-80">{{ (o?.id | klass).name }}</td>
              <td class="td-small">{{ o.count }}</td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
  <div class="col-md-6">
    <div class="height-90pc">
      <p class="btn btn-fill label-Minion">
        TOTAL of Classes in Level type MINION
      </p>
      <table class="table report-table table-striped ">
        <thead class="sticky"
               style="top: 0px">
          <tr>
            <th class="th-clickable"
                (click)="sortClassesByParameter('name')">
              Class
            </th>
            <th class="th-clickable center"
                (click)="sortClassesByParameter('count')">
              Total
            </th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let o of levelClasses[+LevelType.MINION]">
            <tr>
              <td class="td-80">{{ (o?.id | klass)?.name }}</td>
              <td class="td-small">{{ o.count }}</td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>
