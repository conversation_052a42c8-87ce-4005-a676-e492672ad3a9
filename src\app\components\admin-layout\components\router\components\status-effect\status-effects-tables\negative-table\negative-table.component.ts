import { Component, EventEmitter, Output } from '@angular/core';
import { NegativeTable } from 'src/app/lib/@bus-tier/models';
import { Button } from 'src/app/lib/@pres-tier/data';
import { NegativeTableService } from 'src/app/services/negative-table.service';
import { Alert } from 'src/lib/darkcloud';

@Component({
  selector: 'app-negative-table',
  templateUrl: './negative-table.component.html',
  styleUrls: ['./negative-table.component.scss']
})
export class NegativeTableComponent {

  
  titles = ['ID', 'CATEGORY', 'ID AFFLICTION','STATUS', 'SKILL RESIST USER','SKILL RESIST ENEMY',
    'OPERATOR', 'VALUE', 'STATUS EFFECT NAME', 'DESCRIPTION', 'POWER POINTS (PP)', 'ALL', 'DURATION (TURNS)'];
    activeLanguage = 'PTBR';
    listNegativeTable: NegativeTable[] = [];

    @Output() activeTab2 = new EventEmitter<string>();
    isListNegativeEmpty: boolean;
   
    public readonly excelButtonTemplate: Button.Templateable = {
      title: 'Paste content from excel',
      onClick: this.onExcelPaste.bind(this),
      iconClass: 'excel-icon',
      btnClass: Button.Klasses.FILL_ORANGE,
    };
    constructor(
      private _negativeTableService: NegativeTableService
    ){}
   
   
    async ngOnInit(): Promise<void>{
      
        this.removeEmptyItems();
         this.listNegativeTable = this._negativeTableService.models;
         this.isListNegativeEmpty = this.listNegativeTable.length === 0;   
   
      }

      removeEmptyItems() {
        this._negativeTableService.toFinishLoading();
        this._negativeTableService.models = this._negativeTableService.models.filter(negativeItem => negativeItem.idNegative !== "");
        this._negativeTableService.toSave();
      }
   
      async onExcelPaste() {
        const text = await navigator.clipboard.readText();
        const lines = text.split(/\r?\n/).filter(line => line);    
        const processedData: string[][] = [];
      
        if (lines.length > 0) {
          lines.forEach(line => {
            // Divide cada linha em colunas e remove a primeira coluna
            const values = line.split("\t").map(value => value.trim()).slice(1);
      
            processedData.push(values);
          });
      
          // Verifica se o número de colunas em cada linha corresponde ao número de colunas em this.titles
          const isColumnCountValid = processedData.every(row => row.length === this.titles.length);
      
          if (!isColumnCountValid) {
            Alert.showError('Invalid number of columns');
            return;
          }
    
          this._negativeTableService.models = [];
          this._negativeTableService.toSave();
      
          for (let index = 0; index < processedData.length; index++) {
            this._negativeTableService.createNewnNegativeTable(processedData[index]);
          }    
   
          Alert.ShowSuccess('Negative Table imported successfully!');
          this.activeTab2.emit('negativeTable');
          this.ngOnInit();
        }
      }
      
   
      changeNegative(rowIndex: number, name: string, newValue: string){
   
        if (name === 'idNegative') {
         this.listNegativeTable[rowIndex].idNegative = newValue;        
        }
        else if (name === 'category') {
          this.listNegativeTable[rowIndex].category = newValue;        
         }
         else if (name === 'idAffliction') {
          this.listNegativeTable[rowIndex].idAffliction = newValue;        
         }     
         else if (name === 'status') {
          this.listNegativeTable[rowIndex].status = newValue;        
         }
         else if (name === 'skillResistUser') {
           this.listNegativeTable[rowIndex].skillResistUser = newValue;        
          }
        else if (name === 'skillResistEnemy') {
            this.listNegativeTable[rowIndex].skillResistEnemy = newValue;        
          }
         else if (name === 'operator') {
          this.listNegativeTable[rowIndex].operator = newValue;        
         }
         else if (name === 'value') {
          this.listNegativeTable[rowIndex].value = newValue;        
         }
         else if (name === 'statusEffectName') {
          this.listNegativeTable[rowIndex].statusEffectName = newValue;        
         }
         else if (name === 'description') {
          this.listNegativeTable[rowIndex].description = newValue;        
         }
         else if (name === 'powerPoints') {
          this.listNegativeTable[rowIndex].powerPoints = newValue;        
         }
         else if (name === 'allNegative') {
          this.listNegativeTable[rowIndex].allNegative = newValue;        
         }
         else if (name === 'duration') {
           this.listNegativeTable[rowIndex].duration = newValue;        
          }
   
        this._negativeTableService.svcToModify(this.listNegativeTable[rowIndex]);
      }    
   


}
