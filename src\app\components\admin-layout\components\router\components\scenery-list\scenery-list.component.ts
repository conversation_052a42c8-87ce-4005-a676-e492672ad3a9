import { Component } from '@angular/core';
import { SceneryService } from 'src/app/services/scenery.service';
import { Scenery, Area } from 'src/app/lib/@bus-tier/models';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { PopupService } from 'src/app/services/popup.service';
import { AreaService } from 'src/app/services/area.service';
import { Popup } from 'src/lib/darkcloud';
import { Sorting } from 'src/lib/darkcloud/angular/easy-mvc/Sorting';
import { ReviewService } from 'src/app/services/review.service';
import { comparable, Index } from 'src/lib/others';
import { EasyMVC } from 'src/lib/darkcloud/angular';
import { TranslationService } from 'src/app/services/translation.service';
import { LanguageService } from 'src/app/services/language.service';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-scenery-list',
  templateUrl: './scenery-list.component.html',
  styleUrls: ['scenery-list.component.scss']
})

export class SceneryListComponent extends TranslatableListComponent<Scenery> 
{

  constructor(
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    private _sceneryService: SceneryService,
    private _popupService: PopupService,
    private _areaService: AreaService,
    private _reviewService: ReviewService,
    protected override _translationService: TranslationService,
    protected override _languageService: LanguageService
  ) 
  {
    super(_sceneryService, _activatedRoute, _userSettingsService, 'id', _translationService, _languageService);
  }
  protected override lstFilterParameters: EasyMVC.Filter[] = [{ name: 'areaId' }];
  public preloadedAreas: Area[] = [];

  public areaNames: Index<string>;

  override lstInit() 
  {
    this.preloadAreas();
  }

  preloadAreas(): void 
  {
    this.preloadedAreas = [];
    this.areaNames = {};
    this._sceneryService.models.forEach((scenery) => 
    {
      if (scenery.areaId && !this.preloadedAreas.find((area) => area.id === scenery.areaId)
      ) 
      {
        const area = this._areaService.svcFindById(scenery.areaId);
        this.preloadedAreas.push(area);
      }
    });
  }

  protected override filterItem(scenery: Scenery) 
  {
    return ((this.lstFilterValue['areaId'] as string) === 'ALL' || scenery.areaId === (this.lstFilterValue['areaId'] as string));
  }

  override lstAfterInitFetchList() 
  {
    this.areaNames = {};
    this._areaService.models.forEach((area) => 
    {
      this.areaNames[area.id] = area.name;
    });
  }

  protected override specialSort(parameter: Sorting.Parameter) 
  {
    switch (parameter) 
    {
      case 'assigned':
        this._modelService.models.sort((a, b) =>
          this.srtLstOrder === 'ascending'
            ? this._reviewService.reviewResults[a.id].assignedAt.length >
              this._reviewService.reviewResults[b.id].assignedAt.length ? 1 : -1
            : this._reviewService.reviewResults[a.id].assignedAt.length <
              this._reviewService.reviewResults[b.id].assignedAt.length ? 1 : -1);
        break;
      case 'area':
        this._modelService.models.sort((a, b) => this.srtLstOrder === 'ascending'
            ? comparable(this.areaNames[a.areaId]) > comparable(this.areaNames[b.areaId]) ? 1 : -1
            : comparable(this.areaNames[a.areaId]) < comparable(this.areaNames[b.areaId]) ? 1 : -1);
        break;
      default:
        this.defaultSort(parameter);
        break;
    }
  }

  public async toPromptSelectBindArea(scenery: Scenery) 
  {
    const selectedClassButton = await this._popupService.fire<Area, Area>(
      new Popup.Interface(
        {
          title: 'Select Area',
          actionsClass: 'column',
        },
        Popup.toButtonList(
          this._areaService.models,
          {
            parameters: ['hierarchyCode', 'name'],
            mapping: '[<hierarchyCode>]: <name>',
          },
          { undefinedTitle: 'No Area' }
        ),
        {
          hideButton: { value: this._areaService.svcFindById(scenery.areaId) },
        }
      )
    );

    if (!selectedClassButton) return;
    

    scenery.areaId = selectedClassButton.value?.id;
    await this._sceneryService.svcToModify(scenery);
    this.preloadAreas();
  }

  public downloadSceneryOrtography(scenery: Scenery)
  {
    this._translationService.getSceneryOrtography(scenery, true);
  }

  public isSceneryTranslated(scenery: Scenery)
  {
    return this._translationService.checkTranslation(scenery.id, 'EN-US');
  }

  updateColor(model, value, colorLabel: HTMLElement)
  {
    this.updateInformation(model, 'hex', value);
    colorLabel.style.backgroundColor = value;
  }

  changeName(scenery: Scenery, fieldName, value:string) {
    scenery.isReviewedName = false;
    this.lstOnChange(scenery, fieldName, value);
  }

}
