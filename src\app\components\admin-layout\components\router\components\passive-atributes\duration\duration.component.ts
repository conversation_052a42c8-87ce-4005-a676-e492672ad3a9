import { ChangeDetectorRef, Component, EventEmitter, Output } from '@angular/core';
import { Alert } from '../../../../../../../../lib/darkcloud';
import { Duration } from '../../../../../../../lib/@bus-tier/models/Duration';
import { DurationService } from '../../../../../../../services/duration.service';

@Component({
  selector: 'app-duration',
  templateUrl: './duration.component.html',
  styleUrls: ['./duration.component.scss']
})
export class DurationComponent {

  @Output() pasteExcelData = new EventEmitter<void>();
  @Output() descrptionOutput = new EventEmitter<string>();
  excelDuration: any[] = [];
  listDuration: Duration[] = [];

  constructor(
    private ref: ChangeDetectorRef,
    private _durationService: DurationService,
  ){}

  public async ngOnInit()  {
    this._durationService.toFinishLoading();
    this.listDuration =  this._durationService.models;
    this.removeEmptyLines();
    this.descrptionOutput.emit(`Showing ${this.listDuration.length} results`); 

  }

  removeEmptyLines() {
    this.listDuration = this.listDuration.filter(line => line?.idValue && line?.description);
    this.listDuration.forEach((x) => this._durationService.svcToModify(x));
    this._durationService.toSave();
  }

  async onExcelPaste(): Promise<void> {
    const durationFields: string[] = ['ID', 'DESCRIPTION', 'TYPE', 'VALUE'];
    
    try {
       const text = await navigator.clipboard.readText();  
      const lines = text.split(/\r?\n/).filter(line => line); 

     // Remove linhas vazias no final
      while (lines.length > 0 && !lines[lines.length - 1].trim()) {
      lines.pop();
    }     
    
      this._durationService.toFinishLoading();      
      this.listDuration = this._durationService.models = [];
      this._durationService.toSave();
      
     if (lines.length === 0) {
      throw new Error('No data found. Check if you copied the lines correctly.');
    }
    
     const headerColumns = lines[0].split(/\t/).map(col => col.trim());   
      
      if (headerColumns.length !== durationFields.length) {
        throw new Error('Number of incorrect columns. Check Excel format.');
      }      

      this.excelDuration = lines.slice(0).map((line, lineindex) => {
        const cols = line.split(/\t/).map(col => col.trim());  
              // Verifica se a primeira coluna (ID) está vazia
       if (!cols[0]) {
        throw new Error(`The ${lineindex + 1} line has the first empty column. Check the data.`);
     }              
        return {
          idValue: cols[0],       
          description: cols[1],   
          type: cols[2],           
          value: cols[3]        
        };
      });
  

      this.excelDuration.forEach((x) => this._durationService.createNewDuration(x));
      await this._durationService.toSave();
  
      Alert.ShowSuccess('Excel copied successfully!');  
      this.ref.detectChanges();
      this.ngOnInit();
    } catch (error) {
      Alert.showError(error.message || 'Error in processing Excel data.');
    }
  }
  

}
