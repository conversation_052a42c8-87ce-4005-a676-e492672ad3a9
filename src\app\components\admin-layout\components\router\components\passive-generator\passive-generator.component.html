<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons 
          [cardTitle]="listName"
          [cardDescription]="cardDescription"
          [rightButtonTemplates]="[addButtonTemplate]">
        </app-header-with-buttons>
        <app-header-search (inputKeyup)="lstOnChangeFilter($event)" (searchOptions)="lstOnChangeFilterOptions($event)"></app-header-search>
      </div>
    </div>
    <!--List-->
    <div class="card">
      <table class="table table-list">
        <thead class="sticky">
          <tr>
            <th rowspan="4">Index</th>
            <th rowspan="4" class="th-clickable" (click)="sortListByParameter('id')">ID</th>
            <th class="th-clickable" rowspan="4" (click)="sortListByParameter('description')">Description</th>
            <th class="th-clickable" rowspan="4" (click)="sortListByParameter('description')">Preview</th>
            <th colspan="2">Bonus</th>
            <th colspan="4">Condition</th>
            <th rowspan="3">Actions</th>
          </tr>
          <tr>
            <th class="th-clickable" rowspan="2" (click)="sortListByParameter('bonusType')">Type</th>
            <th class="th-clickable" rowspan="2" (click)="sortListByParameter('bonusValue')">Value (%)</th>
            <th colspan="3">Trigger</th>
            <th class="th-clickable" rowspan="2" (click)="sortListByParameter('conditionDuration')">Duration (s)</th>
          </tr>
          <tr>
            <th class="th-clickable" (click)="sortListByParameter('conditionType')">Type</th>
            <th class="th-clickable" (click)="sortListByParameter('conditionComparator')">Operator</th>
            <th class="th-clickable" (click)="sortListByParameter('conditionValue')">Value</th>
          </tr>
          <tr>
            <th>{{ '{' }}bonusType{{ '}' }} </th>
            <th>{{ '{' }}bonusValue{{ '}' }} </th>
            <th>{{ '{' }}triggerType{{ '}' }} </th>
            <th>{{ '{' }}triggerOperator{{ '}' }} </th>
            <th>{{ '{' }}triggerValue{{ '}' }} </th>
            <th>{{ '{' }}duration{{ '}' }} </th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="
                let passive of lstIds | passives;
                let i = index;
                trackBy: trackById
              ">
            <tr id="{{ passive.id }}">
              <td class="td-sort">{{ i + 1 }}</td>
              <td class="td-id">{{ passive.id }}</td>
              <td class="td-notes">
                <textarea 
                  class="form-control"
                  style="height: 60px;"
                  type="text"
                  value="{{ (passive | translation : lstLanguage : passive?.id : 'description') }}"
                  #description
                  (change)="lstOnChange(passive, 'description', description.value)">
                </textarea>
              </td>
              <td class="td-notes">
                  <span class="passive-preview" [innerHTML]="previewDescription(passive)"></span>
              </td>
              <td class="td-actions">
                <select 
                  class="dropdown filter-dropdown auto" style="width: 120px!important;"
                  [(ngModel)]="passive.bonusType"
                  (ngModelChange)="lstOnChange(passive, 'bonusType', passive.bonusType)">
                  <option value="+ Sp.ATK">+ Sp.ATK</option>
                  <option value="ATK">ATK</option>
                  <option value="DEF">DEF</option>
                  <option value="HP">HP</option>
                  <option value="Stun">Stun</option>
                </select>
              </td>
              <td class="td-actions">
                <input
                  type="number" style="width: 120px!important;"
                  [value]="passive.bonusValue"
                  #bonusValue
                  (change)="lstOnChange(passive, 'bonusValue', bonusValue.value)"/>
              </td>
              <td class="td-actions">
                <select 
                  class="dropdown filter-dropdown auto" style="width: 120px!important;"
                  [(ngModel)]="passive.conditionType"
                  (ngModelChange)="lstOnChange(passive, 'conditionType', passive.conditionType)">
                  <option value="HP">HP</option>
                  <option value="BLOCK">BLOCK</option>
                  <option value="TIMER">TIMER</option>
                  <option value="SUPER EFFECTIVE">SUPER EFFECTIVE</option>
                </select>
              </td>
              <td class="td-actions">
                <select 
                  class="dropdown filter-dropdown auto" style="width: 120px!important;"
                  [(ngModel)]="passive.conditionComparator"
                  (ngModelChange)="lstOnChange(passive, 'conditionComparator', passive.conditionComparator)">
                  <option value=""> </option>
                  <option value="<"><</option>
                  <option value=">">></option>
                  <option value="=">=</option>
                </select>
              </td>
              <td class="td-actions">
                <input
                  type="number" style="width: 120px!important;"
                  [value]="passive.conditionValue"
                  #conditionValue
                  (change)="lstOnChange(passive, 'conditionValue', conditionValue.value)"/>
              </td>
              <td class="td-actions">
                <input
                  type="number" style="width: 120px!important;"
                  [value]="passive.conditionDuration"
                  #conditionDuration
                  (change)="lstOnChange(passive, 'conditionDuration', conditionDuration.value)"/>
              </td>
              <td class="td-actions">
                <button 
                  class="btn btn-danger btn-fill btn-remove"
                  (click)="removeFromPassive(passive)">
                  <i class="pe-7s-close"></i>
                </button>
                <button 
                  class="btn btn-gray btn-fill translation-button"
                  (click)="downloadSceneryOrtography(passive)">
                  <div class="mat-translate"></div>
                </button>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>
