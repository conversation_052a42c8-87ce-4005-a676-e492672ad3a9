import { type } from 'os';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import {
  RoadBlockType,
  validOperator,
} from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';

export class RoadBlock
  extends Base<Data.Hard.IRoadBlock, Data.Result.IRoadBlock>
  implements Required<Data.Hard.IRoadBlock>
{
  public static generateId(
    storyBoxId: string,
    markerId: string,
    index: number
  ): string {
    if (storyBoxId) {
      return storyBoxId + '.' + IdPrefixes.ROADBLOCK + index;
    } else {
      return markerId + '.' + IdPrefixes.ROADBLOCK + index;
    }
  }

  constructor(
    public storyBoxId: string,
    public type: Data.Hard.RoadBlockType,
    public index: number,
    dataAccess: RoadBlock['TDataAccess'],

    public itemId: string,
    public itemAmount: number,

    public characterId: string,
    public markerId: string
  ) {
    super(
      {
        hard: {
          id: RoadBlock.generateId(storyBoxId, markerId, index),
          type,
          index,
          itemId,
          itemAmount,
          characterId,
          storyBoxId,
          markerId,
        },
      },
      dataAccess
    );
  }

  get StoryBoxId(): string {
    return this.hard.storyBoxId;
  }

  set MarkerId(value: string) {
    this.hard.markerId = value;
  }

  get MarkerId() {
    return this.hard.markerId;
  }

  get ID() {
    return this.hard.id;
  }

  set Type(value: RoadBlockType | number) {
    this.hard.type = value;
  }

  get Type() {
    return this.hard.type;
  }

  set ItemID(value: string) {
    this.hard.itemId = value;
  }

  get ItemID() {
    return this.hard.itemId;
  }

  set ItemAmount(value: number) {
    this.hard.itemAmount = value;
  }

  get ItemAmount() {
    return this.hard.itemAmount;
  }

  set CharacterID(value: string) {
    this.hard.characterId = value;
  }

  get CharacterID() {
    return this.hard.characterId;
  }

  /////////////////////////////////////

  /**
   * Newly added
   */
  get operator() {
    return this.hard.operator;
  }

  set operator(value: validOperator) {
    this.hard.operator = value;
  }

  get klassId() {
    return this.hard.klassId;
  }
  set klassId(value: string) {
    this.hard.klassId = value;
  }

  get spokeElementId() {
    return this.hard.spokeElementId;
  }
  set spokeElementId(value: string) {
    this.hard.spokeElementId = value;
  }

  get validateIfTrue() {
    if (
      this.hard.validateIfTrue === undefined ||
      this.hard.validateIfTrue === null
    ) {
      return true;
    }
    return this.hard.validateIfTrue;
  }
  set validateIfTrue(value: boolean) {
    this.hard.validateIfTrue = value;
  }

}
