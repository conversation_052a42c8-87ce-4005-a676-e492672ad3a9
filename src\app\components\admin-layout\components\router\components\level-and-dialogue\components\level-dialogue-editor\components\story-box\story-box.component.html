<div class="row" *ngIf="storyBox">
  <div class="col-md-12">
    <div class="card card_background_storyBox " id="{{ storyBox?.id }}">
      <div *ngFor="let roadblock of this.roadBlocksUseds">
        <div id="{{roadblock.id}}">
          <app-roadblock [toRemove]="toRemoveProcessConditionFunc" [currentRoadblock]="roadblock"
            [storyBoxId]="storyBox.id" class="center">
          </app-roadblock>
        </div>

      </div>
      <div *ngIf="this.roadBlocksUseds.length > 1" class="component_AndOr">
        <div style="font-weight: 900; font-size: 20px;"
          [ngStyle]="{'opacity': storyBox.AndOrCondition == 'AND' ? 1 : 0.3, 'color':storyBox.AndOrCondition == 'AND' ? '#ff00aa' : 'black'}">
          AND</div>
        <label class="switch">
          <input [checked]="storyBox.AndOrCondition == 'AND' ? false : true"
            (change)="this.chooseAndOrCondition($event)" type="checkbox">
          <span class="slider"></span>
        </label>
        <div style="font-weight: 900; font-size: 20px;"
          [ngStyle]="{'opacity': storyBox.AndOrCondition == 'OR' ? 1 : 0.3, 'color':storyBox.AndOrCondition == 'OR' ? 'blue' : 'black'}">
          OR</div>
      </div>
      <!--Box header-->
      <div class="header">
        <!--Delete button for box-->
        <button id="delete-button" class="btn btn-fill btn-danger pull-right" (click)="toRemove(storyBox)">
          <i class="pe-7s-close-circle"></i>
        </button>

        <!--Box sort order buttons-->
        <div [title]="storyBox?.id" class="select pull-right">
          <button class="btn btn-success btn-simple btn-invert btn-xs" (click)="toMove(-1)">
            <i class="pe-7s-angle-up-circle"></i>
          </button>
          {{ index }}
          <button class="btn btn-danger btn-simple btn-invert btn-xs" (click)="toMove(1)">
            <i class="pe-7s-angle-down-circle"></i>
          </button>
        </div>

        <div class="row-responsive">
          <!--Dialog box ID name-->
          <p class="category">
            {{ storyBox | typeName }} > {{ storyBox?.id }}
          </p>
          <p class="box-title-watermark">
            {{ storyBox | typeName }}
          </p>
        </div>

        <!--Box title-->
        <!-- <i class="{{ storyBox.name }}"></i>-->
        <div class="component_label">
          <div class="component_label">
            <i class="icon title {{ storyBox | typeIcon }}"></i>
            <input class="form-control form-short form-title" type="text"
              [value]="!storyBox?.label || !isTextValid ? '<<Label for progress condition>>' : storyBox?.label" #label
              (change)="changeLabel($event)" />
          </div>

          <div>
            <h1 *ngIf=" storyBox?.label">
              <i [ngStyle]="{'color': hasLabelText ? '#00ff04' : '#000' }" class="pe-7s-key pe-5x pe-va component_key">
                <div *ngIf="this.usedRoadBlocks.length > 0" class="circle branch-circle margIcon"
                  tooltip="Used on: {{this.usedOnLevels | enumerateList}}" placement='top' delay='250' id="text-size"
                  ttPadding="10px" ttWidth="{{this.usedOnLevels | textSize}}">
                  <p style="text-align: center;">{{this.usedRoadBlocks.length}}</p>
                </div>
              </i>
            </h1>
          </div>

        </div>
        <div class="table-responsive">
          <!--List of Dialog Box Progresses-->
          <table class="table table-hover table-box table table-responsive speech-table">
            <thead>
            </thead>
            <tbody>
              <tr *ngFor="let previouSpeechOrOption of dialogue | previousSpeech : storyBox:index">
                <td class="category">
                  <h4>
                    [...]
                    <span>{{ (previouSpeechOrOption.speakerId | character)?.name }}: </span>
                    <span [innerHTML]="previouSpeechOrOption.message  | rpgFormatting"></span>
                  </h4>
                </td>
              </tr>
              <tr>
                <td>
                  <span class="nu-icon pe-7s-info" data-tooltip="Line represents character introduction"></span>
                </td>
              </tr>
              <ng-container *ngFor="let storyProgressId of storyBox?.storyProgressIds let i = index;">
                <ng-container [ngSwitch]="(storyProgressId | storyProgress) | typeName">
                  <!-- SPEECH -->
                  <ng-container *ngSwitchCase="'Speech'">
                    <tr class="storybox-row" id="{{ storyProgressId }}" app-speech [toMove]="toMoveStoryProgressFunc"
                      [toRemove]="toRemoveStoryProgressFunc" [speechId]="storyProgressId" [index]="i"
                      [preloadedSpeakers]="preloadedSpeakers" [language]="language">
                    </tr>
                  </ng-container>
                  <!-- EVENT -->
                  <ng-container *ngSwitchCase="'Event'">
                    <tr class="storybox-row" id="{{ storyProgressId }}" app-event
                      [preloadedMissionsOfArea]="preloadedMissionsOfArea" [toMove]="toMoveStoryProgressFunc"
                      [toRemove]="toRemoveStoryProgressFunc" [eventId]="storyProgressId" [index]="i">
                    </tr>
                  </ng-container>
                  <!-- MARKER -->
                  <ng-container *ngSwitchCase="'Marker'">
                    <tr class="storybox-row" id="{{ storyProgressId }}" app-marker [toMove]="toMoveStoryProgressFunc"
                      [toRemove]="toRemoveStoryProgressFunc" [markerId]="storyProgressId" [index]="i">
                    </tr>
                  </ng-container>
                </ng-container>
              </ng-container>
            </tbody>
            <div class="row">
              <div class="btn-group">
                <button id="add-event-button" class="btn btn-fill btn-sm btn-mi" (click)="toAddMissionEvent()">
                  <i class="pe-7s-camera center"></i>
                  Add Mission Event
                </button>
                <button id="add-event-button" class="btn btn-sm btn-warning btn-fill btn-item"
                  (click)="toAddItemEvent()">
                  <i class="pe-7s-camera center"></i>
                  Add Item Event
                </button>
                <button id="add-event-button" class="btn btn-sm btn-warning btn-fill btn-boss"
                  (click)="toAddBossEvent()">
                  <i class="pe-7s-camera center"></i>
                  Add Boss Event
                </button>
                <button id="add-event-button" class="btn btn-sm btn-fill btn-cinematic" (click)="toAddCinematicEvent()">
                  <i class="pe-7s-camera center"></i>
                  Add Cinematic Event
                </button>
                <button id="add-event-button" class="btn btn-sm btn-fill btn-loop" (click)="toAddLoopEvent()">
                  <i class="pe-7s-camera center"></i>
                  Add Loop Event
                </button>
              </div>
              <div class="pull-right">
                <button id="add-message-button" class="btn btn-sm btn-success btn-fill" (click)="toAddRoadblock()">
                  <i class="pe-7s-shield center"></i>
                  Add Progress Condition
                </button>
                <button id="add-message-button" class="btn btn-sm btn-primary btn-fill marker-b"
                  (click)="toAddMarker()">
                  <i class="pe-7s-ribbon center"></i>
                  Add Marker
                </button>
                <button id="add-marker-button" class="btn btn-sm btn-success btn-fill" (click)="toAddSpeech()">
                  <i class="pe-7s-comment center"></i>
                  Add Speech
                </button>
              </div>
            </div>
          </table>
        </div>
      </div>
    </div>