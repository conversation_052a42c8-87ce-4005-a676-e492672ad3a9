<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">
        <div class="card-header-content" style="position: absolute; top: 10%;">
          <h3 class="title">Relic Uses</h3>
          <p style="width:60vw;" class="category">{{ description}}</p>
        </div>
      </div>
    </div>

    <div style="width: 40%;">
      <table class="table table-list">
        <thead>
          <tr>
            <th style="width: 3%;">Index</th>
            <th class="th-clickable" (click)="sortByNameRarity()" style="cursor: pointer;">
              Caracter Rarity
              <i class="pe-7s-sort" style="margin-left: 5px; color: #999;"></i>
            </th>
            <th class="th-clickable" (click)="sortByNumberRelicUse()" style="cursor: pointer;">
              Number of Relic Uses
              <i class="pe-7s-sort" style="margin-left: 5px; color: #999;"></i>
            </th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let inspira of listRelicUses; let i = index;">
            <tr>
              <td class="other-td td-index">{{ i + 1 }}</td>
              <td class="other-td"  [ngStyle]="{'background-color': inspira.nameRarity | tierColor : 'Character Rarity', 'color':'#fff'}">
                {{inspira.nameRarity}}
              </td>
              <td class="other-td">
                <input placeholder=" " class="form-control form-short background-input-table-color" type="number"
                  #numberRelicUse [value]="inspira.numberRelicUse"
                  (change)="onChangeAvailableRarity(inspira, numberRelicUse.value)" />
              </td>
          </ng-container>

        </tbody>
      </table>
      <ng-container *ngIf="listRelicUses.length == 0">
        <p class="noWeapons">No Relic Uses</p>
      </ng-container>
    </div>
  </div>

</div>