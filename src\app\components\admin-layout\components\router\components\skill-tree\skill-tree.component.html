<div class="card list-header" style="height: 70px; margin: 15px 30px 0;">
    <div class="header">
        <button class="{{activeTab === 'costs' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
            (click)="switchToTab('costs')">
            Costs
        </button>
        <button class="{{activeTab === 'amplifiers' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
            (click)="switchToTab('amplifiers')" style="margin-left: 5px;">
            Amplifiers
        </button>
    </div>
</div>

<ng-container *ngIf="activeTab === 'costs'">
    <div class="card list-header" style="height: auto; margin: 30px; margin-bottom: 0px;">
        <div class="header">
            <button class="{{activeTab2 === 'golden' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab2('golden')">
                Golden
            </button>
            <button class="{{activeTab2 === 'souls' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab2('souls')" style="margin-left: 5px;">
                Souls
            </button>
            <button class="{{activeTab2 === 'sigilos' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab2('sigilos')" style="margin-left: 5px;">
                Sigilos
            </button>

            <div id="master" class="div-excel">
                <div id="button" style="position: relative; top: 15px;">
                  <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'" class="add-buttons"
                    [buttonTemplates]="[excelButtonTemplate]">
                  </app-button-group>
                </div>

                <div class="card-header-content">
                    <h3>{{ title }}</h3>
                    <div class="text-description">
                      <p style="width:60vw;" class="category">{{ description }}</p>
                    </div>     
                  </div> 
              </div>
        </div>
    </div>
    <ng-container *ngIf="activeTab2 === 'golden'">
        <app-golden  [copySkillTree]="listExcel"></app-golden>
    </ng-container>
    <ng-container *ngIf="activeTab2 === 'souls'">
        <app-souls  [copySkillTree]="listExcel"></app-souls>
    </ng-container>
    <ng-container *ngIf="activeTab2 === 'sigilos'">
        <app-sigilos  [copySkillTree]="listExcel"></app-sigilos>
    </ng-container>

</ng-container>

<ng-container *ngIf="activeTab === 'amplifiers'">
    <app-amplifiers></app-amplifiers>
</ng-container>


