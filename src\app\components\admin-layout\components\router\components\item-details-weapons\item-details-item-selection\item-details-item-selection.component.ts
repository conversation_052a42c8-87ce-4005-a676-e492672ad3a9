import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { Item } from 'src/app/lib/@bus-tier/models';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';
import { ItemService, PassiveService, UserSettingsService, WeaponService, WeaponUpgradeService } from 'src/app/services';
import { CustomService } from 'src/app/services/custom.service';
import { ItemClassService } from 'src/app/services/item-class.service';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';
import { comparable, extractInt } from 'src/lib/others';
import { PassiveSkill } from '../../../../../../../lib/@bus-tier/models/PassiveSkill';
import { PassiveSkillService } from '../../../../../../../services/passiveSkill.service';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-item-details-item-selection',
  templateUrl: './item-details-item-selection.component.html',
})
export class ItemDetailsItemSelectionComponent extends TranslatableListComponent<Item> implements OnInit
{
  @Output() itemSelected: EventEmitter<string> = new EventEmitter();
  listDescription = "";
  itemList: Item[] = [];
  sortList:Item[] = [];
  itemListSort: Item[] = [];
  itemListLineup = [];
  itemListOrder: Item[] = [];
  custom: Custom;
  itemIds : string[] = [];
  sortWeaponOrder = -1;
  sortNameOrder = -1;
  sortIdOrder = -1;
  caseSensitive:boolean = false;
  accentSensitive:boolean = false;
  invertIndexSorting: boolean = true;
  isLinedup:boolean = false;
  itemsDPassiveSkill: PassiveSkill[];
  isPassiveSkill = false;

  constructor(
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    private _itemService: ItemService,
    private _itemClassService: ItemClassService,
    private _weaponService: WeaponService,
    private _weaponUpgradeService: WeaponUpgradeService,
    protected override _translationService: TranslationService,
    protected override _languageService: LanguageService,
    protected _customService: CustomService,
    private _router: Router,
    private weaponService: WeaponService,
    private passiveService: PassiveService,
    private _passiveSkill: PassiveSkillService,
  ) 
  {
    super(_itemService, _activatedRoute, _userSettingsService, 'name', _translationService, _languageService);
  }


  override async ngOnInit()
  {
    this.fixData();
    this.custom = await this._customService.svcGetInstance();
    this.buildItemList();
    this._itemClassService.toFinishLoading();
    this._itemService.toFinishLoading();

    // Set up periodic refresh to detect changes from class selection
    setInterval(async () => {
      await this.refreshItemList();
    }, 500);
  }

  private buildItemList()
  {
    this.itemList = [];
    this.itemIds = [];

    for(let i = 0; i < this._itemClassService.models.length; i++)
    {
      for(let j = 0; j < this._customService.listOfSelectedClasses.length; j++)
      {
        if(this._itemClassService.models[i].name.toLowerCase() == this._customService.listOfSelectedClasses[j].toLowerCase())
        {
          for(let k = 0; k < this._itemClassService.models[i].itemIds.length; k++)
          {
            let item = this._itemService.svcFindById(this._itemClassService.models[i].itemIds[k]);
            if (item && !this.itemList.find(existingItem => existingItem.id === item.id)) {
              this.itemList.push(item);
            }
          }
        }
      }
    }
    this.sortListByName();
    this.getCodeTypeItem();
    this.itemList.forEach((item) => this.itemIds.push(item.id));
    this.listDescription = `Showing ${this.itemList.length} results`;
  }

  private lastSelectedClasses: string[] = [];

  private async refreshItemList(): Promise<void>
  {
    // Check if selected classes have changed
    const currentSelectedClasses = [...this._customService.listOfSelectedClasses];
    if (JSON.stringify(this.lastSelectedClasses) !== JSON.stringify(currentSelectedClasses)) {
      this.lastSelectedClasses = currentSelectedClasses;
      this.buildItemList();
    }
  }

  getCodeTypeItem() {    
       for (let index = 0; index < this._itemClassService.models.length; index++) {
        this.itemList.filter((item) => {
         if(this._itemClassService.models[index].itemIds.includes(item.id)) {
          item.codeType = this._itemClassService.models[index].id;        
        }          
        });        
       }          
  }
  
  fixData(): void
  {
    this.weaponService.models.forEach(weapon => 
    {
      weapon.passives = weapon.passives?.filter(w => this.passiveService.models.map(p => p.id).includes(w));
      this.weaponService.svcToModify(weapon);
    });
  }

  selectCollectible(character,itemListLineup)
  {
    this._weaponService.currentSelectedCharacter = character.id;
    this._router.navigate(['weaponRecord'], {queryParams: {collectibles:this.itemIds, character:character.id, itemListLineup}, skipLocationChange: true});
  }

  search(term: string): void 
  {
    this.itemList = [];
    this.lstSearchTerm = term;

    if(this._customService.listOfSelectedClasses.length == 0) return;
    
    for (const model of this._itemService.models) 
      if (this.itemMatchesQuery(model, term)) this.itemList.push(model);    
    
    if(!term) this.itemList = this._itemService.models;
    
    this.listDescription = `Showing ${this.itemList.length} results`;
  }
  
  itemMatchesQuery(model:Item, query: string): boolean
  {
    let nameMatch:boolean = false;
    let descriptionMatch:boolean = false;

    const normalizedQuery = this.accentSensitive ? query.normalize('NFKD').replace(/[\u0300-\u036f]/g, '') : query;
    const name = this.caseSensitive ? model.name : model.name;

    if (this.accentSensitive) 
    {
      if(this.caseSensitive)
      {
        let localName = name.normalize('NFKD').replace(/[\u0300-\u036f]/g, '');
        nameMatch = localName.includes(normalizedQuery);
      }
      else
      {
        let localName = name.normalize('NFKD').replace(/[\u0300-\u036f]/g, '');
        nameMatch = localName.toLowerCase().includes(normalizedQuery.toLowerCase());
      }
    }
    else if(this.caseSensitive)
    {
      let localName = name.normalize('NFKD').replace(/[\u0300-\u036f]/g, '');
      nameMatch = localName.includes(normalizedQuery);
    }
    else nameMatch = name.toLowerCase().trim().includes(normalizedQuery.toLowerCase().trim());    
    
    const description = this.caseSensitive ? model?.description : model?.description?.toLowerCase();
    if (this.accentSensitive)
    {
      if(this.caseSensitive)
      {
        let localDescription = description.normalize('NFKD').replace(/[\u0300-\u036f]/g, '');
        descriptionMatch = localDescription.includes(normalizedQuery);
      }
      else
      {
        let localDescription = description.normalize('NFKD').replace(/[\u0300-\u036f]/g, '');
        descriptionMatch = localDescription.toLowerCase().includes(normalizedQuery.toLowerCase());
      }
    }
    else if(this.caseSensitive)
    {
      let localDescription = description.normalize('NFKD').replace(/[\u0300-\u036f]/g, '');
      descriptionMatch = localDescription.includes(normalizedQuery);
    }
    else descriptionMatch = description?.includes(normalizedQuery);

    return nameMatch || descriptionMatch;
  }


  searchFilterOptions(event)
  {
    this.caseSensitive = event.caseSensitive;
    this.accentSensitive = event.accentSensitive;
  }

  sortListById()
  {  
    
    this.itemList.sort((a,b) => 
    {
      let itemNameA = comparable(extractInt(a.id));
      let itemNameB = comparable(extractInt(b.id));

      if(itemNameA > itemNameB) return this.invertIndexSorting? 1 : -1;      
      else if(itemNameB > itemNameA) return this.invertIndexSorting? -1 : 1;      
      else return 0;
    });    
    this.invertIndexSorting = !this.invertIndexSorting;   
  }

  override sortListByName()
  {
    this.sortNameOrder *= -1;
    this.itemList.sort((a, b) => 
    {  
      return this.sortNameOrder * a.name.localeCompare(b.name);
    });
    this.isLinedup = true;   

    if(this.isLinedup) {
      this.itemList.forEach((item) => this.itemListLineup.push(item.id));
    }
    
  }

  sortListByWeapon()
  {
    this.sortWeaponOrder *= -1;
    this.itemList.sort((a, b) => 
    {
      let aCount = 0;
      let bCount = 0;
      if(this.checkIfWeaponIsCompleted(a, 1) != "rgb(128, 128, 128)") aCount++;
      let color = this.checkIfWeaponIsCompleted(a, 2);
      if(color != "rgb(128, 128, 128)")
      {
        if(color == "rgb(239 145 83)") aCount += 0.5;        
        else aCount++;        
      }
      if(this.checkIfWeaponIsCompleted(a, 3) != "rgb(128, 128, 128)") aCount++;
      if(this.checkIfWeaponIsCompleted(b, 1) != "rgb(128, 128, 128)") bCount++;
      color = this.checkIfWeaponIsCompleted(b, 2);
      if(color != "rgb(128, 128, 128)")
      {
        if(color == "rgb(239 145 83)") bCount += 0.5;        
        else bCount++;        
      }
      if(this.checkIfWeaponIsCompleted(b, 3) != "rgb(128, 128, 128)") bCount++;
      return this.sortWeaponOrder * (aCount - bCount);
    });
  }

  selectWeapon(item: Item)
  {
    this.selectCollectible(item, this.itemListLineup);
    this._customService.setCustomField(item.id, 'selectedWeaponId');
    this._customService.currentSelectedItem = item.id;
  }

  checkIfCompleted(item: Item)
  {
    if(this.checkIfWeaponIsCompleted(item, 1) === "rgb(128, 128, 128)") return false;
    if(this.checkIfWeaponIsCompleted(item, 2) === "rgb(128, 128, 128)") return false;
    if(this.checkIfWeaponIsCompleted(item, 3) === "rgb(128, 128, 128)") return false;
    return true;
  }

  checkIfWeaponIsCompleted(item: Item, phase: number)
  {
    let weapon = this._weaponService.models.find(w => w.itemId === item.id);
    this._passiveSkill.toFinishLoading();
    this.itemsDPassiveSkill = this._passiveSkill.models;

   let passiveSkill = this._passiveSkill.models.find(x => x.idNameWeapon === item.id && x.descriptions.length > 0);

    if(phase === 1)
    {
      if(!weapon) return "rgb(128, 128, 128)";
    
      if(weapon.description?.trim().split(' ').join('').length == 0)
        weapon.description = weapon.description?.trim().split(' ').join('')
      
      if(weapon.description || weapon.charactersId?.length > 0 || weapon.classesId?.length > 0 || weapon.shake || weapon.hit || weapon.split)
      {        
        return "rgb(29, 199, 234)";
      }
    }
    else if(phase === 2)
    {
      let weaponUpgrades = this._weaponUpgradeService.models.filter(wu => wu.itemId == item.id);
      if(!(weaponUpgrades?.length > 0)) return "rgb(128, 128, 128)";
      let counter = 0;
      weaponUpgrades.forEach(wu => 
      {
        if(counter < 130)
        {
          if(wu.atkw) counter++;
          if(wu.cooldown) counter++;
          if(wu.gold) counter++;
          if(wu.ichor) counter++;
          if(wu.souls) counter++;
          if(wu.time) counter++;
          if(wu.rubies) counter++;
          if(wu.titanium) counter++;
          if(wu.adamantium) counter++;
          if(wu.hellnium) counter++;
        }
      });
      
      if(counter >= 130) return "rgb(29, 199, 234)";      
      if(counter > 0) return "rgb(239, 145, 83)";      
    }
    else if(phase === 3)
    {   
      if(!passiveSkill) return "rgb(128, 128, 128)";
      if(passiveSkill) return "rgb(29, 199, 234)";      
    }

    return "rgb(128, 128, 128)";
  }

  public async downloadWeaponOrtography(item: Item) 
  {
    await this._weaponService.toFinishLoading();

    let weapon = this._weaponService.models.find(w => w.itemId === item.id);
    weapon.description = weapon.description;
    await this._weaponService.svcToModify(weapon);
    await this._weaponService.toSave();
    this._translationService.getWeaponOrtography(weapon, true);
  }
}
