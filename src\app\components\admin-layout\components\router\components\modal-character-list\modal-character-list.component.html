<div class="background-div" (mousedown)="handleOutsideMouseClick($event)" style="overflow-y: auto;">
    <div id="modal-close" @popup class="popup-report" *ngIf="popupStats" style="background-color: black;">
      <div  class="modalClose">	
        <button type="button" class="close" data-dismiss="modal" aria-label="Fechar">
        <span aria-hidden="true">&times;</span>
      </button>	      
      </div> 
      <div>      
          <span>Assigned at {{character | review}}</span>
      </div>    
  </div>  
  </div>