import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ItemClass } from 'src/app/lib/@bus-tier/models/ItemClass';
import { UserSettingsService } from 'src/app/services';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { FILTER_SUFFIX_PATH } from 'src/lib/darkcloud/angular/dsadmin/constants/others';

@Component({
  selector: 'app-upgrades-generator',
  templateUrl: './upgrades-generator.component.html',
})
export class UpgradesGeneratorComponent implements OnInit
{
  public itemClasses: ItemClass[] = [];
  public reviewOrderAscending: boolean = false;
  public activeTab: string;

  constructor(
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    protected _translationService: TranslationService,
    protected _languageService: LanguageService
  ) {}

  ngOnInit(): void 
  {
    const tab = localStorage.getItem(`tab-upgradesGeneratorComponent${FILTER_SUFFIX_PATH}`);
    this.activeTab = tab === 'null' || !tab ? 'class-selection' : tab;
  }

  public switchToTab(tab: string) 
  {
    this.activeTab = tab;
    localStorage.setItem(`tab-upgradesGeneratorComponent${FILTER_SUFFIX_PATH}`, this.activeTab);
  }
}
