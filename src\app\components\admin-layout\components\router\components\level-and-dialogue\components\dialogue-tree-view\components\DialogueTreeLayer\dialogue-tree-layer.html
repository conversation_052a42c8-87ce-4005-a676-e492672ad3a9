<!-- Main container for dialogue tree layer -->
<div>
  <!-- Start marker leaf (green circle at the beginning of the dialogue tree) -->
  <dialogue-tree-leaf
    *ngIf="layerType == 'start'"
    [leafType]="layerType"
    [deadLeaf]="isDeadLayer"
    [selectedChoiceOptions]="selectedChoiceOptions"
    [selectedInvestigationOptions]="selectedInvestigationOptions"
    [selectedDilemmaOptions]="selectedDilemmaOptions"
    [roadblockEvaluationEnabled]="roadblockEvaluationEnabled"
    (choiceOptionSelected)="onChoiceOptionSelected($event.optionBoxId, $event.optionId)"
    (investigationOptionSelected)="onInvestigationOptionSelected($event.optionBoxId, $event.optionId, $event.selected)"
    (dilemmaOptionSelected)="onDilemmaOptionSelected($event.dilemmaBoxId, $event.dilemmaId)">
  </dialogue-tree-leaf>

  <!-- STORY BOX LAYER: Contains narrative text and story progression -->
  <ng-container *ngIf="storyBox !== undefined">
    <!-- Incoming branch connecting from previous layer -->
    <dialogue-tree-branch
      [inPaths]="inPaths"
      [outPaths]="[true]"
      [hasRoadblocks]="incomingBranchRoadblockCount > 0"
      [roadblockCount]="incomingBranchRoadblockCount"
      [hasOptionRoadblocks]="hasPreviousLayerOptionRoadblocks"
      [selectedChoiceOptions]="selectedChoiceOptions"
      [selectedDilemmaOptions]="selectedDilemmaOptions"
      [parentOptionBoxId]="getPreviousOptionBoxId()"
      [optionIds]="getPreviousOptionIds()"
      [isInvestigationBox]="isPreviousLayerInvestigationBox()"
      [isFromOptionLayer]="isPreviousLayerOptionLayer()"
      [roadblockEvaluationEnabled]="roadblockEvaluationEnabled"
      [selectedChoiceOptionsForRoadblocks]="selectedChoiceOptions"
      [selectedInvestigationOptions]="selectedInvestigationOptions"
      [selectedDilemmaOptionsForRoadblocks]="selectedDilemmaOptions">
    </dialogue-tree-branch>

    <!-- Story box display element -->
    <dialogue-tree-leaf
      [objectId]="storyBox.id"
      leafType="StoryBox"
      [deadLeaf]="isDeadLayer"
      [selectedChoiceOptions]="selectedChoiceOptions"
      [selectedInvestigationOptions]="selectedInvestigationOptions"
      [selectedDilemmaOptions]="selectedDilemmaOptions"
      [roadblockEvaluationEnabled]="roadblockEvaluationEnabled"
      (choiceOptionSelected)="onChoiceOptionSelected($event.optionBoxId, $event.optionId)"
      (investigationOptionSelected)="onInvestigationOptionSelected($event.optionBoxId, $event.optionId, $event.selected)"
      (dilemmaOptionSelected)="onDilemmaOptionSelected($event.dilemmaBoxId, $event.dilemmaId)">
    </dialogue-tree-leaf>
  </ng-container>


  <!-- CHOICE/INVESTIGATION BOX LAYER: Contains user-selectable options -->
  <ng-container *ngIf="optionBox !== undefined">
    <!-- Incoming branch connecting from previous layer -->
    <dialogue-tree-branch
      [inPaths]="inPaths"
      [outPaths]="[true]"
      [hasRoadblocks]="incomingBranchRoadblockCount > 0"
      [roadblockCount]="incomingBranchRoadblockCount"
      [hasOptionRoadblocks]="hasPreviousLayerOptionRoadblocks"
      [selectedChoiceOptions]="selectedChoiceOptions"
      [selectedDilemmaOptions]="selectedDilemmaOptions"
      [parentOptionBoxId]="getPreviousOptionBoxId()"
      [optionIds]="getPreviousOptionIds()"
      [isInvestigationBox]="isPreviousLayerInvestigationBox()"
      [isFromOptionLayer]="isPreviousLayerOptionLayer()"
      [roadblockEvaluationEnabled]="roadblockEvaluationEnabled"
      [selectedChoiceOptionsForRoadblocks]="selectedChoiceOptions"
      [selectedInvestigationOptions]="selectedInvestigationOptions">
    </dialogue-tree-branch>

    <!-- Parent option box display (Choice or Investigation) -->
    <dialogue-tree-leaf
      [objectId]="optionBox.id"
      [leafType]="optionBox.type == 0 ? 'ChoiceBox' : 'InvestigationBox'"
      [deadLeaf]="isDeadLayer"
      [selectedChoiceOptions]="selectedChoiceOptions"
      [selectedInvestigationOptions]="selectedInvestigationOptions"
      [roadblockEvaluationEnabled]="roadblockEvaluationEnabled"
      (choiceOptionSelected)="onChoiceOptionSelected($event.optionBoxId, $event.optionId)"
      (investigationOptionSelected)="onInvestigationOptionSelected($event.optionBoxId, $event.optionId, $event.selected)">
    </dialogue-tree-leaf>

    <!-- Child options section -->
    <div *ngIf="optionsIds?.length > 0">
      <!-- Outgoing branches to child options (includes dice failures) -->
      <dialogue-tree-branch
        [inPaths]="[true]"
        [outPaths]="outPaths"
        [drawAllLinesOutput]="true"
        [hasRoadblocks]="hasRoadblocks"
        [roadblockCount]="outgoingBranchRoadblockCount"
        [hasOptionRoadblocks]="hasAnyOptionRoadblocks()"
        [selectedChoiceOptions]="selectedChoiceOptions"
        [parentOptionBoxId]="optionBox?.id"
        [optionIds]="getCombinedOptionIds()"
        [isInvestigationBox]="optionBox?.type === 1"
        [roadblockEvaluationEnabled]="roadblockEvaluationEnabled"
        [selectedChoiceOptionsForRoadblocks]="selectedChoiceOptions"
        [selectedInvestigationOptions]="selectedInvestigationOptions">
      </dialogue-tree-branch>

      <!-- Option display wrapper -->
      <div class="tree-leaf-wrapper">
        <!-- Combined options and dice variants in correct order -->
        <dialogue-tree-leaf
        *ngFor="let optionId of getCombinedOptionIdsInOrder()"
          [objectId]="optionId"
          [leafType]="getLeafTypeForOptionId(optionId)"
          class="tree-leaf-column"
          [ngClass]="{'has-roadblocks': hasAnyOptionRoadblocks()}"
          [deadLeaf]="isDeadLayer"
          [selectedChoiceOptions]="selectedChoiceOptions"
          [selectedInvestigationOptions]="selectedInvestigationOptions"
          [selectedDilemmaOptions]="selectedDilemmaOptions"
          [roadblockEvaluationEnabled]="roadblockEvaluationEnabled"
          (choiceOptionSelected)="onChoiceOptionSelected($event.optionBoxId, $event.optionId)"
          (investigationOptionSelected)="onInvestigationOptionSelected($event.optionBoxId, $event.optionId, $event.selected)"
          (dilemmaOptionSelected)="onDilemmaOptionSelected($event.dilemmaBoxId, $event.dilemmaId)">
        </dialogue-tree-leaf>
      </div>
    </div>
  </ng-container>


  <!-- DILEMMA BOX LAYER: Contains moral choice options -->
  <ng-container *ngIf="dilemmaBox !== undefined">
    <!-- Incoming branch connecting from previous layer -->
    <dialogue-tree-branch
      [inPaths]="inPaths"
      [outPaths]="[true]"
      [hasRoadblocks]="hasRoadblocks"
      [roadblockCount]="incomingBranchRoadblockCount"
      [hasOptionRoadblocks]="hasPreviousLayerOptionRoadblocks"
      [selectedChoiceOptions]="selectedChoiceOptions"
      [selectedDilemmaOptions]="selectedDilemmaOptions"
      [parentOptionBoxId]="getPreviousOptionBoxId()"
      [optionIds]="getPreviousOptionIds()"
      [isInvestigationBox]="isPreviousLayerInvestigationBox()"
      [isFromOptionLayer]="isPreviousLayerOptionLayer()"
      [roadblockEvaluationEnabled]="roadblockEvaluationEnabled"
      [selectedChoiceOptionsForRoadblocks]="selectedChoiceOptions"
      [selectedInvestigationOptions]="selectedInvestigationOptions"
      [selectedDilemmaOptionsForRoadblocks]="selectedDilemmaOptions">
    </dialogue-tree-branch>

    <!-- Parent dilemma box display -->
    <dialogue-tree-leaf
      [objectId]="dilemmaBox.id"
      leafType="DilemmaBox"
      [deadLeaf]="isDeadLayer"
      [selectedChoiceOptions]="selectedChoiceOptions"
      [selectedInvestigationOptions]="selectedInvestigationOptions"
      [selectedDilemmaOptions]="selectedDilemmaOptions"
      [roadblockEvaluationEnabled]="roadblockEvaluationEnabled"
      (choiceOptionSelected)="onChoiceOptionSelected($event.optionBoxId, $event.optionId)"
      (investigationOptionSelected)="onInvestigationOptionSelected($event.optionBoxId, $event.optionId, $event.selected)"
      (dilemmaOptionSelected)="onDilemmaOptionSelected($event.dilemmaBoxId, $event.dilemmaId)">
    </dialogue-tree-leaf>

    <!-- Child dilemma options section -->
    <div *ngIf="dilemmasIds?.length > 0">
      <!-- Outgoing branches to child dilemmas -->
      <dialogue-tree-branch
        [inPaths]="[true]"
        [outPaths]="outPaths"
        [drawAllLinesOutput]="true"
        [hasRoadblocks]="hasRoadblocks"
        [roadblockCount]="outgoingBranchRoadblockCount"
        [hasOptionRoadblocks]="hasAnyOptionRoadblocks()"
        [selectedDilemmaOptions]="selectedDilemmaOptions"
        [parentOptionBoxId]="dilemmaBox?.id"
        [optionIds]="dilemmasIds"
        [roadblockEvaluationEnabled]="roadblockEvaluationEnabled"
        [selectedChoiceOptionsForRoadblocks]="selectedChoiceOptions"
        [selectedInvestigationOptions]="selectedInvestigationOptions"
        [selectedDilemmaOptionsForRoadblocks]="selectedDilemmaOptions">
      </dialogue-tree-branch>

      <!-- Dilemma options display wrapper -->
      <div class="tree-leaf-wrapper">
        <dialogue-tree-leaf
        *ngFor="let dilemmaId of dilemmasIds"
          [objectId]="dilemmaId"
          leafType="DilemmaOption"
          class="tree-leaf-column"
          [ngClass]="{'has-roadblocks': hasAnyOptionRoadblocks()}"
          [deadLeaf]="isDeadLayer"
          [selectedChoiceOptions]="selectedChoiceOptions"
          [selectedInvestigationOptions]="selectedInvestigationOptions"
          [selectedDilemmaOptions]="selectedDilemmaOptions"
          [roadblockEvaluationEnabled]="roadblockEvaluationEnabled"
          (choiceOptionSelected)="onChoiceOptionSelected($event.optionBoxId, $event.optionId)"
          (investigationOptionSelected)="onInvestigationOptionSelected($event.optionBoxId, $event.optionId, $event.selected)"
          (dilemmaOptionSelected)="onDilemmaOptionSelected($event.dilemmaBoxId, $event.dilemmaId)">
        </dialogue-tree-leaf>
      </div>
    </div>
  </ng-container>

  <!-- END LAYER: Final marker for dialogue termination -->
  <dialogue-tree-branch
    *ngIf="layerType == 'end'"
    [inPaths]="inPaths"
    [outPaths]="[true]"
    [hasRoadblocks]="false"
    [roadblockCount]="0">
  </dialogue-tree-branch>

  <dialogue-tree-leaf
    *ngIf="layerType == 'end'"
    [leafType]="layerType"
    [deadLeaf]="isDeadLayer"
    [designFinalLine]="false"
    [isFinishDialogueEnd]="isDeadEnd"
    [parentIsDialogueOption]="parentIsDialogueOption"
    [selectedChoiceOptions]="selectedChoiceOptions"
    [selectedInvestigationOptions]="selectedInvestigationOptions"
    [selectedDilemmaOptions]="selectedDilemmaOptions"
    [roadblockEvaluationEnabled]="roadblockEvaluationEnabled"
    (choiceOptionSelected)="onChoiceOptionSelected($event.optionBoxId, $event.optionId)"
    (investigationOptionSelected)="onInvestigationOptionSelected($event.optionBoxId, $event.optionId, $event.selected)"
    (dilemmaOptionSelected)="onDilemmaOptionSelected($event.dilemmaBoxId, $event.dilemmaId)">
  </dialogue-tree-leaf>

</div>
