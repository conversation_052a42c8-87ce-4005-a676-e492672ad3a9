<div class="row">
    <div class="col-md-12">
        <div class="report-table-wrapper">
            <table class="table report-table rable-striped">
                <thead class="sticky" style="top: 1px;">
                    <tr>
                        <th></th>
                        <th class="th-clickable"
                            (click)="sortArrayByType(this.tempProgressCondition)">Type</th>
                        <th  class="th-clickable"
                            (click)="sortArrayByLocation(this.tempProgressCondition)">Location</th>
                        <th class="th-clickable"
                            (click)="sortArrayByCondition(this.tempProgressCondition)">Condition</th>
                    </tr>
                </thead>
                <tbody>
                    <ng-container *ngFor="let prog of this.tempProgressCondition; let i = index;">
                        <tr
                        [title]="prog.roadblock?.id"
                        class="tr-clickable"
                        (click)="access(prog.roadblock?.StoryBoxId)">
                            <td class="ind"><span>{{i}}. </span></td>
                            <td style="width: 10% !important;">{{ prog.roadblock?.Type | roadblockTypeDisplay }}</td>
                            <td style=" width: 20% !important;" >
                            <span 
                                [ngStyle]="{'background-color': prog.color == undefined ? '' : rgb(prog.color)}"
                                style="height: 10px;
                                width: 10px;
                                border-radius: 50%;
                                display: inline-block;" class="dot">
                            </span>{{ [prog.roadblock?.ID] | location }}</td>
                           <td class="td-20" [ngStyle]="{'color': CheckConditionType(prog.roadblock) ? 'red' : ''}">{{ prog.roadblock | displayCondition }}</td>
                        </tr>
                    </ng-container>
                </tbody>
            </table>
        </div>
    </div>
</div>
