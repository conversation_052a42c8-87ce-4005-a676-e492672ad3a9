    <div class="m-margin">
        <table class="table-bordered">
            <thead>
              <tr>
                <th class="trBC" style="width: 5%;">Index</th>
                <th class="trBC" style="width: 8%;">DC min</th>
                <th class="trBC" style="width: 8%;">DC max</th>
                <th class="trBC">Description</th>
                <th class="trBC" style="width: 8%;">Actions</th>
              </tr>
            </thead>
            <tbody>
              <ng-container *ngFor="let item of listDCGuide; let i = index">
                <tr>
                  <td class="gray">{{i + 1}}</td>
                  <td>
                    <input style="width: 100%;"
                           class="background-input-table-color form-control form-short "
                           placeholder=" " type="number" #dcMin
                           [(ngModel)]="item.dcMin"
                           (change)="changeDCValue(i, 'dcMin', dcMin.value)" />
                  </td>
                  <td>
                    <input style="width: 100%;"
                           class="background-input-table-color form-control form-short "
                           placeholder=" " type="number" 
                           [(ngModel)]="item.dcMax" #dcMax
                           (change)="changeDCValue(i, 'dcMax', dcMax.value)" />
                  </td>
                  <td>
                    <input style="width: 100%;"
                           class="background-input-table-color form-control form-short "
                           placeholder=" " type="text"
                           [(ngModel)]="item.description"
                           (change)="changeDCValue(i, 'description', item.description)" />
                  </td>
                  <td class="td-actions"
                      style="display: flex; justify-content: space-around;">
                    <button class="btn btn-danger btn-fill btn-remove"
                            (click)="removeLineDC(i)">
                      <i class="pe-7s-close"></i>
                    </button>
                  </td>
                </tr>
              </ng-container>
            </tbody>
          </table>
      
    </div>
