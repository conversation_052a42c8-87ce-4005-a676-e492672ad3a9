import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { StoryBoxService } from 'src/app/services/story-box.service';
import { OptionBoxService } from 'src/app/services/option-box.service';
import { MarkerService } from 'src/app/services/marker.service';
import { OptionService } from 'src/app/services/option.service';
import { StoryBox, OptionBox, Option, DilemmaBox, Dilemma} from 'src/app/lib/@bus-tier/models';
import { RoadBlock } from 'src/app/lib/@bus-tier/models/RoadBlock';
import { AnswerDilemmaBoxService } from 'src/app/services';
import { DilemmaService } from 'src/app/services';
import { DilemmaBoxService } from 'src/app/services';
import { RoadBlockService } from 'src/app/services/road-block.service';
import { LabelColorService } from 'src/app/services/label-color.service';
import { DiceColorService } from 'src/app/services/dice-color.service';
import { LevelHelperService } from 'src/app/services';
import { MarkerType } from 'src/lib/darkcloud/dialogue-system';
import { RoadBlockType } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard';
import { DialogueTreeRoadblockEvaluationService } from 'src/app/services/dialogue-tree-roadblock-evaluation.service';
import { DialogueTreeSelectionStateService } from 'src/app/services/dialogue-tree-selection-state.service';
import { DialogueTreeLabelManagementService } from 'src/app/services/dialogue-tree-label-management.service';
import { DialogueTreeUtilityService } from 'src/app/services/dialogue-tree-utility.service';

@Component({
  selector: 'dialogue-tree-leaf',
  templateUrl: './dialogue-tree-leaf.html',
  styleUrls: ['./dialogue-tree-leaf.scss'],
})
/**
 * Dialogue tree leaf component that renders individual dialogue elements.
 * Handles different types: StoryBox, OptionBox, DilemmaBox, Options, and special markers.
 * Supports interactive selection, roadblock evaluation, and visual indicators.
 */
export class DialogueTreeLeafComponent implements OnInit
{
  // Component configuration
  @Input() public leafType: string = 'default';
  @Input() public deadLeaf: boolean = false;
  @Input() public designFinalLine: boolean = true;
  @Input() public isFinishDialogueEnd: boolean = false;
  @Input() public parentIsDialogueOption: boolean = false;
  @Input() public objectId: string;

  // Interactive state
  @Input() public selectedChoiceOptions: Map<string, string> = new Map();
  @Input() public selectedInvestigationOptions: Map<string, Set<string>> = new Map();
  @Input() public selectedDilemmaOptions: Map<string, string> = new Map();
  @Input() public roadblockEvaluationEnabled: boolean = true;

  // Events
  @Output() public choiceOptionSelected = new EventEmitter<{optionBoxId: string, optionId: string | null}>();
  @Output() public investigationOptionSelected = new EventEmitter<{optionBoxId: string, optionId: string, selected: boolean}>();
  @Output() public dilemmaOptionSelected = new EventEmitter<{dilemmaBoxId: string, dilemmaId: string | null}>();
  @Output() isDeadEnd: EventEmitter<boolean> = new EventEmitter();
  @Output() isRestartDialogue: EventEmitter<boolean> = new EventEmitter();

  // Data models for different dialogue element types
  private storyBox: StoryBox;
  private optionBox: OptionBox;
  private option: Option;
  private dilemmaBox: DilemmaBox;
  private dilemma: Dilemma;

  // Dice system properties
  private parentOption: Option;
  private isDiceNegativeOutcome: boolean = false;

  // Display properties
  public text: string;
  public subText: string;
  public hasRoadBlock: boolean = false;
  public roadBlocks: RoadBlock[] = [];
  public deadEnd: boolean = false;
  public restartDialogue: boolean = false;

  constructor(
    private _optionBoxService: OptionBoxService,
    private _storyBoxService: StoryBoxService,
    private _dilemmaService: DilemmaService,
    private _optionService: OptionService,
    private _markerService: MarkerService,
    private _answerDilemmaBoxService: AnswerDilemmaBoxService,
    private _dilemmaBoxService: DilemmaBoxService,
    private _roadBlockService: RoadBlockService,
    private _labelColorService: LabelColorService,
    private _diceColorService: DiceColorService,
    private _levelHelperService: LevelHelperService,
    private _roadblockEvaluationService: DialogueTreeRoadblockEvaluationService,
    private _selectionStateService: DialogueTreeSelectionStateService,
    private _labelManagementService: DialogueTreeLabelManagementService,
    private _dialogueTreeUtility: DialogueTreeUtilityService
  ) {}

  
  public ngOnInit(): void
  {
    // Initialize the component by setting up data models, special leaf types, and dialogue elements
    this.initializeDataModels();
    this.initializeSpecialLeafTypes();
    this.initializeDialogueElements();
    this.setupDebuggingHelpers();
  }

  /**
   * Initialize all data models by looking up the objectId in each service
   */
  private initializeDataModels(): void {
    this.storyBox = this._storyBoxService.svcFindById(this.objectId);
    this.optionBox = this._optionBoxService.svcFindById(this.objectId);
    this.option = this._optionService.svcFindById(this.objectId);
    this.dilemmaBox = this._dilemmaBoxService.svcFindById(this.objectId);
    this.dilemma = this._dilemmaService.svcFindById(this.objectId);
  }

  /**
   * Handle special leaf types and dice negative outcome detection
   */
  private initializeSpecialLeafTypes(): void {
    // Check if this StoryBox is a dice negative outcome
    if (this.storyBox && !this.option && !this.optionBox && !this.dilemmaBox && !this.dilemma) {
      this.checkIfDiceNegativeOutcome();
    }

    if(this.leafType == 'start') {
      this.text = "Start";
    }
    else if(this.leafType == 'end') {
      this.text = "End";
    }
  }

  /**
   * Initialize dialogue elements
   */
  private initializeDialogueElements(): void {
    if (this.storyBox && !this.isDiceNegativeOutcome) //Handle regular StoryBox leaf (not dice negative outcome)
    {
      // Use safe helper methods for property setup
      this.setBasicTextProperties(this.leafType, this.storyBox.id);
      this.setupRoadblockProperties(this.storyBox.id);


      // First pass: check for RESTART_DIALOGUE (takes precedence)
      let hasRestartDialogue = false;
      this.storyBox.storyProgressIds.forEach(storyProgressId =>
      {
        let progressMarker = this._markerService.svcFindById(storyProgressId);
        if(progressMarker && progressMarker.type == MarkerType.RESTART_DIALOGUE)
        {
          hasRestartDialogue = true;
          this.restartDialogue = true;
          this.isRestartDialogue.emit(true);
        }
      });

      // Second pass: only check for FINISH_DIALOGUE if no RESTART_DIALOGUE found
      if (!hasRestartDialogue) {
        this.storyBox.storyProgressIds.forEach(storyProgressId =>
        {
          let progressMarker = this._markerService.svcFindById(storyProgressId);
          if(progressMarker && progressMarker.type == MarkerType.FINISH_DIALOGUE)
          {
            this.deadEnd = true;
            this.isDeadEnd.emit(true);
          }
        });
      }
    }
    else if (this.optionBox) //Handle OptionBox parent leaf
    {
      // Use safe helper methods for property setup
      this.setBasicTextProperties(this.leafType, this.optionBox.id);
      this.setupRoadblockProperties(this.optionBox.id);
    }
    else if (this.option) //Handle OptionBox's children leaves
    {
      // Use safe helper methods for property setup
      this.setBasicTextProperties(this.leafType, this.option.id);
      this.setupRoadblockProperties(this.option.answerBoxId);

      let answerBox = this._storyBoxService.svcFindById(this.option.answerBoxId);

      // First pass: check for RESTART_DIALOGUE (takes precedence)
      let hasRestartDialogue = false;
      answerBox.storyProgressIds.forEach(storyProgressId =>
      {
        let progressMarker = this._markerService.svcFindById(storyProgressId);
        if(progressMarker && progressMarker.type == MarkerType.RESTART_DIALOGUE)
        {
          hasRestartDialogue = true;
          this.restartDialogue = true;
          this.isRestartDialogue.emit(true);

        }
      });

      // Second pass: only check for FINISH_DIALOGUE if no RESTART_DIALOGUE found
      if (!hasRestartDialogue) {
        answerBox.storyProgressIds.forEach(storyProgressId =>
        {
          let progressMarker = this._markerService.svcFindById(storyProgressId);
          if(progressMarker && progressMarker.type == MarkerType.FINISH_DIALOGUE)
          {
            this.deadEnd = true;
            this.isDeadEnd.emit(true);
          }
        });
      }
    }
    else if (this.dilemmaBox) //Handle DilemmaBox parent leaf
    {
      // Use safe helper methods for property setup
      this.setBasicTextProperties(this.leafType, this.dilemmaBox.id);
      this.setupRoadblockProperties(this.dilemmaBox.id);
    }
    else if (this.dilemma) //Handle DilemmaBox's children leaves
    {
      // Use safe helper methods for property setup
      this.setBasicTextProperties(this.leafType, this.dilemma.id);
      this.setupRoadblockProperties(this.dilemma.idDilemmaBox);


      let answerDilemmaBox = this._answerDilemmaBoxService.svcFindById(this.dilemma.idDilemmaBox);

      // First pass: check for RESTART_DIALOGUE (takes precedence)
      let hasRestartDialogue = false;
      answerDilemmaBox.storyProgressIds.forEach(storyProgressId =>
      {
        let progressMarker = this._markerService.svcFindById(storyProgressId);
        if(progressMarker && progressMarker.type == MarkerType.RESTART_DIALOGUE)
        {
          hasRestartDialogue = true;
          this.restartDialogue = true;
          this.isRestartDialogue.emit(true);
        }
      });

      // Second pass: only check for FINISH_DIALOGUE if no RESTART_DIALOGUE found
      if (!hasRestartDialogue) {
        answerDilemmaBox.storyProgressIds.forEach(storyProgressId =>
        {
          let progressMarker = this._markerService.svcFindById(storyProgressId);
          if(progressMarker && progressMarker.type == MarkerType.FINISH_DIALOGUE)
          {
            this.deadEnd = true;
            this.isDeadEnd.emit(true);
          }
        });
      }
    }
  }

  /**
   * Set basic text and subText properties
   */
  private setBasicTextProperties(text: string, subText: string): void {
    this.text = text;
    this.subText = subText;
  }

  /**
   * Setup roadblock properties for a given box ID
   */
  private setupRoadblockProperties(boxId: string): void {
    this.roadBlocks = this._roadBlockService.filterStoryboxForAllRoadblocks(boxId);
    this.hasRoadBlock = this._dialogueTreeUtility.hasRoadblocks(boxId);
  }

  /**
   * Get label from any dialogue box type using unified logic
   */
  private getDialogueBoxLabel(): string {
    // Direct box labels (StoryBox, OptionBox, DilemmaBox)
    if (this.storyBox?.label) return this.storyBox.label;
    if (this.optionBox?.label) return this.optionBox.label;
    if (this.dilemmaBox?.label) return this.dilemmaBox.label;

    // Answer box labels (Option -> StoryBox, Dilemma -> AnswerDilemmaBox)
    if (this.option?.answerBoxId) {
      const answerBox = this._storyBoxService.svcFindById(this.option.answerBoxId);
      if (answerBox?.labelOption) return answerBox.labelOption;
    }

    if (this.dilemma?.idDilemmaBox) {
      const answerDilemmaBox = this._answerDilemmaBoxService.svcFindById(this.dilemma.idDilemmaBox);
      if (answerDilemmaBox?.label) return answerDilemmaBox.label;
    }

    return '';
  }

  /**
   * Get AND/OR condition from any dialogue box type using unified logic
   */
  private getDialogueBoxAndOrCondition(): string {
    // Check all box types and return their AndOrCondition with 'OR' fallback
    return this.storyBox?.AndOrCondition ||
           this.optionBox?.AndOrCondition ||
           this.option?.AndOrCondition ||
           this.dilemmaBox?.AndOrCondition ||
           this.dilemma?.AndOrCondition ||
           'OR'; // Default fallback
  }

  /**
   * Setup debugging helpers
   */
  private setupDebuggingHelpers(): void {
    // Temporary debugging: expose clearColors method globally
    (window as any).clearLabelColors = () => {
      this._labelColorService.clearAllColors();
      console.log('🎨 Label colors cleared! Refresh the page to see new colors.');
    };
  }

  /**
   * Check if the current leaf is a dialogue option type (including dice failures)
   */
  isDialogueOption(): boolean {
    return this._dialogueTreeUtility.isDialogueOption(this.leafType);
  }

  /**
   * Check if this element should be hidden (combines all hiding logic)
   */
  shouldHideElement(): boolean {
    const shouldHide = this.shouldHideOption() || this.shouldHideBasedOnRoadblocks();



    return shouldHide;
  }

  /**
   * Check if we should show the finish dialogue separator
   * Shows for End boxes created by FINISH_DIALOGUE that are:
   * - NOT dialogue options (current or parent)
   * - Created by FINISH_DIALOGUE marker
   * - NOT dead/unreachable content
   * - NOT hidden due to roadblock evaluation
   */
  shouldShowFinishDialogueSeparator(): boolean {
    // Only show for End boxes created by FINISH_DIALOGUE
    if (this.leafType !== 'end' || !this.isFinishDialogueEnd) {
      return false;
    }

    // Don't show for dialogue options (current or parent)
    if (this.isDialogueOption() || this.parentIsDialogueOption) {
      return false;
    }

    // Don't show if it's unreachable content
    if (this.deadLeaf) {
      return false;
    }

    // Don't show if element is hidden due to roadblock evaluation
    if (this.shouldHideElement()) {
      return false;
    }

    return true;
  }

  /**
   * Get the CSS class for roadblock positioning based on matching status
   * - Only unmatched SPOKE_IN roadblocks: RIGHT side
   * - Mixed (matched + unmatched SPOKE_IN): LEFT side
   * - Only matched SPOKE_IN roadblocks: LEFT side
   * - Non-SPOKE_IN roadblocks: Always LEFT side
   */
  getRoadblockSideClass(): string {
    const hasMatchedRoadblocks = this.hasAnyMatchedRoadblocks();
    const hasUnmatchedRoadblocks = this.hasAnyUnmatchedRoadblocks();

    // If only unmatched SPOKE_IN roadblocks (no matched ones), position on the right
    // Non-SPOKE_IN roadblocks don't affect this logic and always stay left
    if (hasUnmatchedRoadblocks && !hasMatchedRoadblocks) {
      if (this.isOptionType()) {
        return 'roadblock-option-right';
      } else {
        return 'roadblock-right';
      }
    }

    // All other cases (only matched, mixed, or non-SPOKE_IN) stay on the left
    if (this.isOptionType()) {
      return 'roadblock-option-left';
    } else {
      return 'roadblock-left';
    }
  }

  /**
   * Determine if the current leaf is an Option type (ChoiceOption, InvestigationOption, DilemmaOption, or dice failures)
   */
  isOptionType(): boolean {
    return this._dialogueTreeUtility.isOptionType(this.leafType);
  }

  /**
   * Get the CSS class for horizontal branch styling based on positioning
   * Returns Option-specific classes for Option types, generic classes for others
   * Handles both left and right positioning with appropriate arrow directions
   */
  getHorizontalBranchClass(): string {
    const hasMatchedRoadblocks = this.hasAnyMatchedRoadblocks();
    const hasUnmatchedRoadblocks = this.hasAnyUnmatchedRoadblocks();

    // Determine if roadblocks are positioned on the right (only unmatched)
    const isRightPositioned = hasUnmatchedRoadblocks && !hasMatchedRoadblocks;

    if (this.isOptionType()) {
      // Use Option-specific horizontal branch class
      if (isRightPositioned) {
        return 'roadblock-horizontal-branch-option arrow-right-option';
      } else {
        return 'roadblock-horizontal-branch-option arrow-left-option';
      }
    } else {
      // Use generic horizontal branch class for regular roadblocks
      if (isRightPositioned) {
        return 'roadblock-horizontal-branch arrow-right';
      } else {
        return 'roadblock-horizontal-branch arrow-left';
      }
    }
  }

  /**
   * Check if the current dialogue box has a progress condition label
   */
  hasProgressLabel(): boolean {
    return this.getDialogueBoxLabel() !== '';
  }

  /**
   * Get the current dialogue box label
   */
  getCurrentLabel(): string {
    return this.getDialogueBoxLabel();
  }

  /**
   * Get the current dialogue ID from the router
   */
  getCurrentDialogueId(): string {
    return this._labelManagementService.getCurrentDialogueId();
  }

  /**
   * Check if the current label has a matching roadblock in the dialogue
   */
  hasRoadblockMatchInDialogue(): boolean {
    const currentLabel = this.getCurrentLabel();
    return this._labelManagementService.hasRoadblockMatchInDialogue(currentLabel);
  }

  /**
   * Get the color for the progress condition label with matching logic
   */
  getProgressLabelColor(): string {
    const currentLabel = this.getCurrentLabel();
    return this._labelManagementService.getProgressLabelColor(currentLabel);
  }

  /**
   * Get the icon color for the key (black for unmatched, white for matched)
   */
  getKeyIconColor(): string {
    const currentLabel = this.getCurrentLabel();
    return this._labelManagementService.getKeyIconColor(currentLabel);
  }

  /**
   * Get the hover text for the progress label key icon
   * Returns the actual label name for interior labels, and adds "Used in" info for exterior labels
   */
  getProgressLabelHoverText(): string {
    const currentLabel = this.getCurrentLabel();
    return this._labelManagementService.getProgressLabelHoverText(currentLabel);
  }

  /**
   * Check if any roadblocks in this dialogue box have matches in the current dialogue
   */
  hasAnyMatchedRoadblocks(): boolean {
    return this._roadblockEvaluationService.hasAnyMatchedRoadblocks(this.roadBlocks);
  }

  /**
   * Check if any SPOKE_IN roadblocks in this dialogue box have no matches in the current dialogue
   * Non-spoke roadblocks are ignored for positioning logic
   */
  hasAnyUnmatchedRoadblocks(): boolean {
    return this._roadblockEvaluationService.hasAnyUnmatchedRoadblocks(this.roadBlocks);
  }

  /**
   * Helper method to check if a label has a match in the current dialogue
   */
  private checkLabelMatchInDialogue(label: string): boolean {
    if (!label) return false;

    const currentDialogueId = this.getCurrentDialogueId();
    if (!currentDialogueId) return false;

    const dialoguePrefix = currentDialogueId.split('#')[0];
    const dialogueLabels = this._levelHelperService.models
      .filter((sp: any) => sp.elementId && sp.elementId.startsWith(dialoguePrefix + '.'))
      .map((sp: any) => this._labelColorService.extractLabelFromSpokeText(sp.text))
      .filter((extractedLabel: string) => extractedLabel && extractedLabel.trim() !== '');

    return dialogueLabels.includes(label);
  }

  /**
   * Get the And/Or condition from the parent dialogue box
   * Now uses unified helper method to eliminate complex if/else chain
   */
  getAndOrCondition(): string {
    return this.getDialogueBoxAndOrCondition();
  }

  /**
   * Check if this StoryBox is a dice negative outcome by searching for any Option
   * that has this StoryBox as its answerBoxNegativeId
   */
  private checkIfDiceNegativeOutcome(): void {
    if (!this.storyBox) return;

    // Search through all options to find one that has this StoryBox as negative outcome
    const allOptions = this._optionService.models;
    this.parentOption = allOptions.find(option => option.answerBoxNegativeId === this.storyBox.id);

    if (this.parentOption) {
      this.isDiceNegativeOutcome = true;
      // Override the display to show this as a dice negative option instead of a regular StoryBox
      this.handleDiceNegativeOutcome();
    }
  }

  /**
   * Handle display logic for dice negative outcome StoryBoxes
   */
  private handleDiceNegativeOutcome(): void {
    if (!this.parentOption || !this.storyBox) return;

    // Override the leafType to show as dice failure instead of StoryBox
    this.leafType = this.getDiceNegativeDisplayType();
    this.text = this.leafType;
    this.subText = this.parentOption.id; // Show the parent option ID for reference

    // Get roadblocks for this dice negative outcome StoryBox - use the same logic as regular options
    this.roadBlocks = this._roadBlockService.filterStoryboxForAllRoadblocks(this.storyBox.id);
    this.hasRoadBlock = this._dialogueTreeUtility.hasRoadblocks(this.storyBox.id);

    // Check for dialogue markers (FINISH_DIALOGUE, RESTART_DIALOGUE)
    this.checkDialogueMarkers();
  }

  /**
   * Get the display type for dice negative outcomes based on parent option type
   */
  private getDiceNegativeDisplayType(): string {
    if (!this.parentOption) return 'DiceFailure';

    // Determine the parent option box type to show appropriate failure type
    const parentOptionBoxId = this.parentOption.id.substring(0, this.parentOption.id.lastIndexOf('.'));
    const parentOptionBox = this._optionBoxService.svcFindById(parentOptionBoxId);

    if (parentOptionBox) {
      switch (parentOptionBox.type) {
        case 0: // Choice
          return 'ChoiceFailure';
        case 1: // Investigation
          return 'InvestigationFailure';
        default:
          return 'DiceFailure';
      }
    }

    return 'DiceFailure';
  }

  /**
   * Check dialogue markers for dice negative outcomes
   */
  private checkDialogueMarkers(): void {
    if (!this.storyBox) return;

    // First pass: check for RESTART_DIALOGUE (takes precedence)
    let hasRestartDialogue = false;
    this.storyBox.storyProgressIds.forEach(storyProgressId => {
      let progressMarker = this._markerService.svcFindById(storyProgressId);
      if (progressMarker && progressMarker.type == MarkerType.RESTART_DIALOGUE) {
        hasRestartDialogue = true;
        this.restartDialogue = true;
        this.isRestartDialogue.emit(true);
      }
    });

    // Second pass: only check for FINISH_DIALOGUE if no RESTART_DIALOGUE found
    if (!hasRestartDialogue) {
      this.storyBox.storyProgressIds.forEach(storyProgressId => {
        let progressMarker = this._markerService.svcFindById(storyProgressId);
        if (progressMarker && progressMarker.type == MarkerType.FINISH_DIALOGUE) {
          this.deadEnd = true;
          this.isDeadEnd.emit(true);
        }
      });
    }
  }

  // ===== DICE SYSTEM METHODS =====

  /**
   * Check if this element should show a dice icon
   * Returns true for regular options with dice system OR dice failure variants
   */
  shouldShowDiceIcon(): boolean {
    // Regular option with dice system
    if (this.option && this.option.answerBoxNegativeId) {
      return true;
    }

    // Dice failure variant
    if (this.isDiceNegativeOutcome && this.parentOption) {
      return true;
    }

    return false;
  }

  /**
   * Get the dice color for this element
   * Both success and failure variants get the same color based on the option ID
   */
  getDiceColor(): string {
    // Regular option with dice system
    if (this.option && this.option.answerBoxNegativeId) {
      return this._diceColorService.getColorForDicePair(this.option.id);
    }

    // Dice failure variant
    if (this.isDiceNegativeOutcome && this.parentOption) {
      return this._diceColorService.getColorForDiceFailure(this.storyBox.id, this.parentOption.id);
    }

    return '#CCCCCC'; // Default gray
  }

  /**
   * Get the dice icon color (always white)
   */
  getDiceIconColor(): string {
    return '#FFFFFF'; // Always white for dice icons
  }

  // Interactive functionality methods

  /**
   * Handle click on a choice option
   * If the option is already selected, unselect it. Otherwise, select it.
   */
  public onChoiceOptionClick(): void {
    if (this.isChoiceOption() && this.option) {
      const parentOptionBoxId = this.getParentOptionBoxId();
      if (parentOptionBoxId) {
        this._selectionStateService.handleChoiceOptionClick(
          this.option.id,
          parentOptionBoxId,
          this.selectedChoiceOptions,
          (data) => this.choiceOptionSelected.emit(data)
        );
      }
    }
  }

  /**
   * Handle click on a dice failure (behavior depends on parent box type)
   * - In Choice boxes: behaves like choice options (single selection)
   * - In Investigation boxes: behaves like investigation options (multiple selection)
   */
  public onDiceFailureClick(): void {
    if (this.isDiceFailure() && this.storyBox && this.parentOption) {
      // For dice failures, use the parent option's box as the parent
      const parentOptionBoxId = this._selectionStateService.getParentOptionBoxIdFromOption(this.parentOption);
      if (parentOptionBoxId) {
        // Determine parent box type to use appropriate selection logic
        const parentOptionBox = this._optionBoxService.svcFindById(parentOptionBoxId);
        if (parentOptionBox) {
          if (parentOptionBox.type === 1) {
            // Investigation box: use investigation selection logic (multiple selection)
            this._selectionStateService.handleInvestigationOptionClick(
              this.storyBox.id, // Use the dice failure StoryBox ID as the "option" ID
              parentOptionBoxId,
              this.selectedInvestigationOptions,
              (data) => this.investigationOptionSelected.emit(data)
            );
          } else {
            // Choice box (type 0) or other: use choice selection logic (single selection)
            this._selectionStateService.handleChoiceOptionClick(
              this.storyBox.id, // Use the dice failure StoryBox ID as the "option" ID
              parentOptionBoxId,
              this.selectedChoiceOptions,
              (data) => this.choiceOptionSelected.emit(data)
            );
          }
        }
      }
    }
  }

  /**
   * Handle click on an investigation option
   * Investigation options allow multiple selections, so toggle the selection state
   */
  public onInvestigationOptionClick(): void {
    if (this.isInvestigationOption() && this.option) {
      const parentOptionBoxId = this.getParentOptionBoxId();
      if (parentOptionBoxId) {
        this._selectionStateService.handleInvestigationOptionClick(
          this.option.id,
          parentOptionBoxId,
          this.selectedInvestigationOptions,
          (data) => this.investigationOptionSelected.emit(data)
        );
      }
    }
  }

  /**
   * Handle click on a dilemma option
   * If the option is already selected, unselect it. Otherwise, select it.
   */
  public onDilemmaOptionClick(): void {
    if (this.isDilemmaOption() && this.dilemma) {
      const parentDilemmaBoxId = this.dilemma.idDilemmaBox;
      if (parentDilemmaBoxId) {
        this._selectionStateService.handleDilemmaOptionClick(
          this.dilemma.id,
          parentDilemmaBoxId,
          this.selectedDilemmaOptions,
          (data) => this.dilemmaOptionSelected.emit(data)
        );
      }
    }
  }

  /**
   * Check if this leaf represents a choice option that can be clicked
   */
  public isChoiceOption(): boolean {
    return this._selectionStateService.isChoiceOption(this.leafType);
  }

  /**
   * Check if this leaf represents a dice failure that can be clicked (treated like choice options)
   */
  public isDiceFailure(): boolean {
    return this.leafType === 'ChoiceFailure' ||
           this.leafType === 'InvestigationFailure' ||
           this.leafType === 'DiceFailure';
  }

  /**
   * Check if this leaf represents an investigation option that can be clicked
   */
  public isInvestigationOption(): boolean {
    return this._selectionStateService.isInvestigationOption(this.leafType);
  }

  /**
   * Check if this leaf represents a dilemma option that can be clicked
   */
  public isDilemmaOption(): boolean {
    return this._selectionStateService.isDilemmaOption(this.leafType);
  }

  /**
   * Check if this option is currently selected (for choice options and dice failures)
   */
  public isSelected(): boolean {
    // Regular choice options
    if (this.isChoiceOption() && this.option) {
      const parentOptionBoxId = this.getParentOptionBoxId();
      return this._selectionStateService.isOptionSelected(this.option.id, parentOptionBoxId, this.selectedChoiceOptions);
    }

    // Dice failures (behavior depends on parent box type)
    if (this.isDiceFailure() && this.storyBox && this.parentOption) {
      const parentOptionBoxId = this._selectionStateService.getParentOptionBoxIdFromOption(this.parentOption);
      if (parentOptionBoxId) {
        // Determine parent box type to check appropriate selection map
        const parentOptionBox = this._optionBoxService.svcFindById(parentOptionBoxId);
        if (parentOptionBox) {
          if (parentOptionBox.type === 1) {
            // Investigation box: check investigation selections
            return this._selectionStateService.isInvestigationOptionSelected(this.storyBox.id, parentOptionBoxId, this.selectedInvestigationOptions);
          } else {
            // Choice box (type 0) or other: check choice selections
            return this._selectionStateService.isOptionSelected(this.storyBox.id, parentOptionBoxId, this.selectedChoiceOptions);
          }
        }
      }
    }

    return false;
  }

  /**
   * Check if this investigation option is currently selected
   */
  public isInvestigationSelected(): boolean {
    if (!this.isInvestigationOption() || !this.option) return false;

    const parentOptionBoxId = this.getParentOptionBoxId();
    return this._selectionStateService.isInvestigationOptionSelected(this.option.id, parentOptionBoxId, this.selectedInvestigationOptions);
  }

  /**
   * Check if this dilemma option is currently selected
   */
  public isDilemmaSelected(): boolean {
    if (!this.isDilemmaOption() || !this.dilemma) return false;

    return this._selectionStateService.isDilemmaOptionSelected(this.dilemma.id, this.selectedDilemmaOptions);
  }

  /**
   * Check if any option is selected for this option's parent box (includes dice failures)
   */
  public hasParentSelection(): boolean {
    // Regular choice options
    if (this.isChoiceOption() && this.option) {
      const parentOptionBoxId = this.getParentOptionBoxId();
      return this._selectionStateService.hasChoiceParentSelection(parentOptionBoxId, this.selectedChoiceOptions);
    }

    // Dice failures (treated like choice options)
    if (this.isDiceFailure() && this.storyBox && this.parentOption) {
      const parentOptionBoxId = this._selectionStateService.getParentOptionBoxIdFromOption(this.parentOption);
      return this._selectionStateService.hasChoiceParentSelection(parentOptionBoxId, this.selectedChoiceOptions);
    }

    return false;
  }

  /**
   * Check if any dilemma is selected in the same dilemma group as this dilemma
   * Since each dilemma has its own dilemma box, we group them by their common prefix
   */
  public hasDilemmaParentSelection(): boolean {
    if (!this.isDilemmaOption() || !this.dilemma) return false;

    return this._selectionStateService.hasDilemmaParentSelection(this.dilemma.id, this.selectedDilemmaOptions);
  }

  /**
   * Check if this option should be hidden based on selections (includes dice failures)
   */
  public shouldHideOption(): boolean {
    // Handle dice failures separately
    if (this.isDiceFailure() && this.storyBox && this.parentOption) {
      const parentOptionBoxId = this._selectionStateService.getParentOptionBoxIdFromOption(this.parentOption);

      // Use the same single-selection logic as choice options
      if (!this._selectionStateService.hasChoiceParentSelection(parentOptionBoxId, this.selectedChoiceOptions)) {
        return false; // No selection made, show all options
      }

      // If this dice failure is selected, never hide it
      if (this._selectionStateService.isOptionSelected(this.storyBox.id, parentOptionBoxId, this.selectedChoiceOptions)) {
        return false;
      }

      // If another option is selected in the same box, hide this dice failure
      return true;
    }

    // Regular options (choice, investigation, dilemma)
    const parentOptionBoxId = this.getParentOptionBoxId();
    const parentDilemmaBoxId = this.getParentDilemmaBoxId();

    return this._selectionStateService.shouldHideOptionBasedOnSelection(
      this.leafType,
      this.option?.id,
      this.dilemma?.id,
      parentOptionBoxId || parentDilemmaBoxId,
      this.selectedChoiceOptions,
      this.selectedDilemmaOptions
    );
  }

  /**
   * Check if this element should be hidden based on roadblock dependencies
   * This evaluates whether roadblocks are satisfied by current choice selections
   * AND checks if parent elements are hidden (cascading logic)
   */
  public shouldHideBasedOnRoadblocks(): boolean {
    const andOrCondition = this.getAndOrCondition();

    return this._roadblockEvaluationService.shouldHideBasedOnRoadblocks(
      this.roadblockEvaluationEnabled,
      this.hasRoadBlock,
      this.roadBlocks,
      andOrCondition,
      this.selectedChoiceOptions,
      this.selectedInvestigationOptions,
      this.selectedDilemmaOptions,
      () => this.shouldHideBasedOnParent()
    );
  }

  /**
   * Get the parent option box ID for this option
   */
  private getParentOptionBoxId(): string | undefined {
    if (!this.option) return undefined;
    return this._selectionStateService.getParentOptionBoxId(this.option.id);
  }

  /**
   * Get the parent dilemma box ID for this dilemma option
   */
  private getParentDilemmaBoxId(): string | undefined {
    if (!this.dilemma) return undefined;
    return this._selectionStateService.getParentDilemmaBoxId(this.dilemma.id);
  }

  /**
   * Check if a specific roadblock is satisfied by current selections
   */
  private isRoadblockSatisfied(roadblock: RoadBlock): boolean {
    // Only evaluate "Spoke In" roadblocks that have matches in current dialogue
    if (+roadblock.Type !== +RoadBlockType.SPOKE_IN || !roadblock.spokeElementId) {
      return true; // Non-spoke roadblocks are always considered satisfied for now
    }

    // Get the spoke element referenced by this roadblock
    const spokeElement = this._levelHelperService.models.find((sp: any) =>
      sp.elementId === roadblock.spokeElementId
    );

    if (!spokeElement?.text) {
      return true; // If spoke element not found, consider satisfied
    }

    // Extract the label from the spoke element text
    const referencedLabel = this._labelColorService.extractLabelFromSpokeText(spokeElement.text);
    if (!referencedLabel) {
      return true; // If no label found, consider satisfied
    }

    // Check if this label exists in current dialogue
    if (!this.checkLabelMatchInDialogue(referencedLabel)) {
      return true; // If label not in current dialogue, don't evaluate (leave visible)
    }

    // Check if any selected choice option OR visible story box has this label
    const isSatisfied = this.isLabelSatisfiedBySelections(referencedLabel);
    return isSatisfied;
  }

  /**
   * Check if a label is satisfied by current choice selections, investigation selections, OR visible standalone story boxes
   */
  private isLabelSatisfiedBySelections(targetLabel: string): boolean {
    // Check selected choice options first
    if (this.selectedChoiceOptions && this.selectedChoiceOptions.size > 0) {
      // Get all selected choice options and check their labels
      for (const [, selectedOptionId] of this.selectedChoiceOptions.entries()) {
        const selectedOption = this._optionService.svcFindById(selectedOptionId);
        if (!selectedOption) continue;

        // Check if this option has the target label
        // Labels can be on the option itself or on its answer box
        if (this._labelManagementService.optionHasLabel(selectedOption, targetLabel)) {
          return true;
        }
      }
    }

    // Check selected investigation options
    if (this.selectedInvestigationOptions && this.selectedInvestigationOptions.size > 0) {
      // Get all selected investigation options and check their labels
      for (const [, selectedOptionIds] of this.selectedInvestigationOptions.entries()) {
        for (const selectedOptionId of selectedOptionIds) {
          const selectedOption = this._optionService.svcFindById(selectedOptionId);
          if (!selectedOption) continue;

          // Check if this option has the target label
          // Labels can be on the option itself or on its answer box
          if (this._labelManagementService.optionHasLabel(selectedOption, targetLabel)) {
            return true;
          }
        }
      }
    }

    // Check selected dilemma options
    if (this.selectedDilemmaOptions && this.selectedDilemmaOptions.size > 0) {
      // Get all selected dilemma options and check their labels
      for (const [, selectedDilemmaId] of this.selectedDilemmaOptions.entries()) {
        const selectedDilemma = this._dilemmaService.svcFindById(selectedDilemmaId);
        if (!selectedDilemma) continue;

        // Check if this dilemma has the target label
        // Labels can be on the dilemma itself or on its answer box
        if (this._labelManagementService.dilemmaHasLabel(selectedDilemma, targetLabel)) {
          return true;
        }
      }
    }

    // Also check visible standalone story boxes with progress condition labels
    // These are always reachable if they don't have unsatisfied roadblocks
    if (this.checkStandaloneStoryBoxesForLabel(targetLabel)) {
      return true;
    }

    return false;
  }

  /**
   * Check if any standalone story boxes in the current dialogue have the target label
   * This excludes answer boxes (which contain "opt" in their ID) and only checks
   * actual standalone story boxes with progress condition labels
   */
  private checkStandaloneStoryBoxesForLabel(targetLabel: string): boolean {
    const currentDialogueId = this.getCurrentDialogueId();
    if (!currentDialogueId) {
      return false;
    }

    // Extract dialogue prefix for filtering (e.g., "A8.L1794.D0_PT-BR")
    const dialoguePrefix = currentDialogueId.split('#')[0];

    // Get all story boxes in the current dialogue, but exclude answer boxes
    const standaloneStoryBoxes = this._storyBoxService.models.filter(storyBox =>
      storyBox.id &&
      storyBox.id.startsWith(dialoguePrefix + '.') &&
      !storyBox.id.includes('.opt') // Exclude answer boxes (they contain ".opt" in their ID)
    );

    for (const storyBox of standaloneStoryBoxes) {
      // Check if this story box has the target label
      if (storyBox.label && storyBox.label.trim() === targetLabel.trim()) {
        // Check if this story box is actually visible in the current dialogue state
        if (this.isStoryBoxActuallyVisible(storyBox)) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * Check if a story box is actually visible in the current dialogue state
   * Uses the same logic as the dialogue tree rendering to determine visibility
   */
  private isStoryBoxActuallyVisible(storyBox: any): boolean {
    // If roadblock evaluation is disabled, all story boxes are visible
    if (!this.roadblockEvaluationEnabled) {
      return true;
    }

    // Use the existing roadblock evaluation logic that's used by the dialogue tree
    // This is the same logic used in shouldHideElement() but specifically for story boxes
    if (!this._dialogueTreeUtility.hasRoadblocks(storyBox.id)) {
      return true; // No roadblocks, always visible
    }

    const storyBoxRoadblocks = this._roadBlockService.filterStoryboxForAllRoadblocks(storyBox.id);

    // Get the AND/OR condition for this story box
    const andOrCondition = storyBox.AndOrCondition || 'OR';

    // Evaluate roadblocks using the same logic as the main roadblock evaluation
    const roadblockResults = storyBoxRoadblocks.map(roadblock => this.isRoadblockSatisfied(roadblock));

    if (andOrCondition === 'AND') {
      // ALL roadblocks must be satisfied
      return roadblockResults.every(result => result);
    } else {
      // OR condition: AT LEAST ONE roadblock must be satisfied
      return roadblockResults.some(result => result);
    }
  }
  
  /**
   * Check if this element should be hidden because its parent is hidden
   * This implements cascading roadblock logic for all option types:
   * - Choice options cascade from choice boxes
   * - Investigation options cascade from investigation boxes
   * - Dilemma options cascade from dilemma boxes
   * - Dice failure outcomes cascade from their parent option's box
   */
  private shouldHideBasedOnParent(): boolean {
    // Apply cascading logic to all option types and their children
    if (this.isChildOfOptionBox() || this.isDiceFailureOutcome()) {
      const parentHidden = this.isParentBoxHidden();
      if (parentHidden) {
      }
      return parentHidden;
    }

    // For other elements, no cascading logic needed
    return false;
  }

  /**
   * Check if this element is a child of any option box (choice, investigation, dilemma, or dice failure)
   */
  private isChildOfOptionBox(): boolean {
    return this._dialogueTreeUtility.isChildOfOptionBox(this.leafType) || this.isDiceFailure();
  }

  /**
   * Check if this element is a dice failure outcome (child of choice option)
   */
  private isDiceFailureOutcome(): boolean {
    return this.isDiceNegativeOutcome && this.leafType === 'StoryBox';
  }

  /**
   * Check if the parent box (choice, investigation, dilemma, or dice failure) should be hidden based on roadblocks
   * This method creates a virtual parent leaf to check if it would be hidden using the same logic
   */
  private isParentBoxHidden(): boolean {
    // For choice options, check if their parent choice box should be hidden
    if (this.isChoiceOption() && this.option) {
      const parentBoxId = this.getParentOptionBoxId();
      return this.isParentBoxActuallyHidden(parentBoxId, 'ChoiceBox');
    }

    // For investigation options, check if their parent investigation box should be hidden
    if (this.isInvestigationOption() && this.option) {
      const parentBoxId = this.getParentOptionBoxId();
      return this.isParentBoxActuallyHidden(parentBoxId, 'InvestigationBox');
    }

    // For dilemma options, check if their parent dilemma box should be hidden
    if (this.isDilemmaOption() && this.dilemma) {
      // Extract the parent DilemmaBox ID from the dilemma option ID
      // e.g., "A23.L2044.D0_PT-BR.DIL1.dlm5" -> "A23.L2044.D0_PT-BR.DIL1"
      const parentBoxId = this.getParentDilemmaBoxId();
      const parentHidden = this.isParentBoxActuallyHidden(parentBoxId, 'DilemmaBox');
      return parentHidden;
    }

    // For dice failures, check if their parent option's box should be hidden
    if (this.isDiceFailure() && this.storyBox && this.parentOption) {
      const parentBoxId = this._selectionStateService.getParentOptionBoxIdFromOption(this.parentOption);
      // Determine the parent box type and evaluate accordingly
      const optionBox = this._optionBoxService.svcFindById(parentBoxId);
      if (optionBox) {
        if (optionBox.type === 0) { // Choice box (type 0)
          return this.isParentBoxActuallyHidden(parentBoxId, 'ChoiceBox');
        } else { // Investigation box (type 1)
          return this.isParentBoxActuallyHidden(parentBoxId, 'InvestigationBox');
        }
      }
    }

    // For dice failure outcomes, check if their parent option's box should be hidden
    if (this.isDiceFailureOutcome() && this.storyBox) {
      // Find the parent option that has this story box as negative outcome
      const parentOption = this._optionService.models.find(option =>
        option.answerBoxNegativeId === this.storyBox.id
      );

      if (parentOption) {
        const parentBoxId = this._selectionStateService.getParentOptionBoxIdFromOption(parentOption);
        // Determine the parent box type and evaluate accordingly
        const optionBox = this._optionBoxService.svcFindById(parentBoxId);
        if (optionBox) {
          if (optionBox.type === 0) { // Choice box (type 0)
            return this.isParentBoxActuallyHidden(parentBoxId, 'ChoiceBox');
          } else { // Investigation box (type 1)
            return this.isParentBoxActuallyHidden(parentBoxId, 'InvestigationBox');
          }
        }
      }
    }

    return false;
  }

  /**
   * Check if a parent box is actually hidden by simulating the same shouldHideElement logic
   * that the parent box leaf component would use
   */
  private isParentBoxActuallyHidden(parentBoxId: string, parentBoxType: string): boolean {
    if (!parentBoxId) return false;

    // Check if roadblock evaluation is enabled
    if (!this.roadblockEvaluationEnabled) {
      return false; // Roadblock evaluation disabled
    }

    // Get roadblocks for the parent box
    if (!this._dialogueTreeUtility.hasRoadblocks(parentBoxId)) {
      return false; // No roadblocks on parent box, don't hide
    }

    const roadblocks = this._roadBlockService.filterStoryboxForAllRoadblocks(parentBoxId);

    // Get the parent box to check its AND/OR condition
    let parentBox: any = null;
    let andOrCondition = 'OR';

    if (parentBoxType === 'ChoiceBox' || parentBoxType === 'InvestigationBox') {
      parentBox = this._optionBoxService.svcFindById(parentBoxId);
      andOrCondition = (parentBox as any)?.AndOrCondition || 'OR';
    } else if (parentBoxType === 'DilemmaBox') {
      parentBox = this._dilemmaBoxService.svcFindById(parentBoxId);
      andOrCondition = (parentBox as any)?.AndOrCondition || 'OR';
    }

    if (!parentBox) return false;

    // Use the same inverted logic as the main shouldHideBasedOnRoadblocks method
    let shouldHide: boolean;
    if (andOrCondition === 'AND') {
      // ALL roadblocks must be satisfied - hide if ANY roadblock is not satisfied
      shouldHide = !roadblocks.every(roadblock => this.isRoadblockSatisfied(roadblock));
    } else {
      // OR condition (default): AT LEAST ONE roadblock must be satisfied - hide if NO roadblocks are satisfied
      shouldHide = !roadblocks.some(roadblock => this.isRoadblockSatisfied(roadblock));
    }

    return shouldHide;
  }
}