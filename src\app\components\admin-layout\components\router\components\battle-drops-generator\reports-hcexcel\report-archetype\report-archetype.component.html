            <!--TOTAL UNIQUE CHARACTERES-->
                <div>
                    <div style="width: 100%;">
                        <table class="table table-list">
                            <thead>
                                <tr>
                                    <th class="dark-gray" [attr.colspan]="orderAreaList.length + 2">
                                        <h5>TOTAL ARCHETYPES</h5>
                                    </th>
                                </tr>
                                <tr>
                                    <th class="thBC">ARCHETYPES</th>
                                    <th class="thBC">Total</th>
                                    <th class="thBC" style="width: 70px;"
                                        *ngFor=" let area of orderAreaList; let i = index;">
                                        {{area.order}}
                                    </th>
                                    
                                </tr>
                            </thead>
                            <tbody>
                               <ng-container *ngFor="let totalArch of totalListArchetypes; let e = index;">
                                    <tr>
                                        <td class="other-td" style="width: 8%;"
                                           [ngStyle]="{'background-color': getColorNameArchetype(totalArch.nameArchetype)}" >
                                            {{totalArch.nameArchetype}}
                                        </td>                                     
                                         <td class="other-td" >{{totalArch.total_line}}</td>         
                                            <ng-container *ngFor="let item of totalArch.listCicleLevel; let e = index;">
                                                <td class="other-td" style="width: 30px;">{{item.totalUniqueCharacteres}}</td>
                                            </ng-container>  
                                    </tr>
                                    <tr>
                                        <td class="other-td" style="font-weight: 700; border-top: none;">
                                            {{totalArch.total_name}}
                                        </td>
                                        <ng-container *ngFor="let item of totalArch.total_column; let e = index;">
                                            <td class="other-td" style="width: 30px; border-top: none;">{{item}}</td>
                                        </ng-container>  
                                    </tr>
                                </ng-container>                              
        
                            </tbody>
                        </table>
                        <ng-container *ngIf="totalListArchetypes.length === 0">
                            <p class="noWeapons">Empty Archetypes list.</p>
                        </ng-container>
                    </div>
                </div>
     