<div class="background-div handleOut" aria-hidden="true">
    <div class="modal-backdrop"></div>
    <div id="modal-close" @popup class="popup-report"
        style="background-color: white;">
        <div class="modal-header">
            <div style="display: flex; justify-content: space-between;">
                <h3 style="color:black !important; text-align: center;" class="modal-title">Items submitted for review</h3>
                    <div class="modal-close btn-close" style="margin-top: -10px;">
                        <button (click)="closeViewSendPopup()" class="btn btn-danger btn-fill btn-remove modal-btn-close">
                        <i class="pe-7s-close i-icon"></i>
                    </button>
                </div>
            </div>
        </div>
        <div class="contextInfo">
            <!-- Listagem de itens enviados para revisão -->
            <div class="items-list-container">
                <!-- <PERSON><PERSON><PERSON><PERSON><PERSON> da tabela -->
                <div class="table-header">
                    <div class="header-cell lote-column">Lote</div>
                    <div class="header-cell id-column">Id</div>
                    <div class="header-cell name-column">Field name</div>
                    <div class="header-cell content-column">Content</div>
                </div>

                <!-- Container com scroll para os itens -->
                <div class="items-scroll-container">
                    <!-- Verifica se há itens para mostrar -->
                    <div *ngIf="listViewSend && listViewSend.length > 0; else noItems" class="items-list">
                        <!-- Loop pelos itens enviados -->
                        <div *ngFor="let item of listViewSend; let i = index" class="item-row" [class.even]="i % 2 === 0">
                            <div class="item-cell lote-column">{{ item.lote || 'N/A' }}</div>
                            <div class="item-cell id-column">{{ item.id || 'N/A' }}</div>
                            <div class="item-cell name-column">{{ (item.name || 'N/A') | titleConvention:'firstCapitalLetter' }}</div>
                            <div class="item-cell content-column">
                            <ng-container *ngIf="item.textValue">
                                <div class="content-list">
                                <div *ngFor="let text of item.textValue; let j = index" class="content-item">
                                    {{ text }}
                                </div>
                                </div>
                            </ng-container>
                            <ng-container *ngIf="!item.textValue && item.textValueString">
                                <div class="content-list">
                                <div class="content-textValueString">
                                    {{ item.textValueString }}
                                </div>
                                </div>
                            </ng-container>
                            <ng-container *ngIf="!item.textValue && !item.textValueString">
                                <span class="no-content">No content</span>
                            </ng-container>
                            </div>
                        </div>
                    </div>

                    <!-- Mensagem quando não há itens -->
                    <ng-template #noItems>
                        <div class="no-items">
                            <p>No items have been submitted for review yet.</p>
                        </div>
                    </ng-template>
                </div>
            </div>
        </div>

    </div>
</div>