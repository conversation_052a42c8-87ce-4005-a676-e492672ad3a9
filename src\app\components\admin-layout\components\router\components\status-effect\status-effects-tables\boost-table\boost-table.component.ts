import { Component, EventEmitter, Output } from '@angular/core';
import { BoostTable } from 'src/app/lib/@bus-tier/models';
import { Button } from 'src/app/lib/@pres-tier/data';
import { BoostTableService } from 'src/app/services';
import { Alert } from 'src/lib/darkcloud';

@Component({
  selector: 'app-boost-table',
  templateUrl: './boost-table.component.html',
  styleUrls: ['./boost-table.component.scss']
})
export class BoostTableComponent {

  
titles = ['ID', 'CATEGORY', 'ID AFFLICTION', 'STATUS', 'SKILL RESIST USER',
    'OPERATOR', 'VALUE', 'STATUS EFFECT NAME', 'DESCRIPTION', 'POWER POINTS (PP)', 'ALL', 'DURATION (TURNS)'];
 listBoostTable: BoostTable[] = [];
 activeLanguage = 'PTBR';
 @Output() activeTab2 = new EventEmitter<string>();
 isListBoostEmpty: boolean;

 public readonly excelButtonTemplate: Button.Templateable = {
   title: 'Paste content from excel',
   onClick: this.onExcelPaste.bind(this),
   iconClass: 'excel-icon',
   btnClass: Button.Klasses.FILL_ORANGE,
 };
 constructor(
   private _boostTableService: BoostTableService
 ){}


 async ngOnInit(): Promise<void>{
   
     this.removeEmptyItems();
      this.listBoostTable = this._boostTableService.models;
      this.isListBoostEmpty = this.listBoostTable.length === 0;  

   }

   removeEmptyItems() {
     this._boostTableService.toFinishLoading();
     this._boostTableService.models = this._boostTableService.models.filter(boostItem => boostItem.idBoost !== "");
     this._boostTableService.toSave();
   }

   async onExcelPaste() {
     const text = await navigator.clipboard.readText();
     const lines = text.split(/\r?\n/).filter(line => line);    
     const processedData: string[][] = [];
   
     if (lines.length > 0) {
       lines.forEach(line => {
         // Divide cada linha em colunas e remove a primeira coluna
         const values = line.split("\t").map(value => value.trim()).slice(1);
   
         processedData.push(values);
       });
   
       // Verifica se o número de colunas em cada linha corresponde ao número de colunas em this.titles
       const isColumnCountValid = processedData.every(row => row.length === this.titles.length);
   
       if (!isColumnCountValid) {
         Alert.showError('Invalid number of columns');
         return;
       }
 
       this._boostTableService.models = [];
       this._boostTableService.toSave();
   
       for (let index = 0; index < processedData.length; index++) {
         this._boostTableService.createNewBoostTable(processedData[index]);
       }    

       Alert.ShowSuccess('Boost Table imported successfully!');
       this.activeTab2.emit('boostTable');
       this.ngOnInit();
     }
   }
   

   changeBoost(rowIndex: number, name: string, newValue: string){
 
     if (name === 'idBoost') {
      this.listBoostTable[rowIndex].idBoost = newValue;        
     }
     else if (name === 'category') {
       this.listBoostTable[rowIndex].category = newValue;        
      }
      else if (name === 'idAffliction') {
        this.listBoostTable[rowIndex].idAffliction = newValue;        
       }
      else if (name === 'status') {
       this.listBoostTable[rowIndex].status = newValue;        
      }
      else if (name === 'skillResistUser') {
        this.listBoostTable[rowIndex].skillResistUser = newValue;        
       }
      else if (name === 'operator') {
       this.listBoostTable[rowIndex].operator = newValue;        
      }
      else if (name === 'value') {
       this.listBoostTable[rowIndex].value = newValue;        
      }
      else if (name === 'statusEffectName') {
       this.listBoostTable[rowIndex].statusEffectName = newValue;        
      }
      else if (name === 'description') {
       this.listBoostTable[rowIndex].description = newValue;        
      }
      else if (name === 'powerPoints') {
       this.listBoostTable[rowIndex].powerPoints = newValue;        
      }
      else if (name === 'allBoost') {
       this.listBoostTable[rowIndex].allBoost = newValue;        
      }
      else if (name === 'duration') {
        this.listBoostTable[rowIndex].duration = newValue;        
       }

     this._boostTableService.svcToModify(this.listBoostTable[rowIndex]);
   }    



}
