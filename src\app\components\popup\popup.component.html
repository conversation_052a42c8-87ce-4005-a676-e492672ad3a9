<div class="swal2-container swal2-center swal2-backdrop-show close-on-click">
  <div style="display: contents; flex-direction: row;">
    <ng-container *ngFor="let popupInterface of popupInterfaces; let interfaceIndex = index">
      <div id="popup"
           class="window swal2-modal swal2-show"
           aria-labelledby="swal2-title"
           aria-describedby="swal2-content"
           tabindex="-1"
           role="dialog"
           aria-live="assertive"
           aria-modal="true"
           style="display: flex;  margin:10px; ">
        <div class="swal2-header">
          <div class="modal-close margin-inline-start: auto;">
            <button (click)="closeReferencePopup(interfaceIndex)" class="swal2-close" style="display: flex;">
                <i class="pe-7s-close i-icon"></i>
            </button>
        </div>
          <h2 class="swal2-title">
            {{
            popupInterface.style.title
            }}</h2>
        </div>
        <div class="swal2-content" style="align-content: center;">
          <ng-container *ngIf="popupInterface.style.text !== undefined && popupInterface.style.text !== '' && popupInterface.style.text.length > 70; else elseBlock">
            <div class="swal2-html-container tips"
                style="display: block;">
              {{
                popupInterface.style.text
              }}
            </div>
          </ng-container>
          <ng-template #elseBlock>
            <div class="swal2-html-container"
                 style="display: block; font-size: 14px; 
                      max-width: 850px;">
                {{popupInterface.style.text}}
            </div>
            <br>
            <div class="swal2-html-container"
            style="display: block; font-size: 13px; 
                 max-width: 850px;">
              {{popupInterface.style?.subText}}
            </div>
        </ng-template>
        </div>
        <div *ngIf="refreshBtn && popupInterface.style.text !== undefined && popupInterface.style.text !== '' && popupInterface.style.text.length > 70;" style="margin-left: 94%;">
          <button class="i-btn"
                  style="font-size: 16px;"
                  title="Regenerate response"
                  (click)="_popupService.refreshPopup(popupInterface)">
              <i class="icon pe-7s-refresh-2"></i>
          </button>
        </div>
        <div [ngClass]="'actions-' + popupInterface.style.actionsClass"
             style="overflow:auto; margin:10px; max-height: 80vh; align-self: center;">
          <ng-container *ngFor="let button of popupInterface.buttons; let buttonIndex = index">
            <div style="display: flex;">
              <button [ngStyle]="(button.value | information)?.hex
              ? { 'border-color':     (button.value | information)?.hex,
                  'background-color': (button.value | information)?.hex,
                   color: '#fff' }: {}" id="interfaceIndex"
                      ngClass="btn btn-main {{ 'btn-' + popupInterface.style.buttonSize + ' ' + button.klass + ( btnSelected === button ? ' btn-fill' : '')}}
                {{button === selectedButton[interfaceIndex] ? 'btn-fill btn-primary' : 'btn-fill'}}"
                      (click)="ClickButton(interfaceIndex, buttonIndex);">
                {{ button.text }}
              </button>
              <ng-container *ngIf="button.audioId">
                <div class="btn btn-sm btn-disabled">
                  {{ (((button.audioId | audioFile ) | review)?.assignedAt.length) || 0 }}
                </div>
                <app-audio-listener [klass]="'btn btn-sm'"
                                    [audioId]="button.audioId">
                </app-audio-listener>
              </ng-container>
            </div>
          </ng-container>

          <ng-container *ngIf="popupInterface.buttons.length === 0">
            <div>
              <i class="icon pe-7s-refresh-2 icon-refresh-text"></i>
            </div>           
          </ng-container>

        </div>
      </div>
    </ng-container>
  </div>
</div>