<div class="rpg-text-wrapper form-100"
     (mouseenter)="toggleFormatPopupByHover(true)"
     (mouseleave)="toggleFormatPopupByHover(focused ? true : false)">
     
  <app-rpg-formatting-popup class="rpg-formatting-popup"
                            *ngIf="toggleFormattingPopup || hoveringFormatPopup"
                            (applyformat)="formatText($event); inputBox.innerText = this.content"
                            (mouseenter)="hoveringFormatPopup = true"
                            (mouseleave)="hoveringFormatPopup = false">
  </app-rpg-formatting-popup>

  <app-content-input (focusing)="focused = $event; toggleFormatPopup($event);"
                     (click)="focused = !!$event; toggleFormatPopup(!!$event);"
                     class="rpg-input-holder"
                     [class]="'form-control form-100 rpg-input'"
                     #inputBox
                     (contentChange)="onChangeContent(inputBox.content);"
                     [initialContent]="content || ''"
                     [language]="language"
                     >
   </app-content-input>

  <p class="form-control form-100 rpg-display"
     [innerHTML]="content | rpgFormatting"></p>
</div>
