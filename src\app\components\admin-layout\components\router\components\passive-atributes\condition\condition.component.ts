import { ChangeDetectorRef, Component, EventEmitter, Output } from '@angular/core';
import { Alert } from 'src/lib/darkcloud';
import { ConditionTrigger } from '../../../../../../../lib/@bus-tier/models/ConditionTrigger';
import { ConditionTriggerService } from '../../../../../../../services/conditionTrigger.service';

@Component({
  selector: 'app-condition',
  templateUrl: './condition.component.html',
  styleUrls: ['./condition.component.scss']
})
export class ConditionComponent {

  @Output() pasteExcelData = new EventEmitter<void>();
  @Output() descrptionOutput = new EventEmitter<string>();
  listCondition: ConditionTrigger[] = [];
  excelCondition: any[] = [];

  constructor(
    private _conditionTriggerService: ConditionTriggerService,
    private ref: ChangeDetectorRef
  ){}

  public async ngOnInit() {

    this._conditionTriggerService.toFinishLoading();
    this.listCondition = this._conditionTriggerService.models;
    this.removeEmptyLines();
    this.descrptionOutput.emit(`Showing ${this.listCondition.length} results`); 
    
  }
  removeEmptyLines() {
    this.listCondition = this.listCondition.filter(condition => condition?.idValue && condition?.description);
    this.listCondition.forEach((x) => this._conditionTriggerService.svcToModify(x));
  }

  async onExcelPaste(): Promise<void> {
    const conditionFields: string[] = ['ID', 'DESCRIPTION', 'TYPE', 'OPERATOR', 'VALUE'];
    try {
      const text = await navigator.clipboard.readText(); 
      const lines = text.split(/\r?\n/).filter(line => line);      
      
      // Remove linhas vazias no final
      while (lines.length > 0 && !lines[lines.length - 1].trim()) {
        lines.pop();
      }

      this._conditionTriggerService.toFinishLoading();      
      this.listCondition = this._conditionTriggerService.models = [];
      this._conditionTriggerService.toSave();

      if (lines.length === 0) {
        throw new Error('No data found. Check if you copied the lines correctly.');
      }

      const headerColumns = lines[0].split(/\t/).map(col => col.trim());
      
      
      if (headerColumns.length !== conditionFields.length) {
        throw new Error('Number of incorrect columns. Check Excel format.');
      }    

      this.excelCondition = lines.slice(0).map((line, lineindex) => {
        const cols = line.split(/\t/).map(col => col.trim());

      // Verifica se a primeira coluna (ID) está vazia
       if (!cols[0]) {
         throw new Error(`The ${lineindex + 1} line has the first empty column. Check the data.`);
      }        
        
        return {
          idValue: cols[0],        // 1ª Coluna -> 'idValue'
          description: cols[1],    // 2ª Coluna -> 'description'
          type: cols[2],           // 3ª Coluna -> 'type'
          operator: cols[3],       // 4ª Coluna -> 'operator'
          value: cols[4]           // 5ª Coluna -> 'value'
        };
      });  
        
      this.excelCondition.forEach((x) => this._conditionTriggerService.createNewCondition(x));  

      await this._conditionTriggerService.toSave();
      Alert.ShowSuccess('Excel copied successfully!');  
      this.ref.detectChanges();
      this.ngOnInit();
    } catch (error) {
      Alert.showError(error.message || 'Error in processing Excel data.');
    }
  }
  
}
