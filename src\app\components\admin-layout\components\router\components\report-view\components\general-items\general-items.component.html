<div class="row">

  <!-- START: Receive Events -->
  <div class="col-md-4">
    <div class="report-table-wrapper">
      <h3>Receive Events - Total: {{this.receivedItems.length}}</h3>
      <table class="table report-table table-striped">
        <thead class="sticky" style="top: 0px;">
          <tr>
            <th style="text-align: right" class="th-clickable"
              (click)="this.sortListByNumericType(this.receivedItems, 'amount')">Amount</th>
            <th class="th-clickable" (click)="this.sortListByStringType(this.receivedItems, 'item')">Item</th>
            <th class="th-clickable" (click)="this.sortListByStringType(this.receivedItems, 'location')">Location</th>
            <th style="position: relative" placement='top' delay='250' ttWidth="auto" ttAlign="center" ttPadding="10px"
              tooltip="Show items inside a progress condition" class="custom-th"
              (click)="this.sortListByNumericType(this.receivedItems, 'progress')">Progress
              <i class="pe-7s-info"></i>
            </th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let receive of this.receivedItems; let i = index">
            <tr class="tr-clickable" (click)="goToLocation(receive)">
              <td class="td-20px">{{receive.amount}}</td>
              <td class="">{{receive.item}}</td>
              <td class="td-100">{{receive.location}}</td>
              <td class="td-20px" *ngIf="receive.progress">
                <i style=" color:rgb(0, 0, 0); font-weight:900; font-size: 60px;" class="pe-7s-attention"></i>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
  <!-- END: Receive Events -->

  <!-- START: Trade Events -->
  <div class="col-md-4">
    <h3>Trade Events - Total: {{this.tradeItems.length}}</h3>
    <div class="report-table-wrapper">
      <table class="table report-table table-striped">
        <thead class="sticky" style="top: 0px;">
          <tr>
            <th style="text-align: right" class="th-clickable"
              (click)="this.sortListByNumericType(this.tradeItems, 'amount')">Amount</th>
            <th class="th-clickable" (click)="this.sortListByStringType(this.tradeItems, 'item')">Item</th>
            <th class="th-clickable" (click)="this.sortListByStringType(this.tradeItems, 'location')">Location</th>
            <th style="position: relative" placement='top' delay='250' ttWidth="auto" ttAlign="center" ttPadding="10px"
              tooltip="Show items inside a progress condition" class="custom-th"
              (click)="this.sortListByNumericType(this.tradeItems, 'progress')">Progress
              <i class="pe-7s-info"></i>
            </th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let trade of this.tradeItems; let i = index">
            <tr class="tr-clickable" (click)="goToLocation(trade)">
              <td class="td-20px">{{trade.amount}}</td>
              <td class="">{{trade.item}}</td>
              <td class="td-100">{{trade.location}}</td>
              <td class="td-20px" *ngIf="trade.progress">
                <i style=" color:rgb(0, 0, 0); font-weight:900; font-size: 60px;" class="pe-7s-attention"></i>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
  <!-- END: Trade Events -->

  <!-- START: Give Events -->
  <div class="col-md-4">
    <h3>Give Events - Total: {{this.giveItems.length}} </h3>
    <div class="report-table-wrapper">
      <table class="table report-table table-striped">
        <thead class="sticky" style="top: 0px;">
          <tr>
            <th style="text-align: right" class="th-clickable"
              (click)="this.sortListByNumericType(this.giveItems, 'amount')">Amount</th>
            <th class="th-clickable" (click)="this.sortListByStringType(this.giveItems, 'item')">Item</th>
            <th class="th-clickable" (click)="this.sortListByStringType(this.giveItems, 'location')">Location</th>
            <th style="position: relative" placement='top' delay='250' ttWidth="auto" ttAlign="center" ttPadding="10px"
              tooltip="Show items inside a progress condition" class="custom-th"
              (click)="this.sortListByNumericType(this.giveItems, 'progress')">Progress
              <i class="pe-7s-info"></i>
            </th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let give of this.giveItems; let i = index">
            <tr class="tr-clickable" (click)="goToLocation(give)">
              <td class="td-20px">{{give.amount}}</td>
              <td class="">{{give.item}}</td>
              <td class="td-100">{{give.location}}</td>
              <td class="td-20px" *ngIf="give.progress">
                <i style=" color:rgb(0, 0, 0); font-weight:900; font-size: 60px;" class="pe-7s-attention"></i>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
  <!-- END: Give Events -->
</div>