.cardContent {
    padding-top: 10px;
    display: flex;
    justify-content: center;
    align-items: baseline;
}

.filter-dropdown.limited {
  max-width: 650px;
}



.selectedCategory {
  display: inline-block; 
  margin: 10px; 
  margin-bottom: 15px; 
  width: 300px !important; 
  cursor: pointer;
}

.container {
    margin-top: 30px;
    width: 100%;
    display: flex;
   // justify-content: center;
}
  
.content .skill {
    flex: 3;
  }

  .content {
    width: 25%; 
    margin-left: 10px;
    flex: 1;
  }

h3, .h3 {
    font-size: 28px;
    margin: 10px 0 10px !important;
}

h4 {
    margin: 0 !important;
}

.titlecard{
    width: 135px;
    display: flex;
    justify-content: center;
    margin-top: 10px;
    border-radius: 5px;
    height: 45px;
    align-items: center;
}

.titleCategory{
    display: flex; 
    justify-content: center; 
    font-weight: 600; 
    width: 200px; 
    padding-left: 100px;
}

.titleEffect {
    font-weight: 600; 
    padding-left: 10px;
    padding-right: 15px;
    display: flex;
    justify-content: space-between;
}

.listRepetition {
  margin-left: 13px;
  display: flex;
  justify-content: space-between;
  width: 555px;
  margin-top: 14px;
}


#customers {
  font-family: Arial, Helvetica, sans-serif;
  border-collapse: collapse;
  width: 100%;
}

#customers td, #customers th {
  border: 1px solid #ddd;
  padding: 8px;
}

#customers tr:nth-child(even){background-color: #f2f2f2;}

#customers tr:hover {background-color: #ddd;}

#customers th {
  padding-top: 12px;
  padding-bottom: 12px;
  text-align: left;
  background-color: #04AA6D;
  color: white;
}


  /* Wrapper do dropdown */
  .dropdown-toggle {
    width: 100%; 
    font-size: 14px;
    text-align: left;
    border: 1px solid #ccc;
    border-radius: .25rem;
    background-color: #fff;
    cursor: pointer;
    height: 4vh;
    display: flex;
    align-items: center;
    margin-top: 10px;
  }

  .dropdown-wrapper {
    position: relative;
    overflow: visible; /* Garante que o dropdown possa ser exibido */
    z-index: 10; /* Torna o dropdown acessível em camadas */
}

.dropdown-menu {
    position: absolute;
    top: 90%; /* Exibe o menu logo abaixo do botão */
    left: 0;
    width: 100%; /* Largura alinhada ao botão */
    z-index: 100; /* Torna o menu visível acima de outros elementos */
    background-color: #fff;
    border: 1px solid #ccc;
    border-radius: 0px;
    list-style: none;
    padding: 0;
    display: block;
    max-height: 300px;
    overflow-y: auto;
}

.dropdown-menu {
  visibility: visible !important;
  margin: 0;
  padding: 0;
  display: block;
  z-index: 9000;
  position: absolute;
  opacity: 100;
  filter: alpha(opacity = 0);
  box-shadow: 1px 2px 3px #00000020;
}

.dropdown-menu li {
  display: flex;
  align-items: center;
  padding: 5px 5px;
  cursor: pointer;
  justify-content: space-between;
}

.dropdown-menu li:hover {
  background-color: #0070c0;
  color: white;
  .circle {
    background-color: white;
  }
}

.isDisabled-Status {
  border: 1px solid #ced4da;
  color: #aaaaaa;
  line-height: 1.5;
  cursor: default;
}
.circle {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: none !important;
  background-color: #d9d9d9;
  color: #000;
  font-size: 14px;
  font-weight: bold;
}

.circle:hover {
  background-color: white;
}

.spanTitle {
  padding-top: 7px;
}

.fontWeight {
  font-weight: bold;
}

.repRemain {
  display: flex;
  justify-content: space-between;
  padding-left: 15px;
  padding-right: 15px;
}

.remaing {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  border: none !important;
  background-color: #d9d9d9;
  color: #000;
  font-size: 15px;
  font-weight: bold;
}

/*Ícone de informações */
.iconInter {
  text-align: center;
  font-size: 20px !important;
  margin-top: 1px;
  margin-left: 3px;
}
i:hover{
  color: red;
  cursor: pointer;
}

.pulse-red {
  border: 1px solid #ff0000;
  animation: pulse 0.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1);
  }
  100% {
    transform: scale(1);
  }
}
