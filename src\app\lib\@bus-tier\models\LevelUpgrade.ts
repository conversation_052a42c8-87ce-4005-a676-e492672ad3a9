import { Data } from "src/lib/darkcloud/angular/dsadmin";
import { IdPrefixes } from "src/lib/darkcloud/dialogue-system";
import { Base } from "./Base";

export class LevelUpgrade extends Base<Data.Hard.ILevelUpgrade, Data.Result.ILevelUpgrade> implements Required<Data.Hard.ILevelUpgrade>
{
    public static generateId(index: number)
    {
        return IdPrefixes.LEVEL_UPGRADE + index;
    }

    constructor(index: number, dataAccess: LevelUpgrade['TDataAccess']
    )
    {
        super({hard: 
        {
          id: LevelUpgrade.generateId(index),
          playerLevel: 0,
          totalXP: 0,
        }}, dataAccess);
    }

    public get areaId()
    {
        return this.hard.areaId;
    }

    public set areaId(value: string)
    {
        this.hard.areaId = value;
    }

    public get playerLevel()
    {
        return this.hard.playerLevel;
    }

    public set playerLevel(value: number)
    {
        this.hard.playerLevel = value;
    }

    public get astralPlan()
    {
        return this.hard.astralPlan;
    }

    public set astralPlan(value: number)
    {
        this.hard.astralPlan = value;
    }

    public get totalXP()
    {
        return this.hard.totalXP;
    }

    public set totalXP(value: number)
    {
        this.hard.totalXP = value;
    }

    public get timeToUpgrade()
    {
        return this.hard.timeToUpgrade;
    }

    public set timeToUpgrade(value: number)
    {
        this.hard.timeToUpgrade = value;
    }

    public get rubies()
    {
        return this.hard.rubies;
    }

    public set rubies(value: number)
    {
        this.hard.rubies = value;
    }

    public get hc()
    {
        return this.hard.hc;
    }

    public set hc(value: number)
    {
        this.hard.hc = value;
    }
}
