import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Minigame } from 'src/app/lib/@bus-tier/models/Minigame';
import { LevelService, PopupService, ReviewService, UserSettingsService } from 'src/app/services';
import { LanguageService } from 'src/app/services/language.service';
import { MinigameService } from 'src/app/services/minigame.service';
import { TranslationService } from 'src/app/services/translation.service';
import { Alert } from 'src/lib/darkcloud';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc';

@Component({
  selector: 'app-minigame',
  templateUrl: './minigame.component.html',
  styleUrls: ['./minigame.component.scss']
})
export class MinigameComponent extends SortableListComponent<Minigame>{

  constructor(
    private _reviewService: ReviewService,
    private _minigameService: MinigameService,
    private _popupService: PopupService,
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    private _levelService: LevelService,
    protected _languageService: LanguageService,
    protected _translationService: TranslationService
  ) 
  {
    super(_minigameService, _activatedRoute, _userSettingsService, 'name');
  }

  public minigames: Minigame[] = [];
  
  override lstInit()
  {
    this.minigames = this._minigameService.models;
    this.lstIds = this.minigames.map(x => {
      return x.id;
    });
  }

  async removeMinigame(minigame: Minigame)
  {
    
    let linkedLevels = this._levelService.models.filter(x => {
      return x.minigameId == minigame.id;
    });
    const confirm = await Alert.showConfirm(
      'Area you sure you want to delete ' + minigame.name + '?',
      'It is being used in ' + linkedLevels.length + ' level(s)',
      'Yes'
    );

    if(!confirm)
      return;

    await this._minigameService.svcToRemove(minigame.id);
    linkedLevels.forEach(async element => {
      element.minigameId = undefined;
      await this._levelService.svcToModify(element);
    });
    let index = this.minigames.indexOf(minigame);
    this.minigames.splice(index, 1);
  }

}
