import { ReviewService } from 'src/app/services/review.service';
import { SoundService } from 'src/app/services/sound.service';
import { Component, OnInit } from '@angular/core';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Emotion, Sound } from 'src/app/lib/@bus-tier/models';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/SortableListComponent';
import { PopupService } from 'src/app/services/popup.service';
import { EmotionService } from 'src/app/services/emotion.service';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Sorting } from 'src/lib/darkcloud/angular/easy-mvc/Sorting';
import { Alert, Popup } from 'src/lib/darkcloud';
import { Gender, Voices, GameTypes } from 'src/lib/darkcloud/dialogue-system';
import { Button } from 'src/app/lib/@pres-tier/data';
import { DisplayCheckService } from 'src/app/services/display-check.service';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { SpeechService } from 'src/app/services';
import { SpinnerService } from 'src/app/spinner/spinner.service';
import { SearchOnPageService } from 'src/app/services/search-on-page.service';
import { ActivatedRoute, Router } from '@angular/router';
import { comparableString } from 'src/lib/others';

@Component({
  selector: 'app-audio-list',
  templateUrl: './audio-list.component.html',
  styleUrls: ['./audio-list.component.scss'],
})
export class AudioListComponent extends SortableListComponent<Sound> implements OnInit{
  public readonly Gender = Gender;
  public readonly Voices = Voices;
  

  public readonly uploadButtonTemplate: Button.Templateable = {
    btnClass: Button.Klasses.FILL_GREEN,
    title: 'Refresh',
    onClick: this.handleFileInput.bind(this),
    iconClass: 'pe-7s-music',
  };

  audioList = []
  description;
  caseSensitive: boolean = false;
  accentSensitive: boolean = false;
  queryValue: string = '';

  constructor(
    private spinnerService: SpinnerService,
    private _reviewService: ReviewService,
    _activatedRoute: ActivatedRoute,
    private _soundService: SoundService,
    private _popupService: PopupService,
    private _emotionService: EmotionService,
    private http: HttpClient,
    readonly userSettingsService: UserSettingsService,
    private _router: Router,
    private _displayCheckService: DisplayCheckService,
    protected _languageService: LanguageService,
    protected _translationService: TranslationService,
    protected _searchOnPageService: SearchOnPageService,
    private _speechService: SpeechService
  ) {
    super(_soundService, _activatedRoute, userSettingsService, 'name');
    
  }
  
  public override ngOnInit(): Promise<void> 
  {
    this.spinnerService.setState(true)
    this.toAnalyzeAssetsFolder()
    return null
  }

  public redirectToEmotionList() 
  {
    this._router.navigate(['emotions']);
  }

  handleFileInput(files: FileList) 
  {
    this._router.navigate(['audioUploader']);
  }

  public async toAnalyzeAssetsFolder() 
  {
    if(this._soundService.models.length > 0)
    {
      this.audioList = await this._soundService.models
    }
    this.description = `Showing ${this.audioList.length} results`
    this.spinnerService.setState(false)
  }

  protected override specialSort(parameter: Sorting.Parameter) {
    switch (parameter) {
      case 'emotionId':
        this._modelService.models.sort((a, b) => {
          if (this.srtLstOrder === 'undefinedOnTop') {
            return a[parameter] === undefined && b[parameter] !== undefined
              ? -1
              : 1;
          } else {
            const compA =
              this.srtLstOrder === 'ascending'
                ? this._emotionService.svcFindById(a[parameter])?.name
                : this._emotionService.svcFindById(b[parameter])?.name;
            const compB =
              this.srtLstOrder === 'ascending'
                ? this._emotionService.svcFindById(b[parameter])?.name
                : this._emotionService.svcFindById(a[parameter])?.name;
            return compB !== undefined && compA !== undefined
              ? compA > compB
                ? -1
                : 1
              : a[parameter] === undefined
              ? 1
              : -1;
          }
        });
        break;
      case 'voices':
        this._modelService.models.sort((a, b) => {
          if (this.srtLstOrder === 'undefinedOnTop') {
            return a[parameter] === undefined && b[parameter] !== undefined
              ? -1
              : 1;
          } else {
            const compA =
              this.srtLstOrder === 'ascending'
                ? GameTypes.voiceName[a[parameter]]
                : GameTypes.voiceName[b[parameter]];
            const compB =
              this.srtLstOrder === 'ascending'
                ? GameTypes.voiceName[b[parameter]]
                : GameTypes.voiceName[a[parameter]];
            return compB !== undefined && compA !== undefined
              ? compA > compB
                ? -1
                : 1
              : GameTypes.voiceName[a[parameter]] === undefined
              ? 1
              : -1;
          }
        });
        break;
      case 'assigned':
        this._modelService.models.sort((a, b) =>
          this.srtLstOrder === 'ascending'
            ? this._reviewService.reviewResults[a.id].assignedAt.length >
              this._reviewService.reviewResults[b.id].assignedAt.length
              ? 1
              : -1
            : this._reviewService.reviewResults[b.id].assignedAt.length >
              this._reviewService.reviewResults[a.id].assignedAt.length
            ? 1
            : -1
        );
        break;
      default:
        this.defaultSort(parameter);
        break;
    }
  }
  /**
   * Prompts a button list to select the voices of the audio file
   * @param character character to change the gender of
   */
  public async toPromptSelectEmotion(sound: Sound) 
  {
    const selectedBtn = await this._popupService.fire<Emotion, Emotion>(
      new Popup.Interface(
        {
          title: 'Select Emotion',
          actionsClass: 'column',
        },
        Popup.toButtonList(this._emotionService.models, 'name'),
        {
          hideButton: 
          {
            value: this._emotionService.svcFindById(sound?.emotionId),
          },
        }
      )
    );

    if (!selectedBtn) return;
    

    sound.emotionId = selectedBtn.value.id;
    await this._soundService.svcToModify(sound);
  }

  /**
   * Prompts a button list to select the gender of the audio file
   * @param character character to change the gender of
   */
  public async toPromptSelectGender(sound: Sound) 
  {
    const btns =
      sound.voices == Voices.NONHUMAN
        ? Popup.genderButtons.filter((btn) => btn.value == Gender.UNKNOWN)
        : Popup.genderButtons.filter((btn) => btn.value != Gender.UNKNOWN);
    const selectedBtn = await this._popupService.fire<Gender, Gender>(
      new Popup.Interface(
        {
          title: 'Select Gender',
          actionsClass: 'column',
        },
        btns,
        {
          hideButton: { value: sound.gender },
        }
      )
    );

    if (!selectedBtn) return;
    
    sound.gender = selectedBtn.value;
    await this._soundService.svcToModify(sound);
  }

  /**
   * Prompts a button list to select the voices of the audio file
   * @param character character to change the gender of
   */
  public async toPromptSelectVoice(sound: Sound) 
  {
    const selectedBtn = await this._popupService.fire<Voices, Voices>(
      new Popup.Interface(
        {
          title: 'Select Voices',
          actionsClass: 'column',
        },
        Popup.voiceButtons,
        {
          hideButton: { value: sound.voices },
        }
      )
    );

    if (!selectedBtn) return;
    
    sound.voices = selectedBtn.value;
    await this._soundService.svcToModify(sound);
  }

  async fixSpeechAudioID(oldAudioId: string, newAudioId: string) 
  {
    let t = this._speechService.models.filter(spc => spc.audioId == oldAudioId);
    t.forEach(speech => {
      speech.audioId = newAudioId;
      this._speechService.models[this._speechService.indexOfId(speech.id)] = speech;
    })
  }

     /**
   * Removes an modelect from the list as well from the service data array
   */
      httpOptions = 
      {
        headers: new HttpHeaders(
        {
          'Content-Type':  'application/json'
        })
      };

      
  public override async lstPromptRemove(listItem:any) 
  {
    let canReturn = false
    // removes from the database
    const removed = await this._modelService.toPromptRemove(listItem);
    if (!removed) return;
    
    this._soundService.models = this._soundService.models.filter(sound =>
    {
      if(sound.id == listItem.id)
      {
        canReturn = true
      }
      return sound.id !== listItem.id
    })
      
    this.lstResetHighlights();
    this.lstRemove(listItem);
    this._soundService.toSave()

    if(canReturn) return
    const json = JSON.stringify(listItem.id);

    this.audioList = this.audioList.filter(audio => audio.hard.id !== listItem.hard.id)
       
     const formData = new FormData()
    formData.append('file',json)
    let fileName = listItem.id.split('.')[0].replace('af','')
    let fileExtension = listItem.id.split('.')[1]
 
    this.http.delete(`http://localhost:3000/audios/${fileName}/${fileExtension}`).subscribe
    ({
      next: (v) => console.log(v),
      error: (e) => 
      {
        Alert.showError(e, 'This is a server ERROR. Please take a screen shot of this error and show to the developer!');
        console.error("Errorrrr",e)
      },
      complete: () => console.info('complete') 
    }) 
  }

  /**
   * Atualiza as opções de busca e refiltra a lista
   */
  public updateSearchOptions(caseSensitive: boolean, accentSensitive: boolean): void {
    this.caseSensitive = caseSensitive;
    this.accentSensitive = accentSensitive;
    this.filterMissionNotes();
  }

  /**
   * Atualiza o termo de busca e refiltra a lista
   */
  public updateSearchTerm(term: string): void {
    this.queryValue = term;
    this.filterMissionNotes();
  }

    /**
     * Filtra a lista de mission notes baseado no termo de busca
     * Busca nos campos name e description usando translation pipe
     */
    private filterMissionNotes(): void {
      // Sempre usa this._modelService.models como fonte (que pode estar ordenada)
      const sourceList = this._modelService.models.length > 0 ? this._modelService.models : this.audioList;
  
      if (!this.queryValue || this.queryValue.trim() === '') {
        this.audioList = [...sourceList];
        return;
      }
  
      const searchOptions = {
        caseSensitive: this.caseSensitive,
        accentSensitive: this.accentSensitive
      };
  
      const comparableQuery = comparableString(this.queryValue, searchOptions);
  
      this.audioList = sourceList.filter(audioName => {
        // Busca no campo name (usando translation)
        const name = audioName.name || '';
        const nameMatch = comparableString(name, searchOptions).includes(comparableQuery);  
        return nameMatch;
      });
    }
  

}
