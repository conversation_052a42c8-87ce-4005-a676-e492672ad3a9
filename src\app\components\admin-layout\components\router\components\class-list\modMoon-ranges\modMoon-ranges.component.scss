.main-menu-efect {
    padding-right: 15px;
    padding-left: 15px; 
    min-height: calc(100% - 210px);
    margin-top: 30px;
  }
  
  
  .card {  
     padding-top: 17px !important;
   }
  
   .card-header-content {
     display: block;
     margin-left: 30px;
     margin-right: 15px;
     width: 30%;
   }
  
   //tabela
   .card-container 
  {
    display: flex;
    flex-direction: column;
    align-items: center;
  
    .card 
    {
      border: 1px solid #ccc;
      padding: 15px;
      margin: 5px;
      width: 50vw;
      opacity: 1;
      transition: opacity 0.5s ease-in-out;
    } 
  }
  
  
  h3{
      font-size: 28px;
      margin: 10px 0 10px;
  }

// Estilos específicos para a tabela ModMoon Ranges
.table {
  width: 100%;
  margin-bottom: 1rem;
  background-color: transparent;

  th {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    padding: 8px;
    text-align: center;
    font-size: 12px;
  }

  td {
    border: 1px solid black;
    padding: 4px;
    text-align: center;
   

    &.td-notes {
      padding: 2px;
      width: 180px;
    }
  }
}

// Cabeçalhos agrupados
.table thead tr:first-child th {
  background-color: #e9ecef;  
  font-size: 13px;
}

.table thead tr:last-child th {
  background-color: #f8f9fa;
  font-size: 12px;
}

.styleTitle {
   text-transform: capitalize !important;
}

.gray {
background-color:#ddd;
}

.alignLeft {
  text-align: left !important;
}

.subTitle {
  background-color: #AEAAAA !important;
}

/* Scroll horizontal simples para tabelas grandes */
.horizontal-scroll {
  overflow: auto;
}


.table-responsive {
  /* Personalização da barra de scroll */
  &::-webkit-scrollbar {
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
}

.backtable {
  background-color: white !important;
}

.table-bordered {
    width: 100%;
   margin-bottom: 50px;
  }
  
  .table-bordered th, .table-bordered td {
    padding: 8px;
    text-align: center;
  }