<div class="m-container">
    <table class="table-bordered">
        <thead>
            <tr>
                <th rowspan="2" class="trBC">INDEX</th>
                <th colspan="5" class="trBC">CONDITION (Trigger)</th>
            </tr>
            <tr>
                <th>ID</th>
                <th>Description</th>
                <th>Type</th>
                <th>Value</th>
            </tr>
        </thead>
        <tbody>
            <ng-container *ngIf="listDuration.length > 0">
                <tr *ngFor="let dura of listDuration; let i = index">
                    <td>{{ i + 1 }}</td>
                    <td class="bc">{{dura.idValue}}</td>
                    <td class="bc">{{dura.description}}</td>
                    <td class="bc">{{dura.type}}</td>
                    <td class="bc">{{dura.value}}</td>
                </tr>
            </ng-container>
        </tbody>
    </table>
    
</div>

<ng-container *ngIf="listDuration.length === 0">
    <div style="text-align: center;">
        <h4>Empty List</h4>
    </div>
</ng-container>