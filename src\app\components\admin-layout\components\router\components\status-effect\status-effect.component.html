<div class="main-menu-efect">
  <div class="container-fluid">
    <div class="list-header-row update">

      <div class="card">

        <div style="width: 50%;">
          <button style="margin-left: 15px;"
            class="{{activeTab === 'category' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
            (click)="switchToTab('category')">Category</button>
          <button style="margin-left: 2px;"
            class="{{activeTab === 'repetition' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
            (click)="switchToTab('repetition')">Repetition</button>
          <button style="margin-left: 2px;"
            class="{{activeTab === 'idBlocks' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
            (click)="switchToTab('idBlocks')">ID Blocks</button>
          <button style="margin-left: 2px;"
            class="{{activeTab === 'status-effect' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
            (click)="switchToTab('status-effect')">Status Effects tables</button>
        </div>

        <ng-container *ngIf="isTab">
          <div style="display: flex; justify-content: space-between;">
            <div class="card-header-content" style="margin-top: 20px;">
              <h3 class="title">{{title}}</h3>
              <p style="width:60vw;" class="category">{{ description}}</p>
            </div>

            <div style="display: flex; align-items: end; justify-content: end;">
              <div style="margin-right: 15px; margin-bottom: 16px;">
                <ng-container *ngIf="activeTab === 'category'">
                  <!--BUTTON ADD + -->
                  <div id="add"
                    style="display: flex; align-items: flex-end; justify-content: end; margin-right: 10px; position: relative;">
                    <button class="btn btn-success btn-fill ng-star-inserted" (click)="addStatusEffect()">
                      <i class="pe-7s-plus"></i>
                    </button>
                  </div>
                </ng-container>



                <ng-container *ngIf="activeTab === 'repetition'">
                  <!--BUTTON EXCEL-->
                  <div id="button" style="position: absolute;">
                    <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'"
                      class="add-buttons" [buttonTemplates]="[excelButtonTemplate]">
                    </app-button-group>
                  </div>
                </ng-container>

                <ng-container *ngIf="activeTab === 'idBlocks'">

                  <ul>
                    <li style="margin-left: 2px;" class="{{activeTab2 === 'chaos' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                      (click)="switchToTab2('chaos')">Chaos ID Blocks</li>
                    <li style="margin-left: 2px;" class="{{activeTab2 === 'dispel' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                      (click)="switchToTab2('dispel')">Dispel ID Blocks</li>
                    <li style="margin-left: 2px;" class="{{activeTab2 === 'ailment' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                      (click)="switchToTab2('ailment')">Ailment ID Blocks</li>
                    <li style="margin-left: 2px;" class="{{activeTab2 === 'negative' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                      (click)="switchToTab2('negative')">Negative ID Blocks</li>
                    <li style="margin-left: 2px;" class="{{activeTab2 === 'defensive' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                      (click)="switchToTab2('defensive')">Defensive ID Blocks</li>
                    <li style="margin-left: 2px;" class="{{activeTab2 === 'healing' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                      (click)="switchToTab2('healing')">Healing ID Blocks</li>
                    <li style="margin-left: 2px;" class="{{activeTab2 === 'boost' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                      (click)="switchToTab2('boost')">Boost ID Blocks</li>
                  </ul>

                </ng-container>


                <ng-container *ngIf="activeTab === 'status-effect'">

                  <ul>
                    <li style="margin-left: 2px;" class="{{activeTab3 === 'combo' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                      (click)="switchToTab3('combo')">Combo Table</li>
                          <li style="margin-left: 2px;" class="{{activeTab3 === 'chaosTable' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                      (click)="switchToTab3('chaosTable')">Chaos Table</li>
                    <li style="margin-left: 2px;" class="{{activeTab3 === 'afflictionTable' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                      (click)="switchToTab3('afflictionTable')">Affliction Table</li>
                    <li style="margin-left: 2px;" class="{{activeTab3 === 'hybridTable' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                      (click)="switchToTab3('hybridTable')">Hybrid Table</li>
                    <li style="margin-left: 2px;" class="{{activeTab3 === 'dispelTable' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                      (click)="switchToTab3('dispelTable')">Dispel Table</li>
                    <li style="margin-left: 2px;" class="{{activeTab3 === 'ailmentTable' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                      (click)="switchToTab3('ailmentTable')">Ailment Table</li>
                    <li style="margin-left: 2px;" class="{{activeTab3 === 'negativeTable' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                      (click)="switchToTab3('negativeTable')">Negative Table</li>
                    <li style="margin-left: 2px;" class="{{activeTab3 === 'defensiveTable' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                      (click)="switchToTab3('defensiveTable')">Defensive Table</li>
                    <li style="margin-left: 2px;" class="{{activeTab3 === 'healingTable' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                      (click)="switchToTab3('healingTable')">Healing Table</li>
                    <li style="margin-left: 2px;" class="{{activeTab3 === 'boostTable' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                      (click)="switchToTab3('boostTable')">Boost Table</li>
                  </ul>

                </ng-container>

              </div>
            </div>
          </div>
          <!--
                    <div style="margin-left: 15px;">
            <app-header-search (inputKeyup)="search($event)" (searchOptions)="lstOnChangeFilterOptions($event)">
            </app-header-search>
          </div>
          -->

        </ng-container>

      </div>
    </div>

    <app-category-status-effect *ngIf="activeTab === 'category'"
      (descriptionOutput)="receiveText($event)"></app-category-status-effect>
    <app-repetition-status-effect *ngIf="activeTab === 'repetition'" [copyExcelRepetition]="listExcelRepetition"
      (descriptionOutput)="receiveText($event)"></app-repetition-status-effect>

    <!--BUTTON ID BLOCKS-->
    <app-boost-id-blocks *ngIf="activeTab2 === 'boost' && activeTab === 'idBlocks'"
      (activeTab2)="switchToTab2($event)"></app-boost-id-blocks>
    <app-healing-id-blocks *ngIf="activeTab2 === 'healing' && activeTab === 'idBlocks'"
      (activeTab2)="switchToTab2($event)"></app-healing-id-blocks>
    <app-defensive-id-blocks *ngIf="activeTab2 === 'defensive' && activeTab === 'idBlocks'"
      (activeTab2)="switchToTab2($event)"></app-defensive-id-blocks>
    <app-negative-id-blocks *ngIf="activeTab2 === 'negative' && activeTab === 'idBlocks'"
      (activeTab2)="switchToTab2($event)"></app-negative-id-blocks>
    <app-ailment-id-blocks *ngIf="activeTab2 === 'ailment' && activeTab === 'idBlocks'"
      (activeTab2)="switchToTab2($event)"></app-ailment-id-blocks>
    <app-dispel-id-blocks *ngIf="activeTab2 === 'dispel' && activeTab === 'idBlocks'"
      (activeTab2)="switchToTab2($event)"></app-dispel-id-blocks>
    <app-chaos *ngIf="activeTab2 === 'chaos' && activeTab === 'idBlocks'"
      (activeTab2)="switchToTab2($event)"></app-chaos>


    <!-- BUTTON STATUS EFFECTS TABLES-->
    <app-boost-table *ngIf="activeTab3 === 'boostTable' && activeTab === 'status-effect'"
      (activeTab2)="switchToTab3($event)"></app-boost-table>
    <app-healing-table *ngIf="activeTab3 === 'healingTable' && activeTab === 'status-effect'"
      (activeTab2)="switchToTab3($event)"></app-healing-table>
    <app-defensive-table *ngIf="activeTab3 === 'defensiveTable' && activeTab === 'status-effect'"
      (activeTab2)="switchToTab3($event)"></app-defensive-table>
    <app-negative-table *ngIf="activeTab3 === 'negativeTable' && activeTab === 'status-effect'"
      (activeTab2)="switchToTab3($event)"></app-negative-table>
    <app-ailment-table *ngIf="activeTab3 === 'ailmentTable' && activeTab === 'status-effect'"
      (activeTab2)="switchToTab3($event)"></app-ailment-table>
    <app-dispel-table *ngIf="activeTab3 === 'dispelTable' && activeTab === 'status-effect'"
      (activeTab2)="switchToTab3($event)"></app-dispel-table>
    <app-hybrid-table *ngIf="activeTab3 === 'hybridTable' && activeTab === 'status-effect'"
      (activeTab2)="switchToTab3($event)"></app-hybrid-table>
    <app-affliction-table *ngIf="activeTab3 === 'afflictionTable' && activeTab === 'status-effect'"
      (activeTab2)="switchToTab3($event)"></app-affliction-table>
      <app-combo *ngIf="activeTab3 === 'combo' && activeTab === 'status-effect'"
      (activeTab2)="switchToTab3($event)"></app-combo>
      <app-chaos-table *ngIf="activeTab3 === 'chaosTable' && activeTab === 'status-effect'"
      (activeTab2)="switchToTab3($event)"></app-chaos-table>

  </div>
</div>