import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { Button } from 'src/app/lib/@pres-tier/data';
import { IndexStorageService } from 'src/app/services';
import { BonusService } from 'src/app/services/bonus.service';
import { FILTER_SUFFIX_PATH } from 'src/lib/darkcloud/angular/dsadmin/constants/others';
import { ConditionTriggerService } from '../../../../../../services/conditionTrigger.service';
import { DurationService } from '../../../../../../services/duration.service';
import { PassiveAllowedService } from '../../../../../../services/passiveAllowed.service';
import { BonusComponent } from './bonus/bonus.component';
import { ConditionComponent } from './condition/condition.component';
import { DurationComponent } from './duration/duration.component';
import { PassiveAllowedComponent } from './passive-allowed/passive-allowed.component';


@Component({
  selector: 'app-passive-atributes',
  templateUrl: './passive-atributes.component.html',
  styleUrls: ['./passive-atributes.component.scss']
})
export class PassiveAtributesComponent implements OnInit, OnDestroy {

  public activeTab: string;
  activeLanguage = 'PTBR';
  cardTitle: string;
  description: string;
  timeout: any; 

  public readonly excelButtonTemplate: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  // Este EventEmitter será ligado ao componente filho
  @ViewChild(BonusComponent) bonusComponent: BonusComponent;
  @ViewChild(ConditionComponent) conditionComponent: ConditionComponent;
  @ViewChild(DurationComponent) durationComponent: DurationComponent;
  @ViewChild(PassiveAllowedComponent) passiveAllowedComponent: PassiveAllowedComponent;
  

  constructor(
    private _indexStorageService: IndexStorageService,  
    private bonusService : BonusService,
    private _durationService: DurationService,
    private _conditionTriggerService: ConditionTriggerService,
    private _passiveAllowedService: PassiveAllowedService,) {}

  public ngOnInit() {
    this.cardTitle = 'BONUS';    
    const tab = localStorage.getItem(`tab-PassiveAtributesComponent${FILTER_SUFFIX_PATH}`);
    this.activeTab = 'bonus';

    this.activeLanguage = IndexStorageService.activeLanguage;
    this._indexStorageService.onLanguageChange$.subscribe(l => {
      this.activeLanguage = IndexStorageService.activeLanguage;
    });
   
      this.timeout = setTimeout(()=> {
        if(this.bonusService.models.length > 0) {
        this.description = `Showing ${this.bonusService.models.length} results`; 
        } else {
          this.description = `Showing ${0} results`;  
        }
      }, 610);  

  }

  receiveText(text: string) {
    this.description = text;
  }

  public switchToTab(tab: string) {
    this.activeTab = tab;
    localStorage.setItem(`tab-PassiveAtributesComponent${FILTER_SUFFIX_PATH}`, this.activeTab);

    if (this.activeTab === 'condition') {
      this.cardTitle = 'CONDITION (Trigger)';

      this.timeout = setTimeout(()=> {
        if(this._conditionTriggerService.models.length > 0) {
        this.description = `Showing ${this._conditionTriggerService.models.length} results`; 
        } else {
          this.description = `Showing ${0} results`;  
        }
      }, 610);  

    } else if (this.activeTab === 'duration') {
      this.cardTitle = 'DURATION';
      this.timeout = setTimeout(()=> {
        if(this._durationService.models.length > 0) {
        this.description = `Showing ${this._durationService.models.length} results`; 
        } else {
          this.description = `Showing ${0} results`;  
        }
      }, 610);  
    } else if (this.activeTab === 'passiveAllowed') {
      this.cardTitle = 'PASSIVE ALLOWED FOR WEAPONS';
      this.timeout = setTimeout(()=> {
        if(this._passiveAllowedService.models.length > 0) {
        this.description = `Showing ${this._passiveAllowedService.models.length} results`; 
        } else {
          this.description = `Showing ${0} results`;  
        }
      }, 710); 
    } else {
      this.cardTitle = 'BONUS';
      this.timeout = setTimeout(()=> {
        if(this.bonusService.models.length > 0) {
        this.description = `Showing ${this.bonusService.models.length} results`; 
        } else {
          this.description = `Showing ${0} results`;  
        }
      }, 610);  
    }
  }

  ngOnDestroy() {
    clearInterval(this.timeout);
  }

  onExcelPaste() {
      
    // Emite o evento para o componente filho
    if (this.activeTab === 'bonus' && this.bonusComponent) {
      this.bonusComponent.onExcelPaste();
    } else if (this.activeTab === 'condition' && this.conditionComponent) {
      this.conditionComponent.onExcelPaste();
    } else if (this.activeTab === 'duration' && this.durationComponent) {
      this.durationComponent.onExcelPaste();
    } else {
      this.passiveAllowedComponent.onExcelPaste();
    }
  
  }
}
