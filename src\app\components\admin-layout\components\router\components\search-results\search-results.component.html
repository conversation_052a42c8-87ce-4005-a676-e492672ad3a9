<div class="main-content">
  <div class="card list-header-row">
    <div class="col-md-12">
      <div class="card list-header" style="height: 120px">
        <div class="category">
          Showing {{ results.length }} of {{ allResults.length }}
        </div>
        <div class="search-bar-wrapper">
          <i class="pe-7s-search"></i>
          <input type="text" class="form-control" [(ngModel)]="term" (ngModelChange)="search()"/>
          <p>Accent sensitive</p>
          <input type="checkbox" class="toggle" (click)="toggleSearcherAccentSetting()"/>
          <p>Case sensitive</p>
          <input type="checkbox" class="toggle" (click)="toggleSearcherCaseSetting()"/>
          <p>Include Removed</p>
          <input type="checkbox" class="toggle" (click)="toggleDisplayRemovedResults()"/>
          <p>Show All Results</p>
          <input type="checkbox" class="toggle" (click)="toggleShowAllResults()"/>
        </div>
      </div>
    </div>
  </div>
  <div class="centered-controls">
    <button class="btn-success btn-fill btn" (click)="search()">
      <i class="pe-7s-play"></i>
    </button>
    <button class="btn-danger btn-fill btn" (click)="stopSearch()">
      <i class="pe-7s-close"></i>
    </button>
  </div>
    <div class="col-md-12">
      <div class="card">
        <table class="table search-results-table table-striped">
          <thead style="top: 100px">
            <th></th>
            <th>
              <select name="Type Name" id="typeName" [(ngModel)]="filterTypeName">
                <option value="">None</option>
                <option value="Character">Character</option>
                <option value="Class">Class</option>
                <option value="Area">Area</option>
                <option value="Level">Level</option>
                <option value="Event">Event</option>
                <option value="Marker">Marker</option>
                <option value="Mission">Mission</option>
                <option value="Scenery">Scenery</option>
                <option value="Item">Item</option>
                <option value="Tutorial">Tutorial</option>
                <option value="Microloop">Microloop</option>
                <option value="Dilemma">DilemmaBox</option>
              </select>
            </th>
            <th>Error</th>
            <th>
              Result
              <select 
                class="dropdown filter-dropdown limited center" name="itemTypeFilter"
                (change)="filterSearchResults()" [(ngModel)]="resultType">
                <option value="ALL">Any</option>
                <option *ngFor="let type of preloadedResultTypes" value="{{ type }}">
                  {{ type | searchResultTypeName }}
                </option>
              </select>
            </th>
            <th>
              Parameter
              <select 
                class="dropdown filter-dropdown limited center" name="itemTypeFilter"
                (change)="filterSearchResults()" [(ngModel)]="resultParam">
                <option value="ALL">Any</option>
                <option *ngFor="let parameter of preloadedParams" value="{{ parameter }}">
                  {{ parameter }}
                </option>
              </select>
            </th>
            <th class="th-clickable" (click)="sortListByParameter('hierarchyCode')">
              Hierarchy
            </th>
            <th>Detail</th>
            <th>Location Type</th>
          </thead>
          <tbody>
            <ng-container *ngFor="let result of results | searchResultFilter : filterTypeName; let i = index; trackBy: trackByIndex">
              <tr [title]="result.id" [ngClass]="result.wasRemoved ? '' : ' tr-clickable'" (click)="redirectTo(result)">
                <td class="td-sort">
                  {{ i + 1 }}
                </td>
                <td class="td-20px">
                  <i [ngClass]="result | searchResultIcon"></i>
                </td>
                <td class="td-20px">
                  <i *ngIf="result.wasRemoved" class="pe-7s-trash error"></i>
                </td>
                <td class="td-auto center">
                  {{ result.type | searchResultTypeName }}
                </td>
                <td class="td-auto center">
                  {{ result.field }}
                </td>
                <td class="td-20">
                  {{([result?.id] | location).length > 0 ? ([result.id] | location) : 'Ophan'}}    
                </td>
                <td class="td-30">
                  <ng-container *ngIf="+result.type >= +ResultType.PARAMETER_MATCH">
                    <span [innerHTML]="((result | searchResultValue) | highlightWord : term : true | rpgFormatting) || 
                      ([result.id] | location)">
                    </span>
                  </ng-container>
                </td>
                <td class="td-20" [ngClass]="result | searchResultClass">
                  <span [innerHTML]="result | searchResultDescription: term"></span>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
    </div>
</div>