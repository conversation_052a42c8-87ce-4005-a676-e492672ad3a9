.card {
    // padding-top: 8px !important;
     padding-top: 17px !important;
     height: 80px;
   }
 
   .card-header-content {
     display: block;
     margin-left: 30px;
     margin-right: 15px;
     width: 40%;
   }

   .table-bordered {
    width: 100%;
   }

th, td {
    
    text-align: center;
    border: 1px solid #2f2f2f;
}

.td-sort {
    background-color: #e6e6e6 !important;
}

.other-td
{
    border-left: 1px solid rgb(187, 187, 187) ;
    border-right: 1px solid rgb(187, 187, 187) ;
    border-bottom: 1px solid rgb(187, 187, 187);
}

.order {
    font-weight: 400;
    height: 100px; 
    width: 4%; 
    border: 2px gray solid !important;
}

.inputModif {
    border-style:solid; 
    top:-1000px !important;
    width: 100%; 
    padding-left: 15px;
}
.textAlig {
    text-align: left;
    padding-left: 8px;
}