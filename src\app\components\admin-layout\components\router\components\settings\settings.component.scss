
#loader {
    position: absolute;
    display: block;
    left: 0;
    right: 0;
    margin-left: auto;
    margin-right: auto;
    z-index: 1;
    -webkit-transform: translate3d(0, -50%, 0);
    -moz-transform: translate3d(0, -50%, 0);
    -o-transform: translate3d(0, -50%, 0);
    -ms-transform: translate3d(0, -50%, 0);
    transform: translate3d(0, -50%, 0);
    text-align: center;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    top: 50%;
    animation: fadeOut 30s;
    animation-delay: 10s; /* adiciona uma pausa de 7 segundos antes de iniciar a animação */
  }

  @keyframes fadeOut {
    0% {
      opacity: 1;
    }
    100% {
      opacity: 0;
    }
  }



.roadblockContainer
{
    position: absolute; 
    right:30%; 
    min-width: 600px; 
    min-height: 300px; 
    top:50%; 
    border: 3px solid #cacaca; 
    overflow-y: scroll;
    background-color: white;
}

.roadblockContent
{
    z-index: 99999; 
    padding: 10px; 
    background-color: white;
}

.ai-icon
{
    max-width: 45px;
    max-height: 45px;
    padding-bottom: 2px;
}

.icon-OpenAi
{
    max-width: 80px;
    max-height: 60px;
    padding-bottom: 2px;
    padding-right: 5px;
}

.btn-OpenAi {
   background-color: #C0C0C0;
   opacity: 1;
   border: silver !important;
}

.roadblockCard
{
    display: flex;
    box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2); 
    min-height: 100px;
    align-items: center;
    align-content: center;
    justify-content: center;
}

.popupContainer
{
    position: absolute; 
    right:40%; 
    min-width: 600px; 
    max-height: 300px; 
    top:30%; 
    border: 3px solid #cacaca; 
    overflow-y: scroll;
    background-color: white;
    flex-direction: column;
}

.popupContent
{
    z-index: 99999;
    padding: 10px; 
    background-color: white;
}

.popupCard
{
    display: flex;
    box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2); 
    min-height: 100px;
    align-items: center;
    align-content: center;
    justify-content: center;
}

.popupHeader
{
    display:flex; 
    flex-direction: row;
    align-items: center;
    align-content: center;
    justify-content: space-around;

}

.content-card {
 padding: 5px 15px 10px 15px;
}

.div_color {
border-left: 1px solid #ccc;  
}

.icon-size {
    font-size: 30px !important;
}
.btn-height {
    height: 45px !important;
}