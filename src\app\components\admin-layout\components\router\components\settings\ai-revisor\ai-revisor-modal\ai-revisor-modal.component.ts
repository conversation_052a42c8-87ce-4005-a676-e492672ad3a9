import { Component, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-ai-revisor-modal',
  templateUrl: './ai-revisor-modal.component.html',
  styleUrls: ['./ai-revisor-modal.component.scss']
})
export class AiRevisorModalComponent {

  /**
   * Controla se o modal está visível ou não
   */
  @Input() isModalInfo: boolean = false;

  /**
   * Evento emitido quando o modal deve ser fechado
   */
  @Output() modalClose = new EventEmitter<void>();

  /**
   * Fecha o modal e emite o evento para o componente pai
   */
  closeAreaStatsPopup(): void {
    this.modalClose.emit();
  }

  /**
   * Fecha o modal quando o mouse sai da área (mouseleave)
   */
  onMouseLeave(): void {
    this.modalClose.emit();
  }

}
