
<div class="list-header-row update">
    <div class="card">
        <div style="display: flex; justify-content: space-between;">
            <div class="card-header-content">
                <h3 class="title">KnowledgeCheck</h3>
                <p style="width:60vw;" class="category">{{ description}}</p>
            </div>
        </div>
    </div>
</div>

<div style="display: flex; flex-direction: row; overflow-x: auto; width: 700px;">
    <table class="table table-list borderList">
        <thead>
            <tr>
                <th th colspan="10">FAILURE LEVELS</th>
            </tr>
        </thead>
        <tbody>
            <tr *ngFor="let title of titles; let i = index">
                <td class="td-id aligTitle">
                    {{title}}
                </td>
                <td class="td-id">
                    <input class="background-input-table-color form-control form-short" placeholder=" "
                        type="number" [value]="getValueTitle(title, 0)" #dice1
                        (change)="changeFrustationValue(title, 0, dice1.value)" />
                </td>
                <td class="td-id">
                    <input class="background-input-table-color form-control form-short" placeholder=" "
                        type="number" [value]="getValueTitle(title, 1)" #dice2
                        (change)="changeFrustationValue(title, 1, dice2.value)" />
                </td>
            </tr>
        </tbody>
    </table>
</div>


