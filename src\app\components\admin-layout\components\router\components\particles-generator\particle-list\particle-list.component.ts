import { Component, EventEmitter, Input, Output} from '@angular/core';
import { AreaService } from 'src/app/services/area.service';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Area, Item } from 'src/app/lib/@bus-tier/models';
import { CharacterService, LevelService } from 'src/app/services';
import { TranslationService } from 'src/app/services/translation.service';
import { LanguageService } from 'src/app/services/language.service';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';
import { fadeIn, popup } from '../../bound-item-list/bound-list.component.animations';
import { Particle } from 'src/app/lib/@bus-tier/models/Particle';
import { ActivatedRoute } from '@angular/router';

type Particles = {
    name : string, 
    hcLevel : number,
    atk: number,
}
@Component({
  selector: 'app-particle-list',
  templateUrl: './particle-list.component.html',
  styleUrls: ['./particle-list.component.scss'],
  animations: [fadeIn, popup]
})
export class ParticleListComponent extends TranslatableListComponent<Area> {
  constructor(
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    private _areaService: AreaService,
    private _levelService: LevelService,
    private _characterService: CharacterService,
    protected override _translationService: TranslationService,
    protected override _languageService: LanguageService
  ) {
    super(_areaService, _activatedRoute, _userSettingsService, 'name', _translationService, _languageService);
  }
  
  @Output() isParticleListOpen = new EventEmitter<boolean>();

  @Input() particleList:Particle[] = [] 
  @Input() itemList:Item[] = [] 
   
   particlesList : Particles[] = [];
   uniqueParticlesList : Particles[] = []
  
override lstInit(){
    this.setParticlesList()
}

setParticlesList(){
    this.itemList.forEach(item => {
        this.particleList.forEach(particle => {
            if(item.id === particle.itemId)
            {
                let p = {
                    name : item.name, 
                    hcLevel : particle.hcLevel, 
                    atk:particle.atk,
                }
                this.particlesList.push(p)
            }           
        })
    })  
}
  closeListStatsPopup()
  {
    this.isParticleListOpen.emit(false)
  }


  
  sortByNameOrder = -1;
  sortByName() {
    this.sortByNameOrder *= -1;
    this.particlesList.sort((a, b) => {
        return this.sortByNameOrder *  a.name.localeCompare(b.name);
    });
  }

  sortHCLevelOrder = -1;
  sortHCLevel() {
    this.sortHCLevelOrder *= -1;
    this.particlesList.sort((a, b) => {      
        return this.sortHCLevelOrder * (a.hcLevel - b.hcLevel);
    });
  }

  sortATKOrder = -1;
  sortATK() {
    this.sortATKOrder *= -1;
    this.particlesList.sort((a, b) => {      
        return this.sortATKOrder * (a.atk - b.atk);
    });
  }


}
