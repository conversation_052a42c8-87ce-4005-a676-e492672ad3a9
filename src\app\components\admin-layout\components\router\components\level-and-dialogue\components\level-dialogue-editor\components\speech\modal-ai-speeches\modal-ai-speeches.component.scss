/* Fundo escurecido (backdrop) */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5); /* Ajuste a opacidade aqui */
  z-index: 998; /* <PERSON>e ser menor que o modal */
}

.background-div {	
  position: relative !important;	
}	
.item-overlay
{
    position: absolute;
    //border: 15px solid black;
    height: 100%;
    width: 100%;
    background-color: white;
    z-index: 150;
}

.card_investigation {
  background-color: #9bc9ba;
}

.popup-report
{
    position: fixed;
    height: 70%;
    background-color: white;
    transform: translate(-0%, 20%);
    z-index: 999; /* Maior que o backdrop */
    top: 7%;
    margin: auto;
    left: 0;
    right: 0;
    margin-left: auto;
    margin-right: auto;
    margin-top: auto;
    margin-bottom: auto;
}

.c-title {
    display: flex; 
    align-items: center; 
    width: 100%;
    padding: 10px;
}

.btn-close {
  width: 100%;
  display: flex;
  justify-content: end;
}

.comp-container {
  display: flex; 
  justify-content: center; 
  margin-bottom: 10px;
  align-items: center;
}

.title-element {
  margin-top: 5px;
  padding: 10px; 
  text-align: center;
}

.width-container {
 // width: 920px; 
 display: contents; 
}

.total-modal {
    background-color: transparent; 
    border-radius: 5px; 
   // border: 2px solid #555; 
    padding-bottom: 10px;
    padding-left: 20px;
    padding-right: 20px;
}

.total-content {
    overflow-y: auto;
    height: auto;
    scrollbar-width: thin;
    scroll-behavior: auto;    
    max-height: 690px;
}

 
.box {
  width: 900px;
  margin: 20px 0;
  border: 1px solid #ccc; 
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  height: fit-content;
  background-color: white;
}

.box h3, .box h4 {
  margin: 0;
  padding: 0;
  font-weight: 700;
}

.box-confirm {
  width: 500px;
  margin: 20px 0;
  border: 1px solid #ccc; 
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  height: fit-content;
  padding: 20px;
  background-color: white;
  justify-items: center;
  margin-left: 20px;
}

.box-grammar {
  width: 280px;
  margin: 20px 0;
  border: 1px solid #ccc; 
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  height: fit-content;
  padding: 20px;
  background-color: white;
  justify-items: center;
  margin-right: 20px;
}

h3, h4 {
  margin: 0;
  padding: 0;
  font-weight: 700 !important;
}

h1 {
  font-size: 60px;
}

.position-icon {
  float: right;
  margin-left: 20px; 
  margin-right: 30px;
  margin-top: -10px;
}

.icon	
{	
    font-size: 30px;	
    color: #999;	
    position: relative;	
    transition: transform 0.3s;	
    transform-origin: center;	
}	
.icon:hover	
{	
    font-size: 30px;	
    color: #555;	
    transform: translate(-2.5%) rotate(180deg);	
}	

.swal-icon-close {
  position: relative;
  z-index: 2;
  top: -40px;
  right: 0;
  align-items: center;
  justify-content: center;
  width: 1.2em;
  height: 1.2em;
  padding: 0;
  overflow: hidden;
  transition: color .1s ease-out;
  border: none;
  border-radius: 0;
  background: 0 0;
  color: #ccc;
  font-family: serif;
  font-size: 2.5em;
  line-height: 1.2;
  cursor: pointer;
  float: inline-end;
  float: right;;

}

.btn-modal {
  width: 100%;
  background-color: red; 
  color: white; 
  margin-right: 10px;
  border-color: red;
  margin-bottom: 3px;
}

.btn-cancel {
  width: 100%;
}

//Falta revisard aqui pra baixo

.c-message {
  color:#8c8b8b; 
  font-weight: 100; 
  margin-bottom: 5px;
}

.btn-remove {
  font-size: 2px; 
  margin: 0; 
  padding: 3px 5px
}
.style-icon {
  font-size: 24px; 
  margin: 0; padding: 0
}

hr {
  border-top: 1px solid #ddd;
  margin-right: 300px;
  margin-top: 0 !important;
}

.inv-box {
  display: flex;   
  padding-left: 20px; 
  align-items: center; 
}
.inv-input {
  width: 60%;
  margin-left: 60px;
  margin-right: 20px;
}

//popup
.actions-column {
 display: grid;
 z-index: 1;
 flex-wrap: nowrap;
 justify-content: center;
 padding: 10px;
}

.icon-refresh-text {
  font-size: 50px !important;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.content-refresh {
  margin: auto;
  display: flex;
  max-height: 200vh;
  align-self: center;
  align-items: center;
  padding-bottom: 20px;
}

.btn-dimension {
  min-width: 200px !important;
  width: 100% !important;
  margin-top: 3px;
}

.selectedOption {
  background-color: #3472f7;
  color: #fff;
}

.selected {
  background-color: graya;
  color: #fff;
}

button {
  white-space: normal;
  word-wrap: break-word;
  text-align: left; /* Opcional, caso queira alinhar o texto à esquerda */
  max-width: 100%; /* Garante que o texto não ultrapasse o tamanho do botão */
}

///
.loading-dots {
  display: flex;
  width: 60px;
  height: 20px;
  position: relative;
  justify-self: center;
  justify-content: center;
  margin: auto;
}

.loading-dots .dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #333;
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  animation: move 2s linear infinite;
}

.loading-dots .dot:nth-child(1) {
  animation-delay: 0s;
}

.loading-dots .dot:nth-child(2) {
  animation-delay: 0.5s;
}

.loading-dots .dot:nth-child(3) {
  animation-delay: 1s;
}

@keyframes move {
  0% {
    left: 0;
  }
  50% {
    left: 40px;
  }
  100% {
    left: 0;
  }
}

.div-description {
  display: block; 
  margin-left: 20px; 
  margin-right: 20px; 
  text-align: center;
}

.sendCharaterMessage {
  width: 90%;
  padding-left: 20px;
  font-weight: 600;
 color: red;
 text-decoration: underline;
}

.div-selected {
  margin:10px; 
  max-height: 85vh; 
  align-self: center; 
  margin-top: 50px; 
  margin-bottom: 25px;
}

.text-center {
  margin-bottom: 3px;
  text-align: center;
}