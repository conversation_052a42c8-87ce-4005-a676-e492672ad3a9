$mynewcolor: #000000;

.card_background_storyBox {
  background-color: #bdced9;
}

.btn-mi {
  background-color: #ed7d31;
  border-color: #ed7d31;
}
.btn-mi:hover,
.btn-mi:focus,
.btn-mi:active,
.btn-mi.active,
.open .dropdown-toggle.btn-mi {
  color: #ffffff;
  background-color: #f09846;
  border-color: #fc9b40;
}
.switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.component_AndOr {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  gap: 10px;
  align-content: center;
  align-items: center;
  align-self: center;
  margin-left: 10px;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ff00aa;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

input:checked + .slider {
  background-color: #2196f3;
}

input:focus + .slider {
  box-shadow: 0 0 1px #2196f3;
}

input:checked + .slider:before {
  -webkit-transform: translateX(26px);
  -ms-transform: translateX(26px);
  transform: translateX(26px);
}

/* Rounded sliders */
.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}

.btn-mi:active,
.btn-mi.active,
.open .dropdown-toggle.btn-mi {
  background-image: none;
}

.btn-mi.disabled,
.btn-mi[disabled],
fieldset[disabled] .btn-mi,
.btn-mi.disabled:hover,
.btn-mi[disabled]:hover,
fieldset[disabled] .btn-mi:hover,
.btn-mi.disabled:focus,
.btn-mi[disabled]:focus,
fieldset[disabled] .btn-mi:focus,
.btn-mi.disabled:active,
.btn-mi[disabled]:active,
fieldset[disabled] .btn-mi:active,
.btn-mi.disabled.active,
.btn-mi[disabled].active,
fieldset[disabled] .btn-mi.active {
  background-color: #fc9b40;
  border-color: #fc9b40;
}

.btn-mi .badge {
  color: #fc9b40;
  background-color: #ffffff;
}

.btn-boss {
  background-color: #ff00bf;
  border-color: #ff00bf;
}
.btn-boss:hover,
.btn-boss:focus,
.btn-boss:active,
.btn-boss.active,
.open .dropdown-toggle.btn-boss {
  color: #ffffff;
  background-color: #e000a8;
  border-color: #e000a8;
}

.btn-item {
  background-color: #ffc000;
  border-color: #ffc000;
}
.btn-item:hover,
.btn-item:focus,
.btn-item:active,
.btn-item.active,
.open .dropdown-toggle.btn-item {
  color: #ffffff;
  background-color: #f2c02c;
  border-color: #ffc000;
}

.btn-item:active,
.btn-item.active,
.open .dropdown-toggle.btn-item {
  background-image: none;
}

.btn-item.disabled,
.btn-item[disabled],
fieldset[disabled] .btn-item,
.btn-item.disabled:hover,
.btn-item[disabled]:hover,
fieldset[disabled] .btn-item:hover,
.btn-item.disabled:focus,
.btn-item[disabled]:focus,
fieldset[disabled] .btn-item:focus,
.btn-item.disabled:active,
.btn-item[disabled]:active,
fieldset[disabled] .btn-item:active,
.btn-item.disabled.active,
.btn-item[disabled].active,
fieldset[disabled] .btn-item.active {
  background-color: #ffc000;
  border-color: #ffc000;
}

.btn-item .badge {
  color: #ffc000;
  background-color: #ffffff;
}

.btn-cinematic {
  background-color: #7b7b7b;
  border-color: #7b7b7b;
}
.btn-cinematic:hover,
.btn-cinematic:focus,
.btn-cinematic:active,
.btn-cinematic.active,
.open .dropdown-toggle.btn-cinematic {
  color: #ffffff;
  background-color: #787878;
  border-color: #7b7b7b;
}

.btn-cinematic:active,
.btn-cinematic.active,
.open .dropdown-toggle.btn-cinematic {
  background-image: none;
}

.btn-cinematic.disabled,
.btn-cinematic[disabled],
fieldset[disabled] .btn-cinematic,
.btn-cinematic.disabled:hover,
.btn-cinematic[disabled]:hover,
fieldset[disabled] .btn-cinematic:hover,
.btn-cinematic.disabled:focus,
.btn-cinematic[disabled]:focus,
fieldset[disabled] .btn-cinematic:focus,
.btn-cinematic.disabled:active,
.btn-cinematic[disabled]:active,
fieldset[disabled] .btn-cinematic:active,
.btn-cinematic.disabled.active,
.btn-cinematic[disabled].active,
fieldset[disabled] .btn-cinematic.active {
  background-color: #7b7b7b;
  border-color: #7b7b7b;
}

.btn-cinematic .badge {
  color: #7b7b7b;
  background-color: #ffffff;
}

.btn-loop {
  color: #ffffff;
  background-color: #2c2c2c;
}

.marker-b {
  margin-left: 25px !important;
}

.nu-icon {
  font-size: 31px;
  color: #8f8686;
  margin-left: 47px;
}

.center {
  margin: auto;
}

.margIcon {
  margin: auto;
}

.component_label {
  display:flex; 
  flex-direction: row; 
  justify-content:space-between; 
  align-items:center
}

.component_key {
  font-weight: bold;
  font-size: 150%;
  float: right;
  margin-right: 70px;
  display: flex;
  align-items: flex-end;
  align-content: flex-end;
}
