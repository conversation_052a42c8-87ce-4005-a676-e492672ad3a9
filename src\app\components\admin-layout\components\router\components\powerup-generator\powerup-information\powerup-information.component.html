<div class="content-weapon">
  <div class="card" style="margin-top: 20px;">
    <app-header-with-buttons 
      [cardTitle]="title" 
      [cardDescription]="description"
      [rightButtonTemplates]="[excelButtonTemplate]"
      [isBackButtonEnabled]="false">
    </app-header-with-buttons>
  </div>
  <br />
  <div class="content">
    <div *ngIf="activeTab === 'Ingredients'">
      <table class="table table-list">
        <thead class="sticky">
          <tr>
            <th class="th-clickable" (click)="sortListByName()">Power-up</th>
            <th class="th-clickable" (click)="sortPowerupListByNumber('labLevel')">LAB Level (GATING.LAB)</th>
            <th class="th-clickable" (click)="sortPowerupListByNumber('souls')">Souls Cost</th>
            <th class="th-clickable" (click)="sortPowerupListByNumber('time')">Time to create (min)</th>
            <th class="th-clickable" (click)="sortPowerupListByNumber('rubies')">Skip Price </th>
            <th *ngFor="let particleId of this.custom?.particlesOrder; let i = index"
                class="th-clickable" (click)="sortListByParticleIndex(i)">
              <select 
                class="dropdown filter-dropdown limited"
                style="display: inline-block; margin: 5px;" #pp
                (change)="changeParticleOrder(pp.value, i)"
                (click)="$event.stopPropagation();">
                <option 
                  *ngFor="let p of particles"
                  [selected]="p.id === particleId" value="{{ p.id }}">{{
                  GetParticleName(p.id) }}</option>
              </select>
            </th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let powerup of powerups">
            <tr id="{{ powerup.id }}">
              <td class="td-id powerup-title">{{ GetPowerupName(powerup.id) }}
              </td>
              <td class="td-id">
                <input 
                  type="number" #InputLabLevel
                  style="border-style:solid;"
                  [value]="powerup.labLevel"
                  [ngClass]="{'empty-input': !InputLabLevel.value, 'full-input': InputLabLevel.value}"
                  (change)="changePowerUpValue(powerup, InputLabLevel.value, 'labLevel')" />
              </td>
              <td class="td-id">
                <input type="number" #InputSouls
                  style="border-style:solid;"
                  [value]="powerup.souls"
                  [ngClass]="{'empty-input': !InputSouls.value}"
                  (change)="changePowerUpValue(powerup, InputSouls.value, 'souls')" />
              </td>
              <td class="td-id">
                <input type="number" #InputTime
                  style="border-style:solid;"
                  [value]="powerup.time"
                  [ngClass]="{'empty-input': !InputTime.value}"
                  (change)="changePowerUpValue(powerup, InputTime.value, 'time')" />
              </td>
              <td class="td-id">
                <input type="number" #InputSkipPrice
                  style="border-style:solid;"
                  [value]="powerup.rubies"
                  [ngClass]="{'empty-input': !InputSkipPrice.value}"
                  (change)="changePowerUpValue(powerup, InputSkipPrice.value, 'rubies')" />
              </td>
              <td class="td-id"
                  *ngFor="let particleId of this.custom.particlesOrder; let i = index">
                  <input type="number" #InputParticle
                    style="border-style:solid;"
                    [value]="GetParticleValue(powerup, i)"
                    [ngClass]="{'empty-input': !InputParticle.value}"
                    (change)="changeParticleValue(powerup, InputParticle.value, i)" />
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>

    <div *ngIf="activeTab === 'statistics'">
      <table class="table table-list">
        <thead class="sticky">
          <tr>
            <th rowspan="3" class="th-clickable" (click)="sortStatsByName()">Power-up</th>
            <th rowspan="3" class="th-clickable"
                (click)="sortPowerupStatListByNumber('duration')">Effect Duration (sec)</th>
            <th colspan="4"
                style="background-color: rgb(73, 136, 179); color: white;">Partner</th>
            <th colspan="3"
                style="background-color: rgb(194, 88, 88); color: white;">Enemy</th>
            <th colspan="2"
                style="background-color: rgb(73, 136, 179); color: white;">Partner</th>
            <th colspan="7"
                style="background-color: rgb(194, 88, 88); color: white;">Enemy</th>
          </tr>
          <tr>
            <th class="border-header">+ HP</th>
            <th class="border-header">+ HP</th>
            <th class="border-header">+ ATK</th>
            <th class="border-header">+ DEF</th>
            <th class="border-header">- HP</th>
            <th class="border-header">- ATK</th>
            <th class="border-header">- DEF</th>
            <th class="border-header">+ QI</th>
            <th class="border-header">+ LUK</th>
            <th class="border-header">- QI</th>
            <th class="border-header">- LUK</th>
            <th class="border-header" colspan="2">- AGG</th>
            <th class="border-header">AGG = 0</th>
            <th class="border-header" colspan="2">DMG = 0</th>
          </tr>
          <tr>
            <th class="th-clickable" (click)="sortPowerupStatListByNumber('partnerRegen')">Regen HP</th>
            <th class="th-clickable" (click)="sortPowerupStatListByNumber('partnerHP')">Boost HP</th>
            <th class="th-clickable" (click)="sortPowerupStatListByNumber('partnerATK')">Boost ATK</th>
            <th class="th-clickable" (click)="sortPowerupStatListByNumber('partnerDEF')">Boost DEF</th>
            <th class="th-clickable" (click)="sortPowerupStatListByNumber('enemyHP')">Blow HP</th>
            <th class="th-clickable" (click)="sortPowerupStatListByNumber('enemyATK')">Blow ATK</th>
            <th class="th-clickable" (click)="sortPowerupStatListByNumber('enemyDEF')">Blow DEF</th>
            <th class="th-clickable" (click)="sortPowerupStatListByNumber('partnerQI')">Boost QI</th>
            <th class="th-clickable" (click)="sortPowerupStatListByNumber('partnerLuck')">Boost Luck</th>
            <th class="th-clickable" (click)="sortPowerupStatListByNumber('enemyQI')">Blow QI</th>
            <th class="th-clickable" (click)="sortPowerupStatListByNumber('enemyLuck')">Blow Luck</th>
            <th class="th-clickable" (click)="sortPowerupStatListByNumber('enemyIntimidate')">Intimidate</th>
            <th class="th-clickable" (click)="sortPowerupStatListByNumber('enemySoothe')">Soothe</th>
            <th class="th-clickable" (click)="sortPowerupStatListByNumber('enemyInvisibility')">Invisibility</th>
            <th class="th-clickable" (click)="sortPowerupStatListByNumber('enemyInvulnerability')">Invulnerability</th>
            <th class="th-clickable" (click)="sortPowerupStatListByNumber('enemySlowDown')">Slow Down</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let powerupStat of powerupsStats">
            <tr id="{{ powerupStat.id }}">
              <td class="td-id">{{ GetPowerupStatName(powerupStat.id) }}</td>
              <td class="td-id">
                <input type="number" #InputDuration
                  style="border-style:solid;"
                  [value]="powerupStat.duration"
                  [ngClass]="{'empty-input': !InputDuration.value}"
                  (change)="changePowerUpStatValue(powerupStat, InputDuration.value, 'duration')" />
              </td>
              <td class="td-id">
                <input type="number" #InputPartnerRegen
                  style="border-style:solid;"
                  [value]="powerupStat.partnerRegen"
                  [ngClass]="{'empty-input': !InputPartnerRegen.value}"
                  (change)="changePowerUpStatValue(powerupStat, InputPartnerRegen.value, 'partnerRegen')"  />
              </td>
              <td class="td-id">
                <input type="number" #InputPartnerBoostHP
                  style="border-style:solid;"
                  [value]="powerupStat.partnerHP"
                  [ngClass]="{'empty-input': !InputPartnerBoostHP.value}"
                  (change)="changePowerUpStatValue(powerupStat, InputPartnerBoostHP.value, 'partnerHP')"  />
              </td>
              <td class="td-id">
                <input type="number" #InputPartnerBoostATK
                  style="border-style:solid;"
                  [value]="powerupStat.partnerATK"
                  [ngClass]="{'empty-input': !InputPartnerBoostATK.value}"
                  (change)="changePowerUpStatValue(powerupStat, InputPartnerBoostATK.value, 'partnerATK')" />
              </td>
              <td class="td-id">
                <input type="number" #InputPartnerBoostDEF
                  style="border-style:solid;"
                  [value]="powerupStat.partnerDEF"
                  [ngClass]="{'empty-input': !InputPartnerBoostDEF.value}"
                  (change)="changePowerUpStatValue(powerupStat, InputPartnerBoostDEF.value, 'partnerDEF')"/>
              </td>
              <td class="td-id">
                <input type="number" #InputPartnerBlowHP
                  style="border-style:solid;"
                  [value]="powerupStat.enemyHP"
                  [ngClass]="{'empty-input': !InputPartnerBlowHP.value}"
                  (change)="changePowerUpStatValue(powerupStat, InputPartnerBlowHP.value, 'enemyHP')" />
              </td>
              <td class="td-id">
                <input type="number" #InputPartnerBlowATK
                  style="border-style:solid;"
                  [value]="powerupStat.enemyATK"
                  [ngClass]="{'empty-input': !InputPartnerBlowATK.value}"
                  (change)="changePowerUpStatValue(powerupStat, InputPartnerBlowATK.value, 'enemyATK')" />
              </td>
              <td class="td-id">
                <input type="number" #InputPartnerBlowDEF
                  style="border-style:solid;"
                  [value]="powerupStat.enemyDEF"
                  [ngClass]="{'empty-input': !InputPartnerBlowDEF.value}"
                  (change)="changePowerUpStatValue(powerupStat, InputPartnerBlowDEF.value, 'enemyDEF')"  />
              </td>
              <td class="td-id">
                <input type="number" #InputPartnerBoostQI
                  style="border-style:solid;"
                  [value]="powerupStat.partnerQI"
                  [ngClass]="{'empty-input': !InputPartnerBoostQI.value}"
                  (change)="changePowerUpStatValue(powerupStat, InputPartnerBoostQI.value, 'partnerQI')"  />
              </td>
              <td class="td-id">
                <input type="number" #InputPartnerBoostLuck
                  style="border-style:solid;"
                  [value]="powerupStat.partnerLuck"
                  [ngClass]="{'empty-input': !InputPartnerBoostLuck.value}"
                  (change)="changePowerUpStatValue(powerupStat, InputPartnerBoostLuck.value, 'partnerLuck')"   />
              </td>
              <td class="td-id">
                <input type="number" #InputPartnerBlowQI
                  style="border-style:solid;"
                  [value]="powerupStat.enemyQI"
                  [ngClass]="{'empty-input': !InputPartnerBlowQI.value}"
                  (change)="changePowerUpStatValue(powerupStat, InputPartnerBlowQI.value, 'enemyQI')"  />
              </td>
              <td class="td-id">
                <input type="number" #InputPartnerBlowLuck
                  style="border-style:solid;"
                  [value]="powerupStat.enemyLuck"
                  [ngClass]="{'empty-input': !InputPartnerBlowLuck.value}"
                  (change)="changePowerUpStatValue(powerupStat, InputPartnerBlowLuck.value, 'enemyLuck')"  />
              </td>
              <td class="td-id">
                <input type="number" #InputPartnerIntimidate
                  style="border-style:solid;"
                  [value]="powerupStat.enemyIntimidate"
                  [ngClass]="{'empty-input': !InputPartnerIntimidate.value}"
                  (change)="changePowerUpStatValue(powerupStat, InputPartnerIntimidate.value, 'enemyIntimidate')"  />
              </td>
              <td class="td-id">
                <input type="number" #InputPartnerSmooth
                  style="border-style:solid;"
                  [value]="powerupStat.enemySoothe"
                  [ngClass]="{'empty-input': !InputPartnerSmooth.value}"
                  (change)="changePowerUpStatValue(powerupStat, InputPartnerSmooth.value, 'enemySoothe')" />
              </td>
              <td class="td-id">
                <input type="number" #InputPartnerInvisibility
                  style="border-style:solid;"
                  [value]="powerupStat.enemyInvisibility"
                  [ngClass]="{'empty-input': !InputPartnerInvisibility.value}"
                  (change)="changePowerUpStatValue(powerupStat, InputPartnerInvisibility.value, 'enemyInvisibility')"  />
              </td>
              <td class="td-id">
                <input type="number" #InputPartnerInvulnerability
                  style="border-style:solid;"
                  [value]="powerupStat.enemyInvulnerability"
                  [ngClass]="{'empty-input': !InputPartnerInvulnerability.value}"
                  (change)="changePowerUpStatValue(powerupStat, InputPartnerInvulnerability.value, 'enemyInvulnerability')" />
              </td>
              <td class="td-id">
                <input type="number" #InputPartnerSlowDown
                  style="border-style:solid;"
                  [value]="powerupStat.enemySlowDown"
                  [ngClass]="{'empty-input': !InputPartnerSlowDown.value}"
                  (change)="changePowerUpStatValue(powerupStat, InputPartnerSlowDown.value, 'enemySlowDown')"  />
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>

  </div>

</div>