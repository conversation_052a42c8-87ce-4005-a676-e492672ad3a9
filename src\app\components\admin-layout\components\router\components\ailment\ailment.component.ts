import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Ailment } from 'src/app/lib/@bus-tier/models/Ailment';
import { Button } from 'src/app/lib/@pres-tier/data';
import { UserSettingsService } from 'src/app/services';
import { AilmentService } from 'src/app/services/ailment.service';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationCheckService } from 'src/app/services/translation-check.service';
import { TranslationService } from 'src/app/services/translation.service';
import { Alert } from 'src/lib/darkcloud';
import { FILTER_SUFFIX_PATH } from 'src/lib/darkcloud/angular/dsadmin/constants/others';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';
import { language } from 'src/lib/darkcloud/dialogue-system/game-types';

@Component({
  selector: 'app-ailment',
  templateUrl: './ailment.component.html',
  styleUrls: ['./ailment.component.scss']
})
export class AilmentComponent extends TranslatableListComponent<Ailment> 
{
  constructor(
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    private _ailmentService: AilmentService,
    protected override _translationService: TranslationService,
    protected override _languageService: LanguageService,
    private _translationCheckService: TranslationCheckService,

  ) 
  {
    super(_ailmentService, _activatedRoute, _userSettingsService, 'name', _translationService, _languageService);
  }

  public language: language = 'PT-BR';
  public ailmentList: Ailment[] = [];
  public ailmentSearch: Ailment[] = [];
  description:string = '';
  activeTab: string;
  isTab = false;
   btnSubContext: string = FILTER_SUFFIX_PATH;
 
  public readonly statusTemplate: Button.Templateable = 
  {
    title: 'Add a new instance to the list',
    onClick: this.addStatus.bind(this),
    iconClass: 'pe-7s-plus',
    btnClass: Button.Klasses.FILL_GREEN,
  };
  
  override async ngOnInit ()
  {
    await this._ailmentService.toFinishLoading();
    this.ailmentList = this._ailmentService.models;
    this.sortByAilment();   
    this.description = `Showing ${this.ailmentList.length} results`;    
  }

  async addStatus()
  {
    let ailment;

    try 
    {
      ailment = await this._ailmentService.svcPromptCreateNew();
    } 
    catch (e) 
    {
      Alert.showError("This Ailment already exists!");
      return
    }
    if(!ailment) return;
    this.ngOnInit();
   
  }

  async removeElement(status : Ailment)
  {
    const confirm = await Alert.showRemoveAlert(status.ailment);  
    if (!confirm) return;
    
    this._ailmentService.models = await this._ailmentService.models.filter(s => s.id !== status.id);
    await this._ailmentService.toSave();
    this.ailmentList = this.ailmentList.filter(s => s !== status);
    this.ngOnInit();
  }

  public getModifierOrtography(status: Ailment)
  {
    this._translationService.getAilmentOrtography(status, true);
  }

  sortBySkillOrder = -1;
  sortByAilment() 
  {
    this.sortBySkillOrder *= -1;
    this.ailmentList.sort((a, b) => 
    {
        return this.sortBySkillOrder *  a.ailment.localeCompare(b.ailment);
    });
    this.lstFetchLists();
  }
 
  sortByAcronymOrder = -1;
  sortByAcronym() 
  {
    this.sortByAcronymOrder *= -1;
    this.ailmentList.sort((a, b) => 
    {
        return this.sortByAcronymOrder *  a?.acronym.localeCompare(b?.acronym);
    });
    this.lstFetchLists();
  }

  search(searchWord:string) {
    searchWord =  searchWord.toLowerCase();    
    this.ailmentSearch = this.ailmentList;

    if(searchWord == '') return this.ngOnInit();

    this.ailmentList = []
      
    for (let index = 0; index < this.ailmentSearch.length; index++) {
      if (this.ailmentSearch[index]?.ailment?.toLowerCase().includes(searchWord) || 
      this.ailmentSearch[index]?.acronym?.toLowerCase().includes(searchWord) ||
      this.ailmentSearch[index]?.description?.toLowerCase().includes(searchWord)) {
        this.ailmentList.push(this.ailmentSearch[index]);
      }      
    }   
    this.description = `Showing ${this.ailmentList.length} results`;
    return this.ailmentList;
  }

  defenseChange(ailment: Ailment, fieldAilment: string, value: string) {
    ailment.isReviewedAilment = false;
    ailment.revisionCounterAilmentAI = 0;
    this.lstOnChange(ailment, 'ailment', value);
  }

  descriptionChange(ailment: Ailment, description: string, value: string) {
    ailment.isReviewedDescription = false;
    ailment.revisionCounterDescriptionAI = 0;
    this.lstOnChange(ailment, 'description', value);
  }

    clickBtn(isActive: boolean) {
    if (isActive) {
      this.activeTab = 'Healing Change';
      this.isTab = true;
    }
    localStorage.setItem(`tab-AilmentComponent${FILTER_SUFFIX_PATH}`, this.activeTab);
  }

    clickBtnAilmentHandler(event: boolean): void {
      if (event) {
        this.activeTab = 'keywords';
        this.isTab = false;
        // Atualizar o armazenamento local para persistir o estado
        localStorage.setItem(`tab-AilmentComponent${FILTER_SUFFIX_PATH}`, this.activeTab);
      }
    }
}
