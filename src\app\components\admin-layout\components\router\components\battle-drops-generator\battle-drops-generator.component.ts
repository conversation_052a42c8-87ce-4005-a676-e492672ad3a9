import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { UserSettingsService } from 'src/app/services';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { FILTER_SUFFIX_PATH } from 'src/lib/darkcloud/angular/dsadmin/constants/others';


@Component({
  selector: 'app-battle-drops-generator',
  templateUrl: './battle-drops-generator.component.html',

})
export class BattleDropsGeneratorComponent implements OnInit
{

  constructor(
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    protected _translationService: TranslationService,
    protected _languageService: LanguageService
  ) {}

  public reviewOrderAscending: boolean = false;
  public activeTab: string;
  public activeTab2: string;
  public activeTab3: string;
  isSelectAreaDrop = false;

  ngOnInit(): void 
  {
    const tab = localStorage.getItem(`tab-BattleDropsGeneratorComponent${FILTER_SUFFIX_PATH}`);
    const tab2 = localStorage.getItem(`tab-BattleDropsGeneratorComponent2${FILTER_SUFFIX_PATH}`);
    const tab3 = localStorage.getItem(`tab-BattleDropsGeneratorComponent3${FILTER_SUFFIX_PATH}`);
    this.activeTab = tab === 'null' || !tab ? 'necromodsDrops' : tab;      

    if(this.activeTab === 'necromodsDrops') {
      this.activeTab2 = 'codeBlockDrops';
    } else {
      this.activeTab3 = 'particleDrop';
    }

}

  public switchToTab(tab: string) 
  {
    if(tab === 'slot0') {
      this.activeTab2 = 'drops';
      this.isSelectAreaDrop = true;
    }

    this.activeTab = tab;
    localStorage.setItem(`tab-BattleDropsGeneratorComponent${FILTER_SUFFIX_PATH}`, this.activeTab);
  }
  public switchToTab2(tab: string) 
  {

    if(tab === 'drops') {
      this.isSelectAreaDrop = true;
      }

    this.activeTab2 = tab;
    localStorage.setItem(`tab-BattleDropsGeneratorComponent2${FILTER_SUFFIX_PATH}`, this.activeTab2);
  }
  public switchToTab3(tab: string) 
  {
    this.activeTab3 = tab;
    localStorage.setItem(`tab-BattleDropsGeneratorComponent3${FILTER_SUFFIX_PATH}`, this.activeTab3);
  }
}
