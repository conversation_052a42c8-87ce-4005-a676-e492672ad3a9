.roadblock-info-container {
  position: relative;
  margin-left: 10px;
  margin-right: 10px; // More space after the branch line
  z-index: 10;
  pointer-events: auto; // Allow interaction with the roadblock info
  display: inline-block; // Ensure proper block behavior
}

.roadblock-info-box {
  width: 200px; // Slightly wider for better readability
  padding: 8px 10px;
  margin: 5px 0; // Remove horizontal margin to prevent overlap
  border: 2px solid #ff6b35;
  border-radius: 6px;
  background-color: white;
  color: #d84315;
  font-size: 12px; // Slightly larger font for better readability
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  line-height: 1.4;
  min-height: 50px; // Ensure consistent height
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.roadblock-type {
  font-weight: bold;
  font-size: 13px;
  margin-bottom: 3px;
  color: black;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.roadblock-details {
  font-size: 11px;
  margin-bottom: 3px;
  word-wrap: break-word;
  color: #565656;
}

.roadblock-id {
  font-size: 9px;
  color: #9a9a9a;
  font-style: italic;
  margin-top: 2px;
}

// Multiple roadblocks styling
.roadblock-multiple-header {
  font-weight: bold;
  font-size: 14px;
  margin-bottom: 8px;
  color: black;
  text-align: center;
  border-bottom: 1px solid #ff6b35;
  padding-bottom: 4px;
}

.roadblock-item {
  margin-bottom: 6px;

  &:last-child {
    margin-bottom: 0;
  }
}

// Separator container with And/Or overlay
.roadblock-separator-container {
  position: relative;
  height: 30px;
  display: flex;
  align-items: center;
}

.roadblock-separator {
  height: 1px;
  background-color: #ffcc80;
  opacity: 0.6;
  width: 100%;
}

// Lock icon styling for Spoke In roadblocks
.roadblock-lock-container {
  position: absolute;
  left: -15px;
  top: 45%;
  transform: translateY(-50%);
  width: 40px;
  height: 30px;
  border-radius: 0 15px 15px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 5;
  box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.roadblock-lock-icon {
  font-size: 19px;
  font-weight: bold;
}

// And/Or condition styling - positioned on top of separator
.roadblock-and-or-condition {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
}

.and-or-text {
  font-weight: 900;
  font-size: 14px;
  padding: 3px 10px;
  border-radius: 10px;
  border: 2px solid;
  background-color: white;
  text-align: center;
  min-width: 45px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  line-height: 1.2;
}

.and-or-text.and-active {
  color: #ff00aa;
  border-color: #ff00aa;
  background-color: #fff0f8;
}

.and-or-text.or-active {
  color: #0066ff;
  border-color: #0066ff;
  background-color: #f0f6ff;
}
