.window {
  position: relative;
  box-sizing: border-box;
  flex-direction: column;
  justify-content: center;
  max-width: 100%;
  padding: 1.25em;
  border: none;
  border-radius: 0.3125em;
  background: #fff;
  font-family: inherit;
  font-size: 1rem;
}

.actions-column,
.actions-default,
.actions-row {
  display: grid;
  z-index: 1;
  flex-wrap: nowrap;
  justify-content: center;
  padding: 10px;
}
.actions-default {
  align-items: center;
}
.actions-column {
  flex-direction: column;
}
.actions-row {
  flex-direction: row;
}

.btn {
  border-width: 1px !important;
  border-color: #fff !important;
}

.btn-main {
  min-width: 200px;
  width: 100%;
}
.icon	
{	
    font-size: 30px;	
    color: #999;	
    position: relative;	
    transition: transform 0.3s;	
    transform-origin: center;	
}	
.icon:hover	
{	
    font-size: 30px;	
    color: #555;	
    transform: translate(-2.5%) rotate(180deg);	
}	
.i-btn	
{	
    width: 30px;	
    height: 30px;	
}
.btn-undefined {
  background-color: transparent !important;
  border-color: #888 !important;
  color: #888 !important;
  margin-bottom: 1.1px;
  margin-top: 1.1px;
  text-transform: capitalize;
}

.icon-refresh-text {
  font-size: 70px !important;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.content-refresh {
  margin: 10px;
  max-height: 80vh;
  align-self: center;
}