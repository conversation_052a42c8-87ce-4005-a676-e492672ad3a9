<div class="card list-header" style="height: 70px; margin: 30px; margin-bottom: 0px;">
    <div class="header" style="display:flex; flex-direction:row; justify-content: space-between;">
        <div style="display:flex; flex-direction:row; justify-content: space-between; gap: 5px;">
            <button routerLink="/charactersSelector"
                class="{{this.inThisPage ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"> Characters Selection
            </button>
            <button routerLink="/animationsSelector"
                class="{{!this.inThisPage ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"> Animations Selection
            </button>
            <button routerLink="/itemsSelector"
                class="{{!this.inThisPage ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"> Items Selection </button>
        </div>
    </div>
</div>

<div class="main-content">
    <ng-container *ngIf="this._areaService.models.length">
        <div style="margin-top: 25px;" class="card">
            <app-header-with-buttons [cardTitle]="'Selection Section'" [cardDescription]="description"
                [rightButtonTemplates]="[excelButtonTemplate, exportExcelButtonTemplate]" [isBackButtonEnabled]="false">
            </app-header-with-buttons>
            <app-header-search (inputKeyup)="search($event)"
                (searchOptions)="searchConditions($event)"></app-header-search>
        </div>
        <div class="card horizontal-scroll">
            <table class="table table-list card">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th class="th-clickable" (click)="sortByName('character')">Name</th>
                        <th class="th-clickable" (click)="sortByName('description')">Notes</th>
                        <th class="th-clickable" (click)="sortElements('concept')">Concept</th>
                        <th class="th-clickable" (click)="sortElements('separation')">
                            <div style="display:flex; flex-direction:column;">
                                <div>Separation</div>
                                <div>(pre-finishing)</div>
                            </div>
                        </th>
                        <th class="th-clickable" (click)="sortElements('preFinish')">PreFinish</th>
                        <th class="th-clickable" (click)="sortElements('finish')">Finish</th>
                        <th class="th-clickable" (click)="sortElements('postFinish')">
                            <div style="display:flex; flex-direction:column;">
                                <div>Separation</div>
                                <div>(post-finishing)</div>
                            </div>
                        </th>
                        <th class="th-clickable" (click)="sortElements('rev1')">REV1</th>
                        <th class="th-clickable" (click)="sortElements('rev2')">REV2</th>
                        <th>Areas
                            <select #areas (change)="onChangeArea(areas.value)"
                                class="dropdown filter-dropdown limited center">
                                <option default value="All">All</option>
                                <option *ngFor="let area of this._areaService.models" [value]="area.name">
                                    {{area.hierarchyCode + " : " + area.name}}</option>
                            </select>
                        </th>
                    </tr>
                </thead>

                <tbody>
                    <ng-container *ngFor="let charSelector of charactersSelectors; let i = index">
                        <tr *ngIf="charactersSelectors">
                            <td>
                                {{i}}
                            </td>
                            <td>
                                {{charSelector.character}}
                            </td>
                            <td>
                                <textarea placeholder=" " class="form-control" type="text" #notes
                                    (change)="onChangeNotes(charSelector, notes.value)"
                                    value="{{ charSelector.description }}"></textarea>
                            </td>
                            <td>
                                <input type="checkbox" [checked]="charSelector.concept"
                                    (click)="onChangeCheckbox(charSelector, 'concept')">
                            </td>
                            <td>
                                <input type="checkbox" [checked]="charSelector.separation"
                                    (click)="onChangeCheckbox(charSelector, 'separation')">
                            </td>
                            <td>
                                <input type="checkbox" [checked]="charSelector.preFinish"
                                    (click)="onChangeCheckbox(charSelector, 'preFinish')">
                            </td>
                            <td>
                                <input type="checkbox" [checked]="charSelector.finish"
                                    (click)="onChangeCheckbox(charSelector, 'finish')">
                            </td>
                            <td>
                                <input type="checkbox" [checked]="charSelector?.postFinish"
                                    (click)="onChangeCheckbox(charSelector, 'postFinish')">
                            </td>
                            <td>
                                <input type="checkbox" class="inputt" [checked]="charSelector?.rev1"
                                    (click)="onChangeCheckbox(charSelector, 'rev1')">
                            </td>
                            <td>
                                <input type="checkbox" class="inputt" [checked]="charSelector?.rev2"
                                    (click)="onChangeCheckbox(charSelector, 'rev2')">
                            </td>
                            <td>
                                {{(charSelector.characterArea | area)?.name}}
                            </td>
                        </tr>
                    </ng-container>
                </tbody>
            </table>
        </div>

        <!-- Spinner-->
        <ng-container *ngIf="loadingSpinner">
            <app-loading-spinner></app-loading-spinner>
        </ng-container>

    </ng-container>


    <ng-container *ngIf="!this._areaService.models.length">
        <div class="noCharacters">
            <p style="font-weight: 800;">
                <i class="pe-7s-attention"></i>
                No Characters Selection
            </p>
            <p>Check if I import the database correctly.</p>
        </div>
    </ng-container>

</div>