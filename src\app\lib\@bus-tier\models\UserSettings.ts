import { Data } from 'src/lib/darkcloud/angular/dsadmin';

export class UserSettings implements Data.Hard.IUserSettings {
  public icons: { id: string; name: string }[] = [];
  public objectInformations: { [id: string]: Data.Internal.Base } = {};
  constructor(hard?: Data.Hard.IUserSettings) {
    if (hard != undefined) {
      this.icons = hard.icons;
      this.objectInformations = hard.objectInformations;
    }
  }
}
