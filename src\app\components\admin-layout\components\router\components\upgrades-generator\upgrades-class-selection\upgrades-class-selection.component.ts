import { Component, OnInit } from '@angular/core';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';
import { ItemClass } from 'src/app/lib/@bus-tier/models/ItemClass';
import { CustomService } from 'src/app/services/custom.service';
import { ItemClassService } from 'src/app/services/item-class.service';

@Component({
  selector: 'app-upgrades-class-selection',
  templateUrl: './upgrades-class-selection.component.html',
  styleUrls: ['./upgrades-class-selection.component.scss'],
})
export class UpgradesClassSelectionComponent implements OnInit 
{

  itemClasses: ItemClass[];
  custom: Custom;

  constructor(
    private _itemClassService: ItemClassService,
    private _customService: CustomService,
  ) {

  }

  async ngOnInit(): Promise<void>
  {
    this.itemClasses = this._itemClassService.models;
    this.custom = await this._customService.svcGetInstance();
    if(!this.custom.upgradesClassItem)
    {
      this.custom.upgradesClassItem = [];
    }

    // Auto-select default item classes for upgrades if none are selected
    await this.autoSelectDefaultClasses();
  }

  private async autoSelectDefaultClasses() {
    // Only auto-select if no classes are currently selected
    if (this.custom.upgradesClassItem.length === 0) {
      // Look for common upgrade-related item classes
      const upgradeClasses = [
        "ARMAS COMUNS",      // Common weapons that can be upgraded
        "ARMAS ESPECIAIS",   // Special weapons that can be upgraded
        "BLUEPRINTS",        // Blueprints that might need upgrades
        "UPGRADES"           // Direct upgrade items if they exist
      ];

      let needsUpdate = false;

      // Try to find and auto-select relevant classes
      upgradeClasses.forEach(className => {
        const itemClass = this._itemClassService.models.find((x) => x.name === className);
        if (itemClass && !this.custom.upgradesClassItem.includes(itemClass.id)) {
          this.custom.upgradesClassItem.push(itemClass.id);
          needsUpdate = true;
        }
      });

      // If no specific upgrade classes found, select the first available class as fallback
      if (this.custom.upgradesClassItem.length === 0 && this.itemClasses.length > 0) {
        this.custom.upgradesClassItem.push(this.itemClasses[0].id);
        needsUpdate = true;
      }

      // Save changes if any were made
      if (needsUpdate) {
        this._customService.svcToModify(this.custom);
        this._customService.toSave();
      }
    }
  }

  async onItemClassSelected(itemClass: ItemClass)
  {
    this._customService.toggleItemClass(itemClass, 'upgradesClassItem');
    this.custom = await this._customService.svcGetInstance();
  }

}
