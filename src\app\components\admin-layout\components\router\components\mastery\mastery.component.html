<div class="main-content">
  <div class="container-fluid">
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons 
          [cardTitle]="listName"
          [cardDescription]="description"
          [rightButtonTemplates]="[excelButtonTemplate]">
        </app-header-with-buttons>
      </div>
    </div>

    <div class="card">
      <table class="table table-list">
        <thead>
          <tr>
            <th colspan="12" class="dark-gray" ><h4>Mastery</h4></th>
          </tr>
          <tr>
            <th rowspan="2" style="width:5%; padding-left: 7px;" class="dark-gray">Index</th>
            <th rowspan="2" style="width:10%; min-width: 96px;" class="dark-gray">Rarity (RW)</th>
            <th colspan="2" class="dark-gray">Slot A</th>
            <th colspan="2" class="dark-gray">Slot B</th>
            <th colspan="2" class="dark-gray">Slot C</th>
            <th rowspan="2" style="width:10%" class="dark-gray">Mastery Extremes</th>
          </tr>
          <tr>
            <th style="width:10%" class="dark-gray">Skill Dominated</th>
            <th style="width:10%" class="dark-gray">Multiplier</th>
            <th style="width:10%" class="dark-gray">Skill Dominated</th>
            <th style="width:10%" class="dark-gray">Multiplier</th>
            <th style="width:10%" class="dark-gray">Skill Dominated</th>
            <th style="width:10%" class="dark-gray">Multiplier</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let mastery of masteryList; let i = index">
            <tr>
              <td class="td-id indexColor">{{ i + 1 }}</td>
              <td [style.background-color]="mastery?.typeMasteryRarity | tierColor: 'Weapon Rarity'"
                  [ngStyle]="{ 'color':'white', 'font-weight': '500'}">
                  {{ mastery?.typeMasteryRarity }}
              </td>
              <td> 
                <input placeholder=" " style="padding-left: 10px;" type="text" [value]="mastery.slotA?.skillDominated" #inputFieldA_Skill [ngClass]="{'empty-input': !inputFieldA_Skill.value}"
                (change)="changeSlotASkillDominated(mastery, inputFieldA_Skill.value, 'slotA.skillDominated')" >
                </td>
              <td>
                <input placeholder=" " style="padding-left: 10px;" type="number" [value]="mastery.slotA?.multiplier" #inputFieldA_Mul [ngClass]="{'empty-input': !inputFieldA_Mul.value}"
                (change)="changeSlotASkillDominated(mastery, inputFieldA_Mul.value, 'slotA.multiplier')" >              
              </td>
              <td>
                <input placeholder=" " style="padding-left: 10px;" type="text" [value]="mastery.slotB?.skillDominated" #inputFieldB_Skill [ngClass]="{'empty-input': !inputFieldB_Skill.value}"
                (change)="changeSlotASkillDominated(mastery, inputFieldB_Skill.value, 'slotB.skillDominated')" >               
              </td>
              <td>
                <input placeholder=" " style="padding-left: 10px;" type="number" [value]="mastery.slotB?.multiplier" #inputFieldB_Multi [ngClass]="{'empty-input': !inputFieldB_Multi.value}"
                (change)="changeSlotASkillDominated(mastery, inputFieldB_Multi.value, 'slotB.multiplier')" >              
              </td>
              <td>
                <input placeholder=" " style="padding-left: 10px;" type="text" [value]="mastery.slotC?.skillDominated" #inputFieldC_Skill [ngClass]="{'empty-input': !inputFieldC_Skill.value}"
                (change)="changeSlotASkillDominated(mastery, inputFieldC_Skill.value, 'slotC.skillDominated')" >               
              </td>
              <td>
                <input placeholder=" " style="padding-left: 10px;" type="number" [value]="mastery.slotC?.multiplier" #inputFieldC_Mul [ngClass]="{'empty-input': !inputFieldC_Mul.value}"
                (change)="changeSlotASkillDominated(mastery, inputFieldC_Mul.value, 'slotC.multiplier')">              
              </td>          
              <td>
                <input placeholder=" " style="padding-left: 10px;" type="text" [value]="mastery.extremes" #inputFieldExt [ngClass]="{'empty-input': !inputFieldExt.value}"
                (change)="changeSlotASkillDominated(mastery, inputFieldExt.value, 'extremes')">                
              </td>
            </tr>
          </ng-container>
        </tbody>         
      </table>
    </div>
    
    
    </div>
    <!--  
        <div class='footer'>
      <div >Collectible does not have the STATUS</div>
      <div >Collectible has the STATUS</div>
    </div> 
    -->

</div>

