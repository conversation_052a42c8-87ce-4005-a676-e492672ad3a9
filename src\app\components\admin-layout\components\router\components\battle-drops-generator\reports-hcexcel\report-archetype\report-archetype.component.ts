import { Component } from '@angular/core';
import { Area, Character, Class, TotalArchetypes } from 'src/app/lib/@bus-tier/models';
import { ArchetypeList } from 'src/app/lib/@bus-tier/models/ArchetypeList';
import { AreaService, CharacterService, ClassService, TotalArchetypesService } from 'src/app/services';
import { ArchetypeListService } from 'src/app/services/archetypesList.service';

interface NewListItem {
  classId: string;
  nameClass: string;
  areaId: string;
  nameArchetype: string;
  order?: number;
}


@Component({
  selector: 'app-report-archetype',
  templateUrl: './report-archetype.component.html',
  styleUrls: ['./report-archetype.component.scss'],
})
export class ReportArchetypeComponent {
  orderAreaList = [];
  weaponRarityNames = [];
  listOrderArea: Area[];
  listArchetype: ArchetypeList[] = [];
  listClass: Class[] = [];
  listCharactres: Character[];
  totalListArchetypes: TotalArchetypes[] = [];
  sortNameOrder = +1;

  constructor(
    private _areaService: AreaService,
    private _archetypeListService: ArchetypeListService,
    private _classService: ClassService,
    private _characterService: CharacterService,
    private _totalArchetypesService: TotalArchetypesService
  ) {}

  async ngOnInit(): Promise<void> {
    // Aguarde o carregamento de todos os serviços
    this._areaService.toFinishLoading(),
      this._totalArchetypesService.toFinishLoading(),
      (this.listArchetype = this._archetypeListService.models);
    this.listClass = this._classService.models;
    this.listCharactres = this._characterService.models;

    this.listOrderArea = this._areaService.models.filter(
      (x) => x.order.toString() !== ''
    );

    setTimeout(() => {
      this.calculateQtdeAndOrderCicle();
    }, 100);
  }

  //Para a criação da tabela e sua lógica foi usado o conteúdo da tabela Character List (Menu Characters) e Class List (Menu Classes) fazendo o relacionamento entre eles
  calculateQtdeAndOrderCicle() {
    this._totalArchetypesService.models = [];
    this._totalArchetypesService.toSave();
    this.removeAreaDuplicate();
    this.lineupOrderHC();

    this.listArchetype.forEach((archetype) => {
      this._totalArchetypesService.createNewTotalArchetypes(archetype);
    });

    this.totalListArchetypes = this._totalArchetypesService.models;

    const newList = this.listCharactres
      .map((character) => {
        const matchingClass = this.listClass.find(
          (classItem) => classItem.id === character.classId
        );
        if (matchingClass) {
          return {
            classId: matchingClass.id,
            nameClass: matchingClass.name,
            areaId: character.areaId,
            nameArchetype: matchingClass.nameArchetype,
          } as NewListItem;
        } else {
          return null; // ou algum outro valor padrão
        }
      })
      .filter((item) => item !== null);

    // Depois comparar o areaId do array newList com o id do array this.listOrderArea, se for igual adicione um novo campo  chamado 'order' = order do this.listOrderArea
    for (let i = 0; i < newList.length; i++) {
      for (let j = 0; j < this.listOrderArea.length; j++) {
        if (newList[i].areaId == this.listOrderArea[j].id) {
          newList[i].order = this.listOrderArea[j].order;
          break;
        }
      }
    }

    const qtdeMap: { [key: string]: { qtde: number; order: number } } = {};

    // Passo 1: Contar os itens iguais em `newList` por `nameArchetype` e `order`
    newList.forEach((item) => {
      const key = `${item.nameArchetype}-${item.order}`;

      if (!qtdeMap[key]) {
        qtdeMap[key] = { qtde: 1, order: item.order };
      } else {
        qtdeMap[key].qtde++;
      }
    });

    // Passo 2: Adicionar os valores no `listCicleLevel` de `this.totalListArchetypes`
    this.totalListArchetypes.forEach((archetype) => {
      if (!archetype.listCicleLevel) {
        archetype.listCicleLevel = [];
      }

      // Adiciona a Area ao listCicleLevel
      this.orderAreaList.forEach((area) => {
        // Garante que não haja duplicatas antes de adicionar
        if (
          !archetype.listCicleLevel.some(
            (item) => item.orderCicle === area.order
          )
        ) {
          archetype.listCicleLevel.push({
            orderCicle: area.order,
            totalUniqueCharacteres: 0,
          });
        }
      });

      // Encontrar todas as correspondências de `nameArchetype`
      Object.keys(qtdeMap).forEach((key) => {
        const [nameArchetype, order] = key.split('-');

        if (archetype.nameArchetype === nameArchetype) {
          const { qtde, order: orderCicle } = qtdeMap[key];

          // Verificar se já existe no `listCicleLevel`
          const existingItem = archetype.listCicleLevel.find(
            (item) => item.orderCicle === orderCicle
          );

          if (existingItem) {
            existingItem.totalUniqueCharacteres += qtde;
          }
        }
      });

      this._totalArchetypesService.svcToModify(archetype);
    });

    this.orderListCicleLevel();
    this.calculateTotalLine();
  }

  /// Calcula o valor total de cada linha para gerar o valor TOTAL da coluna
  //Para cada elemento do array, ele acessa o campo listCicleLevel que é um array e calcula o valor total do campo totalUniqueCharacteres usando o método reduce().
  //O método reduce() é usado para calcular o valor total do campo totalUniqueCharacteres. Ele começa com um valor inicial de 0 e adiciona o valor de cada elemento do array listCicleLevel ao total.
  calculateTotalLine() {
    this.totalListArchetypes.forEach((archetype) => {
      const totalUniqueCharacteres = archetype.listCicleLevel.reduce(
        (acc, current) => acc + current.totalUniqueCharacteres,
        0
      );
      archetype.total_line = totalUniqueCharacteres;
      this._totalArchetypesService.svcToModify(archetype);
    });
    this.calculateTotalColumn();
  }

  /// Calcula o valor total de cada coluna para gerar o valor total de cada linha
  calculateTotalColumn() {
    const archeTypeTotal = [];
    let lineTotal = 0;

    this.totalListArchetypes.forEach((archetype) => {
      lineTotal += archetype?.total_line;
    });
    archeTypeTotal.push(lineTotal);

    for (let i = 0; i < 20; i++) {
      let total = 0;
      for (let j = 0; j < 6; j++) {
        total +=
          this.totalListArchetypes[j]?.listCicleLevel[i]
            ?.totalUniqueCharacteres;
      }

      archeTypeTotal.push(total);
    }
    const existArch = this.totalListArchetypes.find((arch) => arch.total_name === 'Total');

    if (existArch) {
      existArch.total_column = archeTypeTotal;
      this._totalArchetypesService.svcToModify(existArch);
    } else {
      const newArchetype =
        this._totalArchetypesService.createNewTotalAreasArchetypes();
      newArchetype.total_name = 'Total';
      newArchetype.total_column = archeTypeTotal;
      this._totalArchetypesService.svcToModify(newArchetype);
    }
  }

  removeAreaDuplicate() {
    //Remove order duplicados
    this.orderAreaList = this.listOrderArea.filter(
      (item, index, self) =>
        index === self.findIndex((t) => t.order === item.order)
    );
  }

  lineupOrderHC() {
    this.sortNameOrder *= +1;
    this.orderAreaList.sort((a, b) => {
      return this.sortNameOrder * a.order.localeCompare(b.order);
    });
  }

  orderListCicleLevel() {
    this.sortNameOrder *= +1;
    for (let index = 0; index < this.totalListArchetypes.length; index++) {
      this.totalListArchetypes[index].listCicleLevel.sort((a, b) => {
        return this.sortNameOrder * (a.orderCicle - b.orderCicle);
      });
    }
  }

  getColorNameArchetype(archetypeName: string): string {
    let color: string = '#383838';

    if (archetypeName == undefined) return color;

    for (let i = 0; i < this._archetypeListService.models.length; i++) {
      if (this._archetypeListService.models[i].name == archetypeName) {
        color = this._archetypeListService.models[i].color;
        break;
      }
    }
    return color;
  }
}
