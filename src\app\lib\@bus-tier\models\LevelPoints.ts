import { Data } from "src/lib/darkcloud/angular/dsadmin";
import { ListHC } from "src/lib/darkcloud/angular/dsadmin/v9/data/hard";
import { IdPrefixes } from "src/lib/darkcloud/dialogue-system";
import { Base } from "./Base";

export class LevelPoints extends Base<Data.Hard.ILevelPoints, Data.Result.ILevelPoints> implements Required<Data.Hard.ILevelPoints>
{
    static generateId(index: number): string
    {
        return IdPrefixes.LEVELPOINTS + index;
    }

    constructor(
      index: number,
      dataAccess: LevelPoints['TDataAccess']) 
    {
      super(
          {
              hard: 
              {
                  id: LevelPoints.generateId(index),
              },
          },
          dataAccess
      );
    }

    protected getInternalFetch() {
      return {};
     }
     
    public get listCicleLevel(): ListHC[]
    {
      return this.hard.listCicleLevel;
    }
    public set listCicleLevel(value: ListHC[])
    {
      this.hard.listCicleLevel = value;
    }

    public get nameTypeLevel(): string
    {
      return this.hard.nameTypeLevel;
    }
    public set nameTypeLevel(value: string)
    {
      this.hard.nameTypeLevel = value;
    }



}