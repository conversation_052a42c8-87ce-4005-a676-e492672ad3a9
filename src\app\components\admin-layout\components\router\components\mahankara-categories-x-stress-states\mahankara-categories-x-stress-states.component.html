  <ng-container *ngIf="listCategoriesXStress.length > 0">
    <div class="table-container">
      <table class="table table-list borderList">
        <thead>
          <tr>
            <th *ngFor="let title of titles">{{title}}</th>   
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of listCategoriesXStress; let i = index">                                
                  <td class="td-id aligTitle">
                      <input class="background-input-table-color form-control form-short noCursor" placeholder=" " disabled
                          type="text" #index [ngClass]="{'empty-input': !index.value}"
                          [value]="item.indexCategory" (change)="changeCategoriesXValue(i,'indexCategory', index.value)" />
                  </td>
                  <td class="td-id">
                      <input class="background-input-table-color form-control form-short" placeholder=" "
                          type="text" #humor [ngClass]="{'empty-input': !humor.value}"
                          [value]="item.humor" (change)="changeCategoriesXValue(i,'humor', humor.value)" />
                  </td>
                  <td class="td-id">
                      <input class="background-input-table-color form-control form-short" placeholder=" "
                          type="text" #sarcasm [ngClass]="{'empty-input': !sarcasm.value}"
                          [value]="item.sarcasm" (change)="changeCategoriesXValue(i, 'sarcasm', sarcasm.value)" />
                  </td>
                  <td class="td-id">
                      <input class="background-input-table-color form-control form-short" placeholder=" "
                          type="text" #seriousness [ngClass]="{'empty-input': !seriousness.value}"
                          [value]="item.seriousness" (change)="changeCategoriesXValue(i, 'seriousness', seriousness.value)" />
                  </td>
                  <td class="td-id">
                      <input class="background-input-table-color form-control form-short" placeholder=" "
                          type="text" #aggressiveness [ngClass]="{'empty-input': !aggressiveness.value}"
                          [value]="item.aggressiveness" (change)="changeCategoriesXValue(i, 'aggressiveness', aggressiveness.value)" />
                  </td>
                  <td class="td-id">
                      <input class="background-input-table-color form-control form-short" placeholder=" "
                          type="text" #despair [ngClass]="{'empty-input': !despair.value}"
                          [value]="item.despair" (change)="changeCategoriesXValue(i, 'despair', despair.value)" />
                  </td> 
          </tr>
      </tbody>
      </table>
    </div>
  </ng-container>


<ng-container *ngIf="listCategoriesXStress.length === 0">
    <div class="card" style="text-align: center; padding: 20px;">
        <h3>Empty list. Click to create the list.</h3>
    </div>
</ng-container>
