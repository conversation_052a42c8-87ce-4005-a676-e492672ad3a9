<div class="popup " [ngClass]="{'hide': !popupOpen}"
     (click)="togglePopup($event)">
  <div class="wrapper">
    <!--Header-->
    <div class="sticky list-header-row update" style="height: 132px;">
      <div class="card">
        <app-header-with-buttons 
          [cardTitle]="listName"
          [cardDescription]="cardDescription">
        </app-header-with-buttons>
        <app-header-search 
          (inputKeyup)="lstOnChangeFilter($event)"
          (searchOptions)="lstOnChangeFilterOptions($event)">
        </app-header-search>
      </div>
    </div>
    <div class="card">
      <table class="table table-list table-popup">
        <thead class="sticky">
          <tr>
            <th class="th-clickable" (click)="sortListByParameter('id')">
              ID
            </th>
            <th class="th-clickable" (click)="sortListByParameter('name')">
              Name & Title
            </th>
            <th>Description</th>
            <th class="th-clickable">
              Class
            </th>
            <th>Area
            </th>
            <th class="th-clickable" (click)="sortListByParameter('type')">
              Group
            </th>
            <th>Selected</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let c of lstIds | characters">
            <tr id="{{ c.id }}">
              <td class="td-id">{{ c.id }}</td>
              <td>
                <span>{{ c.name }}</span>
              </td>
              <td>
                <span>{{ c.description }}</span>
              </td>
              <td>
                <span [ngClass]="
                      c.classId ? 'btn btn-info btn-fill' : 'btn'
                    " style="cursor: context-menu;">
                  {{ (c.classId | klass)?.name || "undefined" }}
                </span>
              </td>
              <td>
                <span [ngClass]="
                      (c.areaId | area)
                        ? 'btn btn-info btn-fill'
                        : 'btn'
                    " style="cursor: context-menu;">
                  {{ (c.areaId | area)?.name || "undefined" }}
                </span>
              </td>
              <td>
                <span ngClass="btn btn-fill {{
                      c.type | characterTypeButton
                    }}" style="cursor: context-menu;">
                  {{ c.type | characterTypeName }}
                </span>
              </td>
              <td>
                <input type="checkbox" [checked]="checkCharacter(c) ? true : false"
                       (change)="changeValue($event, c)" />
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>
<h5 [ngClass]="{'character-group': true, 'selected-background': selectedCharacters?.length === 0}" (click)="togglePopup(undefined)">
  <span class="character" *ngFor="let character of selectedCharacters">{{character.name }}</span>
</h5>