import { Component, EventEmitter, Input, Output } from '@angular/core';
import { Button } from 'src/app/lib/@pres-tier/data';

@Component({
  selector: 'app-item-details-header',
  templateUrl: './item-details-header.component.html',
  styleUrls: ['./item-details-header.component.scss']
})
export class ItemDetailsHeaderComponent {

  @Input() cardTooltip: string = '';
  @Input() selectedOption: string = '';
  @Input() cardTitle: string;
  @Input() valueBossLevel: string;
  @Input() nameRarity: string;
  @Input() nameClass: string;
  @Input() type: string;
  @Input() cardDescription: string;
  @Input() btnSubContext = false;
  @Input() textDescriptionRecord = false;
  @Input() rightButtonTemplates: Button.Templateable[] = [];
  @Input() leftButtonTemplates: Button.Templateable[] = [];
  @Output() cardBackButtonClick = new EventEmitter();
  @Input() isBackButtonEnabled = false;
  @Input() hasDropdownSelect = false;
  activeLanguage = 'PTBR';
  @Input() elements = [];
  @Output() selectedElement = new EventEmitter<any>();
  @Output() clickBtn = new EventEmitter<any>();
  @Input() buttonLabel: string;
  @Input() titleBossLevel: string;
  @Input() isActive: boolean;

  changeElement(event: any) {
    this.selectedElement.emit(event.target.value);
  }

  btnClickContext() {
    this.clickBtn.emit();
  }
}
