import { Component, OnInit } from '@angular/core';
import { AreaService, DialogueService, EventService, ItemService, LevelService, ReviewService, RpgService } from 'src/app/services';
import { Area, Dialogue, Level } from 'src/app/lib/@bus-tier/models';
import { EventType, GameTypes } from 'src/lib/darkcloud/dialogue-system';
import { RoadBlockService } from 'src/app/services/road-block.service';
import { SortingService } from 'src/app/services/sorting.service';
import { HighlightElement } from 'src/lib/others';
import { MicroloopService } from 'src/app/services/microloop.service';
import { MicroloopContainerService } from 'src/app/services/microloop-container.service';
import { RPGType } from 'src/lib/darkcloud/rpg-formatting';
import { ActivatedRoute, Router } from '@angular/router';

export interface AuxiliarType 
{
  amount:number;
  itemId:string;
  eventId:string;
  type:number;
  location:string;
  item:string;
  progress:number
}

@Component({
  selector: 'app-general-items',
  templateUrl: './general-items.component.html',
})

export class GeneralItemsComponent implements OnInit 
{
  receivedItems:AuxiliarType[] = [];
  tradeItems:AuxiliarType[] = [];
  giveItems:AuxiliarType[] = [];
  changeSortString:number = 1;
  changeSortNumeric:number = 1;
  detectedCurrencyWords = [];


  constructor(
    private _activatedRoute: ActivatedRoute,
    public readonly _router: Router,
    public readonly _areaService: AreaService,
    public readonly _itemService: ItemService,
    private _eventService: EventService,
    private _roadblockService: RoadBlockService,
    private _reviewService: ReviewService,
    private _sortingService: SortingService,
    private _microloopService: MicroloopService,
    private _microloopContainerService:MicroloopContainerService,
    private _dialogueService:DialogueService,
    private _levelService:LevelService,
    private _rpgService: RpgService
  ) 
  {}

  async ngOnInit()
  {
    this.detectedCurrencyWords = await this.getWordsMarkedAsCurrency();
    let keys = Object.keys(this.detectedCurrencyWords);

    this.receivedItems = await this.getEvents(this.receivedItems, 'RECEIVE_ITEM', keys);
    this.tradeItems = await this.getEvents(this.tradeItems, 'TRADE_ITEM', keys);
    this.giveItems = await this.getEvents(this.giveItems, 'GIVE_ITEM', keys);
  } 
  
  async getEvents(eventsList, eventType:string, keys)
  {
    //1. Get events that are Currency type.
    eventsList = this._eventService.models.filter((event) => event.itemId != undefined);
    //2. Get events that are Receive type.
    eventsList = await eventsList.filter(event=> +event.type === +EventType[eventType]);

    eventsList = this.filterEventsByItemnameNotinDetectedcurrencywords(keys, eventsList);
    let aux = [];
    for(let i = 0; i < eventsList.length; i++)
    {
      let newAuxiliar: AuxiliarType = 
      {
        amount:+eventsList[i].amount,
        itemId:eventsList[i].itemId,
        eventId:eventsList[i].id,
        type:eventsList[i].type,
        location:this.findLocation(eventsList[i]?.id),
        item: this._itemService.svcFindById(eventsList[i].itemId)?.name,
        progress: this._roadblockService.filterByStoryBoxId(eventsList[i]?.id?.split('.evt')[0])?.id != undefined ? 1 : 0
      }
      aux.push(newAuxiliar);        
    }
  
    eventsList = [];
    eventsList = aux;
    return eventsList;
  }
  isReceivedItemType = (i: number, eventType:string):boolean => +this._eventService.models[i]?.type == +EventType[eventType];

  filterEventsByItemnameNotinDetectedcurrencywords(keys, list)
  {
    let aux = [];
    for(let i = 0; i < list.length; i++)
    {
      let item = this._itemService.svcFindById(list[i].itemId);
      for(let j = 0; j < keys.length; j++)
      {
        if(item.name.trim() == this.detectedCurrencyWords[keys[j]].word.trim()) break;        
        else if(j == keys.length-1) aux.push(list[i]);
      }
    }
    list = [];
    return aux;
  }
  async getWordsMarkedAsCurrency()
  {
    await this._rpgService.reset();
    return this._rpgService.detections[+RPGType.CURRENCY];    
  }

  goToLocation(item:AuxiliarType)	
  {	
    let levelId: string = item.eventId?.split('.D')[0];
    let levelDialogue:string = '';
    levelDialogue = this.getLevelDialogueId(item, levelDialogue);
   
    this._router.navigate(['levels/' + levelId + '/dialogues/' + levelDialogue]);
    HighlightElement(item.eventId, 500, true);
  } 

  getLevelDialogueId(item:AuxiliarType, levelDialogue:string):string
  {
    let aux = item.eventId.split('.');
    for(let i = 0; i < aux.length; i++)
    {
      if(i == 0) levelDialogue = aux[0];
      else
      {
        levelDialogue += '.'+aux[i];
        if(levelDialogue.includes('.D')) return levelDialogue;
      }
    }
    return levelDialogue;
  }

  sortListByNumericType(list, fieldName:string)
  {
    this.changeSortNumeric = -1*this.changeSortNumeric;
    if(this.changeSortNumeric == 1) list.sort((a,b) => a[fieldName] - b[fieldName]);
    else list.sort((a,b) => b[fieldName] - a[fieldName]);
  }

  sortListByStringType(list, fieldName:string)
  {
    this.changeSortString = -1*this.changeSortString;
    if(this.changeSortString == 1) list.sort((a, b) => a[fieldName].localeCompare(b[fieldName]));
    else list.sort((a, b) => b[fieldName].localeCompare(a[fieldName]));
  }

  findLocation(id: string)
  {
    let location: string = '';
    
    if (!id?.includes('ML')) 
    {
      const areaId = Area.getSubIdFrom(id);
      const area = this._areaService.svcFindById(areaId);
      let hierarchyCode = '';
      const levelId = Level.getSubIdFrom(id);
      const level = this._levelService.svcFindById(levelId);
      const dialogueId = Dialogue.getSubIdFrom(id, 'PT-BR');
      const dialogue = this._dialogueService.svcFindById(dialogueId);
      
      hierarchyCode = this._areaService.svcFindById(areaId)?.hierarchyCode;

      if(this._reviewService.reviewResults[levelId]?.index)
      {
        location = (hierarchyCode ? '[' + hierarchyCode + '] ' : '') +
          (level ? this._reviewService.reviewResults[levelId]?.index + ' ' +
          (level.name ? '"' + level.name + '" ' : '') + (dialogue ? 
          '(' + GameTypes.dialogueTypeDisplay[+dialogue.type] + ')' : '') : area.name);                   
      }
    }
    else if(id.includes('ML'))
    {
      const splits = id.split('.');
      const containerId = splits[0];
      const container = this._microloopContainerService.svcFindById(containerId);
      const loop = this._microloopService.svcFindById(splits[0]+"."+splits[1]);
      if(!loop) return null;
      location = "\n[ML] " + container?.name + " - " + (loop.name);
    }
    
    return location;    
  }
}

