import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { Base } from './Base';

export class Upgrades extends Base<Data.Hard.IUpgrades, Data.Result.IUpgrades> implements Required<Data.Hard.IUpgrades>
{
  private static generateId(index: number): string 
  {
    return IdPrefixes.UPGRADES + index;
  }
  constructor(
    index: number,
    itemId: string,
    dataAccess: Upgrades['TDataAccess']
  ) 
  {
    super(
      {
        hard: {
          id: Upgrades.generateId(index),
          itemId,
        },
      },
      dataAccess
    );
  }
  protected getInternalFetch() 
  {
    return {};
  }

  public get itemId(): string {
    return this.hard.itemId;
  }
  public set itemId(value: string) {
    this.hard.itemId = value;
  }
  public get buildingEfficiency(): number {
    return this.hard.buildingEfficiency;
  }
  public set buildingEfficiency(value: number) {
    this.hard.buildingEfficiency = value;
  }
  public get hellCicle(): number
  {
    return this.hard.hellCicle;
  }
  public set hellCicle(value: number)
  {
    this.hard.hellCicle = value;
  }
  public get upgrades(): string
  {
    return this.hard.upgrades;
  }
  public set upgrades(value: string)
  {
    this.hard.upgrades = value;
  }
  public get workshopLevelUnlock(): number
  {
    return this.hard.workshopLevelUnlock;
  }
  public set workshopLevelUnlock(value: number)
  {
    this.hard.workshopLevelUnlock = value;
  }
  public get rubies(): number
  {
    return this.hard.rubies;
  }
  public set rubies(value: number)
  {
    this.hard.rubies = value;
  }
  public get newEfficiency(): number
  {
    return this.hard.newEfficiency;
  }
  public set newEfficiency(value: number)
  {
    this.hard.newEfficiency = value;
  }
  public get efficiencyBoost(): number
  {
    return this.hard.efficiencyBoost;
  }
  public set efficiencyBoost(value: number)
  {
    this.hard.efficiencyBoost = value;
  }

  public get souls(): number
  {
    return this.hard.souls;
  }
  public set souls(value: number)
  {
    this.hard.souls = value;
  }

  public get minutes(): number
  {
    return this.hard.minutes;
  }
  public set minutes(value: number)
  {
    this.hard.minutes = value;
  }

  public get subParticles(): string[]
  {
    return this.hard.subParticles;
  }

  public set subParticles(value: string[])
  {
    this.hard.subParticles = value;
  }  

  public get ingredientsOrder(): string[]
  {
    return this.hard.ingredientsOrder;
  }
  
  public set ingredientsOrder(value: string[])
  {
    this.hard.ingredientsOrder = value;
  }

  public get ingredientsAmount(): number[]
  {
    return this.hard.ingredientsAmount;
  }
  
  public set ingredientsAmount(value: number[])
  {
    this.hard.ingredientsAmount = value;
  }
}
