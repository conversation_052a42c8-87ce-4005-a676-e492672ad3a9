import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  Output,
} from '@angular/core';
import { Subscription } from 'rxjs/internal/Subscription';
import {
  Area,
  Character,
  Class,
  Dialogue,
  Item,
  Level,
  Mission,
  Option,
  OptionBox,
  SpokePlace,
  StoryBox,
  SubContext,
} from 'src/app/lib/@bus-tier/models';
import { Atributte } from 'src/app/lib/@bus-tier/models/Atributte';
import { BattleUpgrade } from 'src/app/lib/@bus-tier/models/BattleUpgrade';
import { DCGuide } from 'src/app/lib/@bus-tier/models/DCGuide';
import { RoadBlock } from 'src/app/lib/@bus-tier/models/RoadBlock';
import { Typing } from 'src/app/lib/@pres-tier';
import { LocationPipe } from 'src/app/pipes/location.pipe';
import {
  AreaService,
  BattleUpgradeService,
  ClassService,
  DCGuideService,
  LevelService,
  MODMoonAttributesService,
  ReviewService,
  SubContextService,
} from 'src/app/services';
import { AtributteService } from 'src/app/services/atributte.service';
import { DialogueService } from 'src/app/services/dialogue.service';
import { EventHandlerService } from 'src/app/services/eventHandler.service';
import { OptionBoxService } from 'src/app/services/option-box.service';
import { OptionService } from 'src/app/services/option.service';
import { RoadBlockService } from 'src/app/services/road-block.service';
import { StoryBoxService } from 'src/app/services/story-box.service';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Alert } from 'src/lib/darkcloud';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { RoadBlockType } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard/IRoadBlock';
import { GameTypes, OptionBoxType } from 'src/lib/darkcloud/dialogue-system';
import { HighlightElement } from 'src/lib/others';
import { LevelHelperService } from '../../../../../../../../../../services/level-helper.service';
import {
  fadeIn,
  popup,
} from '../../../../../bound-item-list/bound-list.component.animations';
import { EventService } from 'src/app/services/event.service';
import { MarkerService } from 'src/app/services/marker.service';
import { SpeechService } from 'src/app/services/speech.service';
import { SettingsComponent } from '../../../../../settings/settings.component';
import { Router } from '@angular/router';


interface SpokePlaceHelper {
  elementId: string;
  text: string;
  label: string;
  component: string;
}
@Component({
  selector: 'app-choice-box',
  templateUrl: './choice-box.component.html',
  styleUrls: ['./choice-box.component.scss'],
  animations: [fadeIn, popup],
})
export class ChoiceBoxComponent implements OnDestroy {
  @Input() index: number;
  @Input() dialogue: Dialogue;
  @Input() optionBox: OptionBox;
  @Input() preloadedSpeakers: Character[]; //A informação do Speakers é adicionada nos levels
  @Input() preloadedMissionsOfArea: Mission[];
  @Input() language: string;
  @Output() refresh: EventEmitter<void> = new EventEmitter();
  public HighlightElement = HighlightElement;
  public OptionBoxType = OptionBoxType;
  private rpgSubscription: Subscription;
  public roadblockId: string;
  existOptionBox: Option;
  isReset = false;
  optionReset: Option;

  toRemoveProcessConditionFunc: (roadblock: any) => void;

  popupStats = false;
  openAttributeBox = false;
  openITDBox = false;
  openSubcontext = false;
  openDCBox = false;
  isConfirm = false;
  selectedOpponent: any;
  selectedAttribute: any;  selectedITD: any;
  selectSubcontext: any;
  resultDC: number;
  descriptionDCGuide: string;
  descriptionSubcontext: string;
  mc: number;
  valueC = 2;
  valueBL: number;
  positive: string;
  optionId: string;
  classeName: string;
  speaker: Character;
  atributte: Atributte[] = [];
  listSubcontext: SubContext[] = [];
  getAtributte: Atributte;
  currentBL: BattleUpgrade;
  optionDifficultyClass: Option;
  difficulty: any;
  subcontext: string;
  public hasLabel: boolean = false;
  selectSpeaker = [];
  timeout: any;
  timeout2: any;
  optionBoxes;
  answerBoxPositive: StoryBox;
  getOption;
  spokePlaces = [];
  usedRoadBlocks = [];
  usedOnLevels = [];
  classe: Class[] = [];
  public itemList: Item[] = [];
  roadBlocksUseds: RoadBlock[] = [];
  listDCGuide: DCGuide[] = [];
  isTextValid = true;
  isModMonn: boolean = false; 
  isIdNegative: string;
  listSpokePlace: SpokePlace[] = [];
  listMoonAttributes = [];

  //html
  speak: Character;

  constructor(
    private _userSettingsService: UserSettingsService,
    private _dialogueService: DialogueService,
    private _optionBoxService: OptionBoxService,
    private _atributteService: AtributteService,
    private _speechService: SpeechService,
    private _eventService: EventService,
    private _markerService: MarkerService,
    private _storyBoxService: StoryBoxService,
    private _optionService: OptionService,
    private _roadblockService: RoadBlockService,
    private _eventsHandlerService: EventHandlerService,
    private _change: ChangeDetectorRef,
    private _router: Router,
    private _levelHelperService: LevelHelperService,
    private _areaService: AreaService,
    private _levelService: LevelService,
    private _reviewService: ReviewService,
    private _dcGuideService: DCGuideService,
    private _classService: ClassService,
    private _subContextService: SubContextService,
    private locationPipe: LocationPipe,
    private _settingsComponent :SettingsComponent,
    private _battleUpgradeService: BattleUpgradeService,
    private _modMoonAttributesService: MODMoonAttributesService,
    
  ) {
    this.toRemoveProcessConditionFunc = this.toRemoveRoadblock.bind(this);
  }


  async ngOnInit() {  
    this.rpgSubscription = this._eventsHandlerService
      .OnRpgWordAdded()
      .subscribe((object) => {
        this.optionBox = this._optionBoxService.svcCloneById(this.optionBox.id);
      });
    this.timeout = setTimeout(() => {
      this._change.detectChanges();
    }, 500);

    if (this.optionBox) {
      if (this.optionBox?.AndOrCondition == undefined) {
        this.optionBox['AndOrCondition'] = 'OR';
      }
  
      this._dcGuideService.toFinishLoading();
      this.listDCGuide = this._dcGuideService.models;
      this._levelHelperService.toFinishLoading();
      this.listSpokePlace = this._levelHelperService.models;
      await this.getRoadBlocks();
        this.optionBoxes = this._optionService.svcFilterByIds(this.optionBox.optionIds, true);
        this. addTypeOPtions();
        this.roadblocksForKeyInformation();
    }

  }


  addTypeOPtions() {
    this.optionBoxes.forEach((option) => {  
      if (option.type === undefined) {
        option.typ = this.optionBox.type;
      }
      this._optionService.svcToModify(option);
    });
  }
  
async roadblocksForKeyInformation() {
  const roadblocks = this._roadblockService.models.filter((roadblock) => {
    return roadblock.spokeElementId === this.optionBox?.id;
  });

  roadblocks.forEach((roadblock) => {
    const dialogueIds = this._dialogueService.models.filter((dialogue) => {
      return (
        roadblock.StoryBoxId &&
        !dialogue.id.includes('ML') &&
        roadblock.StoryBoxId.includes(dialogue.id)
      );
    }).map((dialogue) => dialogue.id);

    dialogueIds.forEach((dialogueId) => {
      const areaId = Area.getSubIdFrom(dialogueId);
      const area = this._areaService.svcFindById(areaId);
      if (!area) return;

      const levelId = Level.getSubIdFrom(dialogueId);
      const level = this._levelService.svcFindById(levelId);
      const dialogue = this._dialogueService.svcFindById(Dialogue.getSubIdFrom(dialogueId, 'PT-BR'));
      const hierarchyCode = area.hierarchyCode;
      const levelIndex = this._reviewService.reviewResults[levelId]?.index;
      const type = GameTypes.dialogueTypeDisplay[+dialogue.type];

      this.usedOnLevels.push(`[${hierarchyCode}] ${levelIndex} "${level.name}" (${type})`);
    });

    this.usedRoadBlocks.push(roadblock);
  });
}

  async getRoadBlocks() {
    this._optionBoxService.toFinishLoading();
    const idsToRemove = this._optionBoxService.models.map((option) => option.id);

    this._roadblockService.toFinishLoading();
    this._roadblockService.models.filter((roadblock) => !idsToRemove.includes(roadblock?.StoryBoxId));
    let rb = this._roadblockService.models.find((rb) => rb.spokeElementId == this.optionBox.id);
    if (rb) this.hasLabel = true;
  }

  checkLocation(location: string[]) {
    const existLocation = this.locationPipe.transform(location);
    return existLocation;
  }

  ngOnDestroy() {
    // prevent memory leak when component is destroyed
    this.rpgSubscription.unsubscribe();

    clearInterval(this.timeout);
    clearInterval(this.timeout2);
  }

  public getInformation(id: string): Data.Internal.Base {
    return this._userSettingsService.getInformation(id);
  }

  public updateInformation<TKey extends keyof Data.Internal.Base>(
    id: string,
    key: TKey,
    value: Data.Internal.Base[TKey]
  ) {
    if (id) this._userSettingsService.updateInformation(id, key, value);
  }

  // ng trackers (for list optimization)
  trackByIndex(index: number, option: Option): any {
    return index;
  }

  async toMove(transpose: number) {
    if (this.dialogue.boxIds.length <= 1) return;
    const oldIndex = this.dialogue.boxIds.indexOf(this.optionBox.id) + 0;
    const newIndex = oldIndex + transpose;
    if (!this.dialogue.boxIds[newIndex]) return;

    this.dialogue.boxIds[oldIndex] = this.dialogue.boxIds[newIndex];
    this.dialogue.boxIds[newIndex] = this.optionBox.id;
    await this._dialogueService.svcToModify(this.dialogue);

    this._change.detectChanges();
    this.refresh.emit();
  }

 async toRemove(box: OptionBox) { //Remove todo o componente
    if (await Alert.showRemoveAlert(box.id)) {
       this._dialogueService.removeBox(this.dialogue, box.id);
       this._optionBoxService.svcToRemove(box.id);
       box?.optionIds.forEach((optionId) => {
        this._optionService.svcToRemove(optionId);
      });
       this.removePostiveAndNegativeComponent(box.id);
      this._userSettingsService.removeIdObjectInformations(box.id);
      this.removePlacesLabels(box.id); 

      this._change.detectChanges();
      this._settingsComponent.loadPlaces();
      this.ngOnInit();
      this.refresh.emit();
      this.uploadRedirect(box.id);   
    }
    this._change.detectChanges();
    this.refresh.emit();
  }

  removePlacesLabels(id: string) {
    this._roadblockService.models.forEach((roadblock) => {
      if (roadblock.spokeElementId === id) {
        roadblock.spokeElementId = undefined;
        this._roadblockService.svcToModify(roadblock);
      }
    });   

    this._levelHelperService.models.forEach((places) => {
      if (places.elementId.includes(id)) {
        this._levelHelperService.svcToRemove(places.id);
      }
    });     
  }

  uploadRedirect(id: string) {
    const levelId = id.split('.').slice(0, 2).join('.');
    const dialogueId = id.split('.').slice(0, 3).join('.');

    this._router.navigate(['levels/' + levelId + '/dialogues']);
    setTimeout(() => {      
      this._router.navigate(['levels/' + levelId + '/dialogues/' + dialogueId]);
    }, 75);
  }

  public async onChange(
    optionBox: OptionBox,
    property?: string,
    value?: string
  ) {
    if (property) optionBox[property] = value;

    await this._optionBoxService.svcToModify(this.optionBox);
    this._change.detectChanges();
  }

  public async onChangeOption(option: Option) {
    await this._optionService.svcToModify(option);
   // this._change.detectChanges();
  }

  public async onChangeOptionValue(
    option: Option,
    property: string,
    value: any
  ) {
    option[property] = value;
    await this._optionService.svcToModify(option);
    this._change.detectChanges();
    this.refresh.emit();
  }

  //Look the method on the documentation to understand better.
  async updateOptionFromAnswerBox(option: Option) {
    for (let i = 0; i < this.optionBoxes.length; i++) {
      if (this.optionBoxes[i].id == option.id) {
        this.optionBoxes[i].label = option.label;
        break;
      }
    }

    await this._optionService.svcToModify(option);
    this._change.detectChanges();
    this.refresh.emit();
  }

  public async selectWeight(option: Option) {
    await Alert.showNumberField('Choose Weight').then(async (e) => {
      if (e.isDismissed) return;

      option.weight = e.value;
      if (option.weight === 0) {
        option.weight = undefined;
      }
      await this._optionService.svcToModify(option);
    });
    this._change.detectChanges();
    this.refresh.emit();
  }

  // methods that can be called within the html (add, remove, move listItems)
  public async toMoveOption(option: Option, transpose: number) {
    if (this.optionBox.optionIds.length <= 1) return;

    const oldIndex = this.optionBox.optionIds.indexOf(option.id) + 0;
    const newIndex = oldIndex + transpose;
    if (!this.optionBox.optionIds[newIndex]) return;

    this.optionBox.optionIds[oldIndex] = this.optionBox.optionIds[newIndex];
    this.optionBox.optionIds[newIndex] = option.id;
    await this._optionBoxService.svcToModify(this.optionBox);
    this._change.detectChanges();
    this.ngOnInit();
    this.refresh.emit();
  }

  listDit = [
    { name: 'Muito fácil: 5', levelValeu: 5 },
    { name: 'Fácil: 10', levelValeu: 10 },
    { name: 'Moderado: 15', levelValeu: 15 },
    { name: 'Difícil: 20', levelValeu: 20 },
    { name: 'Muito difícil: 25', levelValeu: 25 },
    { name: 'Quase impossível: 30', levelValeu: 30 },
  ];

  // Difficulty class
  public async openModalDificultyClass(option: Option) {
    this.optionDifficultyClass = option;

    this._atributteService.toFinishLoading();
    this.atributte = this._atributteService.models;

    // Verifica se há valores já definidos
    if ( option.choiceDifficulty !== undefined) {
        this.existOptionBox = option; 
        this.isReset = true;
        this.optionReset = option;
        this.openModalChoice(option);  
    } else {
      // Se não houver valores definidos, abre o modal sem preenchimento
      this.speaker = null;
      this.getAtributte = null;
      this.difficulty = null;
      this.resultDC = null;
      this.classeName = null;
      this.selectedOpponent = null;
      this.valueBL = null;
      this.currentBL = undefined;
      this.selectedITD = null;
      this.selectSubcontext = null;
      this.selectedAttribute = null;
      this.descriptionSubcontext = null;
      this.isReset = false;
      this.listMoonAttributes = [];
      this.isModMonn = false;

      // Inicializa os valores
      this.openAttributeBox = false;
      this.openSubcontext = false;
      this.openITDBox = false;
      this.openDCBox = false;
    }
    this.popupStats = true;
  }

  openModalChoice(option: Option) {
    // Preenche os valores já definidos
    this.speaker = this.preloadedSpeakers.find(
      (s) => s.name === option?.choiceSpeech
    );
    this.selectedOpponent = this.speaker;
    this.valueBL = option?.bl;
    this.classeName = option?.classeNameOpponet;

    // Preenche atributo
    this.getAtributte = this.atributte.find(
      (atri) => atri.atributte === option?.choiceAtributte
    );
    this.selectedAttribute = this.getAtributte;
    this.mc = option?.classModifierValue;

    this.listMoonAttributes = [];
    this._modMoonAttributesService.models.forEach((moonAttribute) => {
      const index = moonAttribute.idsAtributte.indexOf(this.selectedAttribute.id);
      if (index !== -1 && moonAttribute.modMoonAtributte[index] !== '') {        
        this.listMoonAttributes.push(moonAttribute.modMoonAtributte[index]);
      }
    }); 
    this.isModMonn = option?.isModMoon;

    //Sucontext
    this._subContextService.toFinishLoading();
    this.listSubcontext = this._subContextService.models.filter(
      (x) => x.atributte === this.selectedAttribute?.atributte
    );
    this.descriptionSubcontext = option?.subContextDescription;
    this.selectSubcontext = option?.subcontext ? option?.subcontext : 'None (Default)';

    this.listSubcontext.forEach((x) => {
      if (!x.subContext.includes('None (Default)')) {
        x.subContext.unshift('None (Default)');
      }
    });

    // Preenche ITD
    this.difficulty = this.listDit.find(
      (dif) => dif.name === option.choiceDifficulty
    );
    this.selectedITD = this.difficulty;
    this.getDCGuide(option);
    // Preenche o DC
    this.resultDC = option.resultDC;

    // Abre os campos relevantes
    this.openAttributeBox = true;
    this.openSubcontext = true;
    this.openITDBox = true;
    this.openDCBox = true;
  }

  getDCGuide(option: Option) {
    for (let index = 0; index < this.listDCGuide.length; index++) {
      if (
        this.listDCGuide[index].dcMin === option.resultDC ||
        this.listDCGuide[index].dcMax === option.resultDC
      ) {
        option.descriptionDCGuide = this.listDCGuide[index].description;
        this.descriptionDCGuide = this.listDCGuide[index].description;
        this._optionService.svcToModify(option);
        this._optionService.toSave();
        this._change.detectChanges();
      }
    }
  }

  //MODAL
  getOpponent(speak: Character) {
    this.speaker = speak;
    this.selectedOpponent = speak;
    this.openAttributeBox = true;

    // Resetar caixas subsequentes
    this.selectedAttribute = null;
    this.openITDBox = false;
    this.selectedITD = null;
    this.openDCBox = false;
    this.resultDC = null;
    this.isConfirm = false; // Habilita o botão de confirmação após alteração

    // Verif qual a classe do opponent
    this.classe = this._classService.models.filter(
      (x) => x.id === this.speaker.classId
    );
    this.classe.forEach((x) => (this.classeName = x.name));

    // Verif valor do BL
    this.currentBL = this._battleUpgradeService.models.find(
      (modifier) => modifier.character == this.speaker.id
    );

    if (this.currentBL === undefined) {
      this.valueBL = 0;
    } else {
      this.valueBL = this.currentBL.bl;
    }
  }

  getAttribute(atri: Atributte) {
    this.getAtributte = atri;
    this.selectedAttribute = atri;
    this.openITDBox = false;
    this.openDCBox = false;
    this.openSubcontext = false;
    this.listMoonAttributes = [];

    this.listSubcontext = [];

    // Resetar caixa subsequente
    this.selectedITD = null;
    this.openDCBox = false;
    this.resultDC = null;
    this.isConfirm = false;

    if (this.classe.length == 0) {
      this.classe = this._classService.models.filter(
        (x) => x.id === this.speaker.classId
      );
    }

    let positioAtributte;

    // Verif posição do atributo selecionada na lista de atributos
    for (let index = 0; index < this._atributteService.models.length; index++) {
      if (this._atributteService.models[index].id === this.getAtributte.id)
        positioAtributte = index;
    }
    // Verif o opponent de acordo com a classe dele e o atributo para definir o valor do MC
    this.mc = this.classe[0]?.amountClass[positioAtributte];

    //Verif se o valor selecionado é igual ao valor do Subcontext
    this._subContextService.toFinishLoading();
    this.listSubcontext = this._subContextService.models.filter(
      (x) => x.atributte === this.selectedAttribute.atributte
    );

    this.listSubcontext.forEach((x) => {
      if (!x.subContext.includes('None (Default)')) {
        x.subContext.unshift('None (Default)');
      }
    });    
    
    this._modMoonAttributesService.models.forEach((moonAttribute) => {
      const index = moonAttribute.idsAtributte.indexOf(this.selectedAttribute.id);
      if (index !== -1 && moonAttribute.modMoonAtributte[index] !== '') {
        this.listMoonAttributes.push(moonAttribute.modMoonAtributte[index]);
      }
    });

    this.selectSubcontext = 'None (Default)';
    this.openSubcontext = true;
    this.openITDBox = false;
    this.selectedITD = null;
  }

  getTaskSubContext(sub: any, index: number) {
    this.subcontext = sub;
    this.selectSubcontext = sub;

    if (this.selectSubcontext !== 'None (Default)') {
      index = index - 1;
      this.listSubcontext.forEach(
        (x) => (this.descriptionSubcontext = x.description[index])
      );
    } else {
      this.descriptionSubcontext = '';
    }

    this.openITDBox = true;
    this.isConfirm = false; // Habilita o botão de confirmação após alteração
    this.openDCBox = false;
    this.resultDC = null;
  }

  getTaskDifficultyClass(dif: any) { 
      this.difficulty = dif;
      this.selectedITD = dif;

      // Verifica se todos os elementos estão selecionados antes de calcular
      if (this.speaker && this.getAtributte && this.difficulty) {
        this.resultDC = this.calculateCD(
          this.difficulty.levelValeu,
          this.valueBL,
          this.valueC,
          this.mc
        );
        // Mantém número inteiro
        this.resultDC = Math.floor(this.resultDC);
        this.isConfirm = true;
      }

      for (let index = 0; index < this.listDCGuide.length; index++) {
        if (
          this.listDCGuide[index].dcMin === this.resultDC ||
          this.listDCGuide[index].dcMax === this.resultDC
        ) {
          this.descriptionDCGuide = this.listDCGuide[index].description;
        }
      }   

    this.openDCBox = true;
  }

    async resetDice(resetOption: Option) { 

      try {
        if (await Alert.showRemoveAlert('DICE')) {    
          const answerPostive =  this._storyBoxService.svcFindById(resetOption.answerBoxId);   
          answerPostive.choicePositive = undefined;
          answerPostive.descriptionDCGuideOption = undefined;
          answerPostive.resultDCOption = undefined;
          answerPostive.nameKnowledge = undefined;
          answerPostive.resultDCOption = undefined;
          answerPostive.subcontext = undefined;
          answerPostive.type = answerPostive.type;
          answerPostive.messageOption = answerPostive?.messageOption;
          this._storyBoxService.svcToModify(answerPostive);      

          this.resetIdNegative(resetOption.answerBoxNegativeId);

          resetOption.answerBoxNegativeId = undefined;
          resetOption.bl = undefined;
          resetOption.choiceAtributte = undefined;
          resetOption.choiceDifficulty = undefined;
          resetOption.choiceNegative = undefined;
          resetOption.choicePositive = undefined;
          resetOption.choiceSpeech = undefined;
          resetOption.classModifierValue = undefined;
          resetOption.classeNameOpponet = undefined;
          resetOption.descriptionDCGuide = undefined;
          resetOption.difficultClassValue = undefined;
          resetOption.fatorSituationModifier = undefined;
          resetOption.investigaDifficulty = undefined;
          resetOption.investigationPositive = undefined;
          resetOption.investigationNegative = undefined;          
          resetOption.nameKnowledge = undefined;
          resetOption.resultDC = undefined;
          resetOption.subContextDescription = undefined;
          resetOption.subcontext = undefined;
          resetOption.valueSituationModifier = undefined;
          this.existOptionBox = undefined;
          resetOption.labelAnswerNegative = undefined;
          resetOption.labelAnswerPositive = resetOption?.labelAnswerPositive 
          this._optionService.svcToModify(resetOption);          
        }
      } catch (error) {
        Alert.showError(error);
      }   

      this.popupStats = false;
      this._change.detectChanges();
     // this.refresh.emit();
      this.ngOnInit();
    }

  resetIdNegative(id: string) {
      this._storyBoxService.svcToRemove(id);
      this._speechService.svcToRemove(id);
      this._eventService.svcToRemove(id);
      this._markerService.svcToRemove(id);
      this._roadblockService.svcToRemove(id);
      const place = this._levelHelperService.models.find((x) => x.elementId === id);
      this._levelHelperService.svcToRemove(place?.id);
    }

  confirmDifficultyClass() {
   
      this.optionDifficultyClass.choiceSpeech = this.speaker.name;
      this.optionDifficultyClass.choiceAtributte = this.getAtributte.atributte;
      this.optionDifficultyClass.choiceDifficulty = this.difficulty.name;
      this.optionDifficultyClass.difficultClassValue = this.difficulty;
      this.optionDifficultyClass.classModifierValue = this.mc;
      this.optionDifficultyClass.classeNameOpponet = this.classeName;
      this.optionDifficultyClass.bl = this.valueBL;
      this.optionDifficultyClass.resultDC = this.resultDC;
      this.optionDifficultyClass.descriptionDCGuide = this.descriptionDCGuide;
      this.optionDifficultyClass.subcontext = this.subcontext;
      this.optionDifficultyClass.subContextDescription = this.descriptionSubcontext;
      this.optionDifficultyClass.type = 0;
      this.optionDifficultyClass.isModMoon = this.isModMonn;

      if (this.existOptionBox) {// Para atualizar dados do DICE      
        this._optionService.svcToModify(this.optionDifficultyClass);
        this._optionService.toSave(); 
      } 
      else {
        this.checkAtributteLevel(this.optionDifficultyClass);
        this.toPromptAddAnswerBox(this.optionDifficultyClass); 
      }
  
      this._change.detectChanges();
      this.isConfirm = false;
      this.popupStats = false;
      this.refresh.emit();
      this.ngOnInit();
  }

  closeAreaStatsPopup() {
    this.popupStats = false;
    this.existOptionBox = undefined;
  }

  handleOutsideMouseClick(event: MouseEvent) {
    if (!this.popupStats) return;
    const myDiv = document.getElementById('modal-close');
    // Get the position relative to the viewport
    const rect = myDiv.getBoundingClientRect();
    const top = rect.top;
    const left = rect.left;
    //Check the x axis
    if (event.clientX < left || event.clientX > left + myDiv.offsetWidth) {
      this.closeAreaStatsPopup();
    } else if (
      event.clientY > top + myDiv.offsetHeight ||
      event.clientY < top
    ) {
      this.closeAreaStatsPopup();
    }
  }

  closeLevelReferencePopup() {
    this.popupStats = false;
    this.ngOnInit();
  }

  //Add positive or negative attribute to option and storyBox
  checkAtributteLevel(option: Option) {
    let valuePositive;

    this._atributteService.models.forEach((x) => {
      if (x.atributte.includes(option.choiceAtributte)) {
        option.choicePositive = x?.positive;
        option.choiceNegative = x?.negative;
        valuePositive = x?.positive;
        return option;
      } else {
        return null;
      }
    });

    this._storyBoxService.models.forEach((x) => {
      for (let index = 0; index < this.optionBoxes.length; index++) {
        if (this.optionBoxes[index].choicePositive != undefined) {
          if (x.id.includes(this.optionBoxes[index].answerBoxId)) {
            x.choicePositive = valuePositive;
          }
        }
      }
    });
    this._storyBoxService.toSave();
  }

  ckeckDifficultyClassValue() {
    if (this.currentBL && this.currentBL?.bl === undefined) {
      const retBossLevel = this.checkBosslevel(this.currentBL);
      this.currentBL.bl = retBossLevel;
      this._battleUpgradeService.svcToModify(this.currentBL);
      this._battleUpgradeService.toSave();
    }
  }

  checkBosslevel(currentCharacter: BattleUpgrade) {
    for (let i = 0; i < currentCharacter?.hp.length; i++) {
      if (currentCharacter?.hp[i] !== null) {
        return currentCharacter.baseLevel[i];
      }
    }
    return 0; // Caso todos os valores de hp sejam nulos
  }

  calculateCD(ITD: number, BL: number, c: number, MC: number): number {
    // Calcula a parte interna da fórmula
    if (BL == undefined) {
      BL = 0;
    }
    const internalCalculation = ITD + (BL / 100) * ((30 - ITD) / c) + MC;

    // Retorna o valor mínimo entre 30 e o cálculo interno
    return Math.min(30, internalCalculation);
  }

  calculateKnowledgeDC(ITD: number, valueSM: number): number {
    const dcValue = ITD + valueSM;

    return dcValue > 30 ? 30 : dcValue;
  }

  public async toPromptAddAnswerBox(option: Option) {
    try {   
        const answerBox2 = await this._storyBoxService.svcPromptCreateNew(option.id);
        option.answerBoxNegativeId = answerBox2.id;  
        option.labelAnswerPositive = this._levelHelperService.models.find((tl) => tl.elementId === option.answerBoxId)?.originalLabel; 
        this._optionService.svcToModify(option);
        this._optionService.toSave();
        this.existOptionBox = undefined;
       // this._change.detectChanges();
    
    } catch (error) {
      Alert.showError(error);
    }
  }

  public async toAddRoadblock() {
    let roadblock = await this._roadblockService.svcPromptCreateNew(
      this.optionBox.id,
      RoadBlockType.OBTAINED_ITEM
    );
    this.roadblockId = roadblock.id;
    this._roadblockService.srvAdd(roadblock);
    this._change.detectChanges();
    await this.roadBlocksUseds.push(roadblock);
    this.refresh.emit();
  }

  async ngAfterViewInit() {
    await this._roadblockService.toFinishLoading();
    this.roadBlocksUseds =
      this._roadblockService.filterStoryboxForAllRoadblocks(this.optionBox?.id);
    this.refresh.emit();
  }

  public async toPromptAddOption() {
    try {
      let option = await this._optionBoxService.addOption(this.optionBox);
      this.optionBoxes.push(option);
      this.optionId = option.id;
      const answerBox = await this._storyBoxService.svcPromptCreateNew(option.id);
      this.answerBoxPositive = answerBox;
      await this._storyBoxService.srvAdd(answerBox);
      option.answerBoxId = answerBox.id;
      await this._optionService.svcToModify(option);
      this._change.detectChanges();

      this.timeout2 = setTimeout(() => {
        this._change.detectChanges();
        this.refresh.emit();
      }, 300);

      // Scroll para o novo componente com efeito suave
      setTimeout(() => {
        HighlightElement(option.id, 110, true, 'transparent');
      }, 100);
      
    } catch (error) {
      Alert.showError(error);
    }
    this._change.detectChanges();
    this.refresh.emit();
  }

 
  public async RemoveOption(option: Option) {
    try {
      if (await Alert.showRemoveAlert('Option: ' + option.message)) {

          this._optionService.svcToRemove(option.id);
          this._optionBoxService.RemoveOption(this.optionBox, option.id);
          this._levelHelperService.svcToRemove(option.answerBoxId);
          this.removePostiveAndNegativeComponent(option.id);

          this._levelHelperService.models.forEach((places) => {
          if (places.elementId === option?.answerBoxId || places.elementId === option?.answerBoxNegativeId) {
            this._levelHelperService.svcToRemove(places.id);
          }
        });

        this._roadblockService.models.forEach((roadblock) => {
          if (roadblock.spokeElementId === option?.answerBoxId || roadblock.spokeElementId === option?.answerBoxNegativeId) {
            roadblock.spokeElementId = undefined;
            this._roadblockService.svcToModify(roadblock);
          }
        });   
     
        this._storyBoxService.checkSpeechInStorybox();//Atualiza os storyProgressIds
        this.ngOnInit();
      }
    } catch (error) {
      Alert.showError(error);
    }
    this.ngOnInit();
    this._change.detectChanges();
    this.refresh.emit();
  }

  async removePostiveAndNegativeComponent(id: string) {    

    this._storyBoxService.models.forEach((storyBox) => {
      if (storyBox.id.includes(id)) {     
         this._storyBoxService.toRemoveStory(storyBox.id);
       // this._storyBoxService.svcToRemove(storyBox.id);
      }
    });
    this._speechService.models.forEach((speech) => {
      if (speech.id.includes(id)) {
        this._speechService.svcToRemove(speech.id);
      }
    });
    this._eventService.models.forEach((event) => {
      if (event.id.includes(id)) {
        this._eventService.svcToRemove(event.id);
      }
    });
    this._markerService.models.forEach((marker) => {
      if (marker.id.includes(id)) {
        this._markerService.svcToRemove(marker.id);
      }
    });
    this._roadblockService.models.forEach((roadblock) => {
      if (roadblock.id.includes(id)) {
        this._roadblockService.svcToRemove(roadblock.id);
      }
    });
    
  }

  async toRemoveRoadblock(roadblock: RoadBlock) {
    if (await Alert.showRemoveAlert(Typing.typeName(roadblock))) {
      this.roadblockId = undefined;
      this._roadblockService.svcToRemove(roadblock.id);
      this.roadBlocksUseds = this.roadBlocksUseds.filter(
        (rb) => rb.id != roadblock.id
      );
      this._change.detectChanges();
      this.refresh.emit();
      this.ngOnInit();
    }
  }

  async changeLabel(event: Event) {
    const inputElement = event.target as HTMLInputElement;
    const text = inputElement.value.trim();

    const existingLabels = {};
    this.listSpokePlace.forEach((spoke) => {
      existingLabels[spoke.originalLabel] = true;
    });

    const spoke = this.listSpokePlace.find(
      (spoke) => spoke.elementId === this.optionBox.id
    );

    if (text === '') {
      inputElement.value = '<<Label for progress condition>>';
      this.optionBox.label = undefined;
      this._optionBoxService.svcToModify(this.optionBox);
      this._optionBoxService.toSave();

      if (spoke) {  
        this.removeBDLabelPlaces(this.optionBox.id, spoke.id);
       }

       this.refresh.emit();   
    } 
    else {
      if (existingLabels[text]) {
        // Label já existe na base de dados do Places 
        Alert.showError('This Label ALREADY Exists!!', '');

       if(this.optionBox.label === undefined) {
        inputElement.value = '<<Label for progress condition>>';
        this._change.detectChanges();
      } else {
        inputElement.value =this.optionBox.label;
      }
       this.refresh.emit();    
      } 
      else {
        // Atualiza label
        if (spoke) {
          spoke.originalLabel = text;
          spoke.text = '[ChoiceBox] ' + text;
          this._levelHelperService.svcToModify(spoke);
        } 
        else {
          // Adiciona label
          const helper: SpokePlaceHelper = {
            elementId: this.optionBox.id,
            label: text,
            component: '[ChoiceBox]',
            text: '[ChoiceBox] ' + text,
          };
          this._levelHelperService.createNewLevelHelper(helper);
        }
        inputElement.value = text;
        this.optionBox.label = text;
        this._optionBoxService.svcToModify(this.optionBox);           
      }        
    }
  }

  async removeBDLabelPlaces(idAnswerBox: string, idPlaces: string) {    

    await this._roadblockService.models.forEach((roadblock) => {
      if (roadblock.spokeElementId === idAnswerBox) {
        roadblock.spokeElementId = undefined;
        this._roadblockService.svcToModify(roadblock);
      }
    });

    await this._levelHelperService.svcToRemove(idPlaces);     
  }


  async chooseAndOrCondition(event) {
    //AND = FALSE, OR = TRUE.
    if (event.target.checked == false) this.optionBox['AndOrCondition'] = 'AND';
    else this.optionBox['AndOrCondition'] = 'OR';

    await this._optionBoxService.svcToModify(this.optionBox);
    await this._optionBoxService.toSave();
  }

  
  onToggleChange(event: any): void {
    this.isModMonn = event.target.checked;
    this.isConfirm = true;  
    console.log('Toggle state:', this.isModMonn);    
  }
}
