<!-- Top branch: incoming connections from parent elements (vertical branches without arrows)-->
<div class="top-branch" [ngClass]="'IP' + inPaths?.toString()">
  <div *ngFor="let iP of inPaths; let i = index" class="branch-space"
       [ngClass]="{'has-option-roadblocks': hasOptionRoadblocks}"
       [ngStyle]="{'height': 'calc(' + getDynamicLineHeight() + 'px + 10px)',
                  'opacity': shouldHideTopBranchFromOptionLayer(i) ? '0' : '1'}">
      <div
        class="branch-line"
        [ngStyle]="{'background-color': iP ? 'white' : 'transparent',
                   'height': getDynamicLineHeight() + 'px'}">
      </div>
  </div>
</div>

<!-- Middle branch: horizontal connecting lines between option layers -->
<div class="middle-branch" [ngStyle]="getMiddleBranchContainerStyle()">
  <div *ngFor="let line of middleBranchLines; let i = index"
       class="middle-line"
       [ngStyle]="getMiddleBranchLineStyle(i)">
  </div>
</div>

<!-- Bottom branch: outgoing connections to child elements (vertical branches with arrows)-->
<div class="bottom-branch" [ngClass]="'OP' + outPaths?.toString()">
  <div *ngFor="let oP of outPaths; let i = index" class="branch-space"
       [ngClass]="{'has-option-roadblocks': hasOptionRoadblocks}"
       [ngStyle]="{'height': 'calc(' + getDynamicLineHeight() + 'px + 10px)',
                  'opacity': isFromOptionLayer ? '1' : (shouldHideBranchBasedOnSelection(i) ? '0' : '1')}">
      <div class="branch-line"
           [ngStyle]="{'height': getDynamicLineHeight() + 'px'}"></div>
      <div class="branch-arrow"></div>
  </div>
</div>
