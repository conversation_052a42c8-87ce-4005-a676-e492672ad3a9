import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/SortableListComponent';
import { ChangeDetectorRef, Component } from '@angular/core';
import { Character, HellniumMining} from 'src/app/lib/@bus-tier/models';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Button } from 'src/app/lib/@pres-tier/data';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { HellniumMiningService } from 'src/app/services/hellnium-mining.service';
import { Alert } from 'src/lib/darkcloud';
import { SpinnerService } from './../../../../../../../../../spinner/spinner.service';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-hellnium-mining-generator',
  templateUrl: './hellnium-mining-generator.component.html',
})
/**
 * Displays and edits emotion data as a list
 */
export class HellniumMiningGeneratorComponent extends SortableListComponent<HellniumMining> {
  constructor(
    private spinnerService:SpinnerService,
    _activatedRoute: ActivatedRoute,
    protected _hellniumMiningService: HellniumMiningService,
    _userSettingsService: UserSettingsService,
    protected _languageService: LanguageService,
    protected _translationService: TranslationService,
    private ref: ChangeDetectorRef,

  ) {
    super(_hellniumMiningService, _activatedRoute, _userSettingsService, 'name');
  }

  public readonly excelButtonTemplate: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  public downloadSceneryOrtography(character: Character)
  {
    this._translationService.getCharacterOrtography(character, true);
  }

  protected override lstInit()
  {

  }
  description = ""
  protected override lstAfterFetchList()
  {
    this._hellniumMiningService.models;
    if(this._hellniumMiningService.models.length === 0)
    {
      for(let l = 1; l <= 20; l++)
      {
        this._hellniumMiningService.createNewLaboratory(l);
      }
      this._hellniumMiningService.toSave();
      this.lstFetchLists();
    }
      //remove empty element that just has lablevel == 0.
      this._hellniumMiningService.models.find(blueprint => 
        {
          if(blueprint.hellniumLevel === 0)          
          this._hellniumMiningService.svcToRemove(blueprint.id)
        })
        this.description = `Showing ${this._hellniumMiningService.models .length} results`;
  }

  async onExcelPaste(): Promise<void>
  {
    this.spinnerService.setState(true)
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter(line => line);
    
    if(this.DisplayErrors(lines)) return

    for(let l = 0; l < lines.length; l++)
    {
      let line = lines[l];
      let cols = line.split(/\t/);
          

      let hellniumMining = this._hellniumMiningService.models.find(tm => tm.hellniumLevel == +(cols[0].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.')));
      if(!hellniumMining)
      {
        hellniumMining = this._hellniumMiningService.createNewLaboratory(+(cols[0].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.')));
      }
      
      if(cols[1]?.trim())
      {
        hellniumMining.souls = +(cols[1].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.'));
      }
      else
      {
        hellniumMining.souls = undefined;
      }
      if(cols[2]?.trim())
      {
        hellniumMining.time = +(cols[2].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.'));
      }
      else
      {
        hellniumMining.time = undefined;
      }
      if(cols[3]?.trim())
      {
        hellniumMining.rubies = +(cols[3].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.'));
      }
      else
      {
        hellniumMining.rubies = undefined;
      }
      if(cols[4]?.trim())
      {
        hellniumMining.storage = +(cols[4].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.'));
      }
      else
      {
        hellniumMining.storage = undefined;
      }
      if(cols[5]?.trim())
      {
        hellniumMining.production = +(cols[5].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.'));
      }
      else
      {
        hellniumMining.production = undefined;
      }

      await  this._hellniumMiningService.svcToModify(hellniumMining);
      await  this._hellniumMiningService.toSave();
      Alert.ShowSuccess('Hellnium Mining imported successfully!');

    }
    this.lstFetchLists();
    this.ref.detectChanges();
    this.spinnerService.setState(false)


  }

  DisplayErrors(array)
  {
    let count = array[0].split(/\t/)
    if(count.length < 6)
    {
      Alert.showError("Copy the HELLNIUM LEVEL column values too!")
      this.spinnerService.setState(false)
      return true
    }
    
    if(count[0] === "")
    {
      Alert.showError("You are probably copying a blank column!")
      this.spinnerService.setState(false)
      return true
    }

    return false
  }

}
