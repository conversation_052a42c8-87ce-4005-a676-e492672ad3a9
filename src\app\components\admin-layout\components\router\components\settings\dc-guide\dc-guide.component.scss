.card-container 
{
    display: flex;
    flex-direction: column;
    align-items: center;
  
    .card 
    {
      border: 1px solid #ccc;
      padding: 15px;
      margin: 5px;
      width: 50vw;
      opacity: 1;
      transition: opacity 0.5s ease-in-out;
    }
   
}

.card-header {
  height: 110px; 
  position: fixed; 
  z-index: 9999;
  width: 100%;
}

.positian-Buttons {
  width: 20%;
  margin: auto;
  position: relative;
  top: -52px;
}
.div-excel {
  position: relative;
  display: flex;
  justify-content: end;
  margin-right: 280px;
}
