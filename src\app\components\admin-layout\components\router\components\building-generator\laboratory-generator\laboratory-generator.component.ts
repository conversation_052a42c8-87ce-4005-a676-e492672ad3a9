import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/SortableListComponent';
import { Component } from '@angular/core';
import { Character, Laboratory,} from 'src/app/lib/@bus-tier/models';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Button } from 'src/app/lib/@pres-tier/data';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';

import { LaboratoryService } from 'src/app/services/laboratory.service';
import { Alert } from 'src/lib/darkcloud';
import { SpinnerService } from './../../../../../../../spinner/spinner.service';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-laboratory-generator',
  templateUrl: './laboratory-generator.component.html',
  styleUrls: ['./laboratory-generator.component.scss'],
})
/**
 * Displays and edits emotion data as a list
 */
export class LaboratoryGeneratorComponent extends SortableListComponent<Laboratory> {
  constructor(
    private spinnerService: SpinnerService,
    _activatedRoute: ActivatedRoute,
    protected _laboratoryService: LaboratoryService,
    _userSettingsService: UserSettingsService,
    protected _languageService: LanguageService,
    protected _translationService: TranslationService
  ) {
    super(_laboratoryService, _activatedRoute, _userSettingsService, 'name');
  }
  description = ""
  public readonly excelButtonTemplate: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  public downloadSceneryOrtography(character: Character) {
    this._translationService.getCharacterOrtography(character, true);
  }

  protected override lstInit() {}

  protected override lstAfterFetchList() {
    this._laboratoryService.models;
    if (this._laboratoryService.models.length == 0) {
      for (let l = 1; l <= 20; l++) {
        this._laboratoryService.createNewLaboratory(l);
      }
      this._laboratoryService.toSave();
      this.lstFetchLists();
    }

    //remove empty element that just has lablevel == 0.
    this._laboratoryService.models = this._laboratoryService.models.filter(
      (blueprint) => blueprint.labLevel !== 0
    );
    this.description = `Showing ${ this._laboratoryService.models.length} results`;
   
  }

  async onExcelPaste(): Promise<void> {
    this.spinnerService.setState(true)
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter(line => line);

    if(this.DisplayErrors(lines)) return
    
    for (let l = 0; l < lines.length; l++) {
      let line = lines[l];
      let cols = line.split(/\t/);

      let laboratory = this._laboratoryService.models.find(
        (laboratory) =>
          laboratory.labLevel ==
          +cols[0].split(' ').join('').replace('.','').replace(',','.')
      );
      if (!laboratory) {
        let labLevel = +cols[0].split(' ').join('').replace('.','').replace(',','.')
        laboratory = this._laboratoryService.createNewLaboratory(labLevel);
        
      }

      if (cols[1]?.trim()) {
        laboratory.improveTitanium = +cols[1]
          .split(' ')
          .join('')
          .split('.')
          .join('')
          .replace(',','.')
      } else {
        laboratory.improveTitanium = undefined;
      }
      if (cols[2]?.trim()) {
        laboratory.improveTime = +cols[2]
          .split(' ')
          .join('')
          .split('.')
          .join('')
          .replace(',','.')
      } else {
        laboratory.improveTitanium = undefined;
      }
      if (cols[3]?.trim()) {
        laboratory.improveRubies = +cols[3]
          .split(' ')
          .join('')
          .split('.')
          .join('')
          .replace(',','.')
      } else {
        laboratory.improveRubies = undefined;
      }
      if (cols[4]?.trim()) {
        laboratory.researchSouls = +cols[4]
          .split(' ')
          .join('')
          .split('.')
          .join('')
          .replace(',','.')
      } else {
        laboratory.researchSouls = undefined;
      }
      if (cols[5]?.trim()) {
        laboratory.researchTime = +cols[5]
          .split(' ')
          .join('')
          .split('.')
          .join('')
          .replace(',','.')
      } else {
        laboratory.researchTime = undefined;
      }
      if (cols[6]?.trim()) {
        laboratory.researchRubies = +cols[6]
          .split(' ')
          .join('')
          .split('.')
          .join('')
          .replace(',','.')
      } else {
        laboratory.researchRubies = undefined;
      }

      await  this._laboratoryService.svcToModify(laboratory);
      await  this._laboratoryService.toSave();
      Alert.ShowSuccess('Laboratory List imported successfully!');

      this.lstFetchLists();
    }
    this.spinnerService.setState(false)
  }

  DisplayErrors(array)
  {
    let count = array[0].split(/\t/)
    if(count.length < 7)
    {
      Alert.showError("Copy the LAB LEVEL column values too!")
      this.spinnerService.setState(false)
      return true
    }
    
    if(count[0] === "")
    {
      Alert.showError("You are probably copying a blank column!")
      this.spinnerService.setState(false)
      return true
    }

    return false
  }
}
