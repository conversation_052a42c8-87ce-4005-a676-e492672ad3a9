<div class="row">
  <div class="col-md-6">
    <div class="report-table-wrapper">
      <p class="category">Introduction Speeches ({{ introductorySpeeches.length }})</p>
      <table class="table report-table table-striped">
        <thead class="sticky" style="top: 0px;">
          <tr>
            <th class="th-clickable"
                (click)="sortIntroductorySpeechesByParameter('name')">Speaker</th>
            <th class="th-clickable"
                (click)="sortIntroductorySpeechesByParameter('message')">Message</th>
            <th class="th-clickable"
                (click)="sortIntroductorySpeechesByParameter('hierarchyCode')">Hierarchy Code</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let speech of introductorySpeeches;">
            <tr>
              <td class="td-20px">
                {{ (speech.speakerId | character)?.name }}
              </td>
              <td class="td-100 td-clickable"
                  (click)="access(speech)">
                <p
                   [innerHTML]="(speech.message | rpgFormatting) | highlightWord : (speech.speakerId | character)?.name : false ">
                </p>
              </td>
              <td class="td-auto center">
                {{(speech.id | area : true).hierarchyCode}}
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>

  <div class="col-md-6">
    <div class="report-table-wrapper">
      <p class="category">Omitted Options ({{ omittedOptions.length }})</p>
      <table class="table report-table table-striped">
        <thead class="sticky"
               style="top: 0px;">
          <tr>
            <th class="th-clickable"
                (click)="sortOmittedOptionsByParameter('message')">Message</th>
            <th class="th-clickable"
                (click)="sortOmittedOptionsByParameter('hierarchyCode')">Hierarchy Code</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let option of omittedOptions;">
            <tr>
              <td [innerHTML]="option.message | rpgFormatting"
                  class="td-80 td-clickable"
                  (click)="access(option)"></td>
              <td class="td-auto center">
                {{(option.id | area : true).hierarchyCode}}
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>



</div>
