<div class="sidebar-wrapper"
     style="width: 100%; overflow: hidden;">
  <div class="logo center">
    <ng-container *ngIf="checkedImportedFile; else notVerified">
      <i style="cursor: pointer"
         (click)="PromptChangeLog()">
         <img style="width: 160px;" src="assets/img/Dark-cloud-logo-preview.png"></i>
    </ng-container>
    <ng-template #notVerified>
      <img style="width: 160px;" src="assets/img/Dark-cloud-logo-preview.png">
      <i title="Version could not be verified. Please import the file data or reset the data."></i>
    </ng-template>
    <h5>{{ appVersion }}</h5>
    <h6 class="center">{{ _languageService.activeLanguage.name }}</h6>
  </div>
  <div class="scrollMenuBar">
    <ul class="nav responsive-nav">
      <li routerLinkActive="active"
          *ngFor="let menuItem of topMenuItems">
        <a [routerLink]="[menuItem.path]">
          <div class="not">
            <ng-container *ngIf="objIdsToReview[menuItem.review[0]]?.length > 0
              || objIdsToReview[menuItem.review[1]]?.length > 0
              || objIdsToReview[menuItem.review[2]]?.length > 0
              || objIdsToReview[menuItem.review[3]]?.length > 0
              || objIdsToReview[menuItem.review[4]]?.length > 0
              || objIdsToReview[menuItem.review[5]]?.length > 0">
              <div tooltip="{{menuItem.reviewTooltip}}" data-html="true" style="white-space:pre-wrap;"
              placement="right" ttWidth="250px" ttAlign="left" class="not-circle bkg-error"> </div>
            </ng-container>
          </div>
          <i class="{{menuItem.icon}}"></i>
          <p>{{menuItem.title}}</p>
        </a>
      </li>
      
    </ul>
    <ul  class="nav responsive-nav">
      <li routerLinkActive="active"
          *ngFor="let menuItem of bottomMenuItems">
          
        <a [routerLink]="[menuItem.path]">
          <div class="not">
            <ng-container  *ngIf="objIdsToReview[menuItem.review[0]]?.length > 0
              || objIdsToReview[menuItem.review[1]]?.length > 0
              || objIdsToReview[menuItem.review[2]]?.length > 0
              || objIdsToReview[menuItem.review[3]]?.length > 0
              || objIdsToReview[menuItem.review[4]]?.length > 0
              || objIdsToReview[menuItem.review[5]]?.length > 0">
                <div tooltip="{{menuItem.reviewTooltip}}" data-html="true" style="white-space:pre-wrap;"
                placement="right" ttWidth="250px" ttAlign="left" #circleOff data-html="true" style="white-space:pre-wrap;"
                placement="right" delay="100" ttWidth="300px" ttAlign="left">
                <div class="not-circle bkg-warning">
                </div>
              </div>
            </ng-container>
            <ng-template >
              <ng-container *ngIf="menuItem.reviewTooltip">              
                <div tooltip="{{menuItem.reviewTooltip}}" data-html="true" style="white-space:pre-wrap;"
                  placement="right" ttWidth="250px" ttAlign="left">
                  <div class="not-circle bkg-circle">
                  </div>
                </div>
              </ng-container>
            </ng-template>
          </div>
          <i class="{{menuItem.icon}}"></i>
          <p >{{menuItem.title}}</p>
        </a>
      </li>
    </ul>
    <br />
    <div class="d-flex justify-content-center">
        <div>
          <ul class="nav responsive-nav">
            <li routerLinkActive="active">
              <a routerLink="search">
                <i class="pe-7s-search"></i>
                <p>Search</p>
              </a>
            </li>     
          </ul> 
        </div>
      
        <div class="nav responsive-nav"  id="searcher">
          <app-searcher></app-searcher>
        </div>  
      </div>
  </div>

</div>

<div class="sidebar-footer">build: {{ buildDate }}</div>
