import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/SortableListComponent';
import { Component } from '@angular/core';
import { BlueprintArchive, Character } from 'src/app/lib/@bus-tier/models';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Button } from 'src/app/lib/@pres-tier/data';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { BlueprintArchiveService } from 'src/app/services/blueprint-archive.service';
import { Alert } from 'src/lib/darkcloud';
import { SpinnerService } from './../../../../../../../spinner/spinner.service';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-blueprint-archive-generator',
  templateUrl: './blueprint-archive-generator.component.html',
})

export class BlueprintArchiveGeneratorComponent extends SortableListComponent<BlueprintArchive> 
{
  override listName:string = 'Forge List';
  description:string = "";

  constructor(
    private spinnerService:SpinnerService,
    _activatedRoute: ActivatedRoute,
    protected _blueprintArchiveService: BlueprintArchiveService,
    _userSettingsService: UserSettingsService,
    protected _languageService: LanguageService,
    protected _translationService: TranslationService,
  ) 
  {
    super(_blueprintArchiveService, _activatedRoute, _userSettingsService, 'name');
  }
  public readonly excelButtonTemplate: Button.Templateable = 
  {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  public downloadSceneryOrtography(character: Character)
  {
    this._translationService.getCharacterOrtography(character, true);
  }

  protected override lstInit(){}

  protected override lstAfterFetchList()
  {
    this._blueprintArchiveService.models;
    if(this._blueprintArchiveService.models.length == 0)
    {
      for(let l = 1; l <= 20; l++)     
        this._blueprintArchiveService.createNewBlueprintArchive(l);
      
      this._blueprintArchiveService.toSave();
      this.lstFetchLists();
    }

    //remove empty element that just has lablevel == 0.
    this._blueprintArchiveService.models.find(blueprint => 
    {
      if(blueprint.labLevel == 0)          
        this._blueprintArchiveService.svcToRemove(blueprint.id);
    })
   this.description = `Showing ${ this._blueprintArchiveService.models.length} results`;
  }

  async onExcelPaste(): Promise<void>
  {
    this.spinnerService.setState(true);
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter(line => line);

    if(this.DisplayErrors(lines)) return
    let blueprintFields:string [] = ['improveTitanium', 'improveTime', 'improveRubies', 'researchSouls', 'researchTime', 'researchRubies'];
    
    for(let l = 0; l < lines.length; l++)
    {
      let line = lines[l];
      let cols = line.split(/\t/);
      

      let blueprintArchive = this._blueprintArchiveService.models.find(bpa => 
      {
        return bpa.labLevel == +(cols[0].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.'));
      });

      if(!blueprintArchive)
      {
        blueprintArchive = this._blueprintArchiveService.createNewBlueprintArchive(+(cols[0].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.')));   
      }

      for(let j = 1; j < blueprintFields.length; j++)
      {
        if(cols[j]?.trim())
        {
          blueprintArchive[blueprintFields[j]] = +(cols[j].split(' ')
          .join('')
          .split('.')
          .join('')
          .replace(',','.'));
        }
        else
        {
          blueprintArchive[blueprintFields[j]] = undefined;
        }
      }

      await  this._blueprintArchiveService.svcToModify(blueprintArchive);
      await  this._blueprintArchiveService.toSave();
      Alert.ShowSuccess('Forge List imported successfully!');

      this.lstFetchLists();
    }
    this.spinnerService.setState(false);

  }
  DisplayErrors(array)
  {
    let count = array[0].split(/\t/);
    if(count.length < 7)
    {
      Alert.showError("Copy the LAB LEVEL column values too!");
      this.spinnerService.setState(false);
      return true;
    }
    
    if(count[0] === "")
    {
      Alert.showError("You are probably copying a blank column!");
      this.spinnerService.setState(false);
      return true;
    }
    return false;
  }
}
