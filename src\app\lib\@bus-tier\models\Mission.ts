import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { Base } from './Base';

export class Mission
  extends Base<Data.Hard.IMission, Data.Result.IMission>
  implements Required<Data.Hard.IMission>
{
  public static generateId(index: number): string {
    return IdPrefixes.MISSION + index;
  }
  public static getSubIdFrom(otherId: string): string {
    return this.getSubId(this.generateId(0), otherId);
  }
  constructor(index: number, name: string, dataAccess: Mission['TDataAccess']) {
    super(
      {
        hard: {
          id: Mission.generateId(index),
          name,
          objectiveIds: []
        },
      },
      dataAccess
    );
  }
  public get xp(): number {
    return this.hard.xp ?? 0;
  }
  public set xp(value: number) {
    this.hard.xp = value;
  }
  protected getInternalFetch() {
    return {};
  }
  public get areaId(): string {
    return this.hard.areaId;
  }
  public set areaId(value: string) {
    this.hard.areaId = value;
  }
  public get description(): string {
    return this.hard.description;
  }
  public set description(value: string) {
    this.hard.description = value;
  }
  public get objectiveIds(): string[] {
    return this.hard.objectiveIds;
  }
  public set objectiveIds(value: string[]) {
    this.hard.objectiveIds = value;
  }
  public get name(): string {
    return this.hard.name;
  }
  public set name(value: string) {
    this.hard.name = value;
  }
  public get isReviewedName(): boolean {
    return this.hard.isReviewedName;
  }
  public set isReviewedName(value: boolean) {
    this.hard.isReviewedName = value;
  }
  public get isReviewedDescription(): boolean {
    return this.hard.isReviewedDescription;
  }
  public set isReviewedDescription(value: boolean) {
    this.hard.isReviewedDescription = value;
  }
  public get revisionCounterNameAI(): number {
    return this.hard.revisionCounterNameAI;
  }
  public set revisionCounterNameAI(value: number) {
    this.hard.revisionCounterNameAI = value;
  }
  public get revisionCounterDescriptionAI(): number {
    return this.hard.revisionCounterDescriptionAI;
  }
  public set revisionCounterDescriptionAI(value: number) {
    this.hard.revisionCounterDescriptionAI = value;
  }
}
