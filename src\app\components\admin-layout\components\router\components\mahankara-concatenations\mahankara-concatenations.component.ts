import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { MahankaraConcatenations } from 'src/app/lib/@bus-tier/models';
import { MahankaraConcatenationsService } from 'src/app/services';
import { Alert } from 'src/lib/darkcloud';

@Component({
  selector: 'app-mahankara-concatenations',
  templateUrl: './mahankara-concatenations.component.html',
  styleUrls: ['./mahankara-concatenations.component.scss']
})
export class MahankaraConcatenationsComponent implements OnChanges, OnInit {
  @Output() descriptionOutput = new EventEmitter<string>();
  @Input() copyExcelMahankaraConcatenations: string[];
  listConcatenations: MahankaraConcatenations[] = [];
  // Flag para controle de inicialização do copyExcelRepetition
  isFirstChange = true;

  constructor(
    private _mahankaraConcatenationsService: MahankaraConcatenationsService
  ) {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes['copyExcelMahankaraConcatenations']) {
      if (this.isFirstChange) {
        // Ignorar a primeira alteração no ciclo de vida
        this.isFirstChange = false;
      } else if (
        this.copyExcelMahankaraConcatenations &&
        this.copyExcelMahankaraConcatenations.length > 0
      ) {
        this.onExcelPaste();
      }
    }
  }

  async ngOnInit(): Promise<void> {
    this._mahankaraConcatenationsService.toFinishLoading();
    this.listConcatenations = this._mahankaraConcatenationsService.models;
    this.descriptionOutput.emit(
      `Showing ${this.listConcatenations.length} results`
    ); 
  }
  async onExcelPaste() {
    this._mahankaraConcatenationsService.models = [];
    this._mahankaraConcatenationsService.toSave();

    // Verifica se `this.copyExcelMahankaraBehavior` contém dados
    if (
      !this.copyExcelMahankaraConcatenations ||
      this.copyExcelMahankaraConcatenations.length === 0
    ) {
      Alert.showError('No data found in the copied Excel content.');
      return this.ngOnInit();
    }

    const expectedColumns = 5;

    //Verificar se todas as linhas possuem o número correto de colunas
    const invalidColumnRows = this.copyExcelMahankaraConcatenations.filter(
      (row) => {
        const cells = row.split('\t'); // O '\t' dividide em células - O delimitador \t é para tabulação (comum em colagens do Excel)
        return cells.length !== expectedColumns;
      }
    );

    if (invalidColumnRows.length > 0) {
      Alert.showError(
        `The number of columns does not match the expected count (${expectedColumns}). Please check the data.`
      );
      return this.ngOnInit();
    }

    this.copyExcelMahankaraConcatenations.forEach((row, index) => {
      const cells = row.split('\t'); // Divide a linha em células
      this._mahankaraConcatenationsService.createNewMahankaraConcatenations(cells);
    });

    this.copyExcelMahankaraConcatenations = [];
    Alert.ShowSuccess('Groupings imported successfully!');
    this.ngOnInit();
  }

  changeMahankaraValue(rowIndex: number, name: string, newValue: string, postionSpeech?: number) {

    if (name === 'order') {
      this.listConcatenations[rowIndex].order = newValue;
    } else if (name === 'concatenations') {
      this.listConcatenations[rowIndex].concatenations[postionSpeech] = newValue;
    }
    this._mahankaraConcatenationsService.svcToModify(this.listConcatenations[rowIndex]);
  }
}

