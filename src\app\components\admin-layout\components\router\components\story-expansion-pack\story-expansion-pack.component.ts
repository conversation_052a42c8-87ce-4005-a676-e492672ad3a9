import { Component, OnDestroy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { StoryExpansionPkg } from 'src/app/lib/@bus-tier/models/StoryExpansionPkg';
import { AreaService, UserSettingsService } from 'src/app/services';
import { LanguageService } from 'src/app/services/language.service';
import { RoadBlockService } from 'src/app/services/road-block.service';
import { SortingService } from 'src/app/services/sorting.service';
import { StoryExpansionPkgService } from 'src/app/services/story-expansion-pkg.service';
import { TranslationService } from 'src/app/services/translation.service';
import { SpinnerService } from 'src/app/spinner/spinner.service';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';

@Component({
  selector: 'app-story-expansion-pack',
  templateUrl: './story-expansion-pack.component.html',
  styleUrls: ['./story-expansion-pack.component.scss']
})
export class StoryExpansionPackComponent extends TranslatableListComponent<StoryExpansionPkg> implements OnDestroy
{
    public override listName = 'Story Expansion Pack List';
    areasList = [];
    storyPack = [];
    desc: string;    
    loadingSpinner = false;
    timeout:any;

    constructor
    (
        public readonly router: Router,
        _activatedRoute: ActivatedRoute,
        private _sortingService : SortingService,
        _userSettingsService: UserSettingsService,
        private _roadblockService: RoadBlockService,
        protected override _languageService: LanguageService,
        protected _storyExpansionService: StoryExpansionPkgService,
        protected override _translationService: TranslationService,
        protected _areaService: AreaService,
        private spinnerService: SpinnerService,
    ){
        super(_storyExpansionService, _activatedRoute, _userSettingsService, 'name', _translationService, _languageService);
    }
    
    protected override async lstInit() 
    {
        await this._storyExpansionService.toFinishLoading();
        await this._areaService.toFinishLoading();
        

        if(this._storyExpansionService.models.length > 0) 
        {
            this.storyPack = [];
            this._storyExpansionService.models.filter(x=> this.storyPack.push(x))
        }

        this.areasList = this._areaService.models
        this.lstFetchLists();     
        this.desc = `Showing ${this.storyPack.length} results`;
    }

    access(pack: StoryExpansionPkg)
    {
        this.spinnerService.setState(true);
        let levelId = this._roadblockService.getLevelId(pack.level);
        let dialogueId = this._roadblockService.getDialogueId(pack.level);
        if(pack.type == 0)
        {
            if(pack.level.split(".")[0].includes("ML"))
            {
                this.router.navigate(['microloops'], { fragment: levelId });
            }
            else
            {
                this.timeout = setTimeout(() => {  
                    this.spinnerService.setState(false);                            
                },400);  
                this.router.navigate(['levels'], { fragment: levelId })
            }
        }
        else if(pack.type == 1)
        {
            if(pack.level.split(".")[0].includes("ML"))
            {
                this.router.navigate(['microloops/' + levelId + '/dialogues/' + dialogueId]);
            }
            else
            {            
             this.timeout = setTimeout(() => {  
                this.spinnerService.setState(false);                            
          },400);  
          this.router.navigate(['levels/' + levelId + '/dialogues/' + dialogueId]);   
           }
        }
    }

    invertAlphabeticalOrder: boolean = true;
    sortListByAlphabeticalOrder(parameter: string)
    {
        this.storyPack.sort((a, b) => 
        {
            const aValue = a.hard[parameter] ?? '';
            const bValue = b.hard[parameter] ?? '';

            return this.invertAlphabeticalOrder ? bValue?.localeCompare(aValue) : aValue?.localeCompare(bValue);
        });
        this.invertAlphabeticalOrder = !this.invertAlphabeticalOrder;
    }

    invertAreaOrder: boolean = true;
    sortListByArea()
    {
        this.storyPack.sort((a, b) =>
        {
            return this._sortingService.sortArrayByLevelId(this.invertAreaOrder, a.hard.level, b.hard.level);
        })
        this.invertAreaOrder = !this.invertAreaOrder;
    }

    invertIdOrder: boolean = true;
    sortListById()
    {
        this.storyPack.sort((a, b) =>
        {
            return this._sortingService.sortArrayByAlphanumericValue(this.invertIdOrder, a.hard.id, b.hard.id)
        })
        this.invertIdOrder = !this.invertIdOrder;
    }
    
    invertTypeOrder: boolean = true;
    sortListByType()
    {
        this.storyPack.sort((a,b) =>
        {
            return this.invertTypeOrder ? b.hard.type - a.hard.type : a.hard.type - b.hard.type;
        });
        this.invertTypeOrder = !this.invertTypeOrder;
    }

    MyChange(story:StoryExpansionPkg, name, value)
    {
        this.lstOnChange(story, name, value);
        story.hard.name = value;
        for(let i = 0; i < this._storyExpansionService.models.length; i++)
        {
            if(this._storyExpansionService.models[i].level == story.level)
            {   
                this._storyExpansionService.models[i].name = value;
                this._storyExpansionService.svcToModify(this._storyExpansionService.models[i]);
                break;
            }
        }
    }

    ngOnDestroy(): void {
       // clearImmediate(this.timeout);
    }

    nameChange(storyExpansion:StoryExpansionPkg, name: string, value: string) {
        storyExpansion.isReviewedName = false;
        storyExpansion.revisionCounterNameAI = 0;
        this.lstOnChange(storyExpansion, 'name', value);
      }
    
      descriptionChange(storyExpansion:StoryExpansionPkg, description: string, value: string) {
        storyExpansion.isReviewedDescription = false;
        storyExpansion.revisionCounterDescriptionAI = 0;
        this.lstOnChange(storyExpansion, 'description', value);
      }
}
