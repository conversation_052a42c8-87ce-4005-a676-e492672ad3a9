<div class="row">
    <div class="col-md-12">
        <table class="table report-table table-stripped">
            <thead class="sticky" style="top: 0px;">
            <tr>
                <th class="th-clickable">Type</th>
                <th
                class="th-clickable"
                (click)="sorting.sortPinMarkers(sorting.byLocation)">Location</th>
            </tr>
            </thead>
            <tbody>
                <ng-container *ngFor="let marker of restartMarkers">
                    <tr
                    [title]="marker.id"
                    class="tr-clickable"
                    (click)="access(marker.id)">
                        <td class="td-auto">{{ marker.type | markerTypeName}}</td>
                        <td class="td-100">{{([marker.id] | location).length > 0 ? ([marker.id] | location) : 'Ophan'}}               
                    </tr>
                </ng-container>
            </tbody>
        </table>
    </div>
</div>