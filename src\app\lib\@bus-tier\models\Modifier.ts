import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { Base } from './Base';

export class Modifier  extends Base<Data.Hard.IModifier, Data.Result.IModifier> implements Required<Data.Hard.IModifier>
{
  public static generateId(index: number, microloop?: boolean): string 
  {
    if(microloop)
    {
      return Modifier.generateMicroloopContainerId(index);
    }
    return IdPrefixes.MODIFIER + index;
  }
  public static generateMicroloopContainerId(index: number): string
  {
    return IdPrefixes.MICROLOOP + index;
  }

  public static getSubIdFrom(otherId: string, microloop?: boolean): string 
  {
    return Base.getSubId(this.generateId(0, microloop), otherId);
  }

  constructor( index: number, skill:string, dataAccess?: Modifier['TDataAccess'], microloop?: boolean
  ) 
  {
    super(
      {
        hard: 
        {
          id: Modifier.generateId(index,microloop),
          skill,
        },
      },
      dataAccess
    );
  }

  protected getInternalFetch() 
  {
    return {};
  }

  public get modifierLevel(): number 
  {
    return this.hard.modifierLevel;
  }
  public set modifierLevel(value: number) 
  {
    this.hard.modifierLevel = value;
  }
  public get skill(): string 
  {
    return this.hard.skill;
  }
  public set skill(value: string) 
  {
    this.hard.skill = value;
  }

  public get acronym(): string 
  {
    return this.hard.acronym;
  }

  public set acronym(value: string) 
  {
    this.hard.acronym = value;
  }

  public get description(): string 
  {
    return this.hard.description;
  }

  public set description(value: string) 
  {
    this.hard.description = value;
  } 
  public get revisionCounterSkillAI(): number {
    return this.hard.revisionCounterSkillAI;
  }
  public set revisionCounterSkillAI(value: number) {
    this.hard.revisionCounterSkillAI = value;
  }
  public get revisionCounterDescriptionAI(): number {
    return this.hard.revisionCounterDescriptionAI;
  }
  public set revisionCounterDescriptionAI(value: number) {
    this.hard.revisionCounterDescriptionAI = value;
  }
  public get isReviewedSkill(): boolean {
    return this.hard.isReviewedSkill;
  }
  public set isReviewedSkill(value: boolean) {
    this.hard.isReviewedSkill = value;
  }
  public get isReviewedDescription(): boolean {
    return this.hard.isReviewedDescription;
  }
  public set isReviewedDescription(value: boolean) {
    this.hard.isReviewedDescription = value;
  }
}
