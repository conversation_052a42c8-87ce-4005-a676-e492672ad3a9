.card-header-wrapper {
  padding: 15px;
  display: flex;
  align-items: flex-start; /* Align all flex items to the top */
  position: relative;
  min-height: 50px; /* Ensure consistent height for button positioning */
}

.card-header-content {
  display: block;
  margin-left: 10px;
  margin-right: 15px;
  width: 35%;
}

.card-header-tooltip {
  display: block;
  margin-left: 33%;
  margin-right: 33%;
  margin-bottom: 50px;
  color:white !important;
  background-color: black;
  padding:30px;
  z-index: 999;
  border-radius: 10px;
}
.card-title-desccription {
  width: 100%;
  padding-bottom: 30px;
}

.text-description {
  height: 30px;
  white-space: normal;
  word-wrap: break-word;
  overflow-x: hidden;
  overflow-y: auto;
  width: 100%;
  max-width: 100%;
  min-height: 100% !important;
}

.btnSubContext {
  position: absolute;
  top: 25px;
  right: 120px; 
}

.textTitles {
  display: flex;
  width: 40%;
  margin-left: 60px;
}
.c-nameRarity {
  margin-left: 5px;
  padding: 6px; 
  margin-top: -6px;
  border-radius: 5px;
}

/* Fixed button positioning for item details header */
.item-details-buttons {
  position: absolute;
  top: 15px; /* Consistent positioning regardless of text content */
  right: 13px;
  z-index: 10;
}

.item-details-buttons .btn-group {
  display: flex;
  padding-top: 6px;
}

.item-details-buttons .btn {
  margin-left: 5px;
}

.item-details-buttons .btn:first-child {
  margin-left: 0;
}
