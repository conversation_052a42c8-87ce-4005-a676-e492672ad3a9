import { ChangeDetectorRef, Component, Input } from '@angular/core';
import { Area } from 'src/app/lib/@bus-tier/models';
import { Drop } from 'src/app/lib/@bus-tier/models/Drop';
import { Button } from 'src/app/lib/@pres-tier/data';
import { AreaService, ChestService, UserSettingsService } from 'src/app/services';
import { DropService } from 'src/app/services/drop.service';
import { TranslationService } from 'src/app/services/translation.service';
import { SpinnerService } from 'src/app/spinner/spinner.service';
import { Alert } from 'src/lib/darkcloud';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc';
import { FILTER_SUFFIX_PATH } from '../../../../../../../../lib/darkcloud/angular/dsadmin/constants/others';
import { ActivatedRoute } from '@angular/router';


@Component({
  selector: 'app-area-drops-generator',
  templateUrl: './area-drops-generator.component.html',
  styleUrls: ['./area-drops-generator.component.scss'],
})
export class AreaDropsGeneratorComponent extends SortableListComponent<Drop>
{
  @Input() slot0: boolean = false;
  @Input() isAreaDrops: boolean = false;
  hasDropdown = true;
  public elements = ['Code Block', 'Gold', 'Ichor', 'Ruby'];
  sortNameOrder = +1; 
  dropsList: Drop[] = [];
  public selectedElement;
  description:string = '';
  areas: Area[];
  areasOrderValue: Area[] = [];
  variance: string = '0';
  public activeTab: string;
  activeLanguage = 'PTBR';
  subText: string;


  constructor(
    private spinnerService: SpinnerService,
    _activatedRoute: ActivatedRoute,
    protected _dropService: DropService,
    private _areaService: AreaService,
    _userSettingsService: UserSettingsService,
    protected _translationService: TranslationService,
    private _chestService: ChestService,
    private ref: ChangeDetectorRef
  ) 
  {
    super(_dropService, _activatedRoute, _userSettingsService, 'name');
  }

  public readonly excelButtonTemplate: Button.Templateable = 
  {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  override async ngOnInit() 
  {

    const tab = localStorage.getItem(
      `tab-AreaDropsGeneratorComponent${FILTER_SUFFIX_PATH}`
    );
   // this.activeTab = 'Code Block';
   // if (this.slot0) this.elements = ['Gold', 'Ruby'];
/* 
    this.subText = tab;
    this.activeTab = tab;  
*/
  
    this.selectedElement = this.elements[0]; 
    
    await this.checkElement();
    await this._areaService.toFinishLoading();
    this.areas = this._areaService.models.filter((a) => !!a.order).sort((a, b) => a.order - b.order);
    this.areas.forEach((area) => 
    {
      if (!this.areasOrderValue.map((m) => m.order).includes(area.order)) 
      {
        this.areasOrderValue?.push(area);
      }
    });

    this.areas = this.areasOrderValue;
    this.ref.detectChanges();
  }

   public async checkElement() 
  {
    this.dropsList = [];
    await this._dropService.toFinishLoading();
    for(let i = 0; i < this._dropService.models.length; i++)
    {
      for(let j = 0; j < this._chestService.models.length; j++)
      {
        if(this._dropService.models[i].element == this.selectedElement &&
          this._dropService.models[i].slot0 == this.slot0 &&
          this._dropService.models[i].variance == this.variance &&
          this._chestService.models[j].acronym != undefined &&
          this._chestService.models[j].acronym != '' &&
          this._chestService.models[j].acronym == this._dropService.models[i].type
          )
        {
          this.dropsList.push(this._dropService.models[i]);
          break;
        }
      }
    }

    if (this.dropsList.length == 0) 
    {
      this._chestService.toFinishLoading();
      for (let i = 0; i < this._chestService.models.length; i++) 
      {
        if(this._chestService.models[i].acronym != undefined && 
          this._chestService.models[i].acronym != '')
          {
            let cbd = new Drop(
              this._dropService.svcNextIndex(),
              this._chestService.models[i].acronym,
              this.selectedElement,
              this.slot0,
              this.variance,
              this._userSettingsService
            );
            this._dropService.srvAdd(cbd);
            this.dropsList.push(cbd);
          }
      }
    }
    else
    {
      this._chestService.toFinishLoading();
      for(let i = 0; i < this._chestService.models.length; i++)
      {
        for(let j = 0; j < this.dropsList.length; j++)
        {
          if(this._chestService.models[i].acronym != undefined &&
            this._chestService.models[i].acronym != '' &&
            this.dropsList[j].type == this._chestService.models[i].acronym) break;          
          if(j == this.dropsList.length-1 &&
            this._chestService.models[i].acronym != undefined &&
            this._chestService.models[i].acronym != '')
          {
            let cbd = new Drop(
              this._dropService.svcNextIndex(),
              this._chestService.models[i].acronym,
              this.selectedElement,
              this.slot0,
              this.variance,
              this._userSettingsService
            );
            this._dropService.srvAdd(cbd);
            this.dropsList.push(cbd);
            this._dropService.toSave();
          }        
        }
      }
    }

    this. lineupOrderTypeDrops();
    this.description = `Showing ${this.dropsList.length} results`;
  }

  lineupOrderTypeDrops() 
  {
  this.sortNameOrder *= +1;
    this.dropsList.sort((a, b) => 
    {  
      return this.sortNameOrder * a.type.localeCompare(b.type);
    });
  }

  public switchToTab(tab: string) {
    this.subText = tab;
    this.activeTab = tab;
    localStorage.setItem(
      `tab-AreaDropsGeneratorComponent${FILTER_SUFFIX_PATH}`,
      this.activeTab
    );
    
    this.selectedElement = tab;
    this.checkElement();
    this.ref.detectChanges();
  }

  changeDropAmount(drop: Drop, amount: string, index: number) 
  { 
    drop.amount[index] = amount == '' ? undefined : +amount;
    this._dropService.svcToModify(drop);
    this._dropService.toSave();
    this.checkElement();
  }

  GetAmountValue(drop: Drop, index: number) 
  {   
    if (!drop.amount) drop.amount = [];    
    return drop.amount[index];
  }

  changeElement(element: string) 
  {
    this.selectedElement = element;
    this.checkElement();
    this.ref.detectChanges();
  }

  async onExcelPaste(): Promise<void> {
    this.spinnerService.setState(true);
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter(line => line);

    if (lines.length === 0) {
        this.displayErrors([`The copied content is empty.`]);
        this.spinnerService.setState(false);
        return;
    }

    const excelHeaders = lines[0].split(/\t/).map(header => header.trim());
   // const expectedColumnsCount = this.dropsList.length + 1; // +1 para a coluna drop.type
    const dropTypeErrors: string[] = []; // Armazena tipos que não foram encontrados
    const duplicateDropTypes: string[] = []; // Armazena tipos duplicados

    const expectedColumnsCount = this.areas.length + 1; // +1 para a coluna do tipo (drop.type)

    // Verifica se o número de colunas no Excel é o mesmo que o esperado
    if (excelHeaders.length !== expectedColumnsCount) {
        this.displayErrors([`The copied data has ${excelHeaders.length} columns, but ${expectedColumnsCount} were expected.`]);
        this.spinnerService.setState(false);
        return;
    }

    const dropTypeSet = new Set<string>(); // Para verificar duplicações

    lines.slice(0).forEach((line, rowIndex) => {
        const cols = line.split(/\t/).map(col => col.trim());
        const dropTypeName = cols[0];

        // Verifica se há duplicatas na primeira coluna
        if (dropTypeSet.has(dropTypeName)) {
            duplicateDropTypes.push(dropTypeName);
        } else {
            dropTypeSet.add(dropTypeName);
        }

        // Procura o drop.type na lista this.dropsList
        const dropObj = this.dropsList.find(drop => drop.type === dropTypeName);
        if (!dropObj) {
            dropTypeErrors.push(dropTypeName);  // Armazena o nome do drop.type que não foi encontrado
        } else {
            // Copia o conteúdo numérico para o objeto drop
            cols.slice(1).forEach((colValue, colIndex) => {
                dropObj.amount = dropObj.amount || []; 

                if (colValue === "") {
                    dropObj.amount[colIndex] = undefined; 
                } else if (!isNaN(parseFloat(colValue))) {
                    dropObj.amount[colIndex] = parseFloat(colValue);         
                }
            });
        }
    });

    // Exibe mensagens de erro se houver problemas
    if (dropTypeErrors.length > 0) {
        this.displayErrors([`Drop type names not found in the system: ${dropTypeErrors.join(', ')}`]);
    }
    if (duplicateDropTypes.length > 0) {
        this.displayErrors([`Duplicate drop type names found: ${duplicateDropTypes.join(', ')}`]);
    }

    // Se não houver erros, salva os dados e atualiza a interface
    if (dropTypeErrors.length === 0 && duplicateDropTypes.length === 0) {

        //this._dropService.models = this.dropsList;
        this.dropsList.forEach((x)=> this._dropService.svcToModify(x));    
        await this._dropService.toSave();     
        this.lstFetchLists();
        this.ref.detectChanges();
        Alert.ShowSuccess('Area Drop list copied successfully!');
        this.lstInit();
    }

    this.spinnerService.setState(false);
}

displayErrors(errors: string[]): boolean {
  if (errors.length) {
      this.spinnerService.setState(false);
      Alert.showError(`${errors.join('\n')}`);
      return true;
  }
  return false;
}


}
