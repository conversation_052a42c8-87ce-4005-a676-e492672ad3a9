import { Component, Input, OnInit } from '@angular/core';
import { Animatic, Area, Cutscene, Event, Item, Mission, StoryBox, Tutorial, Video } from 'src/app/lib/@bus-tier/models';
import { CharacterService, LevelService, ReviewService } from 'src/app/services';
import { EventService } from 'src/app/services/event.service';
import { ItemClassService } from 'src/app/services/item-class.service';
import { ItemService } from 'src/app/services/item.service';
import { MicroloopContainerService } from 'src/app/services/microloop-container.service';
import { MicroloopService } from 'src/app/services/microloop.service';
import { MissionService } from 'src/app/services/mission.service';
import { ObjectiveService } from 'src/app/services/objective.service';
import { TutorialService } from 'src/app/services/tutorial.service';
import { VideoService } from 'src/app/services/video.service';
import { EventParameters, getRequirementsByType, invalidRequirementsByEventTypes } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard';
import { EventCategory, EventCategoryToType, EventType, EventTypeToCategory, VideoType } from 'src/lib/darkcloud/dialogue-system';
import { sortData, sortValues } from 'src/lib/others';
import { Review } from 'src/lib/darkcloud';
import { EasyMVC } from 'src/lib/darkcloud/angular';
import { ActivatedRoute, Router } from '@angular/router';

interface Itemlevel {
  id: string;
  name: string;
  typeItem: any;
  givenAt?: string[];
  receivedAt?: string[];
  tradedAt?: string[];
}

interface ItemGroup {
  name: string;
  id: string;  
  itemIds: Itemlevel[];
}


@Component({
  selector: 'tr[app-event]',
  templateUrl: './event.component.html',
  styleUrls: ['./event.component.scss']
})

export class EventComponent implements OnInit 
{
  @Input() index: number;
  @Input() storyBox: StoryBox;
  @Input() eventId: string;
  @Input() toMove: (event: Event, transpose: number) => void;
  @Input() toRemove: (event: Event) => void;
  @Input() preloadedMissionsOfArea: Mission[];
  @Input() preloadedMissions: Mission[];


  EventType = EventType;
  VideoType = VideoType;
  EventCategory = EventCategory;

  EventCategoryToType = EventCategoryToType;
  EventTypeToCategory = EventTypeToCategory;
  public event: Event;
  public isRequired: { [requirement: string]: boolean } = {};
  displayPreloadedMissions: Mission[] = [];
  currentLevel = undefined;
  countClassItem?: number;
  
  public missionObjectives = [];
  public preloadedVideos: Video[];
  public preloadedTutorials: Tutorial[];
  public preloadedItems: Item[];
  public preloadedMicroloopContainers: Area[];
  public preloadedEventTypes: EventType[];
  public isPercentage: boolean = false;
  public isMicroloop: boolean = false;
  public itemGroups:ItemGroup[] = [];

  constructor(
    private _eventService: EventService,
    private _missionService: MissionService,
    private _objectiveService: ObjectiveService,
    private _tutorialService: TutorialService,
    private _itemService: ItemService,
    private _itemClassService: ItemClassService,
    private _videoService: VideoService,
    private _characterService: CharacterService,
    private _microloopContainerService: MicroloopContainerService,
    private _microloopService: MicroloopService,
    private _activatedRoute: ActivatedRoute,
    private _router: Router,
    private _levelService: LevelService,
    private _reviewService: ReviewService,
  ) {}

  ngOnInit(): void 
  {
    let id = this._router.url.split("/")[2];
    this.currentLevel = this._levelService.svcFindById(id);
    if(!this.currentLevel) this.currentLevel = this._microloopService.svcFindById(id);

    this.event = this._eventService.svcCloneById(this.eventId); 
    this.preloadedEventTypes = this._eventService.eventTypes;
       this.preloadedVideos = 
      [].concat(sortData(this._videoService.models.map((v) => (Cutscene.isCutscene(v) ? v : undefined)).filter((t) => t), 'name'))
      .concat(sortValues(this._videoService.models.map((v) => (Animatic.isAnimatic(v) ? v : undefined)).filter((t) => t),
          (anim) => this._characterService.svcFindById(anim.characterId)?.name, 1));

    this.preloadedMissions = sortData(this._missionService.models, 'name');
    this.displayPreloadedMissions = this.preloadedMissions;
    this.preloadedTutorials = sortData(this._tutorialService.models, 'name');
     this.preloadedItems = sortData(this._itemService.models, 'name');     
     this.preloadedMicroloopContainers = sortData(this._microloopContainerService.models, 'name')
    this.getItemClassesLevels();
    this.loadRequirements();
    this.loadObjectives();

    if(this._activatedRoute.snapshot.url[1].path.includes('ML'))
    {
      this.isMicroloop = true;
    }
    else if(this.EventTypeToCategory[this.event.type] == EventCategory.LOOP)
    {
      this.event.type = EventType.LINK_MICROLOOP; 
      this.onChange();
    }

    if(this.event.isPercentage == true)
      this.isPercentage = this.event.isPercentage;
  }

  //Select items by class
  getItemClassesLevels(){
    this.itemGroups = sortData(this._itemClassService.models.map((item)=>{
      //checks if it contains to define the class
      const valoresEncontrados = this._itemService.models.filter((op)=> item.itemIds.find((x)=> x == op.id))
       const _itemIds:Itemlevel[] = valoresEncontrados.map((x)=>({id:x.id,name:x.name,typeItem: (x?.type != undefined ? x.type.toLocaleString() : x.type)}));
          _itemIds.filter((i) => {       
            _itemIds.forEach((a) => {
              a.givenAt = this.validatesItemInUse(i).givenAt;
              a.receivedAt = this.validatesItemInUse(i).receivedAt;
              a.tradedAt = this.validatesItemInUse(i).tradedAt;                
            });            
         });           
   
      return {id:item.id,name:item.name,itemIds:sortData(_itemIds, 'name')}
    }), 'name');   
  }

  //validates if item is used
  validatesItemInUse(obj: Itemlevel): Review.Result {

    if (!obj) return null;  
    let reviewsItem = this._reviewService.reviewResults[obj.id];
    return reviewsItem;
  } 

  //Validates in which mission it is used and quantity.
  validatesMissionInUse(obj: EasyMVC.IModel): Review.Result {
    if (!obj) return null;  
    let reviewsItem = this._reviewService.reviewResults[obj.id]; 
    return reviewsItem;
  } 
 
  public loadObjectives() 
  {
    if (!this.event.missionId) return;
    let temp = [];
    this._missionService.models.forEach(mission => 
    {
      if(mission != undefined && mission.id == this.event.missionId)
      {
        temp.push(mission);
      }
    })
 
    for(let i = 0; i < temp[0].objectiveIds.length; i++)
    {
      if(temp[0].objectiveIds[i] != undefined)
        this.missionObjectives.push(this._objectiveService.svcFindById(temp[0].objectiveIds[i]));     
    }

    for(let i = 0; i < this.missionObjectives.length; i++)
    {
      if(this.missionObjectives[i]?.description == undefined)
      {
        this._objectiveService.svcToRemove(this.missionObjectives[i]?.id);
      }
    }

  }

  public loadRequirements() 
  {
    const type = this.event.type.toLocaleString(); 
    if (this.event.type === undefined) return;
    this.isRequired = {};
    getRequirementsByType(invalidRequirementsByEventTypes, this.event.type)?.forEach((param) => (this.isRequired[param.key] = true));  
  }

  public async onChange() 
  {  
    if(+this.event.type == 12)
    {
      this.currentLevel.removeSpecialItemBeforeGrind = true;
    }
    else if(this.verifyRemoveSpecialItemBeforeGrind() <= 1)
    {
      this.currentLevel.removeSpecialItemBeforeGrind = false;
    }  
  
    this._levelService.svcToModify(this.currentLevel);
    this._eventService.svcToModify(this.event);
  }

  verifyRemoveSpecialItemBeforeGrind(): number
  {
    let counter = 0; 
    for(let i = 0; i < this._eventService.models.length; i++)
    {
      if(+this._eventService.models[i].type == 12 && this._eventService.models[i]?.id?.split('.')[0] == this.currentLevel?.id?.split('.')[0])
      {
        counter = counter + 1;
        if(counter >= 1) break;
      }
    }
    return counter;
  }

  public async onChangeParameterText<Key extends keyof EventParameters>(
    key: 'amount',
    value: string
  ) 
  {
    this.event[key] = parseInt(value, 10);

    //if(this.isPercentage && this.event.amount > 100) this.event.amount = 100;
    if (this.event.amount == null) this.event.amount = undefined;
    
    this.event.isPercentage = this.isPercentage
    await this._eventService.svcToModify(this.event);
    await this._eventService.toSave();
  }

  public async onChangeToPercentage()
  {
    this.isPercentage = !this.isPercentage;
    //if(this.isPercentage && this.event.amount > 100) this.event.amount = 100;
     if (this.event.amount == null) this.event.amount = undefined;
     await this._eventService.toSave();
    this.event.isPercentage = this.isPercentage;
    await this._eventService.svcToModify(this.event);
   
  }

  getEventCategory(event: Event)
  {
    let type = event.type;
    return EventTypeToCategory[+type];
  }

}