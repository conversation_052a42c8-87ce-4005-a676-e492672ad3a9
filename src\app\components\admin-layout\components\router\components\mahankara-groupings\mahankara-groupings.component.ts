import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { MahankaraGroupings } from 'src/app/lib/@bus-tier/models';
import { MahankaraGroupingsService } from 'src/app/services/mahankaraGroupings.service';
import { Alert } from 'src/lib/darkcloud';

@Component({
  selector: 'app-mahankara-groupings',
  templateUrl: './mahankara-groupings.component.html',
  styleUrls: ['./mahankara-groupings.component.scss']
})
export class MahankaraGroupingsComponent implements OnChanges, OnInit {
  @Output() descriptionOutput = new EventEmitter<string>();
  @Input() copyExcelMahankaraGroupings: string[];
  titles = ['Index Grouping', 'Groupings', 'Description'];
  listGroupings: MahankaraGroupings[] = [];
  // Flag para controle de inicialização do copyExcelRepetition
  isFirstChange = true;

  constructor(
    private _mahankaraGroupingsService: MahankaraGroupingsService
  ) {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes['copyExcelMahankaraGroupings']) {
      if (this.isFirstChange) {
        // Ignorar a primeira alteração no ciclo de vida
        this.isFirstChange = false;
      } else if (
        this.copyExcelMahankaraGroupings &&
        this.copyExcelMahankaraGroupings.length > 0
      ) {
        this.onExcelPaste();
      }
    }
  }

  async ngOnInit(): Promise<void> {
    this._mahankaraGroupingsService.toFinishLoading();
    this.listGroupings = this._mahankaraGroupingsService.models;
    this.descriptionOutput.emit(
      `Showing ${this.listGroupings.length} results`
    ); 
  }
  async onExcelPaste() {
    this._mahankaraGroupingsService.models = [];
    this._mahankaraGroupingsService.toSave();

    // Verifica se `this.copyExcelMahankaraBehavior` contém dados
    if (
      !this.copyExcelMahankaraGroupings ||
      this.copyExcelMahankaraGroupings.length === 0
    ) {
      Alert.showError('No data found in the copied Excel content.');
      return this.ngOnInit();
    }

    const expectedColumns = this.titles.length + 4;

    //Verificar se todas as linhas possuem o número correto de colunas
    const invalidColumnRows = this.copyExcelMahankaraGroupings.filter(
      (row) => {
        const cells = row.split('\t'); // O '\t' dividide em células - O delimitador \t é para tabulação (comum em colagens do Excel)
        return cells.length !== expectedColumns;
      }
    );

    if (invalidColumnRows.length > 0) {
      Alert.showError(
        `The number of columns does not match the expected count (${expectedColumns}). Please check the data.`
      );
      return this.ngOnInit();
    }

    this.copyExcelMahankaraGroupings.forEach((row, index) => {
      const cells = row.split('\t'); // Divide a linha em células
      this._mahankaraGroupingsService.createNewMahankaraGroupings(cells);
    });

    this.copyExcelMahankaraGroupings = [];
    Alert.ShowSuccess('Groupings imported successfully!');
    this.ngOnInit();
  }

  changeMahankaraValue(rowIndex: number, name: string, newValue: string, postionSpeech?: number) {

    if (name === 'indexGrouping') {
      this.listGroupings[rowIndex].indexGrouping = newValue;
    } else if (name === 'groupings') {
      this.listGroupings[rowIndex].groupings = newValue;
    } else if (name === 'description') {
      this.listGroupings[rowIndex].description = newValue;
    } else if (name === 'speechCategory') {
      this.listGroupings[rowIndex].speechCategory[postionSpeech] = newValue;
    }
    this._mahankaraGroupingsService.svcToModify(this.listGroupings[rowIndex]);
  }
}

