import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { MahankaraBehavior } from 'src/app/lib/@bus-tier/models';
import { Button } from 'src/app/lib/@pres-tier/data';
import {
  CategoriesXStressStatesService,
  MahankaraBehaviorTableService,
  MahankaraCategoriesService,
  MahankaraConcatenationsService,
  MahankaraGroupingsService,
  UserSettingsService,
} from 'src/app/services';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { FILTER_SUFFIX_PATH } from 'src/lib/darkcloud/angular/dsadmin/constants/others';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';

@Component({
  selector: 'app-mahankara-dialogs',
  templateUrl: './mahankara-dialogs.component.html',
  styleUrls: ['./mahankara-dialogs.component.scss'],
})
export class MahankaraDialogsComponent extends TranslatableListComponent<MahankaraBehavior> {
  isTab = false;
  activeTab: string;
  title: string;
  description: string;
  activeLanguage = 'PTBR';
  listExcelMahankara: string[] = [];

  public readonly excelButtonTemplate: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  constructor(
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    private router: Router,
    _translationService: TranslationService,
    _languageService: LanguageService,
    private _mahankaraBehaviorTableService: MahankaraBehaviorTableService,
    private _mahankaraCategoriesService: MahankaraCategoriesService,
    private _mahankaraGroupingsService: MahankaraGroupingsService,
    private _mahankaraConcatenationsService: MahankaraConcatenationsService,
    private _categoriesXStressStatesService: CategoriesXStressStatesService
  ) {
    super(
      _mahankaraBehaviorTableService,
      _activatedRoute,
      _userSettingsService,
      'name',
      _translationService,
      _languageService
    );
  }

  public override async ngOnInit(): Promise<void> {
    const tab = localStorage.getItem(
      `tab-OthersComponent${FILTER_SUFFIX_PATH}`
    );
    this.activeTab = tab === 'null' || !tab ? 'keywords' : tab;
    this.isTab = false;
  }

  public switchToTab(tab: string) {
    this.activeTab = tab;
    this.isTab = true;

    if (tab === 'mahankaraBehaviorTable') {
      this.title = 'Mahankara Behavior Table';
      this.description = `Showing ${this._mahankaraBehaviorTableService.models.length} results`;
    } else if (tab === 'categories') {
      this.title = 'Categories';
      this.description = `Showing ${this._mahankaraCategoriesService.models.length} results`;
    } else if (tab === 'groupings') {
      this.title = 'Groupings';
      this.description = `Showing ${this._mahankaraGroupingsService.models.length} results`;
    } else if (tab === 'concatenations') {
      this.description = `Showing ${this._mahankaraConcatenationsService.models.length} results`;
    } else if (tab === 'categoriesXStressStates') {
      this.title = 'Categories X Stress States';
      this.description = `Showing ${this._categoriesXStressStatesService.models.length} results`;
    }
    localStorage.setItem(
      `tab-StatusEffectComponent${FILTER_SUFFIX_PATH}`,
      this.activeTab
    );
  }

  async onExcelPaste() {
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter((line) => line);
    this.listExcelMahankara = lines;
  }

  receiveText(text: string) {
    this.description = text;
  }
}
