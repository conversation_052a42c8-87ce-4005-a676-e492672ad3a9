import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';

export class MahankaraBehavior extends Base<Data.Hard.IMahankaraBehavior, Data.Result.IMahankaraBehavior> implements Required<Data.Hard.IMahankaraBehavior>
{
  public static generateId(index: number): string {
    return IdPrefixes.MAHANKARABEHAVIOR + index;
  }

  constructor( index: number, dataAccess: MahankaraBehavior['TDataAccess']) 
  {
    super(
    {
      hard: 
      {
        id: MahankaraBehavior.generateId(index),     
      },
    },
    dataAccess
    );
  }
  protected getInternalFetch() 
  {
    return {};
  }
  public get mahankaraUpgrade(): number
  {
    return this.hard.mahankaraUpgrade;
  }
  public set mahankaraUpgrade(value: number) 
  {
    this.hard.mahankaraUpgrade = value;
  }
  public get bLAddition(): number
  {
    return this.hard.bLAddition;
  }
  public set bLAddition(value: number) 
  {
    this.hard.bLAddition = value;
  }
  public get partyMemberBLAddition(): number
  {
    return this.hard.partyMemberBLAddition;
  }
  public set partyMemberBLAddition(value: number) 
  {
    this.hard.partyMemberBLAddition = value;
  }
  public get turnDuration(): number
  {
    return this.hard.turnDuration;
  }
  public set turnDuration(value: number) 
  {
    this.hard.turnDuration = value;
  }


}
