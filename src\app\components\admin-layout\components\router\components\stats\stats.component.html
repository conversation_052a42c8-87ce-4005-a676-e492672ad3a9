<div class="card list-header" style="height: 70px; margin: 10px; margin-bottom: 0px; margin-left: 30px; margin-right: 30px;">
  <div class="header">
    <button class="{{activeTab === 'levelUpgrades' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
      (click)="switchToTab('levelUpgrades')">
      Player Level
    </button>
    <button class="{{activeTab === 'levelXP' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
      (click)="switchToTab('levelXP')" style="margin-left: 5px;">
      Levels XP
    </button>

    <button class="{{activeTab === 'config' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
      (click)="switchToTab('config')" style="float: right; position: relative; height: 40px;width: 70px;">
      <i _ngcontent-mut-c250="" class="pe-7s-config" style="font-size: 30px;position: relative;top: -5px;"></i>
    </button>
  </div>
</div>

<app-level-upgrades *ngIf="activeTab === 'levelUpgrades'"> </app-level-upgrades>
<app-level-xp-view *ngIf="activeTab === 'levelXP'"></app-level-xp-view>
<app-config-threshold *ngIf="activeTab === 'config'"></app-config-threshold>