
import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { Class, Weapon } from 'src/app/lib/@bus-tier/models';
import { ClassService } from 'src/app/services';
import { CustomService } from 'src/app/services/custom.service';
import { LanguageService } from 'src/app/services/language.service';

@Component({
  selector: 'class-selector',
  templateUrl: './class-selector.component.html',
  styleUrls: ['./class-selector.component.scss'],
})

export class ClassSelector implements OnInit 
{
  @Output() parentEvent  = new EventEmitter<void>();  
  @Input() currentWeapon: Weapon;
  @Input() enabledSelectedClassessId: string[];
  @Input() selectedClassessId: string[];
  @Output() selectedClassessIdChange = new EventEmitter<string[]>();
  popupOpen = false;
  selectedClasses: Class[];
  classes: Class[];

  constructor(
    protected _customService: CustomService,
    protected _languageService: LanguageService,
    protected _classService: ClassService,
    private ref: ChangeDetectorRef,
  )
  {}

  async ngOnInit(): Promise<void>
  {
    await this._classService.toFinishLoading();
    this.classes = this._classService.models;
    this.selectedClasses = [];
    if(!this.selectedClassessId) this.selectedClassessId = [];
    this.classes.forEach(c => 
    {
      if(this.selectedClassessId.includes(c.id))
      {
        let clas = this._classService.svcFindById(c.id);
        this.selectedClasses.push(clas);
      }
    });
    this.ref.detectChanges();
  }

  reset(classesId, enabledSelectedClassessId)
  {
    this.selectedClassessId = classesId;
    this.enabledSelectedClassessId = enabledSelectedClassessId;
    this.ngOnInit();
  }

  togglePopup(event)
  {
    if(event && event.target !== event.currentTarget) return;
    this.popupOpen = !this.popupOpen;
    if(this.popupOpen) this.classes = this._classService.models;    
  }

  isCharacterInEnabledCharacterSelector(characterId: string)
  {
    for(let i = 0; i < this.enabledSelectedClassessId?.length; i++)
    {
      if(this.enabledSelectedClassessId[i] == characterId)
      {
        this.currentWeapon.enabledClassesId.splice(i, 1);
        break;
      }
    }
  }

  changeValue(event, clas: Class)
  {
    this.isCharacterInEnabledCharacterSelector(clas.id);
  
    if(event.target.checked) this.selectedClasses.push(clas);    
    else this.selectedClasses = this.selectedClasses.filter(c => c.id !== clas.id);    
    this.selectedClassessIdChange.emit(this.selectedClasses.map(c => c.id));  
    this.parentEvent.emit();
  }
}
