import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';

export class KnowledgeDiceFrustration extends Base<Data.Hard.IKnowledgeDiceFrustration, Data.Result.IKnowledgeDiceFrustration> implements Required<Data.Hard.IKnowledgeDiceFrustration> {
  public static generateId(index: number): string {
    return IdPrefixes.KNOWLEDGEDICEFRUSTRATION + index;
  }

  constructor(index: number, dataAccess: KnowledgeDiceFrustration['TDataAccess']) {
    super(
      {
        hard:
        {
          id: KnowledgeDiceFrustration.generateId(index),
        },
      },
      dataAccess
    );
  }

  public get subContext(): string {
    return this.hard.subContext;
  }
  public set subContext(value: string) {
    this.hard.subContext = value;
  }
  public get light(): string[] {
    return this.hard.light;
  }
  public set light(value: string[]) {
    this.hard.light = value;
  }
  public get moderate(): string[] {
    return this.hard.moderate;
  }
  public set moderate(value: string[]) {
    this.hard.moderate = value;
  }
  public get critical(): string[] {
    return this.hard.critical;
  }
  public set critical(value: string[]) {
    this.hard.critical = value;
  }
  public get revisionCounterLightAI(): number[] {
    return this.hard.revisionCounterLightAI;
  }
  public set revisionCounterLightAI(value: number[]) {
    this.hard.revisionCounterLightAI = value;
  }
  public get revisionCounterModerateAI(): number[] {
    return this.hard.revisionCounterModerateAI;
  }
  public set revisionCounterModerateAI(value: number[]) {
    this.hard.revisionCounterModerateAI = value;
  }
  public get revisionCounterCriticalAI(): number[] {
    return this.hard.revisionCounterCriticalAI;
  }
  public set revisionCounterCriticalAI(value: number[]) {
    this.hard.revisionCounterCriticalAI = value;
  }
  public get isReviewedLight(): boolean[] {
    return this.hard.isReviewedLight;
  }
  public set isReviewedLight(value: boolean[]) {
    this.hard.isReviewedLight = value;
  }
  public get isReviewedModerate(): boolean[] {
    return this.hard.isReviewedModerate;
  }
  public set isReviewedModerate(value: boolean[]) {
    this.hard.isReviewedModerate = value;
  }
  public get isReviewedCritical(): boolean[] {
    return this.hard.isReviewedCritical;
  }
  public set isReviewedCritical(value: boolean[]) {
    this.hard.isReviewedCritical = value;
  }
}
