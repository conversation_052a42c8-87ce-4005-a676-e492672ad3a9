 <!-- Modal INFO-->
 <app-ai-revisor-modal
   *ngIf="isModalInfo"
   [isModalInfo]="isModalInfo"
   (modalClose)="closeAreaStatsPopup()">
 </app-ai-revisor-modal>
<!--Fim do Modal INFO-->

<!-- Modal VIEW SEND-->
<app-view-send-modal
  *ngIf="islookSend"
  [isModalInfo]="islookSend"
  [listViewSend]="listViewSend"
  (modalClose)="closeViewSendPopup()">
</app-view-send-modal>
<!--Fim do Modal VIEW SEND-->
    
    <div class="card">
        <div style="display: contents; position: fixed;">
            <div class="col-md-12" style="display: contents"> 
                <div class="header card-header-wrapper" style="height: 100px;"  [ngStyle]="{'height': sendingBatch == undefined ? '100px' : ''}">
                  <div style="position: relative; z-index: 10;">
                    <app-back-button (click)="redirectToSettings()">
                    </app-back-button>
                  </div>

                  <h3 class="title" style="padding-left: 100px;">{{ title }}
                  <i ngClass="iconInter" (click)="onModalClick()" class="pe-7s-info batt"></i>
                </h3>
                  <ng-container *ngIf="sendingBatch !== undefined">
                    <div class="card-send">
                        <div class="card-send-database">
                         <span>Database: </span>
                         <p class="card-send-text">{{nameDB}} </p>
                         <div [ngStyle]="{'color': completedAnalysis === 'Revisão cancelada.' ? 'red': ''}">
                             {{completedAnalysis ? ' - ' + completedAnalysis : ' - Revisão em andamento.'}}
                         </div> 
                        </div>
                        <div class="card-send-api">
                            <span style="font-size: 17px;">API: </span>{{' '}}
                             {{environment.model}} {{'  -  '}}  <span>Temperature: {{' '}} </span> {{environment.temperature}} {{'  -  '}}  <span> Limit: {{' '}} </span> {{environment.limitForReview}}
                           </div>  
                        <div class="card-send-cancel">
                            <button title="View send" (click)="lookSend()">
                                <i class="pe-7s-look"></i>
                            </button>
                            <button *ngIf="listRevisor && !completedAnalysis" (click)="cancelRequest()" class="btn btn-danger btn-fill btn-height">
                                Cancel Review Request
                            </button>
                        </div>                       
                     </div>
                </ng-container>     
                </div>
            </div>
        </div>
    </div>


    <div class="content-holder">            
    <div class="card db-itens">
        <div class="div-menu">
            <table id="customers" style="width: 100%;">
                <tr *ngFor="let item of counterItems; let i = index" [ngClass]="{'selected': selectedItem === item.name}">
                    <td (click)="databaseToReview(item)" style="cursor: pointer;">
                    <span class="div-menu-span">
                        {{ item?.name }}
                    </span>
                    <p>
                        <span class="span-font-weight">Base size: </span> {{item?.baseSize}}
                    </p>
                    <p>
                       <span class="span-font-weight">Total fields: </span> {{item?.totalFields}} 
                    </p>
                    <ng-container *ngIf="item?.totalEmptyFields">
                        <p>
                            <span class="span-font-weight">Total empty fields: </span> {{item.totalEmptyFields ? item.totalEmptyFields : 0}}
                    </ng-container>
                    <ng-container *ngIf="item?.unreviewedItems">
                        <p>
                            <span class="span-font-weight" [ngStyle]="{'color': item?.unreviewedItems != 0 ? 'red': ''}">Fields to be reviewed: </span> {{item?.unreviewedItems}}
                        </p>
                    </ng-container>
                    <ng-container *ngIf="item?.revisedItems">
                        <p>
                            <span class="span-font-weight" style="color: green;">Manually approved reviews: </span> {{item?.revisedItems}}
                        </p> 
                    </ng-container>
           
                    <ng-container *ngIf="selectedItem === item.name">
                        <p>
                            <span class="span-font-weight">Suggestions: </span> {{item?.suggestions ? item?.suggestions : 0}}
                        </p> 
                        <p>
                          <span class="span-font-weight">Suggestions rejected: </span> {{item?.suggestionsRejected ? item?.suggestionsRejected : 0}}    
                      </p> 
                        <p *ngIf="completedAnalysis && item?.totalNoSuggestions">
                            <span class="span-font-weight">Total without suggestions: </span> {{item?.totalNoSuggestions ? item?.totalNoSuggestions : 0}}    
                        </p>      
                    </ng-container>   
                    
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <ng-container *ngIf="!isLotesObeject">
        <div class="card-container myDiv">
            <div id="highlight-element" class="card cards-group container-card" [ngStyle]="{'height': listRevisor.length === 0 ? '400px' : 'auto'}">
                <div *ngFor="let context of listRevisor; let i = index" class="group-together" >               
                      
                    <ng-container *ngIf="(context.newName || context.newDescription || context.newTitle || context.newMessage || context.newWord) && !isLotesObeject ">
                        <div class="card" style="background-color: white;">                       
                            <ng-container *ngIf="context?.newName">
                                <div id="highlight-name-{{ i }}" class="card-div-name">
                                  <h3 class="h3-number">{{ i + 1 }}</h3>
                                    <div class="text-holder">                                                                       
                                        <div class="originalText" >
                                            <span>Name:</span> <br>
                                            <p [innerHTML]="context.name | textDiff: context.newName" style="padding-top: 5px;">{{ context.name }}</p>
                               
                                        </div>                                  
                                            <div class="card text-card">
                                                <div style="width: 100%;">
                                                    <div class="text-to-be-revised">
                                                        <span>Suggestion:</span><br>
                                                        <!-- Modo de visualização -->
                                                        <p *ngIf="!context.isEditingName"
                                                           [innerHTML]="context.newName | highlightChanges: context.name"
                                                           (click)="enableEdit(context, 'name')"
                                                           style="cursor: pointer; padding-top: 5px; border-radius: 3px;"
                                                           title="Clique para editar">
                                                           {{ context.newName }}
                                                        </p>
                                                        <!-- Modo de edição -->
                                                        <div *ngIf="context.isEditingName" class="edit-field">
                                                            <textarea #editNameInput
                                                                      [value]="context.newName"
                                                                      class="form-control"
                                                                      rows="3"
                                                                      style="resize: vertical;">
                                                            </textarea>
                                                            <div class="edit-buttons" style="margin-top: 5px;">
                                                                <button class="btn btn-sm btn-success"
                                                                        (click)="saveEditedField(context, 'name', editNameInput.value)"
                                                                        title="Salvar alterações">
                                                                    <i class="pe-7s-check"></i>
                                                                </button>
                                                                <button class="btn btn-sm btn-secondary"
                                                                        (click)="cancelEdit(context, 'name')"
                                                                        title="Cancelar edição">
                                                                    <i class="pe-7s-close"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                     </div>
                                                </div>
                                                <div class="div-buttom" *ngIf="!context.isEditingName">
                                                    <button (click)="approveText(context.id, context.newName, 'isReviewedName', 'name', i)">
                                                        <i class="pe-7s-check buttomApprove" ></i>
                                                    </button>
                                                    <button (click)="rejectText(context.id,'isReviewedName', 'name', i)">
                                                        <i class="pe-7s-close-circle buttom-reject"></i>
                                                    </button>
                                                </div>
                                            </div>                            
                                        </div>
                                </div> 
                            </ng-container>              
      
                          <ng-container *ngIf="context?.newTitle">
                            <div id="highlight-title-{{ i }}" class="div-highlight">
                              <h3 class="h3-number">{{ i + 1 }}</h3>
                                <div class="text-holder">                                                                
                                    <div class="originalText">
                                        <span>Title:</span><br>                    
                                        <p [innerHTML]="context.title | textDiff: context?.newTitle" style="padding-top: 5px;">{{ context.title }}</p>                                 
                                    </div>                                    
                                        <div class="card text-card">
                                            <div style="width: 100%;">
                                                <div class="text-to-be-revised">
                                                    <span>Suggestion:</span><br>
                                                    <!-- Modo de visualização -->
                                                    <p *ngIf="!context.isEditingTitle"
                                                       [innerHTML]="context?.newTitle | highlightChanges: context.title"
                                                       (click)="enableEdit(context, 'title')"
                                                       style="cursor: pointer; padding-top: 5px; border-radius: 3px;"
                                                       title="Clique para editar">
                                                       {{ context?.newTitle }}
                                                    </p>
                                                    <!-- Modo de edição -->
                                                    <div *ngIf="context.isEditingTitle" class="edit-field">
                                                        <textarea #editTitleInput
                                                                  [value]="context.newTitle"
                                                                  class="form-control"
                                                                  rows="3"
                                                                  style="resize: vertical;">
                                                        </textarea>
                                                        <div class="edit-buttons" style="margin-top: 5px;">
                                                            <button class="btn btn-sm btn-success"
                                                                    (click)="saveEditedField(context, 'title', editTitleInput.value)"
                                                                    title="Salvar alterações">
                                                                <i class="pe-7s-check"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-secondary"
                                                                    (click)="cancelEdit(context, 'title')"
                                                                    title="Cancelar edição">
                                                                <i class="pe-7s-close"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="div-buttom" *ngIf="!context.isEditingTitle">
                                                <button title="Approve" (click)="approveText(context.id, context.title, 'isReviewedTitle', 'title', i)">
                                                    <i class="pe-7s-check buttomApprove"></i>
                                                </button>
                                                <button title="Reject" (click)="rejectText(context.id, 'isReviewedTitle','title', i)">
                                                    <i class="pe-7s-close-circle buttom-reject" ></i>
                                                </button>
                                            </div>
                                        </div>                               
                                    </div>
                                 </div> 
                          </ng-container>

                          <ng-container *ngIf="context?.newDescription">
                            <div id="highlight-description-{{ i }}" class="div-highlight">
                              <h3 class="h3-number">{{ i + 1 }}</h3>
                                <div class="text-holder">                                                                
                                    <div class="originalText" >
                                        <span>Description:</span><br>                         
                                        <p [innerHTML]="context.description | textDiff: context?.newDescription" style="padding-top: 5px;">{{ context.description }}</p>                                     
                                    </div>                                    
                                        <div class="card text-card">
                                            <div style="width: 100%;">
                                                <div class="text-to-be-revised">
                                                    <span>Suggestion:</span><br>
                                                    <!-- Modo de visualização -->
                                                    <p *ngIf="!context.isEditingDescription"
                                                       [innerHTML]="context?.newDescription | highlightChanges: context.description"
                                                       (click)="enableEdit(context, 'description')"
                                                       style="cursor: pointer; padding-top: 5px; border-radius: 3px;"
                                                       title="Clique para editar">
                                                       {{ context?.newDescription }}
                                                    </p>
                                                    <!-- Modo de edição -->
                                                    <div *ngIf="context.isEditingDescription" class="edit-field">
                                                        <textarea #editDescriptionInput
                                                                  [value]="context.newDescription"
                                                                  class="form-control"
                                                                  rows="4"
                                                                  style="resize: vertical;">
                                                        </textarea>
                                                        <div class="edit-buttons" style="margin-top: 5px;">
                                                            <button class="btn btn-sm btn-success"
                                                                    (click)="saveEditedField(context, 'description', editDescriptionInput.value)"
                                                                    title="Salvar alterações">
                                                                <i class="pe-7s-check"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-secondary"
                                                                    (click)="cancelEdit(context, 'description')"
                                                                    title="Cancelar edição">
                                                                <i class="pe-7s-close"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="div-buttom" *ngIf="!context.isEditingDescription">
                                                <button title="Approve" (click)="approveText(context.id, context.newDescription, 'isReviewedDescription', 'description', i)">
                                                    <i class="pe-7s-check buttomApprove" ></i>
                                                </button>
                                                <button title="Reject" (click)="rejectText(context.id, 'isReviewedDescription', 'description', i)">
                                                    <i class="pe-7s-close-circle buttom-reject"></i>
                                                </button>
                                            </div>
                                        </div>                               
                                    </div>
                                 </div>
                          </ng-container>

                          <ng-container *ngIf="context?.newMessage">
                            <div id="highlight-message-{{ i }}" class="div-highlight">
                              <h3 class="h3-number">{{ i + 1 }}</h3>
                                <div class="text-holder">                                                                
                                    <div class="originalText" >
                                        <span>Message:</span><br>                         
                                        <p [innerHTML]="context.message | textDiff: context?.newMessage" style="padding-top: 5px;">{{ context.message }}</p>                                     
                                    </div>                                    
                                        <div class="card text-card">
                                            <div style="width: 100%;">
                                                <div class="text-to-be-revised">
                                                    <span>Suggestion:</span><br>
                                                    <!-- Modo de visualização -->
                                                    <p *ngIf="!context.isEditingMessage"
                                                       [innerHTML]="context?.newMessage | highlightChanges: context.message"
                                                       (click)="enableEdit(context, 'message')"
                                                       style="cursor: pointer; padding-top: 5px; border-radius: 3px;"
                                                       title="Clique para editar">
                                                       {{ context?.newMessage }}
                                                    </p>
                                                    <!-- Modo de edição -->
                                                    <div *ngIf="context.isEditingMessage" class="edit-field">
                                                        <textarea #editMessageInput
                                                                  [value]="context.newMessage"
                                                                  class="form-control"
                                                                  rows="4"
                                                                  style="resize: vertical;">
                                                        </textarea>
                                                        <div class="edit-buttons" style="margin-top: 5px;">
                                                            <button class="btn btn-sm btn-success"
                                                                    (click)="saveEditedField(context, 'message', editMessageInput.value)"
                                                                    title="Salvar alterações">
                                                                <i class="pe-7s-check"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-secondary"
                                                                    (click)="cancelEdit(context, 'message')"
                                                                    title="Cancelar edição">
                                                                <i class="pe-7s-close"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="div-buttom" *ngIf="!context.isEditingMessage">
                                                <button title="Approve" (click)="approveText(context.id, context.newMessage, 'isReviewedMessage', 'message', i)">
                                                    <i class="pe-7s-check buttomApprove"></i>
                                                </button>
                                                <button title="Reject" (click)="rejectText(context.id, 'isReviewedMessage', 'message', i)">
                                                    <i class="pe-7s-close-circle buttom-reject"></i>
                                                </button>
                                            </div>
                                        </div>                               
                                    </div>
                                 </div>
                          </ng-container>

                          <ng-container *ngIf="context?.newWord">
                            <div id="highlight-word-{{ i }}" class="div-highlight">
                              <h3 class="h3-number">{{ i + 1 }}</h3>
                                <div class="text-holder">                                                                
                                    <div class="originalText" >
                                        <span>Word:</span><br>                         
                                        <p [innerHTML]="context.word | textDiff: context?.newWord" style="padding-top: 5px;">{{ context.word }}</p>                                     
                                    </div>                                    
                                        <div class="card text-card">
                                            <div style="width: 100%;">
                                                <div class="text-to-be-revised">
                                                    <span>Suggestion:</span><br>
                                                    <!-- Modo de visualização -->
                                                    <p *ngIf="!context.isEditingWord"
                                                       [innerHTML]="context?.newWord | highlightChanges: context.word"
                                                       (click)="enableEdit(context, 'word')"
                                                       style="cursor: pointer; padding-top: 5px; border-radius: 3px;"
                                                       title="Clique para editar">
                                                       {{ context?.newWord }}
                                                    </p>
                                                    <!-- Modo de edição -->
                                                    <div *ngIf="context.isEditingWord" class="edit-field">
                                                        <input #editWordInput
                                                               [value]="context.newWord"
                                                               class="form-control"
                                                               type="text">
                                                        <div class="edit-buttons" style="margin-top: 5px;">
                                                            <button class="btn btn-sm btn-success"
                                                                    (click)="saveEditedField(context, 'word', editWordInput.value)"
                                                                    title="Salvar alterações">
                                                                <i class="pe-7s-check"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-secondary"
                                                                    (click)="cancelEdit(context, 'word')"
                                                                    title="Cancelar edição">
                                                                <i class="pe-7s-close"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="div-buttom" *ngIf="!context.isEditingWord">
                                                <button title="Approve" (click)="approveText(context.id, context.newWord, 'isReviewedWord', 'word', i)">
                                                    <i class="pe-7s-check buttomApprove"></i>
                                                </button>
                                                <button title="Reject" (click)="rejectText(context.id, 'isReviewedWord', 'word', i)">
                                                    <i class="pe-7s-close-circle buttom-reject"></i>
                                                </button>
                                            </div>
                                        </div>                               
                                    </div>
                                 </div>
                          </ng-container>
                        </div>
                        </ng-container>                                        
                </div> 

                   <ng-container *ngIf="isLoadingLotes && isSelectedItem">
                    <div class="div-loading" style="margin-top: 20px;">
                        <div class="content-refresh">
                            <i class="icon pe-7s-refresh-2 icon-refresh-text"></i>                      
                        </div> 
                          <h4 style="margin-bottom: 0 !important;">Loading, please wait.</h4> 
                          <p *ngIf="sendingBatch" class="p-sendinhg" >{{sendingBatch}}</p>               
                            <div class="loading-dots">                        
                                <div class="dot"></div>
                                <div class="dot"></div>
                                 <div class="dot"></div>
                            </div>   
                    </div>                                             
                  </ng-container>

                  <ng-container *ngIf="!isSelectedItem && listRevisor.length === 0">
                    <div class="div-loading">
                        <h4>Select a database to review.</h4>
                    </div>
                  </ng-container>

                  <ng-container *ngIf="isSelectedItem && listRevisor.length === 0 && !isLotesObeject">
                    <div class="div-loading">
                        <h4>No suggestions.</h4>
                    </div>
                  </ng-container>
            </div>
            <ng-container *ngIf="completedAnalysis && listRevisor.length > 0">
                <div class="div-buttons-general">
                    <button title="Approve all" class="btn btn-fill btn-green-approved bt-width m-right" (click)="approveAll()">
                        Approve all
                        <i class="pe-7s-check i-color-font"></i>
                    </button>
                    <button title="Reject All" class="btn btn-fill btn-danger bt-width" (click)="rejectAll()">
                        Reject All
                        <i class="pe-7s-close-circle i-color-font"></i>
                    </button>
                </div>
              </ng-container>
        </div> 
    </ng-container>

    <!-- DICE FRUSTRATION -->
    <ng-container *ngIf="isLotesObeject">
            <div class="card-container myDiv">
                <div id="highlight-element" class="card cards-group container-card" [ngStyle]="{'height': listObjectRevisor.length === 0 ? '400px' : 'auto'}">                  
                      
                        <div *ngFor="let context of listObjectRevisor; let i = index" class="group-together" >  
                            <ng-container *ngIf="context?.newLight">                             
                                    <div class="card" style="background-color: white;">
                                      <div id="highlight-light-{{ i }}" class="card-div-name">
                                          <h3 class="h3-number">{{ i + 1  }}</h3>
                                            <div class="text-holder">                                                                       
                                                <div class="originalText" >
                                                    <span>Light:</span> <br>
                                                    <p [innerHTML]="context.light | textDiff: context.newLight" style="padding-top: 5px;">{{ context.light }}</p>
                                       
                                                </div>                                  
                                                    <div class="card text-card">
                                                        <div style="width: 100%;">
                                                            <div class="text-to-be-revised">
                                                                <span>Suggestion:</span><br>
                                                                <!-- Modo de visualização -->
                                                                <p *ngIf="!context.isEditingLight"
                                                                   [innerHTML]="context.newLight | highlightChanges: context.light"
                                                                   (click)="enableObjectEdit(context, 'light')"
                                                                   style="cursor: pointer; padding-top: 5px; border-radius: 3px;"
                                                                   title="Clique para editar">
                                                                   {{ context.newLight }}
                                                                </p>
                                                                <!-- Modo de edição -->
                                                                <div *ngIf="context.isEditingLight" class="edit-field">
                                                                    <textarea #editLightInput
                                                                              [value]="context.newLight"
                                                                              class="form-control"
                                                                              rows="3"
                                                                              style="resize: vertical;">
                                                                    </textarea>
                                                                    <div class="edit-buttons" style="margin-top: 5px;">
                                                                        <button class="btn btn-sm btn-success"
                                                                                (click)="saveEditedObjectField(context, 'light', editLightInput.value)"
                                                                                title="Salvar alterações">
                                                                            <i class="pe-7s-check"></i>
                                                                        </button>
                                                                        <button class="btn btn-sm btn-secondary"
                                                                                (click)="cancelObjectEdit(context, 'light')"
                                                                                title="Cancelar edição">
                                                                            <i class="pe-7s-close"></i>
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                             </div>
                                                        </div>
                                                        <div class="div-buttom" *ngIf="!context.isEditingLight">
                                                            <button (click)="approveObejectText(context, 'light', i)">
                                                                <i class="pe-7s-check buttomApprove" ></i>
                                                            </button>
                                                            <button (click)="rejectTextObject(context, 'light', i)">
                                                                <i class="pe-7s-close-circle buttom-reject"></i>
                                                            </button>
                                                        </div>
                                                    </div>                            
                                                </div>
                                        
                                              </div> 
                                      </div>                           
                           </ng-container>    
                        <ng-container *ngIf="context?.newModerate">                        
                                        <div class="card" style="background-color: white;">
                                        <div id="highlight-moderate-{{ i }}" class="card-div-name">
                                            <h3 class="h3-number">{{ i + 1  }}</h3>
                                              <div class="text-holder">                                                                       
                                                  <div class="originalText" >
                                                      <span>Moderate</span> <br>
                                                      <p [innerHTML]="context.moderate | textDiff: context.newModerate" style="padding-top: 5px;">{{ context.moderate }}</p>
                                         
                                                  </div>                                  
                                                      <div class="card text-card">
                                                          <div style="width: 100%;">
                                                              <div class="text-to-be-revised">
                                                                  <span>Suggestion:</span><br>
                                                                  <!-- Modo de visualização -->
                                                                  <p *ngIf="!context.isEditingModerate"
                                                                     [innerHTML]="context.newModerate | highlightChanges: context.moderate"
                                                                     (click)="enableObjectEdit(context, 'moderate')"
                                                                     style="cursor: pointer; padding-top: 5px; border-radius: 3px;"
                                                                     title="Clique para editar">
                                                                     {{ context.newModerate }}
                                                                  </p>
                                                                  <!-- Modo de edição -->
                                                                  <div *ngIf="context.isEditingModerate" class="edit-field">
                                                                      <textarea #editModerateInput
                                                                                [value]="context.newModerate"
                                                                                class="form-control"
                                                                                rows="3"
                                                                                style="resize: vertical;">
                                                                      </textarea>
                                                                      <div class="edit-buttons" style="margin-top: 5px;">
                                                                          <button class="btn btn-sm btn-success"
                                                                                  (click)="saveEditedObjectField(context, 'moderate', editModerateInput.value)"
                                                                                  title="Salvar alterações">
                                                                              <i class="pe-7s-check"></i>
                                                                          </button>
                                                                          <button class="btn btn-sm btn-secondary"
                                                                                  (click)="cancelObjectEdit(context, 'moderate')"
                                                                                  title="Cancelar edição">
                                                                              <i class="pe-7s-close"></i>
                                                                          </button>
                                                                      </div>
                                                                  </div>
                                                               </div>
                                                          </div>
                                                          <div class="div-buttom" *ngIf="!context.isEditingModerate">
                                                              <button (click)="approveObejectText(context, 'moderate', i)">
                                                                  <i class="pe-7s-check buttomApprove" ></i>
                                                              </button>
                                                              <button (click)="rejectTextObject(context, 'moderate', i)">
                                                                  <i class="pe-7s-close-circle buttom-reject"></i>
                                                              </button>
                                                          </div>
                                                      </div>                            
                                                  </div>
                                            </div> 
                                       </div>                              
                        </ng-container>
                        <ng-container *ngIf="context?.newCritical">
                                        <div class="card" style="background-color: white;">
                                        <div id="highlight-critical-{{ i  }}" class="card-div-name">
                                            <h3 class="h3-number">{{ i + 1 }}</h3>
                                              <div class="text-holder">                                                                       
                                                  <div class="originalText" >
                                                      <span>Critical:</span> <br>
                                                      <p [innerHTML]="context.critical | textDiff: context.newCritical" style="padding-top: 5px;">{{ context.critical }}</p>
                                         
                                                  </div>                                  
                                                      <div class="card text-card">
                                                          <div style="width: 100%;">
                                                              <div class="text-to-be-revised">
                                                                  <span>Suggestion:</span><br>
                                                                  <!-- Modo de visualização -->
                                                                  <p *ngIf="!context.isEditingCritical"
                                                                     [innerHTML]="context.newCritical | highlightChanges: context.critical"
                                                                     (click)="enableObjectEdit(context, 'critical')"
                                                                     style="cursor: pointer; padding-top: 5px; border-radius: 3px;"
                                                                     title="Clique para editar">
                                                                     {{ context.newCritical }}
                                                                  </p>
                                                                  <!-- Modo de edição -->
                                                                  <div *ngIf="context.isEditingCritical" class="edit-field">
                                                                      <textarea #editCriticalInput
                                                                                [value]="context.newCritical"
                                                                                class="form-control"
                                                                                rows="3"
                                                                                style="resize: vertical;">
                                                                      </textarea>
                                                                      <div class="edit-buttons" style="margin-top: 5px;">
                                                                          <button class="btn btn-sm btn-success"
                                                                                  (click)="saveEditedObjectField(context, 'critical', editCriticalInput.value)"
                                                                                  title="Salvar alterações">
                                                                              <i class="pe-7s-check"></i>
                                                                          </button>
                                                                          <button class="btn btn-sm btn-secondary"
                                                                                  (click)="cancelObjectEdit(context, 'critical')"
                                                                                  title="Cancelar edição">
                                                                              <i class="pe-7s-close"></i>
                                                                          </button>
                                                                      </div>
                                                                  </div>
                                                               </div>
                                                          </div>
                                                          <div class="div-buttom" *ngIf="!context.isEditingCritical">
                                                              <button (click)="approveObejectText(context, 'critical', i)">
                                                                  <i class="pe-7s-check buttomApprove" ></i>
                                                              </button>
                                                              <button (click)="rejectTextObject(context, 'critical', i)">
                                                                  <i class="pe-7s-close-circle buttom-reject"></i>
                                                              </button>
                                                          </div>
                                                      </div>                            
                                                  </div>
                                            </div> 
                                            </div>            
                        </ng-container> 
                        
                        <ng-container *ngIf="context?.newFactor">
                                        <div class="card" style="background-color: white;">
                                        <div id="highlight-factor-{{ i  }}" class="card-div-name">
                                            <h3 class="h3-number">{{ i + 1 }}</h3>
                                              <div class="text-holder">                                                                       
                                                  <div class="originalText" >
                                                      <span>Factor:</span> <br>
                                                      <p [innerHTML]="context.factor | textDiff: context.newFactor" style="padding-top: 5px;">{{ context.factor }}</p>
                                         
                                                  </div>                                  
                                                      <div class="card text-card">
                                                          <div style="width: 100%;">
                                                              <div class="text-to-be-revised">
                                                                  <span>Suggestion:</span><br>
                                                                  <!-- Modo de visualização -->
                                                                  <p *ngIf="!context.isEditingFactor"
                                                                     [innerHTML]="context.newFactor | highlightChanges: context.factor"
                                                                     (click)="enableObjectEdit(context, 'factor')"
                                                                     style="cursor: pointer; padding-top: 5px; border-radius: 3px;"
                                                                     title="Clique para editar">
                                                                     {{ context.newFactor }}
                                                                  </p>
                                                                  <!-- Modo de edição -->
                                                                  <div *ngIf="context.isEditingFactor" class="edit-field">
                                                                      <textarea #editFactorInput
                                                                                [value]="context.newFactor"
                                                                                class="form-control"
                                                                                rows="3"
                                                                                style="resize: vertical;">
                                                                      </textarea>
                                                                      <div class="edit-buttons" style="margin-top: 5px;">
                                                                          <button class="btn btn-sm btn-success"
                                                                                  (click)="saveEditedObjectField(context, 'factor', editFactorInput.value)"
                                                                                  title="Salvar alterações">
                                                                              <i class="pe-7s-check"></i>
                                                                          </button>
                                                                          <button class="btn btn-sm btn-secondary"
                                                                                  (click)="cancelObjectEdit(context, 'factor')"
                                                                                  title="Cancelar edição">
                                                                              <i class="pe-7s-close"></i>
                                                                          </button>
                                                                      </div>
                                                                  </div>
                                                               </div>
                                                          </div>
                                                          <div class="div-buttom" *ngIf="!context.isEditingFactor">
                                                              <button (click)="approveObejectText(context, 'factor', i)">
                                                                  <i class="pe-7s-check buttomApprove" ></i>
                                                              </button>
                                                              <button (click)="rejectTextObject(context, 'factor', i)">
                                                                  <i class="pe-7s-close-circle buttom-reject"></i>
                                                              </button>
                                                          </div>
                                                      </div>                            
                                                  </div>
                                            </div> 
                                            </div>            
                        </ng-container>
                        
                              <ng-container *ngIf="context?.newDescription">
                                        <div class="card" style="background-color: white;">
                                        <div id="highlight-description-{{ i  }}" class="card-div-name">
                                            <h3 class="h3-number">{{ i + 1 }}</h3>
                                              <div class="text-holder">                                                                       
                                                  <div class="originalText" >
                                                      <span>Description:</span> <br>
                                                      <p [innerHTML]="context.description | textDiff: context.newDescription" style="padding-top: 5px;">{{ context.description }}</p>
                                         
                                                  </div>                                  
                                                      <div class="card text-card">
                                                          <div style="width: 100%;">
                                                              <div class="text-to-be-revised">
                                                                  <span>Suggestion:</span><br>
                                                                  <!-- Modo de visualização -->
                                                                  <p *ngIf="!context.isEditingDescription"
                                                                     [innerHTML]="context.newDescription | highlightChanges: context.description"
                                                                     (click)="enableObjectEdit(context, 'description')"
                                                                     style="cursor: pointer; padding-top: 5px; border-radius: 3px;"
                                                                     title="Clique para editar">
                                                                     {{ context.newDescription }}
                                                                  </p>
                                                                  <!-- Modo de edição -->
                                                                  <div *ngIf="context.isEditingDescription" class="edit-field">
                                                                      <textarea #editDescriptionInput
                                                                                [value]="context.newDescription"
                                                                                class="form-control"
                                                                                rows="3"
                                                                                style="resize: vertical;">
                                                                      </textarea>
                                                                      <div class="edit-buttons" style="margin-top: 5px;">
                                                                          <button class="btn btn-sm btn-success"
                                                                                  (click)="saveEditedObjectField(context, 'description', editDescriptionInput.value)"
                                                                                  title="Salvar alterações">
                                                                              <i class="pe-7s-check"></i>
                                                                          </button>
                                                                          <button class="btn btn-sm btn-secondary"
                                                                                  (click)="cancelObjectEdit(context, 'description')"
                                                                                  title="Cancelar edição">
                                                                              <i class="pe-7s-close"></i>
                                                                          </button>
                                                                      </div>
                                                                  </div>
                                                               </div>
                                                          </div>
                                                          <div class="div-buttom" *ngIf="!context.isEditingDescription">
                                                              <button (click)="approveObejectText(context, 'description', i)">
                                                                  <i class="pe-7s-check buttomApprove" ></i>
                                                              </button>
                                                              <button (click)="rejectTextObject(context, 'description', i)">
                                                                  <i class="pe-7s-close-circle buttom-reject"></i>
                                                              </button>
                                                          </div>
                                                      </div>                            
                                                  </div>
                                            </div> 
                                            </div>            
                        </ng-container>   
                                           
                    </div> 
    
             
                       <ng-container *ngIf="isLoadingLotes && isSelectedItem">
                        <div class="div-loading" style="margin-top: 20px;">
                            <div class="content-refresh">
                                <i class="icon pe-7s-refresh-2 icon-refresh-text"></i>                      
                            </div> 
                              <h4 style="margin-bottom: 0 !important;">Loading, please wait.</h4> 
                              <p *ngIf="sendingBatch" class="p-sendinhg">{{sendingBatch}}</p>               
                                <div class="loading-dots">                        
                                    <div class="dot"></div>
                                    <div class="dot"></div>
                                     <div class="dot"></div>
                                </div>   
                        </div>                                             
                      </ng-container>
    
                      <ng-container *ngIf="!isSelectedItem && listObjectRevisor.length === 0">
                        <div class="div-loading">
                            <h4>Select a database to review.</h4>
                        </div>
                      </ng-container>
    
                      <ng-container *ngIf="isSelectedItem && listObjectRevisor.length === 0 && !isLotesObeject">
                        <div class="div-loading">
                            <h4>No suggestions.</h4>
                        </div>
                      </ng-container>
                </div>
                <ng-container *ngIf="completedAnalysis && listObjectRevisor.length > 0">
                    <div class="div-buttons-general">
                        <button title="Approve all" class="btn btn-fill btn-green-approved bt-width m-right" (click)="approveAll()">
                            Approve all
                            <i class="pe-7s-check i-color-font"></i>
                        </button>
                        <button title="Reject All" class="btn btn-fill btn-danger bt-width" (click)="rejectAll()">
                            Reject All
                            <i class="pe-7s-close-circle i-color-font"></i>
                        </button>
                    </div>
                  </ng-container>
            </div> 

    </ng-container>

    </div>

