import {
  IdPrefixes,
} from 'src/lib/darkcloud/dialogue-system';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { Base } from './Base';
import { Slot } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard';

export class Mastery extends Base<Data.Hard.IMastery, Data.Result.IMastery> implements Required<Data.Hard.IMastery>
{
  private static generateId(index: number): string {
    return IdPrefixes.MASTERY + index;
  }
  constructor(
    index: number,
    typeMasteryRarity: string,
    dataAccess: Mastery['TDataAccess']
  ) {
    super(
      {
        hard: {
          id: Mastery.generateId(index),
          typeMasteryRarity,
        },
      },
      dataAccess
    );
  }
  protected getInternalFetch() {
    return {};
  }
  public get typeMasteryRarity(): string
  {
    return this.hard.typeMasteryRarity;
  }
  public set typeMasteryRarity(value: string)
  {
    this.hard.typeMasteryRarity = value;
  }

  public get slotA(): Slot
  {
    return this.hard.slotA;
  }
  public set slotA(value: Slot)
  {
    this.hard.slotA = value;
  }

  public get slotB(): Slot
  {
    return this.hard.slotB;
  }
  public set slotB(value: Slot)
  {
    this.hard.slotB = value;
  }

  public get slotC(): Slot
  {
    return this.hard.slotC;
  }
  public set slotC(value: Slot)
  {
    this.hard.slotC = value;
  }
  public get extremes(): string
  {
    return this.hard.extremes;
  }
  public set extremes(value: string)
  {
    this.hard.extremes = value;
  }

}
