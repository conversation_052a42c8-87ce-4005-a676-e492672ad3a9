import { IdPrefixes} from 'src/lib/darkcloud/dialogue-system';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { Base } from './Base';
import { valuesHC } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard';

export class SpecialWeaponsHC extends Base<Data.Hard.ISpecialWeaponsHC, Data.Result.ISpecialWeaponsHC>  implements Required<Data.Hard.ISpecialWeaponsHC>
{
  private static generateId(index: number): string {
    return IdPrefixes.SPECIAL_WEAPONSHC + index;
  }
  constructor(
    
    index: number,
    name: string,
    dataAccess: SpecialWeaponsHC['TDataAccess']
  ) {
    super(
      {
        hard: {
          id: SpecialWeaponsHC.generateId(index),
          name,
        },
      },
      dataAccess
    );
  }
  protected getInternalFetch() {
    return {};
  }
  
  public get name(): string
  {
    return this.hard.name;
  }
  public set name(value: string)
  {
    this.hard.name = value;
  } 
  public get idCircle(): string
  {
    return this.hard.idCircle;
  }
  public set idCircle(value: string)
  {
    this.hard.idCircle = value;
  } 
  public get hc(): number
  {
    return this.hard.hc;
  }
  public set hc(value: number)
  {
    this.hard.hc = value;
  }

  public get bluePrintReceivedHC(): valuesHC[]
  {
    return this.hard.bluePrintReceivedHC;
  }
  public set bluePrintReceivedHC(value: valuesHC[])
  {
    this.hard.bluePrintReceivedHC = value;
  }
  public get wlRange(): string
  {
    return this.hard.wlRange;
  }
  public set wlRange(value: string)
  {
    this.hard.wlRange = value;
  }
}
