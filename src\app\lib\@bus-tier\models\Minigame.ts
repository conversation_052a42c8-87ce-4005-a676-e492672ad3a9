import { Data } from "src/lib/darkcloud/angular/dsadmin";
import { IdPrefixes } from "src/lib/darkcloud/dialogue-system";
import { Base } from "./Base";

export class Minigame extends Base<Data.Hard.IMinigame, Data.Result.IMinigame>
implements Required<Data.Hard.IMinigame>
{
    private static generateId(index: number): string
    {
        return IdPrefixes.MINIGAME + index;
    }
    constructor(index: number, name: string, dataAccess: Minigame['TDataAccess'])
    {
        super(
            {
                hard: {
                    id: Minigame.generateId(index),
                    name
                },
            },
            dataAccess
        );
    }

    public get name()
    {
        return this.hard.name;
    }
    public set name(value: string)
    {
        this.hard.name = value;
    }

    public get description()
    {
        return this.hard.description;
    }
    public set description(value: string)
    {
        this.hard.description = value;
    }

    public get notes()
    {
        return this.hard.notes;
    }
    public set notes(value: string)
    {
        this.hard.notes = value;
    }
}
