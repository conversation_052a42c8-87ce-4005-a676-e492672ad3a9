<div class="card list-header"
     style="height: 70px; margin: 30px; margin-bottom: 0px;">
  <div class="header">
    <button class="{{activeTab === 'class-selection' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
            (click)="switchToTab('class-selection')">
      1 - Item Class
    </button>
    <button class="{{activeTab === 'particle-selection' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
            (click)="switchToTab('particle-selection')" style="margin-left: 5px;">
      2 - Silicate Selection
    </button>
    <!-- Need to put activeTab2 to make it work on -->
    <div style=" float:right" *ngIf="activeTab === 'particle-selection'">
      <button class="'btn btn-fill'"
              (click)="switchToTab('class-selection')">
        Silicates Ingredients
      </button>
    <!--    
      <button class="{{activeTab === 'class-selection' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
              (click)="switchToTab('class-selection')">
        Silicates Ingredients
      </button>
     <button class="{{activeTab === 'particle-selection' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
              (click)="switchToTab('particle-selection')">
        Passive Silicates
      </button> -->
    </div>
  </div>
</div>

    <app-silicatos *ngIf="activeTab === 'particle-selection'"> </app-silicatos>
    <app-silicate-class-selection *ngIf="activeTab === 'class-selection'"
                                  (itemSelected)="switchToTab('item-record')">
    </app-silicate-class-selection>