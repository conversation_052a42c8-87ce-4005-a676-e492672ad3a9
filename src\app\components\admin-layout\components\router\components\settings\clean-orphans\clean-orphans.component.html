<div class="card" style="height: 110px">
  <div style="width: 100%">
    <app-header-with-buttons
      [isBackButtonEnabled]="true"
      (cardBackButtonClick)="redirectToSettings()"
      [cardTitle]="'NEW Clean & Export as DSA9'"
      [cardDescription]="'Analysis and cleaning of orphaned levels'"
    >
    </app-header-with-buttons>
  </div>
</div>

<div class="main-div">
  <div class="container-fluid">
    <div
      class="card"
      [ngStyle]="{ 'padding-bottom': listOrphons.length == 0 ? '30px' : '0' }"
    >
      <div class="list-header-orphans">
        <!--List no Orphans-->
        <ng-container *ngIf="listEmptyOfOrphans.length > 0">
          <div class="list-orphans">
            <div class="titleCategory" style="margin-bottom: 10px">
              <span>Database List without orphans</span>
            </div>
            <table id="customers">
              <tr *ngFor="let select of listEmptyOfOrphans; let i = index">
                <td>{{i+1}}</td>
                <td style="text-align: left"><span style="font-weight: 700;">List: </span>{{ select }} 🠪 <span style="font-weight: 700;">Ok</span></td>
              </tr>
            </table>
          </div>
        </ng-container>

        <!--List Orphans non-empty-->
        <ng-container *ngIf="listOrphansNoEmpty.length > 0">
          <div class="list-orphans">
            <div class="titleCategory" style="margin-bottom: 10px">
              <span>List of non - clean orphan from the database</span>
            </div>
            <table id="customers">
              <tr *ngFor="let noEmpty of listOrphansNoEmpty; let e = index" style="cursor: pointer !important;" (click)="getNoClenOrphan(noEmpty, e)">
                <td>{{e+1}}</td>
                <td class="noEmpty"><span style="font-weight: 700; color: black;">List: </span>{{ noEmpty.name }}</td>
                <td class="noEmpty"><span style="font-weight: 700; color: black;">Amount: </span> {{ noEmpty.amount }}</td>
              </tr>
            </table>
          </div>
        </ng-container>
      </div>

      <ng-container *ngIf="listOrphons.length > 0">
        <table class="table-bordered">
          <thead>
            <tr>
              <th colspan="7" class="trBC">
                <div style="display: flex">
                  <h3 style="margin: auto">{{ titleOrphans }}</h3>
                  <ng-container *ngIf="listOrphons.length > 0">
                    <button
                      class="btn btn-danger btn-fill btn-removeAll"
                      (click)="removeAllOrphans()"
                    >
                      Remove all
                    </button>
                    <ng-container *ngIf="infoIndex < 11">
                      <button class="btn btn-primary btn-fill btn-removeAll"
                      (click)="nextAllOrphans()">
                      Next orphans
                    </button>
                    </ng-container>  
                  </ng-container>
                </div>
              </th>
            </tr>
            <tr>
              <th>INDEX</th>
              <th>TYPE</th>
              <th colspan="2">ID</th>
              <th>Hierarchy</th>
              <th>Detail</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            <!-- Exemplo de Linhas com dados -->
            <ng-container *ngFor="let cond of listOrphons | searchResultFilter : filterTypeName; let i = index">
              <tr
                [title]="cond.id" [ngClass]="cond?.wasRemoved ? '' : ' tr-clickable'">
                <td>{{ i + 1 }}</td>
                <td (click)="redirectTo(cond)">{{ cond.typeName }}</td>
                <td (click)="redirectTo(cond)">
                  <i [ngClass]="cond | searchResultIcon"></i>
                </td>
                <td (click)="redirectTo(cond)">{{ cond.id }}</td>
                <td (click)="redirectTo(cond)" style="text-align: left">
                  {{ cond.local }}
                </td>
                <td (click)="redirectTo(cond)" class="td-30">
                  {{ cond?.label }}
                </td> 
                <td class="td-actions" style="display: flex; justify-content: center" disabled="cond.label">
                  <div style="width: 50px">
                    <button class="btn btn-danger btn-fill btn-remove" style="padding: 0 !important" (click)="removeLineOrphan(cond.id)">
                      <i _ngcontent-hkn-c250="" class="pe-7s-close"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </ng-container>
    </div>
  </div>
</div>
