import { Component, Input, OnInit } from '@angular/core';
import { RoadBlock } from 'src/app/lib/@bus-tier/models/RoadBlock';
import { RoadBlockType, typeNames } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard';
import { ItemService, CharacterService, ClassService, LevelHelperService } from 'src/app/services';
import { Router } from '@angular/router';
import { LabelColorService } from 'src/app/services/label-color.service';

@Component({
  selector: 'roadblock-info',
  templateUrl: './roadblock-info.component.html',
  styleUrls: ['./roadblock-info.component.scss']
})
/**
 * Roadblock info component that displays roadblock requirements and their details.
 * Handles both single roadblock and multiple roadblocks with AND/OR conditions.
 * Shows detailed information about different roadblock types (items, characters, classes, etc.).
 */
export class RoadblockInfoComponent implements OnInit {
  @Input() roadblock: RoadBlock;
  @Input() roadblocks: RoadBlock[];
  @Input() andOrCondition: string;

  public roadblockTypeName: string = '';
  public roadblockInfos: Array<{
    roadblock: RoadBlock;
    typeName: string;
    displayText: string;
  }> = [];

  constructor(
    private _itemService: ItemService,
    private _characterService: CharacterService,
    private _classService: ClassService,
    private _levelHelperService: LevelHelperService,
    private _labelColorService: LabelColorService,
    private _router: Router
  ) {}

  /**
   * Initialize the component by processing roadblock data and generating display information.
   */
  ngOnInit(): void {
    if (this.roadblocks?.length > 0) {
      this.roadblockInfos = this.roadblocks.map(roadblock => ({
        roadblock,
        typeName: typeNames[roadblock.Type] || 'Unknown',
        displayText: this.getDisplayTextForRoadblock(roadblock)
      }));
    }
    else if (this.roadblock) {
      this.roadblockTypeName = typeNames[this.roadblock.Type] || 'Unknown';
    }
  }

  /**
   * Get display text for a single roadblock (used for backward compatibility).
   */
  getDisplayText(): string {
    if (!this.roadblock) return '';
    return this.getDisplayTextForRoadblock(this.roadblock);
  }

  /**
   * Convert operator text to mathematical symbols
   */
  private getOperatorSymbol(operator: string): string {
    const operatorMap: { [key: string]: string } = {
      'greater': '≥',
      'lesser': '≤',
      'equal': '=',
      'different': '≠'
    };
    return operatorMap[operator] || '≥';
  }

  getDisplayTextForRoadblock(roadblock: RoadBlock): string {
    if (!roadblock) return '';

    let itemName = '';
    let characterName = '';
    let className = '';
    let spokePlace = '';

    // Get item name if roadblock involves an item
    if (roadblock.ItemID && this._itemService) {
      const item = this._itemService.svcFindById(roadblock.ItemID);
      itemName = item?.name || 'Unknown Item';
    }

    // Get character name if roadblock involves a character
    if (roadblock.CharacterID && this._characterService) {
      const character = this._characterService.svcFindById(roadblock.CharacterID);
      characterName = character?.name || 'Unknown Character';
    }

    // Get class name if roadblock involves a class
    if (roadblock.klassId && this._classService) {
      const klass = this._classService.svcFindById(roadblock.klassId);
      className = klass?.name || 'Unknown Class';
    }

    // Get spoke place text if roadblock involves a spoke place
    if (roadblock.spokeElementId && this._levelHelperService) {
      const spokeElement = this._levelHelperService.models.find((sp: any) => sp.elementId === roadblock.spokeElementId);
      if (spokeElement?.text) {
        // Extract only the clean label text without the "[ChoiceBox > Answer]" prefix
        spokePlace = this._labelColorService.extractLabelFromSpokeText(spokeElement.text) || spokeElement.text;
      } else {
        spokePlace = 'Unknown Place';
      }
    }

    switch (+roadblock.Type) {
      case +RoadBlockType.OBTAINED_ITEM:
        const itemOperator = this.getOperatorSymbol(roadblock.operator);
        return `${itemName} ${itemOperator} ${roadblock.ItemAmount || 1}`;
      case +RoadBlockType.TALKED_TO_CHARACTER:
      case +RoadBlockType.SLAIN_CHARACTER:
      case +RoadBlockType.CHARACTER_COLLECTED:
        return characterName;
      case +RoadBlockType.DEFEATED_BOSS:
        return characterName;
      case +RoadBlockType.COLLECTED_CLASS:
        return className;
      case +RoadBlockType.SPOKE_IN:
        return spokePlace;
      case +RoadBlockType.KARMIC_EQUILIBRIUM:
        const karmaOperator = this.getOperatorSymbol(roadblock.operator);
        return `Karma ${karmaOperator} ${roadblock.ItemAmount || 0}`;
      default:
        return typeNames[roadblock.Type] || 'Unknown';
    }
  }

  /**
   * Get the current dialogue ID from the router
   */
  getCurrentDialogueId(): string {
    const url = this._router.url;
    const dialogueId = url.split("/")[4]; // URL structure: /levels/LEVEL_ID/dialogues/DIALOGUE_ID
    return dialogueId;
  }

  /**
   * Check if a roadblock's referenced label exists in the current dialogue
   */
  hasLabelMatchInDialogue(roadblock: RoadBlock): boolean {
    if (+roadblock.Type !== +RoadBlockType.SPOKE_IN || !roadblock.spokeElementId) {
      return false;
    }

    const spokeElement = this._levelHelperService.models.find((sp: any) => sp.elementId === roadblock.spokeElementId);
    if (!spokeElement?.text) {
      return false;
    }

    const referencedLabel = this._labelColorService.extractLabelFromSpokeText(spokeElement.text);
    if (!referencedLabel) {
      return false;
    }

    const currentDialogueId = this.getCurrentDialogueId();
    if (!currentDialogueId) return false;

    // Extract the dialogue prefix (before the #) for label filtering
    const dialoguePrefix = currentDialogueId.split('#')[0];

    // Find all labels in the current dialogue by checking spoke elements
    const dialogueLabels = this._levelHelperService.models
      .filter((sp: any) => sp.elementId && sp.elementId.startsWith(dialoguePrefix + '.'))
      .map((sp: any) => this._labelColorService.extractLabelFromSpokeText(sp.text))
      .filter((label: string) => label && label.trim() !== '');



    // Check if the referenced label exists in the current dialogue
    const hasMatch = dialogueLabels.includes(referencedLabel);


    return hasMatch;
  }

  /**
   * Get the color for a roadblock with matching logic
   */
  getRoadblockColor(roadblock: RoadBlock): string {
    if (+roadblock.Type === +RoadBlockType.SPOKE_IN && roadblock.spokeElementId) {
      const spokeElement = this._levelHelperService.models.find((sp: any) => sp.elementId === roadblock.spokeElementId);
      if (spokeElement?.text) {
        const hasMatch = this.hasLabelMatchInDialogue(roadblock);
        return this._labelColorService.getColorForRoadblockWithMatching(spokeElement.text, hasMatch);
      }
    }
    return '#CCCCCC'; // Default gray for non-spoke roadblocks
  }

  /**
   * Get the icon color for the lock (white for unmatched, white for matched)
   */
  getLockIconColor(roadblock: RoadBlock): string {
    if (+roadblock.Type !== +RoadBlockType.SPOKE_IN) {
      return '#FFFFFF';
    }

    const hasMatch = this.hasLabelMatchInDialogue(roadblock);
    return hasMatch ? '#FFFFFF' : '#FFFFFF'; // Always white for now, but can be customized
  }

  /**
   * Check if roadblock should show a lock icon (SPOKE_IN type)
   */
  shouldShowLockIcon(roadblock: RoadBlock): boolean {
    return +roadblock.Type === +RoadBlockType.SPOKE_IN;
  }
}
