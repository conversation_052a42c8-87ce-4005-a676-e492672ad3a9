import { Gender, IdPrefixes, Voices } from 'src/lib/darkcloud/dialogue-system';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { Base } from './Base';

export class Sound
  extends Base<Data.Hard.ISound, Data.Result.ISound>
  implements Required<Data.Hard.ISound>
{
  public static generateId(index: number): string {
    return IdPrefixes.SOUND + index;
  }
  constructor(index: number, dataAccess: Sound['TDataAccess']) {
    super(
      {
        hard: {
          id: Sound.generateId(index),
        },
      },
      dataAccess
    );
  }
  protected getInternalFetch() {
    return {};
  }
  public get name(): string {
    return this.hard.name;
  }
  public set name(value: string) {
    this.hard.name = value;
  }
  public get voices(): Voices {
    return this.hard.voices;
  }
  public set voices(value: Voices) {
    this.hard.voices = value;
  }
  public get gender(): Gender {
    return this.hard.gender;
  }
  public set gender(value: Gender) {
    this.hard.gender = value;
  }
  public get emotionId(): string {
    return this.hard.emotionId;
  }
  public set emotionId(value: string) {
    this.hard.emotionId = value;
  }
}
