import { Injectable } from '@angular/core';
import { ModelService } from 'src/lib/darkcloud/angular/dsadmin';
import {
  IndexStorageService,
  ReviewService,
  UserSettingsService,
} from 'src/app/services';
import { ModMoonAttributes } from '../lib/@bus-tier/models';
import { Atributte } from '../lib/@bus-tier/models/Atributte';
import { AtributteService } from './atributte.service';


@Injectable({
  providedIn: 'root',
})
export class MODMoonAttributesService extends ModelService<ModMoonAttributes> {

  public override svcPromptCreateNew(...args: any): Promise<ModMoonAttributes> {
    throw new Error('Method not implemented.');
  }

  constructor(
    private _atributteService: AtributteService,
    protected override _reviewService: ReviewService,
    indexStorageService: IndexStorageService,  
    readonly userSettingsService: UserSettingsService,
  ) {
    super(
      {
        defaultConstructor: () =>
          new ModMoonAttributes(0, this.userSettingsService),
      },
      'MODMoonAttributes',
      indexStorageService,
      _reviewService
    );
  }

  public createNewMoonAttribute(): ModMoonAttributes {

    let moon = new ModMoonAttributes(this.svcNextIndex(),this.userSettingsService);
    moon.idsAtributte = [];
    moon.modMoonAtributte = [];
    this._atributteService.models.forEach((atributte) => {
      moon.idsAtributte.push(atributte.id);
      moon.modMoonAtributte.push(''); // Inicializa com string vazia para cada atributo
    });

    this.srvAdd(moon);
    return moon;
  }

}
