import { Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { Character, Weapon } from 'src/app/lib/@bus-tier/models';
import { CharacterService, UserSettingsService } from 'src/app/services';
import { CustomService } from 'src/app/services/custom.service';
import { LanguageService } from 'src/app/services/language.service';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';
import { TranslationService } from 'src/app/services/translation.service';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'enabled-character-selector',
  templateUrl: './enabled-character-selector.component.html',
  styleUrls: ['./enabled-character-selector.component.scss'],
})

export class EnabledCharacterSelector extends TranslatableListComponent<Character> 
{
  @Output() parentEvent  = new EventEmitter<void>();
  @Input() currentWeapon: Weapon;
  @Input() enabledSelectedCharactersId: string[];
  @Input() selectedCharactersId: string[];
  @Output() selectedCharactersIdChange = new EventEmitter<string[]>();
  popupOpen = false;
  selectedCharacters: Character[];
  characters: Character[];

  constructor(
    protected _customService: CustomService,
    protected _characterService: CharacterService,
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    protected override _translationService: TranslationService,
    protected override _languageService: LanguageService
  )
  {
    super(_characterService, _activatedRoute, _userSettingsService, 'name', _translationService, _languageService);
  }
 

 override async lstInit()
  {
    await this._characterService.toFinishLoading();
    this.characters = this._characterService.models;
    this.selectedCharacters = [];
    if(!this.selectedCharactersId) this.selectedCharactersId = [];
    else
    for(let i = 0; i < this.characters.length; i++)
    {
      for(let j = 0; j < this.selectedCharactersId.length; j++)
      {
        if(this.characters[i].id == this.selectedCharactersId[j].trim())
        {
          let character = this._characterService.svcFindById(this.characters[i].id);
          this.selectedCharacters.push(character);
        }
      }
    }
  }

  reset(selectedCharactersIds, enabledSelectedCharactersId)
  {
    this.enabledSelectedCharactersId = enabledSelectedCharactersId;
    this.selectedCharactersId = selectedCharactersIds;
    this.lstInit();
  }

  togglePopup(event)
  {
    if(event && event.target !== event.currentTarget) return
    this.popupOpen = !this.popupOpen;

    if(this.popupOpen) this.characters = this._characterService.models;    
  }

  changeValue(event, character: Character)
  {
    this.isCharacterInEnabledCharacterSelector(character.id);

    if(event.target.checked) this.selectedCharacters.push(character);    
    else this.selectedCharacters = this.selectedCharacters?.filter(c => c.id !== character.id);    
    this.selectedCharactersIdChange.emit(this.selectedCharacters.map(c => c.id));   
    this.parentEvent.emit();
  }


  isCharacterInEnabledCharacterSelector(characterId: string)
  {
    for(let i = 0; i < this.enabledSelectedCharactersId?.length; i++)
    {
      if(this.enabledSelectedCharactersId[i] == characterId)
      {
        this.currentWeapon.charactersId.splice(i, 1);
        break;
      }
    }
  }


  checkCharacter(character: Character):boolean
  {
    let exist = this.selectedCharacters?.filter(c => c.id === character.id);
    return exist?.length > 0;
  }
}
