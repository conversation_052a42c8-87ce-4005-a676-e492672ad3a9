import { Data } from "src/lib/darkcloud/angular/dsadmin";
import { IdPrefixes } from "src/lib/darkcloud/dialogue-system";
import { Base } from "./Base";
import { ItemClass } from "./ItemClass";

export class Tag
extends Base<Data.Hard.ITag, Data.Result.ITag>
implements Required<Data.Hard.ITag>
{
    public static generateId(index: number)
    {
        return IdPrefixes.TAG + index;
    }

    constructor(
        index: number,
        dataAccess: Tag['TDataAccess']
    )
    {
        super({hard: {id: Tag.generateId(index)}}, dataAccess);
    }

    public get name()
    {
        return this.hard.name;
    }

    public set name(value: string)
    {
        this.hard.name = value;
    }

    public get description()
    {
        return this.hard.description;
    }

    public set description(value: string)
    {
        this.hard.description = value;
    }

    public get notes()
    {
        return this.hard.notes;
    }

    public set notes(value: string)
    {
        this.hard.notes = value;
    }
}
