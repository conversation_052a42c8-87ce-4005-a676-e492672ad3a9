import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { Base } from './Base';

export class Theme
  extends Base<Data.Hard.ITheme, Data.Result.ITheme>
  implements Required<Data.Hard.ITheme>
{
  public static generateId(index: number): string {
    return IdPrefixes.ANIMATIC_THEME + index;
  }
  constructor(index: number, name: string, dataAccess: Theme['TDataAccess']) {
    super(
      {
        hard: {
          id: Theme.generateId(index),
          name,
        },
      },
      dataAccess
    );
  }
  protected getInternalFetch() {
    return {};
  }
  public get name(): string {
    return this.hard.name;
  }
  public set name(value: string) {
    this.hard.name = value;
  }
}
