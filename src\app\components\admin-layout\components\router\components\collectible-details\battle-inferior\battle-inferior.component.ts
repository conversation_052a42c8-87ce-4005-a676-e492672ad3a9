import { ChangeDetectorRef, Component, Input } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { BattleInferior } from 'src/app/lib/@bus-tier/models';
import { Button } from 'src/app/lib/@pres-tier/data';
import { AreaService, BattleInferiorService, CharacterService, ClassService, UserSettingsService } from 'src/app/services';
import { Alert } from 'src/lib/darkcloud';
import { ICollectibleDetails } from 'src/lib/darkcloud/angular/dsadmin/v9/data/result';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc';
import { GameTypes } from 'src/lib/darkcloud/dialogue-system';

@Component({
  selector: 'app-battle-inferior',
  templateUrl: './battle-inferior.component.html',
  styleUrls: ['./battle-inferior.component.scss']
})
export class BattleInferiorComponent extends SortableListComponent<BattleInferior> implements ICollectibleDetails {

  @Input() character: string = '';
  currentCharacter: BattleInferior;
  hierachyCode: string = '';
  valueBossLevel: string;
  nameClass: string;
  nameRarity: string;
  type: string;


  constructor(
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    private _battleInferiorService: BattleInferiorService,
    private _characterService: CharacterService,
    private _areaService: AreaService,
    private ref: ChangeDetectorRef,
    private _classService: ClassService,
  ) {
    super(_battleInferiorService, _activatedRoute, _userSettingsService, 'name');
  }

  public readonly excelButtonTemplate: Button.Templateable =
    {
      title: 'Paste content from excel',
      onClick: this.onExcelPaste.bind(this),
      iconClass: 'excel-icon',
      btnClass: Button.Klasses.FILL_ORANGE,
    };


  override async ngOnInit(): Promise<void> {
    await this._battleInferiorService.toFinishLoading();
    this.currentCharacter = this._battleInferiorService.models.find(modifier => modifier.character === this.character);


    let valueCharacter = this.currentCharacter === undefined ? this.character : this.currentCharacter.character;

    setTimeout(() => {
    if (this.currentCharacter !== undefined) {
      const op = this.currentCharacter.bl != undefined ? this.currentCharacter.bl : 0;
      this.valueBossLevel = `BL: ${op}`;
    } else {
      this.valueBossLevel = 'BL: 0';
    }

    const character = this._characterService.models.find(x => x.id === valueCharacter);
    this.nameRarity = character?.rarity;
    this.nameClass = this._classService.models.find(x => x.id === character?.classId)?.name;
    this.type = GameTypes.characterTypeName[character.type];
  }, 100)

    this.takeCharacterHierachyCode();
    this.removeDuplicateIds();
  }
  
  public async removeDuplicateIds() {
    const ids = this._battleInferiorService.models.map(model => model.id);
    const uniqueIds = [...new Set(ids)];
    const duplicateIds = ids.filter(id => ids.indexOf(id) !== ids.lastIndexOf(id));
  
    duplicateIds.forEach(id => {
      this._battleInferiorService.svcToRemove(id);
    });
  }


  reset(character) {
    this.character = character;
    this.ngOnInit();
  }

  async onExcelPaste() {
    try {
      const text = await navigator.clipboard.readText();
      const lines = text.split(/\r?\n/).filter(line => line);

      if (!lines.length) {
        Alert.showError("No data was copied from the Excel sheet.");
        return;
      }


      if (this.DisplayErrors(lines)) return;

      for (let l = 0; l < lines.length; l++) {
        let line = lines[l];
        let cols = line.split(/\t/);

        if (cols.length < 5) {
          Alert.showError(`The data on line ${l + 1} is incomplete. Ensure you have at least 5 columns.`);
          return;
        }

        // Se currentCharacter está definido, atualizar os campos diretamente
        if (this.currentCharacter !== undefined) {
          this.currentCharacter.bl = this.validateNumericValue(cols[0], 'bl', l);
          this.currentCharacter.hp = this.validateNumericValue(cols[1], 'hp', l);
          this.currentCharacter.atk = this.validateNumericValue(cols[2], 'atk', l);
          this.currentCharacter.def = this.validateNumericValue(cols[3], 'def', l);
          this.currentCharacter.atkLim = this.validateNumericValue(cols[4], 'atkLim', l);

          // Atualizar o serviço com o objeto modificado
          this._battleInferiorService.svcToModify(this.currentCharacter);
        } else {
          // Se currentCharacter não existe, criar um novo battle
          let battle = this._battleInferiorService.createNewBattleInferior(this.character);

          battle.bl = this.validateNumericValue(cols[0], 'bl', l);
          battle.hp = this.validateNumericValue(cols[1], 'hp', l);
          battle.atk = this.validateNumericValue(cols[2], 'atk', l);
          battle.def = this.validateNumericValue(cols[3], 'def', l);
          battle.atkLim = this.validateNumericValue(cols[4], 'atkLim', l);

          this._battleInferiorService.svcToModify(battle);
        }

        this._battleInferiorService.toSave();
      }

      this.ref.detectChanges();
      this.ngOnInit();
      Alert.ShowSuccess('Battle List imported successfully!');
    } catch (error) {
      Alert.showError("An error occurred during the paste process.");
    }
  }

  // Função para validar os valores numéricos
  validateNumericValue(cellValue: string, fieldName: string, lineIndex: number): number | undefined {
    if (cellValue?.trim()) {
      const numericValue = parseFloat(cellValue.replace(/[\s.,]/g, '').replace(',', '.'));

      if (isNaN(numericValue)) {
        Alert.showError(`Invalid numeric value in column ${fieldName} at line ${lineIndex + 1}`);
        throw new Error(`Invalid numeric value in column ${fieldName} at line ${lineIndex + 1}`);
      }
      return numericValue;
    }
    return undefined; // Retorna undefined se o valor estiver vazio
  }

  DisplayErrors(array) {
    let count = array[0].split(/\t/);

    if (count[0] === "") {
      Alert.showError("You are probably copying a blank column!");
      return true;
    }

    return false;
  }


  takeCharacterHierachyCode() {
    let areaId = this._characterService.svcFindById(this.character).areaId
    this.hierachyCode = this._areaService.svcFindById(areaId)?.hierarchyCode
  }

  async changeBattleupgradeValue(battleInferior: BattleInferior, value: string, field: string) {
    if (battleInferior !== undefined) {
      battleInferior[field] = value == '' ? undefined : +value;
      await this._battleInferiorService.svcToModify(battleInferior);
      await this._battleInferiorService.toSave();
    } else {
      let battle = this._battleInferiorService.createNewBattleInferior(this.character);
      battle[field] = value == '' ? undefined : +value;
      await this._battleInferiorService.svcToModify(battle);
      await this._battleInferiorService.toSave();
    }

    this.ngOnInit();
  }
}



