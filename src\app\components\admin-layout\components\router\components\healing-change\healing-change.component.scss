.main-menu-efect {
  padding-right: 15px;
  padding-left: 15px; 
  min-height: calc(100% - 210px);
}

.update {
    margin: -2px -2px 0;
    padding-bottom: 5px;
    background-color: #f7f7f6;
    margin-top: 15px;
}

.card-header-content {
display: block;
margin-left: 20px;
margin-right: 15px;
width: 30%;
}

.card {
  // padding-top: 8px !important;
   padding-top: 17px !important;
   padding-bottom: 10px;
 }

 .card-header-content {
   display: block;
   margin-left: 30px;
   margin-right: 15px;
   width: 30%;
 }

 //tabela
 .card-container 
{
  display: flex;
  flex-direction: column;
  align-items: center;

  .card 
  {
    border: 1px solid #ccc;
    padding: 15px;
    margin: 5px;
    width: 50vw;
    opacity: 1;
    transition: opacity 0.5s ease-in-out;
  } 
}


h3{
    font-size: 28px;
    margin: 10px 0 10px;
}

.borderList {
    border: 1px solid #ddd !important;
}

.addButton
{  
 position: absolute;
    top: -1px;
    right: 13px;
}

  .default-color {
    background-color: #AEAAAA !important;
  }

  .aligTitle {
    background-color: white !important;
    color: #565656 !important;
    text-align-last: center !important;
    width: 8%;
  }

  .noCursor {
    cursor: default !important;
  }

  .btn-ailmentChange {
    display: flex;
    margin-bottom: 10px;
    position: absolute;
    right: 30px;
  }
  
  .text-center {
    text-align: center !important;
  }
  .width-buscontext {
    width: 400px !important;
  }
  .paddingTop {
    padding-top: 0px !important;
  }

  // Estilos para a tabela Healing Change
  .healing-table-container {
    overflow-x: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
  }

  .healing-table {
    width: 100%;
    border-collapse: collapse;
    font-family: Arial, sans-serif;
    font-size: 14px;
    background-color: white;

    th, td {
      border: 1px solid #666;
      text-align: center;
      padding: 8px 12px;
      vertical-align: middle;
    }

    thead {
      background-color: #666;
      color: white;
      font-weight: bold;

      .ailment-header {
        background-color: #666;
        color: white;
        font-weight: bold;
        width: 85px;
        vertical-align: middle;
      }

      .turn-header {
        background-color: #666;
        color: #00BFFF; // Cor azul como na imagem
        font-weight: bold;
        text-align: center;
        position: relative;
      }

      .turn-number {
        background-color: #ddd !important;
        color: #333 !important;
        font-weight: bold;
        min-width: 60px;
        width: 60px;
      }
    }

    tbody {
      .ailment-row {
        &:nth-child(even) {
          background-color: #f9f9f9;
        }

        .ailment-name {    
          font-weight: bold;        
          width: 85px;   
        }

        .percentage-cell {
          background-color: white;  
          font-weight: normal;
          min-width: 60px;
          width: 60px;
          padding: 4px;

          &.max-percentage {
            background-color: #ddd;
            font-weight: bold;
          }

          .background-input-table-color {
            width: 100%;
            border: 1px solid #ccc;
            border-radius: 3px;
            padding: 4px 6px;
            font-size: 12px;
            text-align: center;
            background-color: white;

            &:focus {
              border-color: #007bff;
              outline: none;
              box-shadow: 0 0 3px rgba(0, 123, 255, 0.25);
            }

            &.empty-input {
              background-color: #f8f9fa;
              border-color: #dc3545;
            }
          }
        }
      }
    }
  }
  
h3{
    font-size: 25px;
    margin: 10px 0 10px;
}

span {
      color: #00BFFF;
      font-size: 25px;
      font-weight: 600;
    }

.addButton
{  
 position: absolute;
    top: -3.5px;
    right: 13px;

}
.btn-excel {
  top: 330px;
  position: absolute;
  right: 50px;
}

.btn-excel-boos {
  position: absolute;
    display: flex;
    right: 48px;
    margin-top: -57px;
}