import { Data } from "src/lib/darkcloud/angular/dsadmin";
import { IdPrefixes } from "src/lib/darkcloud/dialogue-system";
import { Base } from "./Base";

export class StatusInfo
extends Base<Data.Hard.IStatusInfo, Data.Result.IStatusInfo>
implements Required<Data.Hard.IStatusInfo>
{
    static generateId(index: number): string
    {
        return IdPrefixes.STATUSINFO + index;
    }

    constructor(
        index: number,
        status: string,
        dataAccess: StatusInfo['TDataAccess']
    ) {
        super(
            {
                hard: {
                    id: StatusInfo.generateId(index),
                    status
                },
            },
            dataAccess
        );
    }
   
  
    public get status(): string
    {
      return this.hard.status;
    }
    public set status(value: string)
    {
      this.hard.status = value;
    }

    public get acronym(): string
    {
      return this.hard.acronym;
    }
    public set acronym(value: string)
    {
      this.hard.acronym = value;
    }

    public get resistence(): number
    {
      return this.hard.resistence;
    }
    public set resistence(value: number)
    {
      this.hard.resistence = value;
    }

    public get weakeness(): number
    {
      return this.hard.weakeness;
    }
    public set weakeness(value: number)
    {
      this.hard.weakeness = value;
    }

    public get spDef(): string
    {
      return this.hard.spDef;
    }
    public set spDef(value: string)
    {
      this.hard.spDef = value;
    }
    public get idDefensesSkill(): string
    {
      return this.hard.idDefensesSkill;
    }
    public set idDefensesSkill(value: string)
    {
      this.hard.idDefensesSkill = value;
    }
    public get spAtk(): number
    {
      return this.hard.spAtk;
    }
    public set spAtk(value: number)
    {
      this.hard.spAtk = value;
    }

    public get ascensionOrder(): number
    {
      return this.hard.ascensionOrder;
    }
    public set ascensionOrder(value: number)
    {
      this.hard.ascensionOrder = value;
    }

    public get character(): string
    {
      return this.hard.character;
    }
    public set character(value: string)
    {
      this.hard.character = value;
    }
    public get idSkill(): string
    {
      return this.hard.idSkill;
    }
    public set idSkill(value: string)
    {
      this.hard.idSkill = value;
    }
}