import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';

export class PassiveAllowed  extends Base<Data.Hard.IPassiveAllowed, Data.Result.IPassiveAllowed> implements Required<Data.Hard.IPassiveAllowed>
{
  public static generateId(index: number): string {
    return IdPrefixes.PASSIVEALLOWED + index;
  }

  constructor( index: number, dataAccess: PassiveAllowed['TDataAccess']) 
  {
    super(
    {
      hard: 
      {
        id: PassiveAllowed.generateId(index),
      },
    },
    dataAccess
    );
  }
  protected getInternalFetch() 
  {
    return {};
  }


  public get idValue1(): string 
  {
    return this.hard.idValue1;
  }
  public set idValue1(value: string) 
  {
    this.hard.idValue1 = value;
  }
 
  public get idValue2(): string 
  {
    return this.hard.idValue2;
  }
  public set idValue2(value: string) 
  {
    this.hard.idValue2 = value;
  }

}
