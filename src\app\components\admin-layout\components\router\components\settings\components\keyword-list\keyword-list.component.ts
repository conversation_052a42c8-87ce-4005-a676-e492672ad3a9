import { Keyword } from 'src/app/lib/@bus-tier/models';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { LevelService } from 'src/app/services/level.service';
import { KeywordService } from 'src/app/services/keyword.service';

@Component({
  selector: 'app-keyword-list',
  templateUrl: './keyword-list.component.html',
})

export class KeywordListComponent implements OnInit, OnDestroy 
{
  public keywords: Keyword[] = [];
  timeout:any;
  timeout2:any;
  timeout3:any;

  constructor(
    private _keywordService: KeywordService,
    private _levelService: LevelService
  ) {}
  
  ngOnInit(): void 
  {
    this.renderList();
  }

  trackByIndex(index: number, icon: any): any 
  {
    return index;
  }

  async onChange() 
  {
    await this._keywordService.toSave();
  }

  renderList() 
  {
    this._keywordService.InitValues();
    this.keywords = this._keywordService.models;
    this.timeout = setTimeout(() => 
    {
        this._keywordService.InitValues();
        this.keywords = this._keywordService.models;
    }, 900)
    this.timeout2 = setTimeout(() => 
    {
        this._keywordService.InitValues();
        this.keywords = this._keywordService.models;
    }, 2000)
    this.timeout3 = setTimeout(() => 
    {
        this._keywordService.InitValues();
        this.keywords = this._keywordService.models;
    }, 4000)
  }

  ngOnDestroy() 
  {
    clearInterval(this.timeout);
    clearInterval(this.timeout2);
    clearInterval(this.timeout3);
  }
}
