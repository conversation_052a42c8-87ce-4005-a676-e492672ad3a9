import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';

export class OpenAIEnvironment  extends Base<Data.Hard.IOpenAIEnvironment, Data.Result.IOpenAIEnvironment> implements Required<Data.Hard.IOpenAIEnvironment>
{
  public static generateId(index: number): string {
    return IdPrefixes.OPENAIENVIRONMENT + index;
  }

  constructor( index: number, dataAccess: OpenAIEnvironment['TDataAccess']) 
  {
    super(
    {
      hard: 
      {
        id: OpenAIEnvironment.generateId(index),     
      },
    },
    dataAccess
    );
  }
  protected getInternalFetch() 
  {
    return {};
  }

  public get name(): string 
  {
    return this.hard.name;
  }
  public set name(value: string) 
  {
    this.hard.name = value;
  }
  public get apiKey(): string 
  {
    return this.hard.apiKey;
  }
  public set apiKey(value: string) 
  {
    this.hard.apiKey = value;
  }
  public get model(): string 
  {
    return this.hard.model;
  }
  public set model(value: string) 
  {
    this.hard.model = value;
  }
  public get temperature(): number 
  {
    return this.hard.temperature;
  }
  public set temperature(value: number) 
  {
    this.hard.temperature = value;
  }
  public get limitForReview(): number 
  {
    return this.hard.limitForReview;
  }
  public set limitForReview(value: number) 
  {
    this.hard.limitForReview = value;
  }  
}
