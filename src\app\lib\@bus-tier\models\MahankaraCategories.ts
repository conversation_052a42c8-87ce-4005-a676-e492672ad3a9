import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';

export class MahankaraCategories extends Base<Data.Hard.IMahankaraCategories, Data.Result.IMahankaraBehavior> implements Required<Data.Hard.IMahankaraCategories>
{
  public static generateId(index: number): string {
    return IdPrefixes.MAHANKARACATEGORIES + index;
  }

  constructor( index: number, dataAccess: MahankaraCategories['TDataAccess']) 
  {
    super(
    {
      hard: 
      {
        id: MahankaraCategories.generateId(index),     
      },
    },
    dataAccess
    );
  }
  protected getInternalFetch() 
  {
    return {};
  }
  public get indexCategory(): string
  {
    return this.hard.indexCategory;
  }
  public set indexCategory(value: string) 
  {
    this.hard.indexCategory = value;
  }
  public get speechCategory(): string
  {
    return this.hard.speechCategory;
  }
  public set speechCategory(value: string) 
  {
    this.hard.speechCategory = value;
  }
  public get description(): string
  {
    return this.hard.description;
  }
  public set description(value: string) 
  {
    this.hard.description = value;
  }

}
