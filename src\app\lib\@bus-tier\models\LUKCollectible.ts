import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';
import { intFunction } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard';

export class LUKCollectible extends Base<Data.Hard.ILUKCollectible, Data.Result.ILUKCollectible> implements Required<Data.Hard.ILUKCollectible>
{
  public static generateId(index: number): string {
    return IdPrefixes.LUKCOLLECTIBLE + index;
  }

  constructor( index: number, dataAccess: LUKCollectible['TDataAccess']) 
  {
    super(
    {
      hard: 
      {
        id: LUKCollectible.generateId(index),     
      },
    },
    dataAccess
    );
  }
  protected getInternalFetch() 
  {
    return {};
  }
  public get valuesInt(): intFunction[]
  {
    return this.hard.valuesInt;
  }
  public set valuesInt(value: intFunction[]) 
  {
    this.hard.valuesInt = value;
  } 
  public get idChacracter(): string
  {
    return this.hard.idChacracter;
  }
  public set idChacracter(value: string) 
  {
    this.hard.idChacracter = value;
  } 
  public get nameChacracter(): string
  {
    return this.hard.nameChacracter;
  }
  public set nameChacracter(value: string) 
  {
    this.hard.nameChacracter = value;
  } 

}
