import { PiecesModule } from 'src/app/components/pieces/pieces.module';
import { ObjectiveSublistComponent } from './components/objective-sublist/objective-sublist.component';
import { MissionListComponent } from './mission-list.component';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BrowserModule } from '@angular/platform-browser';
import { FormsModule } from '@angular/forms';
import { MajorModule } from 'src/app/major.module';
import { ContentInputModule } from 'src/app/components/content-input/content-input.module';
import { PopupModule } from 'src/app/components/popup/popup.module';
import { RouterModule } from '@angular/router';
import { MissionNotesComponent } from './components/mission-notes/mission-notes.component';

@NgModule({
  imports: [
    RouterModule,
    CommonModule,
    BrowserModule,
    FormsModule,
    MajorModule,
    ContentInputModule,
    PopupModule,
    PiecesModule,
  ],
  declarations: [MissionListComponent, ObjectiveSublistComponent, MissionNotesComponent],
  exports: [MissionListComponent],
})
export class MissionListModule {}
