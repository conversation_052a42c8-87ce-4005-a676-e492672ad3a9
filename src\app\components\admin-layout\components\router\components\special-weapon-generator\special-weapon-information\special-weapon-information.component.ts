import { Component, OnInit } from '@angular/core';
import { Item, SpecialWeapon } from 'src/app/lib/@bus-tier/models';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';
import { ItemService, ParticleService } from 'src/app/services';
import { CustomService } from 'src/app/services/custom.service';
import { ItemClassService } from 'src/app/services/item-class.service';
import { Button } from 'src/app/lib/@pres-tier/data';
import { SpecialWeaponService } from 'src/app/services/special-weapon.service';
import { Alert } from 'src/lib/darkcloud';
import { SpinnerService } from './../../../../../../../spinner/spinner.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-special-weapon-information',
  templateUrl: './special-weapon-information.component.html',
  styleUrls: ['./special-weapon-information.component.scss'],
})
export class SpecialWeaponInformationComponent implements OnInit 
{
  public readonly excelButtonTemplate: Button.Templateable = 
  {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  custom: Custom;
  item: Item;
  specialWeapons: SpecialWeapon[];
  ingredients: Item[];
  blueprints: Item[];
  uniqueIngredients: Item[];
  description: string;
  ids = []
  selectedClasses = []
  constructor(
    private spinnerService: SpinnerService,
    protected _customService: CustomService,
    protected _itemService: ItemService,
    private _particleService: ParticleService,
    private _specialWeaponService: SpecialWeaponService,
    private _itemClassService: ItemClassService,
    private _router: Router
  ) {}


  async ngOnInit(): Promise<void> 
  {
    await this._customService.toFinishLoading();
    await this._particleService.toFinishLoading();
    await this._itemService.toFinishLoading();
    await this._itemClassService.toFinishLoading();
    await this._specialWeaponService.toFinishLoading();

    this.custom = await this._customService.svcGetInstance();

    if (!this.custom.particlesOrder) this.custom.particlesOrder = [];
    
 
    this.ingredients = this._itemService.models.filter(
      (item) => item.tagIds?.includes('tg19') //tg19 = common ingredients in tags pkg
    );

    this.uniqueIngredients = this._itemService.models.filter(
      (item) => item.tagIds?.includes('tg20') //tg20 = unique ingredients in tags pkg
    );

    this.blueprints = [];
    //item class pkg has a blueprint array by id = IC16
    this._itemClassService.svcFindById('IC16').itemIds.forEach((itemId) => 
    {
      this.blueprints.push(this._itemService.svcFindById(itemId));
    });

    this.selectClasses();
    this.ingredientsOrderInitialization()
    this.description = `Showing ${this.specialWeapons.length} results`;  


  }
   
  selectClasses() 
  {
    this.selectedClasses = this._itemClassService.models.filter((klass) => 
    {
      return this.custom.specialWeaponClassItem.includes(klass.id);
    });
    let itemIds = [];
    
    
    this.selectedClasses.forEach((itemClass) => 
    {
      itemClass.itemIds.forEach((itemId) => 
      {
        itemIds.push(this._itemService.svcFindById(itemId));
      });
    });
    
    this.ids = [];
    itemIds.forEach((id) => 
    {
      this.ids.push(id?.id)
    });

    
    this.specialWeapons = [];

    this._specialWeaponService.models.forEach((sw) =>
    {
      if(this.ids.includes(sw.itemId))
      {
        this.specialWeapons.push(sw);
        this.ids = this.ids.filter(id => id !== sw.itemId);
      }     
    });
    
    this.ids.forEach(id => 
    {      
      this.specialWeapons.push(this._specialWeaponService.createNewPowerUp(id));
    })

    this._specialWeaponService.toSave();
    this.specialWeapons.concat(this._specialWeaponService.models);
  }

  async ingredientsOrderInitialization()
  {
    if(this.specialWeapons[0]?.ingredientsOrder?.length !== this.ingredients.length || !this.specialWeapons[0]?.ingredientsOrder)
    {
      let ids : string[] = [];
      this.ingredients.forEach((item) => ids.push(item.id));

      this.specialWeapons.forEach(sw=> sw.ingredientsOrder = ids);
      await this._specialWeaponService.toSave();
    }
    else
    {
      let ids = this.specialWeapons[0].ingredientsOrder;
      this.ingredients.sort((a,b)=>
      {
        return ids.indexOf(a.id) - ids.indexOf(b.id);
      })
      await this._specialWeaponService.toSave();
    }
  }

  async changeElementsPosition(newElementName, oldElementName)
  {
    Alert.showError('','There is another value with the same name. Change it to avoid repetition');

    let newElementHolder;
    let oldElementHolder;

    let ids = this.specialWeapons[0].ingredientsOrder;
    newElementName = this.ingredients.filter(i=> this.simplifyString(i.name) == this.simplifyString(newElementName));
    newElementName = newElementName[0].id;


    for(let l = 0; l < ids.length; l++)
    {
      if(ids[l] === newElementName) newElementHolder = l;
      if(ids[l] === oldElementName) oldElementHolder = l;
    }
    
    let oldElement = ids[oldElementHolder];
    let newElement = ids[newElementHolder];


    ids.splice(newElementHolder, 1, oldElement);
    ids.splice(oldElementHolder, 1, newElement);
    
    this.specialWeapons.forEach(sw=> sw.ingredientsOrder = ids);
    
    this.ingredients.sort((a,b)=>
    {
      return ids.indexOf(a.id) - ids.indexOf(b.id);
    })
    
    await this._specialWeaponService.toSave();
  }


  GetSpecialWeaponName(memoryModuleId: string): string 
  {
    return this._itemService.svcFindById(this._specialWeaponService.svcFindById(memoryModuleId).itemId)?.name;
  }

  changeBAL(specialWeapon: SpecialWeapon, bal: number) 
  {
    specialWeapon.bpArchiveLevel = bal;
    this._specialWeaponService.svcToModify(specialWeapon);
    this._specialWeaponService.toSave();
  }

  changeSouls(specialWeapon: SpecialWeapon, souls: number) 
  {
    specialWeapon.souls = souls;
    this._specialWeaponService.svcToModify(specialWeapon);
    this._specialWeaponService.toSave();
  }

  changeTime(specialWeapon: SpecialWeapon, time: number) 
  {
    specialWeapon.time = time;
    this._specialWeaponService.svcToModify(specialWeapon);
    this._specialWeaponService.toSave();
  }

  changeSkipPrice(specialWeapon: SpecialWeapon, skipPrice: number) 
  {
    specialWeapon.rubies = skipPrice;
    this._specialWeaponService.svcToModify(specialWeapon);
    this._specialWeaponService.toSave();
  }

  changeBlueprint(specialWeapon: SpecialWeapon, blueprint: string) 
  {
    specialWeapon.bpId = blueprint;
    this._specialWeaponService.svcToModify(specialWeapon);
    this._specialWeaponService.toSave();
  }

  changeUniqueIngredient(
    specialWeapon: SpecialWeapon,
    uniqueIngredient: string
  ) {
    specialWeapon.ingredientId = uniqueIngredient;
    this._specialWeaponService.svcToModify(specialWeapon);
    this._specialWeaponService.toSave();
  }

  GetAmountValue(specialWeapon: SpecialWeapon, index: number) 
  {
    if (!specialWeapon.ingredientsAmount) specialWeapon.ingredientsAmount = [];
    return specialWeapon.ingredientsAmount[index];
  }

  changeIngredientAmount(specialWeapon: SpecialWeapon,amount: number,index: number) 
  {
    specialWeapon.ingredientsAmount[index] = amount;
    this._specialWeaponService.svcToModify(specialWeapon);
    this._specialWeaponService.toSave();
  }

  async onExcelPaste(): Promise<void> 
  {
    this.spinnerService.setState(true);
    const text = await navigator.clipboard.readText();
  
    const lines = text.split(/\r?\n/).filter(line => line);
    let bpaLevel = undefined;
    if(this.DisplayErrors(lines)) return;

    for (let l = 0; l < lines.length; l++) 
    {
      let line = lines[l];
      let cols = line.split(/\t/);

      let item = this._itemService.models.find((i) => this.simplifyString(i.name) == this.simplifyString(cols[1]));
      if (!item) continue;

      let specialWeapon = this.specialWeapons.find((i) => i.itemId == item.id);
      if (!specialWeapon) continue;

      if (cols[0]?.trim()) 
      {
        specialWeapon.bpArchiveLevel = +cols[0]
          .split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.')
        bpaLevel = specialWeapon.bpArchiveLevel;
      } 
      else 
      {
        specialWeapon.bpArchiveLevel = bpaLevel;
      }
      if (cols[2]?.trim()) {
        specialWeapon.souls = +cols[2]
          .split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.')
      } else {
        specialWeapon.souls = undefined;
      }
      if (cols[3]?.trim()) {
        specialWeapon.time = +cols[3]
          .split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.')
      } else {
        specialWeapon.time = undefined;
      }
      if (cols[4]?.trim()) {
        specialWeapon.rubies = +cols[4]
          .split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.')
      } else {
        specialWeapon.rubies = undefined;
      }
      if (cols[5]?.trim()) {
        let itemBP = this._itemService.models.find(
          (i) =>
            this.simplifyString(i.name) ==
            this.simplifyString('Blueprint: ' + cols[5])
        );
        if (itemBP) {
          specialWeapon.bpId = itemBP.id;
        } else {
          specialWeapon.bpId = undefined;
        }
      } else {
        specialWeapon.bpId = undefined;
      }
      if (cols[6]?.trim()) {
        let itemUI = this._itemService.models.find(
          (i) => this.simplifyString(i.name) == this.simplifyString(cols[6])
        );
        if (itemUI) {
          specialWeapon.ingredientId = itemUI.id;
        } else {
          specialWeapon.ingredientId = undefined;
        }
      } else {
        specialWeapon.ingredientId = undefined;
      }

      for (let i = 7; i < cols.length; i++) {
        if (cols[i]?.trim()) {
          specialWeapon.ingredientsAmount[i - 7] = +cols[i]
          .split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.')
        } else {
          specialWeapon.ingredientsAmount[i - 7] = undefined;
        }
      }

      await  this._specialWeaponService.svcToModify(specialWeapon);
      await  this._specialWeaponService.toSave();
      Alert.ShowSuccess('Special Weapons imported successfully!');
      this.spinnerService.setState(false)
      this.description = `Showing ${this.specialWeapons.length} results`;
      

    }
  }

  DisplayErrors(array)
  {
    let count = array[0].split(/\t/)
     if(Number.isNaN(+count[0]))
    {
      Alert.showError("Copy the BLUEPRINT LEVEL column values too!")
      this.spinnerService.setState(false)
      return true
    }
    
    if(count[0] === "")
    {
      Alert.showError("You are probably copying a blank column!")
      this.spinnerService.setState(false)
      return true
    }
    if(array[1].split(/\t/)[0] === "0")
    {
      Alert.showError("The BLUEPRINT LEVEL column has less lines then the rest of lines you are copying from the table!")
      this.spinnerService.setState(false)
      return true
    }

    return false
  }


  simplifyString(str: string): string {
    return (str
      ?.normalize('NFD')
      ?.replace(/[\u0300-\u036f]/g, '')
      ?.toLocaleUpperCase())
      ?.trim()
  }

  sortArchiveLevelOrder = -1;
  sortArchiveLevel() {
    this.sortArchiveLevelOrder *= -1;
    this.specialWeapons.sort((a, b) => {
      if (!a.bpArchiveLevel && b.bpArchiveLevel) return 1;
      if (a.bpArchiveLevel && !b.bpArchiveLevel) return -1;
      if (!a.bpArchiveLevel && !b.bpArchiveLevel) return 0;
      return this.sortArchiveLevelOrder * (a.bpArchiveLevel - b.bpArchiveLevel);
    });
  }

  sortSoulsCostOrder = -1;
  sortListBySoulsCost() {
    this.sortSoulsCostOrder *= -1;
    this.specialWeapons.sort((a, b) => {
      if (!a.souls && b.souls) return 1;
      if (a.souls && !b.souls) return -1;
      if (!a.souls && !b.souls) return 0;
      return this.sortSoulsCostOrder * (a.souls - b.souls);
    });
  }

  sortTimeToCreateOrder = -1;
  sortListByTimeToCreate() {
    this.sortTimeToCreateOrder *= -1;
    this.specialWeapons.sort((a, b) => {
      if (!a.time && b.time) return 1;
      if (a.time && !b.time) return -1;
      if (!a.time && !b.time) return 0;
      return this.sortTimeToCreateOrder * (a.time - b.time);
    });
  }

  sortByRubiesOrder = -1;
  sortByRubies() {
    this.sortByRubiesOrder *= -1;
    this.specialWeapons.sort((a, b) => {
      if (!a.rubies && b.rubies) return 1;
      if (a.rubies && !b.rubies) return -1;
      if (!a.rubies && !b.rubies) return 0;
      return this.sortByRubiesOrder * (a.rubies - b.rubies);
    });
  }
  sortbpIdOrder = -1;
  sortBPId() {
    this.sortbpIdOrder *= -1;
    this.specialWeapons.sort((a, b) => {
      if (!a.bpId && b.bpId) return 1;
      if (a.bpId && !b.bpId) return -1;
      if (!a.bpId && !b.bpId) return 0;
      let nameA = this._itemService.svcFindById(a.bpId).name;
      let nameB = this._itemService.svcFindById(b.bpId).name;
      return this.sortbpIdOrder * nameA.localeCompare(nameB);
    });
  }

  sortUniqueIngredientOrder = -1;
  sortUniqueIngredient() {
    this.sortUniqueIngredientOrder *= -1;
    this.specialWeapons.sort((a, b) => {
      if (!a.ingredientId && b.ingredientId) return 1;
      if (a.ingredientId && !b.ingredientId) return -1;
      if (!a.ingredientId && !b.ingredientId) return 0;
      let nameA = this._itemService.svcFindById(a.ingredientId).name;
      let nameB = this._itemService.svcFindById(b.ingredientId).name;
      return this.sortUniqueIngredientOrder * nameA.localeCompare(nameB);
    });
  }

  sortItemIdOrder = -1;
  sortItemId() {
    this.sortItemIdOrder *= -1;
    this.specialWeapons.sort((a, b) => {
      if (!a.itemId && b.itemId) return 1;
      if (a.itemId && !b.itemId) return -1;
      if (!a.itemId && !b.itemId) return 0;
      let nameA = this._itemService.svcFindById(a.itemId).name;
      let nameB = this._itemService.svcFindById(b.itemId).name;
      return this.sortItemIdOrder * nameA.localeCompare(nameB);
    });
  }

  sortComonIngredientsOrder = -1;
  sortComonIngredients(index: number) {
    this.sortComonIngredientsOrder *= -1;
    this.specialWeapons.sort((a, b) => {
      if (!a.ingredientsAmount[index] && b.ingredientsAmount[index]) return 1;
      if (a.ingredientsAmount[index] && !b.ingredientsAmount[index]) return -1;
      if (!a.ingredientsAmount[index] && !b.ingredientsAmount[index]) return 0;
      return (
        this.sortComonIngredientsOrder *
        (a.ingredientsAmount[index] - b.ingredientsAmount[index])
      );
    });
  }
}
