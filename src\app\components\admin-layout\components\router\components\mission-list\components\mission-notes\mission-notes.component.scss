// Custom search component styles
.custom-search-container {
  display: flex;
  align-items: center;
 // padding: 15px;
  border-radius: 5px;
  margin-bottom: 10px;

  .searchControl {
    width: calc(100% - 200px);
    display: inline-block;

    input {
      display: inline-block;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 9px 12px;
    }
  }

  .searchOptions {
    width: 200px;
    display: inline-block;
    vertical-align: bottom;
    padding-left: 20px;

    input {
      margin: 0px;
      padding: 0px;
      margin-top: 0px;
      vertical-align: middle;
    }

    label {
      margin: 0px;
      padding: 0px;
      padding-left: 8px;
      font-size: 12px;
      cursor: pointer;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .custom-search-container {
    flex-direction: column;
    align-items: stretch;

    .searchControl {
      width: 100%;
      margin-bottom: 10px;
    }

    .searchOptions {
      width: 100%;
      padding-left: 0;
    }
  }
}