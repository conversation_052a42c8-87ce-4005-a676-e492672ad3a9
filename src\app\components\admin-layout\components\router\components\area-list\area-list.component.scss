.item-overlay
{
    position: absolute;
    //border: 15px solid black;
    height: 100%;
    width: 100%;
    background-color: rgb(230, 230, 230);
    z-index: 150;
}

.popup-report
{
    position: fixed;
    height: 45em;
    width: 70%;
    background-color: rgb(255, 255, 255);
    z-index: 9999;
    
    margin: auto;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin-left: auto;
    margin-right: auto;
    margin-top: auto;
    margin-bottom: auto;
    text-align: center;
}

.height-cap
{
    max-height: 1000px;
}

.submit-button
{
    text-align: center;
    margin-left: auto;
    margin-right: auto;
    width: 60%;
    margin-top: 250px;
}

.scrollable-div
{
    overflow-y: scroll;
    height: 280px;
}

table.popup-area
{
  margin: 0px;
  width: 100%;
  border-radius: 5px;

  th {
    margin-left: 50% !important;	
    text-align: center;
    font-weight: bold;
    background-color: #dbdbdb;
  }
  th, td{
    width: 20%;
    height: 40px;
    border: 2px darkgray solid;
    padding: 4px;
    font-size: 18px;
  }

  tr:nth-child(even) {
    background-color: #f1f1f1;
  }

  .empty
  {
    background-color: gray;
  }
}

.background-div {	
  position: relative;	
}	

.background-div.popup-open:before 	
{	
  content: "";	
  position: fixed;	
  top: 0;	
  left: 0;	
  width: 100%;	
  height: 100%;	
  background-color: rgba(0, 0, 0, 0.5);	
  z-index: 9998;	
  pointer-events: none;	
}	
/* Fundo escurecido (backdrop) */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5); /* Ajuste a opacidade aqui */
  z-index: 998; /* Deve ser menor que o modal */
}

.component-modal {
  background-color: #fff;
  border-radius: 5px;
  border: 2px solid #555;
  padding: 5px;
}

.header-modal {
  display: flex; 
  justify-content: space-around; 
  align-items: center; 
  border: 2px solid darkgray; 
  border-radius: 3px; 
  border-bottom: none; 
  padding-right: 40px;
}

.m-close {
  width: 60px;
  position: absolute;
  right: 10px;
  top: 10px;
}

.ball-circle {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #2196F3;
  border: 2px solid #FFFFFF;
  float: right;
}