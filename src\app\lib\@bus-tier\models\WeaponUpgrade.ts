import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';
import { Tag } from './Tag';

export class WeaponUpgrade
  extends Base<Data.Hard.IWeaponUpgrade, Data.Result.IWeaponUpgrade>
  implements Required<Data.Hard.IWeaponUpgrade>
{
  protected static generateId(index: number): string {
    return IdPrefixes.WEAPON_UPGRADE + index;
  }

  constructor(
      index: number,
      itemId: string,
      level: number,
      dataAccess: WeaponUpgrade['TDataAccess']
  )
  {
      super({hard: {id: WeaponUpgrade.generateId(index), itemId, level}}, dataAccess);
  }

  public get itemId(): string
  {
    return this.hard.itemId;
  }
  public set itemId(value: string)
  {
    this.hard.itemId = value;
  }

  public get level(): number
  {
    return this.hard.level;
  }
  public set level(value: number)
  {
    this.hard.level = value;
  }

  public get rarityName(): string
  {
    return this.hard.rarityName;
  }
  public set rarityName(value: string)
  {
    this.hard.rarityName = value;
  }

  public get atkw(): number
  {
    return this.hard.atkw;
  }
  public set atkw(value: number)
  {
    this.hard.atkw = value;
  }

  public get shots(): number
  {
    return this.hard.shots;
  }
  public set shots(value: number)
  {
    this.hard.shots = value;
  }

  public get cooldown(): number
  {
    return this.hard.cooldown;
  }
  public set cooldown(value: number)
  {
    this.hard.cooldown = value;
  }

  public get gold(): number
  {
    return this.hard.gold;
  }
  public set gold(value: number)
  {
    this.hard.gold = value;
  }

  public get goldUp(): number
  {
    return this.hard.goldUp;
  }
  public set goldUp(value: number)
  {
    this.hard.goldUp = value;
  }

  public get ichor(): number
  {
    return this.hard.ichor;
  }
  public set ichor(value: number)
  {
    this.hard.ichor = value;
  }

  public get souls(): number
  {
    return this.hard.souls;
  }
  public set souls(value: number)
  {
    this.hard.souls = value;
  }  
  public get spdBoost(): number
  {
    return this.hard.spdBoost;
  }
  public set spdBoost(value: number)
  {
    this.hard.spdBoost = value;
  }

  public get time(): number
  {
    return this.hard.time;
  }
  public set time(value: number)
  {
    this.hard.time = value;
  }

  public get rubies(): number
  {
    return this.hard.rubies;
  }
  public set rubies(value: number)
  {
    this.hard.rubies = value;
  }

  public get titanium(): number
  {
    return this.hard.titanium;
  }
  public set titanium(value: number)
  {
    this.hard.titanium = value;
  }

  public get adamantium(): number
  {
    return this.hard.adamantium;
  }
  public set adamantium(value: number)
  {
    this.hard.adamantium = value;
  }

  public get hellnium(): number
  {
    return this.hard.hellnium;
  }
  public set hellnium(value: number)
  {
    this.hard.hellnium = value;
  }

}
