<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons [cardTitle]="'Titanium Mining'" [cardDescription]="description"
          [rightButtonTemplates]="[excelButtonTemplate]">
        </app-header-with-buttons>
        <app-header-search (inputKeyup)="lstOnChangeFilter($event)"
          (searchOptions)="lstOnChangeFilterOptions($event)"></app-header-search>
      </div>
    </div>
    <!--List-->
    <div class="card">
      <table class="table table-list">
        <thead class="sticky">
          <tr>
            <th rowspan="3" class="th-clickable" (click)="sortListByParameter('titaniumLevel')">
              Titanium Mining - Level
            </th>
            <th class="th-clickable" (click)="sortListByParameter('souls')">
              Cost
            </th>
            <th class="th-clickable" (click)="sortListByParameter('time')">
              Building Time
            </th>
            <th class="th-clickable" (click)="sortListByParameter('rubies')">
              Skip Cost
            </th>
            <th rowspan="3" class="th-clickable" (click)="sortListByParameter('storage')">
              Local Storage Capacity
            </th>
            <th rowspan="3" class="th-clickable" (click)="sortListByParameter('production')">
              Production per hour
            </th>
          </tr>
          <tr>
            <th class="th-clickable common" (click)="sortListByParameter('souls')">
              SOULS
            </th>
            <th class="th-clickable rare" (click)="sortListByParameter('time')">
              MINUTES
            </th>
            <th class="th-clickable rubies-color" (click)="sortListByParameter('rubies')">
              RUBIES
            </th>
          </tr>
          <tr>
            <th class="th-clickable light-gray" (click)="sortListByParameter('souls')">
              Required to get to this level
            </th>
            <th class="th-clickable light-gray" (click)="sortListByParameter('time')">
              Time to upgrade to this level
            </th>
            <th class="th-clickable light-gray" (click)="sortListByParameter('rubies')">
              Gem to skip the wait (build instantly)
            </th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="
                let titaniuMining of lstIds | titaniumMinings;
                let i = index;
                trackBy: trackById
              ">
            <tr *ngIf="titaniuMining.titaniumLevel !== null" id="{{ titaniuMining.id }}">
              <td>{{ titaniuMining.titaniumLevel }}</td>
              <!-- <td class="td-id">
                <input
                  style="width: 40px;"
                  type="number"
                  #InputlabLevel
                  [value]="laboratory.labLevel"
                  (change)="lstOnChange(laboratory, 'labLevel', InputlabLevel.value)" />
              </td> -->
              <td class="td-id">
                <input class="background-input-table-color" placeholder=" " type="number" #Inputsouls
                  [value]="titaniuMining.souls" (change)="lstOnChange(titaniuMining, 'souls', Inputsouls.value)" />
              </td>
              <td class="td-id">
                <input class="background-input-table-color" placeholder=" " type="number" #Inputtime
                  [value]="titaniuMining.time" (change)="lstOnChange(titaniuMining, 'time', Inputtime.value)" />
              </td>
              <td class="td-id">
                <input class="background-input-table-color" placeholder=" " type="number" #Inputrubies
                  [value]="titaniuMining.rubies" (change)="lstOnChange(titaniuMining, 'rubies', Inputrubies.value)" />
              </td>
              <td class="td-id">
                <input class="background-input-table-color" placeholder=" " type="number" #Inputstorage
                  [value]="titaniuMining.storage"
                  (change)="lstOnChange(titaniuMining, 'storage', Inputstorage.value)" />
              </td>
              <td class="td-id">
                <input class="background-input-table-color" placeholder=" " type="number" #Inputproduction
                  [value]="titaniuMining.production"
                  (change)="lstOnChange(titaniuMining, 'production', Inputproduction.value)" />
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>