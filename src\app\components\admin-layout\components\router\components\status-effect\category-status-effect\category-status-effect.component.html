<ng-container *ngIf="listCategory.length > 0">

    <div style="display: flex; flex-direction: row; overflow-x: auto; width: 100%; justify-content: center;">
        <table class="table table-list borderList">
            <thead>
                <tr>
                    <th colspan="10">
                        <h3>Category</h3>
                    </th>
                </tr>
                <tr>
                    <th>Index</th>
                    <th>Category</th>
                    <th>Typology</th>
                    <th style="width: 25%;">RELATION (NEED TO BE A CHOICE BETWEEN <b>Resist x Weak</b>)</th>
                    <th>Description</th>
                    <th style="width: 80px;">Actions</th>
                </tr>
            </thead>
            <tbody>
                <ng-container *ngFor="let item of listCategory; let i = index">
                    <tr id="{{ item.id }}">
                        <td style="background-color: #ddd;">{{ i+ 1 }}</td>
                        <td class="td-id">
                            <input class="background-input-table-color form-control form-short " type="text"
                                [(ngModel)]="item.category"
                                (change)="changeStatusEffectValue(i, item.category, 'category')" />
                        </td>
                        <td class="td-id">
                            <input class="background-input-table-color form-control form-short " type="text"
                                [(ngModel)]="item.typology"
                                (change)="changeStatusEffectValue(i, item.typology, 'typology')" />
                        </td>
                        <td class="td-id">
                            <select class="dropdown filter-dropdown limited configSelect" #InputRelation
                            (change)="filterRelation(i, InputRelation.value)"  [(ngModel)]="item.relation">                     
                            <option *ngFor="let relation of listElementAffinities" value="{{ relation.name }}">{{ relation.name }}</option>
                          </select>
                        </td>
                        <td class="td-id">
                            <input class="background-input-table-color form-control form-short " type="text"
                                [(ngModel)]="item.description"
                                (change)="changeStatusEffectValue(i, item.description, 'description')" />
                        </td>
                        <td class="td-actions">
                            <button class="btn btn-danger btn-fill btn-remove" (click)="removeLineCategory(i)">
                                <i class="pe-7s-close"></i>
                            </button>
                            <button class="btn btn-gray btn-fill translation-button"
                                (click)="getModifierOrtography(item)">
                                <div class="mat-translate"></div>
                            </button>
                        </td>
                    </tr>
                </ng-container>
            </tbody>
        </table>
    </div>
</ng-container>

<ng-container *ngIf="listCategory.length === 0">
    <div class="card" style="text-align: center; padding: 20px;">
        <h3>Empty list. Click to create the list.</h3>
    </div>
</ng-container>