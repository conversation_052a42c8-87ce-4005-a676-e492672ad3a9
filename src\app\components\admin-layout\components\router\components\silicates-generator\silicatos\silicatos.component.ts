import { Component } from '@angular/core';
import { Item } from 'src/app/lib/@bus-tier/models';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { TranslationService } from 'src/app/services/translation.service';
import { LanguageService } from 'src/app/services/language.service';
import { ItemService, ParticleService, SilicatosService, StatusService } from 'src/app/services';
import { Button } from 'src/app/lib/@pres-tier/data';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/SortableListComponent';
import { Silicatos } from 'src/app/lib/@bus-tier/models/Silicatos';
import { ItemClassService } from 'src/app/services/item-class.service';
import { Particle } from 'src/app/lib/@bus-tier/models/Particle';
import { Alert } from 'src/lib/darkcloud';
import { CustomService } from 'src/app/services/custom.service';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';
import { SpinnerService } from './../../../../../../../spinner/spinner.service';
import { ActivatedRoute } from '@angular/router';


@Component({
  selector: 'app-silicatos',
  templateUrl: './silicatos.component.html',
  styleUrls: ['./silicatos.component.scss']
})

export class SilicatosComponent extends SortableListComponent<Silicatos> 
{   
  description = '';
  subParticles: Item[] = [];
  status: string[] = [];
  particles: Particle[] = [];
  itemSilicatosById : Item[] = [];
  silicatos : Silicatos[] = [];
  silicatosToSearch : Silicatos[] = [];
  silicatosIds : string[] = [];
  subParticlesIds : string[] = [];
  selectedClasses = [];
  custom: Custom;
  ids = [];
  originalArray;
  sortSoulsCostOrder = -1;
  sortSilicatoNameOrder = -1;

  constructor(
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    private _silicatosService: SilicatosService,
    protected _translationService: TranslationService,
    protected _languageService: LanguageService,
    private _itemClassService: ItemClassService,
    protected _itemService: ItemService,
    private _statusService: StatusService,
    private _particleService: ParticleService,
    protected _customService: CustomService,
    private spinnerService: SpinnerService
    ) 
    {
      super(_silicatosService, _activatedRoute, _userSettingsService, 'name');
    }

  public readonly excelButtonTemplate: Button.Templateable = 
  {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  override async ngOnInit(): Promise<void> 
  {
    this.originalArray = this._itemClassService.svcFindById('IC0')?.itemIds?.slice()
    await this._particleService.toFinishLoading();
    await this._itemClassService.toFinishLoading();
    await this._statusService.toFinishLoading();
    await this._silicatosService.toFinishLoading();
    this.custom = await this._customService.svcGetInstance();

    if(!this.custom.particlesOrder)
    {
      this.custom.particlesOrder = [];
    }
    if(this.custom.particlesOrder.length != this._particleService.models.length)
    {
      this.custom.particlesOrder = [];
      this._particleService.models.forEach(particle => {
        this.custom.particlesOrder.push(particle.id);
      });
    }

    this.requireSubparticles()
    this.requireStatusAcronymProperty()
    this.requireSilicatosIdsFromItemList()
    this.createSilicatos()
    this.selectSilicatesClasses()
    this.description = `Showing ${this.silicatosToSearch.length} results`;
  }


  requireSubparticles()
  {    
    this.subParticles = [];
    //item class pkg has a sub atomics particles array by id = IC0
    this._itemClassService.svcFindById('IC0')?.itemIds?.forEach((itemId) => 
    {
        this.subParticles.push(this._itemService.svcFindById(itemId));
    });
  }
  
  async changeElementsPosition( choosedElement, index )
  {
    //get the item that need to go to the new position.
    let newElement
    this._itemService.models.forEach(item=> 
    {
      if(item.name == choosedElement) return newElement = item;        
      return null
    })

    let i = this.originalArray.indexOf(newElement.id);

    let temp = this.originalArray[i];
    this.originalArray[i] = this.originalArray[index];
    this.originalArray[index] = temp;

    this._itemClassService.svcFindById('IC0').itemIds = [];
    await this._itemClassService.toSave();
    this._itemClassService.svcFindById('IC0').itemIds = this.originalArray;
    await this._itemClassService.toSave();    
  }
 
  override lstOnChangeFilter(name:string)
  {
    if(name != '') this.silicatosToSearch = this.silicatos.filter(silicato => silicato.silicatoName.includes(name));
    if(name === '') this.silicatosToSearch = this.silicatos;
  }
    
    async requireStatusAcronymProperty()
    {
      this._statusService.models.forEach(s=> this.status.push(s.acronym)) 
    }

    requireSilicatosIdsFromItemList()
    {
      this.itemSilicatosById = [];
      //item class pkg has a silciatos array by id = IC5
      this._itemClassService.svcFindById("IC5")?.itemIds?.forEach((itemId) => 
      {
        this.itemSilicatosById.push(this._itemService.svcFindById(itemId));
      });
    }

    async createSilicatos()
    {     
      if(this._silicatosService.models.length !== 0)
      {
        this.silicatos = this._silicatosService.models;
        this.silicatosToSearch = this.silicatos;
        //With more time make it work with the silicato pipe to reduce the number of methods to change silicato object values
        this.silicatos.forEach(silicato => this.silicatosIds.push(silicato.id));
      }else
      {
        this.itemSilicatosById.forEach(s => 
        {
          let silicato = this._silicatosService.createNewSilicato(s.name);
          silicato.soulsCost = null;
          silicato.timeToCreate = null;
          silicato.rubiesToSkip = null;
          silicato.subParticles = [];
          silicato.status = [];
          this._silicatosService.toSave();
          this.silicatos.push(silicato);
        })
        this.silicatos.forEach(silicato => this.silicatosIds.push(silicato.id));
        this.silicatosToSearch = this.silicatos;
      }

    }

    async selectSilicatesClasses() 
    {
      this.selectedClasses = this._itemClassService.models.filter((klass) => 
      {
        return this.custom?.silicatesClassItem?.includes(klass.id);
      });
      let itemIds = [];    
      
      this.selectedClasses.forEach((itemClass) => 
      {
        itemClass?.itemIds?.forEach((itemId) => 
        {
          itemIds.push(this._itemService.svcFindById(itemId));
        });
      });
      
      this.ids = [];
      itemIds?.forEach((id) => 
      {
        this.ids.push(id?.name)
      });
  
     this.silicatosToSearch = [];
  
      this._silicatosService.models.forEach((sw) =>
      {
        if(this.ids.includes(sw.silicatoName))
        {
          this.silicatosToSearch.push(sw);
          this.ids = this.ids.filter(id => id !== sw.silicatoName);
        }     
      });
         
      this.ids.forEach(id => 
      {   
        let silicato = this._silicatosService.createNewSilicato(id);
        silicato.soulsCost = null;
        silicato.timeToCreate = null;
        silicato.rubiesToSkip = null;
        silicato.subParticles = [];
        silicato.status = [];
        this.silicatosToSearch.push(silicato);
      })
  
      await this._silicatosService.toSave();
      this.silicatosToSearch.concat(this._silicatosService.models);
    }

    async onExcelPaste(): Promise<void> 
    {
        this.spinnerService.setState(true);
        const text = await navigator.clipboard.readText();
        const lines = text.split(/\r?\n/).filter(line => line);
        if(this.DisplayErrors(lines, this.status.length + 5 + this.subParticles.length)) return;

        for (let l = 0; l < lines.length; l++) 
        {
          let line = lines[l];
          let cols = line.split(/\t/);
          let item = this._silicatosService.models.find((i) =>
            this.simplifyString(i.silicatoName) == this.simplifyString(cols[1]));
          if (!item) continue;
          let silicato = this.silicatos.find((i) => i.silicatoName == item.silicatoName);

          if (!silicato) continue;
    
          if (cols[0]?.trim()) {
            silicato.baseLevelUnlock = +cols[0]
            .split(' ')
            .join('')
            .split('.')
            .join('')
            .replace(',','.')
          } 
          else 
          {
            silicato.baseLevelUnlock = undefined;
          }
       
         let statusLength = this.status.length;

           for (let i = 2; i < statusLength+2; i++) 
           {
              if (cols[i]?.trim()) 
              {
                silicato.status[i-2] = +cols[i]
                .split(' ')
                .join('')
                .split('.')
                .join('')
                .replace(',','.')
              } 
              else 
              {
                  silicato.status[i-2] = undefined;
              }
            } 
         
          if (cols[statusLength+2]?.trim()) 
          {
            silicato.soulsCost = +cols[statusLength+2]
            .split(' ')
            .join('')
            .split('.')
            .join('')
            .replace(',','.')
          } 
          else 
          {
            silicato.soulsCost = undefined;
          }

          if (cols[statusLength+3]?.trim()) 
          {
            silicato.timeToCreate = +cols[statusLength+3]
            .split(' ')
            .join('')
            .split('.')
            .join('')
            .replace(',','.')
          } 
          else 
          {
            silicato.timeToCreate = undefined;
          }

          if (cols[statusLength+4]?.trim()) 
          {
            silicato.rubiesToSkip = +cols[statusLength+4]
            .split(' ')
            .join('')
            .split('.')
            .join('')
            .replace(',','.')
          } 
          else 
          {
            silicato.rubiesToSkip = undefined;
          }

          let subatomicParticlesLength = this.subParticles.length + statusLength + 5
          for (let i = statusLength+5; i < subatomicParticlesLength; i++) 
          {
            if (cols[i]?.trim()) 
            {
              silicato.subParticles[i] = +cols[i]
              .split(' ')
              .join('')
              .split('.')
              .join('')
              .replace(',','.')
            } 
            else 
            {
                silicato.subParticles[i] = undefined;
            }
          }
    
          await this._silicatosService.svcToModify(silicato);
          await this._silicatosService.toSave();
        }
          this.selectSilicatesClasses();
          Alert.ShowSuccess('Silicatos imported successfully!');
          this.spinnerService.setState(false);
          this.description = `Showing ${this._silicatosService.models.length} results`;
      }

      override simplifyString(str: string): string 
      {
        return (str?.normalize('NFD')?.replace(/[\u0300-\u036f]/g, '')?.toLocaleUpperCase())?.trim();
      }

      DisplayErrors(array, length:number) 
      {
        let count = array[0].split(/\t/);
        if (count.length < length) 
        {
          Alert.showError('Copy ALL '+ length +' columns! Including the LAB LEVEL UNLOCK Column!');
          this.spinnerService.setState(false);
          return true;
        }
        if (count[0] === '') 
        {
          Alert.showError('You are probably copying a blank column!');
          this.spinnerService.setState(false)
          return true;
        }
        return false;
      }
    

     async changesStatus(silicato: Silicatos, index:number, value: string) 
     {
        silicato.status[index] = value ? +value : undefined;
        await this._silicatosService.toSave();
      }

      changesSubparticles(silicato: Silicatos, index:number, value: string) 
      {
        silicato.subParticles[index] = value ? +value : undefined;
        this._silicatosService.svcToModify(silicato);
        this._silicatosService.toSave();
      }

      sortListBySilicatoName()
      {
        this.sortSilicatoNameOrder *= -1;
        this.silicatosToSearch.sort((a, b) => 
        {
          return this.sortSilicatoNameOrder * this.simplifyString(a.silicatoName).localeCompare(this.simplifyString(b.silicatoName));
        });
      }
    
      sortListBySoulsCost()
      {
        this.sortSoulsCostOrder *= -1;
        this.silicatosToSearch.sort((a, b) => 
        {
          if(!a.soulsCost && b.soulsCost) return 1;
          if(a.soulsCost && !b.soulsCost) return -1;
          if(!a.soulsCost && !b.soulsCost) return 0;
          return this.sortSoulsCostOrder * (a.soulsCost-b.soulsCost);
        });
      }    
}