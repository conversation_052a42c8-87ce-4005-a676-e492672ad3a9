<div class="m-container">
  <table class="table-bordered">
    <thead>
      <tr>
        <th class="trBC" rowspan="3">INDEX</th>
        <th class="trBC" colspan="3"  rowspan="2">BONUS</th>
        <th class="trBC" colspan="4">Passive Evolution Increases as Rarity of Weapon Scales</th>
      </tr>
      <tr>
        <th colspan="4">Value</th>
      </tr>
      <tr>
        <th>ID</th>
        <th>Description</th>
        <th>Type</th>      
        <th *ngFor="let tier of tierList">{{ tier }}</th>
      </tr>
    </thead>
    <tbody>
      <ng-container *ngIf="listBonus.length > 0">
        <tr *ngFor="let bonus of listBonus; let i = index">
          <td>{{ i + 1 }}</td>
          <td class="bc">{{ bonus?.idValue }}</td>
          <td class="bc" style="text-align: left;">{{ bonus?.description }}</td>
          <td class="bc">{{ bonus?.type }}</td>
          <td *ngFor="let value of bonus.valuesRarity; let idx = index" class="bc"> {{value?.value}}</td>
        </tr>
      </ng-container>
      </tbody>
  </table>  
</div>

<ng-container *ngIf="listBonus.length === 0">
  <div  style="text-align: center;">
    <h4>Empty List</h4>
  </div>      
</ng-container>
