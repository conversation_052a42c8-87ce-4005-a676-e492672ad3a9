.main-menu-efect {
  padding-right: 15px;
  padding-left: 15px; 
  min-height: calc(100% - 210px);
}

.update {
    margin: -2px -2px 0;
    padding-bottom: 5px;
    background-color: #f7f7f6;
    margin-top: 15px;
}

.card-header-content {
display: block;
margin-left: 20px;
margin-right: 15px;
width: 30%;
}

.card {
  // padding-top: 8px !important;
   padding-top: 17px !important;
   padding-bottom: 10px;
 }

 .card-header-content {
   display: block;
   margin-left: 30px;
   margin-right: 15px;
   width: 30%;
 }

 //tabela
 .card-container 
{
  display: flex;
  flex-direction: column;
  align-items: center;

  .card 
  {
    border: 1px solid #ccc;
    padding: 15px;
    margin: 5px;
    width: 50vw;
    opacity: 1;
    transition: opacity 0.5s ease-in-out;
  } 
}