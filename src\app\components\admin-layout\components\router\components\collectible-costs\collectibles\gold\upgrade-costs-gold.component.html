<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons [cardTitle]="'Gold'" [cardDescription]="cardDescription"
          [rightButtonTemplates]="[excelButtonTemplate]">
        </app-header-with-buttons>
      </div>
    </div>
    <!--List-->
    <div class="card">

      <ng-container *ngIf="collectibles.length > 0 && tierList.length > 0">
        <table class="table-bordered">
          <thead class="sticky">
            <tr>
              <th rowspan="2" class="th-clickable" style="color:aliceblue; font-weight: 400;height: 100px;"
                (click)="sortListByParameter('bossLevel')">Boss Level (BL)
              </th>
              <th [attr.colspan]="tierList.length">
                TOTAL OURO / COLLECTIBLE RARITY
              </th>
            </tr>
            <td class="my-td" (click)="sortListByParameter(header)"
              [style.background-color]="(header | tierColor: 'Character Rarity')"
              style="color:aliceblue; font-weight: 500;border: 2px solid #545353;"
              *ngFor="let header of tierList; let i = index;">
              {{ header }}
            </td>
          </thead>
          <tbody>
            <ng-container *ngFor="let codeDrop of collectibles">
              <tr id="{{ codeDrop.id }}">
                <td class="td-id other-td">{{ codeDrop.bossLevel }}</td>
                <td *ngFor="let header of tierList; let i = index" class="other-td">
                  <input placeholder=" " style="border-style:solid; width: 95%;" type="number"
                    [value]="codeDrop.hard[header.toLowerCase() +'GoldAmount']" #inputField
                    [ngClass]="{'empty-input': !inputField.value}"
                    (change)="changeBlockDrops(codeDrop, inputField.value, header.toLowerCase() +'GoldAmount')" />
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </ng-container>

      <ng-container *ngIf="collectibles.length === 0 || tierList.length === 0">
        <div class="card padList" style="text-align: center;">
          <h4 *ngIf="collectibles.length === 0">Empty Code Blocks List.</h4>
          <h4 *ngIf="tierList.length === 0">List of Code Block Rarity is empty.</h4>
        </div>
      </ng-container>

    </div>
  </div>
</div>