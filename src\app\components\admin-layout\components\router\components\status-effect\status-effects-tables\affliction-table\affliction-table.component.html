<div class="div-container">
    <div class="width: 2221px;">
        <table class="table table-list borderList">
            <thead>    
                <tr>
                    <th [attr.colspan]="titles.length +1">
                        <h3>AFFLICTION</h3>
                    </th>
                    <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'" class="addButton"
                        [buttonTemplates]="[excelButtonTemplate]">
                    </app-button-group>
                </tr>
                <ng-container *ngIf="!isListAfflictionEmpty">
           
                    <tr>
                        <th class="default-color">Order</th>
                        <th
                          [ngClass]="(title === 'SKILL RESIST USER') ? 'skill-resist' : 'default-color'"
                          *ngFor="let title of titles">
                          {{title}}
                        </th>
                      </tr>
                      
                </ng-container>
            </thead>
            <ng-container *ngIf="!isListAfflictionEmpty">
                <tbody>
                    <tr *ngFor="let item of listAfflictionTable; let i = index">
                        <td style="background-color: #ddd; width: 2%;">{{ i + 1 }}</td>               
                            <td class="td-id aligTitle" style="width: 8%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #idAffliction [ngClass]="{'empty-input': !idAffliction.value}"
                                    [value]="item.idAffliction" (change)="changeDefensive(i,'idAffliction', idAffliction.value)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 8%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #idCategory [ngClass]="{'empty-input': !idCategory.value}"
                                    [value]="item.category" (change)="changeDefensive(i,'category', idCategory.value)" />
                            </td>    
                            <td class="td-id aligTitle" style="width: 8%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #status [ngClass]="{'empty-input': !status.value}"
                                    [value]="item.status" (change)="changeDefensive(i, 'status', status.value)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 8%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #skillResistUser [ngClass]="{'empty-input': !skillResistUser.value}"
                                    [value]="item.skillResistUser" (change)="changeDefensive(i, 'skillResistUser', skillResistUser.value)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 8%">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #operator [ngClass]="{'empty-input': !operator.value}"
                                    [value]="item.operator" (change)="changeDefensive(i, 'operator',operator.value)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 8%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #value [ngClass]="{'empty-input': !value.value}"
                                [value]="item.value" (change)="changeDefensive(i, 'value', value.value)" />
                            </td>
                            <td class="td-id" style="width: 60%; word-break: break-word;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #description [ngClass]="{'empty-input': !description.value}"
                                    [value]="item.description" (change)="changeDefensive(i, 'description', description.value)" />
                            </td>                   
                    </tr>
                </tbody>
            </ng-container>
            <ng-container *ngIf="isListAfflictionEmpty">
                <div class="card" style="text-align: center; padding: 20px;">
                    <h3>The list has not yet been imported.</h3>
                </div>
            </ng-container>
        </table>
</div>
    
</div>

