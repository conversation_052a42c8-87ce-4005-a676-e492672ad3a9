import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Effect, Passive } from 'src/app/lib/@bus-tier/models';
import { EffectService } from 'src/app/services';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/SortableListComponent';

@Component({
  selector: 'app-effect-generator',
  templateUrl: './effect-generator.component.html',
  styleUrls: ['./effect-generator.component.scss'],
})
/**
 * Displays and edits emotion data as a list
 */
export class EffectGeneratorComponent extends SortableListComponent<Effect> implements OnInit {

  listEffect = [];
  constructor(
    _activatedRoute: ActivatedRoute,
    protected _effectService: EffectService,
    _userSettingsService: UserSettingsService,
    protected _languageService: LanguageService,
    protected _translationService: TranslationService,
  ) {
    super(_effectService, _activatedRoute, _userSettingsService, 'name');
  }


  override async ngOnInit(): Promise<void> {

    this._effectService.toFinishLoading();
    this.listEffect = this._effectService.models;
  }
  public downloadSceneryOrtography(passive: Passive)
  {
    this._translationService.getPassiveOrtography(passive, true);
  }

  removeEffect(index: number) {
    const itemToRemove = this.listEffect[index];

    if (itemToRemove && itemToRemove.id) {
      this._effectService.svcToRemove(itemToRemove.id);
    }
    this.listEffect.splice(index, 1); // Remove o item da lista local
   this.ngOnInit(); 
  }
}
