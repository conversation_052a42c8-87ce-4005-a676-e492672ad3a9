<!--event sort order buttons-->
<td class="td-sort" id="{{ speech?.id }}" (click)="HighlightElement(speech?.id, 10, true)">
  <div [title]="speech.id">
    <button 
      class="btn btn-success btn-simple btn-invert btn-sm"
      (click)="toMove(speech, -1)">
      <i class="pe-7s-angle-up-circle"></i>
    </button>
    {{ index }}
    <button 
      class="btn btn-danger btn-simple btn-invert btn-sm"
      (click)="toMove(speech, 1)">
      <i class="pe-7s-angle-down-circle"></i>
    </button>
  </div>
</td>
<!-- introduction toggle -->
<td class="center" style="background-color: #8f8686;">
  <input 
    title="Introduction"
    class="toggle"
    type="checkbox"
    [(ngModel)]="speech.isIntroduction"
    (change)="onChange()"/>
</td>
<!-- speaker selection -->
<td class="same-width">
  <button 
    ngClass="btn btn-sm btn-white {{ speech.speakerId ? ' btn-fill' : 'btn-hidden' }}"
    (click)="toPromptSelectSpeaker(speech)">
    {{ (speech.speakerId | character)?.name || 'Undefined' }}
  </button>
</td>
<!-- message field -->
<td class="td-100" id="{{ speech?.id }}" >
<h6 style="color:#8c8b8b; font-weight: 100;">{{speech.id}}</h6>
  <div style="display: flex">
    <div class="ai-btn" style="width: 70%;">
      <button 
        ngClass="btn btn-sm {{ speech.emotionId ? ' btn-fill' : 'btn-hidden' }}"
        [ngStyle]="{'background-color': ((speech.emotionId | emotion) | information)?.hex, 
        'border-color': (speech.emotionId | emotion | information)?.hex}"
        (click)="toPromptSelectEmotion(speech)">
        {{ (speech.emotionId | emotion)?.name || 'Undefined'}}
      </button>
      <ng-container *ngIf="speech.speakerId">
        <ng-container *ngIf="speech.audioId === undefined; else audio">
          <button 
            class="btn btn-sm btn-hidden"
            (click)="toPromptSelectAudio(speech)">
            <i class="pe-7s-volume"></i>
          </button>
        </ng-container>
        <ng-template #audio>
          <app-audio-listener 
            (click)="toPromptSelectAudio(speech)"
            [klass]="'btn btn-sm btn-fill btn-info'"
            [hoverToPlay]="true"
            [audioId]="speech.audioId"
            [title]="(speech.audioId | audioFile)?.name || 'undefined'">
          </app-audio-listener>
        </ng-template>
      </ng-container>
    </div>
    <div class="ai-btn" style="justify-content: flex-end;">
      <button 
        class="btn btn-sm" title="Ghostwriter"
        (click)="clickOpenAIPrompt(speech, 'Ghostwriter')">
        <i class="ghost"></i>
      </button>
      <button 
        class="btn btn-sm" title="Grammar"
        (click)="clickOpenAIPrompt(speech, 'Grammar')">
        <i class="pe-7s-study"></i>
      </button>
      <button 
        class="btn btn-sm" title="Creative Writing"
        (click)="clickOpenAIPrompt(speech, 'Creative Writing')">
        <i class="pe-7s-light"></i>
      </button>
    </div>
  </div>
  <div>
    <app-rpg-content-input 
      #rpgInputBox
      [rpgSuggestionLink]="speech"
      [content]="speech.message"
      (contentChange)="onChangeText($event, 'message')"
      (change)="onChangeText($event, 'message')"
      [language]="language">
    </app-rpg-content-input>
  </div>
</td>
<td class="td-actions">
  <button 
    style="font-size: 2px; margin: 0; padding: 3px 5px"
    class="btn btn-fill btn-danger"
    (click)="toRemove(speech)">
    <i style="font-size: 24px; margin: 0; padding: 0"
       class="pe-7s-close"></i>
  </button>
</td>

<app-modal-ai-speeches *ngIf="selectType" [speech]="speech" [selectType]="selectType" (closeModalEvent)="onModalClose()"></app-modal-ai-speeches>