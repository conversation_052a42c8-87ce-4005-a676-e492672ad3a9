import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';
import { Tag } from './Tag';

export class PowerUp
  extends Base<Data.Hard.IPowerUp, Data.Result.IPowerUp>
  implements Required<Data.Hard.IPowerUp>
{
  protected static generateId(index: number): string {
    return IdPrefixes.POWER_UP + index;
  }

  constructor(
      index: number,
      itemId: string,
      dataAccess: PowerUp['TDataAccess']
  )
  {
      super({hard: {id: PowerUp.generateId(index), itemId}}, dataAccess);
  }

  public get itemId(): string
  {
    return this.hard.itemId;
  }
  public set itemId(value: string)
  {
    this.hard.itemId = value;
  }

  public get labLevel(): number
  {
    return this.hard.labLevel;
  }
  public set labLevel(value: number)
  {
    this.hard.labLevel = value;
  }

  public get souls(): number
  {
    return this.hard.souls;
  }
  public set souls(value: number)
  {
    this.hard.souls = value;
  }

  public get time(): number
  {
    return this.hard.time;
  }
  public set time(value: number)
  {
    this.hard.time = value;
  }

  public get rubies(): number
  {
    return this.hard.rubies;
  }
  public set rubies(value: number)
  {
    this.hard.rubies = value;
  }

  public get particlesValue(): number[]
  {
    return this.hard.particlesValue;
  }
  public set particlesValue(value: number[])
  {
    this.hard.particlesValue = value;
  }

}
