import { Component } from '@angular/core';
import { RelicUsesService, TierService } from 'src/app/services';
import { RelicUses } from '../../../../../../../lib/@bus-tier/models/RelicUses';

@Component({
  selector: 'app-relic-Uses',
  templateUrl: './relicUses.component.html',
  styleUrls: ['./relicUses.component.scss']
})
export class RelicUserComponent {

  weaponRarityNames = [];
  description: string;
  sortNameOrder = +1;
  sortNumberOrder = +1;
  listRelicUses: RelicUses[];

  constructor(
    private _tierListService: TierService,
    private _relicUsesService: RelicUsesService,
  ) {}


  ngOnInit() {
    this._tierListService.toFinishLoading();

    this._tierListService.models = this._tierListService.models.filter((tier) => tier.name !== undefined);
    this.weaponRarityNames = this._tierListService.models.filter((tier) => tier.selectDrop === 'Character Rarity');   
    
    this.createdInspirationPoints();
    this.getCompare();
   //this.lineupOrderCommon();
    this.description = `Showing ${this.listRelicUses.length} results`;
    console.log(this.listRelicUses);
  }

  createdInspirationPoints() {
    this.weaponRarityNames.forEach((x) => {
      this._relicUsesService.createNewrelicUses(x);
     });   
  }

  getCompare() {
    this._relicUsesService.toFinishLoading();
    const listRelic = this._relicUsesService.models;

    if (listRelic.length > 0) {
      this.weaponRarityNames.forEach((x) => {
        for (let index = 0; index < listRelic.length; index++) {
          if(x.id === listRelic[index].idRarity && x.name !== listRelic[index].nameRarity ){
            listRelic[index].nameRarity = x.name;
            this._relicUsesService.svcToModify(listRelic[index]);
          }         
        }        
      });
    }
    this.listRelicUses = this._relicUsesService.models;
  }
    
  async onChangeAvailableRarity(relic: RelicUses, value: string) {
    relic.numberRelicUse = value == '' ? undefined : +value;
    this._relicUsesService.svcToModify(relic);
    this._relicUsesService.toSave();
  }
  
  /**
   * Ordena a lista por nameRarity em ordem alfabética (A-Z / Z-A)
   */
  sortByNameRarity() {
    this.sortNameOrder *= -1;
    this.listRelicUses.sort((a, b) => {
      if (!a.nameRarity && !b.nameRarity) return 0;
      if (!a.nameRarity && b.nameRarity) return 1;
      if (a.nameRarity && !b.nameRarity) return -1;

      return this.sortNameOrder * a.nameRarity.localeCompare(b.nameRarity, 'pt-BR', {
        numeric: true,
        sensitivity: 'base'
      });
    });
  }

  /**
   * Ordena a lista por numberRelicUse em ordem numérica crescente/decrescente
   */
  sortByNumberRelicUse() {
    this.sortNumberOrder *= -1;
    this.listRelicUses.sort((a, b) => {
      const numA = a.numberRelicUse !== undefined ? +a.numberRelicUse : -1;
      const numB = b.numberRelicUse !== undefined ? +b.numberRelicUse : -1;

      // Valores undefined vão para o final
      if (numA === -1 && numB === -1) return 0;
      if (numA === -1 && numB !== -1) return 1;
      if (numA !== -1 && numB === -1) return -1;

      return this.sortNumberOrder * (numA - numB);
    });
  }

  lineupOrderCommon()
  {
  this.sortNameOrder *= +1;
    this.listRelicUses.sort((a, b) =>
    {
      return this.sortNameOrder * a.nameRarity.localeCompare(b.nameRarity);
    });
  }
}
