import { Injectable } from '@angular/core';
import { IndexStorageService, ReviewService } from 'src/app/services';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { ModelService } from 'src/lib/darkcloud/angular/dsadmin';
import { RPCEscapeTable } from '../lib/@bus-tier/models';

@Injectable({
  providedIn: 'root',
})
export class RPCEscapeTableService extends  ModelService<RPCEscapeTable> 
{

  public override svcPromptCreateNew(...args: any): Promise<RPCEscapeTable> {
    throw new Error('Method not implemented.');
  }

  constructor(
    indexStorageService: IndexStorageService,
    readonly userSettingsService: UserSettingsService,
    reviewService: ReviewService,
  ) 
  {
    super(
      {
        defaultConstructor: () =>
          new RPCEscapeTable(0, this.userSettingsService),
      },
      'RPCEscapeTable',
      indexStorageService,
      reviewService,
    );
  }
  
  public async createNewRPCEscapeTable() {  

    let escape = new RPCEscapeTable(this.svcNextIndex(), this.userSettingsService);   
    this.srvAdd(escape);
    return escape;    

  }
}
