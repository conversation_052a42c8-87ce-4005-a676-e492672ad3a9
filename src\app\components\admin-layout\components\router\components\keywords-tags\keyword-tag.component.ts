import { Component, OnDestroy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { KeywordsTags } from 'src/app/lib/@bus-tier/models';
import { But<PERSON> } from 'src/app/lib/@pres-tier/data';
import { KeywordService, KeywordsTagsService, UserSettingsService } from 'src/app/services';
import { LanguageService } from 'src/app/services/language.service';
import { Alert } from 'src/lib/darkcloud';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc';

@Component({
  selector: 'app-keyword-tag',
  templateUrl: './keyword-tag.component.html',
  styleUrls: ['./keyword-tag.component.scss']
})
export class KeywordTagComponent extends SortableListComponent<KeywordsTags> implements OnDestroy 
{
  timeout:any;
  public readonly addTagButton: Button.Templateable = 
  {
    title: 'Add a new tag to the list',
    onClick: this.createTag.bind(this),
    iconClass: 'pe-7s-plus',
    btnClass: Button.Klasses.FILL_GREEN,
  };

  constructor(
    _activatedRoute: ActivatedRoute,
    private _keywordsTagsService: KeywordsTagsService,
    _userSettingsService: UserSettingsService,
    private _router: Router,
    protected _languageService: LanguageService,
    private _keywordService: KeywordService,
  )
  {
    super(_keywordsTagsService, _activatedRoute, _userSettingsService, 'name');
  }

  override async lstInit()
  {
    if(!(this.lstIds?.length <= 0)) return;

    await this._keywordsTagsService.toFinishLoading();
    this.lstIds = this._keywordsTagsService.models.map(x => x.id);
  }

  public async createTag()
  {
    let newTag;
    try 
    {
      newTag = await this._keywordsTagsService.svcPromptCreateNew();
    } 
    catch (e) 
    {
      Alert.showError("Tag já existe!");
    }
    if(!newTag) return;

    this._keywordsTagsService.srvAdd(newTag);;
    this._keywordsTagsService.modelIds.push(newTag.id);

    this.refreshList();
  }

  redirectToItemClasses()
  {
    this._router.navigate(['others']);
  }

  updateColor(model, value, colorLabel: HTMLElement)
  {
    this.updateInformation(model, 'hex', value);
    colorLabel.style.backgroundColor = value;
  }

  refreshList(scroll?: boolean)
  {
    let listProxy = this.lstIds;
    this.lstIds = [];
   this.timeout = setTimeout(() => 
   {
      this.lstIds = listProxy;
      this._keywordsTagsService.svcReviewAll();
    }, 1);
  }

  public override async lstPromptRemove(listItem: any) 
  {
    // removes from the database
    const removed = await this._modelService.toPromptRemove(listItem);
    if (!removed) return;
    
    this.lstResetHighlights();
    this.lstRemove(listItem);
    this.removeTagFromKeyword(listItem);
  }

  removeTagFromKeyword(listItem)
  {
    this._keywordService.models = this._keywordService.models.filter(async keyword => 
    {
      keyword.keywordTags = keyword.keywordTags?.filter(tag => tag != listItem.id);

      await this._keywordService.svcToModify(keyword);
      await this._keywordService.toSave();

    })
  }

  ngOnDestroy() 
  {
    clearInterval(this.timeout);   
  }
}
