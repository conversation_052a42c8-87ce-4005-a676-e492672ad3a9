import { Component, OnInit } from '@angular/core';
import { AreaService, EventService, ItemService, ReviewService, RpgService } from 'src/app/services';
import { EasyMVC } from 'src/lib/darkcloud/angular';
import { Event } from 'src/app/lib/@bus-tier/models';
import { RPGType } from 'src/lib/darkcloud/rpg-formatting';
import { Accessment, Sortment } from 'src/app/lib/@pres-tier';
import { EventType } from 'src/lib/darkcloud/dialogue-system';
import { RoadBlockService } from 'src/app/services/road-block.service';
import { SortingService } from 'src/app/services/sorting.service';
import { RPGFormatting } from 'src/lib/darkcloud';
import { ActivatedRoute, Router } from '@angular/router';

export interface Preloadable 
{
  currencyGiveEvents: Event[];
  currencyTradeEvents: Event[];
  currencyReceiveEvents: Event[];
}

@Component({
  selector: 'app-currency-report',
  templateUrl: './currency-report.component.html',
})
export class CurrencyReportComponent extends EasyMVC.PreloadComponent<Preloadable> implements OnInit 
{
  Sortment = Sortment;
  RPGType = RPGFormatting.RPGType;
  sorting: Sorting = new Sorting(this);
  accessing: Accessing = new Accessing(this);
  sortedById = false;
  reverseOrder: boolean = false;
  detectedCurrencyWords = [];
  public receiveEvents: Event[] = [];
  public receiveEventsRoadblockIds: string[] = [];

  public tradeEvents: Event[] = [];
  public tradeEventsRoadblockIds: string[] = [];

  public giveEvents: Event[] = [];
  public giveEventsRoadblockIds: string[] = [];
  public sortedEvents: Event[] = [];

  constructor(
    private _activatedRoute: ActivatedRoute,
    public readonly router: Router,
    public readonly areaService: AreaService,
    public readonly itemService: ItemService,
    private _eventService: EventService,
    private _roadblockService: RoadBlockService,
    private _reviewService: ReviewService,
    private _sortingService: SortingService,
    private _rpgService: RpgService
  ) 
  {
    super(_activatedRoute, (data) => data.preloadedCurrencyReportData);
  }

  

  async ngOnInit() 
  {
    await this._eventService.toFinishLoading();
    this.detectedCurrencyWords = await this.getWordsMarkedAsCurrency();

    ///////////////////////// Get Receive Events /////////////////////////////
    const receiveArrays = await this.getEvents(this.receiveEvents, 'RECEIVE_ITEM', this.receiveEventsRoadblockIds);
    this.receiveEvents = receiveArrays.eventsList;
    this.receiveEventsRoadblockIds = receiveArrays.roadblockList;

    ///////////////////////// Get Trade Events //////////////////////////
    const tradeArrays = await this.getEvents(this.tradeEvents, 'TRADE_ITEM', this.tradeEventsRoadblockIds);
    this.tradeEvents = tradeArrays.eventsList;
    this.tradeEventsRoadblockIds = tradeArrays.roadblockList;

    //////////////////////////// Get Give Events ///////////////////////////////////
    const giveArrays = await this.getEvents(this.giveEvents, 'GIVE_ITEM', this.giveEventsRoadblockIds);
    this.giveEvents = giveArrays.eventsList;
    this.giveEventsRoadblockIds = giveArrays.roadblockList;
  }

  async getWordsMarkedAsCurrency()
  {
    await this._rpgService.reset();
    return this._rpgService.detections[+RPGType.CURRENCY];    
  }

  async getEvents(eventsList, eventType, roadblockList)
  {
    //1. Get events that are Currency type.
    eventsList = this._eventService.models.filter((event) => event.itemId != undefined);
    //2. Get events that are Receive type.
    eventsList = eventsList.filter(event=> +event.type === +EventType[eventType]);
    //3. Get words marked as currency.
    
    //4. Get events that the item is marked as currency (The item name should be included in the words marked as currency)
    let keys = Object.keys(this.detectedCurrencyWords);
    
    eventsList = await this.filterEventsByItemnameInDetectedcurrencywords(keys, eventsList);
    //5. Get Storybox ids.
    let receiveIds = eventsList.map(x => 
    {
      let split = x.id.split('.');
      let storyBoxId = '';
      if(split[0]) storyBoxId = split[0];      
      for(let i = 1; i < split.length; i++)
      {
        if(split[i])
        {
          storyBoxId += '.' + split[i];
          if(split[i].includes('SB')) return storyBoxId;
        }
      }
      return storyBoxId;
    });

    //5.1 Get Roadblocks that are in the event storybox.
    roadblockList = await this._roadblockService.filterByStoryBoxIds(receiveIds, false);
    return {eventsList: eventsList, roadblockList: roadblockList};
  }

  filterEventsByItemnameInDetectedcurrencywords(keys, list)
  {
    let aux = [];
    for(let i = 0; i < list.length; i++)
    {
      let item = this.itemService.svcFindById(list[i].itemId);
      for(let j = 0; j < keys.length; j++)
      {
        if(item.name == this.detectedCurrencyWords[keys[j]].word)
        {
          aux.push(list[i]);
        }
      }
    }
    list = [];
    return aux;
  }

  setGiveEventRoadblockIds() 
  {
    let giveIds = this.giveEvents.map(x => 
    {
      let split = x.id.split('.');
      let storyBoxId = '';
      if(split[0]) storyBoxId = split[0];      
      for(let i = 1; i < split.length; i++)
      {
        if(split[i])
        {
          storyBoxId += '.' + split[i];
          if(split[i].includes('SB')) return storyBoxId;
        }
      }
     
      return storyBoxId;
    });
    
    this.giveEventsRoadblockIds = this._roadblockService.filterByStoryBoxIds(giveIds, false);
  }

  setTradeEventRoadblockIds() 
  {
    let tradeIds = this.tradeEvents.map(x => 
    {
      let split = x.id.split('.');
      let storyBoxId = '';
      if(split[0]) storyBoxId = split[0];      
      for(let i = 1; i < split.length; i++)
      {
        if(split[i])
        {
          storyBoxId += '.' + split[i];
          if(split[i].includes('SB')) return storyBoxId;
        }
      }
     
      return storyBoxId;
    });
    this.tradeEventsRoadblockIds = this._roadblockService.filterByStoryBoxIds(tradeIds, false);
  }

  setReceiveEventRoadblockIds() 
  {
    let receiveIds = this.receiveEvents.map(x => 
    {
      let split = x.id.split('.');
      let storyBoxId = '';
      if(split[0]) storyBoxId = split[0];      
      for(let i = 1; i < split.length; i++)
      {
        if(split[i])
        {
          storyBoxId += '.' + split[i];
          if(split[i].includes('SB')) return storyBoxId;
        }
      }
     
      return storyBoxId;
    });
    this.receiveEventsRoadblockIds = this._roadblockService.filterByStoryBoxIds(receiveIds, false);
  }

  sortByProgress(array: string[], events: Event[]) 
  {	
    const definedItems = array.filter(item => item !== undefined && item !== '');	
    const undefinedItems = array.filter(item => item === undefined || item === '');	
  	
    definedItems.sort((a, b) => this._sortingService.sortArrayByAlphanumericValue(!this.reverseOrder, a, b));	
  	
    const sortedArray = this.reverseOrder ? undefinedItems.concat(definedItems) : definedItems.concat(undefinedItems);	
    array.splice(0, array.length, ...sortedArray);	
    const sortedIds = sortedArray.map(item => 
    {	
      if (item && item.includes('.')) 
      {	
        const id = item.replace(/\.[^.]*$/, '');	
        return id;	
      } 
      else return '';        
    });	

    let sortedEvents = events.slice().sort((a, b) => 
    {	
      const indexA = sortedIds.indexOf(a.hard?.id?.replace(/\.[^.]*$/, ''));	
      const indexB = sortedIds.indexOf(b.hard?.id?.replace(/\.[^.]*$/, ''));	
      if (indexA === -1 && indexB === -1) return 0; 	
      else if (indexA === -1) return 1;	
      else if (indexB === -1) return -1; 	
      else return indexA - indexB; 	
    });	
    if (this.reverseOrder) { sortedEvents = sortedEvents.reverse(); }	
    	
    events.splice(0, events.length, ...sortedEvents)	
    this.reverseOrder = !this.reverseOrder;	
  }	

  updateArray(array: any[], events: Event[]) 
  {	
    this.sortByProgress(array, events);	
  }

  sortById(events: any[]) 
  {
    if (!this.sortedById) 
    {
      events.sort((a, b) => 
      {
        let aHierarchy = +a.id.split('.')[0].substring(1);
        let aLevel = +a.id.split('.')[1].substring(1);

        let bHierarchy = +b.id.split('.')[0].substring(1);
        let bLevel = +b.id.split('.')[1].substring(1);

        if (aHierarchy == bHierarchy) 
        {
          if (aLevel > bLevel) return 1;          
          if (aLevel == bLevel) return 0;          
          if (aLevel < bLevel) return -1;          
        }
        if (aHierarchy > bHierarchy) return 1;
        else return -1;

      });

      this.sortedById = true;
      this.setGiveEventRoadblockIds();
      this.setReceiveEventRoadblockIds();
      this.setTradeEventRoadblockIds();
    }
    else if(this.sortedById)
    {
      events.sort((a, b) => 
      {
        let aHierarchy = +a.id.split('.')[0].substring(1);
        let aLevel = +a.id.split('.')[1].substring(1);

        let bHierarchy = +b.id.split('.')[0].substring(1);
        let bLevel = +b.id.split('.')[1].substring(1);

        if (aHierarchy == bHierarchy) 
        {
          if (aLevel > bLevel) return -1;          
          if (aLevel == bLevel) return 0;          
          if (aLevel < bLevel) return 1;          
        }
        if (aHierarchy > bHierarchy) return -1;
        else return 1;
      });

      this.sortedById = false;
      this.setGiveEventRoadblockIds();
      this.setReceiveEventRoadblockIds();
      this.setTradeEventRoadblockIds();
    }
  }
}

class Sorting extends Sortment.Sorting 
{
  byLocation = Sortment.byLocation(this._component.areaService);
  byLevelLocation = Sortment.byLevelLocation(this._component.areaService);
  byItem = Sortment.byItem(this._component.itemService);
  byType = (event: Event) => +event.type;
  byAmount = (event: Event) => event.amount;
  byId = (event: Event) => event.id;
  constructor(private readonly _component: CurrencyReportComponent) 
  {
    super();
  }
  public sortCurrencyGiveEvents(by: Sortment.Sortion<Event>) 
  {
    this.execute(this._component.giveEvents, by);
    this._component.setGiveEventRoadblockIds();
  }
  public sortCurrencyTradeEvents(by: Sortment.Sortion<Event>) 
  {
    this.execute(this._component.tradeEvents, by);
    this._component.setTradeEventRoadblockIds();
  }
  public sortCurrencyReceiveEvents(by: Sortment.Sortion<Event>) 
  {
    this.execute(this._component.receiveEvents, by);
    this._component.setReceiveEventRoadblockIds();
  }
}

class Accessing extends Accessment.Accessing 
{
  towardsDialogueEditor = Accessment.towardsDialogueEditor<Event>((m) => m.id);
  public accessEvent(event: Event, towards: Accessment.Accession<Event>) 
  {
    this.execute(event, towards);
  }
}
