<div class="main-content">
    <div class="container-fluid">
        <div class="card list-header-row">
            <app-header-with-buttons [cardTitle]="listName" [cardDescription]="cardDescription"
                [rightButtonTemplates]="[addButtonTemplate]">
            </app-header-with-buttons>
        </div>
    </div>
    <div class="card" style="margin-left: 15px; margin-right: 15px;">
        <table class="table table-list">
            <thead style="top: 115px;">
                <tr>
                    <th class="th-clickable" (click)="sortListByParameter('id')">
                        ID
                    </th>
                    <th class="th-clickable" (click)="sortListByParameter('name')">
                        Name
                    </th>
                    <th>Progress</th>
                    <th style="position: relative" placement='top' delay='250' ttWidth="auto" ttAlign="center"
                        ttPadding="10px" tooltip="Translate Language Pack">Actions
                        <span style="font-size: 14px" class="pe-7s-info"></span>
                    </th>
                    <th style="position: relative" placement='top' delay='250' ttWidth="auto" ttAlign="center"
                        ttPadding="10px" tooltip="Select translate system language" style="width: 10vw;">
                        Selected Language
                        <span style="font-size: 14px" class="pe-7s-info"></span>
                    </th>
                    <th style="width: 5vw">
                        Delete
                    </th>
                </tr>
            </thead>
            <tbody *ngFor="let language of languages; let i = index">
                <tr id="LANGbase">
                    <td class="td-sort">{{ language }}</td>
                    <td><input type="text" class="form-control form-title form-short" style="width: 95%;"
                            [value]="language" readonly></td>
                    <td class="border-left"></td>
                    <td class="td-actions border-both-sides" style="display: flex; justify-content: center;">
                        <button class="btn btn-size btn-fill btn-remove" title="Translate"
                            (click)="downloadLanguagePack(language)">
                            <i class="mat-translate"></i>
                        </button>
                        <br>
                    </td>
                    <td><input type="checkbox" [(ngModel)]="isChecked[i]" (click)="selectActiveLanguage(language, i)">
                    </td>
                    <td class="td-actions border-left">
                        <button class="btn btn-size btn-danger btn-fill btn-remove" title="Remove this language"
                            (click)="removeLanguage(language)">
                            <i class="pe-7s-close"></i>
                        </button>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>