<!-- Main dialogue tree leaf element with dynamic styling and visibility -->
<div class="tree-leaf"
     [ngClass]="{'has-multiple-roadblocks': roadBlocks.length > 1}"
     [style.visibility]="shouldHideElement() ? 'hidden' : 'visible'"
     [style.opacity]="shouldHideElement() ? '0' : '1'">
  <div class="leaf-container">
    <!-- Main leaf box with type-specific styling and interactive behavior -->
    <div class="leaf-box"
      [ngClass]="{'leaf-dead': deadLeaf,
                  'leaf-start': leafType == 'start',
                  'leaf-end': leafType == 'end',
                  'leaf-story-box': leafType == 'StoryBox',
                  'leaf-choice-box': leafType == 'ChoiceBox',
                  'leaf-investigation-box': leafType == 'InvestigationBox',
                  'leaf-dilemma-box': leafType == 'DilemmaBox',
                  'leaf-choice-option': leafType == 'ChoiceOption',
                  'leaf-investigation-option': leafType == 'InvestigationOption',
                  'leaf-dilemma-option': leafType == 'DilemmaOption',
                  'leaf-choice-failure': leafType == 'ChoiceFailure',
                  'leaf-investigation-failure': leafType == 'InvestigationFailure',
                  'leaf-dice-failure': leafType == 'DiceFailure',
                  'leaf-has-roadblock': hasRoadBlock,
                  'leaf-has-label': hasProgressLabel(),
                  'leaf-interactive': isChoiceOption() || isInvestigationOption() || isDilemmaOption() || isDiceFailure(),
                  'leaf-selected': isSelected() || isInvestigationSelected() || isDilemmaSelected(),
                  'leaf-unselected': (hasParentSelection() && !isSelected()) || (hasDilemmaParentSelection() && !isDilemmaSelected())}"
      [style.cursor]="(isChoiceOption() || isInvestigationOption() || isDilemmaOption() || isDiceFailure()) ? 'pointer' : 'default'"
      (click)="isChoiceOption() ? onChoiceOptionClick() : (isInvestigationOption() ? onInvestigationOptionClick() : (isDilemmaOption() ? onDilemmaOptionClick() : (isDiceFailure() ? onDiceFailureClick() : null)))"
      [title]="isChoiceOption() ? 'Click to select this choice option' : (isInvestigationOption() ? 'Click to toggle this investigation option' : (isDilemmaOption() ? 'Click to select this dilemma option' : (isDiceFailure() ? 'Click to select this dice failure option' : '')))">
      <!-- Main content area with text and visual indicators -->
      <div class="leaf-content">
        <div class="leaf-text">{{ text }}</div>
        <div class="leaf-subtext">{{ subText }}</div>

        <!-- Progress label key icon (shows when element unlocks roadblocks) -->
        <div *ngIf="hasProgressLabel()"
             class="leaf-key-container"
             [class.matched]="hasRoadblockMatchInDialogue()"
             [class.unmatched]="!hasRoadblockMatchInDialogue()"
             [style.background-color]="getProgressLabelColor()"
             [title]="getProgressLabelHoverText()">
          <i class="pe-7s-key leaf-key-icon" [style.color]="getKeyIconColor()"></i>
        </div>

        <!-- Dice system indicator for options with random outcomes -->
        <div *ngIf="shouldShowDiceIcon()"
             class="leaf-dice-container"
             [style.background-color]="getDiceColor()"
             title="Dice System Option">
          <img src="assets/svg/dice-d20.svg"
               class="leaf-dice-icon"
               [style.filter]="'brightness(0) invert(1)'"
               alt="Dice">
        </div>

        <!-- Green check icon for selected options -->
        <div *ngIf="isSelected() || isInvestigationSelected() || isDilemmaSelected()"
             class="leaf-check-container"
             title="Selected Option">
          <i class="pe-7s-check leaf-check-icon"></i>
        </div>
      </div>
    </div>

    <!-- Single left-side roadblock container -->
    <div class="roadblocks-container" *ngIf="hasRoadBlock && roadBlocks.length > 0">
      <div class="roadblock-branch-container" [ngClass]="getRoadblockSideClass()">
        <div [ngClass]="getHorizontalBranchClass()"></div>
        <roadblock-info [roadblocks]="roadBlocks" [andOrCondition]="getAndOrCondition()"></roadblock-info>
      </div>
    </div>
  </div>
</div>
<!-- Separator for FINISH_DIALOGUE End boxes (not dialogue options, not final tree end) -->
<ng-container *ngIf="shouldShowFinishDialogueSeparator()">
  <!-- Visual Separator -->
  <div class="restart-separator">
    <div class="separator-line"></div>
    <div class="diamonds">◆◆◆</div>
    <div class="separator-line"></div>
  </div>
</ng-container>

<!-- Regular leaf line (show unless: deadLeaf, deadEnd, or restartDialogue) -->
<div
  class="leaf-line"
  *ngIf="!deadLeaf && !deadEnd && !restartDialogue && designFinalLine && !shouldHideElement()"></div>

<!-- Restart Dialogue Section -->
<div *ngIf="restartDialogue && !shouldHideElement()" class="restart-dialogue-section">
  <!-- Restart Dialogue Box -->
  <div class="leaf-box restart-dialogue-box">
    <div class="leaf-content">
      <div class="leaf-text">Restart Dialogue</div>
    </div>
  </div>

  <!-- For regular dialogue boxes: show separator, continue box, and tree continuation -->
  <ng-container *ngIf="!isDialogueOption()">
    <!-- Visual Separator -->
    <div class="restart-separator">
      <div class="separator-line"></div>
      <div class="diamonds">◆◆◆</div>
      <div class="separator-line"></div>
    </div>

    <!-- Continue Box -->
    <div class="leaf-box continue-box">
      <div class="leaf-content">
        <div class="leaf-text">Continue</div>
      </div>
    </div>

    <!-- Continue the tree normally (only for regular dialogue boxes) -->
    <div class="leaf-line" *ngIf="!deadLeaf && designFinalLine && !shouldHideElement()"></div>
  </ng-container>

  <!-- For dialogue options: no separator, no continue box, no tree continuation (like finish dialogue) -->
</div>

<!-- FINISH_DIALOGUE End Section - show for all types -->
<dialogue-tree-layer
  layerType="end"
  *ngIf="deadEnd && !shouldHideElement()"
  [isDeadLayer]="deadLeaf"
  [isDeadEnd]="deadEnd"
  [parentIsDialogueOption]="isDialogueOption()"
  [selectedChoiceOptions]="selectedChoiceOptions"
  [roadblockEvaluationEnabled]="roadblockEvaluationEnabled"></dialogue-tree-layer>
