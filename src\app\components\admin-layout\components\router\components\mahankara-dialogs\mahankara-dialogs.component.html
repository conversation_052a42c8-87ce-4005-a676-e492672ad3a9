<div class="main-menu-efect">
    <div class="container-fluid">
      <div class="list-header-row update">
  
        <div class="card">
  
          <div style="width: 50%;">
            <button style="margin-left: 15px;"
              class="{{activeTab === 'mahankaraBehaviorTable' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
              (click)="switchToTab('mahankaraBehaviorTable')">Mahankara Behavior Table</button>
            <button style="margin-left: 2px;"
              class="{{activeTab === 'categories' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
              (click)="switchToTab('categories')">Categories</button>
            <button style="margin-left: 2px;"
              class="{{activeTab === 'groupings' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
              (click)="switchToTab('groupings')">Groupings</button>
            <button style="margin-left: 2px;"
              class="{{activeTab === 'concatenations' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
              (click)="switchToTab('concatenations')">Concatenations</button>
              <button style="margin-left: 2px;"
              class="{{activeTab === 'categoriesXStressStates' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
              (click)="switchToTab('categoriesXStressStates')">Categories X Stress States</button>
          </div>
  
          <ng-container *ngIf="isTab">
            <div style="display: flex; justify-content: space-between;">
              <div class="card-header-content" style="margin-top: 20px;">
                <h3 class="title">{{title}}</h3>
                <p style="width:60vw;" class="category">{{ description}}</p>
              </div>
  
              <div style="display: flex; align-items: end; justify-content: end;">
                <div style="margin-right: 15px; margin-bottom: 16px;">  
  
                  <ng-container>
                    <!--BUTTON EXCEL-->
                    <div id="button" style="position: absolute;">
                      <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'"
                        class="add-buttons" [buttonTemplates]="[excelButtonTemplate]">
                      </app-button-group>
                    </div>
                  </ng-container>
                </div>
              </div>
            </div>   
          </ng-container>
  
        </div>
      </div>
  
        <app-mahankara-behavior-table *ngIf="activeTab === 'mahankaraBehaviorTable'" [copyExcelMahankaraBehavior]="listExcelMahankara" 
        (descriptionOutput)="receiveText($event)" > 
       </app-mahankara-behavior-table>
        <app-mahankara-categories *ngIf="activeTab === 'categories'" [copyExcelMahankaraCategories]="listExcelMahankara" 
        (descriptionOutput)="receiveText($event)">            
        </app-mahankara-categories>
        <app-mahankara-groupings *ngIf="activeTab === 'groupings'" [copyExcelMahankaraGroupings]="listExcelMahankara"  
        (descriptionOutput)="receiveText($event)">            
        </app-mahankara-groupings>
        <app-mahankara-concatenations *ngIf="activeTab === 'concatenations'" [copyExcelMahankaraConcatenations]="listExcelMahankara"  
        (descriptionOutput)="receiveText($event)">            
        </app-mahankara-concatenations>
        <app-mahankara-categories-x-stress-states *ngIf="activeTab === 'categoriesXStressStates'" [copyExcelCategoriesXStress]="listExcelMahankara"  
        (descriptionOutput)="receiveText($event)">            
        </app-mahankara-categories-x-stress-states>
      
  
    </div>
  </div>