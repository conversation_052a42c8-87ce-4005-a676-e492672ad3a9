import { ChangeDetectorRef, Component, Input, OnInit, ViewChild } from '@angular/core';
import { CodeBlockDrop } from 'src/app/lib/@bus-tier/models';
import { UserSettingsService } from 'src/app/services';
import { CodeBlockDropService } from 'src/app/services/code-block-drop.service';
import { TranslationService } from 'src/app/services/translation.service';
import { FILTER_SUFFIX_PATH } from 'src/lib/darkcloud/angular/dsadmin/constants/others';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc';
import { IngredientVarianceComponent } from '../ingredient-variance/ingredient-variance.component';
import { ParticleVarianceComponent } from '../particle-variance/particle-variance.component';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-other-drops-generator',
  templateUrl: './other-drops-generator.component.html',
  styleUrls: ['./other-drops-generator.component.scss'],
})
export class OtherDropsGeneratorComponent extends SortableListComponent<CodeBlockDrop> implements OnInit
{
  @ViewChild(IngredientVarianceComponent) childComponent: IngredientVarianceComponent;
  @ViewChild(ParticleVarianceComponent) childComponent2: ParticleVarianceComponent;
  @Input() probability = true;
  public activeTab: string;
  activeTab3:string = 'particleDrop';
  activeTab2:string = 'Particles';
  ingredientType:string = '';

  constructor(
    _activatedRoute: ActivatedRoute,
    protected _codeBlockDropService: CodeBlockDropService,
    _userSettingsService: UserSettingsService,
    protected _translationService: TranslationService,
    private ref: ChangeDetectorRef
  ) 
  {
    super(_codeBlockDropService, _activatedRoute, _userSettingsService, 'name');
  }

  public override ngOnInit(): Promise<void> 
  {
    const tab = localStorage.getItem(`tab-BattleDropsGeneratorComponent${FILTER_SUFFIX_PATH}`);
    this.activeTab = tab === 'null' || !tab ? 'particleDrop' : tab;
    return null;
  }

  goToParticleDrop(path:string, type?:string)
  {
    let values = {ingredientType: type, activeTab: path};
    if(this.childComponent) this.childComponent.settingsValuesFromParent(values);
    if(this.childComponent2) this.childComponent2.settingsValuesFromParent(values);
    this.activeTab3 = path;
    if(type) this.ingredientType = type;
  }

  receiveActiveTab(activeTab)
  {
    this.activeTab2 = activeTab;
    localStorage.setItem(
      `tab-BattleDropsGeneratorComponent${FILTER_SUFFIX_PATH}`,
      this.activeTab
    );
    this.ngOnInit();
  }
}
