<body class="background-div" (mousedown)="handleOutsideMouseClick($event)" style="overflow-y: auto;">
  <div class="modal-backdrop" *ngIf="popupStats"></div> 
  <div id="modal-close" @popup class="popup-report" *ngIf="popupStats" style="background-color: transparent;">

    <div class="component-modal">	
      <div class="header-modal">	
        <i class="btn-icon pe-7s-angle-left-circle" style="cursor: pointer;"	
          (click)="moveNext(false)"></i>	
        <i class="i-txt">{{currentAreaName}}</i>	
        <i class="btn-icon pe-7s-angle-right-circle" style="cursor: pointer;"	
          (click)="moveNext(true)"></i>	
      </div>
      <div  class="modal-close m-close">	
        <button 
          class="btn btn-danger btn-fill btn-remove modal-btn-close" 	
          (click)="closeAreaStatsPopup()">	
          <i class="pe-7s-close i-icon"></i>	
        </button>	      
      </div>

    <table class="popup-area">
      <thead>
        <tr>
          <th colspan="2"></th>
          <th>This Area</th>
          <th>Total</th>
          <th>Area Average</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td rowspan="6">Levels</td>
          <td>Minion</td>
          <td>{{ countMinionLevel | thousandNumberFormat}}</td>
          <td>{{ countMinionTotal | thousandNumberFormat }}</td>
          <td>{{ percentageMinionLevel }}%</td>
        </tr>
        <tr>
          <td>Boss</td>
          <td>{{ countBossLevel | thousandNumberFormat }}</td>
          <td>{{ countBossTotal | thousandNumberFormat }}</td>
          <td>{{ percentageBossLevel }}%</td>
        </tr>
        <tr>
          <td>Secondary</td>
          <td>{{ countSecondaryLevel | thousandNumberFormat }}</td>
          <td>{{ countSecondaryTotal | thousandNumberFormat }}</td>
          <td>{{ percentageSecondaryLevel }}%</td>
        </tr>
        <tr>
          <td>Transition</td>
          <td>{{ counTransitionLevel | thousandNumberFormat }}</td>
          <td>{{ countTransitionTotal | thousandNumberFormat }}</td>
          <td>{{ percentageTransitionLevel }}%</td>
        </tr>
        <tr>
          <td>Minigame</td>
          <td>{{ counMinigameLevel | thousandNumberFormat }}</td>
          <td>{{ countMinigameTotal | thousandNumberFormat }}</td>
          <td>{{ percentageMinigameLevel }}%</td>
        </tr>
        <tr>
          <td>Total</td>
          <td>{{ totalLevelsArea | thousandNumberFormat }}</td>
          <td>{{ totalLevels | thousandNumberFormat }}</td>
          <td>{{ levelsPerArea }}</td>
        </tr>
        <tr>
          <td colspan="2">Words</td>
          <td>{{ totalWordsArea | thousandNumberFormat }}</td>
          <td>{{ totalWords | thousandNumberFormat }}</td>
          <td>{{ wordsPerArea }}</td>
        </tr>
        <tr>
          <td colspan="2">Words / Level</td>
          <td>{{ wordsPerLevelArea | thousandNumberFormat }}</td>
          <td>{{ wordsPerLevel | thousandNumberFormat }}</td>
          <td class="empty"></td>
        </tr>
        <tr>
          <td rowspan="4">In-Map</td>
          <td>Name</td>
          <td>{{ imNamesArea | thousandNumberFormat }}</td>
          <td>{{ imNames | thousandNumberFormat }}</td>
          <td>{{ imNamesPerArea }}</td>
        </tr>
        <tr>
          <td>Description</td>
          <td>{{ imDescriptionArea | thousandNumberFormat }}</td>
          <td>{{ imDescription | thousandNumberFormat }}</td>
          <td>{{ imDescriptionPerArea }}</td>
        </tr>
        <tr>
          <td>Name / Level</td>
          <td>{{ imNamesPercentArea | percent:'1.1-1' }}</td>
          <td>{{ imNamesPercent | percent:'1.1-1' }}</td>
          <td class="empty"></td>
        </tr>
        <tr>
          <td>Description / Level</td>
          <td>{{ imDescriptionPercentArea | percent:'1.1-1'  }}</td>
          <td>{{ imDescriptionPercent | percent:'1.1-1'  }}</td>
          <td class="empty"></td>
        </tr>
      </tbody>
    </table>
  </div>
</div>

  <div class="main-content">
    <div class="container-fluid">
      <!--Header-->
      <div class="list-header-row update">
        <div class="card">
          <app-header-with-buttons 
            [cardTitle]="listName"
            [cardDescription]="cardDescription"
            [rightButtonTemplates]="[addAreaTemplate]">
          </app-header-with-buttons>
          <app-header-search (inputKeyup)="lstOnChangeFilter($event)" (searchOptions)="lstOnChangeFilterOptions($event)"></app-header-search>
        </div>
      </div>
      <!--List-->
      <div class="card" >
        <table class="table table-list">
          <thead class="sticky">
            <tr>
              <th>Index</th>
              <th>ID</th>
              <th>Hierarchy code</th>
              <th>HC</th>
              <th style="min-width: 150px;">Name & Description
                <div class="ball-circle"></div>
              </th>
              <th>Notes</th>
              <th>Stats</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let area of lstIds | areas; let i = index; trackBy: trackByIndex">
              <tr id="{{ area.id }}">
                <td class="td-sort">{{ i+1 }}</td>
                <td class="td-id">{{ area.id }}</td>
                <td class="td-id">
                  <input 
                    class="form-control form-title form-short form-id"
                    type="text"
                    value="{{area.hierarchyCode}}"
                    #hierarchyCode
                    (change)="lstOnChange(area, 'hierarchyCode', hierarchyCode.value)" />
                </td>
                <td class="td-id ">
                  <input
                    style="width: 80px;border-style:solid;"
                    type="number"
                    #InputOrder
                    step="0.5"
                    [value]="area.order"
                    (change)="changeMinionStatsHC(area, InputOrder.value)" />
                </td>
                <td class="td-notes">
                  <input 
                    class="form-control form-short"
                    type="text"
                    value="{{ (area | translation : lstLanguage : area.id : 'name') }}"
                    #name
                    (change)="changeAreaName(area, 'name', name.value)"/>
                  <textarea 
                    class="form-control"
                    type="text"
                    value="{{ (area | translation : lstLanguage : area.id : 'description') }}"
                    #description
                    (change)="changeDescription(area, 'description', description.value)">
                  </textarea>
                </td>
                <td class="td-notes">
                  <textarea 
                    class="form-control borderless"
                    value="{{ (area | translation : lstLanguage : area.id : 'notes') }}"
                    #notes
                    (change)="lstOnChange(area, 'notes', notes.value)">
                  </textarea>
                </td>
                <td>                
                  <button (click)="areaStatsPopup(area, i)">
                    <i class="pe-7s-more"></i>
                  </button>
                </td>
                <td class="td-actions">
                  <button 
                    class="btn btn-danger btn-fill btn-remove"
                    (click)="removeArea(area)">
                    <i class="pe-7s-close"></i>
                  </button><br>
                  <button 
                    class="btn btn-gray btn-fill translation-button"
                    (click)="getAreaOrtography(area)">                          
                    <div class="mat-translate"></div>
                  </button>
                  <div></div>
                </td>
            </ng-container>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>