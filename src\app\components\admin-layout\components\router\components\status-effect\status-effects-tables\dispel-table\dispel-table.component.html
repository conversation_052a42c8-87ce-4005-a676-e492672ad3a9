<div class="div-container">
    <div class="width: 2221px;">
        <table class="table table-list borderList">
            <thead>
                <tr>
                    <th [attr.colspan]="titles.length +1">
                        <h3>Dispel</h3>
                    </th>
                    <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'" class="addButton"
                        [buttonTemplates]="[excelButtonTemplate]">
                    </app-button-group>
                </tr>
                <ng-container *ngIf="!isListDispelEmpty">

                    <tr>
                        <th class="default-color">Order</th>
                        <th [ngClass]="(title === 'SKILL RESIST USER') ? 'skill-resist' : 'default-color'"
                            *ngFor="let title of titles">{{title}}</th>
                    </tr>

                </ng-container>
            </thead>
            <ng-container *ngIf="!isListDispelEmpty">
                <tbody>
                    <tr *ngFor="let item of listDispelTable; let i = index">
                        <td style="background-color: #ddd; width: 4%;">{{ i + 1 }}</td>
                        <td class="td-id aligTitle" style="width: 11%;">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #idDispel [ngClass]="{'empty-input': !idDispel.value}"
                                [value]="item.idDispel" (change)="changeDispel(i,'idDispel', idDispel.value)" />
                        </td>
                        <td class="td-id aligTitle" style="width: 8%;">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #idCategory [ngClass]="{'empty-input': !idCategory.value}"
                                [value]="item.category" (change)="changeDispel(i,'category', idCategory.value)" />
                        </td>
                        <td class="td-id aligTitle" style="width: 8%;">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #skillResistUser [ngClass]="{'empty-input': !skillResistUser.value}"
                                [value]="item.skillResistUser"
                                (change)="changeDispel(i, 'skillResistUser', skillResistUser.value)" />
                        </td>
                        <td class="td-id" style="width: 10%;">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #statusEffectName [ngClass]="{'empty-input': !statusEffectName.value}"
                                [value]="item.statusEffectName"
                                (change)="changeDispel(i, 'statusEffectName', statusEffectName.value)" />
                        </td>
                        <td class="td-id" style="width: 40%; word-break: break-word;">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #description [ngClass]="{'empty-input': !description.value}"
                                [value]="item.description"
                                (change)="changeDispel(i, 'description', description.value)" />
                        </td>
                        <td class="td-id aligTitle" style="width: 10%;">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #powerPoints [ngClass]="{'empty-input': !powerPoints.value}"
                                [value]="item.powerPoints"
                                (change)="changeDispel(i, 'powerPoints', powerPoints.value)" />
                        </td>
                        <td class="td-id aligTitle" style="width: 10%;">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #allDispel [ngClass]="{'empty-input': !allDispel.value}"
                                [value]="item.allDispel" (change)="changeDispel(i, 'allDispel', allDispel.value)" />
                        </td>
                    </tr>
                </tbody>
            </ng-container>
            <ng-container *ngIf="isListDispelEmpty">
                <div class="card" style="text-align: center; padding: 20px;">
                    <h3>The list has not yet been imported.</h3>
                </div>
            </ng-container>
        </table>
    </div>

</div>