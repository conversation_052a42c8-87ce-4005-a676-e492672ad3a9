import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { Base } from './Base';

export class QnA
  extends Base<Data.Hard.IQnA, Data.Result.IQnA>
  implements Required<Data.Hard.IQnA>
{
  public static generateId(animaticId: string, index: number): string {
    return animaticId + '.' + IdPrefixes.QNA + index;
  }
  constructor(
    index: number,
    animaticId: string,
    dataAccess: QnA['TDataAccess']
  ) {
    super({ hard: { id: QnA.generateId(animaticId, index) } }, dataAccess);
  }
  protected getInternalFetch() {
    return {};
  }
  public get question(): string {
    return this.hard.question ?? null;
  }
  public set question(value: string) {
    this.hard.question = value;
  }
  public get answer(): string {
    return this.hard.answer ?? null;
  }
  public set answer(value: string) {
    this.hard.answer = value;
  }
}
