# SortableListComponent - Métodos de Ordenação de Missões

## Visão Geral

Os métodos `sortMissionObjectives()`, `sortMissionMissions()` e `sortMissionListById()` foram refatorados na classe abstrata `SortableListComponent` para funcionar diretamente com templates HTML. Agora podem ser chamados diretamente do template e utilizam um sistema de métodos abstratos que devem ser implementados pelos componentes filhos.

## Funcionalidade

### sortMissionObjectives()
Categoriza modelos em quatro grupos baseados em seus relacionamentos com modelos de referência:

1. **No References** (Sem Referências): Modelos que não possuem IDs de referência
2. **Not Assigned** (Não Atribuídos): Modelos com IDs de referência que não existem no serviço de referência
3. **Single Assigned** (Atribuição Única): Modelos com IDs de referência que aparecem uma vez no serviço de referência
4. **Multiple Assigned** (Atribuição Múltipla): Modelos com IDs de referência que aparecem múltiplas vezes no serviço de referência

### sortMissionMissions()
Categoriza missões em três grupos baseados em seu status de atribuição em eventos:

1. **Not Assigned** (Não Atribuídas): Missões que não têm eventos de atribuição (tipo 3)
2. **Single Assigned** (Atribuição Única): Missões que aparecem uma vez em eventos de atribuição
3. **Multiple Assigned** (Atribuição Múltipla): Missões que aparecem múltiplas vezes em eventos de atribuição

### sortMissionListById()
Ordena missões por ID de forma alternada:

1. **Crescente**: Ordena IDs numericamente (M1, M2, M3...)
2. **Decrescente**: Ordena IDs numericamente reverso (M3, M2, M1...)
3. **Alternância**: Cada clique alterna entre crescente e decrescente

## Uso no Template HTML

```html
<th class="th-clickable" (click)="sortMissionListById()">
  ID
</th>
<th class="th-clickable" (click)="sortMissionMissions()">
  Missions
</th>
<th class="th-clickable" (click)="sortMissionObjectives()">
  Objectives
</th>
```

## Implementação no Componente

### Para sortMissionObjectives()

```typescript
// Indica se o componente suporta ordenação de objetivos
protected override hasObjectiveSortingCapability(): boolean {
  return true;
}

// Retorna o serviço de referência (ex: EventService)
protected override getReferenceService(): any {
  return this._eventService;
}

// Retorna os arrays para armazenar resultados categorizados
protected override getObjectiveSortingArrays(): {
  noReferences: Model[],
  noAssigned: Model[],
  singleAssigned: Model[],
  multipleAssigned: Model[],
  result: Model[]
} {
  return {
    noReferences: this.noObjectives,
    noAssigned: this.noAssigned,
    singleAssigned: this.singleAssigned,
    multipleAssigned: this.multipleAssigned,
    result: this.missions
  };
}

// Retorna o contador de inversão (por referência)
protected override getInvertCounter(): { value: number } {
  return this.invertObjectivesCounter;
}

// Atualiza o componente com os resultados ordenados
protected override updateSortedResults(sortedResults: Model[]): void {
  this.missions = sortedResults;
  this.invertObjectives = this.invertObjectivesCounter.value;
}
```

### Para sortMissionMissions()

```typescript
// Indica se o componente suporta ordenação de missões
protected override hasMissionSortingCapability(): boolean {
  return true;
}

// Retorna os arrays para armazenar resultados de missões categorizadas
protected override getMissionSortingArrays(): {
  noAssigned: Model[],
  singleAssigned: Model[],
  multipleAssigned: Model[],
  result: Model[]
} {
  return {
    noAssigned: this.noMissionAssigned,
    singleAssigned: this.singleMissionAssigned,
    multipleAssigned: this.multipleMissionAssigned,
    result: this.missions
  };
}

// Retorna o contador de inversão para missões (por referência)
protected override getMissionInvertCounter(): { value: number } {
  return this.invertMissionsCounter;
}

// Atualiza o componente com os resultados de missões ordenadas
protected override updateMissionSortedResults(sortedResults: Model[]): void {
  this.missions = sortedResults;
  this.invertMissions = this.invertMissionsCounter.value;
}
```

### Para sortMissionListById()

```typescript
// Indica se o componente suporta ordenação por ID de missões
protected override hasMissionIdSortingCapability(): boolean {
  return true;
}

// Retorna a flag de ordenação ascendente (por referência)
protected override getMissionIdSortAscending(): { value: boolean } {
  return this.sortAscendingFlag;
}

// Retorna o array de missões para ordenar
protected override getMissionArray(): Mission[] {
  return this.missions;
}

// Atualiza o componente com os resultados ordenados por ID
protected override updateMissionIdSortedResults(sortedResults: Mission[]): void {
  this.missions = sortedResults;
  // A flag sortAscending já é atualizada por referência no método genérico
}
```

### Exemplo Completo: MissionListComponent

```typescript
export class MissionListComponent extends TranslatableListComponent<Mission> {
  // Arrays para categorização
  noObjectives: Mission[] = [];
  noAssigned: Mission[] = [];
  multipleAssigned: Mission[] = [];
  singleAssigned: Mission[] = [];
  missions: Mission[] = [];
  invertObjectives: number = 1;

  // Contador por referência para o método genérico
  private invertObjectivesCounter = { value: 0 };

  // Implementação dos métodos abstratos
  protected override hasObjectiveSortingCapability(): boolean {
    return true;
  }

  protected override getReferenceService(): any {
    return this._eventService;
  }

  protected override getObjectiveSortingArrays(): {
    noReferences: Mission[],
    noAssigned: Mission[],
    singleAssigned: Mission[],
    multipleAssigned: Mission[],
    result: Mission[]
  } {
    return {
      noReferences: this.noObjectives,
      noAssigned: this.noAssigned,
      singleAssigned: this.singleAssigned,
      multipleAssigned: this.multipleAssigned,
      result: this.missions
    };
  }

  protected override getInvertCounter(): { value: number } {
    if (this.invertObjectivesCounter.value === 0) {
      this.invertObjectivesCounter.value = this.invertObjectives;
    }
    return this.invertObjectivesCounter;
  }

  protected override updateSortedResults(sortedResults: Mission[]): void {
    this.missions = sortedResults;
    this.invertObjectives = this.invertObjectivesCounter.value;
  }
}
```

### Template HTML

```html
<th class="th-clickable" (click)="sortMissionObjectives()">
  Objectives
</th>
```

## Comportamento da Ordenação

### sortMissionObjectives() - 4 Ciclos
1. **Ciclo 1**: noAssigned → noReferences → singleAssigned → multipleAssigned
2. **Ciclo 2**: noReferences → singleAssigned → multipleAssigned → noAssigned
3. **Ciclo 3**: singleAssigned → multipleAssigned → noAssigned → noReferences
4. **Ciclo 4**: multipleAssigned → noAssigned → noReferences → singleAssigned

### sortMissionMissions() - 3 Ciclos
1. **Ciclo 1**: noAssigned → singleAssigned → multipleAssigned
2. **Ciclo 2**: singleAssigned → multipleAssigned → noAssigned
3. **Ciclo 3**: multipleAssigned → noAssigned → singleAssigned

### sortMissionListById() - 2 Estados
1. **Crescente**: IDs ordenados numericamente (M1, M2, M3...)
2. **Decrescente**: IDs ordenados numericamente reverso (M3, M2, M1...)

## Benefícios

1. **Uso Direto no Template**: Pode ser chamado diretamente do HTML sem métodos intermediários
2. **Reutilização**: Lógica centralizada na classe base
3. **Flexibilidade**: Componentes implementam apenas os métodos necessários
4. **Manutenibilidade**: Código organizado e bem estruturado
5. **Type Safety**: Totalmente tipado com TypeScript
6. **Compatibilidade**: Mantém a interface original do HTML

## Vantagens da Nova Implementação

- ✅ **Chamada Direta**: `(click)="sortMissionObjectives()"` funciona diretamente
- ✅ **Código Limpo**: Remove duplicação de lógica complexa
- ✅ **Extensibilidade**: Outros componentes podem implementar facilmente
- ✅ **Manutenção**: Lógica centralizada na classe base
- ✅ **Flexibilidade**: Cada componente define seus próprios serviços e arrays

## Migração do Código Existente

Para componentes que já usam `sortMissionObjectives()`:

1. **Remover** o método `sortMissionObjectives()` do componente
2. **Implementar** os 5 métodos abstratos obrigatórios
3. **Manter** as propriedades existentes (arrays, contadores)
4. **Testar** a funcionalidade no template HTML

O HTML não precisa ser alterado - continua funcionando com `(click)="sortMissionObjectives()"`.

## Comparação dos Métodos

| Aspecto | sortMissionObjectives | sortMissionMissions | sortMissionListById |
|---------|----------------------|-------------------|-------------------|
| **Grupos** | 4 (noReferences, noAssigned, singleAssigned, multipleAssigned) | 3 (noAssigned, singleAssigned, multipleAssigned) | 2 (crescente, decrescente) |
| **Ciclos** | 4 ordens diferentes | 3 ordens diferentes | 2 estados alternados |
| **Campo Verificado** | `objectiveIds` array | `missionId` individual | `id` com extração numérica |
| **Condição Especial** | Qualquer tipo de evento | Apenas eventos tipo 3 (ASSIGN_MISSION) | Remove prefixo "M" do ID |
| **Contador** | `invertObjectives` | `invertMissions` | `sortAscending` boolean |
| **Complexidade** | Alta (relacionamentos) | Média (atribuições) | Baixa (ordenação simples) |
