<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons [cardTitle]="'Tier List'" [cardDescription]="description"
          [rightButtonTemplates]="[statusTemplate]" [elements]="elements" [hasDropdownSelect]="true"
          (selectedElement)="changeElement($event)">
        </app-header-with-buttons>
        <app-header-search (inputKeyup)="search($event)"
          (searchOptions)="lstOnChangeFilterOptions($event)"></app-header-search>
      </div>
    </div>
    <!--List-->
    <div class="card">
      <table class="table table-list">
        <thead class="sticky">
          <tr>
            <th colspan="5">
              <h3>{{this.selectedElement}}</h3>
            </th>
          </tr>
          <tr>
            <th>Index</th>
            <th>Color</th>
            <th class="th-clickable" (click)="sortListByName()">Name
              <div class="ball-circle"></div>
            </th>
            <th class="th-clickable" (click)="sortListByName()">Acronym</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor=" let tier of this.tierList; let i = index; trackBy: trackById">           
            <tr *ngIf="tier.selectDrop == this.selectedElement">
              <td class="td-sort" style="width: 50px;">{{ i + 1 }}</td>
              <td #colorLabel style="width: 100px;" [ngStyle]="{ background: tier.color }" (click)="color.click()">
                <input #color type="color" style="visibility: hidden" [value]="tier.color"
                  (change)="updateColor(tier, color.value, colorLabel);" />
              </td>
              <td class="td-id">
                <input class="form-control form-short "
                  [ngStyle]="{'background-color': +name.value <= 0 ? '#404040' : '#ffffff'}" type="text"
                  value="{{ tier.name }}" #name (change)="updateValue(tier, name.value, 'name')" />
              </td>
              <td class="td-id">
                <input class="form-control form-short "
                  [ngStyle]="{'background-color': +acronym.value <= 0 ? '#404040' : '#ffffff'}" type="text"
                  value="{{ tier.acronym }}" #acronym (change)="updateValue(tier, acronym.value, 'acronym')" />
              </td>
              <td class="td-actions" style="width: 170px;">
                <button class="btn btn-danger btn-fill btn-remove" (click)="removeTier(tier)">
                  <i class="pe-7s-close"></i>
                </button><br>
                <button class="btn btn-gray btn-fill translation-button" (click)="getTierOrtography(tier)">
                  <div class="mat-translate"></div>
                </button>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>