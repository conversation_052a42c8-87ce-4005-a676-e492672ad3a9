import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/SortableListComponent';
import { ChangeDetectorRef, Component } from '@angular/core';
import { AdamantiumMining, Character} from 'src/app/lib/@bus-tier/models';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Button } from 'src/app/lib/@pres-tier/data';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { AdamantiumMiningService } from 'src/app/services/adamantium-mining.service';
import { Alert } from 'src/lib/darkcloud';
import { SpinnerService } from './../../../../../../../../../spinner/spinner.service';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-adamantium-mining-generator',
  templateUrl: './adamantium-mining-generator.component.html',
})
/**
 * Displays and edits emotion data as a list
 */
export class AdamantiumMiningGeneratorComponent extends SortableListComponent<AdamantiumMining> {
  constructor(
    private spinnerService: SpinnerService,
    _activatedRoute: ActivatedRoute,
    protected _adamantiumMiningService: AdamantiumMiningService,
    _userSettingsService: UserSettingsService,
    protected _languageService: LanguageService,
    protected _translationService: TranslationService,
    private ref: ChangeDetectorRef
  ) {
    super(
      _adamantiumMiningService,
      _activatedRoute,
      _userSettingsService,
      'name'
    );
  }

  public readonly excelButtonTemplate: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  public downloadSceneryOrtography(character: Character) {
    this._translationService.getCharacterOrtography(character, true);
  }

  protected override lstInit() {}
  description = '';
  protected override lstAfterFetchList() {
    this._adamantiumMiningService.models;
    if (this._adamantiumMiningService.models.length == 0) {
      for (let l = 1; l <= 20; l++) {
        this._adamantiumMiningService.createNewLaboratory(l);
      }
      this._adamantiumMiningService.toSave();
      this.lstFetchLists();
    }

    //remove empty element that just has lablevel == 0.
    this._adamantiumMiningService.models.find((blueprint) => {
      if (blueprint.adamantiumLevel === 0)
        this._adamantiumMiningService.svcToRemove(blueprint.id);
    });
    this.description = `Showing ${this._adamantiumMiningService.models.length} results`;
  }

  async onExcelPaste(): Promise<void> {
    this.spinnerService.setState(true)
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter((line) => line);

    if (this.DisplayErrors(lines)) return;

    for (let l = 0; l < lines.length; l++) {
      let line = lines[l];
      let cols = line.split(/\t/);

      let adamantiumMining = this._adamantiumMiningService.models.find(
        (tm) =>
          tm.adamantiumLevel ==
          +cols[0].split(' ').join('').split('.').join('').replace(',', '.')
      );
      if (!adamantiumMining) {
        adamantiumMining = this._adamantiumMiningService.createNewLaboratory(
          +cols[0].split(' ').join('').split('.').join('').replace(',', '.')
        );
      }

      if (cols[1]?.trim()) {
        adamantiumMining.souls = +cols[1]
          .split(' ')
          .join('')
          .split('.')
          .join('')
          .replace(',', '.');
      } else {
        adamantiumMining.souls = undefined;
      }
      if (cols[2]?.trim()) {
        adamantiumMining.time = +cols[2]
          .split(' ')
          .join('')
          .split('.')
          .join('')
          .replace(',', '.');
      } else {
        adamantiumMining.time = undefined;
      }
      if (cols[3]?.trim()) {
        adamantiumMining.rubies = +cols[3]
          .split(' ')
          .join('')
          .split('.')
          .join('')
          .replace(',', '.');
      } else {
        adamantiumMining.rubies = undefined;
      }
      if (cols[4]?.trim()) {
        adamantiumMining.storage = +cols[4]
          .split(' ')
          .join('')
          .split('.')
          .join('')
          .replace(',', '.');
      } else {
        adamantiumMining.storage = undefined;
      }
      if (cols[5]?.trim()) {
        adamantiumMining.production = +cols[5]
          .split(' ')
          .join('')
          .split('.')
          .join('')
          .replace(',', '.');
      } else {
        adamantiumMining.production = undefined;
      }

      await this._adamantiumMiningService.svcToModify(adamantiumMining);
      await  this._adamantiumMiningService.toSave();
      Alert.ShowSuccess('Adamantium Mining imported successfully!');
    }
    this.lstFetchLists();
    this.ref.detectChanges();
    this.spinnerService.setState(false)

  }

  DisplayErrors(array) {
    let count = array[0].split(/\t/);
    if (count.length < 6) {
      Alert.showError('Copy the ADAMANTIUM LEVEL column values too!');
      this.spinnerService.setState(false)
      return true;
    }

    if (count[0] === '') {
      Alert.showError('You are probably copying a blank column!');
      this.spinnerService.setState(false)
      return true;
    }

    return false;
  }
}
