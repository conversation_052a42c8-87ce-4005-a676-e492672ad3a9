import { Component, OnInit, Input, ViewChild, ElementRef, ChangeDetectorRef } from '@angular/core';
import { StoryBoxService } from 'src/app/services/story-box.service';
import { OptionBoxService } from 'src/app/services/option-box.service';
import { DilemmaBoxService } from 'src/app/services';
import { Dialogue, StoryBox, OptionBox, DilemmaBox} from 'src/app/lib/@bus-tier/models';
import { RoadBlockService } from 'src/app/services/road-block.service';

/**
 * Main dialogue simulator component that orchestrates the dialogue playback experience.
 * Manages the sequential loading of dialogue boxes and provides controls for simulation settings.
 */
@Component({
  selector: 'dialogue-simulator',
  templateUrl: './dialogue-simulator.component.html',
  styleUrls: ['dialogue-simulator.component.scss']
})
export class DialogueSimulatorComponent implements OnInit {
  // UI element references
  @ViewChild('messages') private myScrollContainer: ElementRef;

  // Animation timing configuration
  messageTyppingDelay = 600; // Milliseconds for typing animation
  messagesBetweenDelay = 1000; // Milliseconds between messages

  // Input data and state management
  @Input() public dialogue: Dialogue;
  public allBoxes: (StoryBox | OptionBox | DilemmaBox)[] = []; // All dialogue boxes in sequence
  public loadedBoxes: (StoryBox | OptionBox | DilemmaBox)[] = []; // Currently displayed boxes
  public finishedSimulation = false;

  // Feature toggles for simulation behavior
  public isRoadBlockViewEnabled = true; // Show/hide roadblock indicators
  public isDiceSimulationEnabled = true; // Enable/disable dice roll mechanics

  constructor(
    private _optionBoxService: OptionBoxService,
    private _storyBoxService: StoryBoxService,
    private _dilemmaBoxService: DilemmaBoxService,
    private _change: ChangeDetectorRef,
    private _roadBlockService: RoadBlockService,
  ) {
  }

  /**
   * Initialize the dialogue simulator by loading all dialogue boxes from the dialogue definition
   */
  public ngOnInit(): void {
    // Load all dialogue boxes from the dialogue's box IDs
    if (this.dialogue.boxIds.length > 0) {
      this.dialogue.boxIds.forEach(boxId => {
        // Try to find the box in each service (story, option, or dilemma)
        let storyBox = this._storyBoxService.svcFindById(boxId);
        let optionBox = this._optionBoxService.svcFindById(boxId);
        let dilemmabox = this._dilemmaBoxService.svcFindById(boxId);

        // Add the found box to our collection
        if (storyBox) {
          this.allBoxes.push(storyBox);
        } else if (optionBox) {
          this.allBoxes.push(optionBox);
        } else if (dilemmabox) {
          this.allBoxes.push(dilemmabox);
        }
      });
    }

    // Start the simulation by processing the first box
    this.processNextBox();
    console.log('LoadedBoxes', this.loadedBoxes);
  }

  /**
   * Process the next dialogue box in sequence or mark simulation as finished
   */
  public processNextBox(): void {
    if (this.allBoxes.length > this.loadedBoxes.length) {
      // Add the next box to the loaded boxes for display
      this.loadedBoxes.push(this.allBoxes[this.loadedBoxes.length]);
    } else {
      // All boxes have been processed
      this.finishedSimulation = true;
    }
    this.scrollToBottom();
    this._change.detectChanges();
  }

  /**
   * Toggle the visibility of roadblock indicators in the dialogue tree
   */
  toggleRoadBlockView(): void {
    this.isRoadBlockViewEnabled = !this.isRoadBlockViewEnabled;
  }

  /**
   * Toggle the dice simulation system on/off
   */
  toggleDiceSimulation(): void {
    this.isDiceSimulationEnabled = !this.isDiceSimulationEnabled;
  }

  /**
   * Update scroll position and trigger change detection
   */
  public updateScroll(): void {
    this.scrollToBottom();
    this._change.detectChanges();
  }

  /**
   * Adjust message animation speed based on slider input
   * @param value Slider value (0-3000, inverted for speed)
   */
  public changeMessageSpeed(value: string | number): void {
    // Convert string input to number and invert for speed
    const numValue = typeof value === 'string' ? parseInt(value, 10) : value;
    const invertedValue = 3000 - numValue;
    this.messagesBetweenDelay = Math.round(invertedValue * 1);
    this.messageTyppingDelay = Math.round(invertedValue * 0.6);
  }

  /**
   * Update the visual progress indicator on the speed slider
   * @param event Input event from the range slider
   */
  public updateSliderProgress(event: any): void {
    // Calculate progress percentage for the green trail effect
    const progress = ((event.target.value - event.target.min) / (event.target.max - event.target.min)) * 100;
    event.target.style.setProperty('--slider-progress', `${progress}%`);
  }

  /**
   * Lifecycle hook to ensure scroll position stays at bottom after view updates
   */
  ngAfterViewChecked(): void {
    this.scrollToBottom();
  }

  /**
   * Scroll the message container to the bottom to show latest messages
   */
  public scrollToBottom(): void {
    try {
      this.myScrollContainer.nativeElement.scrollTop = this.myScrollContainer.nativeElement.scrollHeight;
    } catch(err) {
      // Silently handle cases where scroll container is not available
    }
  }

}
