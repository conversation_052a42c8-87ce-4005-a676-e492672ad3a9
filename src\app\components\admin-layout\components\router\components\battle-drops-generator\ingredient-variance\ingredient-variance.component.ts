import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

import { IngredientVariance, WeaponUpgrade } from 'src/app/lib/@bus-tier/models';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';
import { ItemClass } from 'src/app/lib/@bus-tier/models/ItemClass';
import { Button } from 'src/app/lib/@pres-tier/data';
import { AreaService, ItemService, ItemsSelectorService, UserSettingsService } from 'src/app/services';
import { IngredientVarianceService } from 'src/app/services/ingredient-variance.service';
import { ItemClassService } from 'src/app/services/item-class.service';
import { TranslationService } from 'src/app/services/translation.service';
import { Alert } from 'src/lib/darkcloud';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc';
import { SpinnerService } from '../../../../../../../spinner/spinner.service';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-ingredient-variance',
  templateUrl: './ingredient-variance.component.html',
  styleUrls: ['./ingredient-variance.component.scss'],
})

export class IngredientVarianceComponent extends SortableListComponent<IngredientVariance> implements OnInit
{
  @Input() probability = true;
  particlesList:IngredientVariance[] = [];
  description:string = '';
  @Input() ingredientType:string = '';
  areas: number[] = [];
  sortNameOrder = +1;  
  itemClass: ItemClass;
  override listName:string = '';
  custom: Custom;
  isErrors = false;
  weaponUpgrades: WeaponUpgrade[];
  activeTab:string = 'areaDrop';
  public elements = ['Particles', 'Ingredients'];
  @Output() activeTab2 = new EventEmitter<string>();
  activeLanguage = 'PTBR';
  isModalInfo = false;
  textElement: string;


  constructor(
    private spinnerService: SpinnerService,
    _activatedRoute: ActivatedRoute,
    protected _ingredientVarianceService: IngredientVarianceService,
    _userSettingsService: UserSettingsService,
    private _router: Router,
    protected _translationService: TranslationService,
    private _itemsSelectorService: ItemsSelectorService,
    private _areaService: AreaService,
    private ref: ChangeDetectorRef,
    private _itemService: ItemService,
    private _itemClassService: ItemClassService,
    
  ) 
  {
    super(_ingredientVarianceService, _activatedRoute, _userSettingsService, 'name');
  }

  public override async ngOnInit(): Promise<void>
  {
    await this._ingredientVarianceService.toFinishLoading();
    this.checksIdNameItemClassParticleVariance();
   // this.particlesList = this._ingredientVarianceService.models.filter((part) => part.type == this.ingredientType);    
   
      this.activeTab = 'Ingredients';

      this.generateFirstParticleDrops('');
      this.generateFirstParticleDrops('A');
      this.generateFirstParticleDrops('B');  
      this.lineupOrderIngredientsList();

      const supTab = this.ingredientType ? `${this.ingredientType}`: '';
  
      if(supTab === '') {
        this.activeTab = undefined;
        this.textElement = '';
        this.listName = 'Area Drops';  
      } 
      else if(supTab === 'A'){
       this.activeTab = undefined;
       this.textElement = '';
        this.listName = 'Area Drops (Variance A)';
      } 
      else if(supTab === 'B') {
        this.activeTab = undefined;
        this.textElement = '';
        this.listName = 'Area Drops (Variance B)'; 
      }
    return null;
  }

  async checksIdNameItemClassParticleVariance()
  {
    //let valueParticles = [];
    const selectItem = this._itemsSelectorService.models.filter(item => item.itemsSection == 'INGREDIENTES');
  
    for (let index = 0; index < selectItem.length; index++) { 
         this._ingredientVarianceService.models.filter( x => {
        if (x.name === selectItem[index].itemName) {
          x.idItemClass = selectItem[index].ic_id;
          return x;
        }  else {         
          return null;
        }
      });      
    }
    /*
    valueParticles = this._ingredientVarianceService.models.filter(modelItem => {
      return selectItem.some(selectItem => selectItem.ic_id === modelItem.idItemClass);
    });   
*/
    //this._ingredientVarianceService.models = [];
   // this._ingredientVarianceService.models = valueParticles;
    this._ingredientVarianceService.toSave();
       
    this.particlesList = this._ingredientVarianceService.models.filter((part) => part.type == this.ingredientType); 
  }

  lineupOrderIngredientsList() 
  {
  this.sortNameOrder *= +1;
    this.particlesList.sort((a, b) => 
    {  
      return this.sortNameOrder * a.name.localeCompare(b.name);
    });

  }

  showTotalElementsByType()
  {
    let particlesAmount = this.particlesList.filter(particle => particle.type == this.ingredientType);
    this.description = `Showing ${particlesAmount.length} results`;
  }

  //modal
  onModalClick(): void {  
    this.isModalInfo = !this.isModalInfo;
   }

   
  closeAreaStatsPopup() {
    this.isModalInfo =  false;
  }
 
  sendActiveTab(element: string) 
  {
    this.activeTab = element;
    this.textElement = element;
    this.activeTab2.emit(element)
    this.ref.detectChanges();
  }

  settingsValuesFromParent(values)
  {
    this.activeTab = values.activeTab;
    this.ingredientType = values.ingredientType;
    this.ngOnInit();
  }

  getAreaOrder()
  {
    for(let i = 0; i < this._areaService.models.length; i++)
    {
      if(this._areaService.models[i]?.order != undefined && this._areaService.models[i]?.order.toString() != '')
        this.areas.push(this._areaService.models[i]?.order);
    }
    //Sort the array
    this.areas = this.areas.sort((a,b) => a-b);

    //Remove repeated values;
    this.areas = [...new Set(this.areas)];
  }

  async changeIngredientDrop(inputCommonProbability, particle)
  {
    particle.drop = inputCommonProbability;
    await this._ingredientVarianceService.svcToModify(particle);
  }

  async changeIngredientValue(inputCommonProbability, particle, index:number, tableOrder, drop?)
  {
    if(drop) particle.drop = drop;
    else
    {
      particle.amount[index] = inputCommonProbability == '' ? undefined : inputCommonProbability;
      particle.order[index] = tableOrder;
    }
   
    await this._ingredientVarianceService.svcToModify(particle);    
    await this._ingredientVarianceService.toSave();
    this.lstFetchLists();
    this.ref.detectChanges();  
    this.lstInit();
  }
  
  async generateFirstParticleDrops(ingredientType:string)
  {
    for(let i = 0; i < this._itemsSelectorService.models.length; i++)
    {
      if(this._itemsSelectorService.models[i].itemsSection == 'INGREDIENTES')
      {
        for(let j = 0; j < this._itemService.models.length; j++)
        {
          if(this._itemsSelectorService.models[i].itemName.trim() === this._itemService.models[j].name.trim())
          {          
            for(let k = 0; k < this._itemService.models[j]?.tagIds?.length; k++)
            {
              if(this._itemService.models[j].tagIds[k] == 'tg19')
              {
                this._ingredientVarianceService.
                  createNewParticleDrop(ingredientType, this._itemsSelectorService.models[i].itemName);
              }            
            }           
          }
        }
      }
    }
    
    this.checkIfThereIsDifference();
    this.getAreaOrder();
    this.showTotalElementsByType();
    const supTab = this.ingredientType ? ` ${this.ingredientType}`: '';
    this.listName = 'Area Drops - Ingredient ' +supTab;
   // this.ngOnInit();
  }

  //check if there is a difference
  checkIfThereIsDifference() {
    this.itemClass = this._itemClassService.svcFindById('IC18'); //Id Ingredient
    let listItem = [];
     this.itemClass.itemIds.forEach((x) =>{
      listItem.push(this._itemService.svcFindById(x));
     } ) 
     
    let list: ItemClass ;
    this._ingredientVarianceService.models = this._ingredientVarianceService.models.filter((ingredient) => {
     list = listItem.find(item => item.name === ingredient.name);
   
    //// If name exists in listItem and tabIds contains 'tg19', keep item
    if(list) {
      return list.tagIds?.includes('tg19');
    }
    // If the name does not exist in listItem, remove the item
    return false;
   });
    this.particlesList = this._ingredientVarianceService.models.filter((part) => part.type == this.ingredientType);
  }

  public readonly excelButtonTemplate: Button.Templateable = 
  {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  goBack()
  {
    this._router.navigate(['battleDropsGenerator/']);	
  }

  async onExcelPaste(): Promise<void> {
    this.spinnerService.setState(true);
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter(line => line.trim());

    const dropTypeErrors: string[] = [];
    const processedNames = new Set<string>();
    const duplicateDropTypes: string[] = [];

    // Contagem das colunas esperadas
    const expectedCols = this.areas.length + 1;

    for (let l = 0; l < lines.length; l++) {
        let line = lines[l];
        let cols = line.split(/\t/).map(col => col.trim());        

        // Verifica se o número de colunas é menor que o esperado
        if (cols.length < expectedCols) {              
            this.spinnerService.setState(false);
            this.displayErrors([`The copied data has ${cols.length} columns, but ${expectedCols} were expected.`]);         
            return;
        }
        
        const ingredientVariance = cols[0];
        
        // Verifica se há duplicidade de nomes
        if (processedNames.has(ingredientVariance)) {  
            duplicateDropTypes.push(ingredientVariance);                       
        } else {
            processedNames.add(ingredientVariance);
        }    

        // Verifica se o nome da partícula existe no sistema
        let particleDrop = this._ingredientVarianceService.models.find(x =>
            x.name.trim() === ingredientVariance && x.type === this.ingredientType);

        if (!particleDrop) {
            dropTypeErrors.push(ingredientVariance);                 
        }
    }

    if (dropTypeErrors.length > 0) {
        this.spinnerService.setState(false);
        this.displayErrors([`Ingredient Variance name not found in the system: "${dropTypeErrors.join(', ')}"`]);            
        return;  
    }

    if (duplicateDropTypes.length > 0) {
        this.spinnerService.setState(false);
        this.displayErrors([`Duplicate Ingredient Variance type names found: "${duplicateDropTypes.join(', ')}"`]);       
        return;  
    }       

    // Se não houver erros, proceder com a modificação e salvamento
    for (let l = 0; l < lines.length; l++) {
        let line = lines[l];
        let cols = line.split(/\t/).map(col => col.trim());
        const ingredientVariance = cols[0];

        let particleDrop = this._ingredientVarianceService.models.find(x =>
            x.name.trim() === ingredientVariance && x.type === this.ingredientType);
        
        for (let i = 0; i < this.areas.length; i++) {
            let amount = cols[i + 1]
                ? parseFloat(cols[i + 1].replace(' ', '').replace(',', '.').replace('%', ''))
                : undefined;

            if (!isNaN(amount)) {
                particleDrop.amount[i] = amount;
            } else {
                particleDrop.amount[i] = undefined;
            }

            particleDrop['order'][i] = +this.areas[i];
        }
        await this._ingredientVarianceService.svcToModify(particleDrop);
    }

        await this._ingredientVarianceService.toSave();
        Alert.ShowSuccess('Ingredient Variance copied successfully!');
        this.ref.detectChanges(); 
        this.lstFetchLists();           
        this.spinnerService.setState(false);
        this.particlesList = this._ingredientVarianceService.models.filter((part) => part.type == this.ingredientType);
        this.lineupOrderIngredientsList();
  }

      displayErrors(errors: string[]): boolean {
          if (errors.length > 0) {
              Alert.showError(errors.join('\n'));
              return true;
          }
          return false;
      }

}
