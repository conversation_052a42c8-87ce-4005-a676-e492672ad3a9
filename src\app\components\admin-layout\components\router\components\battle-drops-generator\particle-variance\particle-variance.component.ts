import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ParticleVariance, WeaponUpgrade } from 'src/app/lib/@bus-tier/models';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';
import { ItemClass } from 'src/app/lib/@bus-tier/models/ItemClass';
import { Button } from 'src/app/lib/@pres-tier/data';
import { AreaService, ItemsSelectorService, ParticleVarianceService, UserSettingsService } from 'src/app/services';
import { TranslationService } from 'src/app/services/translation.service';
import { Alert } from 'src/lib/darkcloud';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc';
import { SpinnerService } from '../../../../../../../spinner/spinner.service';
import { ActivatedRoute, Router } from '@angular/router';


@Component({
  selector: 'app-particle-variance',
  templateUrl: './particle-variance.component.html',
  styleUrls: ['./particle-variance.component.scss'],
})

export class ParticleVarianceComponent extends SortableListComponent<ParticleVariance> implements OnInit
{
  @Input() probability = true;
  particlesList:ParticleVariance[] = [];
  description:string = '';
  @Input() ingredientType:string = '';
  areas: number[] = [];
  sortNameOrder = +1;
  override listName:string = '';
  custom: Custom;
  supTab: string = '';
  textElement: string;
  itemClass: ItemClass;
  weaponUpgrades: WeaponUpgrade[];
  activeTab:string;
  @Output() activeTab2 = new EventEmitter<string>();
  dropsOptions:string[] = ['', 'MINION', 'BOSSES / SUBBOSSES'];
  public elements = ['Particles', 'Ingredients'];
  activeLanguage = 'PTBR';

  constructor(
    private spinnerService: SpinnerService,
    _activatedRoute: ActivatedRoute,
    protected _particleVarianceService: ParticleVarianceService,
    _userSettingsService: UserSettingsService,
    private _router: Router,
    protected _translationService: TranslationService,
    private _itemsSelectorService: ItemsSelectorService,
    private _areaService: AreaService,
    private ref: ChangeDetectorRef,
  )
  {
    super(_particleVarianceService, _activatedRoute, _userSettingsService, 'name');
  }

  public override async ngOnInit(): Promise<void>
  {
     await this._particleVarianceService.toFinishLoading();
    this.checksIdNameItemClassParticleVariance(); 

    if(this.particlesList.length == 0 || this.particlesList == undefined)
    {
      this.generateFirstParticleDrops('');
      this.generateFirstParticleDrops('A');
      this.generateFirstParticleDrops('B');
    } 
    this.getAreaOrder();
    this.showTotalElementsByType();
    this.lineupOrderVarianceList();
    this.supTab = this.ingredientType ? `${this.ingredientType}`: '';

    if(this.supTab === '') {
      this.activeTab = undefined;
      this.textElement = '';
      this.listName = 'Area Drops ';  
    } 
    else if(this.supTab === 'A'){
      this.activeTab = undefined;
      this.textElement = '';
      this.listName = 'Area Drops (Variance A) ';
    
    } 
    else if(this.supTab === 'B') {
      this.activeTab = undefined;
      this.textElement = '';
      this.listName = 'Area Drops (Variance B) '; 
    }
    
    return null;
  }

  async checksIdNameItemClassParticleVariance()
  {
    let valueParticles = [];
    const selectItem = this._itemsSelectorService.models.filter(item => item?.itemsSection == 'PARTÍCULAS');

    for (let index = 0; index < selectItem.length; index++) { 
         this._particleVarianceService.models.filter( x => {
        if (x.name === selectItem[index].itemName) {
          x.idItemClass = selectItem[index].ic_id;
          return x;
        }  else {         
          return null;
        }
      });      
    }
    
    valueParticles = this._particleVarianceService.models.filter(modelItem => {
      return selectItem.some(selectItem => selectItem.ic_id === modelItem.idItemClass);
    });   

    this._particleVarianceService.models = [];
    this._particleVarianceService.models = valueParticles;
    this._particleVarianceService.toSave();
       
    this.particlesList = this._particleVarianceService.models.filter((part) => part.type == this.ingredientType);
  }

  showTotalElementsByType()
  {
    let particlesAmount = this.particlesList.filter(particle => particle.type == this.ingredientType);  
    this.description = `Showing ${particlesAmount.length} results`;
  }
  

  sendActiveTab(element: string) 
  { 
    this.activeTab = element;
    this.textElement = element;

   // localStorage.setItem(`tab-ParticleVarianceComponent${FILTER_SUFFIX_PATH}`, this.activeTab);
    this.activeTab2.emit(element);
    this.ref.detectChanges();
  }

  settingsValuesFromParent(values)
  {
    this.activeTab = values.activeTab;
    this.ingredientType = values.ingredientType;
    this.ngOnInit();
  }

  getAreaOrder()
  {
    for(let i = 0; i < this._areaService.models.length; i++)
    {
      if(this._areaService.models[i]?.order != undefined && this._areaService.models[i]?.order.toString() != '')
        this.areas.push(this._areaService.models[i]?.order);
    }
    //Sort the array
    this.areas = this.areas.sort((a,b) => a-b);
   // this.lineupOrderParticlesList();
    //Remove repeated values;
    this.areas = [...new Set(this.areas)];
   
  }

  async changeIngredientDrop(inputCommonProbability, particle)
  {
    particle.drop = inputCommonProbability;
    await this._particleVarianceService.svcToModify(particle);
  }

  async changeIngredientValue(inputCommonProbability, particle, index:number, tableOrder, drop?)
  {
    if(drop) particle.drop = drop;
    else
    {
      particle.amount[index] = inputCommonProbability == '' ? undefined : inputCommonProbability;
      particle.order[index] = tableOrder;
    }
   
    await this._particleVarianceService.svcToModify(particle);
  }
  
  async generateFirstParticleDrops(ingredientType:string)
  {
    for(let i = 0; i < this._itemsSelectorService.models.length; i++)
    {
      if(this._itemsSelectorService.models[i].itemsSection == 'PARTÍCULAS')
        this._particleVarianceService.
          createNewParticleDrop(ingredientType, this._itemsSelectorService.models[i].itemName);
    }
    this.particlesList = await this._particleVarianceService.models; 
    this.ngOnInit();
  }

  public readonly excelButtonTemplate: Button.Templateable = 
  {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  goBack()
  {
    this._router.navigate(['battleDropsGenerator/']);	
  }

  async onExcelPaste(): Promise<void> {
    this.spinnerService.setState(true);
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter(line => line.trim());

    const errors: string[] = [];
    const processedNames = new Set<string>();

    for (let l = 0; l < lines.length; l++) {
        let line = lines[l];
        let cols = line.split(/\t/).map(col => col.trim());
           
        // Verifica se o número de colunas na linha é igual ao número de áreas + 2 (para particle.drop)
        if (cols.length !== this.areas.length + 2) {
            this.displayErrors([`The copied data has ${this.areas.length + 2} columns, but ${cols.length} were expected.`]);           
            return;
        }

        const particleName = cols[0];

        // Verifica se o nome da partícula existe na lista particlesList
        let particleDrop = this._particleVarianceService.models.find(x => x.name.trim() === particleName.trim() && x.type === this.ingredientType);
        if (!particleDrop) {
            errors.push(`Particle type names not found in the system: "${particleName}"`);
            continue;
        }

        // Verifica se há duplicidades
        if (processedNames.has(particleName)) {
            errors.push(`Duplicate drop type names found: "${particleName}".`);
            continue;
        }
        processedNames.add(particleName);

        // Processa os valores de amount para cada área
        for (let i = 0; i < this.areas.length; i++) {
            if (cols[i + 1]?.trim()) {
                let amount = parseFloat(cols[i + 1].replace(' ', '').replace(',', '.').replace('%', ''));
                if (isNaN(amount)) {
                    errors.push(`Line ${l + 1}, Column ${i + 2}: Invalid quantity value "${cols[i + 1]}".`);
                    continue;
                } else {
                    particleDrop['amount'][i] = amount;
                }
            } else {
                particleDrop['amount'][i] = undefined;
            }
            // Atualiza a ordem com base nas áreas         
            if (cols[i+1]?.trim()) particleDrop['order'][i] = +this.areas[i];  
        }

        // Atualiza o valor de drop com a última coluna após as áreas
        particleDrop.drop = cols[this.areas.length + 1];
        await this._particleVarianceService.svcToModify(particleDrop);
    }
    // Exibe as mensagens de erro se houver
    if (this.displayErrors(errors)) {
        this.spinnerService.setState(false);
        return;
    }

    await this._particleVarianceService.toSave();
    this.lstFetchLists();
    this.ref.detectChanges();
    Alert.ShowSuccess('Particles copied successfully!');
    this.lstInit();
    this.spinnerService.setState(false);
   // this.ngOnInit();   
}

displayErrors(errors: string[]): boolean {
    if (errors.length > 0) {
        this.spinnerService.setState(false);
        Alert.showError(errors.join('\n'));
        return true;
    }
    return false;
}


  lineupOrderVarianceList() 
  {
  this.sortNameOrder *= +1;
    this.particlesList.sort((a, b) => 
    {  
      return this.sortNameOrder * a.name.localeCompare(b.name);
    });
  }
  sortDropOrder = -1;
  lineupOrderDropsCharacter() 
  {
    this.sortDropOrder *= -1; // inverte a ordem
    this.particlesList.sort((a, b) => {
      return this.sortDropOrder * a.drop.localeCompare(b.drop);
    });    
  }

}
