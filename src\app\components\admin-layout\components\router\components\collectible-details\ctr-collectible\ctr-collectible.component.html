<div style="margin-top: 20px; padding-bottom: 70px;" class="card">

    <ng-container >
        <div style="padding: 20px;">
            <table class="table table-list" style="width: 320px;">
                <thead>
                  <tr>
                    <th class="dark-gray" colspan="2">
                      <h4>CTR</h4>
                    </th>
                  </tr>
                  <tr>
                    <th class="one-Column">LUK.new</th>
                    <th style="width: 50%;">Critical_Chance (%)</th>
                  </tr>
                </thead>
                <tbody>
                  <ng-container *ngFor="let ctr of listCTRCollectibes.valuesCTR; let i = index;">
                    <tr>
                      <td>
                          {{ctr.lukNew}}
                      </td>
                      <td class="gray">
                          {{ctr.critical_change ? ctr.critical_change : ''}}
                      </td>
                    </tr>
                  </ng-container>
                </tbody>
              </table>
              <div *ngIf="listCTRCollectibes">
                <span><b>LUK (Sorte):</b> {{luck ? luck : 'Sem valor no Primal Modifier' }}</span> <br>
                <span><b>CTR_BASE (Chance crítica):</b> {{ctr_base ? ctr_base : 'Sem valor no Primal Modifier'}}</span><br>
                <span><b>CTR_MAX:</b> {{ctr_max ? ctr_max : 'Sem valor no CTR-MAX'}}</span>
              </div>   
        </div>    
    </ng-container>

</div>





