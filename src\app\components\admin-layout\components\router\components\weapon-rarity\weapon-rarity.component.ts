import { Component } from '@angular/core';
import { WeaponRarity } from 'src/app/lib/@bus-tier/models';
import { Button } from 'src/app/lib/@pres-tier/data';
import { AreaService, TierService } from 'src/app/services';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Alert } from 'src/lib/darkcloud';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc';
import { WeaponRarityService } from './../../../../../../services/WeaponRarity.service';
import { SpinnerService } from './../../../../../../spinner/spinner.service';
import { IWeaponRarity } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard/IWeaponRarity';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-weapon-rarity',
  templateUrl: './weapon-rarity.component.html',
  styleUrls: ['./weapon-rarity.component.scss'],
})
export class WeaponRarityComponent extends SortableListComponent<WeaponRarity> {
  areasList = [];
  isText: boolean = true;
  weaponList = [];
  weaponRarity = [];
  weaponRarityNames = [];

  public readonly excelButtonTemplate: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  constructor(
    _activatedRoute: ActivatedRoute,
    protected _weaponRarityService: WeaponRarityService,
    _userSettingsService: UserSettingsService,
    protected _languageService: LanguageService,
    protected _translationService: TranslationService,
    protected _areaService: AreaService,
    private _tierListService: TierService,
    private spinnerService: SpinnerService
  ) {
    super(_weaponRarityService, _activatedRoute, _userSettingsService, 'name');
  }

  protected override async lstInit() {
    await this._weaponRarityService.toFinishLoading();
    await this._tierListService.toFinishLoading();
    this.weaponRarityNames = this._tierListService.models.filter((tier) =>tier.selectDrop === 'Weapon Rarity');
  
    if(this.weaponRarityNames.length > 0) {
      let weapon: IWeaponRarity;
      for (let i = 0; i < this.weaponRarityNames.length; i++) {        
        weapon = this._weaponRarityService.createNewWeaponRarity(this.weaponRarityNames[i]); 
        if(weapon != null){
          weapon.rarityId = this.weaponRarityNames[i].id;
          this.weaponRarity.push(weapon);           
        }            
      }    
    }      

    this.removeNameUndefined();
    this.weaponList = this._weaponRarityService.models;     
     
    this.lstFetchLists();
  }

  removeNameUndefined() {
    let list = [];
     this._weaponRarityService.models.filter((x) => {
      if(x.name != undefined) {
        list.push(x);
      }      
    });
    this._weaponRarityService.models = [];
    this._weaponRarityService.models = list;    
    this._weaponRarityService.toSave(); 
  }

  async onChangeAvailableRarity(weapon: WeaponRarity, value: string) {
    const w = this._weaponRarityService.svcFindById(weapon.id);
    w.availablesSilicatesSlots = value == '' ? undefined : +value;
    w.name = weapon.name;
    await this._weaponRarityService.svcToModify(w);
  }

  async onChangeStars(weapon: WeaponRarity, value: string) {
    const w = this._weaponRarityService.svcFindById(weapon.id);
    w.stars = value == '' ? undefined : +value;
    w.name = weapon.name;
    await this._weaponRarityService.svcToModify(w);  
  }

  async onExcelPaste(): Promise<void> {
    this.spinnerService.setState(true);
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter((line) => line);

    if (this.displayErrors(lines)) return;

    for (let l = 0; l < lines.length; l++) {
      let line = lines[l];
      let cols = line.split(/\t/);

      let weapon = this._weaponRarityService.models.find(
        (w) =>
          w.name ==
          cols[0].split(' ').join('').split('.').join('').replace(',', '.')
      );
      if (!weapon) {
        this.spinnerService.setState(false);
        return Alert.showError(`The name: ${cols[0]} does NOT exists in the Tier list!`);
      }      

      if (cols[1]?.trim()) {
        weapon.availablesSilicatesSlots = +cols[1]
          .split(' ')
          .join('')
          .split('.')
          .join('')
          .replace(',', '.');
      } else weapon.availablesSilicatesSlots = undefined;

      if (cols[2]?.trim()) {
        weapon.stars = +cols[2]
          .split(' ')
          .join('')
          .split('.')
          .join('')
          .replace(',', '.');
      } else weapon.stars = undefined;

      await this._weaponRarityService.svcToModify(weapon);
      await this._weaponRarityService.toSave();
    }
    Alert.ShowSuccess('Weapon Rarity imported successfully!');
    this.lstFetchLists();
    this.spinnerService.setState(false);
  }

  displayErrors(array) {
    let count = array[0].split(/\t/);
    if (count.length < 3) {
      Alert.showError('Copy the WEAPON RARITY column values too!');
      this.spinnerService.setState(false);
      return true;
    }

    if (count[0] === '') {
      Alert.showError('You are probably copying a blank column!');
      this.spinnerService.setState(false);
      return true;
    }
    return false;
  }
}
