import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

/**
 * Enum defining the three choices available in the dice choice popup
 */
export enum DiceChoiceOption {
  POSITIVE = 'positive',   // Guaranteed success
  NEGATIVE = 'negative',   // Guaranteed failure
  ROLL_DICE = 'roll_dice'  // Roll dice to determine outcome
}

/**
 * Interface for the dice choice popup state
 */
export interface DiceChoiceState {
  isVisible: boolean;
  option?: any; // The dialogue option that triggered the choice
  onChoice?: (choice: DiceChoiceOption) => void; // Callback for user's choice
}

/**
 * Service managing the dice choice popup that appears before dice rolls.
 * Allows users to choose between guaranteed outcomes or rolling dice.
 * Part of the dice system that gives players control over risk vs. certainty.
 */
@Injectable({
  providedIn: 'root'
})
export class DiceChoicePopupService {
  // State management for the choice popup
  private choiceState = new BehaviorSubject<DiceChoiceState>({
    isVisible: false
  });

  public choiceState$ = this.choiceState.asObservable();

  constructor() { }

  /**
   * Display the dice choice popup for a dialogue option
   * @param option The dialogue option that triggered the choice
   * @param onChoice Callback function to handle the user's choice
   */
  showChoicePopup(option: any, onChoice: (choice: DiceChoiceOption) => void): void {
    console.log('DiceChoicePopupService: Showing choice popup for option:', option);

    this.choiceState.next({
      isVisible: true,
      option: option,
      onChoice: onChoice
    });
  }

  /**
   * Hide the choice popup and clear state
   */
  hideChoicePopup(): void {
    console.log('DiceChoicePopupService: Hiding choice popup');

    this.choiceState.next({
      isVisible: false,
      option: undefined,
      onChoice: undefined
    });
  }

  /**
   * Process user's choice and execute the callback
   * @param choice The user's selected choice (positive/negative/roll dice)
   */
  makeChoice(choice: DiceChoiceOption): void {
    const currentState = this.choiceState.value;

    console.log('DiceChoicePopupService: User made choice:', choice);

    // Execute the callback if available
    if (currentState.onChoice) {
      currentState.onChoice(choice);
    }

    // Hide the popup after choice is processed
    this.hideChoicePopup();
  }

  /**
   * Get current choice state for immediate access
   * @returns Current state of the choice popup
   */
  getCurrentState(): DiceChoiceState {
    return this.choiceState.value;
  }

  /**
   * Check if choice popup is currently visible
   */
  isVisible(): boolean {
    return this.choiceState.value.isVisible;
  }
}
