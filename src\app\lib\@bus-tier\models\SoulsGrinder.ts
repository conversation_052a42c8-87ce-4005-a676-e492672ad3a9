import {
  CharacterType,
  Gender,
  IdPrefixes,
} from 'src/lib/darkcloud/dialogue-system';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { Base } from './Base';

export class SoulsGrinder  extends Base<Data.Hard.ISoulsGrinder, Data.Result.ISoulsGrinder>
  implements Required<Data.Hard.ISoulsGrinder>
{
  private static generateId(index: number): string {
    return IdPrefixes.SOULS_GRINDER + index;
  }
  constructor(
    index: number,
    soulsLevel: number,
    dataAccess: SoulsGrinder['TDataAccess']
  ) {
    super(
      {
        hard: {
          id: SoulsGrinder.generateId(index),
          soulsLevel,
        },
      },
      dataAccess
    );
  }


  protected getInternalFetch() {
    return {};
  }


  public get soulsLevel(): number
  {
    return this.hard.soulsLevel;
  }
  public set soulsLevel(value: number)
  {
    this.hard.soulsLevel = value;
  }

  public get titaniumCost(): number
  {
    return this.hard.titaniumCost;
  }
  public set titaniumCost(value: number)
  {
    this.hard.titaniumCost = value;
  }

  public get requiredTime(): number
  {
    return this.hard.requiredTime;
  }
  public set requiredTime(value: number)
  {
    this.hard.requiredTime = value;
  }

  public get rubiesSkipCost(): number
  {
    return this.hard.rubiesSkipCost;
  }
  public set rubiesSkipCost(value: number)
  {
    this.hard.rubiesSkipCost = value;
  }

  public get goldSkipCost(): number
  {
    return this.hard.goldSkipCost;
  }
  public set goldSkipCost(value: number)
  {
    this.hard.goldSkipCost = value;
  }

  public get localStorage(): number
  {
    return this.hard.localStorage;
  }
  public set localStorage(value: number)
  {
    this.hard.localStorage = value;
  }

  public get productionPerTime(): number
  {
    return this.hard.productionPerTime;
  }
  public set productionPerTime(value: number)
  {
    this.hard.productionPerTime = value;
  }

  public get type(): string
  {
    return this.hard.type;
  }
  public set type(value: string)
  {
    this.hard.type = value;
  }

}
