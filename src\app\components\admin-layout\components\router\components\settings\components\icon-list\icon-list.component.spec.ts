import { ComponentFixture, TestBed } from "@angular/core/testing";
import { IndexStorageService, LevelService, UserSettingsService } from "src/app/services";
import { IconListComponent } from "./icon-list.component";

class MockService
{
    addIcon(){return;}

    data = {
        icons: [
            {id: 'Test', name: 'Big Gringo'}
        ]
    }
}

class IndexStorageServiceStub{
    getData<T>(...args: any){ return []; }
}

describe('IconListComponent', () => {
    let userSettingsService: UserSettingsService;

    let fixture: ComponentFixture<IconListComponent>;
    let component: IconListComponent;

    beforeEach(() => {
        TestBed.configureTestingModule({
            declarations: [],
            providers: [
                {provide: UserSettingsService, useClass: MockService},
                {provide: LevelService, useClass: MockService},
                {provide: IndexStorageService, useClass: IndexStorageServiceStub}
            ]
        });

        fixture = TestBed.createComponent(IconListComponent);
        component = fixture.componentInstance;

        userSettingsService = TestBed.inject(UserSettingsService);
    });

    it('should be created', () => {
        expect(component).toBeTruthy();
    });

    it('should add Icon', () => {
        let spy = spyOn(userSettingsService, 'addIcon');

        component.toPromptAddIcon();

        expect(spy).toHaveBeenCalled();
    });

    it('should render list', () => {
        component.renderList();

        expect(component.icons).toBe(userSettingsService.data.icons);
    });

});