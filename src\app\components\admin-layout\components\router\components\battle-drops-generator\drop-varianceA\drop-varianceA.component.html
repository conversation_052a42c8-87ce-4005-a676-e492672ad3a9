<div style="margin-top: 20px">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">

        <div class="card-header-content" style="position: absolute; top: 10%;">
          <h3 class="title">Area Drops (Variance A) {{subText ? ' - ' : ''}} {{subText}}</h3>
          <p style="width:60vw;" class="category">{{ description}}</p>
        </div>

        <div style="display: flex; align-items: end; justify-content: end; margin-right: 90px;">
          <div style="margin-right: 15px; margin-bottom: 16px;">
            <button (click)="switchToTab('Code Block')"
              class="{{activeTab === 'Code Block' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}">
              Code Block
            </button>
            <button (click)="switchToTab('Gold')"
              class="{{activeTab === 'Gold' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}" style="margin-left: 5px;">
              Gold
            </button>
            <button (click)="switchToTab('Ichor')"
              class="{{activeTab === 'Ichor' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}" style="margin-left: 5px;">
              Ichor
            </button>
            <button (click)="switchToTab('Ruby')"
              class="{{activeTab === 'Ruby' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}" style="margin-left: 5px;">
              Ruby
            </button>
          </div>
        </div>

        <div style="position: relative; margin-bottom: 3px;">
          <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'" class="add-buttons"
            [buttonTemplates]="[excelButtonTemplate]">
          </app-button-group>
        </div>

      </div>
    </div>

    <ng-container *ngIf="subText != undefined">
    <div style="overflow-x: auto;">
      <table class="table-list">
        <thead class="sticky">
          <tr>
            <th class="th-clickable" (click)="sortListByParameter('type')">Type</th>
            <th *ngFor="let area of areas; let i = index" class="th-clickable"
              (click)="sortListByParameter('amount', i)">{{ area.order }}</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let drop of this.dropsList">
            <tr id="{{ drop.id }}"
              *ngIf="drop.element == this.selectedElement && drop.slot0 == this.slot0 && drop.variance == this.variance">
              <td class="td-id other-td">{{ drop.type }}</td>
              <td class="td-id other-td" *ngFor="let area of areas; let i = index">
                <input type="number" style="border-style:solid; padding-left: 8px;" #InputCommonProbability
                  [ngClass]="{'empty-input': !InputCommonProbability.value}" [value]="GetAmountValue(drop, i)"
                  (change)="changeDropAmount(drop, InputCommonProbability.value, i)" />
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </ng-container>
  </div>