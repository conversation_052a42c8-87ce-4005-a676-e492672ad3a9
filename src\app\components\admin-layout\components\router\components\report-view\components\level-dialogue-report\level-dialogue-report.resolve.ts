import { Injectable } from '@angular/core';
import { MarkerService } from 'src/app/services';
import { EasyMVC } from 'src/lib/darkcloud/angular';
import { ItemType, MarkerType } from 'src/lib/darkcloud/dialogue-system';
import { Preloadable } from './level-dialogue-report.component';

@Injectable({
  providedIn: 'root',
})
export class LevelDialogueReportResolve extends EasyMVC.Resolve<Preloadable> {
  constructor(private _markerService: MarkerService) {
    super(_markerService);
  }
  async rsvOnResolve(): Promise<Preloadable> {
    return {
      finishDialogueMarkers: this._markerService.models.filter(
        (marker) => +marker.type === MarkerType.FINISH_DIALOGUE
      ),
      releaseDialogueMarkers: this._markerService.models.filter(
        (marker) => +marker.type === MarkerType.RELEASE_DIALOGUE
      ),
    };
  }
}
