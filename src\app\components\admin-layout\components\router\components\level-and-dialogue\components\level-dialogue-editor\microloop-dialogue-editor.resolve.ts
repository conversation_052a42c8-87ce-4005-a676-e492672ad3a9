import { Injectable } from "@angular/core";
import { ActivatedRouteSnapshot, Resolve } from "@angular/router";
import { Dialogue, Level, Character, Mission, Area } from "src/app/lib/@bus-tier/models";
import { AreaService, CharacterService, DialogueService, MissionService, SoundService } from "src/app/services";
import { MicroloopService } from "src/app/services/microloop.service";
import { extractInt, sortData } from "src/lib/others";

interface Preloadable {
    dialogue: Dialogue;
    level: Level;
    speakers: Character[];
    missions: Mission[];
    preloadedCharacters: Character[];
}

@Injectable({
    providedIn: 'root'
})
export class MicroloopDialogueEditorResolve implements Resolve<Preloadable>
{
    constructor(
        private _dialogueService: DialogueService,
        private _microloopService: MicroloopService,
        private _characterService: CharacterService,
        private _missionService: MissionService,
        private _areaService: AreaService,
        private _soundService: SoundService
    ){}

    async resolve(route: ActivatedRouteSnapshot): Promise<Preloadable>
    {
        const dialogueId = route.paramMap.get('dialogueId');
        const levelId = route.paramMap.get('levelId');

        await this._dialogueService.toFinishLoading();
        await this._microloopService.toFinishLoading();
        await this._characterService.toFinishLoading();
        await this._missionService.toFinishLoading();
        await this._areaService.toFinishLoading();
        await this._soundService.toFinishLoading();

        const dialogue = this._dialogueService.svcCloneById(dialogueId);
        const level = this._microloopService.svcCloneById(levelId);
        const areaId = Area.getSubIdFrom(level.id);
        const speakers = this._characterService.svcCloneByIds(level.speakerIds);
        const preloadedCharacters = sortData(this._characterService.models, 'name');
        const missions = sortData(
            this._missionService.models.filter(
                mission => extractInt(this._areaService.svcFindById(mission.areaId)?.hierarchyCode)
                === extractInt(this._areaService.svcFindById(areaId)?.hierarchyCode)
            ), 'name'
        );

        return {
            dialogue,
            level,
            speakers,
            missions,
            preloadedCharacters
        };
    }
}