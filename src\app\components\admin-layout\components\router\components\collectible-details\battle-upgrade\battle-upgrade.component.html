<div style="margin-top: 20px" class="list-header-row update">
  <div style="padding-top: 10px;" class="card">
    <app-header-with-buttons [cardTitle]="'Battle Upgrade'" [valueBossLevel]="valueBossLevel" [nameClass]="nameClass"
      [nameRarity]="nameRarity"  [type]="type" [cardDescription]="''" [rightButtonTemplates]="[excelButtonTemplate]"
      [isBackButtonEnabled]="false">
    </app-header-with-buttons>
  </div>
</div>

<div class="card">
  <h2 style="text-align:center;">{{this.hierachyCode}}</h2>
  <table class="table table-list">
    <thead>
      <tr>
        <th class="dark-gray" colspan="6">
          <h4>Battle Upgrade</h4>
        </th>
      </tr>
      <tr>
        <th class="gray">APmin</th>
        <th class="gray">BL</th>
        <th class="gray">HP</th>
        <th class="gray">ATK</th>
        <th class="gray">DEF</th>
        <th class="gray">ATKlim</th>
      </tr>
    </thead>
    <tbody>
      <ng-container *ngFor="let baseLevel of currentCharacter?.baseLevel">
        <tr>
          <td>
            <input placeholder=" " class="background-input-table-color form-control form-short "
              style="border-style:solid;" type="number" #apMin value="{{currentCharacter?.apMin[baseLevel]}}"
              (change)="changeBattleupgradeValue(currentCharacter, apMin.value, baseLevel, 'apMin')" />
          </td>
          <td>
            <input class="form-control form-short " type="text" readonly value="{{baseLevel}}" />
          </td>
          <td>
            <input class="background-input-table-color form-control form-short " placeholder=" " type="number" #hp
              [value]="currentCharacter?.hp[baseLevel]"
              (change)="changeBattleupgradeValue(currentCharacter, hp.value, baseLevel, 'hp')" />
          </td>
          <td>
            <input class="background-input-table-color form-control form-short " placeholder=" " type="number" #atk
              [value]="currentCharacter?.atk[baseLevel]"
              (change)="changeBattleupgradeValue(currentCharacter, atk.value, baseLevel, 'atk')" />
          </td>
          <td>
            <input class="background-input-table-color form-control form-short " placeholder=" " type="number" #def
              [value]="currentCharacter?.def[baseLevel]"
              (change)="changeBattleupgradeValue(currentCharacter, def.value, baseLevel, 'def')" />
          </td>
          <td>
            <input class="background-input-table-color form-control form-short " placeholder=" " type="number" #atkLim
              [value]="currentCharacter?.atkLim[baseLevel]"
              (change)="changeBattleupgradeValue(currentCharacter, atkLim.value, baseLevel, 'atkLim')" />
          </td>
        </tr>
      </ng-container>
    </tbody>
  </table>
</div>