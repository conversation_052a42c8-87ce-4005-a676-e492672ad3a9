import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';

export class MahankaraGroupings extends Base<Data.Hard.IMahankaraGroupings, Data.Result.IMahankaraGroupings> implements Required<Data.Hard.IMahankaraGroupings>
{
  public static generateId(index: number): string {
    return IdPrefixes.MAHANKARAGROUPINGS + index;
  }

  constructor( index: number, dataAccess: MahankaraGroupings['TDataAccess']) 
  {
    super(
    {
      hard: 
      {
        id: MahankaraGroupings.generateId(index),     
      },
    },
    dataAccess
    );
  }
  protected getInternalFetch() 
  {
    return {};
  }
  public get indexGrouping(): string
  {
    return this.hard.indexGrouping;
  }
  public set indexGrouping(value: string) 
  {
    this.hard.indexGrouping = value;
  }
  public get groupings(): string
  {
    return this.hard.groupings;
  }
  public set groupings(value: string) 
  {
    this.hard.groupings = value;
  }
  public get speechCategory(): string[]
  {
    return this.hard.speechCategory;
  }
  public set speechCategory(value: string[]) 
  {
    this.hard.speechCategory = value;
  }
  public get description(): string
  {
    return this.hard.description;
  }
  public set description(value: string) 
  {
    this.hard.description = value;
  }

}
