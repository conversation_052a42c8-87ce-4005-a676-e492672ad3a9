import { Subscription } from 'rxjs';
import { Component, EventEmitter, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { RpgService } from 'src/app/services/rpg.service';
import { RpgWordingSuggestionManagerComponent } from './components/rpg-wording-suggestion-manager/rpg-wording-suggestion-manager.component';
import { FILTER_SUFFIX_PATH } from 'src/lib/darkcloud/angular/dsadmin/constants/others';
import { Option, Speech } from 'src/app/lib/@bus-tier/models';
import { OptionService, PopupService, SpeechService } from 'src/app/services';
import { Alert } from 'src/lib/darkcloud';

interface WordDetection
{
  object: Speech|Option;
  word: string;
}

@Component({
  selector: 'app-rpg-manager',
  templateUrl: './rpg-manager.component.html'
})
export class RpgManagerComponent implements OnInit, OnD<PERSON>roy 
{
  public activeTab: string;
  public totalN: number;
  public nAnalyzedSuggestions: number;
  public nAnalyzedDetected: number;
  public nSuggested: number;
  private _subscription: Subscription;
  @ViewChild(RpgWordingSuggestionManagerComponent) suggestionsComponent: RpgWordingSuggestionManagerComponent;
  @Output() search: EventEmitter<string> = new EventEmitter();

  constructor(
    private _rpgService: RpgService, 
    private _speechService: SpeechService, 
    private _optionService: OptionService, 
    private _popupService: PopupService
    ) {}

  ngOnInit(): void 
  {
    this.totalN = this._rpgService.all.length;
    this.nAnalyzedDetected = this._rpgService.nAnalyzedDetected;
    this.nAnalyzedSuggestions = this._rpgService.nAnalyzedSuggestions;

    this._subscription = this._rpgService.allSubject.subscribe((v) => (this.totalN = v.length));
    this._subscription.add(this._rpgService.nAnalyzedSuggestionsSubject.subscribe((v) => (this.nAnalyzedSuggestions = v)));
    this._subscription.add(this._rpgService.nAnalyzedDetectedSubject.subscribe((v) => (this.nAnalyzedDetected = v)));

    const tab = localStorage.getItem(`tab-RPGManagerComponent.${FILTER_SUFFIX_PATH}`);
    this.activeTab = tab === 'null' || !tab ? 'unlock' : tab;
  }

  async acceptSelectedSuggestions() 
  {
    await this._rpgService.applyChangesInAcceptedSpeeches();
    await this.suggestionsComponent.acceptSelectedSuggestions();
    this.switchToTab('detections');
    await this.ngOnInit();
  }

  public detect() 
  {
    this._rpgService.detectAll();
  }

  public generateSuggestions() 
  {
    this._rpgService.generateSuggestions(true);
    this.switchToTab('detections');
  }

  public cancelSuggestions() 
  {
    this._rpgService.cancelSuggestionGeneration();
  }

  public switchToTab(tab: string) 
  {
    this.activeTab = tab;
    localStorage.setItem(`tab-RPGManagerComponent.${FILTER_SUFFIX_PATH}`,  this.activeTab);
  }

  public async rebuildSymbols()
  {
    let wordDetections: WordDetection[] = [];
    this._rpgService.detections[2].forEach(x => 
    {
      x.detected.forEach(detect => 
      {
        let wordDetection = 
        {
          word: x.word,
          object: detect
        };
        wordDetections.push(wordDetection);
      });
    });

    this._popupService.loadScreen();

    for (let index = 0; index < wordDetections.length; index++) 
    {
      await this.overrideSymbol(wordDetections[index]);
    }

    this._popupService.loadScreen();

    Alert.ShowSuccess('New Format Applied');
  }

  private async overrideSymbol(input: WordDetection)
  {
    let query = '((' + input.word + '))';
    if(!input.object.message.includes(query)) return;
    
    let result = '««' + input.word + '»»';

    input.object.message = input.object.message.replace(query, result);

    if(input.object.id.includes('spc'))
      await this._speechService.svcToModify(input.object as Speech);
    else if(input.object.id.includes('obj'))
      await this._optionService.svcToModify(input.object as Option);
  }

  ngOnDestroy() 
  {
    this._subscription.unsubscribe();
  }
}
