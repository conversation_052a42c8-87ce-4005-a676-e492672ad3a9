import { ChangeDetectorRef, Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Character, CollectibleRarityCodeBlocks } from 'src/app/lib/@bus-tier/models';
import { Button } from 'src/app/lib/@pres-tier/data';
import { TierService } from 'src/app/services';
import { CollectibleRarityCodeBlocksService } from 'src/app/services/collectibleRarityCodeBlocks.service';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Alert } from 'src/lib/darkcloud';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/SortableListComponent';

@Component({
  selector: 'app-upgrade-costs-code-blocks',
  templateUrl: './upgrade-costs-code-blocks.component.html',
  styleUrls: ['./upgrade-costs-code-blocks.component.scss'],

})

export class UpgradeCostsCodeBlocksComponent extends SortableListComponent<CollectibleRarityCodeBlocks> 
{
  tierList:string[] = [];
  characterRarityList:string[] = [];
  codeBlocks:CollectibleRarityCodeBlocks[] = [];
  collectibles:CollectibleRarityCodeBlocks[] = [];

  constructor(
    _activatedRoute: ActivatedRoute,
    protected _collectibleRarityCodeBlocksService: CollectibleRarityCodeBlocksService,
    _userSettingsService: UserSettingsService,
    private _tierListService: TierService,
    protected _languageService: LanguageService,
    protected _translationService: TranslationService,
    private ref: ChangeDetectorRef,
  ) 
  {
    super(_collectibleRarityCodeBlocksService, _activatedRoute, _userSettingsService, 'name');
  }
  public readonly excelButtonTemplate: Button.Templateable = 
  {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  protected override async lstInit() 
  {
    await this._collectibleRarityCodeBlocksService.toFinishLoading();
    await this._tierListService.toFinishLoading();
    this.collectibles = this._collectibleRarityCodeBlocksService.models;
    this.tierList = this._tierListService.fillRarityArrayDynimically('Code Block Rarity', this.tierList);
    this.characterRarityList = this._tierListService.fillRarityArrayDynimically('Character Rarity', this.characterRarityList).filter(x => x != 'Inferior');
    this.getListChacterRarity();
    this.sortTierListByRarity();
  }

  getListChacterRarity() 
  {  
  // Array auxiliar para definir na ordem
  const orderDesired = ["Elementar", "Comum", "Raro", "Épico", "Lendário"];  
  this.characterRarityList.sort((a, b) => orderDesired.indexOf(a) - orderDesired.indexOf(b));
  }

  sortTierListByRarity() {
    this.tierList.sort((a, b) => {
      const aIndex = this.characterRarityList.findIndex(rarity => a.includes(rarity));
      const bIndex = this.characterRarityList.findIndex(rarity => b.includes(rarity));
      return aIndex - bIndex;
    });
  }

  public downloadSceneryOrtography(character: Character) 
  {
    this._translationService.getCharacterOrtography(character, true);
  }

/*
  protected override lstAfterFetchList() 
  {
    if (this._collectibleRarityCodeBlocksService.models.length == 0) 
    {
      for (let l = 0; l <= 10; l++) 
        this._collectibleRarityCodeBlocksService.createNewCollectibleRarityCodeBlocks(l);

      this._collectibleRarityCodeBlocksService.toSave();
      this.lstFetchLists();
    }

    this.codeBlocks = this._collectibleRarityCodeBlocksService.models;
  }
  */

  async onExcelPaste(): Promise<void> {
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter(line => line);

    let collectibleRarityCodeBlocksFields: string[] = [];
    this._collectibleRarityCodeBlocksService.models = [];

    for (let i = 0; i < this.characterRarityList.length; i++) {
        collectibleRarityCodeBlocksFields.push(this.characterRarityList[i].toLowerCase() + 'CodeBlocksAmount');
    }

    for (let l = 0; l < lines.length; l++) {
        const line = lines[l];
        const cols = line.split(/\t/);     
   
        let collectibleRarityCodeBlocks = this._collectibleRarityCodeBlocksService.models.find((laboratory) =>
            laboratory.bossLevel === +cols[0].split(' ').join('').split('.').join('').replace(',', '.')
        );

        if (!collectibleRarityCodeBlocks) {
          const labLevel = +cols[0].split(' ').join('').split('.').join('').replace(',', '.');
          collectibleRarityCodeBlocks = this._collectibleRarityCodeBlocksService.createNewCollectibleRarityCodeBlocks(labLevel);     
        }

        for (let i = 0; i < collectibleRarityCodeBlocksFields.length; i++) {         
            if (cols[i + 1]?.trim()) {
                collectibleRarityCodeBlocks.hard[collectibleRarityCodeBlocksFields[i]] = +(cols[i + 1].split(' ')
                .join('').split('.').join('').replace(',', '.').replace('%', '')
                );
            } else {  
                collectibleRarityCodeBlocks.hard[collectibleRarityCodeBlocksFields[i]] = undefined;
            }
        }
        this._collectibleRarityCodeBlocksService.svcToModify(collectibleRarityCodeBlocks);
    }

    await this._collectibleRarityCodeBlocksService.toSave();
    Alert.ShowSuccess('Collectible Rarity CodeBlocks imported successfully!');
    await this._collectibleRarityCodeBlocksService.toFinishLoading();

    this.collectibles = this._collectibleRarityCodeBlocksService.models;
    this.ref.detectChanges();
}

  async changeBlockDrops(codeBlockDrop: CollectibleRarityCodeBlocks, value: string, fieldName:string)
  {
    codeBlockDrop.hard[fieldName] = value == ''? undefined : +value;
    await this._collectibleRarityCodeBlocksService.svcToModify(codeBlockDrop);
    await this._collectibleRarityCodeBlocksService.toSave();
  }

  //Create the collectible list and remove the lstIds. It to make the sorting.
  sortListByParameterOrder:number = 1;
  public override sortListByParameter(parameter: string): void 
  {
    this.sortListByParameterOrder *= -1;
    this.collectibles.sort((a,b)=>
    {
      if(!a.hard[parameter.toLowerCase()+'CodeBlocksAmount'] && b.hard[parameter.toLowerCase()+'CodeBlocksAmount']) return 1;
      if(a.hard[parameter.toLowerCase()+'CodeBlocksAmount'] && !b.hard[parameter.toLowerCase()+'CodeBlocksAmount']) return -1;
      if(![parameter.toLowerCase()+'CodeBlocksAmount'] && ![parameter.toLowerCase()+'CodeBlocksAmount']) return 0;

      return this.sortListByParameterOrder*+a.hard[parameter.toLowerCase()+'CodeBlocksAmount']- +b.hard[parameter.toLowerCase()+'CodeBlocksAmount'];
    });
  }


  displayErrors(array)
  {
    let count = array[0].split(/\t/);
    if(count.length < this.tierList.length)
    {
      Alert.showError("Copy the BOSS LEVEL column values too!");
      return true;
    }
    
    if(count[0] === "")
    {
      Alert.showError("You are probably copying a blank column!");
      return true;
    }

    return false;
  }
}
