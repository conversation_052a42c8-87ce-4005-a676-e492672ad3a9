import { Data } from "src/lib/darkcloud/angular/dsadmin";
import { IdPrefixes } from "src/lib/darkcloud/dialogue-system";
import { Base } from "./Base";
import { Primal } from "src/lib/darkcloud/angular/dsadmin/v9/data/hard";

export class PrimalModifier extends Base<Data.Hard.IPrimalModifier, Data.Result.IPrimalModifier> implements Required<Data.Hard.IPrimalModifier>
{
    static generateId(index: number): string
    {
        return IdPrefixes.PRIMALMODIFIER + index;
    }

    constructor(
      index: number,
      character:string,
      dataAccess: PrimalModifier['TDataAccess']) 
    {
      super(
          {
              hard: 
              {
                  id: PrimalModifier.generateId(index),
                  character
              },
          },
          dataAccess
      );
    }
   // primalModifier: Data.Hard.Primal[];
   
    public get primalModifier(): Primal[]
    {
      return this.hard.primalModifier;
    }

    public set primalModifier(value: Primal[])
    {
      this.hard.primalModifier = value;
    }

    public get character(): string
    {
      return this.hard.character;
    }

    public set character(value: string)
    {
      this.hard.character = value;
    }
}