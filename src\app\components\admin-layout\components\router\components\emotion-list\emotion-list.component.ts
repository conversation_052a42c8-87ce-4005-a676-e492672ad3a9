import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/SortableListComponent';
import { Sorting } from 'src/lib/darkcloud/angular/easy-mvc/Sorting';
import { Component } from '@angular/core';
import { Emotion } from 'src/app/lib/@bus-tier/models';
import { EmotionService } from 'src/app/services/emotion.service';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { ReviewService } from 'src/app/services/review.service';
import { Button } from 'src/app/lib/@pres-tier/data';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-emotion-list',
  templateUrl: './emotion-list.component.html',
})

export class EmotionListComponent extends SortableListComponent<Emotion> 
{
  constructor(
    _activatedRoute: ActivatedRoute,
    _emotionService: EmotionService,
    _userSettingsService: UserSettingsService,
    private _router: Router,
    private _reviewService: ReviewService,
    protected _languageService: LanguageService,
    protected _translationService: TranslationService
  ) 
  {
    super(_emotionService, _activatedRoute, _userSettingsService, 'name');
  }

  public readonly soundButtonTemplate: Button.Templateable = 
  {
    btnClass: Button.Klasses.BLUE,
    title: 'Go to Sound List',
    onClick: this.redirectToAudioList.bind(this),
    iconClass: 'pe-7s-volume1',
  };

  protected override specialSort(parameter: Sorting.Parameter) 
  {
    switch (parameter) 
    {
      case 'assigned':
        this._modelService.models.sort((a, b) =>
          this.srtLstOrder === 'ascending' ? this._reviewService.reviewResults[a.id].assignedAt.length >
              this._reviewService.reviewResults[b.id].assignedAt.length ? 1 : -1
            : this._reviewService.reviewResults[b.id].assignedAt.length >
              this._reviewService.reviewResults[a.id].assignedAt.length ? 1 : -1);
        break;
      default:
        this.defaultSort(parameter);
        break;
    }
  }

  updateColor(model, value, colorLabel: HTMLElement)
  {
    this.updateInformation(model, 'hex', value);
    colorLabel.style.backgroundColor = value;
  }


  public redirectToAudioList() 
  {
    this._router.navigate(['audios']);
  }
}
