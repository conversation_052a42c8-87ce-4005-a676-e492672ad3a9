<div class="row">
  <div class="col-md-4">
    <div class="report-table-wrapper">
      <h3>Receive Events - Total: {{this.receiveEventsRoadblockIds.length}}</h3>
      <table class="table report-table table-striped">
        <thead class="sticky" style="top: 0px;">
          <tr>
            <th style="text-align: right" class="th-clickable"
              (click)="sorting.sortCurrencyReceiveEvents(sorting.byAmount)">
              Amount <i></i>
            </th>
            <th class="th-clickable" (click)="sorting.sortCurrencyReceiveEvents(sorting.byItem)">Item <i></i></th>
            <th class="th-clickable" style="width: 296px !important;" (click)="sortById(this.receiveEvents)">Location
              <i></i>
            </th>
            <th class="custom-th" (click)="updateArray(receiveEventsRoadblockIds, receiveEvents)"> Progress
              <i class="pe-7s-info" style="left: 10px !important; top: 3px;" style="position: relative" placement='top'
                delay='250' ttWidth="auto" ttAlign="center" ttPadding="10px"
                tooltip="Show items inside a progress condition">
              </i>
            </th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let event of receiveEvents; let i = index">
            <tr [title]="event.id" class="tr-clickable"
              (click)="accessing.accessEvent(event, accessing.towardsDialogueEditor)">
              <td class="td-20px" style="text-align: right">{{ event.amount }}</td>
              <td class="td-auto rpg-color-{{+RPGType.CURRENCY}}" style="width: 130px !important;">{{ (event.itemId |
                item)?.name }}</td>
              <td class="td-100" style="width: 300px !important;">{{ ([event.id] | location) }}</td>
              <td class="td-20px" *ngIf="receiveEventsRoadblockIds[i]">
                <i style=" color:rgb(0, 0, 0); font-weight:900; font-size: 60px;" class="pe-7s-attention"></i>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
  <div class="col-md-4">
    <h3>Trade Events - Total: {{this.tradeEventsRoadblockIds.length}}</h3>
    <div class="report-table-wrapper">
      <table class="table report-table table-striped" style="height: 95% !important;">
        <thead class="sticky" style="top: 0px;">
          <tr>
            <th style="text-align: right" class="th-clickable"
              (click)="sorting.sortCurrencyTradeEvents(sorting.byAmount)">
              Amount <i></i></th>
            <th class="th-clickable" (click)="sorting.sortCurrencyTradeEvents(sorting.byItem)">Item <i></i></th>
            <th class="th-clickable" (click)="sortById(this.tradeEvents)">Location <i></i></th>
            <th class="custom-th" (click)="updateArray(tradeEventsRoadblockIds, tradeEvents)"> Progress
              <i class="pe-7s-info" style="left: 10px !important; top: 3px;" style="position: relative" placement='top'
                delay='250' ttWidth="auto" ttAlign="center" ttPadding="10px"
                tooltip="Show items inside a progress condition">
              </i>
            </th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let event of tradeEvents; let i = index">
            <tr [title]="event.id" class="tr-clickable"
              (click)="accessing.accessEvent(event, accessing.towardsDialogueEditor)">
              <td class="td-20px" style="text-align: right">{{ event.amount }}</td>
              <td class="td-auto rpg-color-{{+RPGType.CURRENCY}}">{{ (event.itemId | item)?.name }}</td>
              <td class="td-auto">{{ ([event.id] | location) }}</td>
              <td class="td-20px" *ngIf="tradeEventsRoadblockIds[i]"><i
                  style="color:rgb(0, 0, 0); font-weight:900; font-size: 60px;" class="pe-7s-attention"></i></td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
  <div class="col-md-4">
    <h3>Give Events - Total: {{this.giveEventsRoadblockIds.length}}</h3>
    <div class="report-table-wrapper">
      <table class="table report-table table-striped" style="height: 95% !important; margin-right: 0px;">
        <thead class="sticky" style="top: 0px;">
          <tr>
            <th style="text-align: right" class="th-clickable"
              (click)="sorting.sortCurrencyGiveEvents(sorting.byAmount)">
              Amount <i></i>
            </th>
            <th class="th-clickable" (click)="sorting.sortCurrencyGiveEvents(sorting.byItem)">Item <i></i></th>
            <th class="th-clickable" (click)="sortById(this.giveEvents)">Location <i></i></th>
            <th class="custom-th" (click)="updateArray(giveEventsRoadblockIds, giveEvents)"> Progress
              <i class="pe-7s-info" style="left: 10px !important; top: 3px;" style="position: relative" placement='top'
                delay='250' ttWidth="auto" ttAlign="center" ttPadding="10px"
                tooltip="Show items inside a progress condition">
              </i>
            </th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let event of giveEvents; let i = index">
            <tr [title]="event.id" class="tr-clickable"
              (click)="accessing.accessEvent(event, accessing.towardsDialogueEditor)">
              <td class="td-20px" style="text-align: right">{{ event.amount }}</td>
              <td class="td-auto rpg-color-{{+RPGType.CURRENCY}}">{{ (event.itemId | item)?.name }}</td>
              <td class="td-auto">{{ ([event.id] | location) }}</td>
              <td class="td-20px" *ngIf="giveEventsRoadblockIds[i]"><i
                  style="color:rgb(0, 0, 0); font-weight:900; font-size: 60px;" class="pe-7s-attention"></i></td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>