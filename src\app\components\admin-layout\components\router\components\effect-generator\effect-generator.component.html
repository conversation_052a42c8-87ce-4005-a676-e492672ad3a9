<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons [cardTitle]="listName" [cardDescription]="cardDescription"
          [rightButtonTemplates]="[addButtonTemplate]">
        </app-header-with-buttons>
        <app-header-search (inputKeyup)="lstOnChangeFilter($event)"
          (searchOptions)="lstOnChangeFilterOptions($event)"></app-header-search>
      </div>
    </div>
    <!--List-->
    <div class="card">
      <table class="table-bordered">
        <thead>
          <tr>
            <th>Index</th>
            <th class="th-clickable" (click)="sortListByParameter('id')">
              ID
            </th>
            <th class="th-clickable" (click)="sortListByParameter('name')">Name
            </th>
            <th style="width: 8%;">Actions</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let effect of listEffect; let i = index; trackBy: trackById">
            <tr id="{{ effect.id }}">
              <td class="td-sort gray">{{ i + 1 }}</td>
              <td class="td-id">{{ effect.id }}</td>
              <td class="td-id" style="text-align: left;">
                <input class="form-control form-title form-short" type="text" value="{{effect.name}}" #effectField
                  [ngStyle]="{'background-color': +effectField.value <= 0 ? '#404040' : '#ffffff'}"
                  (change)="lstOnChange(effect, 'name', effectField.value)" />
              </td>
              <td class="td-actions">
                <button class="btn btn-danger btn-fill btn-remove" (click)="removeEffect(i)">
                  <i class="pe-7s-close"></i>
                </button>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>