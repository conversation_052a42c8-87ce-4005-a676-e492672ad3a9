<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons [cardTitle]="listName" [cardDescription]="desc">
        </app-header-with-buttons>
      </div>
    </div>
    <!--List-->

    <div class="card">
      <table class="table table-list">
        <thead>
          <tr>
            <th class="th-clickable" (click)="sortListById()">ID</th>
            <th class="th-clickable" (click)="sortListByArea()">Trigger Level</th>
            <th class="th-clickable" (click)="sortListByType()">Main Story
              <i class="pe-7s-info" style="position: relative" placement='top' delay='250' ttWidth="auto"
                ttAlign="center" ttPadding="10px"
                tooltip="É uma história que surge a partir de um nível completamente novo, podendo ter um possível novo personagem que faz a chamada para a aventura/missão."></i>
            </th>
            <th class="th-clickable" (click)="sortListByType()">Side Story
              <i class="pe-7s-info" style="position: relative" placement='top' delay='250' ttWidth="auto"
                ttAlign="center" ttPadding="10px"
                tooltip="É uma história que se desenvolve a partir de um nível já descoberto, portanto de um personagem já conhecido, o qual fará a chamada para a aventura/missão."></i>
            </th>
            <th class="th-clickable" (click)="sortListByAlphabeticalOrder('name')">Title
              <div class="ball-circle"></div>
            </th>
            <th class="th-clickable" (click)="sortListByAlphabeticalOrder('description')">Description
              <div class="ball-circle"></div>
            </th>
            <th class="th-clickable" (click)="sortListByAlphabeticalOrder('note')">Note</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let story of storyPack">
            <tr>
              <td>{{ story.id }}</td>
              <td>{{ '['+ (story.area | area)?.hierarchyCode +']' +' '+(story.area | area)?.name}}</td>
              <td class="th-clickable" (click)="access(story)">
                <span *ngIf="story.type == 0">
                  {{story.level}}
                </span>
              </td>
              <td class="th-clickable" (click)="access(story)">
                <span *ngIf="story.type == 1">
                  {{story.level}}
                </span>
              </td>
              <td>
                <input placeholder=" " class="form-control form-short background-input-table-color" type="text" #name
                  value="{{story.name}}" (change)="nameChange(story, 'name', name.value)" />
              </td>
              <td>
                <textarea placeholder=" " style="width:100%;" class="background-input-table-color" type="text"
                  #description value="{{story.description}}"
                  (change)="descriptionChange(story, 'description', description.value)">
                  </textarea>
              </td>
              <td>
                <textarea placeholder=" " style="width:100%" class="background-input-table-color" type="text" #note
                  value="{{story.note}}" (change)="lstOnChange(story, 'note', note.value)">
                  </textarea>
              </td>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>

</div>