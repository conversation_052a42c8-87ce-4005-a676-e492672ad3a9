import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';


export class Passive
  extends Base<Data.Hard.IPassive, Data.Result.IPassive>
  implements Required<Data.Hard.IPassive>
{
  protected static generateId(index: number): string {
    return IdPrefixes.PASSIVE + index;
  }

  constructor(
      index: number,
      dataAccess: Passive['TDataAccess']
  )
  {
      super({hard: {id: Passive.generateId(index), order: index}}, dataAccess);
  }

  public get order(): number
  {
    return this.hard.order;
  }
  public set order(value: number)
  {
    this.hard.order = value;
  }

  public get bonusValue(): number
  {
    return this.hard.bonusValue;
  }
  public set bonusValue(value: number)
  {
    this.hard.bonusValue = value;
  }

  public get conditionValue(): number
  {
    return this.hard.conditionValue;
  }
  public set conditionValue(value: number)
  {
    this.hard.conditionValue = value;
  }

  public get conditionDuration(): number
  {
    return this.hard.conditionDuration;
  }
  public set conditionDuration(value: number)
  {
    this.hard.conditionDuration = value;
  }

  public get description(): string
  {
    return this.hard.description;
  }
  public set description(value: string)
  {
    this.hard.description = value;
  }

  public get bonusValueUnit(): string
  {
    return this.hard.bonusValueUnit;
  }
  public set bonusValueUnit(value: string)
  {
    this.hard.bonusValueUnit = value;
  }

  public get bonusType(): string
  {
    return this.hard.bonusType;
  }
  public set bonusType(value: string)
  {
    this.hard.bonusType = value;
  }

  public get conditionType(): string
  {
    return this.hard.conditionType;
  }
  public set conditionType(value: string)
  {
    this.hard.conditionType = value;
  }

  public get conditionComparator(): string
  {
    return this.hard.conditionComparator;
  }
  public set conditionComparator(value: string)
  {
    this.hard.conditionComparator = value;
  }

  public get conditionValueUnit(): string
  {
    return this.hard.conditionValueUnit;
  }
  public set conditionValueUnit(value: string)
  {
    this.hard.conditionValueUnit = value;
  }

}
