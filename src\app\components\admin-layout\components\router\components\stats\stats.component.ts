import { Component, OnInit } from '@angular/core';
import { FILTER_SUFFIX_PATH } from 'src/lib/darkcloud/angular/dsadmin/constants/others';

@Component({
  selector: 'app-stats-view',
  templateUrl: './stats.component.html',
})
export class StatsComponent implements OnInit {
  public activeTab: string;

  ngOnInit(): void {
    const tab = localStorage.getItem(
      `tab-StatsComponent${FILTER_SUFFIX_PATH}`
    );
    this.activeTab = tab === 'null' || !tab ? 'levelUpgrades' : tab;
  }

  public switchToTab(tab: string) {
    this.activeTab = tab;
    localStorage.setItem(
      `tab-StatsComponent${FILTER_SUFFIX_PATH}`,
      this.activeTab
    );
  }
}
