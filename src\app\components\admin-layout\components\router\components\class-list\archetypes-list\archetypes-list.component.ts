import { Component, OnD<PERSON>roy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ArchetypeList } from 'src/app/lib/@bus-tier/models/ArchetypeList';
import { But<PERSON> } from 'src/app/lib/@pres-tier/data';
import { UserSettingsService } from 'src/app/services';
import { ArchetypeListService } from 'src/app/services/archetypesList.service';
import { TranslationService } from 'src/app/services/translation.service';
import { Alert } from 'src/lib/darkcloud';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc';

@Component({
  selector: 'app-archetypes-list',
  templateUrl: './archetypes-list.component.html',
  styleUrls: ['./archetypes-list.component.scss']
})
export class ArchetypesListComponent extends SortableListComponent<ArchetypeList> implements OnDestroy{
  timeout:any
  public readonly addTagButton: Button.Templateable = {
    title: 'Add a new tag to the list',
    onClick: this.createTag.bind(this),
    iconClass: 'pe-7s-plus',
    btnClass: Button.Klasses.FILL_GREEN,
  };

  constructor(
    _activatedRoute: ActivatedRoute,
    private _archetypeListService: ArchetypeListService,
    _userSettingsService: UserSettingsService,
    private _router: Router,
    protected _translationService: TranslationService,   
  )
  {
    super(_archetypeListService, _activatedRoute, _userSettingsService, 'name');
  }

  override async  lstInit()
  {
    if(!(this.lstIds?.length <= 0))
      return;

    await this._archetypeListService.toFinishLoading();
    this.lstIds = this._archetypeListService.models.map(x => x.id);
  }

  public async createTag()
  {
    let newTag;
    try {
      newTag = await this._archetypeListService.svcPromptCreateNew();
    } catch (e) {
      Alert.showError("Archetype já existe!");
    }
    if(!newTag)
      return;

    this._archetypeListService.srvAdd(newTag);
    this._archetypeListService.modelIds.push(newTag.id)

    this.refreshList();
  }

  redirectToItemClasses()
  {
    this._router.navigate(['classes']);
  }

  updateColor(model, value, colorLabel: HTMLElement)
  {
    this.updateInformation(model, 'hex', value);
    colorLabel.style.backgroundColor = value;
    model.color = value;
    this._archetypeListService.svcToModify(model);
  }

  refreshList(scroll?: boolean)
  {
    let listProxy = this.lstIds;
    this.lstIds = [];
   this.timeout = setTimeout(() => {
      this.lstIds = listProxy
      this._archetypeListService.svcReviewAll()
    }, 1);
  }

  ngOnDestroy() {
    clearInterval(this.timeout)   
  }

}