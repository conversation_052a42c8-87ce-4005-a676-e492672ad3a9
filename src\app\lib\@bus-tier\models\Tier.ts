import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';

export class TierList  extends Base<Data.Hard.ITierList, Data.Result.ITierList> implements Required<Data.Hard.ITierList>
{
  public static generateId(index: number): string {
    return IdPrefixes.TIER + index;
  }
 
  public static getSubIdFrom(otherId: string): string {
    return Base.getSubId(this.generateId(0), otherId);
  }

  constructor( index: number, name:string, dataAccess: TierList['TDataAccess']) 
  {
    super(
    {
      hard: 
      {
        id: TierList.generateId(index),
        name,
      },
    },
    dataAccess
    );
  }
  protected getInternalFetch() 
  {
    return {};
  }

  public get color(): string 
  {
    return this.hard.color;
  }

  public set color(value: string) 
  {
    this.hard.color = value;
  }

  public get name(): string 
  {
    return this.hard.name;
  }

  public set name(value: string) 
  {
    this.hard.name = value;
  }

  public get acronym(): string 
  {
    return this.hard.acronym;
  }

  public set acronym(value: string) 
  {
    this.hard.acronym = value;
  }
  public get selectDrop(): string 
  {
    return this.hard.selectDrop;
  }

  public set selectDrop(value: string) 
  {
    this.hard.selectDrop = value;
  }   
  public get isReviewedName(): boolean {
    return this.hard.isReviewedName;
  }
  public set isReviewedName(value: boolean) {
    this.hard.isReviewedName = value;
  }  
  public get revisionCounterNameAI(): number {
    return this.hard.revisionCounterNameAI;
  }
  public set revisionCounterNameAI(value: number) {
    this.hard.revisionCounterNameAI = value;
  }
}
