import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';

export class Repetition extends Base<Data.Hard.IRepetition, Data.Result.IRepetition> implements Required<Data.Hard.IRepetition>
{
  public static generateId(index: number): string {
    return IdPrefixes.REPETITION + index;
  }

  constructor( index: number, dataAccess: Repetition['TDataAccess']) 
  {
    super(
    {
      hard: 
      {
        id: Repetition.generateId(index),     
      },
    },
    dataAccess
    );
  }
  protected getInternalFetch() 
  {
    return {};
  }
  public get positionNameRarity(): string[]
  {
    return this.hard.positionNameRarity;
  }
  public set positionNameRarity(value: string[]) 
  {
    this.hard.positionNameRarity = value;
  }

}
