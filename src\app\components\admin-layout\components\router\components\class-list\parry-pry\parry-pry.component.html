<div class="main-content">
  <div class="container-fluid">
      <div class="list-header-row update">
        <div class="card">
          <div style="display: flex; justify-content: space-between; padding-bottom: 10px;">
            <div class="card-header-content">
              <h3 class="title"><PERSON> (PRY)</h3>
              <p style="width:60vw;" class="category">{{ description === undefined ? 'Tabela vazia' : description}}</p>
            </div>
            <div style="display: flex; align-items: end; justify-content: end; align-items: center;">
              <ng-container>
                <!--BUTTON EXCEL-->
                <div id="button" style="position: absolute; margin-top: 60px;">
                  <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'" class="add-buttons"
                    [buttonTemplates]="[excelButtonTemplate]">
                  </app-button-group>
                </div>
              </ng-container>
            </div>
          </div>
        </div>
      </div>

          <!-- <PERSON><PERSON><PERSON> Ranges -->
        <div class="card-body">
          <!-- Container com scroll horizontal -->
          <div class="table-responsive horizontal-scroll">
            <table class="table table-bordered">
            <thead>
              <!-- Cabeçalho Superior -->
              <tr>
                <th rowspan="2">Index</th>
                <th rowspan="2" (click)="sortByClassName()" style="cursor: pointer; user-select: none;" title="Click to sort by class name">          
                    CLASS       
                </th>
                <th [attr.colspan]="rarities.length">PRY</th>
              </tr>
              <!-- Subcabeçalho -->
              <tr>
                <th *ngFor="let rarity of rarities; let i = index;"  [style.background-color]="(rarity.name | tierColor: 'Character Rarity')+ '!important'">{{ rarity.name }}</th>  
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let parry of listParryPry; let i = index;" [id]="parry.id">
                <!-- Index -->
                <td class="gray" style="width: 50px !important;">{{ i + 1 }}</td>

                <td class="td-notes backtable" style="padding-left: 5px; width: 150px !important; background-color: gray !important; color: white !important;">
                  {{ parry.className }}
                </td>
                <td class="td-notes backtable" *ngFor="let rarity of rarities" style="width: 120px !important;">
                  {{ getRarityValue(parry, rarity.name) }}
                </td>
              </tr>
            </tbody>
            </table>
          </div>

          <!-- Mensagem quando não há dados -->
          <div *ngIf="listParryPry.length === 0" class="card" style="text-align: center; padding: 20px;">
            <h3>Empty list. Use Excel paste to import data.</h3>
          </div>
        </div>

   </div>
 </div>



