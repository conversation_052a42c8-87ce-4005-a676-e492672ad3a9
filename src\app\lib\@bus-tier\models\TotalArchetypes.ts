import { Data } from "src/lib/darkcloud/angular/dsadmin";
import { IdPrefixes } from "src/lib/darkcloud/dialogue-system";
import { IListUnique } from "../../../../lib/darkcloud/angular/dsadmin/v9/data/hard/IUniqueCharactere";
import { Base } from "./Base";
import { IListTotalUnique } from "src/lib/darkcloud/angular/dsadmin/v9/data/hard";

export class TotalArchetypes extends Base<Data.Hard.ITotalArchetype, Data.Result.ITotalArcheypes> implements Required<Data.Hard.ITotalArchetype>
{
    static generateId(index: number): string
    {
        return IdPrefixes.TOTALARCHETYPES + index;
    }

    constructor(
      index: number,
      dataAccess: TotalArchetypes['TDataAccess']) 
    {
      super(
          {
              hard: 
              {
                  id: TotalArchetypes.generateId(index),
              },
          },
          dataAccess
      );
    }

    protected getInternalFetch() {
    return {};
   }
    public get listCicleLevel(): IListTotalUnique[]
    {
      return this.hard.listCicleLevel;
    }
    public set listCicleLevel(value: IListTotalUnique[])
    {
      this.hard.listCicleLevel = value;
    }
    public get idArchetype(): string
    {
      return this.hard.idArchetype;
    }
    public set idArchetype(value: string)
    {
      this.hard.idArchetype = value;
    }

    public get nameArchetype(): string
    {
      return this.hard.nameArchetype;
    }
    public set nameArchetype(value: string)
    {
      this.hard.nameArchetype = value;
    }
    public get total_name(): string
    {
      return this.hard.total_name;
    }
    public set total_name(value: string)
    {
      this.hard.total_name = value;
    }
    public get total_column(): number[]
    {
      return this.hard.total_column;
    }
    public set total_column(value: number[])
    {
      this.hard.total_column = value;
    }    
    public get total_line(): number
    {
      return this.hard.total_line;
    }
    public set total_line(value: number)
    {
      this.hard.total_line = value;
    }



}