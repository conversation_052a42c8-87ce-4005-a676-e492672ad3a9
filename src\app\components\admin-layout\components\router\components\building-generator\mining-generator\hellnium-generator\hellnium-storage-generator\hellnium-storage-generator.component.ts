import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/SortableListComponent';
import { Component } from '@angular/core';
import { Character,HellniumStorage } from 'src/app/lib/@bus-tier/models';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Button } from 'src/app/lib/@pres-tier/data';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { HellniumStorageService } from 'src/app/services/hellnium-storage.service';
import { Alert } from 'src/lib/darkcloud';
import { SpinnerService } from './../../../../../../../../../spinner/spinner.service';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-hellnium-storage-generator',
  templateUrl: './hellnium-storage-generator.component.html',
})
/**
 * Displays and edits emotion data as a list
 */
export class HellniumStorageGeneratorComponent extends SortableListComponent<HellniumStorage> {
  constructor(
    private spinnerService: SpinnerService,
    _activatedRoute: ActivatedRoute,
    protected _hellniumStorageService: HellniumStorageService,
    _userSettingsService: UserSettingsService,
    protected _languageService: LanguageService,
    protected _translationService: TranslationService,
  ) {
    super(_hellniumStorageService, _activatedRoute, _userSettingsService, 'name');
  }

  public readonly excelButtonTemplate: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  public downloadSceneryOrtography(character: Character)
  {
    this._translationService.getCharacterOrtography(character, true);
  }

  protected override lstInit()
  {

  }
  description = ""
  protected override lstAfterFetchList()
  {
    let soulsTypeCAmount = []
    this._hellniumStorageService.models;
    if(this._hellniumStorageService.models.filter(model => model.type === "A").length === 0)
    {
      for(let l = 1; l <= 20; l++)
      {
        this._hellniumStorageService.createNewHellniumStorage(l, "A");
      }
      this._hellniumStorageService.toSave();
      this.lstFetchLists();
    }

       //remove empty element that just has lablevel == 0.
       this._hellniumStorageService.models.find(blueprint => 
        {
          if(blueprint.hellniumLevel === 0)          
          this._hellniumStorageService.svcToRemove(blueprint.id)
        })
    soulsTypeCAmount = this._hellniumStorageService.models.filter(model => model.type === "A")
        this.description = `Showing ${soulsTypeCAmount.length} results`;
  }

  async onExcelPaste(): Promise<void>
  {
    this.spinnerService.setState(true)
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter(line => line);

   if(this.DisplayErrors(lines)) return


    for(let l = 0; l < lines.length; l++)
    {
      let line = lines[l];
      let cols = line.split(/\t/);
         

      let hellniumStorage = this._hellniumStorageService.models.find(ts => ts.hellniumLevel === +(cols[0].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.'))&& ts.type === "A");
      if(!hellniumStorage)
      {
        hellniumStorage = this._hellniumStorageService.createNewHellniumStorage(+(cols[0].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.')), "A");
      }
      

      if(cols[1]?.trim())
      {
        hellniumStorage.souls = +(cols[1].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.'));
      }
      else
      {
        hellniumStorage.souls = undefined;
      }
      if(cols[2]?.trim())
      {
        hellniumStorage.time = +(cols[2].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.'));
      }
      else
      {
        hellniumStorage.time = undefined;
      }
      if(cols[3]?.trim())
      {
        hellniumStorage.rubies = +(cols[3].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.'));
      }
      else
      {
        hellniumStorage.rubies = undefined;
      }
      if(cols[4]?.trim())
      {
        hellniumStorage.storage = +(cols[4].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.'));
      }
      else
      {
        hellniumStorage.storage = undefined;
      }

      await  this._hellniumStorageService.svcToModify(hellniumStorage);
      await  this._hellniumStorageService.toSave();
      Alert.ShowSuccess('Hellnium Storage imported successfully!');

      this.lstFetchLists();
    }
    this.spinnerService.setState(false)

  }

  DisplayErrors(array)
  {
    let count = array[0].split(/\t/)
    if(count.length < 5)
    {
      Alert.showError("Copy the HELLNIUM LEVEL column values too!")
      this.spinnerService.setState(false)
      return true
    }
    
    if(count[0] === "")
    {
      Alert.showError("You are probably copying a blank column!")
      this.spinnerService.setState(false)
      return true
    }

    return false
  }

}
