import { Component, EventEmitter, Output } from '@angular/core';
import { Item } from 'src/app/lib/@bus-tier/models';
import { ItemService, LevelService, ParticleService, UserSettingsService } from 'src/app/services';
import { TranslationCheckService } from 'src/app/services/translation-check.service';
import { TranslationService } from 'src/app/services/translation.service';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';
import { LanguageService } from 'src/app/services/language.service';
import { CustomService } from 'src/app/services/custom.service';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';
import { ItemClassService } from 'src/app/services/item-class.service';
import { Button } from 'src/app/lib/@pres-tier/data';
import { Particle } from 'src/app/lib/@bus-tier/models/Particle';
import { ActivatedRoute, Router } from '@angular/router';


@Component({
  selector: 'app-particle-item-selection',
  templateUrl: './particle-item-selection.component.html',
})
export class ParticleItemSelectionComponent  extends TranslatableListComponent<Item>{

  constructor(
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    private _itemService: ItemService,
    private _itemClassService: ItemClassService,
    private _particleService: ParticleService,
    private _levelService: LevelService,
    private _translationCheckService: TranslationCheckService,
    protected override _translationService: TranslationService,
    protected override _languageService: LanguageService,
    protected _customService: CustomService,
    private _router: Router,

  ) {
    super(_itemService, _activatedRoute, _userSettingsService, 'name', _translationService, _languageService);
  }

  @Output() itemSelected: EventEmitter<string> = new EventEmitter();
  isParticleListOpen = false;

  listDescription = "";
  itemList: Item[] = [];
  custom: Custom;
 particleList: Particle[] = []


 override async lstInit()
  {
    await this._particleService.toFinishLoading()
    this.custom = await this._customService.svcGetInstance();
    this.itemList = [];

    this.custom.particleClassItem?.forEach(itemClassId => {
      let itemClass = this._itemClassService.svcFindById(itemClassId);
      itemClass.itemIds.forEach(itemId => {
        let item = this._itemService.svcFindById(itemId);
        this.itemList.push(item);
      });
    });
    this.particleList = this._particleService.models
    this.listDescription = "Showing " + this.itemList.length + " results";
  }


public readonly addAreaTemplate: Button.Templateable = {
  title: 'See all particles status',
  onClick: this.addList.bind(this),
  iconClass: 'pe-7s-note2',
  btnClass: Button.Klasses.FILL_BLUE,
};

  sortIdOrder = -1;
  sortListById()
  {
    this.sortIdOrder *= -1;
    this.itemList.sort((a, b) => {
      return this.sortIdOrder * a.id.localeCompare(b.id);
    });
  }

  changeIsParticleListOpen(){
    this.isParticleListOpen = false
  }

  addList()
  {
    this.isParticleListOpen = true;   
  }

   public async downloadMapsOrtography(item: Item) {
    let particle = this._particleService.models.find(w => w.itemId === item.id)
    item.battleDescription = particle.description;
    await this._itemService.svcToModify(item)
    await this._itemService.toSave()
    this._translationService.getParticlesOrtography(particle, true);
  }

  
  async selectWeapon(item: Item, language, id)
  {
    this._translationService.toFinishLoading()
    let result
    if(language == "PT-BR" || !language)
      {
          result = this._translationService.getDataEntry(language, id);
      }

      let particle: Particle;
    await this._particleService.toFinishLoading()
    this._particleService.models.find(w => {
      if(w.itemId === item.id)
      {
        return particle = w;
      }
      return null
    })

    if(result?.battleDescription)
      particle.description = result.battleDescription;
    
    await this._particleService.svcToModify(particle)
    await this._particleService.toSave()

    this._customService.setCustomField(item?.id, 'selectedParticleId');
    this._router.navigate(['particleInformation']);
  }

  
  sortNameOrder = -1;
  override sortListByName()
  {
    this.sortNameOrder *= -1;
    this.itemList.sort((a, b) => {
      return this.sortNameOrder * a.name.localeCompare(b.name);
    });
  }

  sortWeaponOrder = -1;
  sortListByWeapon()
  {
    this.sortWeaponOrder *= -1;
    this.itemList.sort((a, b) => {
      let aCount = 0;
      let bCount = 0;
      if(this.checkIfParticleIsCompleted(a, 1) != "rgb(128, 128, 128)") aCount++;
      if(this.checkIfParticleIsCompleted(b, 1) != "rgb(128, 128, 128)") bCount++;
      return this.sortWeaponOrder * (aCount - bCount);
    });
  }

  checkIfParticleIsCompleted(item: Item, phase: number)
  {
    if(phase == 1)
    {
      let particle = this._particleService.models.find(w => w.itemId == item?.id);
      if(!particle) return "rgb(128, 128, 128)";
      if(particle.description || particle.charactersId?.length > 0 || particle.classesId?.length > 0 || particle.shake ||
        particle.hit || particle.split || particle.atk || particle.hcLevel)
      {
        return "rgb(29, 199, 234)";
      }
    }

    return "rgb(128, 128, 128)";
  }
}
