<div class="popup-simulator" *ngIf="popupSimulatorOpen">
  <button (click)="closeSimulatorPopup()"><i class="pe-7s-close"></i></button>
  <dialogue-simulator [dialogue]="dialogue"></dialogue-simulator>
</div>
<div class="popup-tree" *ngIf="popupOpen">
  <button (click)="closeTreePopup()"><i class="pe-7s-close"></i></button>
  <dialogue-tree-view [dialogue]="dialogue"></dialogue-tree-view>
</div>
<div id="page" class="main-content">
  <div #scrollableDiv class="container-fluid">

    <!-- Start: Header with Buttons -->
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons class="container-fluid" [isBackButtonEnabled]="true"
          [cardTitle]="([dialogue.id] | location)[0]" cardDescription="ID: {{ dialogue.id }}"
          (cardBackButtonClick)="goBack()"
          [rightButtonTemplates]="[clipboardButtonTemplate,flowTreeButtonTemplate,simulatorPopupButtonTemplate]">
        </app-header-with-buttons>
        <br />
        <div style="display: flex; flex-direction: row; justify-content: space-between;">
          <div class="input-custom-box">
            <input type="text" [(ngModel)]="searchQuery" (input)="filterContent()" placeholder="Search"
              (keydown.enter)="scrollToNextMatch()" />
            <span style="margin-right: 5px; color: #555;">{{_searchOnPageService.currentHighlightedIndex + 1}}/{{
              searchQuery !== '' ? _searchOnPageService.highlightedElements.length : 0}}</span>
            <div style="display: flex; border-left: 1px solid #999;">
              <button class="i-btn" (click)="scrollToNextMatch()" style="margin-left: 5px;">
                <i class="pe-7s-angle-down"></i>
              </button>
              <button class="i-btn" (click)="scrollToPreviousMatch()">
                <i class="pe-7s-angle-up"></i>
              </button>
              <button class="i-btn" title="Clear" (click)="clearSearchQuery()" style="margin-right: 5px;">
                <i class="pe-7s-close-circle" style="font-size: 26px;"></i>
              </button>
            </div>
          </div>
        </div>
        <app-header-with-buttons [rightButtonTemplates]="headerRightButtonTemplates"></app-header-with-buttons>
      </div>
    </div>
    <!-- End: Header with Buttons -->


    <div>
      <div class="row">
        <div class="col-md-12">

          <ng-container *ngFor="let box of lazyIds | boxes; let i = index; trackBy: identify">
                  
            <ng-container *ngIf="box">
              <ng-container [ngSwitch]="box | typeName">
                
            <!--Dialog Box: Dilemma Box-->
              <ng-container *ngSwitchCase="'Dilemma Box'">
                <app-dilemma-box [dialogue]="dialogue" [dilBox]="box?.id | dilemmaBox"
                  [preloadedSpeakers]="preloadedSpeakers" [index]="i" [preloadedMissionsOfArea]="preloadedMissionsOfArea"
                  (refresh)="forceRefresh()" [language]="language" (click)="refreshList()">
                </app-dilemma-box>
              </ng-container>

            <!--Dialog Box: Choice Box-->
                <ng-container *ngSwitchCase="'Choice Box'">
                  <app-choice-box [dialogue]="dialogue" [optionBox]="box?.id | optionBox" [index]="i"
                    [preloadedSpeakers]="preloadedSpeakers" [preloadedMissionsOfArea]="preloadedMissionsOfArea"
                    (refresh)="forceRefresh()" [language]="language" (click)="refreshList()">
                  </app-choice-box>
                </ng-container>

            <!--Dialog Box: Investigation Box-->
                <ng-container *ngSwitchCase="'Investigation Box'">
                  <app-investigation [dialogue]="dialogue" [optionBox]="box?.id | optionBox" [index]="i"
                    [preloadedSpeakers]="preloadedSpeakers" [preloadedMissionsOfArea]="preloadedMissionsOfArea"
                    (refresh)="forceRefresh()" [language]="language" (click)="refreshList()">
                  </app-investigation>
                </ng-container>

           <!--Dialog Box: Story Box-->
              <ng-container *ngSwitchCase="'Story Box'">
                <app-story-box [dialogue]="dialogue" [storyBox]="box?.id | storyBox" [index]="i"
                  [preloadedSpeakers]="preloadedSpeakers" [preloadedMissionsOfArea]="preloadedMissionsOfArea"
                  (refresh)="forceRefresh()" [language]="language" (click)="refreshList()">
                </app-story-box>
              </ng-container>

              </ng-container>
            </ng-container>


            <!--Dialog Box: Story Box
            <ng-container *ngIf="isStgoryBox || shouldLoadSTORYBOX(box)">
              <app-story-box [dialogue]="dialogue" [storyBox]="box?.id | storyBox" [index]="i"
                [preloadedSpeakers]="preloadedSpeakers" [preloadedMissionsOfArea]="preloadedMissionsOfArea"
                (refresh)="forceRefresh()" [language]="language" (click)="refreshList()">
              </app-story-box>
            </ng-container>

-->
          </ng-container>



        </div>
      </div>
    </div>
  </div>
</div>