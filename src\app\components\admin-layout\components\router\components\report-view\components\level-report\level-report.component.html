<div class="row">
  <div class="col-md-6">
    <div class="report-table-wrapper">
      <p class="category">Unlocked Levels</p>
      <table class="table report-table table-striped">
        <thead class="sticky" style="top: 0px">
          <tr>
            <th></th>
            <th class="th-clickable" (click)="sorting.sortUnlockMarkers(sorting.byLocation)">Unlocked by</th>
            <th></th>
            <th></th>
            <th class="th-clickable" (click)="sorting.sortUnlockMarkers(sorting.byLevelLocation)">Level to be unlocked
            </th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let marker of preloadedData.unlockMarkers; let i = index">
            <tr>
              <td class="td-20px">
                <div class="circle" [ngStyle]="{
                    'background-color': marker.id | level | typeColor
                  }"></div>
              </td>
              <td class="td-auto td-clickable"
                (click)="accessing.accessMarker(marker, accessing.towardsDialogueEditor)">
                {{ [marker.id] | location }}
              </td>
              <td class="td-auto">
                <i style="position: relative" placement='top' delay='250' ttWidth="auto" ttAlign="left" ttPadding="10px"
                  tooltip="Unlocked" class="pe-7s-key marker pull-right"> </i>
              </td>
              <td class="td-20px">
                <div class="circle" [ngStyle]="{
                    'background-color':
                      marker.levelId
                      | level
                      | typeColor
                  }"></div>
              </td>
              <td class="td-auto td-clickable" (click)="accessing.accessMarker(marker, accessing.towardsLevelList)">
                {{ [marker.levelId] | location }}
              </td>
              <td class="td-auto">
                <i style="position: relative" placement='top' delay='250' ttWidth="auto" ttAlign="left" ttPadding="10px"
                  tooltip="Unlocked" class="pe-7s-unlock success pull-right"></i>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>

  <div class="col-md-6">
    <div class="report-table-wrapper">
      <p class="category">Releases</p>
      <table class="table report-table table-striped">
        <thead class="sticky" style="top: 0px">
          <tr>
            <th></th>
            <th class="th-clickable" (click)="sorting.sortReleaseMarkers(sorting.byLocation)">Released by</th>
            <th></th>
            <th></th>
            <th class="th-clickable" (click)="sorting.sortReleaseMarkers(sorting.byLevelLocation)">Dialogue Level to be
              released</th>
            <th></th>
            <th class="th-clickable" (click)="sorting.sortReleaseMarkers(sorting.byPin)">Pins</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="
              let marker of preloadedData.releaseMarkers;
              let i = index
            ">
            <tr>
              <td class="td-20px">
                <div class="circle" [ngStyle]="{
                    'background-color':
                      marker.id | level | typeColor
                  }"></div>
              </td>
              <td class="td-auto td-clickable"
                (click)="accessing.accessMarker(marker, accessing.towardsDialogueEditor)">
                {{ [marker.id] | location }}
              </td>
              <td class="td-auto">
                <i style="position: relative" placement='top' delay='250' ttWidth="auto" ttAlign="left" ttPadding="10px"
                  tooltip="Unlocked" class="pe-7s-key marker pull-right"> </i>
              </td>
              <td class="td-20px">
                <div class="circle" [ngStyle]="{
                    'background-color':
                      marker.levelId
                      | level
                      | typeColor
                  }"></div>
              </td>
              <td class="td-auto td-clickable" (click)="accessing.accessMarker(marker, accessing.towardsLevelList)">
                {{
                [marker.levelId] | location
                }}
              </td>
              <td class="td-auto">
                <i style="position: relative" placement='top' delay='250' ttWidth="auto" ttAlign="left" ttPadding="10px"
                  tooltip="Unlocked" class="pe-7s-unlock success pull-right"></i>
              </td>
              <td class="td-20px">
                <i *ngIf="marker.pin" class="btn-icon pe-7s-pin">
                </i>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>