<div class="card list-header m-header" >
  <div class="header" style="display:flex; flex-direction:row; justify-content: space-between;">
    <div style="display:flex; flex-direction:row; justify-content: space-between; gap: 5px;">
      <button class="{{activeTab === 'itemClass' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
        (click)="switchToTab('itemClass')">
        Item Classes
      </button>
      <button class="{{activeTab === 'tabs' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
        (click)="switchToTab('tabs')">
        Tabs
      </button>
    </div>
    <div style="display:flex; flex-direction:column;">
      <p class="category" style="align-self:center">Gate XP - Item class</p>
      <select (change)="GateXPItemClassChanged($event)">
        <option *ngFor="let itemClass of itemClasses" [value]="itemClass?.id"
          [selected]="itemClass?.id == custom.gateXPclassItem">{{ itemClass?.name }}
        </option>
      </select>
    </div>
  </div>
</div>


<div class="main-content" *ngIf="activeTab === 'itemClass'">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons [cardTitle]="listName" [cardDescription]="cardDescription"
          [rightButtonTemplates]="[tagsButton, addButtonTemplate]">
        </app-header-with-buttons>
        <app-header-search (inputKeyup)="filterList($event)"
          (searchOptions)="lstOnChangeFilterOptions($event)"></app-header-search>
      </div>
    </div>

    <div class="card">
      <table class="table table-list">
        <thead class="sticky">
          <tr>
            <th class="th-clickable" (click)="sortListByParameter('id')">
              ID
            </th>
            <th class="th-clickable" (click)="sortListByReviews()">
              Reviews
            </th>
            <th class="th-clickable" (click)="sortListByParameter('name')">
              Name and Description
              <div class="ball-circle"></div>
            </th>
            <th class="th-clickable" (click)="sortListByParameter('notes')">
              Notes
            </th>
            <th class="th-clickable" (click)="sortByBoundItemsCount()">
              Bound Items
            </th>
            <th class="th-clickable" (click)="sortByTabs()">
              Tab
            </th>
            <th>
              Actions
            </th>
        </thead>
        <tbody>
          <ng-container
            *ngFor="let itemClass of itemClasses | filteredItemClass : queryValue : lstSearchOptions; let i = index; trackBy: trackById;">
            <tr>
              <td class="td-id">
                {{ itemClass?.id }}
              </td>
              <td>
                <ng-container *ngIf="(itemClass | reviewMessages).length > 0; else assigned">
                  <i tooltip="{{constructToolTipMessage(itemClass, (itemClass | reviewMessages))}}"
                    class="pe-7s-attention warning" placement="right" delay="100">
                  </i>
                  <div>{{ (itemClass | reviewMessages).length }}</div>
                </ng-container>
                <ng-template #assigned>
                  <i class="pe-7s-check success"></i>
                </ng-template>
              </td>
              <td class="td-notes">
                <input type="text" class="form-control form-short"
                  value="{{ (itemClass | translation : lstLanguage: itemClass?.id : 'name') }}" #name
                  (change)="changeItemclassName(itemClass, name.value)">
                <textarea name="description" id="description" class="form-control"
                  style="width: 100%;border-color: #b9b9b9  !important;"
                  value="{{ (itemClass | translation : lstLanguage: itemClass?.id : 'description') }}" #description
                  placeholder="Description..." (change)="changeDescription(itemClass, 'description', description.value)">
                      </textarea>
              </td>
              <td class="td-notes">
                <textarea class="form-control borderless"
                  value="{{ (itemClass | translation : lstLanguage : itemClass.id : 'notes') }}" #notes
                  (change)="lstOnChange(itemClass, 'notes', notes.value)">
                      </textarea>
              </td>
              <td>
                <button class="btn btn-success btn-fill btn-remove" (click)="routeToBoundItems(itemClass)"> Aqui
                  <p>{{ itemClass?.itemIds?.length >= 0 ? itemClass?.itemIds.length : 0 }}</p>
                </button>
              </td>
              <td class="td-auto">
                <button class="btn btn-primary btn-fill" *ngFor="let tab of itemClass?.tagIds; let i = index"
                  (click)="changeTab(itemClass, '', i)" [ngStyle]="{
                          'background-color': (((tab | tab) | async) | information: {hex: '#828282'})?.hex,
                          'border-color': (((tab | tab) | async) | information: {hex: '#828282'})?.hex
                          }">{{ ((tab | tab) | async)?.name }}
                </button>
                <button class="btn btn-success btn-fill" (click)="addTabWithPopup(itemClass)">
                  <i class="pe-7s-plus"></i>
                </button>
              </td>
              <td class="td-actions td-small">
                <button class="btn btn-danger btn-fill btn-remove" (click)="removeItemClass(itemClass)">
                  <i class="pe-7s-close"></i>
                </button>
                <button class="btn btn-gray btn-fill translation-button"
                  (click)="downloadItemClassOrtography(itemClass)">
                  <div class="mat-translate"></div>
                </button>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>


<app-tab-list *ngIf="activeTab === 'tabs'"></app-tab-list>