import { Component, OnInit } from '@angular/core';
import { Option } from 'src/app/lib/@bus-tier/models';
import { SortableListComponent } from '../../../../../../../../../lib/darkcloud/angular/easy-mvc';
import { comparable } from '../../../../../../../../../lib/others';
import { OptionService, UserSettingsService } from '../../../../../../../../services';
import { RoadBlockService } from '../../../../../../../../services/road-block.service';
import { SortingService } from '../../../../../../../../services/sorting.service';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-difficulty-class-reports',
  templateUrl: './difficulty-class-reports.component.html',
  styleUrls: ['./difficulty-class-reports.component.scss']
})
export class DifficultyClassReportsComponent extends SortableListComponent<Option> implements OnInit{

  optionBoxes: Option[] = [];
  reverseLocation: boolean = false;

  constructor(private _optionService: OptionService,
    private _roadblockService: RoadBlockService,
    public readonly router: Router,
    private _sorting : SortingService,
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
  ) {
    super(_optionService, _activatedRoute, _userSettingsService, 'name');
  }

  override async ngOnInit() {

    this._optionService.toFinishLoading();
    this.optionBoxes = this._optionService.models.filter((option) => option.resultDC !== undefined && option.investigaDifficulty === undefined );
    console.log('OptionBoxes', this.optionBoxes);
  }

  access(id: string) {
    let levelId = this._roadblockService.getLevelId(id);
    let dialogueId = this._roadblockService.getDialogueId(id);

    if(id.split(".")[0].includes("ML"))
    {
      this.router.navigate([
        'microloops/' + levelId +
        '/dialogues/' + dialogueId
      ],
      {fragment: id});
    }
    else
    {
      this.router.navigate([
        'levels/' + levelId +
        '/dialogues/' + dialogueId
      ],
      {fragment: id});
    }
  }
  sortArrayByType(array: Option[]) {	
  }	

  sortArrayByLocation(array: Option[]){  
   array.sort((a, b) => {	
      return this._sorting.sortArrayByLevelId(this.reverseLocation, a.id, b.id)	
    });	
    this.reverseLocation = !this.reverseLocation;	
  }
    
  private isSortedByMessage: boolean = false;

  sortArrayByChoice(array: Option[]) {
    array.sort((a, b) => {
      let messageA = comparable(a.message);
      let messageB = comparable(b.message);

      if (!messageA || !messageB) return 0;

      if (messageA > messageB) return this.isSortedByMessage ? 1 : -1;
      else if (messageB > messageA) return this.isSortedByMessage ? -1 : 1;

      return 0;
    });

    this.isSortedByMessage = !this.isSortedByMessage; 
  }

  private isAscendingOrder: boolean = true;
  sortArrayByDifficult(array: Option[]){

    array.sort((a, b) => {
      const aValue = a.resultDC ?? 0;
      const bValue = b.resultDC ?? 0;

      if (this.isAscendingOrder) {
          return aValue - bValue; // Ordem crescente
      } else {
          return bValue - aValue; // Ordem decrescente
      }
  });
  this.isAscendingOrder = !this.isAscendingOrder;

  }

  /**
   * Ordena o array por Attribute (choiceAtributte) em ordem alfabética
   * Alterna entre ordem crescente (A-Z) e decrescente (Z-A)
   */
  private isAttributeAscending: boolean = true;
  sortArrayByAttribute(array: Option[]): void {
    array.sort((a, b) => {
      const attributeA = (a.choiceAtributte || '').toLowerCase();
      const attributeB = (b.choiceAtributte || '').toLowerCase();

      if (this.isAttributeAscending) {
        return attributeA.localeCompare(attributeB); // A-Z
      } else {
        return attributeB.localeCompare(attributeA); // Z-A
      }
    });

    this.isAttributeAscending = !this.isAttributeAscending;
  }

  /**
   * Ordena o array por ModMoon (isModMoon)
   * Primeiro clique: On primeiro (true), depois Off (false)
   * Segundo clique: Off primeiro (false), depois On (true)
   */
  private isModMoonOnFirst: boolean = true;
  sortArrayByModMoon(array: Option[]): void {
    array.sort((a, b) => {
      const valueA = a.isModMoon ?? false;
      const valueB = b.isModMoon ?? false;

      if (this.isModMoonOnFirst) {
        // On primeiro: true (1) vem antes de false (0)
        return Number(valueB) - Number(valueA);
      } else {
        // Off primeiro: false (0) vem antes de true (1)
        return Number(valueA) - Number(valueB);
      }
    });

    this.isModMoonOnFirst = !this.isModMoonOnFirst;
  }
}

