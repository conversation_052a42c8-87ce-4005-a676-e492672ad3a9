import { Component, OnInit } from '@angular/core';
import { SpeechService } from 'src/app/services/speech.service';
import { Speech, Option, Level, Dialogue } from 'src/app/lib/@bus-tier/models';
import { OptionService } from 'src/app/services/option.service';
import { AreaService } from 'src/app/services/area.service';
import { byAreaAndLevelByExtraction } from 'src/app/lib/@bus-tier/sorting';
import { LevelService } from 'src/app/services/level.service';
import { Sorting } from 'src/lib/darkcloud/angular/easy-mvc/Sorting';
import { CharacterService } from 'src/app/services/character.service';
import { comparable } from 'src/lib/others';
import { Router } from '@angular/router';

@Component({
  selector: 'app-message-report',
  templateUrl: './message-report.component.html',
})
export class MessageReportComponent implements OnInit {

  constructor(
    private _speechService: SpeechService,
    private _optionService: OptionService,
    private _areaService: AreaService,
    private _router: Router,
    private _levelService: LevelService,
    private _characterService: CharacterService
  ) {}
  /**
   * Order in which the introductory speech list is sorted
   */
  protected introductorySpeechesSortingOrder: Sorting.Order = 'ascending';
  /**
   * Parameter by which introductory speech the list is sorted
   */
  protected introductorySpeechesSortingParameter: Sorting.Parameter;
  /**
   * Order in which the omitted option list is sorted
   */
  protected omittedOptionsSortingOrder: Sorting.Order = 'ascending';
  /**
   * Parameter by which omitted option speech the list is sorted
   */
  protected omittedOptionsSortingParameter: Sorting.Parameter;

  /**
   * Introduction speeches that appear on the report
   */
  introductorySpeeches: Speech[];

  /**
   * Omitted speeches that appear on the report
   */
  omittedOptions: Option[];

  ngOnInit(): void {
    this.introductorySpeeches = this._speechService.models.filter(
      (speech) => speech.isIntroduction
    );
    this.omittedOptions = this._optionService.models.filter(
      (option) => option.isOmitted
    );

    // sorts introductory speeches by hierarchy code then level
    this.omittedOptions.sort((a, b) =>
      byAreaAndLevelByExtraction(a, b, this._areaService, this._levelService)
    );

    this.sortIntroductorySpeechesByParameter('name');
    this.sortIntroductorySpeechesByParameter('hierarchyCode');
  }
  /**
   * Sorts the introductory speeches by a parameter and resets the sortingOrder if necessary
   * @param parameter Parameter to sort the list by
   */
  public sortIntroductorySpeechesByParameter(param: Sorting.Parameter) {
    this.introductorySpeechesSortingOrder = Sorting.nextOrder(
      this.introductorySpeechesSortingOrder,
      this.introductorySpeechesSortingParameter,
      param
    );
    this.introductorySpeechesSortingParameter = param;
    this.sortsIntroductorySpeeches(this.introductorySpeeches);
  }

  /**
   * Sorts the omitted options by a parameter and resets the sortingOrder if necessary
   * @param parameter Parameter to sort the list by
   */
  public sortOmittedOptionsByParameter(param: Sorting.Parameter) {
    this.omittedOptionsSortingOrder = Sorting.nextOrder(
      this.omittedOptionsSortingOrder,
      this.omittedOptionsSortingParameter,
      param
    );
    this.omittedOptionsSortingParameter = param;
    this.sortOmittedOptions(this.omittedOptions);
  }

  /**
   * Sorts the introductory speeches by parameter depending on which parameter it is
   * @param p Parameter to sort the list by
   */
  private sortsIntroductorySpeeches(data: Speech[]) {
    switch (this.introductorySpeechesSortingParameter) {
      // sorts introductory speeches by hierarchy code then level
      case 'hierarchyCode':
        data.sort((a, b) =>
          this.introductorySpeechesSortingOrder === 'descending'
            ? byAreaAndLevelByExtraction(
                a,
                b,
                this._areaService,
                this._levelService
              )
            : byAreaAndLevelByExtraction(
                b,
                a,
                this._areaService,
                this._levelService
              )
        );
        break;
      // sorts introductory speeches by character name
      case 'name':
        data.sort((a, b) =>
          this.introductorySpeechesSortingOrder === 'descending'
            ? comparable(
                this._characterService.svcFindById(a.speakerId)?.name
              ) >
              comparable(this._characterService.svcFindById(b.speakerId)?.name)
              ? 1
              : -1
            : comparable(
                this._characterService.svcFindById(a.speakerId)?.name
              ) <
              comparable(this._characterService.svcFindById(b.speakerId)?.name)
            ? 1
            : -1
        );
        break;
      // sorts introductory speeches by message
      case 'message':
        data.sort((a, b) =>
          this.introductorySpeechesSortingOrder === 'descending'
            ? comparable(a.message) > comparable(b.message)
              ? 1
              : -1
            : comparable(a.message) < comparable(b.message)
            ? 1
            : -1
        );
        break;
    }
  }

  /**
   * Sorts the omitted options by parameter depending on which parameter it is
   * @param p Parameter to sort the list by
   */
  private sortOmittedOptions(data: Option[]) {
    switch (this.omittedOptionsSortingParameter) {
      // sorts omitted options by hierarchy code then level
      case 'hierarchyCode':
        data.sort((a, b) =>
          this.omittedOptionsSortingOrder === 'descending'
            ? byAreaAndLevelByExtraction(
                a,
                b,
                this._areaService,
                this._levelService
              )
            : byAreaAndLevelByExtraction(
                b,
                a,
                this._areaService,
                this._levelService
              )
        );
        break;
      // sorts omitted options by message
      case 'message':
        data.sort((a, b) =>
          this.omittedOptionsSortingOrder === 'descending'
            ? comparable(a.message) > comparable(b.message)
              ? 1
              : -1
            : comparable(a.message) < comparable(b.message)
            ? 1
            : -1
        );
        break;
    }
  }

  /**
   * Navigates to the Dialogue Editor page with the storyprogress id as the fragment
   */
  access(obj: any): void {
    this._router.navigate(
      [
        'levels/' +
          Level.getSubIdFrom(obj.id) +
          '/dialogues/' +
          Dialogue.getSubIdFrom(obj.id, 'PT-BR'),
      ],
      { fragment: obj.id }
    );
  }
}
