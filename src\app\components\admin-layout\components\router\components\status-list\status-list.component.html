<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons 
          [cardTitle]="'Skill List'"
          [cardDescription]="cardDescription"
          [rightButtonTemplates]="[statusTemplate]">
        </app-header-with-buttons>
        <app-header-search (inputKeyup)="lstOnChangeFilter($event)" (searchOptions)="lstOnChangeFilterOptions($event)"></app-header-search>
      </div>
    </div>
    <!--List-->
    <div class="card">
      <table class="table table-list">
        <thead class="sticky">
          <tr>
            <th class="th-clickable" >Index</th>
            <th class="th-clickable" (click)="sortBySkill()">Skill
              <div class="ball-circle"></div>
            </th>
            <th class="th-clickable" (click)="sortByAcronym()">Acronym</th>
            <th >Description
              <div class="ball-circle"></div>
            </th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor=" let status of this.statusClasses; let i = index; trackBy: trackById">
            <tr id="{{ status.id }}">
              <td class="td-sort">{{ i + 1 }}</td>
              <td class="td-notes">
                <input class="form-control form-short "
                  type="text"
                  value="{{ (status | translation : lstLanguage : status.id : 'skill') }}"
                  #skill
                  [ngStyle]="{'background-color': +skill.value <= 0 ? '#404040' : '#ffffff'}"
                  (change)="changeSkill(status, 'skill', skill.value)"/>
              </td>
              <td class="td-notes" style="width: 20%;" >
              <ng-container *ngIf="status.acronym == undefined || status.acronym == ''">
                <span [ngClass]="{'text-danger pulse-red': status.acronym == undefined || status.acronym == ''}">Campo obrigatório</span>
              </ng-container>          
                <input class="form-control form-short " 
                  type="text"
                  value="{{ status.acronym }}"
                  #acronym
                  [ngStyle]="{'background-color': +acronym.value <= 0 ? '#404040' : '#ffffff'}"
                  (change)="changeSkill(status, 'acronym', acronym.value)"/>                
              </td>
              <td class="td-notes" style="text-align:right; width:100%">
                <textarea class="form-control " style="height:50px"
                  type="text"
                  value="{{ (status | translation : lstLanguage : status.id : 'description') }}"
                  #description
                  [ngStyle]="{'background-color': +description.value <= 0 ? '#404040' : '#ffffff'}"
                  (change)="changeSkill(status, 'description', description.value)">
                </textarea>
              </td>
              <td class="td-actions">
                <button class="btn btn-danger btn-fill btn-remove"
                  (click)="removeElement(status)">
                  <i class="pe-7s-close"></i>
                </button><br>                
                <button 
                  class="btn btn-gray btn-fill translation-button"
                  (click)="getStatusOrtography(status)">
                  <div class="mat-translate"></div>
                </button>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>
