<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons [cardTitle]="'Souls Grinder'" [cardDescription]="description"
          [rightButtonTemplates]="[excelButtonTemplate]">
        </app-header-with-buttons>
        <app-header-search (inputKeyup)="lstOnChangeFilter($event)"
          (searchOptions)="lstOnChangeFilterOptions($event)"></app-header-search>
      </div>
    </div>
    <!--List-->
    <div class="card">
      <table class="table table-list">
        <thead class="sticky">
          <tr>
            <th rowspan="4" class="th-clickable" (click)="this.sortBySoulsLevel()">
              Souls Grinder - Level
            </th>
            <th colspan="3">
              IMPROVEMENT (change SOUL GRINDER level)
            </th>
            <th colspan="3">
              SOULS PRODUCTION
            </th>
          </tr>
          <tr>
            <th class="th-clickable" (click)="this.sortByTitaniumCost()">
              Cost
            </th>
            <th class="th-clickable" (click)="sortByBuildingTime()">
              Building Time
            </th>
            <th class="th-clickable" (click)="sortByRubiesCost()">
              Skip Cost
            </th>
            <th rowspan="3" class="th-clickable" (click)="sortByLocalStorage()">
              Local Storage Capacity
            </th>
            <th rowspan="3" class="th-clickable" (click)="sortByProductionTime()">
              Production Per Hour
            </th>
            <th class="th-clickable" (click)="sortByGoldCost()">
              Skip Cost
            </th>
          </tr>
          <tr>
            <th class="th-clickable silver-color" (click)="this.sortByTitaniumCost()">
              TITANIUM
            </th>
            <th class="th-clickable time-color" (click)="sortByBuildingTime()">
              MINUTES
            </th>
            <th class="th-clickable rubies-color" (click)="sortByRubiesCost()">
              RUBIES
            </th>
            <th class="th-clickable gold-color" (click)="sortByGoldCost()">
              GOLD
            </th>
          </tr>
          <tr>
            <th class="th-clickable light-gray" (click)="this.sortByTitaniumCost()">
              Required to get this level
            </th>
            <th class="th-clickable light-gray" (click)="sortByBuildingTime()">
              Time to upgrade to this level
            </th>
            <th class="th-clickable light-gray" (click)="sortByRubiesCost()">
              Gem to skip the wait (build instantly)
            </th>
            <th class="th-clickable light-gray" (click)="sortByGoldCost()">
              Gold to skip the wait (produces instantly)
            </th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="
                let soulsStorage  of lstIds | soulsGrinderStorages;
                let i = index;
                trackBy: trackById
              ">
            <tr *ngIf="soulsStorage.type === '0'" id="{{ soulsStorage.id}}">

              <td>{{ soulsStorage.soulsLevel}}</td>

              <td class="td-id">
                <input class="background-input-table-color" placeholder=" " type="number" #Inputsouls
                  [value]="soulsStorage.titaniumCost" (change)="changeTitaniumCost(soulsStorage, +Inputsouls.value)" />
              </td>

              <td class="td-id">
                <input class="background-input-table-color" placeholder=" " type="number" #Inputtime
                  [value]="soulsStorage.requiredTime" (change)="changeRequiredTime(soulsStorage, +Inputtime.value)" />
              </td>

              <td class="td-id">
                <input class="background-input-table-color" placeholder=" " type="number" #Inputrubies
                  [value]="soulsStorage.rubiesSkipCost" (change)="changeRubiesCost(soulsStorage, +Inputrubies.value)" />
              </td>

              <td class="td-id">
                <input class="background-input-table-color" placeholder=" " type="number" #Inputstorage
                  [value]="soulsStorage.localStorage"
                  (change)="changeLocalStorage(soulsStorage, +Inputstorage.value)" />
              </td>

              <td class="td-id">
                <input class="background-input-table-color" placeholder=" " type="number" #InputProductionTime
                  [value]="soulsStorage.productionPerTime"
                  (change)="changeProductionTime(soulsStorage, +InputProductionTime.value)" />
              </td>

              <td class="td-id">
                <input class="background-input-table-color" placeholder=" " type="number" #InputGoldCost
                  [value]="soulsStorage.goldSkipCost" (change)="changeGoldCost(soulsStorage, +InputGoldCost.value)" />
              </td>

            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>