import { Component, EventEmitter, Output } from '@angular/core';
import { ComboTable } from 'src/app/lib/@bus-tier/models';
import { Button } from 'src/app/lib/@pres-tier/data';
import { StatusInfoService } from 'src/app/services';
import { ComboTableService } from 'src/app/services/combo-table.service';
import { Alert } from 'src/lib/darkcloud';

@Component({
  selector: 'app-combo',
  templateUrl: './combo.component.html',
  styleUrls: ['./combo.component.scss']
})
export class ComboComponent {

  @Output() activeTab2 = new EventEmitter<string>();
   listComboTable: ComboTable[] = [];
   activeLanguage = 'PTBR';
   titles = ['ID COMBO', 'Category', 'SKILL RESIST USER 1', 'SKILL RESIST USER 2', 'SKILL RESIST USER 3',
    'SKILL RESIST USER 4', 'SKILL RESIST USER 5', 'SKILL RESIST USER 6', 'OPERATOR', 'SP ATK VALUE', 'SP DEF VALUE',
    'STATUS EFFECT NAME', 'DESCRIPTION', 'POWER POINTS (PP)', 'DURATION (TURNS)'];

  public readonly excelButtonTemplate: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
 };

  constructor(
    private _comboTableService: ComboTableService,
    private _elmentalAffinitiesService: StatusInfoService,
  ){}

  async ngOnInit(): Promise<void>{
    this.listComboTable = this._comboTableService.models;
  }

  async onExcelPaste() {
        const text = await navigator.clipboard.readText();
        const lines = text.split(/\r?\n/).filter(line => line);
        const processedData: string[][] = [];
        
        this._comboTableService.models = [];
        this._comboTableService.toSave();

        if (lines.length > 0) {
          lines.forEach(line => {
            // Divide cada linha em colunas
            const values = line.split("\t").map(value => value.trim()).slice(1);
            processedData.push(values);
          });
          console.log('ProcessedData',processedData);
          // Verifica se o número de colunas em cada linha corresponde ao número de colunas em this.titles
          const isColumnCountValid = processedData.every(row => row.length === this.titles.length);

          if (!isColumnCountValid) {
            Alert.showError('Invalid number of columns');
            return this.ngOnInit();
          }

          // Validação dos valores das colunas 1-6 (skillResistUser) com os status do StatusInfoService
          const statusValues = this._elmentalAffinitiesService.models.map(status => status.status);

          for (let rowIndex = 0; rowIndex < processedData.length; rowIndex++) {
            const row = processedData[rowIndex];
            // Verifica as colunas 2-7 (índices 2-7 no array, que correspondem ao skillResistUser)
            for (let colIndex = 2; colIndex <= 7; colIndex++) {
              const cellValue = row[colIndex];
              if (cellValue && cellValue.trim() !== '' && !statusValues.includes(cellValue.trim())) {
                Alert.showError(`Invalid status value "${cellValue}" in row ${rowIndex + 1}, column ${colIndex + 1}. Value must match one of the available status values.`);
                return this.ngOnInit();
              }
            }
          }

          for (let index = 0; index < processedData.length; index++) {
            this._comboTableService.createNewComboTable(processedData[index]);
          }

          Alert.ShowSuccess('Combo Table imported successfully!');
          this.ngOnInit();
        }
  }

  changeCombo(rowIndex: number, name: string, newValue: string, arrayIndex?: number){

        if (name === 'idCombo') {
         this.listComboTable[rowIndex].idCombo = newValue;
        }
        else if (name === 'category') {
          this.listComboTable[rowIndex].category = newValue;
         }
        else if (name === 'skillResistUser') {
          // Garante que o array existe e tem 6 posições
          if (!this.listComboTable[rowIndex].skillResistUser) {
            this.listComboTable[rowIndex].skillResistUser = ['', '', '', '', '', ''];
          }
          // Atualiza a posição específica do array
          if (arrayIndex !== undefined && arrayIndex >= 0 && arrayIndex < 6) {
            this.listComboTable[rowIndex].skillResistUser[arrayIndex] = newValue;
          }
        }
        else if (name === 'operator') {
          this.listComboTable[rowIndex].operator = newValue;
         }
         else if (name === 'sp_ATK_Value') {
          this.listComboTable[rowIndex].sp_ATK_Value = newValue;
         }
         else if (name === 'sp_DEF_Value') {
          this.listComboTable[rowIndex].sp_DEF_Value = newValue;
         }
         else if (name === 'statusEffectName') {
          this.listComboTable[rowIndex].statusEffectName = newValue;
         }
         else if (name === 'description') {
          this.listComboTable[rowIndex].description = newValue;
         }
         else if (name === 'powerPoints') {
          this.listComboTable[rowIndex].powerPoints = newValue;
         }
         else if (name === 'duration') {
           this.listComboTable[rowIndex].duration = newValue;
          }
        this._comboTableService.svcToModify(this.listComboTable[rowIndex]);
   }
   

}
