 .card-header-wrapper {
  padding: 15px;
  display: flex;
}

.card-header-content {
  display: block;
  margin-left: 10px;
  margin-right: 15px;
  width: 35%;
}

.card-header-tooltip {
  display: block;
  margin-left: 33%;
  margin-right: 33%;
  margin-bottom: 50px;
  color:white !important;
  background-color: black;
  padding:30px;
  z-index: 999;
  border-radius: 10px;
}
.card-title-desccription {
  width: 100%;
  padding-bottom: 30px;
}

.text-description {
  height: 30px;
  white-space: normal;
  word-wrap: break-word;
  overflow-x: hidden;
  overflow-y: auto;
  width: 100%;
  max-width: 100%;
  min-height: 100% !important;
  
}

.btnSubContext {
  position: absolute;
  top: 25px;
  right: 120px; 
}

.textTitles {
  display: flex;
  width: 40%;
  margin-left: 60px;
}
.c-nameRarity {
  margin-left: 5px;
  padding: 6px; 
  margin-top: -6px;
  border-radius: 5px;
}