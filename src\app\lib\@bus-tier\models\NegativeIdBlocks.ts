import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';

export class NegativeIdBlocks extends Base<Data.Hard.INegativeIdBlocks, Data.Result.INegativeIdBlocks> implements Required<Data.Hard.INegativeIdBlocks>
{
  public static generateId(index: number): string {
    return IdPrefixes.NEGATIVE + index;
  }

  constructor( index: number, dataAccess: NegativeIdBlocks['TDataAccess']) 
  {
    super(
    {
      hard: 
      {
        id: NegativeIdBlocks.generateId(index),     
      },
    },
    dataAccess
    );
  }
  protected getInternalFetch() 
  {
    return {};
  }
  public get positionNameNegative(): string[]
  {
    return this.hard.positionNameNegative;
  }
  public set positionNameNegative(value: string[]) 
  {
    this.hard.positionNameNegative = value;
  }

}
