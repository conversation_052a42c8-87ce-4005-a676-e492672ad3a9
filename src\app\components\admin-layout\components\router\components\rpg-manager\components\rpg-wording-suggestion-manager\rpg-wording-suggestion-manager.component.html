<div class="row">
  <!-- START: First Table -->
  <div class="col-md-4">
    <div class="report-table-wrapper">
      <table *ngIf="nAnalyzedSuggestions === all.length; else noSuggestions"
             class="table table-striped table-full-width report-table">
        <thead class="sticky"
               style="top: 0px">
          <tr>
            <th class="center">
              IDENTIFIED
            </th>
            <th class="center">
              EXACT MATCH
            </th>
            <th class="center">
              <button type="checkbox"
                      (click)="toggleSelectAllSuggestions(SearchResultType.PARAMETER_MATCH)"
                      class="btn btn-fill btn-small">
                SELECT ALL
              </button>
            </th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let id of ids;">
            <tr *ngIf="+suggestionsIndex[id].type === SearchResultType.PARAMETER_MATCH;">
              <td class="td-auto">
                {{ suggestionsIndex[id].wordsIdentified | toStringWithSpace }}
              </td>
              <td class="td-100 td-clickable"
                  (click)="access(id)">
                <p [innerHTML]="suggestionsIndex[id].newMessage | rpgFormatting "></p>
              </td>
              <td class="center">
                <input [(ngModel)]="suggestionsIndex[id].accepted"
                       type="checkbox"
                       #selectAccept />
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
      <ng-template #noSuggestions>
        <i class="no-error-check pe-7s-check"></i>
      </ng-template>
    </div>
  </div>
  <!-- END: First Table -->

  <!-- START: Second Table -->
  <div class="col-md-4">
    <div class="report-table-wrapper">
      <table *ngIf="nAnalyzedSuggestions === all.length; else noOtherSuggestions"
             class="table table-striped table-full-width report-table">
        <thead class="sticky"
               style="top: 0px">
          <tr>
            <th class="center">
              IDENTIFIED
            </th>
            <th class="center">
              CONTAINS
            </th>
            <th class="center">
              <button type="checkbox"
                      (click)="toggleSelectAllSuggestions(SearchResultType.PARAMETER_CONTAIN)"
                      class="btn btn-fill btn-small">
                SELECT ALL
              </button>
            </th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let id of ids;">
            <tr *ngIf="+suggestionsIndex[id].type === SearchResultType.PARAMETER_CONTAIN;">
              <td class="td-auto">
                {{ suggestionsIndex[id].wordsIdentified | toStringWithSpace }}
              </td>
              <td class="td-100 td-clickable"
                  (click)="access(id)">
                <p [innerHTML]="suggestionsIndex[id].newMessage | rpgFormatting"></p>
              </td>
              <td class="center">
                <input [(ngModel)]="suggestionsIndex[id].accepted"
                       type="checkbox"
                       #selectAccept />
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
      <ng-template #noOtherSuggestions>
        <i class="no-error-check pe-7s-check"></i>
      </ng-template>
    </div>
  </div>  
  <!-- END: Second Table -->
  
  <!-- START: Third Table -->
  <div class="col-md-4">
    <div class="report-table-wrapper">
      <table *ngIf="nAnalyzedSuggestions === all.length; else noOtherSuggestions"
             class="table table-striped table-full-width report-table">
        <thead class="sticky"
               style="top: 0px">
          <tr>
            <th class="center">
              NO IDENTIFIED
            </th>
            <th class="center">
              BUT MARKED
            </th>
            <th class="center">
              <button type="checkbox"
                      (click)="toggleAllNoIdetifieds()"
                      class="btn btn-fill btn-small">
                SELECT ALL
              </button>
            </th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let speech of this._rpgService.allSpeeches;">
            <tr >
              <td class="td-auto">
                {{speech.word}}
              </td>
              <td class="td-100 td-clickable"
                  (click)="access(speech.id)">
                <p [innerHTML]="speech.message | rpgFormatting"></p>
              </td>
              <td class="center">
                <input
                       type="checkbox"
                       [checked]="speech.selected"
                       (change)="toggleNoIdetifieds(speech)"/>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
      <ng-template #noOtherSuggestions>
        <i class="no-error-check pe-7s-check"></i>
      </ng-template>
    </div>
  </div> 
  <!-- END: Third Table -->
</div>
