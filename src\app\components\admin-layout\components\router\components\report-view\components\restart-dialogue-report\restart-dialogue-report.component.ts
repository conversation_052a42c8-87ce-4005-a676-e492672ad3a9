import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Marker } from 'src/app/lib/@bus-tier/models';
import { Sortment } from 'src/app/lib/@pres-tier';
import { AreaService, MarkerService, ReviewService } from 'src/app/services';
import { EasyMVC } from 'src/lib/darkcloud/angular';
import { MarkerType } from 'src/lib/darkcloud/dialogue-system';

export interface Preloadable {
  pinDialogueMarkers: Marker[];
}

@Component({
  selector: 'app-restart-dialogue-report',
  templateUrl: './restart-dialogue-report.component.html',
  styleUrls: ['./restart-dialogue-report.component.scss']
})
export class RestartDialogueReportComponent
extends EasyMVC.PreloadComponent<Preloadable>
implements OnInit {

  sorting: Sorting = new Sorting(this);

  constructor(
    private _activatedRoute: ActivatedRoute,
    public readonly router: Router,
    public readonly areaService: AreaService,
    private _markerService: MarkerService,
    private _reviewService: ReviewService
  ) {
    super(_activatedRoute, (data) => data.preloadedLevelPinReportData)
  }

  public restartMarkers: Marker[] = [];

  ngOnInit(): void 
  {
    this.restartMarkers = this._markerService.models.filter(x => 
    {
      return (+x.type == +MarkerType.RESTART_DIALOGUE);
    });
  }

  access(id: string){
    this.router.navigate(
      [
        'levels/' + this._reviewService.reviewResults[id].levelId +
        '/dialogues/' + this._reviewService.reviewResults[id].dialogueId
      ],
      {fragment: id}
    );
  }

}

class Sorting extends Sortment.Sorting {
  byLocation = Sortment.byLocation(this._component.areaService);
  byLevelLocation = Sortment.byLevelLocation(this._component.areaService);
  byPin = (marker: Marker) => marker.pin;
  byType = (marker: Marker) => marker.type;
  constructor(private readonly _component: RestartDialogueReportComponent) {
    super();
  }
  public sortPinMarkers(by: Sortment.Sortion<Marker>) {
    this.execute(this._component.restartMarkers, by);
  }
}