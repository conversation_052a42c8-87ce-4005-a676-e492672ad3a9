import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { MahankaraBehavior } from 'src/app/lib/@bus-tier/models';
import { MahankaraBehaviorTableService } from 'src/app/services';
import { Alert } from 'src/lib/darkcloud';

@Component({
  selector: 'app-mahankara-behavior-table',
  templateUrl: './mahankara-behavior-table.component.html',
  styleUrls: ['./mahankara-behavior-table.component.scss']
})
export class MahankaraBehaviorTableComponent implements OnChanges, OnInit{

  @Output() descriptionOutput = new EventEmitter<string>();
  @Input() copyExcelMahankaraBehavior: string[];
  titles = ['Mahankara Upgrade','BL Addition', 'Party Member BL Addition', 'Turn Duration'];
  listMahankaraBehavior: MahankaraBehavior[] = [];
  // Flag para controle de inicialização do copyExcelRepetition
  isFirstChange = true;

  constructor(
    private _mahankaraBehaviorTableService: MahankaraBehaviorTableService,
  ) { }

    ngOnChanges(changes: SimpleChanges) {

     if (changes['copyExcelMahankaraBehavior']) {
        if (this.isFirstChange) {
          // Ignorar a primeira alteração no ciclo de vida
          this.isFirstChange = false;
        } else if (this.copyExcelMahankaraBehavior && this.copyExcelMahankaraBehavior.length > 0) {
          this.onExcelPaste();
        }
      }
     
    }

  async ngOnInit(): Promise<void> {

    this._mahankaraBehaviorTableService.toFinishLoading();
    this.listMahankaraBehavior = this._mahankaraBehaviorTableService.models;
    this.descriptionOutput.emit(`Showing ${this.listMahankaraBehavior.length} results`);  
  }
  async onExcelPaste() {  

      this._mahankaraBehaviorTableService.models = [];
      this._mahankaraBehaviorTableService.toSave();

    // Verifica se `this.copyExcelMahankaraBehavior` contém dados
    if (!this.copyExcelMahankaraBehavior || this.copyExcelMahankaraBehavior.length === 0) {
      Alert.showError('No data found in the copied Excel content.');
      return this.ngOnInit();
    }
  
    const expectedColumns = this.titles.length; 
  
    //Verificar se todas as linhas possuem o número correto de colunas
    const invalidColumnRows = this.copyExcelMahankaraBehavior.filter(row => {
      const cells = row.split('\t'); // O '\t' dividide em células - O delimitador \t é para tabulação (comum em colagens do Excel)
      return cells.length !== expectedColumns;
    });
  
    if (invalidColumnRows.length > 0) {
      Alert.showError(`The number of columns does not match the expected count (${expectedColumns}). Please check the data.`);
      return this.ngOnInit();
    }
  
    //Verificar se todas as células são numéricas
    const invalidRows = this.copyExcelMahankaraBehavior.filter(row => {
      const cells = row.split('\t'); // Divide a linha em células
      return cells.some((cell: string) => {
        const numericValue = parseFloat(cell.trim()); // Remove espaços e tenta converter
        return isNaN(numericValue); // Verifica se não é um número
      });
    });
  
    if (invalidRows.length > 0) {
      Alert.showError('Some rows contain non-numeric values. Please check the Excel data.');
      return;
    }
  
    this.copyExcelMahankaraBehavior.forEach((row, index) => {
      const cells = row.split('\t'); // Divide a linha em células   
      const processedData = this.processExcelRow(cells); // Processa a linha em dados necessários   
      this._mahankaraBehaviorTableService.createNewMahankaraBehaviorTable(processedData);
    });

    this.copyExcelMahankaraBehavior = [];
    Alert.ShowSuccess('Mahankara Behavior imported successfully!');
    this.ngOnInit();
  }
   

  private processExcelRow(row: any[]): any {
    // Transforme a linha do Excel no formato necessário para ser salvo.
    const data = {
      0: row[0].valueOf(),
      1: row[1].valueOf(),
      2: row[2].valueOf(),
      3: row[3].valueOf(),   
    };
    return data;
  }
    
  changeMahankaraValue(rowIndex: number, name: string, newValue: string) {
    if (name === 'mahankaraUpgrade') {
      this.listMahankaraBehavior[rowIndex].mahankaraUpgrade = +newValue;        
     }
     else if (name === 'bLAddition') {
       this.listMahankaraBehavior[rowIndex].bLAddition = +newValue;        
      }
      else if (name === 'partyMemberBLAddition') {
       this.listMahankaraBehavior[rowIndex].partyMemberBLAddition = +newValue;        
      }
      else if (name === 'turnDuration') {
       this.listMahankaraBehavior[rowIndex].turnDuration = +newValue;        
      }
      this._mahankaraBehaviorTableService.svcToModify(this.listMahankaraBehavior[rowIndex]);
}


}
