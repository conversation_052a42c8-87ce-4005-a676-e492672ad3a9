import { ChangeDetectorRef, Component, EventEmitter, OnInit, Output } from '@angular/core';
import { AreaService } from 'src/app/services/area.service';
import { ItemClassService } from 'src/app/services/item-class.service';
import { ReviewService } from 'src/app/services/review.service';
import { ItemService } from 'src/app/services/item.service';
import { Item, SpecialWeapon } from 'src/app/lib/@bus-tier/models';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';
import { CustomService } from 'src/app/services/custom.service';
import { SpecialWeaponService } from 'src/app/services/special-weapon.service';
import { ISpecialWeaponsHC } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard';
import { Alert, Review } from 'src/lib/darkcloud';
import { Area } from 'src/app/lib/@bus-tier/models';
import { ItemClass } from 'src/app/lib/@bus-tier/models/ItemClass';
import { SpecialWeaponServiceHC } from 'src/app/services/special-weaponHC.service';


interface Itemlevel {
  hc?: number;
  idCircle?: string;
  nameCircle?: string;
  receivedAt?: string[];
  idNameSW?:string;
  idNameIngredient?:string;
}

@Component({
  selector: 'app-special-weapons',
  templateUrl: './special-weapons.component.html',
  styleUrls: ['./special-weapons.component.scss']
})
export class SpecialWeaponsComponent implements OnInit{

  valueRange: string;
  blueprints: Item[];
  itemIds: string[];
  listItem: Itemlevel[];
  specialWeapons: SpecialWeapon[];
  hcSpecialWeapons: ISpecialWeaponsHC[] = [];
  listHCSpecialWeapons: ISpecialWeaponsHC[] = [];
  toCreateSpecialWeapons: ISpecialWeaponsHC[] = [];
  uniqueIngredients: Item[];
  selectedClasses = [];
  itemClasses: ItemClass[] = [];
  listCommon: Itemlevel[] = [];
  ids = [];
  custom: Custom;
  title: string = 'Special Weapons';
  description: string = '';
  sortNameOrder = +1;

  constructor(
    protected _customService: CustomService,
    private _itemClassService: ItemClassService,
    private _itemService: ItemService,
    private _specialWeaponService: SpecialWeaponService,
    private _specialWeaponServiceHC: SpecialWeaponServiceHC,
    private _areaService: AreaService,
    private _reviewService: ReviewService,
    private _change: ChangeDetectorRef,
  ) {}


  async ngOnInit(): Promise<void>  {
    this._customService.toFinishLoading();
    this.custom = await this._customService.svcGetInstance();

    // Use weaponsWLBaseClassItem to show data based on selected item classes
    // This allows dynamic filtering like app-powerup-information
    if (this.custom.weaponsWLBaseClassItem && this.custom.weaponsWLBaseClassItem.length > 0) {
      this.itemClasses = this._itemClassService.models.filter((klass) =>
        this.custom.weaponsWLBaseClassItem.includes(klass.id)
      );
    } else {
      // No item classes selected - show empty list
      this.itemClasses = [];
    }

    if(this.itemClasses.length > 0) {
      // Collect all item IDs from selected classes
      this.itemIds = [];
      this.itemClasses.forEach(itemClass => {
        if (itemClass && itemClass.itemIds) {
          this.itemIds = this.itemIds.concat(itemClass.itemIds);
        }
      });

      this.selectClasses();
      this.updateSpecialWeapons();
      await this._specialWeaponServiceHC.toFinishLoading();

      this.listHCSpecialWeapons = this._specialWeaponServiceHC.models;
      this.lineupOrderCommon();
      this.updateDescription();
    }
    else {
      Alert.showError('No item classes selected for special weapons. Please check the Custom settings.');
    }
  }


  updateSpecialWeapons() {
    this.toCreateSpecialWeapons = this.filterAndAssignSpecialWeapons();
    this.listHCSpecialWeapons = this._specialWeaponServiceHC.models;

    // Verifica se há conteúdo em listHCSpecialWeapons
    if (this.listHCSpecialWeapons.length > 0) {
        // Itera sobre os itens de toCreateSpecialWeapons para criar novos itens se necessário
        this.toCreateSpecialWeapons.forEach(item => {
            // Verifica se o item tem idCircle correspondente em listHCSpecialWeapons
            const matchingHCSpecialWeapon = this.listHCSpecialWeapons.find(hcItem => hcItem.idCircle === item.idCircle);

            if (matchingHCSpecialWeapon) {
                // Se houver um item com idCircle correspondente, comparar os idNameBPR em bluePrintReceivedHC
                item.bluePrintReceivedHC?.forEach(itemBP => {
                    const matchingBP = matchingHCSpecialWeapon.bluePrintReceivedHC?.find(hcBP => hcBP.idNameBPR === itemBP.idNameBPR);
                    // Se idNameBPR for diferente ou não encontrado, criar um novo SpecialWeaponHC
                    if (!matchingBP) {                                
                      this._specialWeaponServiceHC.addNewBluePrintReceivedHC(item);
                    }
                });
                // Remover bluePrintReceivedHC se idNameBPR não existir em toCreateSpecialWeapons
                const blueprintToRemove = matchingHCSpecialWeapon.bluePrintReceivedHC?.filter(hcBP =>
                    !item.bluePrintReceivedHC?.some(itemBP => itemBP?.idNameBPR === hcBP?.idNameBPR)
                );

                if (blueprintToRemove && blueprintToRemove.length > 0) {
                    blueprintToRemove.forEach(bpToRemove => {
                        // Removendo os itens do bluePrintReceivedHC que não estão mais presentes
                        const indexToRemove = matchingHCSpecialWeapon.bluePrintReceivedHC?.findIndex(hcBP => hcBP.idNameBPR === bpToRemove.idNameBPR);
                        if (indexToRemove !== undefined && indexToRemove !== -1) {
                            matchingHCSpecialWeapon.bluePrintReceivedHC?.splice(indexToRemove, 1);
                        }
                    });
                }

            } else {
                // Se o idCircle não for encontrado, criar um novo SpecialWeaponHC
                this._specialWeaponServiceHC.createNewSpecialWeaponHC(item);
            }
        });

        // Remover toda a estrutura de hcSpecialWeapons se o idCircle não existir em toCreateSpecialWeapons
        const itemsToRemove = this.listHCSpecialWeapons.filter(hcItem =>
            !this.toCreateSpecialWeapons.some(item => item.idCircle === hcItem.idCircle)
        );

        if (itemsToRemove && itemsToRemove.length > 0) {
            itemsToRemove.forEach(itemToRemove => {
                this._specialWeaponServiceHC.svcToRemove(itemToRemove.id);
            });
        }

    } else {
        // Se não houver conteúdo em listHCSpecialWeapons, criar todos os itens de toCreateSpecialWeapons
        this.toCreateSpecialWeapons.forEach(item => {
            this._specialWeaponServiceHC.createNewSpecialWeaponHC(item);
        });
    }
}

  selectClasses()
  {
    // Use weaponsWLBaseClassItem for dynamic filtering
    this.selectedClasses = this._itemClassService.models.filter((klass) =>
    {
      return this.custom.weaponsWLBaseClassItem && this.custom.weaponsWLBaseClassItem.includes(klass.id);
    });
    let itemIds = [];    
    
    this.selectedClasses.forEach((itemClass) => 
    {
      itemClass.itemIds.forEach((itemId) => 
      {
        itemIds.push(this._itemService.svcFindById(itemId));
      });
    });
    
    this.ids = [];
    itemIds.forEach((id) => 
    {
      this.ids.push(id?.id)
    });
    
    this.specialWeapons = [];

    // First, add existing special weapons that match selected item IDs
    this._specialWeaponService.models.forEach((sw) =>
    {
      if(this.ids.includes(sw.itemId))
      {
        this.specialWeapons.push(sw);
        this.ids = this.ids.filter(id => id !== sw.itemId);
      }
    });

    // Then, create new entries for items that don't have special weapon data yet
    // This is the key addition to make it work like powerup-information
    this.ids.forEach(id => {
      this.specialWeapons.push(this._specialWeaponService.createNewPowerUp(id));
    });

    this._specialWeaponService.toSave();

    this.ckeckCircleWeapon();
    this.getHCSpecialWeapons();
  }

  ckeckCircleWeapon() {

    //Pega os dados das armas - NOW SHOWS ALL ITEMS FROM SELECTED CLASSES
    this.itemIds.forEach((item) => {
      const valor = this._itemService.models.filter((op)=> item == op.id);
      this.listItem =  valor.map((x) => ({idNameSW: x.id,nameSW: x.name, receivedAt: this.validatesItemInUse(x)?.receivedAt || [], typeItem: (x?.type != undefined ? x.type.toLocaleString() : x.type)}));
       this.listItem.forEach((m) => {
        // CHANGED: Now includes ALL items, not just those with receivedAt data
        // This makes it behave like powerup-information
        this.listCommon.push(m);
      });
    });

   //Verifica o nome do circulo - handle items with and without receivedAt data
    this.listCommon.map((x) => {
      if(x.receivedAt && x.receivedAt.length > 0) {
        // Items with receivedAt data - process normally
        for (let index = 0; index < x.receivedAt.length; index++) {
          const IdCircle = Area.getSubIdFrom(x.receivedAt[index]);
          const local = this._areaService.svcFindById(IdCircle);
          x.idCircle = local.id;
          x.nameCircle = local.name;
          x.hc = local.order;
        }
      } else {
        // Items without receivedAt data - show with empty/default values
        x.idCircle = 'unknown';
        x.nameCircle = 'No Circle Data';
        x.hc = 0;
      }
    });
  }

  //validates if item is Received
  validatesItemInUse(obj: any): Review.Result {
      if (!obj) return null;  
       let reviewsItem = this._reviewService.reviewResults[obj.id]; 
      return reviewsItem;
    } 

  getHCSpecialWeapons() {   

  this.hcSpecialWeapons = [];
  this.specialWeapons.forEach(sw => {
    const hcWeapon: ISpecialWeaponsHC = {
      bluePrintReceivedHC: [
        {
          idSW: sw.itemId,
          idNameSW: sw.id,// para pegar o nome do SPECIAL WEAPON
          idNameBPR: sw.bpId, // para pegar o nome do Blue Print e depois comparar com o id do this.blueprints
          idNameIngredient: sw.ingredientId, // para pegar o nome do Ingredient e depois comparar com o id do this.uniqueIngredients
        }
      ],
        name: '',
        id: '',
        idCircle: ''
      };
      this.hcSpecialWeapons.push(hcWeapon);
      });   
  }


  filterAndAssignSpecialWeapons(): ISpecialWeaponsHC[] {
    const foundSpecialWeapons: ISpecialWeaponsHC[] = [];

    // CHANGED: Now includes ALL items from selected classes like powerup-information
    this.listCommon.forEach(commonItem => {
        // First, try to find existing special weapons with blueprint relationships
        let foundExisting = false;

        this.hcSpecialWeapons.forEach(specialWeapon => {
            if (specialWeapon.bluePrintReceivedHC) {
                specialWeapon.bluePrintReceivedHC.forEach(blueprint => {
                    if (commonItem?.idNameSW === blueprint?.idNameBPR) {
                        specialWeapon.name = commonItem.nameCircle;
                        specialWeapon.idCircle = commonItem.idCircle;
                        specialWeapon.hc = commonItem.hc;
                        foundSpecialWeapons.push(specialWeapon);
                        foundExisting = true;
                    }
                });
            }
        });

        // If no existing special weapon found, create a new entry for this item
        // This ensures ALL selected items are displayed, not just those with existing data
        if (!foundExisting) {
            const newSpecialWeapon: ISpecialWeaponsHC = {
                id: `temp-${commonItem.idNameSW}`,
                name: commonItem.nameCircle || 'No Circle Data',
                idCircle: commonItem.idCircle || 'unknown',
                hc: commonItem.hc || 0,
                wlRange: '', // Add missing wlRange property
                bluePrintReceivedHC: [{
                    idSW: commonItem.idNameSW,
                    idNameSW: commonItem.idNameSW,
                    idNameBPR: commonItem.idNameSW, // Use item ID as placeholder
                    idNameIngredient: '', // Empty for non-special weapon items
                }]
            };
            foundSpecialWeapons.push(newSpecialWeapon);
        }
    });

    return this.mergeBlueprintsByIdCircle(foundSpecialWeapons);
}

  mergeBlueprintsByIdCircle(foundSpecialWeapons: ISpecialWeaponsHC[]): ISpecialWeaponsHC[] {
      const mergedSpecialWeapons: { [key: string]: ISpecialWeaponsHC } = {};

      foundSpecialWeapons.forEach(specialWeapon => {
          if (specialWeapon.idCircle) {
              if (!mergedSpecialWeapons[specialWeapon.idCircle]) {
                  // Se ainda não existe uma entrada para esse idCircle, cria uma nova
                  mergedSpecialWeapons[specialWeapon.idCircle] = {
                      ...specialWeapon,
                      bluePrintReceivedHC: [...(specialWeapon.bluePrintReceivedHC || [])]
                  };
              } else {
                  // Se já existe uma entrada para esse idCircle, mescla os blueprints
                  mergedSpecialWeapons[specialWeapon.idCircle].bluePrintReceivedHC.push(
                      ...(specialWeapon.bluePrintReceivedHC || [])
                  );
              }
          }
      });
  
      return Object.values(mergedSpecialWeapons);
  }

  GetBluePrintName(idNameBPR: string) {
    return this._itemService.svcFindById(idNameBPR)?.name;
  }  

  GetSpecialWeaponName(memoryModuleId: string): string 
  {
    return this._itemService.svcFindById(this._specialWeaponService.svcFindById(memoryModuleId)?.itemId)?.name;
  }

  GetIngredient(ingredId: string) {
    return this._itemService.svcFindById(ingredId)?.name;
  }

lineupOrderCommon() 
{
this.sortNameOrder *= +1;
  this.listHCSpecialWeapons.sort((a, b) => 
  {  
    return this.sortNameOrder * a.name.localeCompare(b.name);
  });
}

getWlRangeClass(hc: number): string {
  const roundedHc = Math.floor(hc); 

  if (roundedHc >= 0 && roundedHc <= 2) {
    this.valueRange = "1 - 6";  
      return 'green-bg'; 
  } else if (roundedHc >= 3 && roundedHc <= 5) {
    this.valueRange = "7 - 12"; 
      return 'primary-bg'; 
  } else if (roundedHc >= 6 && roundedHc <= 7) {
    this.valueRange = "13 - 16";
      return 'purple-bg'; 
  } else if (roundedHc >= 8 && roundedHc <= 9) {
    this.valueRange = "17 - 20";
      return 'yellow-bg'; 
  } else {
    this.valueRange = '';
    return '';
  }  
  
}

onChangeCommonWeapons(
  comm: any, 
  value: string, 
  fieldName: string, 
  index: number
) {

  // Verifique e defina os valores adequados usando os setters da classe SpecialWeaponsHC
  if (fieldName === 'qi_Min') {
    const bluePrintReceivedHC = comm.bluePrintReceivedHC;
    if (bluePrintReceivedHC && bluePrintReceivedHC[index]) {
      bluePrintReceivedHC[index].qi_Min = value === '' ? undefined : +value;
      comm.bluePrintReceivedHC = bluePrintReceivedHC;
    }
  }

  if (fieldName === 'luck_min') {
    const bluePrintReceivedHC = comm.bluePrintReceivedHC;
    if (bluePrintReceivedHC && bluePrintReceivedHC[index]) {
      bluePrintReceivedHC[index].luck_min = value === '' ? undefined : +value;
      comm.bluePrintReceivedHC = bluePrintReceivedHC;
    }
  }

  if (fieldName === 'wlBase') {
    const bluePrintReceivedHC = comm.bluePrintReceivedHC;
    if (bluePrintReceivedHC && bluePrintReceivedHC[index]) {
      bluePrintReceivedHC[index].wlBase = value === '' ? undefined : +value;
      comm.bluePrintReceivedHC = bluePrintReceivedHC;
    }
  }

  this._specialWeaponServiceHC.svcToModify(comm);
  this._specialWeaponServiceHC.toSave();   
}

sortNameOrderCommon: number = +1; 
orderSortBluePrint() {
    this.sortNameOrderCommon *= -1;
     this.listHCSpecialWeapons.forEach(comm => {
        comm.bluePrintReceivedHC.sort((a, b) => {
         return this.sortNameOrderCommon * a.idNameBPR.localeCompare(b.idNameBPR)
     });
    });  
    this._change.detectChanges();
}

sortNameOrderHC = +1;

orderSortCommonHC() {
  this.sortNameOrderHC *= -1;
  this.listHCSpecialWeapons.sort((a, b) => {
    return this.sortNameOrderHC * (a.hc - b.hc);
  });
}

updateDescription() {
  // Count total received results (sum of all bluePrintReceivedHC arrays)
  let totalResults = 0;
  this.listHCSpecialWeapons.forEach(weapon => {
    if (weapon.bluePrintReceivedHC) {
      totalResults += weapon.bluePrintReceivedHC.length;
    }
  });
  this.description = `Showing ${totalResults} received results`;
}

}
