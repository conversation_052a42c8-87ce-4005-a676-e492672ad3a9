import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { StoryBoxService } from 'src/app/services/story-box.service';
import { OptionBoxService } from 'src/app/services/option-box.service';
import { OptionService } from 'src/app/services/option.service';
import { DilemmaBoxService } from 'src/app/services';
import { StoryBox, OptionBox, DilemmaBox } from 'src/app/lib/@bus-tier/models';
import { DialogueTreeUtilityService } from 'src/app/services/dialogue-tree-utility.service';

@Component({
  selector: 'dialogue-tree-layer',
  templateUrl: './dialogue-tree-layer.html',
  styleUrls: ['./dialogue-tree-layer.scss'],
})
/**
 * Dialogue tree layer component that represents a single layer in the dialogue tree.
 * Each layer can contain different types of dialogue boxes (StoryBox, OptionBox, DilemmaBox)
 * and handles the display of branches, roadblocks, and interactive elements.
 */
export class DialogueTreeLayerComponent implements OnInit
{
  // Layer configuration
  @Input() public isDeadLayer: boolean = false;
  @Input() public isDeadEnd: boolean = false;
  @Input() public parentIsDialogueOption: boolean = false;
  @Input() layerType: string;
  @Input() boxId: string;
  @Input() previousBoxId: string;
  @Input() inPaths: boolean[];
  @Input() outPaths: boolean[];

  // Interactive state for user selections
  @Input() public selectedChoiceOptions: Map<string, string> = new Map();
  @Input() public selectedInvestigationOptions: Map<string, Set<string>> = new Map();
  @Input() public selectedDilemmaOptions: Map<string, string> = new Map();
  @Input() public roadblockEvaluationEnabled: boolean = true;
  @Input() public hasPreviousLayerOptionRoadblocks: boolean = false;

  // Event outputs for user interactions
  @Output() public choiceOptionSelected = new EventEmitter<{optionBoxId: string, optionId: string | null}>();
  @Output() public investigationOptionSelected = new EventEmitter<{optionBoxId: string, optionId: string, selected: boolean}>();
  @Output() public dilemmaOptionSelected = new EventEmitter<{dilemmaBoxId: string, dilemmaId: string | null}>();

  // Data models for different dialogue element types
  public storyBox: StoryBox = undefined;
  public optionBox: OptionBox = undefined;
  public dilemmaBox: DilemmaBox = undefined;
  public optionsIds: string[] = undefined;
  public dilemmasIds: string[] = undefined;
  public diceNegativeOutcomeIds: string[] = [];

  // Roadblock detection and calculations
  public hasRoadblocks: boolean = false;
  public roadblockCount: number = 0;
  public incomingBranchRoadblockCount: number = 0;
  public outgoingBranchRoadblockCount: number = 0;

  constructor(
    private _optionBoxService: OptionBoxService,
    private _optionService: OptionService,
    private _storyBoxService: StoryBoxService,
    private _dilemmaBoxService: DilemmaBoxService,
    private _dialogueTreeUtility: DialogueTreeUtilityService
  ) {}

  /**
   * Initialize the component by identifying the box type and loading appropriate data.
   * Detects roadblocks and sets up the layer based on the box ID prefix.
   */
  public ngOnInit(): void
  {
    const boxType = this._dialogueTreeUtility.detectBoxType(this.boxId);

    if (boxType === 'StoryBox') {
      // Handle StoryBox
      this.storyBox = this._storyBoxService.svcFindById(this.boxId);
      this.detectRoadblocksForStoryBox();
    }
    else if (boxType === 'OptionBox') {
      // Handle OptionBox (Choice/Investigation)
      this.optionBox = this._optionBoxService.svcFindById(this.boxId);
      if (this.optionBox) {
        this.optionsIds = this.optionBox.optionIds;
        this.collectDiceNegativeOutcomes();
        this.detectRoadblocksForOptionBox();
      }
    }
    else if (boxType === 'DilemmaBox') {
      // Handle DilemmaBox
      this.dilemmaBox = this._dilemmaBoxService.svcFindById(this.boxId);
      if (this.dilemmaBox) {
        this.dilemmasIds = this.dilemmaBox.optionDilemmaIds;
        this.detectRoadblocksForDilemmaBox();
      }
    }
    else if (this.boxId) {
      // Log warning for unrecognized box ID format
      console.warn(`Unrecognized box ID format: ${this.boxId}`);
    }
  }

  /**
   * Detect and calculate roadblocks for StoryBox elements.
   * Handles both own roadblocks and parent option roadblocks for branch consistency.
   */
  private detectRoadblocksForStoryBox(): void {
    if (!this.storyBox) return;

    const roadblockInfo = this._dialogueTreeUtility.calculateRoadblockInfo(this.storyBox.id, this.previousBoxId);
    this.roadblockCount = roadblockInfo.roadblockCount;
    this.hasRoadblocks = roadblockInfo.hasRoadblocks;
    this.incomingBranchRoadblockCount = roadblockInfo.incomingBranchRoadblockCount;
    this.outgoingBranchRoadblockCount = roadblockInfo.outgoingBranchRoadblockCount;
  }

  /**
   * Detect and calculate roadblocks for OptionBox elements and their child options.
   * Uses the maximum roadblock count across all children for consistent branch heights.
   */
  private detectRoadblocksForOptionBox(): void {
    if (!this.optionBox) return;

    const roadblockInfo = this._dialogueTreeUtility.calculateRoadblockInfo(this.optionBox.id, this.previousBoxId);
    this.roadblockCount = roadblockInfo.roadblockCount;
    this.hasRoadblocks = roadblockInfo.hasRoadblocks;
    this.incomingBranchRoadblockCount = roadblockInfo.incomingBranchRoadblockCount;
    this.outgoingBranchRoadblockCount = roadblockInfo.outgoingBranchRoadblockCount;
  }

  /**
   * Detect and calculate roadblocks for DilemmaBox elements and their child dilemmas.
   * Uses the maximum roadblock count across all children for consistent branch heights.
   */
  private detectRoadblocksForDilemmaBox(): void {
    if (!this.dilemmaBox) return;

    const roadblockInfo = this._dialogueTreeUtility.calculateRoadblockInfo(this.dilemmaBox.id, this.previousBoxId);
    this.roadblockCount = roadblockInfo.roadblockCount;
    this.hasRoadblocks = roadblockInfo.hasRoadblocks;
    this.incomingBranchRoadblockCount = roadblockInfo.incomingBranchRoadblockCount;
    this.outgoingBranchRoadblockCount = roadblockInfo.outgoingBranchRoadblockCount;
  }



  /**
   * Collect dice negative outcome StoryBox IDs from this OptionBox's options.
   * These represent the failure outcomes for dice-based options.
   */
  private collectDiceNegativeOutcomes(): void {
    if (!this.optionBox?.optionIds) return;

    this.diceNegativeOutcomeIds = this._dialogueTreeUtility.collectDiceNegativeOutcomes(this.optionsIds);
  }

  /**
   * Handle choice option selection from child components.
   * Forwards the event to the parent component.
   */
  public onChoiceOptionSelected(optionBoxId: string, optionId: string | null): void {
    this.choiceOptionSelected.emit({ optionBoxId, optionId });
  }

  /**
   * Handle investigation option selection from child components.
   * Forwards the event to the parent component.
   */
  public onInvestigationOptionSelected(optionBoxId: string, optionId: string, selected: boolean): void {
    this.investigationOptionSelected.emit({ optionBoxId, optionId, selected });
  }

  /**
   * Handle dilemma option selection from child components.
   * Forwards the event to the parent component.
   */
  public onDilemmaOptionSelected(dilemmaBoxId: string, dilemmaId: string | null): void {
    this.dilemmaOptionSelected.emit({ dilemmaBoxId, dilemmaId });
  }


  /**
   * Check if any options in this layer have roadblocks.
   * Used to conditionally apply CSS classes for proper spacing and styling.
   * @returns True if any option has roadblocks, false otherwise
   */
  public hasAnyOptionRoadblocks(): boolean {
    // Check regular options (roadblocks are on answer boxes)
    if (this.optionsIds && this._dialogueTreeUtility.checkOptionsForRoadblocks(this.optionsIds)) {
      return true;
    }

    // Check dice negative outcomes
    if (this.diceNegativeOutcomeIds) {
      for (const diceId of this.diceNegativeOutcomeIds) {
        if (this._dialogueTreeUtility.hasRoadblocks(diceId)) {
          return true;
        }
      }
    }

    // Check dilemmas (roadblocks are on dilemma answer boxes)
    if (this.dilemmasIds && this._dialogueTreeUtility.checkDilemmasForRoadblocks(this.dilemmasIds)) {
      return true;
    }

    return false;
  }

  /**
   * Check if the previous layer was an option layer (ChoiceBox/InvestigationBox/DilemmaBox).
   * Used to determine if this layer's branches should use Layer 2 logic.
   * @returns True if previous layer contains options, false otherwise
   */
  public isPreviousLayerOptionLayer(): boolean {
    return this._dialogueTreeUtility.isOptionLayer(this.previousBoxId);
  }

  /**
   * Check if the previous layer was specifically an investigation box.
   * Used to determine if this layer's branches should hide based on selection.
   * @returns True if previous layer is an InvestigationBox, false otherwise
   */
  public isPreviousLayerInvestigationBox(): boolean {
    return this._dialogueTreeUtility.isInvestigationBox(this.previousBoxId);
  }

  /**
   * Get the previous layer's option box ID.
   * Used to pass to branches for selection-based hiding.
   * @returns The previous box ID if it's an option layer, undefined otherwise
   */
  public getPreviousOptionBoxId(): string | undefined {
    return this.isPreviousLayerOptionLayer() ? this.previousBoxId : undefined;
  }

  /**
   * Get the previous layer's option IDs for branch selection logic.
   * Must match the structure of inPaths, including dice negative outcomes.
   * @returns Array of option/dilemma IDs from the previous layer
   */
  public getPreviousOptionIds(): string[] {
    return this._dialogueTreeUtility.getPreviousOptionIds(this.previousBoxId);
  }

  /**
   * Get combined option IDs including dice failures for branch selection logic.
   * This ensures branches are aware of both regular options and dice failures.
   * Now returns the same order as the visual layout (matching getCombinedOptionIdsInOrder).
   * @returns Array combining regular options and dice negative outcomes in visual order
   */
  public getCombinedOptionIds(): string[] {
    // Use the same logic as getCombinedOptionIdsInOrder to maintain consistency
    return this.getCombinedOptionIdsInOrder();
  }

  /**
   * Get options in the same order as getPreviousOptionIds() creates them.
   * This matches the visual layout with the array structure used by branch logic.
   * Each regular option is immediately followed by its dice variant (if it exists).
   */
  public getCombinedOptionIdsInOrder(): string[] {
    if (!this.optionsIds || this.optionsIds.length === 0) {
      return [];
    }

    const result: string[] = [];

    this.optionsIds.forEach(optionId => {
      // Add the regular option ID
      result.push(optionId);

      // Add dice negative outcome StoryBox ID if it exists
      const option = this._optionService.svcFindById(optionId);
      if (option?.answerBoxNegativeId) {
        result.push(option.answerBoxNegativeId);
      }
    });

    return result;
  }

  /**
   * Determine the leaf type for a given option ID in the combined array.
   * This handles both regular options and dice failure StoryBoxes.
   */
  public getLeafTypeForOptionId(optionId: string): string {
    // Check if this is a regular option
    if (this.optionsIds && this.optionsIds.includes(optionId)) {
      return this.optionBox?.type === 0 ? 'ChoiceOption' : 'InvestigationOption';
    }

    // Check if this is a dice failure StoryBox
    if (this.diceNegativeOutcomeIds && this.diceNegativeOutcomeIds.includes(optionId)) {
      return 'StoryBox';
    }

    // Default fallback
    return 'StoryBox';
  }
}
