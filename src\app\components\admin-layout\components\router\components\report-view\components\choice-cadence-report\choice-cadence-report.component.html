<div class="row" style="z-index:-1 !important; position:relative !important"> 
    
    <div class="col-md-12">
    <div class="card" style="height:50px;">
        <div class="card-body; text-justify;" style="text-align: center;" >
            <p class="card-title;" >Dialogue Boxes Before Choices Boxes</p>
        
        </div>
    </div>

    <div class="col-md-4">
        <div class="report-table-wrapper">
            <table class="table report-table table-striped">
                <thead class="sticky" style="top: 0px;">
                    <tr>
                        <th style="text-align:center;background-color: #4B4B4B !important;" scope="col" colspan="2">Dialogue terminated in period mark</th>
                    </tr>
                    <tr>
                        <th style="text-align: right; "
                            class="th-clickable"
                            (click)="sortListByCharacterName(regSpeech)">
                            Speaker Name
                        </th>
                        <th style="text-align: right"
                            class="th-clickable"
                            (click)="sortListByMessage(regSpeech)">
                            Message
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <ng-container *ngFor="let speech of regSpeech">
                        <tr class="th-clickable" (click)="access(speech.id)">
                            <td class="td-20px">{{ (speech.speakerId | character)?.name }}</td>
                            <td class="td-auto"
                            [innerHtml]="
                            speech.message | rpgFormatting
                            "></td>
                        </tr>
                    </ng-container>
                </tbody>
            </table>
        </div>
    </div>

    <div class="col-md-4">
        <div class="report-table-wrapper">
            <table class="table report-table table-striped">
                <thead class="sticky" style="top: 0px;">
                    <tr>
                        <th style="text-align:center; background-color: #4B4B4B !important;" scope="col" colspan="2">Dialogue terminated in interogation mark</th>
                    </tr>
                    <tr>
                        <th style="text-align: right"
                            class="th-clickable"
                            (click)="sortListByCharacterName(questionSpeech)">
                            Speaker Name
                        </th>
                        <th style="text-align: right"
                            class="th-clickable"
                            (click)="sortListByMessage(questionSpeech)">
                            Message
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <ng-container *ngFor="let speech of questionSpeech">
                        <tr class="th-clickable" (click)="access(speech.id)">
                            <td class="td-20px">{{ (speech.speakerId | character)?.name }}</td>
                            <td class="td-auto"
                            [innerHtml]="
                            speech.message | rpgFormatting
                            "></td>
                        </tr>
                    </ng-container>
                </tbody>
            </table>
        </div>
    </div>

    <div class="col-md-4">
        <div class="report-table-wrapper">
            <table class="table report-table table-striped">
                <thead class="sticky" style="top: 0px;">
                    <tr>
                        <th style="text-align:center;background-color: #4B4B4B !important;" scope="col" colspan="2">All the others dialogues</th>
                    </tr>
                    <tr>
                        <th style="text-align: right"
                            class="th-clickable"
                            (click)="sortListByCharacterName(otherSpeech)">
                            Speaker Name
                        </th>
                        <th style="text-align: right"
                            class="th-clickable"
                            (click)="sortListByMessage(otherSpeech)">
                            Message
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <ng-container *ngFor="let speech of otherSpeech">
                        <tr class="th-clickable" (click)="access(speech.id)">
                            <td class="td-20px">{{ (speech.speakerId | character)?.name  }}</td>
                            <td class="td-auto"
                            [innerHtml]="
                            speech.message | rpgFormatting
                            "></td>
                        </tr>
                    </ng-container>
                </tbody>
            </table>
        </div>
    </div>

</div>

</div>
