<div class="row">
  <!-- START: Battle Minions Table -->
  <div class="col-md-4">
    <div class="report-table-wrapper">
      <table class="table report-table table-striped table-fixed">
        <thead class="sticky">
          <tr>
            <th></th>
            <th class="th-clickable center" (click)="sortsByParameter('hierarchyCode', true)">Level</th>
            <th class="th-clickable center" (click)="sortsByParameter('blockGrind', true)">Blocks</th>
            <th>Battle Minions</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let battleLevel of minionBattleLevels; let i = index">
            <tr (click)="accessLevel(battleLevel.id)" class="td-clickable">
              <td class="td-20px">
                <div class="circle" [ngStyle]="{
                  'background-color': battleLevel | typeColor
                }"></div>
              </td>
              <td>
                {{ [battleLevel.id] | location }}
              </td>
              <td class="td-auto center">
                <i *ngIf="battleLevel.blockGrind" class="btn-icon pe-7s-shield"></i>
                <i style="position: relative" placement='top' delay='250' ttWidth="auto" ttAlign="left" ttPadding="10px"
                  tooltip="Grind blocked" *ngIf="(battleLevel | review).match3BlockedBy.length > 0"
                  style="position: relative" placement='top' delay='250' ttWidth="auto" ttAlign="left" ttPadding="10px"
                  tooltip="Battle Blocked by {{
                  (battleLevel | review).match3BlockedBy | location
                }}" class="btn-icon pe-7s-close-circle"></i>
              </td>
              <td class="td-auto">
                <app-battle-characters-table [level]="battleLevel" [editable]="true">
                </app-battle-characters-table>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
  <!-- END: Battle Minions Table -->

  <!-- START: Bosses Table -->
  <div class="col-md-8">
    <div class="report-table-wrapper">
      <table class="table report-table table-striped">
        <thead class="sticky" style="top: 0px;">
          <tr>
            <th></th>
            <th class="th-clickable center" (click)="sortsByParameter('hierarchyCode')"><i
                style="font-size: 12px !important; font-style: normal;">Level</i><i></i></th>
            <th class="th-clickable center" (click)="sortsByParameter('blockGrind')" style="text-transform: none;"><i
                style="font-size: 12px !important; font-style: normal;">GRIND</i>
              <i class="pe-7s-info" style="position: relative; left: 6%; top: 3px;" placement='top' delay='250'
                ttWidth="auto" ttAlign="center" ttPadding="10px" tooltip="Battle not eligeble for Grind sessions"></i>
            </th>
            <th class="th-clickable center" (click)="sortsByParameter('blockMatch3')" style="text-transform: none;padding-right: 10px;"><i
                style="font-size: 12px !important; font-style: normal;">BLOCK BATTLE</i>
              <i class="pe-7s-info" style="position: relative; left: 6%; top: 3px;" placement='top' delay='250'
                ttWidth="auto" ttAlign="center" ttPadding="10px" tooltip="Battle is blocked"></i>
            </th>
            <th class="th-clickable center" (click)="sortsByParameter('firstAttack')" style="text-transform: none;"><i
                style="font-size: 12px !important; font-style: normal;">FIRST ATTACK</i>
              <i class="pe-7s-info" style="position: relative; left: 6%; top: 3px;" placement='top' delay='250'
                ttWidth="auto" ttAlign="center" ttPadding="10px" tooltip="Enemy has attack Priority"></i>
            </th>
            <th class="th-clickable center" (click)="sortsByParameter('hasCondition')" style="text-transform: none;"><i
                style="font-size: 12px !important; font-style: normal;">CONDITION</i>
              <i class="pe-7s-info" style="position: relative; left: 6%; top: 3px;" placement='top' delay='250'
                ttWidth="auto" ttAlign="center" ttPadding="10px" tooltip="Certain death conditions"></i>
            </th>
            <th style="width: 160px !important;"><i style="font-size: 12px !important; font-style: normal; left: 5px;">Battle Bosses</i></th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let battleLevel of bossBattleLevels; let i = index">
            <tr (click)="accessLevel(battleLevel.id)" class="td-clickable">
              <td class="td-20px">
                <div class="circle" [ngStyle]="{
                  'background-color': battleLevel | typeColor
                }"></div>
              </td>
              <td>
                {{ [battleLevel.id] | location }}
              </td>
              <td class="td-auto center">
                <i [ngClass]="{'warning' : getIconColor(battleLevel.id), 'error': !getIconColor(battleLevel.id)}">
                  <i *ngIf="battleLevel.blockGrind" class="btn-icon pe-7s-shield"></i>
                </i>
              </td>
              <td>
                <div style="left: 30% !important; position: relative !important;">
                  <div *ngIf="(battleLevel | checkForBlockedMatches)">
                    <i *ngIf="(battleLevel.dialogueIds | dialogueByType : DialogueType.END)?.boxIds?.length > 0"
                      [ngClass]="'btn-icon pe-7s-close-circle ' +
                            (((battleLevel.dialogueIds | dialogueByType : DialogueType.END)?.boxIds?.length > 0) ? 'block-error' : 'blocked')"
                      style="position: relative; left: 25%; top: 3px;" placement='top' delay='250' ttWidth="auto"
                      ttAlign="left" ttPadding="10px"
                      tooltip='This level has content in its End Dialog, this may cause anomalous behaviour'></i>
                    <i *ngIf="(battleLevel.dialogueIds | dialogueByType : DialogueType.END)?.boxIds?.length == 0 || !(battleLevel.dialogueIds | dialogueByType : DialogueType.END)?.boxIds?.length"
                      [ngClass]="'btn-icon pe-7s-close-circle ' +
                            (((battleLevel.dialogueIds | dialogueByType : DialogueType.END)?.boxIds?.length > 0) ? 'block-error' : 'blocked')"></i>
                  </div>
                </div>
              </td>
              <td class="td-auto center">
                <div *ngIf="battleLevel.firstAttack">
                  <img src="assets/img/icons/swords-icon.png" />
                </div>
              </td>
              <td>
                <div *ngIf="battleLevel.conditionIds.length > 0" [title]="getConditionDisplay(battleLevel.conditionIds)"
                  class="custom-tooltip" data-tooltip="Test">
                  <div style="left: 30% !important; position: relative !important;"></div>
                  <img src="assets/img/icons/skull-icon.png" style="margin-left: 35px; white-space: pre-wrap;"
                    *ngIf="instantDeathStyling(battleLevel) == 'black'" />
                  <img src="assets/img/icons/skull-icon-orange.png" style="margin-left: 35px; white-space: pre-wrap;"
                    *ngIf="instantDeathStyling(battleLevel) == 'yellow'" (click)="routeToProblemEvent(battleLevel)"
                    style="position: relative" placement='top' delay='250' ttWidth="auto" ttAlign="left"
                    ttPadding="10px" tooltip="Item delivered in a Return A. \ Return B. \ Release Dialog."
                    placement="top" />
                  <img src="assets/img/icons/skull-icon-red.png" style="margin-left: 35px; white-space: pre-wrap;"
                    *ngIf="instantDeathStyling(battleLevel) == 'red'" (click)="routeToProblemEvent(battleLevel)"
                    style="position: relative" placement='top' delay='250' ttWidth="auto" ttAlign="left"
                    ttPadding="10px"
                    tooltip="Item delivered in an Choice Box. \ Investigation Box. \ Init has Block Battle. \ Item no delivered"
                    placement="top" />
                </div>
              </td>
              <td class="td-auto" style="width: 160px !important; display: flex;">
                <app-battle-characters-table [level]="battleLevel" [editable]="true">
                </app-battle-characters-table>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
  <!-- END: Bosses Table -->
</div>