import { Component } from '@angular/core';
import { KnowledgeCheck } from 'src/app/lib/@bus-tier/models';
import { AttributeCheckService, KnowledgeCheckService } from 'src/app/services';

@Component({
  selector: 'app-knowledge-check',
  templateUrl: './knowledge-check.component.html',
  styleUrls: ['./knowledge-check.component.scss']
})
export class KnowledgeCheckComponent {

  listDiceFrustration: KnowledgeCheck;
  titles = ['light', 'moderate', 'critical'];
  description: string;

  constructor(
    public _knowledgeCheckService: KnowledgeCheckService
  ) { }

  async ngOnInit() {
    this._knowledgeCheckService.toFinishLoading();

    setTimeout(async () => {
      this.listDiceFrustration = this._knowledgeCheckService.models[0];

      if (this.listDiceFrustration === undefined) {
        this.listDiceFrustration = await this._knowledgeCheckService.createNewFailureLevel();
      }
      this.description = `Showing 3 results`;
    }, 70);
  }

  getValueTitle(title: string, index: number): any {
    if (title === 'light') {
      return this.listDiceFrustration?.light.length > 0 ? this.listDiceFrustration.light[index] : null;
    } else if (title === 'moderate') {
      return this.listDiceFrustration?.moderate.length > 0 ? this.listDiceFrustration.moderate[index] : null;
    } else if (title === 'critical') {
      return this.listDiceFrustration?.critical.length > 0 ? this.listDiceFrustration.critical[index] : null;
    }
  }

  changeFrustationValue(title: string, index: number, dice: string) {
    if (title === 'light') {
      this.listDiceFrustration.light[index] = dice == '' ? null : +dice;
    } else if (title === 'moderate') {
      this.listDiceFrustration.moderate[index] = dice == '' ? null : +dice;
    } else if (title === 'critical') {
      this.listDiceFrustration.critical[index] = dice == '' ? null : +dice;
    }

    this._knowledgeCheckService.svcToModify(this.listDiceFrustration);
    this._knowledgeCheckService.toSave();
  }

}
