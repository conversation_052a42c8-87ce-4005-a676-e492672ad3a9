import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class LabelColorService {
  private labelColorMap: Map<string, string> = new Map();
  
  // Predefined color palette for labels (strong, visible, distinctly different colors)
  private colorPalette: string[] = [
    '#e6194b', // Vivid Red
    '#3cb44b', // Bright Green
    '#ffe119', // Canary Yellow
    '#0082c8', // Strong Blue
    '#f58231', // Orange
    '#911eb4', // Purple
    '#46f0f0', // Cyan
    '#f032e6', // Hot Pink
    '#d2f53c', // Lime Yellow
    '#fabebe', // Light Pink
    '#008080', // Teal
    '#e6beff', // Lavender
    '#aa6e28', // <PERSON>
    '#6a5acd', // Slate Blue
    '#800000', // Maroon
    '#aaffc3', // Mint
    '#808000', // Olive
    '#ffd8b1', // Peach
    '#000080', // Navy Blue
    '#a9a9ff', // Light Periwinkle
    '#ff69b4', // Bubblegum Pink
    '#cd5c5c', // Indian Red
    '#20b2aa', // Light Sea Green
    '#ff6347', // Tomato
    '#7fffd4', // Aquamarine
    '#daa520', // Goldenrod
    '#40e0d0', // Turquoise
  ];
  
  private usedColorIndices: Set<number> = new Set();

  constructor() {}

  /**
   * Get a unique color for a label. If the label already has a color, return it.
   * If not, assign a new unique color.
   */
  getColorForLabel(label: string): string {
    if (!label || label.trim() === '') {
      return '#CCCCCC'; // Default gray for empty labels
    }

    const normalizedLabel = label.trim().toLowerCase();
    
    // Return existing color if already assigned
    if (this.labelColorMap.has(normalizedLabel)) {
      return this.labelColorMap.get(normalizedLabel)!;
    }

    // Find next available color
    let colorIndex = 0;
    while (this.usedColorIndices.has(colorIndex) && colorIndex < this.colorPalette.length) {
      colorIndex++;
    }

    // If we've used all predefined colors, generate a random one
    let color: string;
    if (colorIndex >= this.colorPalette.length) {
      color = this.generateRandomColor();
    } else {
      color = this.colorPalette[colorIndex];
      this.usedColorIndices.add(colorIndex);
    }

    // Store the mapping
    this.labelColorMap.set(normalizedLabel, color);
    return color;
  }

  /**
   * Get color for a spoke place text (extracts the label from the formatted text)
   */
  getColorForSpokePlace(spokeText: string): string {
    if (!spokeText) return '#CCCCCC';

    // Extract label from formatted text like "[ChoiceBox > Answer] AskLabel"
    const match = spokeText.match(/\]\s*(.+)$/);
    if (match && match[1]) {
      return this.getColorForLabel(match[1].trim());
    }

    return '#CCCCCC';
  }

  /**
   * Get color for a label with level matching logic
   * Returns default color if label has no roadblock match in the level
   */
  getColorForLabelWithMatching(label: string, hasRoadblockMatch: boolean): string {
    if (!hasRoadblockMatch) {
      return '#FFFFFF'; // White background for unmatched labels
    }
    return this.getColorForLabel(label);
  }

  /**
   * Get color for a roadblock with level matching logic
   * Returns default color if roadblock references a label not present in the level
   */
  getColorForRoadblockWithMatching(spokeText: string, hasLabelMatch: boolean): string {
    if (!hasLabelMatch) {
      return '#000000'; // Black background for unmatched roadblocks
    }
    return this.getColorForSpokePlace(spokeText);
  }

  /**
   * Extract label from spoke place text
   */
  extractLabelFromSpokeText(spokeText: string): string {
    if (!spokeText) return '';

    const match = spokeText.match(/\]\s*(.+)$/);
    return match && match[1] ? match[1].trim() : '';
  }

  /**
   * Generate a random color when predefined palette is exhausted
   */
  private generateRandomColor(): string {
    const hue = Math.floor(Math.random() * 360);
    const saturation = 60 + Math.floor(Math.random() * 40); // 60-100%
    const lightness = 50 + Math.floor(Math.random() * 30);  // 50-80%
    return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
  }

  /**
   * Remove a label color mapping (useful when labels are deleted)
   */
  removeLabelColor(label: string): void {
    if (!label) return;
    
    const normalizedLabel = label.trim().toLowerCase();
    const color = this.labelColorMap.get(normalizedLabel);
    
    if (color) {
      // Find and free up the color index if it was from predefined palette
      const colorIndex = this.colorPalette.indexOf(color);
      if (colorIndex !== -1) {
        this.usedColorIndices.delete(colorIndex);
      }
      
      this.labelColorMap.delete(normalizedLabel);
    }
  }

  /**
   * Get all currently assigned label-color mappings
   */
  getAllLabelColors(): Map<string, string> {
    return new Map(this.labelColorMap);
  }

  /**
   * Clear all color assignments (useful for testing or reset)
   */
  clearAllColors(): void {
    this.labelColorMap.clear();
    this.usedColorIndices.clear();
    console.log('🎨 Label color cache cleared - colors will be reassigned');
  }
}
