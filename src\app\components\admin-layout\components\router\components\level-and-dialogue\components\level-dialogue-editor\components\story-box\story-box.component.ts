import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  Output,
} from '@angular/core';
import {
  Area,
  Character,
  Dialogue,
  Item,
  Level,
  Mission,
  SpokePlace,
  StoryBox,
  StoryProgress,
} from 'src/app/lib/@bus-tier/models';
import { RoadBlock } from 'src/app/lib/@bus-tier/models/RoadBlock';
import { Typing } from 'src/app/lib/@pres-tier';
import {
  AreaService,
  LevelService,
  ReviewService,
} from 'src/app/services';
import { DialogueService } from 'src/app/services/dialogue.service';
import { EventService } from 'src/app/services/event.service';
import { MarkerService } from 'src/app/services/marker.service';
import { RoadBlockService } from 'src/app/services/road-block.service';
import { SpeechService } from 'src/app/services/speech.service';
import { StoryBoxService } from 'src/app/services/story-box.service';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Alert } from 'src/lib/darkcloud';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { RoadBlockType } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard';
import { EventType, GameTypes } from 'src/lib/darkcloud/dialogue-system';
import { LevelHelperService } from './../../../../../../../../../../services/level-helper.service';
import { Router } from '@angular/router';
import { HighlightElement } from 'src/lib/others';

interface SpokePlaceHelper {
  elementId: string;
  text: string;
  label: string;
  component: string;
}

@Component({
  selector: 'app-story-box',
  templateUrl: './story-box.component.html',
  styleUrls: ['./story-box.component.scss'],
})
export class StoryBoxComponent {
  @Input() index: number;
  @Input() dialogue: Dialogue;
  @Input() storyBox: StoryBox;
  @Input() preloadedSpeakers: Character[];
  @Input() preloadedMissionsOfArea: Mission[];
  @Input() language: string;
  @Output() refresh: EventEmitter<void> = new EventEmitter();

  storyBoxes: StoryBox[] = [];
  public hasLabelText: boolean = false;
  public itemList: Item[] = [];
  roadBlocksUseds: RoadBlock[] = [];
  public roadblockId: string;
  timeout;
  usedRoadBlocks = [];
  usedOnLevels = [];
  isTextValid = true;
  label: string;
  isUsedRoad = false;
  listSpokePlace: SpokePlace[] = [];
  listStoryProgressIds = [];

  toMoveStoryProgressFunc: (marker: any) => void;
  toRemoveStoryProgressFunc: (marker: any) => void;
  toRemoveProcessConditionFunc: (roadblock: any) => void;
  trackById(index: number, sp: StoryProgress): string {
    return sp.id;
  }

  constructor(
    private _userSettingsService: UserSettingsService,
    private _dialogueService: DialogueService,
    private _storyBoxService: StoryBoxService,
    private _speechService: SpeechService,
    private _eventService: EventService,
    private _markerService: MarkerService,
    private _router: Router,
    private _roadblockService: RoadBlockService,
    private _change: ChangeDetectorRef,
    protected _levelHelperService: LevelHelperService,
    private _areaService: AreaService,
    private _levelService: LevelService,
    private _reviewService: ReviewService,     

  ) {
    this.toMoveStoryProgressFunc = this.toMoveStoryProgress.bind(this);
    this.toRemoveStoryProgressFunc = this.toRemoveStoryProgress.bind(this);
    this.toRemoveProcessConditionFunc = this.toRemoveRoadblock.bind(this);
  }


  async ngOnInit() {
    await this._roadblockService.toFinishLoading(); 
    this._levelHelperService.toFinishLoading();
    this.listSpokePlace = this._levelHelperService.models;
  
    setTimeout(() => {
     if (this.storyBox && this.storyBox.AndOrCondition == undefined) {
      this.storyBox['AndOrCondition'] = 'OR';
    }
    this.storyBox = this.checkEventsAndMarkers(this.storyBox);
    this.getRoadBlocks();
    this.roadblocksForKeyInformation();
    }, 500);
  }

  checkEventsAndMarkers(storyBox: StoryBox) {

    const orderStoryProgressIds = storyBox.storyProgressIds;
    this._speechService.models.forEach((speech) => {
      if (speech.id.includes(storyBox.id)) {
        this.listStoryProgressIds.push(speech.id);
      }
    });
  
    this._eventService.models.forEach((event) => {
      if (event.id.includes(storyBox.id)) {
        this.listStoryProgressIds.push(event.id);
      }
    });
  
    this._markerService.models.forEach((marker) => {
      if (marker.id.includes(storyBox.id)) {
        this.listStoryProgressIds.push(marker.id);
      }
    });
  
    const newListStoryProgressIds = this.maintainIdsPosition(this.listStoryProgressIds, orderStoryProgressIds);

    storyBox.storyProgressIds = [];
    storyBox.storyProgressIds = newListStoryProgressIds;
    this._storyBoxService.svcToModify(storyBox);
    return storyBox;
  }

  //Método criado para manter os IDs existentes na mesma posição
maintainIdsPosition(listStoryProgressIds: string[], storyBoxStoryProgressIds: string[]): string[] {
  const result = [];
  for (let i = 0; i < storyBoxStoryProgressIds.length; i++) { 
    if (listStoryProgressIds.includes(storyBoxStoryProgressIds[i])) {
      result.push(storyBoxStoryProgressIds[i]);
    }
  }
  return result;
}

  async roadblocksForKeyInformation() {
    const roadblocks = this._roadblockService.models.filter((roadblock) => {
      return roadblock.spokeElementId === this.storyBox?.id;
    });

    for (const roadblock of roadblocks) {
      const dialogueIds = this._dialogueService.models.filter((dialogue) => {
        return (
          roadblock.StoryBoxId &&
          !dialogue.id.includes('ML') &&
          roadblock.StoryBoxId.includes(dialogue.id)
        );
      });

      for (const dialogue of dialogueIds) {
        if (dialogue.boxIds.length > 0) {
          const areaId = Area.getSubIdFrom(dialogue.id);
          const area = await this._areaService.svcFindById(areaId);
          if (!area) return;
  
          const levelId = Level.getSubIdFrom(dialogue.id);
          const level = await this._levelService.svcFindById(levelId);
          const dialogueId = Dialogue.getSubIdFrom(dialogue.id, 'PT-BR');
          const dialogueData = await this._dialogueService.svcFindById(
            dialogueId
          );
          const hierarchyCode = area.hierarchyCode;
          const levelIndex = this._reviewService.reviewResults[levelId]?.index;
          const type = GameTypes.dialogueTypeDisplay[+dialogueData.type];
  
          this.usedOnLevels.push(
            `[${hierarchyCode}] ${levelIndex} "${level.name}" (${type})`
          );
        }      
      }    
      this.usedRoadBlocks.push(roadblock);
    }
    
  }

  getRoadBlocks() {
    //verif se tem um roadblock com o id do answerBox usando a label
    this._roadblockService.models.find((sp) => {
      if (sp.spokeElementId == this.storyBox?.id) {
        this.hasLabelText = true;
      }
    });
    
    //verif se existe roadblock para o answerBox
    this._roadblockService.models.forEach((road) => {
      if (road.StoryBoxId == this.storyBox?.id) {
        this.roadBlocksUseds.push(road);
      }
    }); 
  }

  isUsedRoadblock(story: StoryBox): boolean {
    // Retorna true se encontrar um elemento em usedRoadBlocks com o mesmo spokeElementId que o id do answerBox
    const isUsed = this.usedRoadBlocks.some(
      (rb) => rb.spokeElementId === story.id
    );

    if (isUsed) {
      this.isUsedRoad = true;
    } else {
      this.isUsedRoad = false;
    }
    return isUsed;
  }

  public updateInformation<TKey extends keyof Data.Internal.Base>(
    id: string,
    key: TKey,
    value: Data.Internal.Base[TKey]
  ) {
    this._userSettingsService.updateInformation(id, key, value);
  }

  public async toMove(transpose: number) {
    if (this.dialogue.boxIds.length <= 1) return;

    const oldIndex = this.dialogue.boxIds.indexOf(this.storyBox?.id) + 0;
    const newIndex = oldIndex + transpose;
    if (!this.dialogue.boxIds[newIndex]) return;

    this.dialogue.boxIds[oldIndex] = this.dialogue.boxIds[newIndex];
    this.dialogue.boxIds[newIndex] = this.storyBox?.id;
    await this._dialogueService.svcToModify(this.dialogue);
    this._change.detectChanges();
  }

  public async toRemove(storyBox: StoryBox) {

    if (await Alert.showRemoveAlert(storyBox.id)) {
      await this._dialogueService.removeBox(this.dialogue, storyBox?.id);
      await this._storyBoxService.toRemoveStory(storyBox.id);    
      await this.removeSubComponent(storyBox);
       this._userSettingsService.removeIdObjectInformations(storyBox.id);          
   
      this._roadblockService.models.forEach(async (roadblock) => {
        if (roadblock.spokeElementId === storyBox.id) {
          roadblock.spokeElementId = undefined;
          await this._roadblockService.svcToModify(roadblock);
        }
      });   
  
      this._levelHelperService.models.forEach(async (places) => {
        if (places.elementId === storyBox.id) {
          await this._levelHelperService.svcToRemove(places.id);
        }
      });      
    // this._change.detectChanges();
     this.refresh.emit(); 
    } 

  }

  async removeSubComponent(storyBox: StoryBox) {

    if (storyBox.storyProgressIds.length > 0) {

      for (let index = 0; index < storyBox.storyProgressIds.length; index++) {     
        await this._speechService.svcToRemove(storyBox.storyProgressIds[index]);
        await this._eventService.svcToRemove(storyBox.storyProgressIds[index]);
        await this._markerService.svcToRemove(storyBox.storyProgressIds[index]);
        await this._roadblockService.svcToRemove(storyBox.storyProgressIds[index]);
       }
    }
  }

public async toAddSpeech() {
  try {
    const speech = await this._speechService.svcPromptCreateNew(
      this.storyBox?.id
    );
    await this._speechService.srvAdd(speech);
    await this._storyBoxService.addStoryProgress(this.storyBox, speech.id);
    this._change.detectChanges();
    
    // Scroll para o novo componente com efeito suave
    setTimeout(() => {
      HighlightElement(speech.id, 110, true, 'transparent');
    }, 100);
  } catch (error) {
    Alert.showError(error);
  }
}

  public async toAddEvent() {
    try {
      const event = await this._eventService.svcPromptCreateNew(
        this.storyBox?.id
      );
      await this._eventService.srvAdd(event);
      await this._storyBoxService.addStoryProgress(this.storyBox, event.id);
      this._change.detectChanges();
    } catch (error) {
      Alert.showError(error);
    }
  }

  public async toAddMissionEvent() {
    let event = await this._eventService.svcPromptCreateNew(this.storyBox?.id);
    event.type = EventType.ASSIGN_MISSION;
    await this._eventService.srvAdd(event);
    await this._storyBoxService.addStoryProgress(this.storyBox, event.id);
    this._change.detectChanges();
  }

  public async toAddItemEvent() {
    let event = await this._eventService.svcPromptCreateNew(this.storyBox?.id);
    event.type = EventType.GIVE_ITEM;
    await this._eventService.srvAdd(event);
    await this._storyBoxService.addStoryProgress(this.storyBox, event.id);
    this._change.detectChanges();
  }

  public async toAddCinematicEvent() {
    let event = await this._eventService.svcPromptCreateNew(this.storyBox?.id);
    event.type = EventType.PLAY_VIDEO;
    await this._eventService.srvAdd(event);
    await this._storyBoxService.addStoryProgress(this.storyBox, event.id);
    this._change.detectChanges();
  }

  public async toAddLoopEvent() {
    let event = await this._eventService.svcPromptCreateNew(this.storyBox?.id);
    event.type = EventType.REFUSE_PAYMENT;
    await this._eventService.srvAdd(event);
    await this._storyBoxService.addStoryProgress(this.storyBox, event.id);
    this._change.detectChanges();
  }

  public async toAddBossEvent() {
    try {
      const marker = await this._markerService.svcPromptCreateNew(
        this.storyBox?.id
      );
      marker.levelId = undefined;
      marker.type = 0; //Pass this type here to make it be of boss event.
      await this._markerService.srvAdd(marker);
      await this._storyBoxService.addStoryProgress(this.storyBox, marker.id);
      this._change.detectChanges();
      this.ngOnInit();
    } catch (error) {
      Alert.showError(error);
    }
  }

  public async toAddMarker() {
    try {
      const marker = await this._markerService.svcPromptCreateNew(
        this.storyBox?.id
      );
      await this._markerService.srvAdd(marker);
      await this._storyBoxService.addStoryProgress(this.storyBox, marker.id);
      this._change.detectChanges();
    } catch (error) {
      Alert.showError(error);
    }
  }

  public async toMoveStoryProgress(sp: StoryProgress, transpose: number) {
    if (this.storyBox.storyProgressIds.length <= 1) return;

    const oldIndex = this.storyBox.storyProgressIds.indexOf(sp.id);
    const newIndex = oldIndex + transpose;
    if (!this.storyBox.storyProgressIds[newIndex]) return;

    this.storyBox.storyProgressIds[oldIndex] =
      this.storyBox.storyProgressIds[newIndex];
    this.storyBox.storyProgressIds[newIndex] = sp.id;
    await this._storyBoxService.svcToModify(this.storyBox);
    this._change.detectChanges();
  }

  public async toRemoveStoryProgress(sp: StoryProgress) {
    if (await Alert.showRemoveAlert(Typing.typeName(sp))) {
      await this._speechService.svcToRemove(sp.id);
      await this._eventService.svcToRemove(sp.id);
      await this._markerService.svcToRemove(sp.id);
      await this._storyBoxService.toRemoveStoryProgress(
        this.storyBox?.id,
        sp.id
      );
      this._change.detectChanges();
    }
  }

  public async toAddRoadblock() {
    let roadblock = await this._roadblockService.svcPromptCreateNew(
      this.storyBox?.id,
      RoadBlockType.OBTAINED_ITEM
    );
    this.roadblockId = roadblock.id;
    await this._roadblockService.srvAdd(roadblock);

    this.roadBlocksUseds.push(roadblock);
  }

  public async toRemoveRoadblock(roadblock: RoadBlock) {
    if (await Alert.showRemoveAlert(Typing.typeName(roadblock))) {
      this.roadblockId = undefined;
      this._roadblockService.svcToRemove(roadblock.id);
      this.roadBlocksUseds = this.roadBlocksUseds.filter(
        (rb) => rb.id != roadblock.id
      );
      this.ngOnInit();
      this._change.detectChanges();
    }
  }

  async changeLabel(event: Event) {
    const inputElement = event.target as HTMLInputElement;
    const text = inputElement?.value.trim();

    const existingLabels = {};
    this.listSpokePlace.forEach((spoke) => {
      existingLabels[spoke.originalLabel] = true;
    });

    const spoke = this.listSpokePlace.find(
      (spoke) => spoke.elementId === this.storyBox.id
    );

    if (text === '') {
      inputElement.value = '<<Label for progress condition>>';
      this.storyBox.label = undefined;
      this._storyBoxService.svcToModify(this.storyBox);
      this._storyBoxService.toSave();

      if (spoke) {
        this.removeBDLabelPlaces(this.storyBox.id, spoke.id);
      }

      this.refresh.emit();
    } else {
      if (existingLabels[text]) {
        // Label já existe na base de dados do Places
        Alert.showError('This Label ALREADY Exists!!', '');

        if (this.storyBox.label === undefined) {
          inputElement.value = '<<Label for progress condition>>';
          this._change.detectChanges();
        } else {
          inputElement.value = this.storyBox.label;
        }
        this.refresh.emit();
      } else {
        // Atualiza label
        if (spoke) {
          spoke.originalLabel = text;
          spoke.text = '[StoryBox] ' + text;
          this._levelHelperService.svcToModify(spoke);
        } else {
          // Adiciona label
          const helper: SpokePlaceHelper = {
            elementId: this.storyBox.id,
            label: text,
            component: '[StoryBox]',
            text: '[StoryBox] ' + text,
          };
          this._levelHelperService.createNewLevelHelper(helper);
        }
        inputElement.value = text;
        this.storyBox.label = text;
        this._storyBoxService.svcToModify(this.storyBox);
      }
    }
  }

  async removeBDLabelPlaces(idAnswerBox: string, idPlaces: string) {    

    await this._roadblockService.models.forEach((roadblock) => {
      if (roadblock.spokeElementId === idAnswerBox) {
        roadblock.spokeElementId = undefined;
        this._roadblockService.svcToModify(roadblock);
      }
    });

    await this._levelHelperService.svcToRemove(idPlaces);     
  }

  async chooseAndOrCondition(event) {
    //AND = FALSE, OR = TRUE.
    if (event.target.checked == false) this.storyBox['AndOrCondition'] = 'AND';
    else this.storyBox['AndOrCondition'] = 'OR';

    await this._storyBoxService.svcToModify(this.storyBox);
    await this._storyBoxService.toSave();
  }

}
