import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { Base } from './Base';

export class Silicatos  extends Base<Data.Hard.ISilicatos, Data.Result.ISilicatos> implements Required<Data.Hard.ISilicatos>
{
  private static generateId(index: number): string {
    return IdPrefixes.SILICATOS + index;
  }
  constructor(
    index: number,
    silicatoName: string,
    dataAccess: Silicatos['TDataAccess']
  ) {
    super(
      {
        hard: {
          id: Silicatos.generateId(index),
          silicatoName,
        },
      },
      dataAccess
    );
  }

  public get silicatoName(): string {
    return this.hard.silicatoName;
  }
  public set silicatoName(value: string) {
    this.hard.silicatoName = value;
  }

  public get baseLevelUnlock(): number {
    return this.hard.baseLevelUnlock;
  }
  public set baseLevelUnlock(value: number) {
    this.hard.baseLevelUnlock = value;
  }

  public get soulsCost(): number {
    return this.hard.soulsCost;
  }
  public set soulsCost(value: number) {
    this.hard.soulsCost = value;
  }

  public get timeToCreate(): number {
    return this.hard.timeToCreate;
  }
  public set timeToCreate(value: number) {
    this.hard.timeToCreate = value;
  }

  public get rubiesToSkip(): number {
    return this.hard.rubiesToSkip;
  }
  public set rubiesToSkip(value: number) {
    this.hard.rubiesToSkip = value;
  }

  public get subParticles(): number[] {
    return this.hard.subParticles;
  }
  public set subParticles(value: number[]) {
    this.hard.subParticles = value;
  }

  public get status(): number[] {
    return this.hard.status;
  }
  public set status(value: number[]) {
    this.hard.status = value;
  }
  
}
