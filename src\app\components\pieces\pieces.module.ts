import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { MajorModule } from "../../major.module";
import { BackButtonComponent } from './components/back-button/back-button.component';
import { ButtonGroupComponent } from './components/button-group/button-group.component';
import { CardHeaderComponent } from './components/card-header/card-header.component';
import { HeaderWithButtonsComponent } from './components/header-with-buttons/header-with-buttons.component';
import { HeaderSearchComponent } from './header-search/header-search.component';
import { ButtonClassFormatPipe } from './pipes/button-class-format.pipe';
import { RouterModule } from '@angular/router';

@NgModule({
  imports: [CommonModule, BrowserModule, FormsModule, RouterModule, MajorModule],
  declarations: [
    BackButtonComponent,
    CardHeaderComponent,
    HeaderWithButtonsComponent,
    ButtonGroupComponent,
    HeaderSearchComponent,
    ButtonClassFormatPipe,
  ],
  exports: [
    BackButtonComponent,
    CardHeaderComponent,
    HeaderWithButtonsComponent,
    HeaderSearchComponent,
    ButtonGroupComponent,
  ],
})
export class PiecesModule { }
