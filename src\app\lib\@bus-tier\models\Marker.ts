import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes, MarkerType } from 'src/lib/darkcloud/dialogue-system';
import { StoryProgress } from './StoryProgress';

export class Marker
  extends StoryProgress<Data.Hard.IMarker, Data.Result.IMarker>
  implements Required<Data.Hard.IMarker>
{
  public static generateId(storyBoxId: string, index: number): string {
    return storyBoxId + '.' + IdPrefixes.MARKER + index;
  }
  constructor(
    args: { index: number; storyBoxId: string; clonee?: Marker },
    dataAccess: Marker['TDataAccess']
  ) {
    const id = Marker.generateId(args.storyBoxId, args.index);
    if (args.clonee != undefined) {
      super({ hard: args.clonee.hard, newId: id }, dataAccess);
    } else {
      super({ hard: { id } }, dataAccess);
    }
  }
  public get idMessageType(): string {
    return this.hard.idMessageType;
  }
  public set idMessageType(value: string) {
    this.hard.idMessageType = value;
  }
  public get contextType(): string {
    return this.hard.contextType;
  }
  public set contextType(value: string) {
    this.hard.contextType = value;
  }

  public get microloopId(): string {
    return this.hard.microloopId;
  }
  public set microloopId(value: string) {
    this.hard.microloopId = value;
  }
  protected getInternalFetch() {
    return {};
  }
  public get levelId(): string {
    return this.hard.levelId;
  }
  public set levelId(value: string) {
    this.hard.levelId = value;
  }
  public get pin(): boolean {
    return this.hard.pin;
  }
  public set pin(value: boolean) {
    this.hard.pin = value;
  }
  public get characterId(): string {
    return this.hard.characterId;
  }
  public set characterId(value: string) {
    this.hard.characterId = value;
  }
  public get type(): MarkerType {
    return this.hard.type;
  }
  public set type(value: MarkerType) {
    this.hard.type = value;
  }

  public get amountRequired(): number {
    return this.hard.amountRequired;
  }
  public set amountRequired(value: number) {
    this.hard.amountRequired = value;
  }

  public get choosedCondition(): string {
    return this.hard.choosedCondition;
  }
  public set choosedCondition(value: string) {
    this.hard.choosedCondition = value;
  }

  public get unlockCondition(): string {
    return this.hard.unlockCondition;
  }
  public set unlockCondition(value: string) {
    this.hard.unlockCondition = value;
  }

  public get conditionOperator(): string {
    return this.hard.conditionOperator;
  }
  public set conditionOperator(value: string) {
    this.hard.conditionOperator = value;
  }
  public get origin(): number {
    return this.hard.origin;
  }
  public set origin(value: number) {
    this.hard.origin = value;
  }

  public get nameAtackAdvantage(): string {
    return this.hard.nameAtackAdvantage;
  }
  public set nameAtackAdvantage(value: string) {
    this.hard.nameAtackAdvantage = value;
  }
  public get knowledgePointValue(): number {
    return this.hard.knowledgePointValue;
  }
  public set knowledgePointValue(value: number) {
    this.hard.knowledgePointValue = value;
  }
  public get knowledgeId(): string {
    return this.hard.knowledgeId;
  }
  public set knowledgeId(value: string) {
    this.hard.knowledgeId = value;
  }

}
