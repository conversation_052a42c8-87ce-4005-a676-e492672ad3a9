import { Component} from '@angular/core';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { Maps } from 'src/app/lib/@bus-tier/models/Maps';
import { MapsService } from 'src/app/services/maps.service';
import { AreaService } from 'src/app/services';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';
import { Alert } from 'src/lib/darkcloud';
import { Button } from 'src/app/lib/@pres-tier/data';
import { SortingService } from 'src/app/services/sorting.service';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-maps',
  templateUrl: './maps.component.html',
  styleUrls: ['./maps.component.scss'],

})

export class MapsComponent extends  TranslatableListComponent<Maps> 
{

  areasList = [];
  isText : boolean = true;
  keywordsToSearch: Maps[] = [];
  keywordList = [];
  invertAlphabeticalOrder: boolean = true;
  invertAreaOrder: boolean = false;
  invertIdOrder: boolean = true;

  description;
  accentSensitive = false;
  caseSensitive = false;

  constructor(
    _activatedRoute: ActivatedRoute,
    protected _mapsService: MapsService,
    _userSettingsService: UserSettingsService,
    protected override _languageService: LanguageService,
    protected override _translationService: TranslationService,
    protected _areaService: AreaService,
    private _sortingService: SortingService
  ) 
  {
    super(_mapsService, _activatedRoute, _userSettingsService, 'name', _translationService, _languageService);
  }

  public override readonly addButtonTemplate: Button.Templateable = 
  {
    title: 'Add a new Map to the list',
    onClick: this.addMapOnList.bind(this),
    iconClass: 'pe-7s-plus',
    btnClass: Button.Klasses.FILL_GREEN,
  };

  async addMapOnList()
  {
    let name : string = await Alert.showPrompt("Enter Map Name:");
    if(this.isMapAlreadyCreated(name))
    {
      Alert.showError(`The map: ${name} already exists!`);
      return;
    }
    else if(name == '')
    {
      Alert.showError('You need to give a name to the map!');
      return;
    }
    else if(name == undefined) return;
    let newMap = await this._mapsService.createNewMap(name);
    this.lstResetHighlights();
    this.HighlightElement(newMap.id, 100, true);
    this.ngOnInit();
  }

  isMapAlreadyCreated(areaName: string)
  {
    for(let i = 0; i < this._mapsService.models.length; i++)
    {
      if(this._mapsService.models[i]?.name == areaName)
      {
        return true;
      }
    }
    return false;
  }

  public downloadMapsOrtography(maps: Maps) 
  {
    this._translationService.getMapsOrtography(maps, true);
  }

  protected override async lstInit() 
  {
    await this._mapsService.toFinishLoading();
    await this._areaService.toFinishLoading();
    
    if(this._mapsService.models.length > 0) 
    {
      this.keywordList = [];
      this._mapsService.models.filter(x=> this.keywordList.push(x))
    }

    this.keywordsToSearch = this.keywordList;

    // Ordena a lista de áreas pelo hierarchyCode
    this.areasList = this._areaService.models.sort((a, b) => {
      const codeA = a.hierarchyCode || '';
      const codeB = b.hierarchyCode || '';
      return this._sortingService.sortArrayByAlphanumericValue(false, codeA, codeB);
    });

    this.lstFetchLists();
    this.description = `Showing ${ this.keywordsToSearch.length} results`;
    console.log('KeywordsToSearch:', this.keywordsToSearch);
  }

  async removeElement(map : Maps)
  {
    const confirm = await Alert.showRemoveAlert(map.name + ' ' + 'map');
    
    if (!confirm) return;
     
    this.lstIds = this.lstIds.filter(id => map.id !== id);
    this._mapsService.models = await this._mapsService.models.filter(m => m.id !== map.id);
    await this._mapsService.toSave();
    this.lstInit();
  }

  sortListByAlphabeticalOrder(parameter: string)	
  {	
      this.keywordsToSearch.sort((a, b) => 	
      {	
          const aValue = a.hard[parameter] ?? '';	
          const bValue = b.hard[parameter] ?? '';	
          return this.invertAlphabeticalOrder ? bValue?.localeCompare(aValue) : aValue?.localeCompare(bValue);	
      });	
        this.invertAlphabeticalOrder = !this.invertAlphabeticalOrder;	
  }	

sortListByArea() {
  // Verifica se há dados para ordenar
  if (!this.keywordsToSearch || this.keywordsToSearch.length === 0) {
    return;
  }

  // Separa os itens pelos 2 primeiros caracteres da área
  const igualItems: { [key: string]: any[] } = {};
  this.keywordsToSearch.forEach((item) => {
    const areaCode = item.area ? item.area.substring(0, 2) : 'XX';
    if (!igualItems[areaCode]) {
      igualItems[areaCode] = [];
    }
    igualItems[areaCode].push(item);
  });

  // Ordena os itens dentro de cada grupo
  Object.keys(igualItems).forEach((areaCode) => {
    if (this.invertAreaOrder) {
      igualItems[areaCode].sort((a, b) => {
        return b.area.localeCompare(a.area);
      });
    } else {
      igualItems[areaCode].sort((a, b) => {
        return a.area.localeCompare(b.area);
      });
    }
  });

  // Ordena as chaves dos grupos pelos 2 primeiros caracteres (A0, C1, C6, S1, S7, S8, etc.)
  const sortedKeys = Object.keys(igualItems).sort((a, b) => {
    if (this.invertAreaOrder) {
      return b.localeCompare(a);
    } else {
      return a.localeCompare(b);
    }
  }); 

  // Reordena o array this.keywordsToSearch
  this.keywordsToSearch = [];
  sortedKeys.forEach((areaCode) => {
    this.keywordsToSearch = this.keywordsToSearch.concat(igualItems[areaCode]);
  });

  // Alterna o estado de inversão para o próximo clique
  this.invertAreaOrder = !this.invertAreaOrder;
}

  sortListById()	
  {	
      this.keywordsToSearch.sort((a, b) =>	
      {	
          return this._sortingService.sortArrayByAlphanumericValue(this.invertIdOrder, a.id, b.id);	
      })	
      this.invertIdOrder = !this.invertIdOrder;	
  }

  override lstOnChangeFilterOptions(name: {accentSensitive: boolean, caseSensitive: boolean})
  {
    this.accentSensitive = name.accentSensitive;
    this.caseSensitive = name.caseSensitive;
  }
  
  override lstOnChangeFilter(name:string)
  {
    if(name == '')
    {
      this.keywordsToSearch = this.keywordList;
      this.description = `Showing ${ this.keywordsToSearch.length} results`;
      return;
    }
    //Accept all words
    if(!this.accentSensitive && !this.caseSensitive)
    {
      name = this.simplifyString(name).toUpperCase();
      this.keywordsToSearch = this.keywordList.filter(keyword => this.simplifyString(keyword.id)?.toUpperCase()?.includes(name) 
      || this.simplifyString(keyword.area)?.toUpperCase()?.includes(name) || this.simplifyString(keyword.name)?.toUpperCase()?.includes(name)
      || this.simplifyString(keyword.description)?.toUpperCase()?.includes(name) || this.simplifyString(keyword.note)?.toUpperCase()?.includes(name));
    } 
    //case sensitive
    else if(!this.accentSensitive)
    {
      name = this.simplifyString(name);
      this.keywordsToSearch = this.keywordList.filter(keyword => this.simplifyString(keyword.id)?.includes(name) 
      || this.simplifyString(keyword.area)?.includes(name) || this.simplifyString(keyword.name)?.includes(name)
      || this.simplifyString(keyword.description)?.includes(name) || this.simplifyString(keyword.note)?.includes(name));
    }
    //accent sensitive
    else if(!this.caseSensitive)
    {
      name = name.toUpperCase();
      this.keywordsToSearch = this.keywordList.filter(keyword => keyword.id?.toUpperCase()?.includes(name) 
      || keyword.area?.toUpperCase()?.includes(name) || keyword.name?.toUpperCase()?.includes(name)
      || keyword.description?.toUpperCase()?.includes(name) || keyword.note?.toUpperCase()?.includes(name));
    }
    //case sensitive and accent sensitive
    else{
      this.keywordsToSearch = this.keywordList.filter(keyword => keyword.id?.includes(name) 
      || keyword.area?.includes(name) || keyword.name?.includes(name)|| keyword.description?.includes(name)
      || keyword.note?.includes(name))
    }
    
    this.description = `Showing ${ this.keywordsToSearch.length} results`;
  } 

  override simplifyString(str: string): string 
  {
    return (str?.normalize('NFD')?.replace(/[\u0300-\u036f]/g, '')?.trim());
  } 

  
  nameChange(maps: Maps, _name: string, value: string) {
    maps.isReviewedName = false;
    maps.revisionCounterNameAI = 0;
    this.lstOnChange(maps, 'name', value);
  }

  descriptionChange(maps: Maps, _description: string, value: string) {
    maps.isReviewedDescription = false;
    maps.revisionCounterDescriptionAI = 0;
    this.lstOnChange(maps, 'description', value);
  }

  araeaChange(maps: Maps, _area: string, value: string) {
    const areaCode = value.split(':')[0].trim();
    maps.hierarchyCodeArea = areaCode;  
    this._mapsService.svcToModify(maps);   
    this.lstOnChange(maps, 'area', value);
  }

}
