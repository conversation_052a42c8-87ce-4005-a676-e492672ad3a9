import { ChangeDetectorRef, Component, EventEmitter, Output } from '@angular/core';
import { SubContext } from 'src/app/lib/@bus-tier/models';
import { Button } from 'src/app/lib/@pres-tier/data';
import { SubContextService } from 'src/app/services';
import { AtributteService } from 'src/app/services/atributte.service';
import { Alert } from 'src/lib/darkcloud';

@Component({
  selector: 'app-sub-context',
  templateUrl: './sub-context.component.html',
  styleUrls: ['./sub-context.component.scss']
})
export class SubContextComponent {

  @Output() clickBtnAtributte = new EventEmitter<boolean>();
  titles = ['Atributte', 'Subcontext', 'Description'];
  listSubContext: SubContext[] = [];
  newSubContext: SubContext;
  description: string;
  activeLanguage = 'PTBR';
  activeTab: string;
  isSubContext: boolean;

  constructor(
    private _subContextService: SubContextService,
    private _atributteService: AtributteService,
    private ref: ChangeDetectorRef
  ) { }

  public readonly excelButtonTemplate: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };


  async ngOnInit(): Promise<void> {
    this._subContextService.toFinishLoading();

    setTimeout(() => {
      this.listSubContext = this._subContextService.models;

      // Itera sobre a lista e remove apenas o valor 'None (Default)' de subContext[0]
      this.listSubContext.forEach((x) => {
        if (x.subContext[0] === 'None (Default)') {
          x.subContext.shift(); // Remove apenas o valor 'None (Default)'
        }
      });

      this.description = `Showing ${this.listSubContext.length} results`;
      this.isSubContext = this.listSubContext.length > 0;
    }, 60);

  }

  async onExcelPaste() {
    try {
      const text = await navigator.clipboard.readText();
      const lines = text.split(/\r?\n/).filter((line) => line);

      this._subContextService.models = [];
      this._subContextService.toSave();

      for (const line of lines) {
        const values = line.split('\t');
        const atributte = values[0];

        if (values.length !== this.titles.length) {
          Alert.showError(`Error: Number of columns in the imported data does not match the expected (${this.titles.length}).`);
          return;
        }

        // Verifica se o atributo existe em this._atributteService.models
        if (atributte || atributte !== '') {
          const exists = this._atributteService.models.some((model) => model?.atributte === atributte);
          if (!exists) {
            Alert.showError(`Error: Attribute "${atributte}" not found in the system.`);
            return; // Interrompe o fluxo
          }

          this.newSubContext = await this._subContextService.createNewSubContext();
          this.newSubContext.subContext = [];
          this.newSubContext.description = [];

          this.newSubContext.atributte = atributte;
          this.newSubContext.subContext.push(values[1]);
          this.newSubContext.description.push(values[2]);

        } else {
          this.newSubContext.subContext.push(values[1]);
          this.newSubContext.description.push(values[2]);
        }
      }

      this.listSubContext.push(this.newSubContext);
      const index = this.listSubContext.indexOf(this.newSubContext);
      this._subContextService.svcToModify(this.listSubContext[index]);

      this._subContextService.toSave();
      this.ref.detectChanges();
      Alert.ShowSuccess('Subcontext list copied successfully!');
      this.ngOnInit();
    } catch (error) {
      Alert.showError('Error importing data from Excel.');
      console.error(error);
    }
  }



  changeSubContextValue(rowIndex: number, rowSub: number, name: string, newValue: string) {

    if (name === 'atributte') {
      this.listSubContext[rowIndex].atributte = newValue;
    } else if (name === 'subContext') {
      this.listSubContext[rowIndex].subContext[rowSub] = newValue;
    } else if (name === 'description') {
      this.listSubContext[rowIndex].description[rowSub] = newValue;
    }
    this._subContextService.svcToModify(this.listSubContext[rowIndex]);
  }


  btnClickContext() {
    this.clickBtnAtributte.emit(true);
  }

}
