<div class="m-margin">
    <ng-container *ngIf="characterRarityList.length > 0 && ListSouls.length > 0" >
      <div style="margin: 0 auto; width: 60%;">
        <table class="table-bordered">
          <thead>
              <tr>
                  <th style="background-color: #3f3f3f !important; padding: 8px;" [attr.colspan]="characterRarityList.length +1">
                      <h5>Souls</h5>
                  </th>
              </tr>
            <tr>
              <th class="trBC" style="width: 200px; background-color: #3f3f3f !important;">SKILL TREE LEVEL (SKTL)</th>
              <th class="trBC" *ngFor="let rarity of characterRarityList; let i = index;"  [style.background-color]="(rarity | tierColor: 'Character Rarity')+ '!important'">{{rarity}}</th>         
            </tr>
          </thead>
         <tbody>
            <ng-container *ngFor="let item of ListSouls; let i = index">  
              <tr>
                <td class="gray">{{i + 0}}</td>        
                <td [style.background-color]="item?.elementar ? 'white': '#595959'">
                   {{item.elementar}}   
                </td>
                <td [style.background-color]="item?.comum ? 'white': '#595959'">
                  {{item.comum}}
                </td>  
                <td [style.background-color]="item?.raro ? 'white': '#595959'">
                  {{item.raro}}
                </td>
                <td [style.background-color]="item?.epico ? 'white': '#595959'">
                  {{item.epico}}
                </td>
                <td [style.background-color]="item?.lendario ? 'white': '#595959'">
                  {{item.lendario}}
                </td>       
             </tr>
            </ng-container>
          </tbody>     
        </table>
      </div>

    </ng-container> 

    <ng-container *ngIf="ListSouls.length === 0">
      <div style="text-align: center;">
        <h4>Empty Souls list.</h4>
      </div>
    </ng-container>
  
  </div>
  