// Horizontal scroll container
.horizontal-scroll-container {
  overflow-x: auto;
  overflow-y: visible;
  width: 100%;

  // Custom scrollbar styling
  &::-webkit-scrollbar {
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

// Table styling
.table-list {
  min-width: 1400px; // Minimum width to ensure all columns are visible
  margin-bottom: 0;

  td {
    padding: 5px !important;
    vertical-align: middle;
    white-space: nowrap;

    // Allow text wrapping in textarea fields
    &:has(.textarea-field) {
      white-space: normal;
    }
  }

  th {
    white-space: nowrap;
    position: sticky;
    top: 0;
    background-color: #fff;
    z-index: 10;
    border-bottom: 2px solid #dee2e6;
  }
}

// Form controls
.typeSelect {
  border-radius: 2px;
  margin: 5px;
  padding: 5px;
  height: 38px; // Ajustado para 38px para combinar com o input
  width: 100%;
  max-width: 200px;
  min-width: 150px;
  box-sizing: border-box;
}

// Input promptName styling
.fontPlaceholder {
  font-size: 16px;
  width: 210px !important;
  margin: 5px;
  padding: 5px;
  border-radius: 2px;
  box-sizing: border-box;
  border: 1px solid #ccc;

  // Ensure consistent height with select
  line-height: 1.5;
  vertical-align: middle;
}

.form-control {
  width: 100%;
  min-height: 35px;

  // Exclude promptName inputs from general form-control styling
  &:not([id^="promptName_"]) {
    &:disabled {
      background-color: gray;
      color: gray;
      cursor: not-allowed;
      opacity: 0.65;
    }
  }
}

// Textarea specific styling
.textarea-field {
  min-height: 60px;
  max-height: 120px;
  resize: vertical;
  white-space: pre-wrap;
  word-wrap: break-word;
}

// Input field for prompt name
input[id^="promptName_"] {
  // Base styling to match select
  //height: 38px !important;
  margin: 5px;
  padding: 5px;
  border-radius: 2px;
  box-sizing: border-box;
  font-size: 16px;
  line-height: 1.5;
  vertical-align: middle;

  &:disabled {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    cursor: not-allowed;
    opacity: 0.65;

    &::placeholder {
      color: #adb5bd;
      font-style: italic;
    }
  }

  &:not(:disabled) {
    border-color: #ccc;

    &:focus {
      border-color: gray; 
      outline: none;
    }

    &:hover {
      border-color: #999;
    }
  }
}

.gray {
  background-color: #ddd;
}

// Responsive adjustments
@media (max-width: 768px) {
  .horizontal-scroll-container {
    &::-webkit-scrollbar {
      height: 12px; // Larger scrollbar on mobile
    }
  }

  .table-list {
    font-size: 14px;

    td {
      padding: 3px !important;
    }
  }

  .typeSelect {
    margin: 2px;
    padding: 3px;
    height: 32px;
  }
}

.table-list>tbody>tr .form-short {
    height: 26px !important;
}