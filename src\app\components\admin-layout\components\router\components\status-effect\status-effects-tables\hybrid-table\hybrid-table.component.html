<div class="div-container">
    <div class="width: 2221px;">
        <table class="table table-list borderList">
            <thead>    
                <tr>
                    <th [attr.colspan]="titles.length +1">
                        <h3>Hybrid</h3>
                    </th>
                    <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'" class="addButton"
                        [buttonTemplates]="[excelButtonTemplate]">
                    </app-button-group>
                </tr>
                <ng-container *ngIf="!isListHybridEmpty">
           
                    <tr>
                        <th class="default-color">Order</th>
                        <th
                          [ngClass]="(title === 'SKILL RESIST USER' || title === 'SKILL RESIST ENEMY') ? 'skill-resist' : 'default-color'"
                          *ngFor="let title of titles">
                          {{title}}
                        </th>
                      </tr>
                      
                </ng-container>
            </thead>
            <ng-container *ngIf="!isListHybridEmpty">
                <tbody>
                    <tr *ngFor="let item of listHybridTable; let i = index">
                        <td style="background-color: #ddd; width: 3%;">{{ i + 1 }}</td>               
                            <td class="td-id aligTitle" style="width: 7%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #idHybrid [ngClass]="{'empty-input': !idHybrid.value}"
                                    [value]="item.idHybrid" (change)="changeHybrid(i,'idHybrid', idHybrid.value)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 7%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #idCategory [ngClass]="{'empty-input': !idCategory.value}"
                                    [value]="item.category" (change)="changeHybrid(i,'category', idCategory.value)" />
                            </td>
                            <td class="td-id" style="width: 7%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #idBoost [ngClass]="{'empty-input': !idBoost.value}"
                                    [value]="item.idBoost" (change)="changeHybrid(i, 'idBoost', idBoost.value)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 7%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #idNegative [ngClass]="{'empty-input': !idNegative.value}"
                                    [value]="item.idNegative" (change)="changeHybrid(i,'idNegative', idNegative.value)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 8%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #idAffliction [ngClass]="{'empty-input': !idAffliction.value}"
                                    [value]="item.idAffliction" (change)="changeHybrid(i,'idAffliction', idAffliction.value)" />
                            </td>   
                            <td class="td-id aligTitle" style="width: 6%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #skillResistUser [ngClass]="{'empty-input': !skillResistUser.value}"
                                    [value]="item.skillResistUser" (change)="changeHybrid(i, 'skillResistUser', skillResistUser.value)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 8%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #skillResistEnemy [ngClass]="{'empty-input': !skillResistEnemy.value}"
                                    [value]="item.skillResistEnemy" (change)="changeHybrid(i, 'skillResistEnemy', skillResistEnemy.value)" />
                            </td>
                            <td class="td-id" style="width: 8%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #statusEffectName [ngClass]="{'empty-input': !statusEffectName.value}"
                                [value]="item.statusEffectName" (change)="changeHybrid(i, 'statusEffectName', statusEffectName.value)" />
                            </td>
                            <td class="td-id" style="width: 35%; word-break: break-word;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #description [ngClass]="{'empty-input': !description.value}"
                                    [value]="item.description" (change)="changeHybrid(i, 'description', description.value)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 7%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #powerPoints [ngClass]="{'empty-input': !powerPoints.value}"
                                    [value]="item.powerPoints" (change)="changeHybrid(i, 'powerPoints', powerPoints.value)" />
                            </td> 
                            <td class="td-id aligTitle" style="width: 7%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #duration [ngClass]="{'empty-input': !duration.value}"
                                    [value]="item.duration" (change)="changeHybrid(i, 'duration', duration.value)" />
                            </td>              
                    </tr>
                </tbody>
            </ng-container>
            <ng-container *ngIf="isListHybridEmpty">
                <div class="card" style="text-align: center; padding: 20px;">
                    <h3>The list has not yet been imported.</h3>
                </div>
            </ng-container>
        </table>
</div>
    
</div>

