<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons [cardTitle]="listName"
                                [cardDescription]="cardDescription"
                                [rightButtonTemplates]="[soundButtonTemplate, addButtonTemplate]">
        </app-header-with-buttons>
        <app-header-search (inputKeyup)="lstOnChangeFilter($event)" (searchOptions)="lstOnChangeFilterOptions($event)"></app-header-search>
      </div>
    </div>
    <!--List-->
    <div class="card">
      <table class="table table-list">
        <thead class="sticky">
          <tr>
            <th>Index</th>
            <th class="th-clickable"
                (click)="sortListByParameter('id')">
              ID
            </th>
            <th>Label</th>
            <th class="th-clickable"
                (click)="sortListByParameter('name')">
              Name
            </th>
            <th class="th-clickable"
                (click)="sortListByParameter('assigned')">
              Count
            </th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="
                let emotion of lstIds | emotions;
                let i = index;
                trackBy: trackById
              ">
            <tr id="{{ emotion.id }}">
              <td class="td-sort">{{ i + 1 }}</td>
              <td class="td-id">{{ emotion.id }}</td>
              <td #colorLabel class="label-color-td"
                  [ngStyle]="{ background: (emotion | information)?.hex }"
                  (click)="color.click()">
                <input #color
                       type="color"
                       value="{{ (emotion | information)?.hex || '' }}"
                       style="visibility: hidden"
                       (change)="updateColor(emotion, color.value, colorLabel)" />
              </td>
              <td>
                <input class="form-control form-title form-short"
                       type="text"
                       value="{{ emotion.name }}"
                       #name
                       (change)="lstOnChange(emotion, 'name', name.value)" />
              </td>
              <td [title]="(emotion | review).assignedAt | location">{{ (emotion | review).assignedAt.length }}</td>
              <td class="td-actions">
                <button class="btn btn-danger btn-fill btn-remove"
                        (click)="lstPromptRemove(emotion)">
                  <i class="pe-7s-close"></i>
                </button>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>
