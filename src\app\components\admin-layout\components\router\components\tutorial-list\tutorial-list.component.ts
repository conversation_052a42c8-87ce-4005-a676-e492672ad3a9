import { Component } from '@angular/core';
import { Tutorial } from 'src/app/lib/@bus-tier/models';
import { TutorialService } from 'src/app/services/tutorial.service';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { TranslationService } from 'src/app/services/translation.service';
import { LanguageService } from 'src/app/services/language.service';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-tutorial-list',
  templateUrl: './tutorial-list.component.html',
})
/**
 * Displays and edits tutorial data as a list
 */
export class TutorialListComponent extends SortableListComponent<Tutorial> {
  constructor(
    _activatedRoute: ActivatedRoute,
    _tutorialService: TutorialService,
    _userSettingsService: UserSettingsService,
    protected _translationService: TranslationService,
    protected _languageService: LanguageService
  ) {
    super(_tutorialService, _activatedRoute, _userSettingsService, 'name');
  }
}
