import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { TierList } from 'src/app/lib/@bus-tier/models';
import { Button } from 'src/app/lib/@pres-tier/data';
import { CharacterService, CodeBlockDropService, TierService } from 'src/app/services';
import { WeaponRarityService } from 'src/app/services/WeaponRarity.service';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Alert } from 'src/lib/darkcloud';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';
import { language } from 'src/lib/darkcloud/dialogue-system/game-types';
0

@Component({
  selector: 'app-tier-list',
  templateUrl: './tier-list.component.html',
  styleUrls: ['./tier-list.component.scss']
})

export class TierListComponent extends TranslatableListComponent<TierList> implements OnInit
{ 
  description:string = '';
  public language: language = 'PT-BR';
  charactersList = [];
  public tierList: TierList[] = [];
  public listTires: TierList[] = [];
  public allTierList: TierList[] = [];
  public elements:string[] = [];
  selectedElement:string = 'Character Rarity';
  selectCharacter = "Character Rarity";
  selectWeapon = "Weapon Rarity";
  selectCodeBlock = "Code Block Rarity"
  sortListByNameOrder = -1;
  sortNameOrder = -1;

  public readonly statusTemplate: Button.Templateable = 
  {
    title: 'Add a new instance to the list',
    onClick: this.addTier.bind(this),
    iconClass: 'pe-7s-plus',
    btnClass: Button.Klasses.FILL_GREEN,
  };
    
  constructor(
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    private _tierService: TierService,
    protected _weaponRarityService: WeaponRarityService,
    protected _codeBlockDropService: CodeBlockDropService,
    protected override _translationService: TranslationService,
    protected override _languageService: LanguageService,
    protected _characterService: CharacterService,

  ) 
  {
    super(_tierService, _activatedRoute, _userSettingsService, 'name', _translationService, _languageService);
  }
 
 
  public override async ngOnInit(): Promise<void> 
  {
    await this._tierService.toFinishLoading();
    this.elements = this._tierService.elements;    
    this.separateListBySelectDrop();
  }

  changeElement(element: string)
  {
    this.selectedElement = element;
    this.separateListBySelectDrop();
  }

  separateListBySelectDrop()
  {
    this.tierList = [];   
    this.removeEmptyTiers();
    this.tierList = this._tierService.models;
    this.description = `Showing ${this.tierList.length} results`;
  }

  removeEmptyTiers()
  {
    const emptyTier = this._tierService.models.filter((tier) => tier.name == "" || tier.name == undefined);
    emptyTier.forEach((tier) => this._tierService.svcToRemove(tier.id));
    this._tierService.toSave();
  }

  async addTier()
  {
    let tier : TierList | undefined;

    try 
    {
      tier = await this._tierService.svcPromptCreateNew(this.selectedElement);
    } 
    catch (e) 
    {
      Alert.showError("This TIER already exists!");
      return
    }
    if(tier == undefined) return;
      
    //await this._tierService.srvAdd(tier);
     this.ngOnInit();
  } 

  override sortListByName() 
  {
  this.sortNameOrder *= -1;
    this.tierList.sort((a, b) => 
    {  
      return this.sortNameOrder * a.name.localeCompare(b.name);
    });

  }

  async removeTier(tier:TierList)
  {
    let result = await Alert.showConfirm('You realy want to delete this Tier? This action cannot be undo!','', 'OK');
    if(!result) return;
    
    if(tier.selectDrop === this.selectCharacter) {
      await this._characterService.models.forEach((r) => {
        if(r.rarity === tier.name) {
          r.rarity = undefined;
          r.rarityId = undefined;
          this._characterService.svcToModify(r);
        } 
      });      
    }
  
    if(tier.selectDrop === this.selectWeapon) {
      await this._weaponRarityService.models.forEach((wea) => {
        if(wea.name === tier.name) {
          wea.name = undefined;
          wea.rarityId = undefined;
          this._weaponRarityService.svcToModify(wea);
        }
      });
    }
    await this._tierService.svcToRemove(tier.id);
    this._tierService.toSave();
    this.ngOnInit();
  }

  search(searchWord:string)
  {
    if(searchWord == '') return this.tierList = this.allTierList;

    this.tierList = [];
    for(let i = 0; i < this.allTierList.length; i++)
    {
      if(this.allTierList[i]?.name?.includes(searchWord) || this.allTierList[i]?.acronym?.includes(searchWord))
        this.tierList.push(this.allTierList[i]);
    }
    this.description = `Showing ${this.tierList.length} results`;

    return this.tierList;
  }

  async updateColor(tier:TierList, value:string, colorLabel: HTMLElement)
  {
    tier.color = value;
    await this._tierService.svcToModify(tier);
    colorLabel.style.backgroundColor = value;
  }

  async updateValue(tier:TierList, value:string, fieldName:string)
  {
    for(let i = 0; i < this._tierService.models.length; i++)
    {
      if(this._tierService.models[i][fieldName] == value && 
        this._tierService.models[i].selectDrop == this.selectedElement)
        return Alert.showError('', 'The Tier ALREADY EXISTS!')
    }
    tier[fieldName] = value; 
    this.updateLists(tier);
    
    if (fieldName === 'name') {
      tier.isReviewedName = false;
      tier.revisionCounterNameAI = 0;
    }
    await this._tierService.svcToModify(tier);    
  }

  updateLists(valeu) {
    if(valeu.selectDrop === this.selectCharacter) {
      this._characterService.models.filter((char) => {
        if(char.rarityId === valeu.id) {
          char.rarity = valeu.name;
        this._characterService.svcToModify(char);
        }
      });
    }

    if(valeu.selectDrop === this.selectWeapon) {
      this._weaponRarityService.models.forEach((wea) => {
        if(wea.rarityId.includes(valeu.id)) {
          wea.name = valeu.name;
          this._weaponRarityService.svcToModify(wea);
        } else return;
      });
      const list=  this._weaponRarityService.models.filter((x) => x.rarityId.includes(valeu.id));
    }

  }


  public getTierOrtography(status: TierList)
  {
    this._translationService.getTierOrtography(status, true);
  }
}
