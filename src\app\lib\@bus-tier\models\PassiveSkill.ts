import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { MergeDescription } from '../../../../lib/darkcloud/angular/dsadmin/v9/data/hard/IPassiveSkill';
import { Base } from './Base';


export class PassiveSkill extends Base<Data.Hard.IPassiveSkill, Data.Result.IPassiveSkill> implements Required<Data.Hard.IPassiveSkill>
{
  protected static generateId(index: number): string {
    return IdPrefixes.PASSIVESkill + index;
  }

  constructor(
      index: number,
      dataAccess: PassiveSkill['TDataAccess']
  )
  {
      super({hard: 
        {
          id: PassiveSkill.generateId(index),
        }
      }, dataAccess);
  }

  public get idNameWeapon(): string
  {
    return this.hard.idNameWeapon;
  }
  public set idNameWeapon(value: string)
  {
    this.hard.idNameWeapon = value;
  }

  public get nameWeapon(): string
  {
    return this.hard.nameWeapon;
  }
  public set nameWeapon(value: string)
  {
    this.hard.nameWeapon = value;
  }

  public get codeType(): string
  {
    return this.hard.codeType;
  }
  public set codeType(value: string)
  {
    this.hard.codeType = value;
  }

  public get descriptions(): MergeDescription[]
  {
    return this.hard.descriptions;
  }
  public set descriptions(value: MergeDescription[])
  {
    this.hard.descriptions = value;
  }


}
