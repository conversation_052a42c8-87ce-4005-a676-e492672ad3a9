.item-overlay
{
    position: absolute;
    //border: 15px solid black;
    height: 100%;
    width: 100%;
    background-color: white;
    z-index: 150;
}

.popup-report
{
    position: fixed;
    height: 500px;
    width: 50%;
    background-color: white;
    z-index: 149;

    margin: auto;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin-left: auto;
    margin-right: auto;
    margin-top: auto;
    margin-bottom: auto;

    text-align: center;
}

.height-cap
{
    max-height: 1000px;
}

.submit-button
{
    text-align: center;
    margin-left: auto;
    margin-right: auto;
    width: 60%;
    margin-top: 250px;
}

.scrollable-div
{
    overflow-y: scroll;
    height: 280px;
}

table.popup-area
{
  margin: 50px;
  width: 100%;
  border-radius: 5px;

  th {
    font-weight: bold;
    background-color: #dbdbdb;
  }
  th, td{
    width: 20%;
    height: 40px;
    border: 2px darkgray solid;
    padding: 4px;
    font-size: 18px;
  }

  tr:nth-child(even) {
    background-color: #f1f1f1;
  }

  .empty
  {
    background-color: gray;
  }

.wrapper {
    overflow:hidden;
    overflow-y: scroll;
    height: 1px; // change this to desired height
}
}
