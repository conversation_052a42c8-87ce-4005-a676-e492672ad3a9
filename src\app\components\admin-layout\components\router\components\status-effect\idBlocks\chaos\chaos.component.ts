import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { ChaosIdBlocks } from 'src/app/lib/@bus-tier/models/ChaosIdBlocks';
import { Button } from 'src/app/lib/@pres-tier/data';
import { ChaosIdBlockservice } from 'src/app/services';
import { Alert } from 'src/lib/darkcloud';

@Component({
  selector: 'app-chaos',
  templateUrl: './chaos.component.html',
  styleUrls: ['./chaos.component.scss']
})
export class ChaosComponent implements OnInit {

  titles = [1, 2, 3, 4, 5, 6];
  listChaos: ChaosIdBlocks[] = [];
  activeLanguage = 'PTBR';
  @Output() activeTab2 = new EventEmitter<string>();

  public readonly excelButtonTemplate: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  constructor(
    private _chaosIdBlockservice: ChaosIdBlockservice
  ){}

    async ngOnInit(): Promise<void>{

      this.removeEmptyItems();
      this.listChaos = this._chaosIdBlockservice.models;
    }
    removeEmptyItems() {
      this._chaosIdBlockservice.toFinishLoading();    
      // Filtrar os modelos para remover aqueles onde todas as posições são ""
      this._chaosIdBlockservice.models = this._chaosIdBlockservice.models.filter(chaosItem => {
        // Verificar se ao menos uma posição do item não é uma string vazia
        return this.titles.some((_, index) => chaosItem.positionNameChaos[index] !== "");
      });    

      this._chaosIdBlockservice.toSave();      
    }

    async onExcelPaste() {
      const text = await navigator.clipboard.readText();
      const lines = text.split(/\r?\n/).filter(line => line);    
      const processedData: string[][] = [];
    
      if (lines.length > 0) {
        lines.forEach(line => {
          // Divide cada linha em colunas e remove a primeira coluna
          const values = line.split("\t").map(value => value.trim()).slice(1);
    
          processedData.push(values);
        });
    
        // Verifica se o número de colunas em cada linha corresponde ao número de colunas em this.titles
        const isColumnCountValid = processedData.every(row => row.length === this.titles.length);
    
        if (!isColumnCountValid) {
          Alert.showError('Invalid number of columns');
          return;
        }
  
        this._chaosIdBlockservice.models = [];
        this._chaosIdBlockservice.toSave();
    
        for (let index = 0; index < processedData.length; index++) {
          this._chaosIdBlockservice.createNewChaosIdBlocks(processedData[index]);
        }    

        Alert.ShowSuccess('Chaos imported successfully!');
        this.activeTab2.emit('chaos');
        this.ngOnInit();
      }
    }
    
 
    changeBoost(rowIndex: number, colIndex: number, newValue: string){
      if (this.listChaos[rowIndex]) {
        this.listChaos[rowIndex].positionNameChaos[colIndex] = newValue;
        this._chaosIdBlockservice.svcToModify(this.listChaos[rowIndex]);
      }
    }
    
 
}
