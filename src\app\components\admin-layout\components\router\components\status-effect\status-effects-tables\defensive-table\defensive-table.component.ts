import { Component, EventEmitter, Output } from '@angular/core';
import { DefensiveTable } from 'src/app/lib/@bus-tier/models/DefensiveTable';
import { But<PERSON> } from 'src/app/lib/@pres-tier/data';
import { DefensiveTableService } from 'src/app/services';
import { Alert } from 'src/lib/darkcloud';

@Component({
  selector: 'app-defensive-table',
  templateUrl: './defensive-table.component.html',
  styleUrls: ['./defensive-table.component.scss']
})
export class DefensiveTableComponent {

  titles = ['ID', 'CATEGORY', 'STATUS', 'SKILL WEAK USER',
    'OPERATOR', 'VALUE', 'STATUS EFFECT NAME', 'DESCRIPTION', 'POWER POINTS (PP)', 'ALL', 'DURATION (TURNS)'];
    activeLanguage = 'PTBR';
    listDefensiveTable: DefensiveTable[] = [];

    @Output() activeTab2 = new EventEmitter<string>();
    isListDefensiveEmpty: boolean;
   
    public readonly excelButtonTemplate: Button.Templateable = {
      title: 'Paste content from excel',
      onClick: this.onExcelPaste.bind(this),
      iconClass: 'excel-icon',
      btnClass: Button.Klasses.FILL_ORANGE,
    };
    constructor(
      private _defensiveTableService: DefensiveTableService
    ){}
   
   
    async ngOnInit(): Promise<void>{
      
        this.removeEmptyItems();
         this.listDefensiveTable = this._defensiveTableService.models;
         this.isListDefensiveEmpty = this.listDefensiveTable.length === 0;   
   
      }

      removeEmptyItems() {
        this._defensiveTableService.toFinishLoading();
        this._defensiveTableService.models = this._defensiveTableService.models.filter(defensiveItem => defensiveItem.idDefensive !== "");
        this._defensiveTableService.toSave();
      }
   
      async onExcelPaste() {
        const text = await navigator.clipboard.readText();
        const lines = text.split(/\r?\n/).filter(line => line);    
        const processedData: string[][] = [];
      
        if (lines.length > 0) {
          lines.forEach(line => {
            // Divide cada linha em colunas e remove a primeira coluna
            const values = line.split("\t").map(value => value.trim()).slice(1);
      
            processedData.push(values);
          });
      
          // Verifica se o número de colunas em cada linha corresponde ao número de colunas em this.titles
          const isColumnCountValid = processedData.every(row => row.length === this.titles.length);
      
          if (!isColumnCountValid) {
            Alert.showError('Invalid number of columns');
            return;
          }
    
          this._defensiveTableService.models = [];
          this._defensiveTableService.toSave();
      
          for (let index = 0; index < processedData.length; index++) {
            this._defensiveTableService.createNewDefensiveTable(processedData[index]);
          }    
   
          Alert.ShowSuccess('Defensive Table imported successfully!');
          this.activeTab2.emit('defensiveTable');
          this.ngOnInit();
        }
      }
      
   
      changeDefensive(rowIndex: number, name: string, newValue: string){
   
        if (name === 'idDefensive') {
         this.listDefensiveTable[rowIndex].idDefensive = newValue;        
        }
        else if (name === 'category') {
          this.listDefensiveTable[rowIndex].category = newValue;        
         }   
         else if (name === 'status') {
          this.listDefensiveTable[rowIndex].status = newValue;        
         }
         else if (name === 'skillWeakUser') {
           this.listDefensiveTable[rowIndex].skillWeakUser = newValue;        
          }
         else if (name === 'operator') {
          this.listDefensiveTable[rowIndex].operator = newValue;        
         }
         else if (name === 'value') {
          this.listDefensiveTable[rowIndex].value = newValue;        
         }
         else if (name === 'statusEffectName') {
          this.listDefensiveTable[rowIndex].statusEffectName = newValue;        
         }
         else if (name === 'description') {
          this.listDefensiveTable[rowIndex].description = newValue;        
         }
         else if (name === 'powerPoints') {
          this.listDefensiveTable[rowIndex].powerPoints = newValue;        
         }
         else if (name === 'allDefensive') {
          this.listDefensiveTable[rowIndex].allDefensive = newValue;        
         }
         else if (name === 'duration') {
           this.listDefensiveTable[rowIndex].duration = newValue;        
          }
   
        this._defensiveTableService.svcToModify(this.listDefensiveTable[rowIndex]);
      }    
   


}
