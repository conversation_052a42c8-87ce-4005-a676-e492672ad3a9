
<div class="main-content">
    <div class="card m-container">
        <app-header-with-buttons 
            [cardTitle]="'Silicatos'"
            [cardDescription]="description"
            [rightButtonTemplates]="[excelButtonTemplate]"
            [isBackButtonEnabled]="false">
        </app-header-with-buttons>
        <app-header-search (inputKeyup)="lstOnChangeFilter($event)" (searchOptions)="lstOnChangeFilterOptions($event)"></app-header-search>
      </div>
    <div class="card  horizontal-scroll m-container">
        <table id='table'class="table table-list">
            <thead >
                <tr >
                    <th  rowspan="3"  class="th-clickable"
                    (click)="sortListByNumber(silicatosToSearch,'baseLevelUnlock')">Lab Level UNLOCK (GATING.LAB)</th>
                    <th rowspan="3"class="th-clickable"
                    (click)="sortListByName(silicatosToSearch, 'silicatoName')">Quantum Silicate Name</th>
                    <th [attr.colspan]="status?.length" style="font-size: 20px">Sp.ATKw(...)</th>
                    <th>Cost</th>
                    <th >Time to Create</th>
                    <th >Skip Price</th>
                    <th [attr.colspan]="subParticles?.length" rowspan="2" style="font-size: 20px">Subatomic Particles</th>
                </tr>
                <tr>
                    <th rowspan="2" style="font-size: 20px" class="th-clickable souls-color" (click)="sortSublistByNumber(silicatosToSearch,'status', i)"  *ngFor="let s of status; let i = index">
                        {{ s }}
                        
                    </th> 
                    <th class="souls-color">Souls</th>
                    <th class="time-color">Time to Create</th>
                    <th class="rubies-color">RUBIES</th>
                </tr>
                <tr>
                    <th class="th-clickable light-gray-color"
                    (click)="sortListByNumber(silicatosToSearch,'soulsCost')">Required to Create</th>
                    <th class="th-clickable light-gray-color"
                    (click)="sortListByNumber(silicatosToSearch,'timeToCreate')">minutes</th>
                    <th class="th-clickable light-gray-color"
                    (click)="sortListByNumber(silicatosToSearch,'rubiesToSkip')">Gems to Skip</th>
                    
                   <!--  <th [ngClass]="{'light-blue-color': particle.hcLevel > 6, 'gray-color': particle.hcLevel <= 3, 'blue-color': particle.hcLevel  > 3 && particle.hcLevel  <= 6}"  -->
                    <th  
                        class="th-clickable " *ngFor="let particle of subParticles; let i = index">
                        <select id="select" #selectedId (change)="changeElementsPosition(selectedId.value, i)" style="color:#000">
                            <option *ngFor="let p of subParticles"
                            [selected]="p?.name == particle?.name" 
                            value="{{p?.name}}">{{ p?.name }}</option>
                        </select>
                    </th>                     
                </tr>
            </thead>
            <tbody>
                <ng-container *ngFor="let silicato of silicatosToSearch">
                  <tr>        
                    <td class="td-id">
                        <input
                        class="background-input-table-color"
                          placeholder=" "
                          type="number"
                          #baseLevelUnlock
                          [value]="silicato.baseLevelUnlock"
                          (change)="lstOnChange(silicato, 'baseLevelUnlock', baseLevelUnlock.value)"
                          />
                      </td>
                    <td >
                        {{ silicato.silicatoName }}
                    </td>      
                    <td *ngFor="let s of this.status, let i = index">
                        <input
                            class="background-input-table-color "
                            placeholder=" "
                            type="number"
                            #status
                            [value]="silicato.status[i]"  
                            (change)="changesStatus(silicato, i, status.value)"/>
                    </td>  
                    <td class="td-id">
                        <input
                            class="background-input-table-color"
                            placeholder=" "
                            type="number"
                            #soulsCost
                            [value]="silicato.soulsCost"
                            (change)="lstOnChange(silicato, 'soulsCost', soulsCost.value )"
                            />
                    </td>
                    <td class="td-id">
                        <input
                        class="background-input-table-color"
                            placeholder=" "
                            type="number"
                            #timeToCreate
                            [value]="silicato.timeToCreate"
                            (change)="lstOnChange(silicato, 'timeToCreate', timeToCreate.value)"
                             />
                    </td>

                    <td class="td-id">
                        <input
                        class="background-input-table-color"
                            placeholder=" "
                            type="number"
                            #rubiesToSkip
                            [value]="silicato.rubiesToSkip"
                            (change)="lstOnChange(silicato, 'rubiesToSkip', rubiesToSkip.value)"
                            />
                    </td>

                    <td *ngFor="let display of subParticles, let i = index">
                      <input
                      class="background-input-table-color"
                        placeholder=" "
                        type="number"
                        #subParticles
                        [value]="silicato.subParticles[i+this.status.length+5]"
                        (change)="changesSubparticles(silicato, i+this.status.length+5,subParticles.value)"
                        />
                    </td>                  
                </ng-container>
              </tbody>
        </table>
    </div>
</div>