import { Component, OnInit } from '@angular/core';
import { ItemClass } from 'src/app/lib/@bus-tier/models/ItemClass';
import { UserSettingsService } from 'src/app/services';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { FILTER_SUFFIX_PATH } from 'src/lib/darkcloud/angular/dsadmin/constants/others';
import { Button } from 'src/app/lib/@pres-tier/data';
import { ActivatedRoute } from '@angular/router';


@Component({
  selector: 'app-collectible-costs',
  templateUrl: './collectible-costs.component.html',
})
export class CollectibleCostsComponent implements OnInit
{

  constructor(   
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    protected _translationService: TranslationService,
    protected _languageService: LanguageService
  ) {

  }

  public readonly excelButtonTemplate: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  async onExcelPaste(): Promise<void> {}

  public itemClasses: ItemClass[] = [];
  public reviewOrderAscending: boolean = false;
  public activeTab: string = '';
  public activeTab2: string = '';

  ngOnInit(): void {
    const tab = localStorage.getItem(
      `tab-MiningGeneratorComponent${FILTER_SUFFIX_PATH}`
    );
    this.activeTab = tab === 'null' || !tab ? 'upgrade' : tab;  

    if(this.activeTab === 'upgrade') {
      this.activeTab2 = 'code'
    }else{
      this.activeTab2 = 'souls'
    }
  }

  public switchToTab(tab: string) {
    this.activeTab = tab;
    localStorage.setItem(
      `tab-MiningGeneratorComponent${FILTER_SUFFIX_PATH}`,
      this.activeTab      
      );

      if(this.activeTab === 'upgrade') {
        this.activeTab2 = 'code'
      }else{
        this.activeTab2 = 'souls'
      }
  }
}
