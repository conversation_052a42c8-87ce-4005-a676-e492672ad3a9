import { ChangeDetectorRef, Component, Input } from '@angular/core';
import { Character, INTCollectible, PrimalModifier } from 'src/app/lib/@bus-tier/models';
import { CTREVMAXService, INTCollectibleService, PrimalModifierService } from 'src/app/services';


@Component({
  selector: 'app-int-collectible',
  templateUrl: './int-collectible.component.html',
  styleUrls: ['./int-collectible.component.scss']
})
export class IntCollectibleComponent {
  
@Input() character: Character;
primalModifier: PrimalModifier;
ev_base: number;
pr_base: number;
luck: number;
ev_max: number;
int: number;
A = 2; 
B = parseFloat('1,02231'.replace(',', '.')); // Corrigindo a conversão de B
listIntCollectibes: INTCollectible;
  
  constructor(
  private _cTREVMAXService: CTREVMAXService,
  private _primalModifierService: PrimalModifierService,
  private _iNTCollectibleService: INTCollectibleService,  
  private _change: ChangeDetectorRef,
) { }
    
   async ngOnInit(): Promise<void> {
    this._iNTCollectibleService.toFinishLoading();
    this._primalModifierService.toFinishLoading();
    
  this.listIntCollectibes = this._iNTCollectibleService.createNewIntCollectible(this.character);        
     // this._change.detectChanges();
     this.getDataValuesLuk();
  }
    
  getDataValuesLuk() {
    this.listIntCollectibes.valuesInt = [];
    this.ev_base = 0;
    this.luck = 0;
    this.ev_max = 0;
    this.int = 0;
  
    setTimeout(() => {
      this.primalModifier = this._primalModifierService.models.find((i) => i.character === this.character.id);
      this.ev_base = this.primalModifier.primalModifier.find((i) => i.fieldName === 'Evasão').fieldValue;
      this.pr_base = this.primalModifier.primalModifier.find((i) => i.fieldName === 'Precisão').fieldValue;
      this.luck = this.primalModifier.primalModifier.find((i) => i.fieldName === 'Sorte').fieldValue;
      this.int =  this.primalModifier.primalModifier.find((i) => i.fieldName === 'Inteligência').fieldValue;
      
      if (this._cTREVMAXService.models.length > 0) {  
        this.ev_max = this._cTREVMAXService.models[0].ev_max;
      }
    
      for (let newInt = 50; newInt <= 300; newInt++) {
        if (this.ev_base && this.luck) { 
          this.listIntCollectibes.valuesInt.push({
            ev_defluk: this.calculateEV_Qi(newInt).toFixed(2),
            pr_atkluk: this.calculatePR_Qi(newInt).toFixed(2),
            qiNew: newInt
          });
        } else {
          this.listIntCollectibes.valuesInt.push({
            ev_defluk: '',
            pr_atkluk: '',
            qiNew: newInt
          });
        }         
      }
     this._iNTCollectibleService.svcToModify(this.listIntCollectibes);
    },200);
  }
  
  public calculateEV_Qi(newQi: number): number {
    const controlPos = this.controlPositive(newQi, this.int);
    const controlNeg = this.controlNegative(newQi, this.int);
  
    const term1 = controlNeg * this.ev_base * ((this.A + this.B ** newQi) / (this.A + this.B ** this.int));
    const term2 =
      controlPos *
      (((this.ev_max - this.ev_base) / (299.999999 - this.int)) *
        (newQi - this.int) +
        this.ev_base);
  
    return term1 + term2;
  }
  
  public calculatePR_Qi(newQi: number): number {
    const controlPos = this.controlPositive(newQi, this.int);
    const controlNeg = this.controlNegative(newQi, this.int);
  
    const prBaseFraction = this.pr_base / 100; // Convertendo PR para fração decimal corretamente
  
    const term1 = controlNeg * prBaseFraction * ((this.A + this.B ** newQi) / (this.A + this.B ** this.int));
    const term2 =
      controlPos *
      (((1 - prBaseFraction) / (299.999999 - this.int)) *
        (newQi - this.int) +
        prBaseFraction);
  
    return (term1 + term2) * 100; // Convertendo de volta para porcentagem
  }
  // Métodos auxiliares
  private controlPositive(newQi: number, intBase: number): number {
    return (this.sign(newQi - intBase) + 1) / 2;
  }
  
  private controlNegative(newQi: number, intBase: number): number {
    return 1 - this.controlPositive(newQi, intBase);
  }
  
  private sign(value: number): number {
    return value > 0 ? 1 : value < 0 ? -1 : 0;
  }
  
    
  reset(character) {
    this.character = character;
    this.ngOnInit();
  }
  
  
  }
  