import { ChangeDetectorRef, Component, OnDestroy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { MissionNotes } from 'src/app/lib/@bus-tier/models';
import { Button } from 'src/app/lib/@pres-tier/data';
import { MissionNotesService, UserSettingsService } from 'src/app/services';
import { TranslationService } from 'src/app/services/translation.service';
import { Alert } from 'src/lib/darkcloud';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc';
import { comparableString } from 'src/lib/others';

@Component({
  selector: 'app-mission-notes',
  templateUrl: './mission-notes.component.html',
  styleUrls: ['./mission-notes.component.scss']
})
export class MissionNotesComponent extends SortableListComponent<MissionNotes> implements OnDestroy {

  timeout:any;
  caseSensitive: boolean = false;
  accentSensitive: boolean = false;
  listMissionNotes: MissionNotes[] = [];
  filteredMissionNotes: MissionNotes[] = [];
  queryValue: string = '';

  /**
   * Calcula o número de resultados filtrados para exibir na descrição do card
   */
  get filteredResultsCount(): number {
    return this.filteredMissionNotes.length;
  }

  /**
   * Filtra a lista de mission notes baseado no termo de busca
   * Busca nos campos name e description usando translation pipe
   */
  private filterMissionNotes(): void {
    // Sempre usa this._modelService.models como fonte (que pode estar ordenada)
    const sourceList = this._modelService.models.length > 0 ? this._modelService.models : this.listMissionNotes;

    if (!this.queryValue || this.queryValue.trim() === '') {
      this.filteredMissionNotes = [...sourceList];
      return;
    }

    const searchOptions = {
      caseSensitive: this.caseSensitive,
      accentSensitive: this.accentSensitive
    };

    const comparableQuery = comparableString(this.queryValue, searchOptions);

    this.filteredMissionNotes = sourceList.filter(missionNote => {
      // Busca no campo name (usando translation)
      const name = missionNote.name || '';
      const nameMatch = comparableString(name, searchOptions).includes(comparableQuery);

      // Busca no campo description (usando translation)
      const description = missionNote.description || '';
      const descriptionMatch = comparableString(description, searchOptions).includes(comparableQuery);

      return nameMatch || descriptionMatch;
    });
  }

  
public readonly addMissionNotesButton: Button.Templateable = {
      title: 'Add a new Missin Notes to the list',
      onClick: this.createMissionNotes.bind(this),
      iconClass: 'pe-7s-plus',
      btnClass: Button.Klasses.FILL_GREEN,
    };

  constructor(
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    private _router: Router,
    private _change: ChangeDetectorRef,
    private _MissionNotesService: MissionNotesService,
    protected _translationService: TranslationService,

  )
  {
    super(_MissionNotesService, _activatedRoute, _userSettingsService, 'name');
  }

   override async lstInit() {
    if(!(this.lstIds?.length <= 0))
      return;

    this._MissionNotesService.toFinishLoading();
    this.updateBaseList(); // Usa o método centralizado para atualizar
  }


   public async createMissionNotes(){
     let newMissionNotes: MissionNotes;
        try {
          newMissionNotes = await this._MissionNotesService.svcPromptCreateNew();
        } catch (e) {
          Alert.showError("Mission Notes já existe!");
        }
        if(!newMissionNotes)
          return;
        const hora = new Date();
        const horas = hora.getHours();
        const minutos = hora.getMinutes().toString().padStart(2, '0');

     newMissionNotes.createdDate = new Date().toLocaleDateString() + " " + horas + ":" + minutos;
        this._MissionNotesService.srvAdd(newMissionNotes);
        this.lstResetHighlights();
        this.HighlightElement(newMissionNotes.id, 110, true);
        this.updateBaseList(); // Usa o método centralizado
       this.ngOnInit();
      }

      
  redirectToItemClasses()
  {
    this._router.navigate(['missions']);
  }

  /**
   * Atualiza as opções de busca e refiltra a lista
   */
  public updateSearchOptions(caseSensitive: boolean, accentSensitive: boolean): void {
    this.caseSensitive = caseSensitive;
    this.accentSensitive = accentSensitive;
    this.filterMissionNotes();
  }

  /**
   * Atualiza o termo de busca e refiltra a lista
   */
  public updateSearchTerm(term: string): void {
    this.queryValue = term;
    this.filterMissionNotes();
  }

  async removeMision(missionNotes: MissionNotes) {
    const confirm: boolean = await Alert.showRemoveAlert(missionNotes.name);

    if (confirm) {
      this._MissionNotesService.svcToRemove(missionNotes.id);
      this.updateBaseList(); // Usa o método centralizado
      this._change.detectChanges();
      return this.ngOnInit();
    }
    return false;
  }

  ngOnDestroy() {
    clearInterval(this.timeout)
  }

  // Método mantido para compatibilidade (caso seja usado em algum lugar)
  filterList(event: string) {
    this.updateSearchTerm(event);
  }

  // Método mantido para compatibilidade (caso seja usado em algum lugar)
  searchFilterOptions(event: any) {
    this.updateSearchOptions(event.caseSensitive, event.accentSensitive);
  }

  /**
   * Override do método sortListByParameter para funcionar com o sistema de busca
   * Ordena primeiro e depois aplica o filtro
   */
  public override sortListByParameter(parameter: any, index?: number): void {
    // Chama o método original da classe pai para ordenar this._modelService.models
    super.sortListByParameter(parameter, index);

    // Após a ordenação, reaplica o filtro para manter a busca funcionando
    this.filterMissionNotes();
  }

  /**
   * Atualiza a lista base quando há mudanças no serviço
   */
  private updateBaseList(): void {
    this.listMissionNotes = this._MissionNotesService.models;
    this.filterMissionNotes();
  }

  /**
   * Override do método lstOnChange para refiltar após mudanças
   */
  public override async lstOnChange<Key extends keyof MissionNotes = any>(
    model: MissionNotes,
    parameter?: keyof MissionNotes,
    value?: MissionNotes[Key]
  ): Promise<void> {
    // Chama o método original da classe pai
    await super.lstOnChange(model, parameter, value);

    // Refiltra a lista após a mudança para manter a busca atualizada
    this.filterMissionNotes();
  }

}
