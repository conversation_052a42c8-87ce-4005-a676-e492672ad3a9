import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';

// Interfaces para os objetos de Knowledge e Attribute
interface KnowledgeValue {
  id: string;
  value: string;
}

interface AttributeValue {
  id: string;
  value: string;
}

export class ModMoonRanges extends Base<Data.Hard.IModMoonRanges, Data.Result.IModMoonRanges> implements Required<Data.Hard.IModMoonRanges>
{
  public static generateId(index: number): string {
    return IdPrefixes.MODMOONRANGES + index;
  }

  constructor( index: number, dataAccess: ModMoonRanges['TDataAccess']) 
  {
    super(
    {
      hard: 
      {
        id: ModMoonRanges.generateId(index),     
      },
    },
    dataAccess
    );
  }
  protected getInternalFetch() 
  {
    return {};
  }
  public get moonPhase(): string 
  {
    return this.hard.moonPhase;
  }
  public set moonPhase(value: string) 
  {
    this.hard.moonPhase = value;
  }
  public get technicalNomenclature(): string 
  {
    return this.hard.technicalNomenclature;
  }
  public set technicalNomenclature(value: string) 
  {
    this.hard.technicalNomenclature = value;
  }
  public get knowledge(): KnowledgeValue[]
  {
    return this.hard.knowledge;
  }
  public set knowledge(value: KnowledgeValue[])
  {
    this.hard.knowledge = value;
  }
  public get modDanoParty(): string
  {
    return this.hard.modDanoParty;
  }
  public set modDanoParty(value: string) 
  {
    this.hard.modDanoParty = value;
  }

  public get modDanoOponente(): string
  {
    return this.hard.modDanoOponente;
  }
  public set modDanoOponente(value: string) 
  {
    this.hard.modDanoOponente = value;
  }

  public get attribute(): AttributeValue[]
  {
    return this.hard.attribute;
  }
  public set attribute(value: AttributeValue[])
  {
    this.hard.attribute = value;
  }



}
