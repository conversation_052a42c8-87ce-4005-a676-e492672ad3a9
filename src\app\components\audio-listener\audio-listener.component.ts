import { HttpClient } from '@angular/common/http';
import { Component, Input, OnInit, OnDestroy } from '@angular/core';

@Component({
  selector: 'app-audio-listener',
  templateUrl: './audio-listener.component.html',
  styleUrls: ['./audio-listener.component.scss'],
})
export class AudioListenerComponent implements OnInit, OnDestroy 
{
  get audioId(): string 
  {
    return this._audioId;
  }
  @Input() set audioId(val: string) 
  {
    this._audioId = val;
  }
  @Input() disabled: boolean;
  @Input() klass = 'btn btn-fill';
  @Input() hoverToPlay = false;
  public isPaused = true;
  private _audioId: string;
  public audioUrl: string;
  private audioName: string;
  public audio: HTMLAudioElement;
  private _runOnce:boolean = true;
  isPlay:boolean = false;
  
  constructor(private http: HttpClient) {}

  ngOnInit(): void 
  {
    this.loadAudio();
  }

  loadAudio() 
  {
    this.audioUrl = undefined;

    this.audioName = this.audioId.replace('af','');
    this.findAudioUrl();
  }

  findAudioUrl(): void 
  {
    this.getAudioUrl((audioUrl) => 
    {
      this.audioUrl = audioUrl;
      this.audio = new Audio(this.audioUrl);
      this.audio.onpause = () => 
      {
        this.isPaused = true;
      };
      this.audio.onplay = () => 
      {
        this.isPaused = false;
      };
    });
  }

  getAudioUrl(handle: (audioUrl: string) => void): void 
  {
    if (!this.audioName) 
    {
      console.error(`The ${this.audioName} audio does not exist`);
      return;
    }
    const audioUrl = `http://localhost:3000/audio/${this.audioName}`;
  
    this.http.get(audioUrl, 
    {
      responseType: 'blob',
    }).subscribe
    ({
      next: () => 
      {
        handle(audioUrl);
      },
      error: (error: any) => 
      {
        console.error('Error on audio listener in getAudioUrl:', error);
      },
      complete: () => 
      {
        console.log("It's complete the audio listener!");
      }
    });
  }

  playAudio() 
  {
    this.audio.play();
    this.isPlay = true;
    setTimeout(()=>
    {
      this.audio.pause();
      this.isPlay = false;
    }, 2000)
  }

  ngOnDestroy(): void 
  {
    if (!this.audio) return;
    
    this.audio.remove();
    this.audio.srcObject = null;
  }
}
