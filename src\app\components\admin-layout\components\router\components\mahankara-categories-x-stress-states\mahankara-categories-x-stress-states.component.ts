import { Component, EventEmitter, Input, Output, SimpleChanges } from '@angular/core';
import { CategoriesXStressStates } from 'src/app/lib/@bus-tier/models';
import { CategoriesXStressStatesService } from 'src/app/services';
import { Alert } from 'src/lib/darkcloud';

@Component({
  selector: 'app-mahankara-categories-x-stress-states',
  templateUrl: './mahankara-categories-x-stress-states.component.html',
  styleUrls: ['./mahankara-categories-x-stress-states.component.scss']
})
export class MahankaraCategoriesXStressStatesComponent {
  
 @Output() descriptionOutput = new EventEmitter<string>();
  @Input() copyExcelCategoriesXStress: string[];
  titles = ['Index Category', 'Humor', 'Sarcasmo', 'Seriedade', 'Agressividade', 'Desespero'];
  listCategoriesXStress: CategoriesXStressStates[] = [];

    // Flag para controle de inicialização do copyExcelRepetition
    isFirstChange = true;
  
    constructor(
      private _categoriesXStressStatesService: CategoriesXStressStatesService
    ) {}
  

    ngOnChanges(changes: SimpleChanges) {
      if (changes['copyExcelCategoriesXStress']) {
        if (this.isFirstChange) {
          // Ignorar a primeira alteração no ciclo de vida
          this.isFirstChange = false;
        } else if (
          this.copyExcelCategoriesXStress &&
          this.copyExcelCategoriesXStress.length > 0
        ) {
          this.onExcelPaste();
        }
      }
    }

    async ngOnInit(): Promise<void> {
      this._categoriesXStressStatesService.toFinishLoading();
      this.listCategoriesXStress = this._categoriesXStressStatesService.models;
      this.descriptionOutput.emit(
        `Showing ${this.listCategoriesXStress.length} results`
      ); 
    }


 async onExcelPaste() {
 this._categoriesXStressStatesService.models = [];
    this._categoriesXStressStatesService.toSave();

    // Verifica se `this.copyExcelMahankaraBehavior` contém dados
    if (
      !this.copyExcelCategoriesXStress ||
      this.copyExcelCategoriesXStress.length === 0
    ) {
      Alert.showError('No data found in the copied Excel content.');
      return this.ngOnInit();
    }

    const expectedColumns = this.titles.length;

    //Verificar se todas as linhas possuem o número correto de colunas
    const invalidColumnRows = this.copyExcelCategoriesXStress.filter(
      (row) => {
        const cells = row.split('\t'); // O '\t' dividide em células - O delimitador \t é para tabulação (comum em colagens do Excel)
        return cells.length !== expectedColumns;
      }
    );

    if (invalidColumnRows.length > 0) {
      Alert.showError(
        `The number of columns does not match the expected count (${expectedColumns}). Please check the data.`
      );
      return this.ngOnInit();
    }

    this.copyExcelCategoriesXStress.forEach((row, index) => {
      const cells = row.split('\t'); // Divide a linha em células
      this._categoriesXStressStatesService.createNewCategoriesXStressStates(cells);
    });

    this.copyExcelCategoriesXStress = [];
    Alert.ShowSuccess('Categories X Stress States imported successfully!');
    this.ngOnInit();
  }

  changeCategoriesXValue(rowIndex: number, name: string, newValue: string) {
    if (name === 'indexCategory') {
      this.listCategoriesXStress[rowIndex].indexCategory = newValue;
    } else if (name === 'humor') {
      this.listCategoriesXStress[rowIndex].humor = newValue;
    } else if (name === 'sarcasm') {
      this.listCategoriesXStress[rowIndex].sarcasm = newValue;
    } else if (name === 'seriousness') {
      this.listCategoriesXStress[rowIndex].seriousness = newValue;
    } else if (name === 'aggressiveness') {
      this.listCategoriesXStress[rowIndex].aggressiveness = newValue;
    } else if (name === 'despair') {
      this.listCategoriesXStress[rowIndex].despair = newValue;
    }
    this._categoriesXStressStatesService.svcToModify(this.listCategoriesXStress[rowIndex]);
  }
} 

