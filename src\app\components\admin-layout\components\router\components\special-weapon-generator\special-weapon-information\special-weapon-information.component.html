<div class="content-weapon">
  <div class="card">
    <app-header-with-buttons 
      [cardTitle]="'Special Weapons'"
      [cardDescription]="description"
      [rightButtonTemplates]="[excelButtonTemplate]"
      [isBackButtonEnabled]="false">
    </app-header-with-buttons>
  </div>

  <div class="containerTable">
    <table class="table table-list">
      <thead class="sticky" style="margin-bottom: 20px;">   
        <tr >  
          <th rowspan="3" >Index</th>        
          <th class="th-clickable" (click)="sortArchiveLevel()" rowspan="3">FORGE Level (GATING.FORGE)</th>
          <th class="th-clickable" (click)="sortItemId()" rowspan="3">Special Weapon</th>
          <th>Souls Cost</th>
          <th>Time to create (min)</th>
          <th>Skip Price</th>
          <th class="th-clickable" (click)="sortBPId()" rowspan="3">Blueprint</th>
          <th class="th-clickable" (click)="sortUniqueIngredient()"rowspan="3">Unique Ingredient</th>
          <th rowspan="2" [attr.colspan]="ingredients?.length">Common Ingredients</th>
        </tr>
        <tr>
          <th class="Common">souls</th>
          <th class="blue-color">minutes</th>
          <th class="red-color">rubies</th>
        </tr>
        <tr>
          <th (click)="sortListBySoulsCost()" class="light-gray-color th-clickable">Required to Create</th>
          <th (click)="sortListByTimeToCreate()" class="light-gray-color th-clickable">Time to Create</th>
          <th (click)="sortByRubies()" class="light-gray-color th-clickable">Gems to Skip</th>
          <th  
            class="th-clickable" (click)="sortComonIngredients(i)" *ngFor="let particle of ingredients; let i = index">
            <select id="select" #selectedId (change)="changeElementsPosition(selectedId.value, particle.id)" style="color:#000">
              <option *ngFor="let p of ingredients"
                [selected]="p.name == particle.name" 
                value="{{p.name}}">{{ p.name }}
              </option>
            </select>
          </th> 
        </tr>
      </thead>
      <tbody>
        <ng-container *ngFor="let specialWeapon of this.specialWeapons; let i = index">
          <tr id="{{ specialWeapon.id }}">
            <td class="gray-bg">{{i + 1}}</td>
            <td class="td-id">
              <input
                class="background-input-table-color"
                placeholder=" "
                type="number"
                #InputBAL
                [value]="specialWeapon.bpArchiveLevel"
                (change)="changeBAL(specialWeapon, +InputBAL.value)" />
            </td>
            <td class="td-id">{{ GetSpecialWeaponName(specialWeapon.id) }}</td>
            <td class="td-id">
              <input
                class="background-input-table-color"
                placeholder=" "
                type="number"
                #InputSouls
                [value]="specialWeapon.souls"
                (change)="changeSouls(specialWeapon, +InputSouls.value)" />
            </td>
            <td>
              <input
                class="background-input-table-color"
                placeholder=" "
                type="number"
                #InputTime
                [value]="specialWeapon.time"
                (change)="changeTime(specialWeapon, +InputTime.value)" />
            </td>
            <td>
              <input
                class="background-input-table-color"
                placeholder=" "
                type="number"
                #InputSkipPrice
                [value]="specialWeapon.rubies"
                (change)="changeSkipPrice(specialWeapon, +InputSkipPrice.value)" />
            </td>
            <td>
              <select required class="dropdown filter-dropdown limited select-background-color"              
                style="display: inline-block; margin: 5px;"
                #InputBlueprint
                (change)="changeBlueprint(specialWeapon, InputBlueprint.value)">
                <option value=""></option>
                <option *ngFor="let bp of blueprints"
                  [selected]="specialWeapon.bpId == bp?.id"
                  value="{{ bp?.id }}">{{ bp?.name }}
                </option>
              </select>
            </td>
            <td>
              <select required class="dropdown filter-dropdown select-background-color"              
                style="display: inline-block; margin: 5px;"
                #InputUniqueIngredient
                (change)="changeUniqueIngredient(specialWeapon, InputUniqueIngredient.value)">
                <option value=""></option>
                <option *ngFor="let ui of uniqueIngredients"
                  [selected]="specialWeapon.ingredientId == ui.id"
                  value="{{ ui.id }}">{{ ui.name }}
                </option>
              </select>
            </td>
            <td *ngFor="let ingredient of ingredients, let i = index">
              <input
                class="background-input-table-color"
                placeholder=" "
                type="number"
                #InputIngredient
                [value]="GetAmountValue(specialWeapon, i)"
                (change)="changeIngredientAmount(specialWeapon, +InputIngredient.value, i)" />
            </td>
          </tr>
        </ng-container>
      </tbody>
    </table>
  </div>
</div>

