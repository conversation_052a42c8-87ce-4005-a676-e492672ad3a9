# Sistema de Busca Personalizado - MissionNotesComponent

## Visão Geral

Foi criado um sistema de busca personalizado para o `MissionNotesComponent` que substitui o componente `app-header-search` genérico. Este novo sistema oferece busca específica nos campos `name` e `description` das Mission Notes.

## Funcionalidades

### 🔍 **Busca em Múltiplos Campos**
- **Name**: Busca no campo nome da Mission Note
- **Description**: Busca no campo descrição da Mission Note
- **Busca Combinada**: Retorna resultados que contenham o termo em qualquer um dos campos

### ⚙️ **Opções de Busca**
- **Case Sensitive**: Diferencia maiúsculas de minúsculas
- **Accent Sensitive**: Diferencia caracteres acentuados
- **Busca em Tempo Real**: Filtra conforme o usuário digita

### 📊 **Contador <PERSON>**
- Atualiza automaticamente o número de resultados filtrados
- Exibe no header: "Showing X results"

## Implementação Técnica

### Propriedades Adicionadas

```typescript
filteredMissionNotes: MissionNotes[] = [];  // Lista filtrada
queryValue: string = '';                    // Termo de busca
caseSensitive: boolean = false;             // Opção case sensitive
accentSensitive: boolean = false;           // Opção accent sensitive
```

### Métodos Principais

#### `filterMissionNotes()`
```typescript
private filterMissionNotes(): void {
  if (!this.queryValue || this.queryValue.trim() === '') {
    this.filteredMissionNotes = [...this.listMissionNotes];
    return;
  }

  const searchOptions = {
    caseSensitive: this.caseSensitive,
    accentSensitive: this.accentSensitive
  };

  const comparableQuery = comparableString(this.queryValue, searchOptions);

  this.filteredMissionNotes = this.listMissionNotes.filter(missionNote => {
    const name = missionNote.name || '';
    const nameMatch = comparableString(name, searchOptions).includes(comparableQuery);

    const description = missionNote.description || '';
    const descriptionMatch = comparableString(description, searchOptions).includes(comparableQuery);

    return nameMatch || descriptionMatch;
  });
}
```

#### `updateSearchTerm(term: string)`
```typescript
public updateSearchTerm(term: string): void {
  this.queryValue = term;
  this.filterMissionNotes();
}
```

#### `updateSearchOptions(caseSensitive: boolean, accentSensitive: boolean)`
```typescript
public updateSearchOptions(caseSensitive: boolean, accentSensitive: boolean): void {
  this.caseSensitive = caseSensitive;
  this.accentSensitive = accentSensitive;
  this.filterMissionNotes();
}
```

### Contador de Resultados

```typescript
get filteredResultsCount(): number {
  return this.filteredMissionNotes.length;
}
```

## Interface HTML

### Componente de Busca Personalizado

```html
<div class="custom-search-container">
  <div class="searchControl">
    <input class="form-control"
      type="text"
      placeholder="Search in name and description..."
      [(ngModel)]="queryValue"
      (input)="updateSearchTerm(queryValue)" />
  </div>
  <div class="searchOptions">
    <input type="checkbox" 
      [(ngModel)]="accentSensitive"
      (change)="updateSearchOptions(caseSensitive, accentSensitive)"
      id="accentSensitive">
    <label for="accentSensitive">Accent Sensitive</label><br>
    <input type="checkbox" 
      [(ngModel)]="caseSensitive"
      (change)="updateSearchOptions(caseSensitive, accentSensitive)"
      id="caseSensitive">
    <label for="caseSensitive">Case Sensitive</label>
  </div>
</div>
```

### Lista Filtrada

```html
<ng-container *ngFor="let missionNotes of filteredMissionNotes; let i = index; trackBy: trackById">
  <!-- Conteúdo da linha -->
</ng-container>
```

### Mensagens de Estado

```html
<!-- Quando não há Mission Notes -->
<ng-container *ngIf="filteredMissionNotes.length == 0 && listMissionNotes.length == 0">
  <div class="noMissions">
    <p>No Mission Notes</p>
    <p>Click me Add a new Mission Notes to the list.</p>
  </div>
</ng-container>

<!-- Quando não há resultados para a busca -->
<ng-container *ngIf="filteredMissionNotes.length == 0 && listMissionNotes.length > 0">
  <div class="noMissions">
    <p>No Mission Notes found for "{{ queryValue }}"</p>
    <p>Try adjusting your search criteria.</p>
  </div>
</ng-container>
```

## Estilização CSS

```scss
.custom-search-container {
  display: flex;
  align-items: center;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 5px;
  margin-bottom: 10px;

  .searchControl {
    width: calc(100% - 200px);
    
    input {
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 8px 12px;
      
      &:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
      }
    }
  }

  .searchOptions {
    width: 200px;
    padding-left: 20px;
    
    label {
      font-size: 12px;
      cursor: pointer;
    }
  }
}
```

## Vantagens do Sistema Personalizado

1. **🎯 Busca Específica**: Busca apenas nos campos relevantes (name e description)
2. **⚡ Performance**: Não depende de pipes externos, controle total da filtragem
3. **🔧 Customização**: Fácil de modificar e estender para novos campos
4. **📱 Responsivo**: Interface adaptável para diferentes tamanhos de tela
5. **🔄 Tempo Real**: Filtragem instantânea conforme o usuário digita
6. **📊 Feedback Visual**: Contador de resultados e mensagens de estado
7. **🎨 Integração**: Visual consistente com o resto da aplicação

## Compatibilidade

O sistema mantém métodos de compatibilidade para não quebrar integrações existentes:

```typescript
// Métodos mantidos para compatibilidade
filterList(event: string) {
  this.updateSearchTerm(event);
}

searchFilterOptions(event: any) {
  this.updateSearchOptions(event.caseSensitive, event.accentSensitive);
}
```

## Integração com Sistema de Ordenação

### Problema Resolvido
O sistema de busca personalizado foi integrado com o sistema de ordenação existente (`sortListByParameter`) para garantir que ambas as funcionalidades trabalhem em conjunto.

### Solução Implementada

#### Override do sortListByParameter
```typescript
public override sortListByParameter(parameter: any, index?: number): void {
  // Chama o método original da classe pai para ordenar this._modelService.models
  super.sortListByParameter(parameter, index);

  // Após a ordenação, reaplica o filtro para manter a busca funcionando
  this.filterMissionNotes();
}
```

#### Override do lstOnChange
```typescript
public override async lstOnChange<Key extends keyof MissionNotes = any>(
  model: MissionNotes,
  parameter?: keyof MissionNotes,
  value?: MissionNotes[Key]
): Promise<void> {
  // Chama o método original da classe pai
  await super.lstOnChange(model, parameter, value);

  // Refiltra a lista após a mudança para manter a busca atualizada
  this.filterMissionNotes();
}
```

#### Sincronização de Dados
```typescript
private filterMissionNotes(): void {
  // Sempre usa this._modelService.models como fonte (que pode estar ordenada)
  const sourceList = this._modelService.models.length > 0 ? this._modelService.models : this.listMissionNotes;

  // Aplica filtro na lista ordenada
  this.filteredMissionNotes = sourceList.filter(/* lógica de filtro */);
}
```

### Fluxo de Funcionamento Integrado

1. **Ordenação**: Usuário clica em header da coluna
2. **sortListByParameter**: Ordena `this._modelService.models`
3. **filterMissionNotes**: Reaplica filtro na lista ordenada
4. **Template**: Exibe `filteredMissionNotes` ordenada e filtrada

5. **Edição**: Usuário edita um campo
6. **lstOnChange**: Salva mudanças no modelo
7. **filterMissionNotes**: Reaplica filtro para manter busca atualizada

### Funcionalidades Mantidas

- ✅ **Ordenação por Colunas**: Funciona normalmente
- ✅ **Busca em Tempo Real**: Mantém filtro após ordenação
- ✅ **Edição Inline**: Refiltra após mudanças
- ✅ **CRUD Operations**: Integrado com add/remove
- ✅ **Contador Dinâmico**: Sempre atualizado

Este sistema oferece uma solução robusta e personalizada para busca em Mission Notes, proporcionando uma experiência de usuário superior e maior controle sobre a funcionalidade de filtragem, mantendo total compatibilidade com o sistema de ordenação existente.
