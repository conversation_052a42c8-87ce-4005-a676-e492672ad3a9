import { Data } from "src/lib/darkcloud/angular/dsadmin";
import { IdPrefixes } from "src/lib/darkcloud/dialogue-system";
import { IListUnique } from "../../../../lib/darkcloud/angular/dsadmin/v9/data/hard/IUniqueCharactere";
import { Base } from "./Base";

export class UniqueCharactere extends Base<Data.Hard.IUniqueCharactere, Data.Result.IUniqueCharactere> implements Required<Data.Hard.IUniqueCharactere>
{
    static generateId(index: number): string
    {
        return IdPrefixes.UNIQUECHARACTERES + index;
    }

    constructor(
      index: number,
      dataAccess: UniqueCharactere['TDataAccess']) 
    {
      super(
          {
              hard: 
              {
                  id: UniqueCharactere.generateId(index),
              },
          },
          dataAccess
      );
    }

    protected getInternalFetch() {
    return {};
   }
    public get listCicleLevel(): IListUnique[]
    {
      return this.hard.listCicleLevel;
    }
    public set listCicleLevel(value: IListUnique[])
    {
      this.hard.listCicleLevel = value;
    }
    public get idRarity(): string
    {
      return this.hard.idRarity;
    }
    public set idRarity(value: string)
    {
      this.hard.idRarity = value;
    }

    public get nameCharacterRarity(): string
    {
      return this.hard.nameCharacterRarity;
    }
    public set nameCharacterRarity(value: string)
    {
      this.hard.nameCharacterRarity = value;
    }

}