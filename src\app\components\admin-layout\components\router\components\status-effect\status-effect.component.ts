import { Component } from '@angular/core';
import { Category } from 'src/app/lib/@bus-tier/models';
import { AfflictionTableService, AilmentIdBlockservice, AilmentTableService, BoostIdBlockservice, BoostTableService, CategoryStatusEffectService, ChaosIdBlockservice, ChaosTableService, DefensiveIdBlockservice, DefensiveTableService, DispelIdBlockservice, DispelTableService, HealingIdBlockservice, HealingTableService, HybridTableService, NegativeIdBlockservice, RepetitionStatusEffectService } from 'src/app/services';
import { LanguageService } from 'src/app/services/language.service';
import { NegativeTableService } from 'src/app/services/negative-table.service';
import { TranslationService } from 'src/app/services/translation.service';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';
import { FILTER_SUFFIX_PATH } from '../../../../../../../lib/darkcloud/angular/dsadmin/constants/others';
import { Button } from '../../../../../../lib/@pres-tier/data';
import { ActivatedRoute, Router } from '@angular/router';
import { ComboTableService } from 'src/app/services/combo-table.service';

@Component({
  selector: 'app-status-effect',
  templateUrl: './status-effect.component.html',
  styleUrls: ['status-effect.component.scss'],
})

export class StatusEffectComponent extends TranslatableListComponent<Category> 
{ 

  description: string;
  activeTab: string;
  activeTab2: string;
  activeTab3: string;
  title: string
  isTab = false;
  activeLanguage = 'PTBR';
  listExcelRepetition: string[] = [];

  public readonly excelButtonTemplate: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  constructor(
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService, 
    private router : Router,
    protected override _translationService: TranslationService,
    protected override _languageService: LanguageService,
    private _categoryStatusEffectService: CategoryStatusEffectService,
    private _repetitionStatusEffectService: RepetitionStatusEffectService,
    private _boostIdBlockservice: BoostIdBlockservice,
    private _healingtIdBlockservice: HealingIdBlockservice,
    private _defensiveIdBlockservice: DefensiveIdBlockservice,
    private _negativeIdBlockservice: NegativeIdBlockservice,
    private _ailmentIdBlockservice: AilmentIdBlockservice,
    private _dispelIdBlockservice: DispelIdBlockservice,
    private _boostTableService: BoostTableService,
    private _healingTableService: HealingTableService,
    private _defensiveTableService: DefensiveTableService,
    private _negativeTableService: NegativeTableService,
    private _ailmentTableService: AilmentTableService,
    private _dispelTableService: DispelTableService,
    private _hybridTableService: HybridTableService,
    private _afflictionTableService: AfflictionTableService,
    private _chaosIdBlockservice: ChaosIdBlockservice,
    private _comboTableService: ComboTableService,
    private _chaosTableService: ChaosTableService,
    
  ) 
  {
    super(_categoryStatusEffectService, _activatedRoute, _userSettingsService, 'name', _translationService, _languageService);
  }
 
  public override async ngOnInit(): Promise<void> 
  {
 
    const tab = localStorage.getItem(`tab-OthersComponent${FILTER_SUFFIX_PATH}`);
    this.activeTab = tab === 'null' || !tab ? 'keywords' : tab;
    this.isTab = false;
  }
  async onExcelPaste() {
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter(line => line);
    this.listExcelRepetition = lines;   
  }

  addStatusEffect() {
    if(this.activeTab  ==='category') {
      this._categoryStatusEffectService.createNewCategory();  
      this.description =`Showing ${this._categoryStatusEffectService.models.length} results`; 
    }     
  }
 
  public switchToTab(tab: string) 
  {
    this.activeTab = tab;
    this.isTab = true;

    if(tab ==='category') {
      this.title = 'Category';
      this.description =`Showing ${this._categoryStatusEffectService.models.length} results`; 
    }
    else if(tab ==='repetition') {
      this.title = 'Repetition';
      this.description =`Showing ${this._repetitionStatusEffectService.models.length} results`; 
    }
    else if(tab ==='idBlocks') {
      this.title = 'ID Blocks';
      this.description =`Showing ${ 0 } results`; 
    }
    else if(tab ==='status-effect') {
      this.title = 'Status Effect Table';
      this.description =`Showing ${ 0 } results`; 
    }  
    localStorage.setItem(`tab-StatusEffectComponent${FILTER_SUFFIX_PATH}`, this.activeTab);
  } 
  // Submenu do ID Blocks
  switchToTab2(tab2: string){

    this.activeTab2 = tab2;

    if(tab2 ==='boost') {
      this.title = 'Boost ID Blocks';
      this.description =`Showing ${this._boostIdBlockservice.models.length} results`;   
    }
    else if(tab2 ==='healing') {
      this.title = 'Healing ID Blocks';
      this.description =`Showing ${this._healingtIdBlockservice.models.length} results`; 
    }
    else if(tab2 ==='defensive') {
      this.title = 'Defensive ID Blocks';
      this.description =`Showing ${this._defensiveIdBlockservice.models.length} results`; 
    }
    else if(tab2 ==='negative') {
      this.title = 'Negative ID Blocks';
    this.description =`Showing ${this._negativeIdBlockservice.models.length} results`;  
    }
    else if(tab2 ==='ailment') {
      this.title = 'Ailment ID Blocks';
      this.description =`Showing ${this._ailmentIdBlockservice.models.length} results`; 
    }
    else if(tab2 ==='dispel') {
      this.title = 'Dispel ID Blocks'; 
      this.description =`Showing ${this._dispelIdBlockservice.models.length} results`; 
    }
    else if(tab2 ==='chaos') {
      this.title = 'Chaos ID Blocks'; 
      this.description =`Showing ${this._chaosIdBlockservice.models.length} results`; 
    }

    localStorage.setItem(`tab-StatusEffectComponent${FILTER_SUFFIX_PATH}`, this.activeTab2);

  }
    //Submenu do Status Effect Table
    switchToTab3(tab3: string){

    this.activeTab3 = tab3;

    if(tab3 ==='boostTable') {
      this.title = 'Boost Table';
     this.description =`Showing ${this._boostTableService.models.filter(boost => boost.idBoost !== "").length} results`;   
    }
    else if(tab3 ==='healingTable') {
      this.title = 'Healing Table';
      this.description =`Showing ${this._healingTableService.models.filter(healing => healing.idHealing !== "").length} results`;  
    }
    else if(tab3 ==='defensiveTable') {
      this.title = 'Defensive Table';
      this.description =`Showing ${this._defensiveTableService.models.filter(defensive => defensive.idDefensive !== "").length} results`;  
    }
    else if(tab3 ==='negativeTable') {
      this.title = 'Negative Table';
      this.description =`Showing ${this._negativeTableService.models.filter(negative => negative.idNegative !== "").length} results`;
     }
    else if(tab3 ==='ailmentTable') {
      this.title = 'Ailment Table';
      this.description =`Showing ${this._ailmentTableService.models.filter(ailment => ailment.idAilment !== "").length} results`;
    }
    else if(tab3 ==='dispelTable') {
      this.title = 'Dispel Table'; 
      this.description =`Showing ${this._dispelTableService.models.filter(dispel => dispel.idDispel !== "").length} results`;
    }
    else if(tab3 ==='hybridTable') {
      this.title = 'Hybrid Table'; 
      this.description =`Showing ${this._hybridTableService.models.filter(hibrid => hibrid.idHybrid !== "").length} results`; 
    }
    else if(tab3 ==='afflictionTable') {
      this.title = 'Affliction Table'; 
      this.description =`Showing ${this._afflictionTableService.models.filter(affliction => affliction.idAffliction !== "").length} results`;
    }
    else if(tab3 ==='combo') {
      this.title = 'Combo Table'; 
     this.description =`Showing ${this._comboTableService.models.filter(combo => combo.idCombo !== "").length} results`;
    }
    else if(tab3 ==='chaosTable') {
      this.title = 'Chaos Table'; 
     this.description =`Showing ${this._chaosTableService.models.filter(chaos => chaos.idChaosTable !== "").length} results`;
    }

    localStorage.setItem(`tab-StatusEffectComponent${FILTER_SUFFIX_PATH}`, this.activeTab2);

  }

  receiveText(text: string) {
    this.description = text;
  }

  search(searchWord: string)
  {
   /* this.statusClasses = [];
    if(searchWord == '') this.statusClasses = this.allStatusClasses;
    else
    {
      for(let i = 0; i < this.allStatusClasses.length; i++)
      {
        if(this.allStatusClasses[i].trigger.toLowerCase().includes(searchWord.toLowerCase()) ||
          this.allStatusClasses[i].status.toLowerCase().includes(searchWord.toLowerCase()) ||
          this.allStatusClasses[i].operator.toLowerCase().includes(searchWord.toLowerCase()) ||
          this.allStatusClasses[i].name.toLowerCase().includes(searchWord.toLowerCase()) ||
          this.allStatusClasses[i].description.toLowerCase().includes(searchWord.toLowerCase()) ||
          this.allStatusClasses[i].skill.toLowerCase().includes(searchWord.toLowerCase()))
            this.statusClasses.push(this.allStatusClasses[i]);
      }
    }
    this.description = `Showing ${this.statusClasses.length} results`;
    */
  }
}
