import { Component, OnInit } from '@angular/core';
import { OptionService } from 'src/app/services/option.service';
import { AreaService } from 'src/app/services/area.service';
import { Level, Dialogue, Option } from 'src/app/lib/@bus-tier/models';
import { byAreaAndLevelByExtraction } from 'src/app/lib/@bus-tier/sorting';
import { LevelService } from 'src/app/services/level.service';
import { SortingService } from 'src/app/services/sorting.service';
import { Router } from '@angular/router';
@Component({
  selector: 'app-weight-report',
  templateUrl: './weight-report.component.html',
  styleUrls: ['./weight-report.component.scss'],
})
export class WeightReportComponent implements OnInit 
{
  negativeOptions: Option[];
  positiveOptions: Option[];
  negativeOptionsWeightTotal = 0;
  positiveOptionsWeightTotal = 0;

  constructor(
    private _levelService: LevelService,
    private _optionService: OptionService,
    private _areaService: AreaService,
    private _router: Router,
    private _sorting : SortingService,
  ) {}

 

  ngOnInit(): void 
  {
    this.negativeOptions = this._optionService.models.filter(
      (option) => option.weight < 0
    );
    this.positiveOptions = this._optionService.models.filter(
      (option) => option.weight > 0
    );
    this.negativeOptions.forEach((option) => 
    {
      this.negativeOptionsWeightTotal += +option.weight;
    });
    this.positiveOptions.forEach((option) => 
    {
      this.positiveOptionsWeightTotal += +option.weight;
    });

    // sorts omitted options by hierarchy code then level
    this.negativeOptions.sort((a, b) =>
      byAreaAndLevelByExtraction(a, b, this._areaService, this._levelService)
    );

    // sorts negative options by hierarchy code then level
    this.positiveOptions.sort((a, b) =>
      byAreaAndLevelByExtraction(a, b, this._areaService, this._levelService)
    );
  }
  extractNumericValuesFromLevelId(str: string): [string, number, number] 
  {	
    const regex = /([A-Z])(\d+).*L(\d+)/;	
    const match = regex.exec(str);	
    if(match) 
    {	
        const letter = match[1];	
        const numberA = parseInt(match[2]);	
        const numberB = parseInt(match[3]);	
        return [letter ,numberA, numberB];	
    }	
    return ['', 0, 0];	
  }	
  reverseWeight = true;	
  sortArrayByWeight(array: Option[]) 
  {	
    array.sort((a, b) => 
    {	
        return this.reverseWeight! ? b.weight - a.weight : a.weight - b.weight;	
    })	
    this.reverseWeight = !this.reverseWeight;	
  }	
  	
  reverseMessage = true;	
  sortArrayByMessage(array: Option[]) 
  {	
    array.sort((a, b) => 
    {	
        return this.reverseMessage! ? b.message.localeCompare(a.message) : a.message.localeCompare(b.message);	
    });	
    this.reverseMessage = !this.reverseMessage;	
  }	
  reverseCode = true;	
  sortArrayByCode(array: Option[]) 
  {	
    array.sort((a, b) => 
    {	
        return byAreaAndLevelByExtraction(a, b, this._areaService, this._levelService)	
    });	
    if(this.reverseCode) { array.reverse(); }	
    this.reverseCode = !this.reverseCode;	
  }
  access(obj: any): void 
  {
    this._router.navigate(
      [
        'levels/' +
          Level.getSubIdFrom(obj.id) +
          '/dialogues/' +
          Dialogue.getSubIdFrom(obj.id, 'PT-BR'),
      ],
      { fragment: obj.id }
    );
  }
}
