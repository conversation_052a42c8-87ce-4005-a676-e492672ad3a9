import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { showAlert } from 'src/lib/common/alert';
import { Alert } from 'src/lib/darkcloud';
import { Router } from '@angular/router';
import { AreaService, AttributeDiceFrustrationService, CharacterService, ClassService, ItemService, KeywordService, KnowledgeDiceFrustrationService, KnowledgeService, LevelService, MapsService, MissionService, ModifierService, ObjectiveService, OpenAIEnvironmentService, OpenAiPromptService, SpeechService, StatusService, StoryExpansionPkgService, TierService} from 'src/app/services';
import { AIPromptService } from 'src/app/services/ai-prompt.service';
import { firstValueFrom, Subject, Subscription, takeUntil } from 'rxjs';
import { ICounterItems } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard/ICounterItems';
import { ItemClassService } from 'src/app/services/item-class.service';
import { AIPrompt } from 'src/app/lib/@bus-tier/models/AIPrompt';
import { OpenAIEnvironment } from 'src/app/lib/@bus-tier/models/OpenAIEnvironment';
import { AtributteService } from 'src/app/services/atributte.service';
import { ElementalDefensesService } from 'src/app/services/elementalDefenses.service';
import { AilmentService } from 'src/app/services/ailment.service';
import { SituationalModifierService } from 'src/app/services/situational-modifier.service';



export interface RevisorAI {
  id: string;
  name?: string;
  description?: string;
  title?: string;
  message?: string;
  word?: string;
  newName?: string;
  newDescription?: string;
  newMessage?: string;
  newTitle?: string;
  newWord?: string;
  newLight?: string[];
  newModerate?: string[];
  newCritical?: string[];
  light?: string[];
  moderate?: string[];
  critical?: string[];
  counterObject?: number;
  // Propriedades para controle de edição
  isEditingName?: boolean;
  isEditingTitle?: boolean;
  isEditingDescription?: boolean;
  isEditingMessage?: boolean;
  isEditingWord?: boolean;
}

export interface RevisorSituationalModifier {
  id: string;
  factor?: string[];
  description?: string[];
  newFactor?: string[];
  newDescription?: string[];
}

export interface RevisorObjectAI {
  id: string;
  light?: string;
  moderate?: string;
  critical?: string;
  position?: number;
  factor?: string;
  description?: string;
  newFactor?: string;
  newLight?: string;
  newModerate?: string;
  newCritical?: string; 
  newDescription?: string;
  // Propriedades para controle de edição
  isEditingLight?: boolean;
  isEditingModerate?: boolean;
  isEditingCritical?: boolean;
  isEditingFactor?: boolean;
  isEditingDescription?: boolean;
}

export interface IViewSend {
  id?: string;
  name?: string;
  lote?: string;
  textValue?: string[];
  textValueString?: string;
}

@Component({
  selector: 'ai-revisor',
  templateUrl: './ai-revisor.component.html',
  styleUrls: ['./ai-revisor.component.scss'],
})

export class AiRevisorComponent implements OnInit {

  cancel$ = new Subject<void>();
  subscription: Subscription;
  ngUnsubscribe = new Subject<void>();
  title = 'AI Revisor';
  description = '';
  rejectedTexts: string[] = [];
  rejectedCorrections: string[] = [];
  listRevisor: RevisorAI[] = [];
  listRevisorSituationalModifier: RevisorSituationalModifier[] = [];
  listObjectRevisor: RevisorObjectAI[] = [];
  listRevisorObject: RevisorAI;
  listContent: RevisorAI[] = [];
  listViewSend: IViewSend[] = [];
  counterItems: ICounterItems[] = [];
  lenghtLote = [];
  environment: OpenAIEnvironment;
  prompt: AIPrompt;
  sendingBatch: string;
  RevisedItems: string
  UnreviewedItems: string
  isSelectedItem = false;
  isLoadingLotes = false;
  isRemove = false;
  isLotesObeject = false;
  isModalInfo = false;
  isCancelled = false;
  islookSend = false;
  isSendLote: boolean;
  isSituationalModifier = false;
  nameDB: string;
  completedAnalysis: string 
  selectedItem: string;
  renderIndex = 1;
  seqLote = 0;
  


  constructor(
    private _router: Router,
    private _openAiPromptService: OpenAiPromptService,
    private _aiPromptService: AIPromptService,
    private _areaService: AreaService,
    private _levelService: LevelService,
    private _characterService: CharacterService,
    private _change: ChangeDetectorRef,
    private _classService: ClassService,
    private _missionService: MissionService,
    private _objectiveService: ObjectiveService,
    private _itemClassService: ItemClassService,
    private _openAIEnvironmentService: OpenAIEnvironmentService,
    private _speechService: SpeechService,
    private _itemService: ItemService,
    private _keywordService: KeywordService,
    private _statusService: StatusService,
    private _modifierService: ModifierService,
    private _tierService: TierService,
    private _atributteService: AtributteService,
    private _knowledgeService: KnowledgeService,
    private _attributediceFrustrationService: AttributeDiceFrustrationService,
    public _knowledgeDiceFrustrationService: KnowledgeDiceFrustrationService,
    private _elementalDefensesService: ElementalDefensesService,
    private _ailmentService: AilmentService,
    private _mapsService: MapsService,
    private _storyExpansionService: StoryExpansionPkgService,
    private _situationalModifierService: SituationalModifierService,
  ) { }

  async ngOnInit() {
    Alert.showLoadingPleaseWiat();
    this._aiPromptService.toFinishLoading(); 
    
    this._areaService.toFinishLoading(); 
    this._levelService.toFinishLoading();  
    this._speechService.toFinishLoading();
    this._characterService.toFinishLoading();
    this._classService.toFinishLoading();
    this._missionService.toFinishLoading();
    this._objectiveService.toFinishLoading();
    this._itemClassService.toFinishLoading();
    this._openAIEnvironmentService.toFinishLoading();
    this._itemService.toFinishLoading();
    this._keywordService.toFinishLoading();
    this._statusService.toFinishLoading();
    this._modifierService.toFinishLoading();
    this._tierService.toFinishLoading();
    this._atributteService.toFinishLoading();
    this._knowledgeService.toFinishLoading();
    this._attributediceFrustrationService.toFinishLoading();
    this._knowledgeDiceFrustrationService.toFinishLoading();
    this._elementalDefensesService.toFinishLoading();
    this._ailmentService.toFinishLoading();
    this._mapsService.toFinishLoading();
    this._storyExpansionService.toFinishLoading();
    this._situationalModifierService.toFinishLoading();
           
    this.nameDB = '';
    this.isLoadingLotes = false;
    this.getAIPrompt();
    this.checkCounter();
    this.getDBCounterItems(); 
    console.log("SpeechService",  this._speechService)
  }

  getAIPrompt() {

    this.prompt = this._aiPromptService.models.find((x) => x?.selectType === 'AI Revisor');

    if (!this.prompt?.prompt) {
       Alert.WARNINGNOPROMPT(
        'No prompt selected',
        'Check the AI Prompt screen for prompt and type or exit and enter the screen again.'
       );
    }   
    this.environment = this._openAIEnvironmentService.svcFindById(this.prompt?.idEnvironmentAi);
  }
//Verifica o limite de revisão
  checkCounter() {
    this._areaService.checkCounterAreas(this.prompt.idEnvironmentAi);// Areas
    this._levelService.checkCounterLevels(this.prompt.idEnvironmentAi);// Levels
    this._speechService.checkCounterSpeech(this.prompt.idEnvironmentAi); // Speech
    this._characterService.checkCounterCharacters(this.prompt.idEnvironmentAi); // Characters
    this._classService.checkCounterClass(this.prompt.idEnvironmentAi);// Classes
    this._missionService.checkCounterMissions(this.prompt.idEnvironmentAi); // Missions
    this._objectiveService.checkCounterObjectives(this.prompt.idEnvironmentAi); // Objectives
    this._itemClassService.checkCounterItemClasses(this.prompt.idEnvironmentAi);// Item Classes
    this._itemService.checkCounterItemsList(this.prompt.idEnvironmentAi);// Items List
    this._keywordService.checkCounterKeywordList(this.prompt.idEnvironmentAi); // Keywords
    this._statusService.checkCounterSkillList(this.prompt.idEnvironmentAi); // Skills
    this._modifierService.checkCounterModifierList(this.prompt.idEnvironmentAi); // Modifier
    this._tierService.checkCounterTierList(this.prompt.idEnvironmentAi); // Tier List   
    this._atributteService.checkCounterAtributteList(this.prompt.idEnvironmentAi); // Atributte 
    this._knowledgeService.checkCounterKnowledgeList(this.prompt.idEnvironmentAi); // Knowledge
    this._attributediceFrustrationService.checkCounterDiceFrustration(this.prompt.idEnvironmentAi); // Atributte - Dice Frustration
    this._knowledgeDiceFrustrationService.checkCounterKnowledgeDiceFrustration(this.prompt.idEnvironmentAi) // Knowledge - Dice Frustration
    this._elementalDefensesService.checkCounterElementalDefenses(this.prompt.idEnvironmentAi); // Elemental Defenses
    this._ailmentService.checkCounterAilments(this.prompt.idEnvironmentAi); // Ailment
    this._mapsService.checkCounterMaps(this.prompt.idEnvironmentAi);// Maps
    this._storyExpansionService.checkCounterStoryExpansion(this.prompt.idEnvironmentAi);// Story Expansion
    this._situationalModifierService.checkCounterSituationalModifier(this.prompt.idEnvironmentAi); // Situational Modifier

  }

//Verifica a quantidade de items para revisão e as informações do menu lateral
  getDBCounterItems() {
    this.counterItems = []; 

    this.counterItems.push(...(this._areaService.areaItemsCounter() as ICounterItems[]));
    this.counterItems.push(...(this._levelService.levelItemsCounter() as ICounterItems[]));
    this.counterItems.push(...(this._speechService.speechItemsCounter() as ICounterItems[]));    
    this.counterItems.push(...(this._characterService.characterItemsCounter() as ICounterItems[]));  
    this.counterItems.push(...(this._classService.classItemsCounter() as ICounterItems[])); 
    this.counterItems.push(...(this._missionService.missionsItemsCounter() as ICounterItems[]));  
    this.counterItems.push(...(this._objectiveService.objectivesItemsCounter() as ICounterItems[]));   
    this.counterItems.push(...(this._itemClassService.itemClassesCounter() as ICounterItems[]));
    this.counterItems.push(...(this._itemService.itemsLitsCounter() as ICounterItems[]));
    this.counterItems.push(...(this._keywordService.keywordListItemsCounter() as ICounterItems[]));
    this.counterItems.push(...(this._statusService.skillLitsCounter() as ICounterItems[])); 
    this.counterItems.push(...(this._modifierService.modifierLitCounter() as ICounterItems[])); 
    this.counterItems.push(...(this._tierService.tierListCounter() as ICounterItems[]));    
    this.counterItems.push(...(this._atributteService.atributteLitCounter() as ICounterItems[]));
    this.counterItems.push(...(this._knowledgeService.knowledgeLitCounter() as ICounterItems[]));
    this.counterItems.push(...(this._attributediceFrustrationService.diceFrustrationCounter() as ICounterItems[]));
    this.counterItems.push(...(this._knowledgeDiceFrustrationService.knowledgediceFrustrationCounter() as ICounterItems[]));
    this.counterItems.push(...(this._elementalDefensesService.elementalDefensesLitCounter() as ICounterItems[]));
    this.counterItems.push(...(this._ailmentService.ailmentsLitCounter() as ICounterItems[]));
    this.counterItems.push(...(this._mapsService.mapsLitCounter() as ICounterItems[]));  
    this.counterItems.push(...(this._storyExpansionService.storyExpansionLitCounter() as ICounterItems[]));
    this.counterItems.push(...(this._situationalModifierService.situationalModifierCounter() as ICounterItems[]));
    
    this._change.detectChanges();

   // console.log( 'Quantidade de  Items',this.counterItems);
  }
//Seleciona qual banco de dados vai ser revisado e seus conteúdos
  databaseToReview(item: ICounterItems) {
    console.log('Selecionou: ',item);
    if (item.unreviewedItems === 0) {
      Alert.showMessage('No items to review. All items have been reviewed');
      return;
    } 
    this.nameDB = item.name;
    this.listContent = [];
    this.listRevisorSituationalModifier = [];
    this.selectedItem = item.name; 
    this.isLotesObeject = false;
    this.listViewSend = [];
  
   // console.log('Selecionou: ',item);

    if (this.selectedItem === 'Areas') {  
    this.listContent = [
      ...this._areaService.models.filter(area => !area.isReviewedName && area.name !== undefined && area.name.trim() !== '').map(area => ({
        id: area.id,
        name: area.name
      })),
      ...this._areaService.models.filter(area => !area.isReviewedDescription && area.description !== undefined && area.description.trim() !== '').map(area => ({
        id: area.id,
        description: area.description
      }))
    ];
     this.reviewingAIItems(); 

  }
    if (this.selectedItem === 'Levels') {
      this.listContent = [
          ...this._levelService.models.filter(level => !level.isReviewedName && level.name !== undefined && level?.name.trim() !== '').map(level => ({
            id: level.id,
            name: level.name
          })),
          ...this._levelService.models.filter(level => !level.isReviewedDescription && level.description !== undefined && level?.description.trim() !== '').map(level => ({
            id: level.id,
            description: level.description
          }))
        ];
      this.reviewingAIItems();
  }
  if (this.selectedItem === 'Speech') {
    this.listContent = [
        ...this._speechService.models.filter(speech => !speech.isReviewedMessage && (speech?.message && speech.message !== undefined && speech?.message.trim() !== '')).map(speech => ({
          id: speech.id,
          message: speech.message
        }))
      ];
    this.reviewingAIItems();
}
    if (this.selectedItem === 'Characters') {  
      this.listContent = [
        ...this._characterService.models.filter(character => !character.isReviewedName && character.name !== undefined && character?.name.trim() !== '').map(character => ({
          id: character.id,
          name: character.name
        })),
        ...this._characterService.models.filter(character => !character.isReviewedDescription && character.description !== undefined && character?.description.trim() !== '').map(character => ({
          id: character.id,
          description: character.description
        }))
        ,
        ...this._characterService.models.filter(character => !character.isReviewedTitle && character.title !== undefined && character?.title.trim() !== '').map(character => ({
          id: character.id,
          title: character.title
        }))
      ];
      this.reviewingAIItems();
    }
    if (this.selectedItem === 'Classes') {
      this.listContent = [
        ...this._classService.models.filter(classes => !classes.isReviewedName && classes.name !== undefined && classes?.name.trim() !== '').map(classes => ({
          id: classes.id,
          name: classes.name
        })),
        ...this._classService.models.filter(classes => !classes.isReviewedDescription && classes.description !== undefined && classes?.description.trim() !== '').map(classes => ({
          id: classes.id,
          description: classes.description
        }))
      ];
       this.reviewingAIItems();
    }
    if (this.selectedItem === 'Missions') {
      this.listContent = [
        ...this._missionService.models.filter(mission => !mission.isReviewedName && mission.name !== undefined && mission?.name.trim() !== '').map(mission => ({
          id: mission.id,
          name: mission.name
        })),
        ...this._missionService.models.filter(mission => !mission.isReviewedDescription && mission.description !== undefined && mission?.description.trim() !== '').map(mission => ({
          id: mission.id,
          description: mission.description
        }))
      ];
       this.reviewingAIItems();
    }
    if (this.selectedItem === 'Objectives') {
      this.listContent = [
        ...this._objectiveService.models.filter(objective => !objective.isReviewedDescription && objective.description !== undefined && objective?.description.trim() !== '').map(objective => ({
          id: objective.id,
          description: objective.description
        }))
      ];
       this.reviewingAIItems();
    }
    if (this.selectedItem === 'Item Classes') {
      this.listContent = [
        ...this._itemClassService.models.filter(itemClasses => !itemClasses.isReviewedName && itemClasses.name !== undefined && itemClasses?.name.trim() !== '').map(itemClasses => ({
          id: itemClasses.id,
          name: itemClasses.name
        })),
        ...this._itemClassService.models.filter(itemClasses => !itemClasses.isReviewedDescription && itemClasses.description !== undefined && itemClasses?.description.trim() !== '').map(itemClasses => ({
          id: itemClasses.id,
          description: itemClasses.description
        }))
      ];
       this.reviewingAIItems();
    }
    if (this.selectedItem === 'Items List') {
      this.listContent = [
        ...this._itemService.models.filter(itemList => !itemList.isReviewedName && itemList.name !== undefined && itemList?.name.trim() !== '').map(itemList => ({
          id: itemList.id,
          name: itemList.name
        })),
        ...this._itemService.models.filter(itemList => !itemList.isReviewedDescription && itemList.description !== undefined && itemList?.description.trim() !== '').map(itemList => ({
          id: itemList.id,
          description: itemList.description
        }))
      ];
       this.reviewingAIItems();
    } 
    if (this.selectedItem === 'Keywords') {
      this.listContent = [
        ...this._keywordService.models.filter(word => !word.isReviewedWord && word.word !== undefined && word?.word.trim() !== '').map(word => ({
          id: word.id,
          word: word.word
        })), 
      ];
       this.reviewingAIItems();
    }
    if (this.selectedItem === 'Skills') {
      this.listContent = [
        ...this._statusService.models.filter(itemList => !itemList.isReviewedSkill && itemList.skill !== undefined && itemList?.skill.trim() !== '').map(itemList => ({
          id: itemList.id,
          name: itemList.skill
        })),
        ...this._statusService.models.filter(itemList => !itemList.isReviewedDescription && itemList.description !== undefined && itemList?.description.trim() !== '').map(itemList => ({
          id: itemList.id,
          description: itemList.description
        }))
      ];
       this.reviewingAIItems();
    }
    if (this.selectedItem === 'Modifier') {
      this.listContent = [
        ...this._modifierService.models.filter(itemList => !itemList.isReviewedSkill && itemList.skill !== undefined && itemList?.skill.trim() !== '').map(itemList => ({
          id: itemList.id,
          name: itemList.skill
        })),
        ...this._modifierService.models.filter(itemList => !itemList.isReviewedDescription && itemList.description !== undefined && itemList?.description.trim() !== '').map(itemList => ({
          id: itemList.id,
          description: itemList.description
        }))
      ];
       this.reviewingAIItems();
    } 
    if (this.selectedItem === 'Tier List') {
      this.listContent = [
        ...this._tierService.models.filter(objective => !objective.isReviewedName && objective.name !== undefined && objective?.name.trim() !== '').map(objective => ({
          id: objective.id,
          name: objective.name
        }))
      ];
       this.reviewingAIItems();
    }
    if (this.selectedItem === 'Atributte') {
      this.listContent = [
        ...this._atributteService.models.filter(itemList => !itemList.isReviewedAtributte && itemList.atributte !== undefined && itemList?.atributte.trim() !== '').map(itemList => ({
          id: itemList.id,
          name: itemList.atributte
        })),
        ...this._atributteService.models.filter(itemList => !itemList.isReviewedDescription && itemList.description !== undefined && itemList?.description.trim() !== '').map(itemList => ({
          id: itemList.id,
          description: itemList.description
        }))
      ];
       this.reviewingAIItems();
    }
    if (this.selectedItem === 'knowledge') {
      this.listContent = [
        ...this._knowledgeService.models.filter(itemList => !itemList.isReviewedknowledge && itemList.knowledge !== undefined && itemList?.knowledge.trim() !== '').map(itemList => ({
          id: itemList.id,
          name: itemList.knowledge
        })),
        ...this._knowledgeService.models.filter(itemList => !itemList.isReviewedDescription && itemList.description !== undefined && itemList?.description.trim() !== '').map(itemList => ({
          id: itemList.id,
          description: itemList.description
        }))
      ];
       this.reviewingAIItems();
    } 
    if (this.selectedItem === 'Atributte - Dice Frustration') {
      this.isLotesObeject = true

      const lightList = this._attributediceFrustrationService.models.map(model => ({
        id: model.id,
        light: model.light,
        isReviewedLight: model.isReviewedLight,    
      }));
  
      lightList.forEach(itemList => {
        const filteredLight = itemList.light.filter((light, index) => {
          return itemList.isReviewedLight === undefined || !itemList?.isReviewedLight[index];
        });
        if (filteredLight.length > 0) {
          this.listContent.push({
            id: itemList.id,
            light: filteredLight
          });
        }
      });

      const moderateList = this._attributediceFrustrationService.models.map(model => ({
        id: model.id,
        moderate: model.moderate,
        isReviewedModerate: model.isReviewedModerate  
      }));

      moderateList.forEach(itemList => {
        const filteredModerate = itemList.moderate.filter((moderate, index) => {
          return itemList.isReviewedModerate === undefined || !itemList?.isReviewedModerate[index];
        });
        if (filteredModerate.length > 0) {
          this.listContent.push({
            id: itemList.id,
            moderate: filteredModerate
          });
        }
      });

      const criticalList = this._attributediceFrustrationService.models.map(model => ({
        id: model.id,
        critical: model.critical,
        isReviewedCritical: model.isReviewedCritical
      }));

      criticalList.forEach(itemList => {
        const filteredCritical = itemList.critical.filter((critical, index) => {
          return itemList.isReviewedCritical === undefined || !itemList?.isReviewedCritical[index];
        });
        if (filteredCritical.length > 0) {
          this.listContent.push({
            id: itemList.id,
            critical: filteredCritical
          });
        }
      });  
  
     // Alert.showMessage('Funcionalidade em desenvolvimento.');
       this.reviewingObjectsAI();
    } 
    if (this.selectedItem === 'Knowledge - Dice Frustration') {

      this.isLotesObeject = true

      const lightList = this._knowledgeDiceFrustrationService.models.map(model => ({
        id: model.id,
        light: model.light,
        isReviewedLight: model.isReviewedLight,    
      }));
  
      lightList.forEach(itemList => {
        const filteredLight = itemList.light.filter((light, index) => {
          return itemList.isReviewedLight === undefined || !itemList?.isReviewedLight[index];
        });
        if (filteredLight.length > 0) {
          this.listContent.push({
            id: itemList.id,
            light: filteredLight
          });
        }
      });

      const moderateList = this._knowledgeDiceFrustrationService.models.map(model => ({
        id: model.id,
        moderate: model.moderate,
        isReviewedModerate: model.isReviewedModerate  
      }));

      moderateList.forEach(itemList => {
        const filteredModerate = itemList.moderate.filter((moderate, index) => {
          return itemList.isReviewedModerate === undefined || !itemList?.isReviewedModerate[index];
        });
        if (filteredModerate.length > 0) {
          this.listContent.push({
            id: itemList.id,
            moderate: filteredModerate
          });
        }
      });

      const criticalList = this._knowledgeDiceFrustrationService.models.map(model => ({
        id: model.id,
        critical: model.critical,
        isReviewedCritical: model.isReviewedCritical
      }));

      criticalList.forEach(itemList => {
        const filteredCritical = itemList.critical.filter((critical, index) => {
          return itemList.isReviewedCritical === undefined || !itemList?.isReviewedCritical[index];
        });
        if (filteredCritical.length > 0) {
          this.listContent.push({
            id: itemList.id,
            critical: filteredCritical
          });
        }
      });  
      
       this.reviewingObjectsAI();
    }

    if (this.selectedItem === 'Elemental Defenses') {
      this.listContent = [
        ...this._elementalDefensesService.models.filter(itemList => !itemList.isReviewedDefenses && itemList.defenses !== undefined && itemList?.defenses.trim() !== '').map(itemList => ({
          id: itemList.id,
          name: itemList.defenses
        })),
        ...this._elementalDefensesService.models.filter(itemList => !itemList.isReviewedDescription && itemList.description !== undefined && itemList?.description.trim() !== '').map(itemList => ({
          id: itemList.id,
          description: itemList.description
        }))
      ];
       this.reviewingAIItems();
    }
    if (this.selectedItem === 'Ailment') {
      this.listContent = [
        ...this._ailmentService.models.filter(itemList => !itemList.isReviewedAilment && itemList.ailment !== undefined && itemList?.ailment.trim() !== '').map(itemList => ({
          id: itemList.id,
          name: itemList.ailment
        })),
        ...this._ailmentService.models.filter(itemList => !itemList.isReviewedDescription && itemList.description !== undefined && itemList?.description.trim() !== '').map(itemList => ({
          id: itemList.id,
          description: itemList.description
        }))
      ];
       this.reviewingAIItems();
    }
    if (this.selectedItem === 'Maps') {
      this.listContent = [
        ...this._mapsService.models.filter(itemList => !itemList.isReviewedName && itemList.name !== undefined && itemList?.name.trim() !== '').map(itemList => ({
          id: itemList.id,
          name: itemList.name
        })),
        ...this._mapsService.models.filter(itemList => !itemList.isReviewedDescription && itemList.description !== undefined && itemList?.description.trim() !== '').map(itemList => ({
          id: itemList.id,
          description: itemList.description
        }))
      ];
       this.reviewingAIItems();
    }
    if (this.selectedItem === 'Story Expansion Pack') {
      this.listContent = [
        ...this._storyExpansionService.models.filter(itemList => !itemList.isReviewedName && itemList.name !== undefined && itemList?.name.trim() !== '').map(itemList => ({
          id: itemList.id,
          name: itemList.name
        })),
        ...this._storyExpansionService.models.filter(itemList => !itemList.isReviewedDescription && itemList.description !== undefined && itemList?.description.trim() !== '').map(itemList => ({
          id: itemList.id,
          description: itemList.description
        }))
      ];
       this.reviewingAIItems();
    }
     if (this.selectedItem === 'Situational Modifier') {
      this.isLotesObeject = true;
      this.isSituationalModifier = true;

      const factorList = this._situationalModifierService.models.map(model => ({
        id: model.id,
        factor: model.factor,
        isReviewedFactor: model.isReviewedFactor,    
      }));
  
      factorList.forEach(itemList => {
        const filteredFactor = itemList.factor.filter((factor, index) => {
          return itemList.isReviewedFactor === undefined || !itemList?.isReviewedFactor[index];
        });
        if (filteredFactor.length > 0) {
          this.listRevisorSituationalModifier.push({
            id: itemList.id,
            factor: filteredFactor
          });
        }
      });

      const descriptionList = this._situationalModifierService.models.map(model => ({
        id: model.id,
        description: model.description,
        isReviewedDescription: model.isReviewedDescription  
      }));

      descriptionList.forEach(itemList => {
        const filteredDescription = itemList.description.filter((description, index) => {
          return itemList.isReviewedDescription === undefined || !itemList?.isReviewedDescription[index];
        });
        if (filteredDescription.length > 0) {
          this.listRevisorSituationalModifier.push({
            id: itemList.id,
            description: filteredDescription
          });
        }
      });  
   
       this.reviewingObjectsAI();
    } //Fim do Situational Modifier
    
  }

  async reviewingAIItems() {
    this.isLoadingLotes = true;
    this.isCancelled = false;
    this.completedAnalysis = '';
    this.ngUnsubscribe = new Subject<void>(); // recria subject   
    this.listRevisor = [];
    this.isSelectedItem = true;
    let lotes;

    //console.log('PEGOU CONTEÚDOS PARA REVISAR: ', this.listContent);

        lotes = await this._openAiPromptService.dividirLotesPorTokens(
        this.listContent,
        this.prompt.prompt,
        8192,
        15,
        true // verbose
      );   
      const tokens = this._openAiPromptService.contarTokensLote(lotes);
     // console.log(`Esse lote tem aproximadamente ${tokens} tokens.`);
    //  console.log('Quantidade de Lotes: ', lotes);
      this.lenghtLote = lotes;

    try {
      for (let i = 0; i < lotes.length; i++) {
        const lote = lotes[i];
        this.sendingBatch = `Analisando lote ${i + 1}/${lotes.length}...`;
        this.seqLote = i; 
        this.isSendLote = false;       
        this.effectHighlight();
        console.log('Lote enviado para analise: ', lote);

      //Adiciona os itens enviados para revisão na lista de envio para verificação no modal
      lote.forEach(item => {
        Object.keys(item).forEach(key => {
          if (key !== 'id') {
            console.log('Conteúdo do campo: ', item[key]);  
            const viewSendItem: IViewSend = {
              id: item.id,
              name: key,
              lote: `${i + 1}/${lotes.length}`,
              textValueString: item[key]
            };
            this.listViewSend.push(viewSendItem);
          }
        });
      });

        const data = await firstValueFrom(
          this._openAiPromptService.getRevisorAIPrompt(this.prompt, lote).pipe(
            takeUntil(this.ngUnsubscribe)
          )
        );      
      
        const content = data.choices[0].message.content;
       // console.log('Data.choices[0]: ', data.choices[0]);
        if (data.choices[0].finish_reason === 'length') {
          console.warn(`⚠️ Lote ${i + 1} truncado. Considere aumentar o tamanho do lote.`);
        }

        const cleanedContent = content.replace(/```json/gi, '').replace(/```/g, '').trim();
      //  console.log('CONTEUDO LOTE: ', cleanedContent);
        const jsonLote = JSON.parse(cleanedContent);
             
        this.listRevisor.push(...jsonLote);
        this.listRevisor = this.listRevisor.filter((item) => !!item?.newName || !!item?.newTitle || !!item?.newDescription
        || !!item?.newMessage || !!item?.newWord);
      
         //Atualiza a descrição do item
        this.listRevisor = this.listRevisor.map((revisor) => {
        const contents = this.listContent.filter((c) => c.id === revisor.id && c?.description);
       // console.log('CONTENTS da descrição: ', contents);
        if (contents.length > 0) {
          return { ...revisor, description: contents[0].description };
        }
        return revisor;
      }); 
     // console.log('Conteúdo - revisão', this.listRevisor);
      this.isSendLote = true;   
       this.filterRevisorList();
       this.addCounterItems();

        const currentItem = this.counterItems.find((item) => item.name === this.nameDB);
        if (currentItem) {
          currentItem.suggestions = this.listRevisor.length;
        }
        
        this._change.detectChanges();
        await new Promise(resolve => setTimeout(resolve));
      }
  
    //  console.log('Revisão concluida!', this.listRevisor);
      this.completedAnalysis = 'Revisão concluída.';
      this.updateSuggestions();
  
    } catch (e) {
      this.completedAnalysis = 'Revisão cancelada.';
      this.sendingBatch = 'Revisão cancelada.';     
      console.error('Revisão interrompida ou falhou:', e);
    
    } finally {   
      this.isLoadingLotes = false;
      this.isLotesObeject = false;
      this._change.detectChanges();
    }
  }
  
  async reviewingObjectsAI() {
    this.isLoadingLotes = true;
    this.isCancelled = false;
    this.completedAnalysis = '';
    this.ngUnsubscribe = new Subject<void>(); // recria subject   
    this.listRevisor = [];  
    this.listViewSend = [];  
    this.isSelectedItem = true;
    let lotes;
    let listObject: RevisorAI;
    let listObjectSituationalModifier: RevisorSituationalModifier;

   // console.log('PEGOU CONTEÚDOS PARA listContent: ', this.listContent);

   if (this.isSituationalModifier) {
      lotes = this.listRevisorSituationalModifier;
   } 
   else {
      lotes = this.listContent;
   }

     // const tokens = this._openAiPromptService.contarTokensLote(lotes);
     // console.log(`Esse lote tem aproximadamente ${tokens} tokens.`);
     // console.log('Quantidade de Lotes: ', lotes);
      this.lenghtLote = lotes;

    try {
      for (let i = 0; i < lotes.length; i++) {
        const lote = lotes[i];
        this.sendingBatch = `Analisando lote ${i + 1}/${lotes.length}...`;
        this.seqLote = i; 
        this.isSendLote = false;       
       // this.effectHighlight();
        console.log(`Analisando lote ${i + 1}/${lotes.length}...`);
        console.log('Nome do Lote: ', lote);
        console.log('Id Lote: ', lote.id);
        console.log('Lote enviado para analise: ', lote);
        //Adiciona os itens enviados para revisão na lista de envio para verificação no modal
         Object.keys(lote).forEach(key => {
          if (key !== 'id') {
            const item: IViewSend = {
              id: lote.id,
              name: key,
              lote: `${i + 1}/${lotes.length}` ,
              textValue: lote[key]
            };
            this.listViewSend.push(item);
          }
        });           


        const data = await firstValueFrom(
          this._openAiPromptService.getRevisorAIPromptObject(this.prompt, lote).pipe(
            takeUntil(this.ngUnsubscribe)
          )
        );

        const content = data.choices[0].message.content;
      //  console.log('Data.choices[0]: ', data.choices[0]);
        if (data.choices[0].finish_reason === 'length') {
          console.warn(`⚠️ Lote ${i + 1} truncado. Considere aumentar o tamanho do lote.`);
        }

        const cleanedContent = content.replace(/```json/gi, '').replace(/```/g, '').trim();
       // console.log('CONTEUDO LOTE: ', cleanedContent);

       if (this.isSituationalModifier) {
          listObjectSituationalModifier = JSON.parse(cleanedContent);
          console.log('listObjectSituationalModifier: ', listObjectSituationalModifier);
          this.isSendLote = true;  
          this.addCounterItems();

          if(listObjectSituationalModifier?.newFactor && listObjectSituationalModifier?.newFactor.length > 0) {
            for (let i = 0; i < listObjectSituationalModifier.newFactor.length; i++) {
              if (listObjectSituationalModifier.newFactor[i] !== "") {
                this.listObjectRevisor.push({
                  id: listObjectSituationalModifier.id,
                  position: i,
                  factor: listObjectSituationalModifier.factor[i],
                  newFactor: listObjectSituationalModifier.newFactor[i]
                });
              }
            }
          }
          if(listObjectSituationalModifier?.newDescription && listObjectSituationalModifier?.newDescription.length > 0) {
            for (let i = 0; i < listObjectSituationalModifier.newDescription.length; i++) {
              if (listObjectSituationalModifier.newDescription[i] !== "") {
                this.listObjectRevisor.push({
                  id: listObjectSituationalModifier.id,
                  position: i,  
                  description: listObjectSituationalModifier.description[i],
                  newDescription: listObjectSituationalModifier.newDescription[i]
                });
              }
            }
          }     
        } 
        else {

          listObject = JSON.parse(cleanedContent);
          this.isSendLote = true;  
          this.addCounterItems();

        if(listObject?.newLight && listObject?.newLight.length > 0) {
          for (let i = 0; i < listObject.newLight.length; i++) {
            if (listObject.newLight[i] !== "") {
              this.listObjectRevisor.push({
                id: listObject.id,
                position: i,
                light: listObject.light[i],
                newLight: listObject.newLight[i]
              });
            }
          }
        }
     
        if(listObject?.newModerate && listObject?.newModerate.length > 0) {
          for (let i = 0; i < listObject?.newModerate.length; i++) {
            if (listObject?.newModerate[i] !== "") {
              this.listObjectRevisor.push({
                id: listObject.id,
                position: i,
                moderate: listObject.moderate[i],
                newModerate: listObject.newModerate[i]
              });
            }
          }
        }

        if(listObject?.newCritical && listObject?.newCritical.length > 0) {
          for (let i = 0; i < listObject?.newCritical.length; i++) {
            if (listObject?.newCritical[i] !== "") {
              this.listObjectRevisor.push({
                id: listObject.id,
                position: i,
                critical: listObject.critical[i],
                newCritical: listObject.newCritical[i]
              });
            }
          } 
        }

        }
  

        const currentItem = this.counterItems.find((item) => item.name === this.nameDB);
        if (currentItem) {
          currentItem.suggestions = this.listObjectRevisor.length;
      }
     
   //console.log('Novo valores: ', this.listObjectRevisor); 
       // console.log('Conteúdo que passou aqui', this.listRevisor);
        this._change.detectChanges();
        await new Promise(resolve => setTimeout(resolve));
      }   
    console.log('LISTAGEM DE ITENS ENVIADOS', this.listViewSend);
     // console.log('Revisão concluida!', this.listRevisor);
      this.completedAnalysis = 'Revisão concluída.';  
      this.updateObjectSuggestions();
  
    } catch (e) {
      this.completedAnalysis = 'Revisão cancelada.';
      this.sendingBatch = 'Revisão cancelada.';     
      console.error('Revisão interrompida ou falhou:', e);  
      console.log('LISTAGEM DE ITENS ENVIADOS', this.listViewSend);
  
    } finally {   
      this.isLoadingLotes = false;
      this._change.detectChanges();
    }
  }


  
updateSuggestions(): void {
  const currentItem = this.counterItems.find((item) => item.name === this.nameDB);
  if (currentItem) {
    currentItem.totalNoSuggestions = currentItem.unreviewedItems - this.listRevisor.length - currentItem.suggestionsRejected;
    currentItem.suggestions = this.listRevisor.length;
  }
  this._change.detectChanges();
}

updateObjectSuggestions(): void {
  const currentItem = this.counterItems.find((item) => item.name === this.nameDB);
  if (currentItem) {
   // console.log('Verif o contador: ', this.listObjectRevisor); 
    currentItem.totalNoSuggestions = currentItem.unreviewedItems - this.listObjectRevisor.length - currentItem.suggestionsRejected;
    currentItem.suggestions = this.listObjectRevisor.length;
  }
  this._change.detectChanges();
}

filterRevisorList() { //Filtra deixar somente os que forem diferentes, que houve correções
this.listRevisor = this.listRevisor.filter((revisor) => {
  let isDifferent = false;

  if (revisor?.name && revisor.newName && revisor.name !== revisor.newName) {
    isDifferent = true;
  }
  if (revisor?.title && revisor.newTitle && revisor.title !== revisor.newTitle) {
    isDifferent = true;
  }
  if (revisor?.description && revisor.newDescription && revisor.description !== revisor.newDescription) {
    isDifferent = true;
  }
  if (revisor?.message && revisor.newMessage && revisor.message !== revisor.newMessage) {
    isDifferent = true;
  }
  if (revisor?.word && revisor.newWord && revisor.word !== revisor.newWord) {
    isDifferent = true;
  }
  return isDifferent;
});
}

//Pega os itens do lote enviado para que possam ser contabilizados.
addCounterItems() {
  let filteredItems = [];
  let filterObejct: RevisorAI;

  //console.log('Lotes: ', this.lenghtLote);
  //console.log('Sequencia do lote: ', this.seqLote);
  const items = this.lenghtLote[this.seqLote];
  //console.log('Itens enviados: ', items);

    if(!this.isLotesObeject) {
      filteredItems = this.removeSuggestionList(items); 
    // console.log('Lista filtrada sem as sugestões: ', filteredItems)
  } else {
    this.listObjectRevisor
  }
 
  
  if (this.isSendLote) {
     //console.log('Envia para contar: ', filteredItems);
     if (this.nameDB === 'Areas') {
      this._areaService.addCounterAreas(filteredItems, this.prompt.idEnvironmentAi);
    } 
    else if (this.nameDB === 'Levels') {
      this._levelService.addCounterLevels(filteredItems, this.prompt.idEnvironmentAi);
    }
    else if (this.nameDB === 'Speech') {
      this._speechService.addCounterSpeech(filteredItems, this.prompt.idEnvironmentAi);
    }
    else if (this.nameDB === 'Characters') {
      this._characterService.addCounterCharacters(filteredItems, this.prompt.idEnvironmentAi);
    }
    else if (this.nameDB === 'Classes') {
      this._classService.addCounterClass(filteredItems, this.prompt.idEnvironmentAi);
    }
    else if (this.nameDB === 'Missions') {
      this._missionService.addCounterMissions(filteredItems, this.prompt.idEnvironmentAi);
    }
    else if (this.nameDB === 'Objectives') {
      this._objectiveService.addCounterObjectives(filteredItems, this.prompt.idEnvironmentAi);
    }
    else if (this.nameDB === 'Item Classes') {
      this._itemClassService.addCounterItemClasses(filteredItems, this.prompt.idEnvironmentAi);
    }
    else if (this.nameDB === 'Items List') {
      this._itemService.addCounterItemsList(filteredItems, this.prompt.idEnvironmentAi);
    }
    else if (this.nameDB === 'Keywords') {
      this._keywordService.addCounterKeywordList(filteredItems, this.prompt.idEnvironmentAi);
    }
    else if (this.nameDB === 'Skills') {
      this._statusService.addCounterSkillList(filteredItems, this.prompt.idEnvironmentAi);
    }
    else if (this.nameDB === 'Modifier') {
      this._modifierService.addCounterModifierList(filteredItems, this.prompt.idEnvironmentAi);
    }
    else if (this.nameDB === 'Tier List') {
      this._tierService.addCounterTierList(filteredItems, this.prompt.idEnvironmentAi);
    }
    else if (this.nameDB === 'Atributte') {
      this._atributteService.addCounterAtributte(filteredItems, this.prompt.idEnvironmentAi);
    }
    else if (this.nameDB === 'Knowledge') {
      this._knowledgeService.addCounterKnowledge(filteredItems, this.prompt.idEnvironmentAi);
    }    
    else if (this.nameDB === 'Atributte - Dice Frustration') {
     this._attributediceFrustrationService.addCounterDiceFrustration(items, this.listObjectRevisor, this.prompt.idEnvironmentAi);
    }
    else if (this.nameDB === 'Knowledge - Dice Frustration') {
    this._knowledgeDiceFrustrationService.addCounterKnowledgeDiceFrustration(items, this.listObjectRevisor, this.prompt.idEnvironmentAi);
    }
    else if (this.nameDB === 'Elemental Defenses') {
      this._elementalDefensesService.addCounterElementalDefenses(filteredItems, this.prompt.idEnvironmentAi);
    }
    else if (this.nameDB === 'Ailment') {
      this._ailmentService.addCounterAilments(filteredItems, this.prompt.idEnvironmentAi);
    }
    else if (this.nameDB === 'Maps') {
      this._mapsService.addCounterMaps(filteredItems, this.prompt.idEnvironmentAi);
    }
    else if (this.nameDB === 'Story Expansion Pack') {
      this._storyExpansionService.addCounterStoryExpansion(filteredItems, this.prompt.idEnvironmentAi);
    }
    else if (this.nameDB === 'Situational Modifier') {
      this._situationalModifierService.addCounterSituationalModifier(items, this.listObjectRevisor, this.prompt.idEnvironmentAi);
    }

  } else {
    console.log('Não enviar:: ', items);
  }
}

removeSuggestionList(items: any[]) {
  return items.filter((item) => {
    return !this.listRevisor.some((revisor) => {
      if (item?.name && revisor.name === item.name && revisor.id === item.id) {
        return true;
      } 
      else if (item.title && revisor.title === item.title && revisor.id === item.id) {
        return true;      
      }
      else if (item.description && revisor.description === item.description && revisor.id === item.id) {
        return true;
      }
      else if (item.message && revisor.message === item.message && revisor.id === item.id) {
        return true;      
      }
      else if (item.word && revisor.word === item.word && revisor.id === item.id) {
        return true;     
      }    
      return false;
    });
  });
}

  
  public redirectToSettings() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
    this.isCancelled = true;   
    this.cleanFields();
    this._router.navigate(['settings']);
  }

  approveText(id: string, revisedContext: string, booleanField: string, nameField: string, index: number) {
    this.isRemove = false;
    this.savetextRevisor(id, booleanField, this.isRemove,  revisedContext, nameField, index);  
    this.effectHighlightRemove(index, nameField);

    setTimeout(() => {
     // console.log('Lista para remoção: ', this.listRevisor);      
        if (index !== -1) {
          this.listRevisor = [...this.listRevisor.slice(0, index), ...this.listRevisor.slice(index + 1)];
        }  
  
      else {
        this.listRevisor = this.listRevisor.filter((item) => {
          if ( item.id === id) {
            return item[nameField][index] = "";
          } else {
            return item;
          }
         ;
        });
      }
      this.updateCounterItems();      
      this._change.detectChanges(); 
    }, 500);

  }

  savetextRevisor(id: string, booleanField: string, isRemove: boolean, revisedContext?: string, nameField?: string, index?: number) {

      if (this.nameDB === 'Areas') {
        const area = this._areaService.svcFindById(id);

        if (!isRemove) {
          area[nameField] = revisedContext;
        }  
        nameField === 'name' ? area.revisionCounterNameAI = 0 : area.revisionCounterDescriptionAI = 0;
        area[booleanField] = true;  
        this._areaService.svcToModify(area);        
      } 
      else if (this.nameDB === 'Levels') {
        const level = this._levelService.svcFindById(id);

        if (!isRemove) {
          level[nameField] = revisedContext;
        }
        nameField === 'name' ? level.revisionCounterNameAI = 0 : level.revisionCounterDescriptionAI = 0;   
        level[booleanField] = true;
        this._levelService.svcToModify(level);
      } 
      else if (this.nameDB === 'Speech') {
        const speech = this._speechService.svcFindById(id);

        if (!isRemove) {
          speech[nameField] = revisedContext;
        }
        speech.revisionCounterMessageAI = 0; 
        speech[booleanField] = true;
        this._speechService.svcToModify(speech);
      } 
      else if (this.nameDB === 'Characters') {
        const character = this._characterService.svcFindById(id);

        if (!isRemove) {
          character[nameField] = revisedContext;
        }
        if (nameField === 'name') {
          character.revisionCounterNameAI = 0
        } 
        else  if (nameField === 'title') {
          character.revisionCounterTitle = 0
        } 
        else  if (nameField === 'description') {
          character.revisionCounterDescriptionAI = 0
        }
        character[booleanField] = true;
        this._characterService.svcToModify(character);        
      }
      else if (this.nameDB === 'Classes') {
        const classe = this._classService.svcFindById(id);
        if (!isRemove) {
          classe[nameField] = revisedContext;
        }
        nameField === 'name' ? classe.revisionCounterNameAI = 0 : classe.revisionCounterDescriptionAI = 0; 
        classe[booleanField] = true;
        this._classService.svcToModify(classe);
      } 
      else if (this.nameDB === 'Missions') {
        const missionItem = this._missionService.svcFindById(id);
        if (!isRemove) {
          missionItem[nameField] = revisedContext;
        }
        missionItem[booleanField] = true;
        nameField === 'name' ? missionItem.revisionCounterNameAI = 0 : missionItem.revisionCounterDescriptionAI = 0; 
        this._missionService.svcToModify(missionItem);
      }
      else if (this.nameDB === 'Objectives') {
        const objectiveItem = this._objectiveService.svcFindById(id);
        if (!isRemove) {
          console.log("Chegou Descrição: ", objectiveItem);
          objectiveItem[nameField] = revisedContext;
        }
        objectiveItem.revisionCounterDescriptionAI = 0; 
        objectiveItem[booleanField] = true;
        this._objectiveService.svcToModify(objectiveItem);
      }
      else if (this.nameDB === 'Item Classes') {
        const itemClasses = this._itemClassService.svcFindById(id);
        if (!isRemove) {
          itemClasses[nameField] = revisedContext;
        }
        nameField === 'name' ? itemClasses.revisionCounterNameAI = 0 : itemClasses.revisionCounterDescriptionAI = 0; 
        itemClasses[booleanField] = true;
        this._itemClassService.svcToModify(itemClasses);
      }
       else if (this.nameDB === 'Items List') {
        const itemList = this._itemService.svcFindById(id);
        if (!isRemove) {
          itemList[nameField] = revisedContext;
        }
        nameField === 'name' ? itemList.revisionCounterNameAI = 0 : itemList.revisionCounterDescriptionAI = 0; 
        itemList[booleanField] = true;
        this._itemService.svcToModify(itemList);
      } 
      else if (this.nameDB === 'Keywords') {
        const wordList = this._keywordService.svcFindById(id);
        if (!isRemove) {
          wordList[nameField] = revisedContext;
        }
        wordList.revisionCounterWordAI = 0; 
        wordList[booleanField] = true;
        this._keywordService.svcToModify(wordList);
      } 
      else if (this.nameDB === 'Skills') {
        const skills = this._statusService.svcFindById(id);
        if (!isRemove) {
          nameField === 'name' ? skills.skill = revisedContext : skills.description = revisedContext;          
        }
        nameField === 'name' ? skills.revisionCounterSkillAI = 0 : skills.revisionCounterDescriptionAI = 0; 
        nameField === 'name' ? skills.isReviewedSkill = true : skills.isReviewedDescription = true;
        this._statusService.svcToModify(skills);
      }
      else if (this.nameDB === 'Modifier') {
        const modifier = this._modifierService.svcFindById(id);
        if (!isRemove) {
          nameField === 'name' ? modifier.skill = revisedContext : modifier.description = revisedContext;          
        }
        nameField === 'name' ? modifier.revisionCounterSkillAI = 0 : modifier.revisionCounterDescriptionAI = 0; 
        nameField === 'name' ? modifier.isReviewedSkill = true : modifier.isReviewedDescription = true;
        this._modifierService.svcToModify(modifier);
      } 
      else if (this.nameDB === 'Tier List') {
        const tierItem = this._tierService.svcFindById(id);
        if (!isRemove) {
          tierItem[nameField] = revisedContext;
        }
        tierItem.revisionCounterNameAI = 0; 
        tierItem[booleanField] = true;
        this._tierService.svcToModify(tierItem);
      }
      else if (this.nameDB === 'Atributte') {
        const atributte = this._atributteService.svcFindById(id);
        if (!isRemove) {
          nameField === 'name' ? atributte.atributte = revisedContext : atributte.description = revisedContext;          
        }
        nameField === 'name' ? atributte.revisionCounterAtributteAI = 0 : atributte.revisionCounterDescriptionAI = 0; 
        nameField === 'name' ? atributte.isReviewedAtributte = true : atributte.isReviewedDescription = true;
        this._atributteService.svcToModify(atributte);
      }
      else if (this.nameDB === 'knowledge') {
        const knowledgeList = this._knowledgeService.svcFindById(id);
        if (!isRemove) {
          nameField === 'name' ? knowledgeList.knowledge = revisedContext : knowledgeList.description = revisedContext;          
        }
        nameField === 'name' ? knowledgeList.revisionCounterknowledgeAI = 0 : knowledgeList.revisionCounterDescriptionAI = 0; 
        nameField === 'name' ?  knowledgeList.isReviewedknowledge = true : knowledgeList.isReviewedDescription = true;
        this._knowledgeService.svcToModify(knowledgeList);
      }   
     else if (this.nameDB === 'Elemental Defenses') {
        const elemental = this._elementalDefensesService.svcFindById(id);
        if (!isRemove) {
          nameField === 'name' ? elemental.defenses = revisedContext : elemental.description = revisedContext;          
        }
        nameField === 'name' ? elemental.revisionCounterDefensesAI = 0 : elemental.revisionCounterDescriptionAI = 0; 
        nameField === 'name' ? elemental.isReviewedDefenses = true : elemental.isReviewedDescription = true;
        this._elementalDefensesService.svcToModify(elemental);
      }    
     else if (this.nameDB === 'Ailment') {
        const ailmentList = this._ailmentService.svcFindById(id);
        if (!isRemove) {
          nameField === 'name' ? ailmentList.ailment = revisedContext : ailmentList.description = revisedContext;          
        }
        nameField === 'name' ? ailmentList.revisionCounterAilmentAI = 0 : ailmentList.revisionCounterDescriptionAI = 0; 
        nameField === 'name' ? ailmentList.isReviewedAilment = true : ailmentList.isReviewedDescription = true;
        this._ailmentService.svcToModify(ailmentList);
      }
    else if (this.nameDB === 'Maps') {
        const maps = this._mapsService.svcFindById(id);
        if (!isRemove) {
          nameField === 'name' ? maps.name = revisedContext : maps.description = revisedContext;          
        }
        nameField === 'name' ? maps.revisionCounterNameAI = 0 : maps.revisionCounterDescriptionAI = 0; 
        maps[booleanField] = true;
        this._mapsService.svcToModify(maps);
      }  
     else if (this.nameDB === 'Story Expansion Pack') {
        const story = this._storyExpansionService.svcFindById(id);
        if (!isRemove) {
          nameField === 'name' ? story.name = revisedContext : story.description = revisedContext;          
        }
        nameField === 'name' ? story.revisionCounterNameAI = 0 : story.revisionCounterDescriptionAI = 0; 
        story[booleanField] = true;
        this._storyExpansionService.svcToModify(story);
      }
      
  }

  approveObejectText(context: RevisorObjectAI, nameField: string, index: number) {
    this.isRemove = false;
    this.saveObjectRevisor(context, this.isRemove, nameField);  
    this.effectHighlightRemove(index, nameField);

    setTimeout(() => {

     if (index !== -1) {
          this.listObjectRevisor = [...this.listObjectRevisor.slice(0, index), ...this.listObjectRevisor.slice(index + 1)];
        }  
  
      else {
        this.listObjectRevisor = this.listObjectRevisor.filter((item) => {
          if ( item.id === context.id) {
            return item[nameField][index] = "";
          } else {
            return item;
          }
         ;
        });
      }

      this.updateCounterItems();      
      this._change.detectChanges(); 
    }, 500);
   // console.log('Lista dos DICES: ', this._knowledgeDiceFrustrationService.models);
  }

  saveObjectRevisor(context: RevisorObjectAI, isRemove: boolean, nameField?: string) {
    let position: number;
    
    if (this.nameDB === 'Atributte - Dice Frustration') {        
      //  console.log('Salva Atributte - Dice Frustration: ', context);
      const  atributteDice = this._attributediceFrustrationService.svcFindById(context.id);

      if (nameField === 'light') {  
        atributteDice.light.forEach((item, index) => { 
          if (item === context.light) {
            position = index;
          }     
        });

        if (!isRemove) {
          atributteDice.light[position] = context.newLight;
        }    
        atributteDice.revisionCounterLightAI[position] = 0;
        atributteDice.isReviewedLight[position] = true;
      } 
      else if (nameField === 'moderate') {

        atributteDice.moderate.forEach((item, index) => {
          if (item === context.moderate) {
            position = index;
          }     
        });

        if (!isRemove) {
          atributteDice.moderate[position] = context.newModerate;
        }    
        atributteDice.revisionCounterModerateAI[position] = 0;
        atributteDice.isReviewedModerate[position] = true;      
      }      
      else {
        //critical
        atributteDice.critical.forEach((item, index) => {
          if (item === context.critical) {
            position = index;
          }     
        });
          
        if (!isRemove) {
          atributteDice.critical[position] = context.newCritical;
        }    
        atributteDice.revisionCounterCriticalAI[position] = 0;
        atributteDice.isReviewedCritical[position] = true;
      }

      this._attributediceFrustrationService.svcToModify(atributteDice);   

      } 
      else if (this.nameDB === 'Knowledge - Dice Frustration' ) {
       // console.log('knowledge - Dice Frustration: ', context);
        const  knowledgeDice = this._knowledgeDiceFrustrationService.svcFindById(context.id);
  
        if (nameField === 'light') {
          knowledgeDice.light.forEach((item, index) => {
            if (item === context.light) {
              position = index;
            }     
          });

          if (!isRemove) {
            knowledgeDice.light[position] = context.newLight;
          }    
          knowledgeDice.revisionCounterLightAI[position] = 0;
          knowledgeDice.isReviewedLight[position] = true;
        } 
        else if (nameField === 'moderate') {
          knowledgeDice.moderate.forEach((item, index) => {
            if (item === context.light) {
              position = index;
            }     
          });

          if (!isRemove) {
            knowledgeDice.moderate[position] = context.newModerate;
          }    
          knowledgeDice.revisionCounterModerateAI[position] = 0;
          knowledgeDice.isReviewedModerate[position] = true;      
        }      
        else {
          //critical
          knowledgeDice.critical.forEach((item, index) => {
            if (item === context.light) {
              position = index;
            }     
          });

          if (!isRemove) {
            knowledgeDice.critical[position] = context.newCritical;
          }    
          knowledgeDice.revisionCounterCriticalAI[position] = 0;
          knowledgeDice.isReviewedCritical[position] = true;
        }
        this._knowledgeDiceFrustrationService.svcToModify(knowledgeDice); 
      }

      else if (this.nameDB === 'Situational Modifier') {

        const situationalModifier = this._situationalModifierService.svcFindById(context.id);

        if (nameField === 'factor') {
          situationalModifier.factor.forEach((item, index) => {
            if (item === context.factor) {
              position = index;
            }     
          });

          if (!isRemove) {
            situationalModifier.factor[position] = context.newFactor; 
            situationalModifier.revisionCounterFactorAI[position] = 0;
            situationalModifier.isReviewedFactor[position] = true;
          } 
        }
        else {
          situationalModifier.description.forEach((item, index) => {
            if (item === context.description) {
              position = index;
            }     
          });

          if (!isRemove) {
            situationalModifier.description[position] = context.newDescription;
            situationalModifier.revisionCounterDescriptionAI[position] = 0;
            situationalModifier.isReviewedDescription[position] = true;
          } 
        } 
        this._situationalModifierService.svcToModify(situationalModifier); 
      }
}

  updateCounterItems(): void {
    const currentItem = this.counterItems.find((item) => item.name === this.nameDB);
    if (currentItem) {
      currentItem.revisedItems++;
      currentItem.unreviewedItems--; 
      currentItem.suggestions--;    
    }
    this._change.detectChanges();
  }

  rejectText(id: string, booleanField: string, nameField: string, index: number) {
    this.isRemove = true;
    this.effectHighlightRemove(index, nameField);
    this.savetextRevisor(id, booleanField, this.isRemove, '', nameField, index);  
  setTimeout(() => {

    if (index !== -1) {
      this.listRevisor = [...this.listRevisor.slice(0, index), ...this.listRevisor.slice(index + 1)];
    }  
    this.updateSuggestionsRejected();
    this._change.detectChanges(); 
  }, 500);
    
  }

  rejectTextObject(context: RevisorObjectAI, nameField: string, index: number) {
    this.isRemove = true;
    this.effectHighlightRemove(index, nameField); 
    this.saveObjectRevisor(context, this.isRemove, nameField); 

  setTimeout(() => {

    if (index !== -1) {
      this.listObjectRevisor = [...this.listObjectRevisor.slice(0, index), ...this.listObjectRevisor.slice(index + 1)];
    }  
  else {
    this.listObjectRevisor = this.listObjectRevisor.filter((item) => {
      if ( item.id === context.id) {
        return item[nameField][index] = "";
      } else {
        return item;
      }
     ;
    });
  }
    this.updateSuggestionsRejected();
    this._change.detectChanges(); 

  }, 500);    
  }

  updateSuggestionsRejected(): void {
    const currentItem = this.counterItems.find((item) => item.name === this.nameDB);
    if (currentItem) {
      currentItem.suggestionsRejected++;
      currentItem.suggestions--;       
    }
    this._change.detectChanges();
  }
  
  // função para remover o item e aplicar o efeito de highlight
  effectHighlightRemove(index: number, type: string) {
    const highlightElement = document.getElementById(`highlight-${type}-${index}`);
    if (this.isRemove) {
      highlightElement.style.backgroundColor = '#ff0000';
    } 
    else {
      highlightElement.style.backgroundColor = 'green';
    }
    
    setTimeout(() => {
      highlightElement.style.backgroundColor = '';
    }, 500);
  }

effectHighlight() {
  const highlightElement = document.getElementById('highlight-element');
  highlightElement.style.backgroundColor = 'rgb(186, 218, 85)';
  setTimeout(() => {
    highlightElement.style.backgroundColor = '';
  }, 500);
}


approveAll() {   
  let position: number; 

if (this.nameDB === 'Areas') {
    this.listRevisor.forEach((area) => {
      const areaItem = this._areaService.svcFindById(area.id);
      if (area?.newName) {
        areaItem.name = area.newName;
        areaItem.isReviewedName = true;
        areaItem.revisionCounterNameAI = 0;
      } else { 
        areaItem.description = area.newDescription;
        areaItem.isReviewedDescription = true;
        areaItem.revisionCounterDescriptionAI = 0; 
      }   
      this._areaService.svcToModify(areaItem);

    });      
  } 
  else if (this.nameDB === 'Levels') {
    this.listRevisor.forEach((level) => {
      const levelItem = this._levelService.svcFindById(level.id);
     if (level?.newName) {
        levelItem.name = level.newName;
        levelItem.isReviewedName = true;
        levelItem.revisionCounterNameAI = 0;
      } else { 
        levelItem.description = level.newDescription;
        levelItem.isReviewedDescription = true;
        levelItem.revisionCounterDescriptionAI = 0; 
      } 
      this._levelService.svcToModify(levelItem);
    }); 
  }
  else if (this.nameDB === 'Speech') {
    this.listRevisor.forEach((speech) => {
      const speechItem = this._speechService.svcFindById(speech.id);
      speechItem.message = speech.newMessage;
      speechItem.isReviewedMessage = true;  
      speechItem.revisionCounterMessageAI = 0;     
      this._speechService.svcToModify(speechItem);
    }); 
  }  
  else if (this.nameDB === 'Characters') {
    this.listRevisor.forEach((character) => {
    const characterItem = this._characterService.svcFindById(character.id);
      if (character?.newName) {
        characterItem.name = character.newName;
        characterItem.isReviewedName = true;
        characterItem.revisionCounterNameAI = 0;
      }
     else if (character?.title) {
        characterItem.title = character.title;
        characterItem.isReviewedTitle= true;
        characterItem.revisionCounterTitle = 0;
      } else {
        characterItem.description = character.newDescription;
        characterItem.isReviewedDescription = true;
        characterItem.revisionCounterDescriptionAI = 0; 
      }
      this._characterService.svcToModify(characterItem);
    });
  }
  else if (this.nameDB === 'Classes') {
    this.listRevisor.forEach((classe) => {
      const calsseItem = this._classService.svcFindById(classe.id);
     if (classe?.newName) {
        calsseItem.name = classe.newName;
        calsseItem.isReviewedName = true;
        calsseItem.revisionCounterNameAI = 0;
      } else { 
        calsseItem.description = classe.newDescription;
        calsseItem.isReviewedDescription = true;
        calsseItem.revisionCounterDescriptionAI = 0; 
      } 
      this._classService.svcToModify(calsseItem);
    }); 
  }
  else if (this.nameDB === 'Missions') {
    this.listRevisor.forEach((misson) => {
      const missionItem = this._missionService.svcFindById(misson.id);
     if (misson?.newName) {
      missionItem.name = misson.newName;
      missionItem.isReviewedName = true;
      missionItem.revisionCounterNameAI = 0;
      } else { 
        missionItem.description = misson.newDescription;
        missionItem.isReviewedDescription = true;
        missionItem.revisionCounterDescriptionAI = 0; 
      } 
      this._missionService.svcToModify(missionItem);
    }); 
  }
  else if (this.nameDB === 'Objectives') {
    this.listRevisor.forEach((objective) => {
      const objectiveItem = this._objectiveService.svcFindById(objective.id);
      objectiveItem.description = objective.newDescription;
      objectiveItem.isReviewedDescription = true;  
      objectiveItem.revisionCounterDescriptionAI = 0;     
      this._objectiveService.svcToModify(objectiveItem);
    }); 
  }
  else if (this.nameDB === 'Item Classes') {
    this.listRevisor.forEach((iClass) => {
      const itemClasses = this._itemClassService.svcFindById(iClass.id);
      if (iClass?.newName) {
        itemClasses.name = iClass.newName;
        itemClasses.isReviewedName = true;
        itemClasses.revisionCounterNameAI = 0;
        } else { 
          itemClasses.description = iClass.newDescription;
          itemClasses.isReviewedDescription = true; 
          itemClasses.revisionCounterDescriptionAI = 0; 
        }      
      this._itemClassService.svcToModify(itemClasses);
    }); 
  }
  else if (this.nameDB === 'Items List') {
    this.listRevisor.forEach((iClass) => {
      const itemList = this._itemService.svcFindById(iClass.id);
      if (iClass?.newName) {
        itemList.name = iClass.newName;
        itemList.isReviewedName = true;
        itemList.revisionCounterNameAI = 0;
        } else { 
          itemList.description = iClass.newDescription;
          itemList.isReviewedDescription = true; 
          itemList.revisionCounterDescriptionAI = 0; 
        }      
      this._itemService.svcToModify(itemList);
    }); 
  }
  else if (this.nameDB === 'Keywords') {
    this.listRevisor.forEach((word) => {
      const wordList = this._keywordService.svcFindById(word.id);
   
      wordList.word = word.newWord;
      wordList.isReviewedWord = true;
      wordList.revisionCounterWordAI = 0;        
      this._keywordService.svcToModify(wordList);
    }); 
  }
  else if (this.nameDB === 'Skills') {
    this.listRevisor.forEach((skillList) => {
      const itemList = this._statusService.svcFindById(skillList.id);
      if (skillList?.newName) {
        itemList.skill = skillList.newName;
        itemList.isReviewedSkill = true;
        itemList.revisionCounterSkillAI = 0;
        } else { 
          itemList.description = skillList.newDescription;
          itemList.isReviewedDescription = true; 
          itemList.revisionCounterDescriptionAI = 0; 
        }      
      this._statusService.svcToModify(itemList);
    }); 
  }
  else if (this.nameDB === 'Modifier') {
    this.listRevisor.forEach((skillList) => {
      const itemList = this._modifierService.svcFindById(skillList.id);
      if (skillList?.newName) {
        itemList.skill = skillList.newName;
        itemList.isReviewedSkill = true;
        itemList.revisionCounterSkillAI = 0;
        } else { 
          itemList.description = skillList.newDescription;
          itemList.isReviewedDescription = true; 
          itemList.revisionCounterDescriptionAI = 0; 
        }      
      this._modifierService.svcToModify(itemList);
    }); 
  }
  else if (this.nameDB === 'Tier List') {
    this.listRevisor.forEach((tier) => {
      const tierItem = this._tierService.svcFindById(tier.id);
      tierItem.name = tier.newName;
      tierItem.isReviewedName = true;  
      tierItem.revisionCounterNameAI = 0;     
      this._tierService.svcToModify(tierItem);
    }); 
  }
  else if (this.nameDB === 'Atributte') {
    this.listRevisor.forEach((atribute) => {
      const itemList = this._atributteService.svcFindById(atribute.id);
      if (atribute?.newName) {
        itemList.atributte = atribute.newName;
        itemList.isReviewedAtributte = true;
        itemList.revisionCounterAtributteAI = 0;
        } else { 
          itemList.description = atribute.newDescription;
          itemList.isReviewedDescription = true; 
          itemList.revisionCounterDescriptionAI = 0; 
        }      
      this._atributteService.svcToModify(itemList);
    }); 
  }
  else if (this.nameDB === 'knowledge') {
    this.listRevisor.forEach((atribute) => {
      const itemList = this._knowledgeService.svcFindById(atribute.id);
      if (atribute?.newName) {
        itemList.knowledge = atribute.newName;
        itemList.isReviewedknowledge = true;
        itemList.revisionCounterknowledgeAI = 0;
        } else { 
          itemList.description = atribute.newDescription;
          itemList.isReviewedDescription = true; 
          itemList.revisionCounterDescriptionAI = 0; 
        }      
      this._knowledgeService.svcToModify(itemList);
    }); 
  }
  else if (this.nameDB === 'Atributte - Dice Frustration') {
    this.listObjectRevisor.forEach((atribute) => {
      const atributteDice = this._attributediceFrustrationService.svcFindById(atribute.id);

      if (atribute?.light) {

        atributteDice.light.forEach((item, index) => {
          if (item === atribute.light) {
            position = index;
          }     
        });

        atributteDice.light[position] = atribute.newLight;
        atributteDice.isReviewedLight[position] = true;
        atributteDice.revisionCounterLightAI[position] = 0;
        } 
        else if (atribute?.moderate) {

          atributteDice.moderate.forEach((item, index) => {
            if (item === atribute.moderate) {
              position = index;
            }     
          });

          atributteDice.moderate[atribute.position] = atribute.newModerate;
          atributteDice.isReviewedModerate[atribute.position] = true;
          atributteDice.revisionCounterModerateAI[atribute.position] = 0;
        }
        else {
          atributteDice.critical.forEach((item, index) => {
            if (item === atribute.critical) {
              position = index;
            }     
          });
          atributteDice.critical[position] = atribute.newCritical;
          atributteDice.isReviewedCritical[position] = true;
          atributteDice.revisionCounterCriticalAI[position] = 0;
        }
      this._attributediceFrustrationService.svcToModify(atributteDice);
    })
    
  }
  else if (this.nameDB === 'Knowledge - Dice Frustration') {
    this.listObjectRevisor.forEach((knowledge) => {
      const knowledgeDice = this._knowledgeDiceFrustrationService.svcFindById(knowledge.id);

      if (knowledge?.light) {
        
        knowledgeDice.light.forEach((item, index) => {
          if (item === knowledge.light) {
            position = index;
          }     
        });
        knowledgeDice.light[knowledge.position] = knowledge.light;
        knowledgeDice.isReviewedLight[knowledge.position] = true;
        knowledgeDice.revisionCounterLightAI[knowledge.position] = 0;
        } 
        else if (knowledge?.moderate) {
          knowledgeDice.moderate.forEach((item, index) => {
            if (item === knowledge.moderate) {
              position = index;
            }     
          });

          knowledgeDice.moderate[knowledge.position] = knowledge.moderate;
          knowledgeDice.isReviewedModerate[knowledge.position] = true;
          knowledgeDice.revisionCounterModerateAI[knowledge.position] = 0;
        }
        else {
          knowledgeDice.critical.forEach((item, index) => {
            if (item === knowledge.critical) {
              position = index;
            }     
          });
          knowledgeDice.critical[knowledge.position] = knowledge.critical;
          knowledgeDice.isReviewedCritical[knowledge.position] = true;
          knowledgeDice.revisionCounterCriticalAI[knowledge.position] = 0;
        }
      this._knowledgeDiceFrustrationService.svcToModify(knowledgeDice); 
    });
    
  }
  else if (this.nameDB === 'ElementalDefenses') {
      this.listRevisor.forEach((elemental) => {
        const itemList = this._elementalDefensesService.svcFindById(elemental.id);
        if (elemental?.newName) {
          itemList.defenses = elemental.newName;
          itemList.isReviewedDefenses = true;
          itemList.revisionCounterDefensesAI = 0;
          } else { 
            itemList.description = elemental.newDescription;
            itemList.isReviewedDescription = true; 
            itemList.revisionCounterDescriptionAI = 0; 
          }      
        this._elementalDefensesService.svcToModify(itemList);
    }); 
  }
  else if (this.nameDB === 'Ailment') {
    this.listRevisor.forEach((ailment) => {
      const itemList = this._ailmentService.svcFindById(ailment.id);
      if (ailment?.newName) {
        itemList.ailment = ailment.newName;
        itemList.isReviewedAilment = true;
        itemList.revisionCounterAilmentAI = 0;
        } else { 
          itemList.description = ailment.newDescription;
          itemList.isReviewedDescription = true; 
          itemList.revisionCounterDescriptionAI = 0; 
        }      
      this._ailmentService.svcToModify(itemList);
  }); 
}
else if (this.nameDB === 'Maps') {
  this.listRevisor.forEach((mapsList) => {
    const maps = this._mapsService.svcFindById(mapsList.id);
    if (mapsList?.newName) {
      maps.name = mapsList.newName;
      maps.isReviewedName = true;
      maps.revisionCounterNameAI = 0;
      } else { 
        maps.description = mapsList.newDescription;
        maps.isReviewedDescription = true; 
        maps.revisionCounterDescriptionAI = 0; 
      }      
    this._mapsService.svcToModify(maps);
}); 
}
else if (this.nameDB === 'Story Expansion Pack') {
  this.listRevisor.forEach((storyExpansion) => {
    const story = this._storyExpansionService.svcFindById(storyExpansion.id);
    if (storyExpansion?.newName) {
      story.name = storyExpansion.newName;
      story.isReviewedName = true;
      story.revisionCounterNameAI = 0;
      } else { 
        story.description = storyExpansion.newDescription;
        story.isReviewedDescription = true; 
        story.revisionCounterDescriptionAI = 0; 
      }      
    this._storyExpansionService.svcToModify(story);
}); 
}
  else if (this.nameDB === 'Situational Modifier') {
    this.listObjectRevisor.forEach((sitMod) => {
      const situationalModifier = this._situationalModifierService.svcFindById(sitMod.id);

      if (sitMod?.factor) {

        situationalModifier.factor.forEach((item, index) => {
          if (item === sitMod.factor) {
            position = index;
          }     
        });

        situationalModifier.factor[position] = sitMod.newFactor;
        situationalModifier.isReviewedFactor[position] = true;
        situationalModifier.revisionCounterFactorAI[position] = 0;
        } 
        else if (sitMod?.description) {

          situationalModifier.description.forEach((item, index) => {
            if (item === sitMod.description) {
              position = index;
            }     
          });

          situationalModifier.description[sitMod.position] = sitMod.newModerate;
          situationalModifier.isReviewedDescription[sitMod.position] = true;
          situationalModifier.revisionCounterDescriptionAI[sitMod.position] = 0;
        }
    
       this._situationalModifierService.svcToModify(situationalModifier);
    })
    
  }


  this.getDBCounterItems();
  showAlert('Success', 'All revisions approved!', 'success'); 
  this.cleanFields();
  this._change.detectChanges(); 
}

rejectAll() {
  let position: number;
  
    if (this.nameDB === 'Areas') {
        this.listRevisor.forEach((area) => {
            const areaItem = this._areaService.svcFindById(area.id);
            if (area?.newName) {
              areaItem.isReviewedName = true;
            } else {
              areaItem.isReviewedDescription = true;
            }          
         this._areaService.svcToModify(areaItem);
        });       
      } 
      else if (this.nameDB === 'Levels') {
        this.listRevisor.forEach((level) => {
            const levelItem = this._levelService.svcFindById(level.id);
            if (level?.newName) {
              levelItem.isReviewedName = true;
            } else {
              levelItem.isReviewedDescription = true;
            }            
           this._levelService.svcToModify(levelItem);
        });
      }
      else if (this.nameDB === 'Speech') {
        this.listRevisor.forEach((speech) => {
          const speechItem = this._speechService.svcFindById(speech.id);  
          speechItem.isReviewedMessage = true;
          this._speechService.svcToModify(speechItem);
      });
     }
      else if (this.nameDB === 'Characters') {
        this.listRevisor.forEach((character) => {
            const characterItem = this._characterService.svcFindById(character.id);
            if (character?.newName) {
              characterItem.isReviewedName = true;            
            }
            else if (character?.title) {
              characterItem.isReviewedTitle = true;
            }
             else {
              characterItem.isReviewedDescription = true;
            }            
          this._characterService.svcToModify(characterItem);
        });        
      }
      else if (this.nameDB === 'Classes') {
        this.listRevisor.forEach((classe) => {
            const classItem = this._classService.svcFindById(classe.id);
            if (classe?.newName) {
              classItem.isReviewedName = true;
            } else {
              classItem.isReviewedDescription = true;
            } 
          this._classService.svcToModify(classItem);
        });
      }
      else if (this.nameDB === 'Missions') {
        this.listRevisor.forEach((mission) => {
            const missionItem = this._missionService.svcFindById(mission.id);
            if (mission?.newName) {
              missionItem.isReviewedName = true;
            } else {
              missionItem.isReviewedDescription = true;
            }
          this._missionService.svcToModify(missionItem);
        });
      }
      else if (this.nameDB === 'Objectives') {
        this.listRevisor.forEach((objective) => {
          const objectiveItem = this._objectiveService.svcFindById(objective.id);  
          objectiveItem.isReviewedDescription = true;
          this._objectiveService.svcToModify(objectiveItem);
      });
     }
     else if (this.nameDB === 'Item Classes') {
        this.listRevisor.forEach((itemClasse) => {
            const itemClasses = this._itemClassService.svcFindById(itemClasse.id);
            if (itemClasse?.newName) {
              itemClasses.isReviewedName = true;              
            } 
            else {
              itemClasses.isReviewedDescription = true;
            }             
          this._itemClassService.svcToModify(itemClasses);
        });
      }    
      else if (this.nameDB === 'Items List') {
        this.listRevisor.forEach((list) => {
            const itemList = this._itemService.svcFindById(list.id);
            if (list?.newName) {
              itemList.isReviewedName = true;              
            } 
            else {
              itemList.isReviewedDescription = true;
            }             
          this._itemService.svcToModify(itemList);
        });
      }
      else if (this.nameDB === 'Keywords') {
        this.listRevisor.forEach((listWord) => {
            const itemKeyword = this._keywordService.svcFindById(listWord.id);           
            itemKeyword.isReviewedWord = true; 
           this._keywordService.svcToModify(itemKeyword);
        });
      }
      else if (this.nameDB === 'Skills') {
        this.listRevisor.forEach((list) => {
            const itemList = this._statusService.svcFindById(list.id);
            if (list?.newName) {
              itemList.isReviewedSkill = true;              
            } 
            else {
              itemList.isReviewedDescription = true;
            }             
          this._statusService.svcToModify(itemList);
        });
      }
      else if (this.nameDB === 'Modifier') {
        this.listRevisor.forEach((list) => {
            const itemList = this._modifierService.svcFindById(list.id);
            if (list?.newName) {
              itemList.isReviewedSkill = true;              
            } 
            else {
              itemList.isReviewedDescription = true;
            }             
          this._modifierService.svcToModify(itemList);
        });
      }
      else if (this.nameDB === 'Tier List') {
        this.listRevisor.forEach((objective) => {
          const tierItem = this._tierService.svcFindById(objective.id);  
          tierItem.isReviewedName = true;
          this._tierService.svcToModify(tierItem);
      });
     }
     else if (this.nameDB === 'Atributte') {
      this.listRevisor.forEach((list) => {
          const itemList = this._atributteService.svcFindById(list.id);
          if (list?.newName) {
            itemList.isReviewedAtributte = true;              
          } 
          else {
            itemList.isReviewedDescription = true;
          }             
        this._atributteService.svcToModify(itemList);
      });
    }
    else if (this.nameDB === 'Knowledge') {
      this.listRevisor.forEach((list) => {
          const itemList = this._knowledgeService.svcFindById(list.id);
          if (list?.newName) {
            itemList.isReviewedknowledge = true;              
          } 
          else {
            itemList.isReviewedDescription = true;
          }             
        this._knowledgeService.svcToModify(itemList);
      });
    }
    else if (this.nameDB === 'Atributte - Dice Frustration') {
      this.listObjectRevisor.forEach((list) => {
        const atributteDice = this._attributediceFrustrationService.svcFindById(list.id);

        if (list?.light) {
          atributteDice.light.forEach((item, index) => {
            if (item === list.light) {
              position = index;
            }     
          });
  
          atributteDice.isReviewedLight[position] = true; 
          atributteDice.revisionCounterLightAI[position] = 0;             
        } 
        if (list?.moderate) {
          atributteDice.moderate.forEach((item, index) => { 
            if (item === list.moderate) {
              position = index;
            }     
          });
          atributteDice.isReviewedModerate[position] = true;
          atributteDice.revisionCounterModerateAI[position] = 0;              
        } 
        else {
          atributteDice.critical.forEach((item, index) => { 
            if (item === list.critical) {
              position = index;
            }     
          });
          atributteDice.isReviewedCritical[position] = true; 
          atributteDice.revisionCounterCriticalAI[position] = 0;             
    
        }             
        this._attributediceFrustrationService.svcToModify(atributteDice);
      });

    }
    else if (this.nameDB === 'Knowledge - Dice Frustration') {
      this.listObjectRevisor.forEach((list) => {
        const knowledgeDice = this._knowledgeDiceFrustrationService.svcFindById(list.id);
        
        if (list?.light) {
          knowledgeDice.light.forEach((item, index) => {
            if (item === list.light) {
              position = index;
            }     
          });
  
          knowledgeDice.isReviewedLight[position] = true;
          knowledgeDice.revisionCounterLightAI[position] = 0;        
        } 
        if (list?.moderate) {
          knowledgeDice.moderate.forEach((item, index) => { 
            if (item === list.moderate) {
              position = index;
            }     
          });
          knowledgeDice.isReviewedModerate[position] = true;
          knowledgeDice.revisionCounterModerateAI[position] = 0;              
        } 
        else {
          knowledgeDice.critical.forEach((item, index) => { 
            if (item === list.critical) {
              position = index;
            }     
          });
          knowledgeDice.isReviewedCritical[position] = true; 
          knowledgeDice.revisionCounterCriticalAI[position] = 0;             
    
        }                       
        this._knowledgeDiceFrustrationService.svcToModify(knowledgeDice);
      });
      
    }
    else if (this.nameDB === 'Elemental Defenses') {
      this.listRevisor.forEach((list) => {
          const itemList = this._elementalDefensesService.svcFindById(list.id);
          if (list?.newName) {
            itemList.isReviewedDefenses = true;              
          } 
          else {
            itemList.isReviewedDescription = true;
          }             
        this._elementalDefensesService.svcToModify(itemList);
      });
    }
    else if (this.nameDB === 'Ailment') {
      this.listRevisor.forEach((list) => {
          const ailment = this._ailmentService.svcFindById(list.id);
          if (list?.newName) {
            ailment.isReviewedAilment = true;              
          } 
          else {
            ailment.isReviewedDescription = true;
          }             
        this._ailmentService.svcToModify(ailment);
      });
    }
    else if (this.nameDB === 'Maps') {
      this.listRevisor.forEach((list) => {
          const maps = this._mapsService.svcFindById(list.id);
          if (list?.newName) {
            maps.isReviewedName = true;              
          } 
          else {
            maps.isReviewedDescription = true;
          }             
        this._mapsService.svcToModify(maps);
      });
    }
    else if (this.nameDB === 'Story Expansion Pack') {
      this.listRevisor.forEach((list) => {
          const stooryExpansion = this._storyExpansionService.svcFindById(list.id);
          if (list?.newName) {
            stooryExpansion.isReviewedName = true;              
          } 
          else {
            stooryExpansion.isReviewedDescription = true;
          }             
        this._storyExpansionService.svcToModify(stooryExpansion);
      });
    }

    else if (this.nameDB === 'Situation Modifier') {
      this.listObjectRevisor.forEach((list) => {
        const situationalModifier = this._situationalModifierService.svcFindById(list.id);

        if (list?.light) {
          situationalModifier.factor.forEach((item, index) => {
            if (item === list.factor) {
              position = index;
            }     
          });
  
          situationalModifier.isReviewedFactor[position] = true; 
          situationalModifier.revisionCounterFactorAI[position] = 0;             
        } 
        if (list?.description) {
          situationalModifier.description.forEach((item, index) => { 
            if (item === list.description) {
              position = index;
            }     
          });
          situationalModifier.isReviewedDescription[position] = true;
          situationalModifier.revisionCounterDescriptionAI[position] = 0;              
        }                 
        this._situationalModifierService.svcToModify(situationalModifier);
      });

    }


    showAlert('Success', 'All revisions rejected!', 'success'); 
    this.getDBCounterItems();
    this.cleanFields();
    this._change.detectChanges(); 
}

// Cancela a solicitação a API
cancelRequest() {
  this.ngUnsubscribe.next();
  this.ngUnsubscribe.complete();
  
  this.isCancelled = true;
  this.isLoadingLotes = false;
  this.completedAnalysis = 'Revisão cancelada.';
 /* if (this.listRevisor.length === 0) {
    this.listContent = [];
  }*/
 if (this.isLotesObeject) {
  this.updateObjectSuggestions();
 }
  this.listContent = [];
  this.addCounterItems();
  this._change.detectChanges(); 
}

cleanFields() {
  this.listRevisor = [];
  this.listObjectRevisor = [];
  this.listRevisorSituationalModifier = [];
  this.isLoadingLotes = false;
  this.isSelectedItem = false;  
  this.sendingBatch = undefined;
  this.selectedItem = '';
  this._change.detectChanges(); 
}

  //modal Info
  onModalClick(): void {
    this.isModalInfo = !this.isModalInfo;
  }

  closeAreaStatsPopup() {
    this.isModalInfo = false;
  }

  closeViewSendPopup() {
    this.islookSend = false;
  }

  /**
   * Ativa o modo de edição para um campo específico
   * @param context - Contexto do revisor
   * @param fieldName - Nome do campo a ser editado
   */
  enableEdit(context: RevisorAI, fieldName: string): void {
    // Desativa edição em outros campos do mesmo contexto
    this.disableAllEdits(context);

    // Ativa edição no campo específico
    switch (fieldName) {
      case 'name':
        context.isEditingName = true;
        break;
      case 'title':
        context.isEditingTitle = true;
        break;
      case 'description':
        context.isEditingDescription = true;
        break;
      case 'message':
        context.isEditingMessage = true;
        break;
      case 'word':
        context.isEditingWord = true;
        break;
    }
    this._change.detectChanges();
  }

  /**
   * Ativa o modo de edição para campos de objeto
   * @param context - Contexto do objeto revisor
   * @param fieldName - Nome do campo a ser editado
   */
  enableObjectEdit(context: RevisorObjectAI, fieldName: string): void {
    // Desativa edição em outros campos do mesmo contexto
    this.disableAllObjectEdits(context);

    // Ativa edição no campo específico
    switch (fieldName) {
      case 'light':
        context.isEditingLight = true;
        break;
      case 'moderate':
        context.isEditingModerate = true;
        break;
      case 'critical':
        context.isEditingCritical = true;
        break;
        case 'factor':
        context.isEditingFactor = true;
        break;
      case 'description':
        context.isEditingDescription = true;
        break;
    }
    this._change.detectChanges();
  }

  /**
   * Desativa edição em todos os campos de um contexto
   * @param context - Contexto do revisor
   */
  disableAllEdits(context: RevisorAI): void {
    context.isEditingName = false;
    context.isEditingTitle = false;
    context.isEditingDescription = false;
    context.isEditingMessage = false;
    context.isEditingWord = false;
  }

  /**
   * Desativa edição em todos os campos de um objeto
   * @param context - Contexto do objeto revisor
   */
  disableAllObjectEdits(context: RevisorObjectAI): void {
    context.isEditingLight = false;
    context.isEditingModerate = false;
    context.isEditingCritical = false;
    context.isEditingFactor = false;
    context.isEditingDescription = false;
  }

  /**
   * Salva as alterações feitas no campo editado
   * @param context - Contexto do revisor
   * @param fieldName - Nome do campo editado
   * @param newValue - Novo valor do campo
   */
  saveEditedField(context: RevisorAI, fieldName: string, newValue: string): void {
    // Atualiza o valor no contexto
    switch (fieldName) {
      case 'name':
        context.newName = newValue;
        context.isEditingName = false;
        break;
      case 'title':
        context.newTitle = newValue;
        context.isEditingTitle = false;
        break;
      case 'description':
        context.newDescription = newValue;
        context.isEditingDescription = false;
        break;
      case 'message':
        context.newMessage = newValue;
        context.isEditingMessage = false;
        break;
      case 'word':
        context.newWord = newValue;
        context.isEditingWord = false;
        break;
    }

    this._change.detectChanges();
  }
 /**
   * Salva as alterações feitas no campo de objeto editado
   * @param context - Contexto do objeto revisor
   * @param fieldName - Nome do campo editado
   * @param newValue - Novo valor do campo
   */
  saveEditedObjectField(context: RevisorObjectAI, fieldName: string, newValue: string): void {
    // Atualiza o valor no contexto
    switch (fieldName) {
      case 'light':
        context.newLight = newValue;
        context.isEditingLight = false;
        break;
      case 'moderate':
        context.newModerate = newValue;
        context.isEditingModerate = false;
        break;
      case 'critical':
        context.newCritical = newValue;
        context.isEditingCritical = false;
        break;
      case 'factor':
        context.newFactor = newValue;
        context.isEditingFactor = false;
        break;
      case 'description':
        context.newDescription = newValue;
        context.isEditingDescription = false;
        break;
    }

    this._change.detectChanges();
  }

  /**
   * Cancela a edição e restaura o valor original
   * @param context - Contexto do revisor
   * @param fieldName - Nome do campo sendo editado
   */
  cancelEdit(context: RevisorAI, fieldName: string): void {
    this.disableAllEdits(context);
    this._change.detectChanges();
  }

  /**
   * Cancela a edição de objeto e restaura o valor original
   * @param context - Contexto do objeto revisor
   * @param fieldName - Nome do campo sendo editado
   */
  cancelObjectEdit(context: RevisorObjectAI, fieldName: string): void {
    this.disableAllObjectEdits(context);
    this._change.detectChanges();
  }

  lookSend() {
    this.islookSend = !this.islookSend;
  }

}
