<div class="background-div" (mousedown)="handleOutsideMouseClick($event)">
    <!--
  <link rel="preload" href="assets/img/icon_brain_new-preview.png" as="image" />
  -->  
  <link rel="preload" href="assets/img/brain.png" as="image">
  <link rel="preload" href="assets/img/icon_dilemma.png" as="image">

  <ng-container *ngIf="popupStats">
    <div class="modal-backdrop" *ngIf="popupStats"></div> 
    <div id="modal-close" @popup class="popup-report" [ngStyle]="{'width': openDilemmaBox ? '1066px' : 'fit-content'}"
      style="background-color: transparent;">
      <div class="total-modal">
        <div class="c-title">
          <h2 style="font-weight: 700; width: 100%; text-align: center;">Dilemma Points</h2>
          <div class="modal-close margin-inline-start: auto;">
            <button (click)="closeAreaStatsPopup()" class="btn btn-danger btn-fill btn-remove modal-btn-close">
              <i class="pe-7s-close i-icon"></i>
            </button>
          </div>
        </div>

        <div style="display: flex; justify-content: center; margin-bottom: 10px; margin-top: 20px;">
          <div class="dilemma-container">

            <div class="threshold-container">
              <div>
                <label style="font-size: 20px;" for="threshold-select">THRESHOLD</label>
                <select id="threshold-select" (change)="updateThreshold($event)" [value]="existValueThreshold">
                  <option *ngFor="let value of thresholdOptions" [value]="value.valueThreshold"
                    [selected]=" value.valueThreshold === existValueThreshold">{{ value.valueThreshold }}</option>
                </select>
              </div>
              <button class="brain-button" style="background-image: url('assets/img/brain.png')" (click)="openSearchGPT()" loading="lazy">
              </button>
            </div>

            <div class="attributes-container">
              <div *ngFor="let attribute of atributtoClasses; let i = index" class="attribute-box">
                <label>{{ attribute.atributte }}</label>
                <select class="attSelect" (change)="updatePoints(attribute.id, attribute.atributte, $event)"
                  [value]="existValuePoints[i].valuePoints">
                  <option *ngFor="let valuePoint of pointsOptions" [value]="valuePoint"
                    [selected]="valuePoint === existValuePoints[i].valuePoints"
                    [ngStyle]="{'padding-right': valuePoint ? '15px': ''}">{{ valuePoint }}</option>
                </select>
              </div>
            </div>

            <textarea class="description-area" [(ngModel)]="justificationChatGPT"
              placeholder="Digite uma descrição"
              (ngModelChange)="onJustificationChange($event)">
                  <ng-container *ngIf="responsePrompt.length === 0">
                        <div class="loading-dots">
                                <div class="dot"></div>
                                <div class="dot"></div>
                                <div class="dot"></div>
                          </div>
                  </ng-container>
            </textarea>
            <ng-container *ngIf="isConfirm">
              <button class="save-button" (click)="saveDilemmaPoints()">Confirm</button>
            </ng-container>
          </div>

        </div>

      </div>
    </div>
  </ng-container>
  <!--FIM DO MODAL-->



  <div class="row" *ngIf="dilBox">

    <div class="col-md-12">
      <div class="card bc-color" id="{{ dilBox.id }}">
        <div *ngFor="let roadblock of this.roadBlocksUseds">
          <div id="{{roadblock.id}}">
            <app-roadblock [optionBox]="dilBox" [toRemove]="toRemoveProcessConditionFunc" [storyBoxId]="dilBox.id"
            [currentRoadblock]="roadblock" class="center">
          </app-roadblock>
          </div>
        </div>
        <div *ngIf="this.roadBlocksUseds.length > 1" class="component_roadblock">
          <div style="font-weight: 900; font-size: 20px" class="color-white"
            [ngStyle]="{'opacity': dilBox.AndOrCondition == 'AND' ? 1 : 0.3, 'color':dilBox.AndOrCondition == 'AND' ? '#ff00aa' : 'black'}">
            AND</div>
          <label class="switch">
            <input [checked]="dilBox.AndOrCondition == 'AND' ? false : true"
              (change)="this.chooseAndOrCondition($event)" type="checkbox">
            <span class="slider"></span>
          </label>
          <div style="font-weight: 900; font-size: 20px;" class="color-white"
            [ngStyle]="{'opacity': dilBox.AndOrCondition == 'OR' ? 1 : 0.3, 'color': dilBox.AndOrCondition == 'OR' ? 'blue' : 'black'}">
            OR</div>
        </div>
        <!--Box header-->
        <div class="header">
          <!--Delete button for box-->
          <button id="delete-button" class="btn btn-fill btn-danger pull-right" (click)="toRemove(dilBox)">
            <i class="pe-7s-close-circle"></i>
          </button>

          <!--Box sort order buttons-->
          <div [title]="dilBox.id" class="select pull-right">
            <button class="btn btn-success btn-simple btn-invert btn-xs" (click)="toMove(-1)">
              <i class="pe-7s-angle-up-circle"></i>
            </button>
            <span class="color-white">{{ index }}</span>
            <button class="btn btn-danger btn-simple btn-invert btn-xs" (click)="toMove(1)">
              <i class="pe-7s-angle-down-circle"></i>
            </button>
          </div>


          <div class="row-responsive">
            <!--Dialog box ID name-->
            <p class="categoryText">
              {{ dilBox | typeName }} > {{ dilBox.id }}
            </p>
            <p class="box-title-watermark color-grey-light">
              {{ dilBox | typeName }}
            </p>
          </div>
          <!--Box title-->
          <div class="header">
            <div class="component_label">
              <div class="component_label">
                <i class="icon title pe-7s-network color-white"></i>
                <!-- Circle over key -->

                <input class="form-control form-short  form-title color-grey-light" type="text"
                  value="{{ !dilBox.label ? '<<Label for progress condition>>' : dilBox.label }}" #label
                  (change)="changeLabel($event)" />
              </div>

              <h1 *ngIf="dilBox.label">
                <i [ngStyle]="{'color': isUsedRoadblock(dilBox) ? '#00ff04' : '#d3d3d3' }"
                  class="pe-7s-key pe-5x pe-va color-grey-light icon-css">
                  <div id="text-size" *ngIf="isUsedRoadblock(dilBox)" class="circle branch-circle margIcon"
                    tooltip="Used on: {{this.usedOnLevels | enumerateList}}" placement='top' delay='250'
                    ttPadding="10px" ttWidth="{{this.usedOnLevels | textSize}}">
                    <p style="text-align: center;">
                      {{this.usedRoadBlocks.length}}</p>
                  </div>
                </i>
              </h1>
            </div>
          </div>
        </div>
        <div class="table-responsive">
          <table class="table table-hover table-box">
            <thead>
              <tr style="text-align-last: center;">
                <th>Order</th>
                <th>Thought</th>
                <th>Dilemma Points</th>
                <th>Message</th>
                <th>Weight</th>
                <th>Answer</th>
                <th>Action</th>
              </tr>
            </thead>
            <!--choice box-->
            <tbody>
              <ng-container *ngFor="let option of this.optionBoxes; let i = index">
                <tr colspan="5"></tr>
                <tr id="{{ option.id }}">
                  <!--toggle emit-->
                  <td class="td-sort">
                    <div class="compact color-grey-light">
                      <!-- order buttons -->
                      <button class="btn btn-success btn-simple btn-invert btn-xs" (click)="toMoveOption(option, -1)">
                        <i class="pe-7s-angle-up-circle"></i>
                      </button>
                      {{ i }}
                      <button class="btn btn-danger btn-simple btn-invert btn-xs" (click)="toMoveOption(option, 1)">
                        <i class="pe-7s-angle-down-circle"></i>
                      </button>
                    </div>
                  </td>
                  <td style="text-align-last: center">
                    <!--toggle emit-->
                    <input type="checkbox" class="toggle center" [(ngModel)]="option.isOmitted"
                      (change)="onChangeOption(option)" />
                  </td>

                  <!--Dilemma Points-->
                  <td>
                    <button class="btn btn-danger btn-simple btn-invert btn-xs iconBrain"
                      style="background-image: url('assets/img/icon_dilemma.png')" loading="lazy" title="Dilemma Box"
                      (click)="openModalDilemmaPoints(option)">
                    </button>
                  </td>


                  <!--message-->
                  <td class="td-100">
                    <div style="display: flex; justify-content: space-between;">
                    <h6 style="font-weight: 100; margin-bottom: 5px;" class="color-grey-light">
                      {{option.id}}</h6>                      
                        <div class="quadrados">
                            <div *ngFor="let point of option.points; let i = index" class="quadrado"  title="{{point.nameAtributte}}: {{point.valuePoints}}"
                            [style.background-color]="checkBtnPoints(option, i) ? 'rgb(29, 199, 234)': 'rgb(128, 128, 128)'">

                            </div>
                        </div>
                      </div>
                    <app-rpg-content-input [rpgSuggestionLink]="option" #rpgInputBox [content]="option.message"
                      (contentChange)="onChangeOptionValue(option, 'message', $event)" [language]="language">
                    </app-rpg-content-input>
                  </td>
                  <!--weight button-->
                  <td>
                    <button class="btn btn-lg btn-fill btn-neutral" (click)="selectWeight(option)"
                      (change)="selectWeight(option)">
                      {{ option.weight }}
                    </button>
                  </td>
                  <!--Answer-->
                  <td>
                    <button class="btn btn-lg btn-fill color-grey-light"
                      (click)="HighlightElement(option.idDilemmaBox, 10, true)">
                      Answer
                    </button>
                  </td>
                  <td clas="td-actions">
                    <button style="font-size: 2px; margin: 0; padding: 3px 5px" class="btn btn-fill btn-danger"
                      (click)="RemoveOption(option)">
                      <i style="font-size: 24px; margin: 0; padding: 0" class="pe-7s-close"></i>
                    </button>
                  </td>
                </tr>
              </ng-container>

            </tbody>
          </table>
          <div class="row">
            <div class="pull-right">
              <button id="add-message-button" class="btn btn-sm btn-success btn-fill" style="margin-right: 25px;"
                (click)="toAddRoadblock()">
                <i class="pe-7s-shield center"></i>
                Add Progress Condition
              </button>
              <button id="add-marker-button" class="btn btn-sm btn-success btn-fill"
                (click)="toPromptAddAnswerDilemma()">
                <i class="pe-7s-help1 center"></i> Add Dilemma
              </button>
            </div>
            <div class="row"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <ng-container *ngIf="dilBox != undefined">
    <ng-container *ngFor="let option of dilBox?.optionDilemmaIds| dilemmas; let i = index">
      <div class="row">
        <div class="col-md-12">
          <div class="row">
            <ng-container>
              <div class="col-md-1">
                <button (click)="HighlightElement(option.id, 10, true)" class="btn btn-fill btn-simple card bc-color"
                  [ngStyle]="dilBox | boxStyle">
                  <div class="content table-responsive table-full-width">
                    <i class="icon pe-7s-network"></i>
                  </div>
                </button>
              </div>
            </ng-container>

            <div class="col-md-11">

              <app-answerDilemma-box (updateOption)="updateOptionFromAnswerBox($event)" [option]="option.id | dilm" [answerBox]="option.idDilemmaBox | answerDilemmaBox"
                [preloadedSpeakers]="preloadedSpeakers" [preloadedMissionsOfArea]="preloadedMissionsOfArea"
                [optionBox]="dilBox" [language]="language">
              </app-answerDilemma-box>
            </div>

          </div>
        </div>
      </div>
    </ng-container>
  </ng-container>

</div>