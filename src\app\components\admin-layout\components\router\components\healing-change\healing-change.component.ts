import { ChangeDetectorRef, Component, EventEmitter, Output } from '@angular/core';
import { HealingChangePerTurnPARTY } from 'src/app/lib/@bus-tier/models';
import { Ailment } from 'src/app/lib/@bus-tier/models/Ailment';
import { HealingChangePerTurnBOOS } from 'src/app/lib/@bus-tier/models/HealingChangePerTurnBOOS';
import { Button } from 'src/app/lib/@pres-tier/data';
import { HealingChangePerTurnBOOSService, HealingChangePerTurnPARTYService } from 'src/app/services';
import { AilmentService } from 'src/app/services/ailment.service';
import { Alert } from 'src/lib/darkcloud';

@Component({
  selector: 'app-healing-change',
  templateUrl: './healing-change.component.html',
  styleUrls: ['./healing-change.component.scss']
})
export class HealingChangeComponent {

      public readonly excelButtonTemplateParty: Button.Templateable = {
      title: 'Paste content from excel',
      onClick: this.onExcelPasteParty.bind(this),
      iconClass: 'excel-icon',
      btnClass: Button.Klasses.FILL_ORANGE,
    };

  public readonly excelButtonTemplateBoss: Button.Templateable = {
      title: 'Paste content from excel',
      onClick: this.onExcelPasteBoss.bind(this),
      iconClass: 'excel-icon',
      btnClass: Button.Klasses.FILL_ORANGE,
    };


  @Output() clickBtnAilment = new EventEmitter<boolean>();
   titleTurmParty = 'Healing Chance Per Turn -';
   titleTurnBoss = 'Healing Chance Per Turn -';

    description: string;
    activeLanguage = 'PTBR';
    activeTab: string;  
    ailmentList: Ailment[] = [];
    healingChangeListParty: HealingChangePerTurnPARTY[] = [];
    healingChangeListBoss: HealingChangePerTurnBOOS[] = [];
 


    constructor(
      private ref: ChangeDetectorRef,
     private _ailmentService: AilmentService,
     private _healingChangePartyService: HealingChangePerTurnPARTYService,
     private _healingChangeBossService: HealingChangePerTurnBOOSService,      
    ) { }



    async ngOnInit(): Promise<void> {
      await this._ailmentService.toFinishLoading();
      await this._healingChangePartyService.toFinishLoading();
      await this._healingChangeBossService.toFinishLoading();
      this.ailmentList = this._ailmentService.models;
      this.healingChangeListParty = this._healingChangePartyService.models;
      this.healingChangeListBoss = this._healingChangeBossService.models;
      
      this.sortAilmentListAlphabetically();
      this.description = `Showing ${this.healingChangeListParty.length} results`;   
    }

  async onExcelPasteParty() {
      try {
        const text = await navigator.clipboard.readText();
        const lines = text.split(/\r?\n/).filter((line) => line);

        // Limpar dados existentes
        this._healingChangePartyService.models = [];
        this._healingChangePartyService.toSave();

        // Processar cada linha do Excel
        for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
          const line = lines[lineIndex];
          const cols = line.split(/\t/).map(col => col.trim());

          // Validação 1: Verificar se tem exatamente 12 colunas
          if (cols.length !== 12) {
            Alert.showError(`Error at line ${lineIndex + 1}: Content must contain exactly 12 columns. ${cols.length} columns found.`);
            return;
          }

          const ailmentName = cols[0]; // Primeira coluna (Ailment)

          // Validação 2: Verificar se o ailment existe na lista
          const existingAilment = this.ailmentList.find(ailment => ailment.ailment === ailmentName);
          if (!existingAilment) {
            Alert.showError(`Error at line ${lineIndex + 1}: The ailment "${ailmentName}" was not found in the list of ailments.`);
            return;
          }

          // Criar novo HealingChange
          const changeList = await this._healingChangePartyService.createNewHealingChange(ailmentName);
          if (!changeList) {
            Alert.showError(`Error at line ${lineIndex + 1}: Could not create HealingChange for "${ailmentName}".`);
            return;
          }

          // Atribuir o ID do ailment
          changeList.ailmentId = existingAilment.id;

          // Atribuir as porcentagens das demais colunas (colunas 1-11)
          changeList.valuePosition = cols.slice(1); // Remove a primeira coluna e pega o resto

          // Salvar as modificações
          this._healingChangePartyService.svcToModify(changeList);
        }

        // Atualizar a lista e a tela
        this.healingChangeListParty = this._healingChangePartyService.models;

        // Ordenar a lista de ailments alfabeticamente após importação
        this.sortAilmentListAlphabetically();

        this.description = `Showing ${this.healingChangeListParty.length} results`;

        Alert.ShowSuccess('Excel copied successfully!');
        this.ref.detectChanges();

      } catch (error) {
        Alert.showError('Error importing data from Excel.');
        console.error(error);
      }
    }


  async onExcelPasteBoss() {
      try {
        const text = await navigator.clipboard.readText();
        const lines = text.split(/\r?\n/).filter((line) => line);

        // Limpar dados existentes
        this._healingChangeBossService.models = [];
        this._healingChangeBossService.toSave();

        // Processar cada linha do Excel
        for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
          const line = lines[lineIndex];
          const cols = line.split(/\t/).map(col => col.trim());

          // Validação 1: Verificar se tem exatamente 12 colunas
          if (cols.length !== 12) {
            Alert.showError(`Error at line ${lineIndex + 1}: Content must contain exactly 12 columns. ${cols.length} columns found.`);
            return;
          }

          const ailmentName = cols[0]; // Primeira coluna (Ailment)

          // Validação 2: Verificar se o ailment existe na lista
          const existingAilment = this.ailmentList.find(ailment => ailment.ailment === ailmentName);
          if (!existingAilment) {
            Alert.showError(`Error at line ${lineIndex + 1}: The ailment "${ailmentName}" was not found in the list of ailments.`);
            return;
          }

          // Criar novo HealingChange
          const changeList = await this._healingChangeBossService.createNewHealingChangeBoss(ailmentName);
          if (!changeList) {
            Alert.showError(`Error at line ${lineIndex + 1}: Could not create HealingChange for "${ailmentName}".`);
            return;
          }

          // Atribuir o ID do ailment
          changeList.ailmentId = existingAilment.id;

          // Atribuir as porcentagens das demais colunas (colunas 1-11)
          changeList.valuePosition = cols.slice(1); // Remove a primeira coluna e pega o resto

          // Salvar as modificações
          this._healingChangeBossService.svcToModify(changeList);
        }

        // Atualizar a lista e a tela
        this.healingChangeListBoss = this._healingChangeBossService.models;

        // Ordenar a lista de ailments alfabeticamente após importação
        this.sortAilmentListAlphabetically();
    
        this.description = `Showing ${this.healingChangeListBoss.length} results`;

        Alert.ShowSuccess('Excel copied successfully!');
        this.ref.detectChanges();

      } catch (error) {
        Alert.showError('Error importing data from Excel.');
        console.error(error);
      }
    }

      btnClickAilment() {
    this.clickBtnAilment.emit(true);
  }

  // Método para gerar os números dos turnos (1 a 11)
  getTurnNumbers(): number[] {
    return Array.from({ length: 11 }, (_, i) => i + 1);
  }

  // Método para obter as porcentagens de um ailment específico
  getPercentagesForAilment(id: string): string[] {
    // Encontra o HealingChange correspondente ao ailment
    const healingChange = this.healingChangeListParty.find(hc => hc.ailmentId === id);

    if (healingChange && healingChange.valuePosition) {
      // Retorna as porcentagens formatadas
      return healingChange.valuePosition.map(value => {
        // Se o valor já tem %, retorna como está, senão adiciona %
        return value.includes('%') ? value : `${value}%`;
      });
    }

    // Se não encontrar, retorna array vazio com 11 posições
    return Array(11).fill('0,0%');
  }

  // Método para obter o HealingChange de um ailment específico
  getHealingChangeForAilment(ailmentId: string): HealingChangePerTurnPARTY | null {
    return this.healingChangeListParty.find(hc => hc.ailmentId === ailmentId) || null;
  }

    getHealingChangeForTurnBoss(ailmentId: string): HealingChangePerTurnBOOS | null {
    return this.healingChangeListBoss.find(hc => hc.ailmentId === ailmentId) || null;
  }


  // Método para alterar valores de porcentagem
  changePercentageValue(ailmentId: string, columnIndex: number, newValue: string) {
    const healingChange = this.healingChangeListParty.find(hc => hc.ailmentId === ailmentId);

    if (healingChange) {
      // Inicializar valuePosition se não existir
      if (!healingChange.valuePosition) {
        healingChange.valuePosition = Array(11).fill('0,0%');
      }

      // Atualizar o valor na posição específica
      healingChange.valuePosition[columnIndex] = newValue;

      // Salvar as modificações
      this._healingChangePartyService.svcToModify(healingChange);
    }
  }

    changePercentageValueTurnBoss(ailmentId: string, columnIndex: number, newValue: string) {
    const healingChange = this.healingChangeListBoss.find(hc => hc.ailmentId === ailmentId);

    if (healingChange) {
      if (!healingChange.valuePosition) {
        healingChange.valuePosition = Array(11).fill('0,0%');
      }      
      healingChange.valuePosition[columnIndex] = newValue;
      this._healingChangeBossService.svcToModify(healingChange);
    }
  }

  // Método para ordenar a lista de ailments alfabeticamente
  sortAilmentListAlphabetically(): void {
    this.ailmentList.sort((a, b) => {
      // Comparação case-insensitive para ordenação alfabética
      const ailmentA = a.ailment?.toLowerCase() || '';
      const ailmentB = b.ailment?.toLowerCase() || '';

      return ailmentA.localeCompare(ailmentB, 'pt-BR', {
        numeric: true,
        sensitivity: 'base'
      });
    });
  }


}
