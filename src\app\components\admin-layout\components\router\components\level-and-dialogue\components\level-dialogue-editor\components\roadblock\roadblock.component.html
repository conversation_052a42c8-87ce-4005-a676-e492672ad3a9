<td colspan="6">
    <div id="{{roadblock?.id}}" style="margin-top: 10px; display: grid;">
        <p class="title-category">{{ roadblock?.id }}</p>
        <table>
            <tbody>
                <tr [ngClass]="{'marker': !!markerId}">
                    <td *ngIf="!markerId">
                        <div style="margin-left: 20px;">
                            <button class="btn btn-outline-light" (click)="toRemove(roadblock)">
                                <i class="pe-7s-close-circle"></i>
                            </button>
                        </div>
                    </td>

                    <td>
                        <label>Validate If True</label><br>
                        <input type="checkbox" (change)="changeRoadblock()" [checked]="roadblock?.validateIfTrue">
                    </td>
                    <td>
                        <label style="float: left;">Unlock Condition</label><br>
                        <select #selectList class="filter-dropdown auto" (change)="changeType($event)">
                            <option></option>
                            <option *ngFor="let type of types; let i = index" [value]="types[i]"
                                [selected]="type == roadblock?.Type">{{ typeNames[i] }}
                            </option>
                        </select>
                    </td>
                    <!--Item Requirement-->
                    <td colspan="5" class="table-width" *ngIf="roadblock?.Type == 1">
                        <div style="margin-top: 12px;">
                            <input type="text" class="inputSearchInter" style="width: 412px !important;"
                                placeholder="Search Box" 
                                (ngModelChange)="filterObtainedListItem($event)"
                                [(ngModel)]="filterObtainedTextFromList"
                                [readonly]="isDisabled" 
                                (focus)="enableSearch()" 
                                (blur)="disableSearchIfEmpty()" 
                                (mouseenter)="enableSearch()" 
                                (mouseleave)="disableSearchIfEmpty()">
                        </div>
                        <label class="mg-top">Item Requirement</label><br>
                        <select class="filter-dropdown" style="width: 412px;" (click)="changeItem($event)">
                            <option></option>
                            <option *ngFor="let item of displayItemList" [value]="item.id"
                                [selected]="item.id == roadblock?.ItemID" class="inputSearch unlockStyle">
                                {{ item.name }}
                            </option>
                        </select>

                        <ng-container *ngIf="roadblock?.validateIfTrue">
                            <div style="width: 412px; display: flex; gap: 12px;">
                                <div style="width: 200px !important;">
                                    <label class="mg-top">Condition Operator</label><br>
                                    <select name="operator" id="operator" class="inputSearch"
                                        style="width: 200px !important; margin-bottom: 12px;"
                                        (change)="changeOperator($event)">
                                        <option value="greater" [selected]="roadblock?.operator === 'greater'">≥
                                        </option>
                                        <option value="lesser" [selected]="roadblock?.operator === 'lesser'">≤</option>
                                        <option value="equal" [selected]="roadblock?.operator === 'equal'">=</option>
                                        <option value="different" [selected]="roadblock?.operator === 'different'">
                                            <span>&#8800;</span></option>
                                    </select>
                                </div>

                                <div style="width: 200px !important; align-self: self-end; margin-bottom: 12px;">
                                    <input type="number" class="inputSearch" style="width: 200px !important;"
                                        [value]="roadblock?.ItemAmount" placeholder="Amount required..." min="0"
                                        (change)="changeItemAmount($event)">
                                </div>
                            </div>

                        </ng-container>

                    </td>
                    <!--fim Item Requirement-->

                    <!--Character ID-->
                    <td colspan="5" class="table-width"
                        *ngIf="roadblock?.Type != 1 && roadblock?.Type != 4 && roadblock?.Type != 5 && roadblock?.Type != 6">
                        <!--TALKED TO CHARACTER-->
                        <ng-container *ngIf="roadblock?.Type >= 2">
                            <div style="margin-top: 12px;">
                                <input type="text" class="inputSearchInter unlockStyle" placeholder="Search Box"
                                    (ngModelChange)="filterCharacterListItem($event)"
                                    [(ngModel)]="filterObtainedTextFromList"
                                    [readonly]="isDisabled" 
                                    (focus)="enableSearch()" 
                                    (blur)="disableSearchIfEmpty()" 
                                    (mouseenter)="enableSearch()" 
                                    (mouseleave)="disableSearchIfEmpty()">
                            </div>
                            <label style="float: left;">Character ID</label><br>
                            <select (click)="changeCharacterId($event)" class="inputSearch unlockStyle">
                                <option></option>
                                <option *ngFor="let character of displayCharacterList" [value]="character.id"
                                    [selected]="character.id == roadblock?.CharacterID">
                                    {{ character.name }}
                                </option>
                            </select>
                        </ng-container>

                        <!--BOSS-->
                        <ng-container *ngIf="roadblock?.Type == 0">
                            <div style="margin-top: 12px;">
                                <input type="text" class="inputSearchInter unlockStyle" placeholder="Search Box"
                                    (ngModelChange)="filterBossListItem($event)"
                                    [(ngModel)]="filterObtainedTextFromList"
                                    [readonly]="isDisabled" 
                                    (focus)="enableSearch()" 
                                    (blur)="disableSearchIfEmpty()" 
                                    (mouseenter)="enableSearch()" 
                                    (mouseleave)="disableSearchIfEmpty()">
                            </div>
                            <label style="float: left;">Character ID</label><br>
                            <select (click)="changeCharacterId($event)" class="inputSearch unlockStyle">
                                <option></option>
                                <option *ngFor="let boss of displayBossList" [value]="boss.id"
                                    [selected]="boss.id == roadblock?.CharacterID">
                                    {{ boss.name }}
                                </option>
                            </select>
                        </ng-container>
                    </td>
                    <!--Fim Character ID-->

                    <!--COLLECTED CLASS-->
                    <td colspan="5" class="table-width" *ngIf="roadblock?.Type == 4">
                        <div style="text-align-last: left; margin-top: 12px;">
                            <input type="text" class="inputSearchInter" style="width: 200px !important;"
                                placeholder="Search Box" (ngModelChange)="filterClassListItem($event)"
                                [(ngModel)]="filterObtainedTextFromList"
                                [readonly]="isDisabled" 
                                (focus)="enableSearch()" 
                                (blur)="disableSearchIfEmpty()" 
                                (mouseenter)="enableSearch()" 
                                (mouseleave)="disableSearchIfEmpty()">
                        </div>
                        <div style="width: 200px !important; text-align: left;">
                            <label style="text-align-last: left !important; margin-top: 12px;">Class</label><br>
                            <select (click)="changeKlassId($event)" class="inputSearch"
                                style="width: 200px !important; margin-bottom: 12px;">
                                <option></option>
                                <option *ngFor="let klass of displayClassList" [value]="klass.id"
                                    [selected]="klass.id == roadblock?.klassId">
                                    {{ klass.name }}
                                </option>
                            </select>
                        </div>
                    </td>
                    <!-- fim COLLECTED CLASS-->

                    <!--PLACES-->
                    <td colspan="5" class="table-width" *ngIf="roadblock?.Type == 5">
                        <div style="text-align-last: left; margin-top: 12px;">
                            <input type="text" class="inputSearchInter" placeholder="Search Box" 
                            (ngModelChange)="filterSpokePlacesListItem($event)" 
                            [(ngModel)]="filterObtainedTextFromList"
                            [readonly]="isDisabled" 
                            (focus)="enableSearch()" 
                            (blur)="disableSearchIfEmpty()" 
                            (mouseenter)="enableSearch()" 
                            (mouseleave)="disableSearchIfEmpty()" />                      
                          </div>
                          
                        <label class="mg-top">Places</label><br>
                        <select (change)="changeSpokeId($event);" class="inputSearch" style="margin-bottom: 12px;">
                            <option></option>
                            <option *ngFor="let spokePlace of displaySpokePlaces" [value]="spokePlace.elementId"
                                [selected]="spokePlace.elementId == roadblock?.spokeElementId">{{ spokePlace.text }}
                            </option>
                        </select>
                    </td>
                    <!-- fim PLACES-->

                    <!--Karma Requirement-->
                    <td colspan="5" class="table-width" *ngIf="roadblock?.Type == 6">
                        <ng-container *ngIf="roadblock?.validateIfTrue">
                            <div style="text-align-last: left !important; margin-top: 12px;">
                                <label *ngIf="roadblock?.validateIfTrue">Karma Requirement</label><br>
                                <input class="inputSearchInter" style="width: 200px !important;" type="number"
                                    placeholder="Amount required..." min="0" [value]="roadblock?.ItemAmount"
                                    (change)="changeItemAmount($event)">
                            </div>
                            <div style="width: 200px !important; display: grid;">
                                <label style="text-align-last: left !important; margin-top: 12px;">Condition
                                    Operator</label>
                                <select name="operator" id="operator" (change)="changeOperator($event)"
                                    class="inputSearch" style="width: 200px !important; margin-bottom: 12px;">
                                    <option></option>
                                    <option value="greater" [selected]="roadblock?.operator === 'greater'">≥</option>
                                    <option value="lesser" [selected]="roadblock?.operator === 'lesser'">≤</option>
                                    <option value="equal" [selected]="roadblock?.operator === 'equal'">=</option>
                                    <option value="different" [selected]="roadblock?.operator === 'different'">
                                        <span>&#8800;</span>
                                    </option>
                                </select>
                            </div>
                        </ng-container>
                    </td>
                    <!-- fim Karma Requirement-->
                </tr>
            </tbody>
        </table>
    </div>
</td>