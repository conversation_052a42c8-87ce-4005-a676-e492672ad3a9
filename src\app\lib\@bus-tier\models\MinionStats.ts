import { CharacterType, Gender, IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { Base } from './Base';

export class MinionStats extends Base<Data.Hard.IMinionStats, Data.Result.IMinionStats> implements Required<Data.Hard.IMinionStats>
{
  private static generateId(index: number) : string 
  {
    return IdPrefixes.MINION_STATS + index;
  }

  constructor( index: number, dataAccess: MinionStats['TDataAccess']) 
  {
    super(
      {
        hard: 
        {
          id: MinionStats.generateId(index)
        },
      },
      dataAccess
    );
  }
  public override get id()
  {
    return this.hard.id;
  }

  public get area(): string 
  {
    return this.hard.area;
  }

  public set area(value: string) 
  {
    this.hard.area = value;
  }

  public get hc(): number 
  {
    return this.hard.hc;
  }

  public set hc(value: number) 
  {
    this.hard.hc = value;
  }

  public get name(): string 
  {
    return this.hard.name;
  }
  
  public set name(value: string) 
  {
    this.hard.name = value;
  }

  public get klass(): string 
  {
    return this.hard.klass;
  }
  
  public set klass(value: string) 
  {
    this.hard.klass = value;
  }

  public get ml(): number 
  {
    return this.hard.ml;
  }

  public set ml(value: number) 
  {
    this.hard.ml = value;
  }

  public get hp(): number 
  {
    return this.hard.hp;
  }

  public set hp(value: number) 
  {
    this.hard.hp = value;
  }

  public get atk(): number 
  {
    return this.hard.atk;
  }

  public set atk(value: number) 
  {
    this.hard.atk = value;
  }

  public get def(): number 
  {
    return this.hard.def;
  }

  public set def(value: number) 
  {
    this.hard.def = value;
  }

  public get qi(): number 
  {
    return this.hard.qi;
  }

  public set qi(value: number) 
  {
    this.hard.qi = value;
  }

  public get luk(): number 
  {
    return this.hard.luk;
  }

  public set luk(value: number) 
  {
    this.hard.luk = value;
  }

  public get pr(): number 
  {
    return this.hard.pr;
  }

  public set pr(value: number) 
  {
    this.hard.pr = value;
  }
  
  public get ev(): number 
  {
    return this.hard.ev;
  }

  public set ev(value: number) 
  {
    this.hard.ev = value;
  }  
}
