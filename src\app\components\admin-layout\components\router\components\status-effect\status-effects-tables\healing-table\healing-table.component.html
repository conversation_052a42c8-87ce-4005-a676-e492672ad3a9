<div class="div-container">
    <div class="width: 2221px;">
        <table class="table table-list borderList">
            <thead>    
                <tr>
                    <th [attr.colspan]="titles.length +1">
                        <h3>Healing</h3>
                    </th>
                    <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'" class="addButton"
                        [buttonTemplates]="[excelButtonTemplate]">
                    </app-button-group>
                </tr>
                <ng-container *ngIf="!isListHealingEmpty">
           
                    <tr>
                        <th class="default-color">Order</th>
                        <th
                          [ngClass]="(title === 'SKILL RESIST USER') ? 'skill-resist' : 'default-color'"
                          *ngFor="let title of titles">
                          {{title}}
                        </th>
                      </tr>
                      
                </ng-container>
            </thead>
            <ng-container *ngIf="!isListHealingEmpty">
                <tbody>
                    <tr *ngFor="let item of listHealingTable; let i = index">
                        <td style="background-color: #ddd;">{{ i + 1 }}</td>               
                            <td class="td-id aligTitle">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #idHealing [ngClass]="{'empty-input': !idHealing.value}"
                                    [value]="item.idHealing" (change)="changeHealimg(i,'idHealing', idHealing.value)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 110px;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #idCategory [ngClass]="{'empty-input': !idCategory.value}"
                                    [value]="item.category" (change)="changeHealimg(i,'category', idCategory.value)" />
                            </td>    
                            <td class="td-id aligTitle" style="width: 110px;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #status [ngClass]="{'empty-input': !status.value}"
                                    [value]="item.status" (change)="changeHealimg(i, 'status', status.value)" />
                            </td>
                            <td class="td-id aligTitle">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #skillResistUser [ngClass]="{'empty-input': !skillResistUser.value}"
                                    [value]="item.skillResistUser" (change)="changeHealimg(i, 'skillResistUser', skillResistUser.value)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 110px;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #operator [ngClass]="{'empty-input': !operator.value}"
                                    [value]="item.operator" (change)="changeHealimg(i, 'operator',operator.value)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 110px;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #value [ngClass]="{'empty-input': !value.value}"
                                [value]="item.value" (change)="changeHealimg(i, 'value', value.value)" />
                            </td>
                            <td class="td-id" style="width: 200px;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #statusEffectName [ngClass]="{'empty-input': !statusEffectName.value}"
                                [value]="item.statusEffectName" (change)="changeHealimg(i, 'statusEffectName', statusEffectName.value)" />
                            </td>
                            <td class="td-id" style="width: 700px; word-break: break-word;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #description [ngClass]="{'empty-input': !description.value}"
                                    [value]="item.description" (change)="changeHealimg(i, 'description', description.value)" />
                            </td>
                            <td class="td-id aligTitle">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #powerPoints [ngClass]="{'empty-input': !powerPoints.value}"
                                    [value]="item.powerPoints" (change)="changeHealimg(i, 'powerPoints', powerPoints.value)" />
                            </td>
                            <td class="td-id aligTitle">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #allHealing [ngClass]="{'empty-input': !allHealing.value}"
                                    [value]="item.allHealing" (change)="changeHealimg(i, 'allHealing', allHealing.value)" />
                            </td>  
                            <td class="td-id aligTitle">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #duration [ngClass]="{'empty-input': !duration.value}"
                                    [value]="item.duration" (change)="changeHealimg(i, 'duration', duration.value)" />
                            </td>                
                    </tr>
                </tbody>
            </ng-container>
            <ng-container *ngIf="isListHealingEmpty">
                <div class="card" style="text-align: center; padding: 20px;">
                    <h3>The list has not yet been imported.</h3>
                </div>
            </ng-container>
        </table>
</div>
    
</div>

