import { AudioListenerModule } from './../audio-listener/audio-list.module';
import { MajorModule } from 'src/app/major.module';
import { AppRoutingModule } from './components/router/app-routing.module';
import { SidebarModule } from './components/sidebar/sidebar.module';
import { CommonModule } from '@angular/common';
import { BrowserModule } from '@angular/platform-browser';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NgModule } from '@angular/core';

@NgModule({
  imports: [
    CommonModule,
    BrowserModule,
    FormsModule,
    RouterModule,
    AppRoutingModule,
    SidebarModule,
    MajorModule,
    AudioListenerModule
  ],
  exports: [AppRoutingModule, SidebarModule],
  declarations: [],
})
export class AdminLayoutModule {}
