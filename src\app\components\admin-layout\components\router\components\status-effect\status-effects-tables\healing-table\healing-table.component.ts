import { Component, EventEmitter, Output } from '@angular/core';
import { HealingTable } from 'src/app/lib/@bus-tier/models';
import { Button } from 'src/app/lib/@pres-tier/data';
import { HealingTableService } from 'src/app/services';
import { Alert } from 'src/lib/darkcloud';

@Component({
  selector: 'app-healing-table',
  templateUrl: './healing-table.component.html',
  styleUrls: ['./healing-table.component.scss']
})
export class HealingTableComponent {

  
    titles = ['ID', 'CATEGORY', 'STATUS', 'SKILL RESIST USER',
        'OPERATOR', 'VALUE', 'STATUS EFFECT NAME', 'DESCRIPTION', 'POWER POINTS (PP)', 'ALL', 'DURATION (TURNS)'];
        listHealingTable: HealingTable[] = [];
        activeLanguage = 'PTBR';
        @Output() activeTab2 = new EventEmitter<string>();
        isListHealingEmpty: boolean;
       
        public readonly excelButtonTemplate: Button.Templateable = {
          title: 'Paste content from excel',
          onClick: this.onExcelPaste.bind(this),
          iconClass: 'excel-icon',
          btnClass: Button.Klasses.FILL_ORANGE,
        };
        constructor(
          private _healingTableService: HealingTableService
        ){}
       
       
        async ngOnInit(): Promise<void>{
          
            this.removeEmptyItems();
             this.listHealingTable = this._healingTableService.models;
             this.isListHealingEmpty = this.listHealingTable.length === 0;   
       
          }

          removeEmptyItems() {
            this._healingTableService.toFinishLoading();
            this._healingTableService.models = this._healingTableService.models.filter(healingItem => healingItem.idHealing !== "");
            this._healingTableService.toSave();
          }

          async onExcelPaste() {
            const text = await navigator.clipboard.readText();
            const lines = text.split(/\r?\n/).filter(line => line);    
            const processedData: string[][] = [];
          
            if (lines.length > 0) {
              lines.forEach(line => {
                // Divide cada linha em colunas e remove a primeira coluna
                const values = line.split("\t").map(value => value.trim()).slice(1);
          
                processedData.push(values);
              });
          
              // Verifica se o número de colunas em cada linha corresponde ao número de colunas em this.titles
              const isColumnCountValid = processedData.every(row => row.length === this.titles.length);
          
              if (!isColumnCountValid) {
                Alert.showError('Invalid number of columns');
                return;
              }
        
              this._healingTableService.models = [];
              this._healingTableService.toSave();
          
              for (let index = 0; index < processedData.length; index++) {
                this._healingTableService.createNewHealingTable(processedData[index]);
              }    
       
              Alert.ShowSuccess('Healing Table imported successfully!');
              this.activeTab2.emit('healingTable');
              this.ngOnInit();
            }
          }
          
       
          changeHealimg(rowIndex: number, name: string, newValue: string){
         
            if (name === 'idHealing') {
             this.listHealingTable[rowIndex].idHealing = newValue;        
            }
            else if (name === 'category') {
              this.listHealingTable[rowIndex].category = newValue;        
             }   
             else if (name === 'status') {
              this.listHealingTable[rowIndex].status = newValue;        
             }
             else if (name === 'skillResistUser') {
               this.listHealingTable[rowIndex].skillResistUser = newValue;        
              }
             else if (name === 'operator') {
              this.listHealingTable[rowIndex].operator = newValue;        
             }
             else if (name === 'value') {
              this.listHealingTable[rowIndex].value = newValue;        
             }
             else if (name === 'statusEffectName') {
              this.listHealingTable[rowIndex].statusEffectName = newValue;        
             }
             else if (name === 'description') {
              this.listHealingTable[rowIndex].description = newValue;        
             }
             else if (name === 'powerPoints') {
              this.listHealingTable[rowIndex].powerPoints = newValue;        
             }
             else if (name === 'allHealing') {
              this.listHealingTable[rowIndex].allHealing = newValue;        
             }
             else if (name === 'duration') {
               this.listHealingTable[rowIndex].duration = newValue;        
              }
       
            this._healingTableService.svcToModify(this.listHealingTable[rowIndex]);
          }    
       
  

}
