import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { RPGFormatting } from 'src/lib/darkcloud';
import { Option, Speech } from 'src/app/lib/@bus-tier/models';
import { RpgService } from 'src/app/services/rpg.service';
import { language } from 'src/lib/darkcloud/dialogue-system/game-types';
import { EventHandlerService } from 'src/app/services/eventHandler.service';
interface Vector
{
  start:number,
  end:number
}

@Component({
  selector: 'app-rpg-content-input',
  templateUrl: './rpg-content-input.component.html',
})

export class RpgContentInputComponent
{
  toggleFormattingPopup: boolean;
  focused: boolean;
  hoveringFormatPopup: boolean;
  selection
  inputBox
  selectionStart:number = 0;
  selectionEnd:number = 0;
  inputText:string = '';
  @Input() rpgSuggestionLink: Option | Speech;
  @Input() content: string;
  @Input() language: language;
  @Output() contentChange: EventEmitter<string> = new EventEmitter();
  @Output() rpgWordAdded: EventEmitter<{word: string, rpgType: RPGFormatting.RPGType}> = new EventEmitter();

  constructor(private _rpgService: RpgService, private _eventsHandlerService: EventHandlerService) {}

  onChangeContent(content: string) 
  {
    this.content = content;
    this.contentChange.emit(this.content);
  }

  toggleFormatPopupByHover(value: boolean)
  {
    if (document.getSelection().rangeCount > 0) return;
    
    this.toggleFormatPopup(value);
  }  

  toggleFormatPopup(value: boolean) 
  {
    if (!value) 
    {
      this.toggleFormattingPopup = false;
      return;
    }
    //Object containig the data from the page.
    this.selection = window.getSelection();
    this.inputBox = this.selection?.getRangeAt(0).startContainer.parentElement;
    //Get the word index start selection. Deep copy to avoid reference change.
    this.selectionStart = +JSON.stringify(JSON.parse(this.selection.focusOffset));
    //Get the word index end selection.
    this.selectionEnd = +JSON.stringify(JSON.parse(this.selection.anchorOffset));
    //All the text from the input.
    this.inputText = this.inputBox.innerText;

    //open icons popup window
    if (this.selectionStart != this.selectionEnd) 
    {
      this.toggleFormattingPopup = true;
    } 
    else 
    {
      this.toggleFormattingPopup = false;
    }
  }
//For more information look the method name on documentation.
  async formatText(typeClicked: RPGFormatting.RPGType) 
  {
    if (!this.inputBox.classList.contains('content-input')) return; 
    //Verify which button we click in the popup
    const format = RPGFormatting.FORMATS.find((t) => +t.type === +typeClicked);

    //Normalize the start and end number independent of which side the selection started.
    if (+this.selectionStart > +this.selectionEnd) 
    {
      let aux = this.selectionStart;
      this.selectionStart = this.selectionEnd;
      this.selectionEnd = aux;      
    } 
    else if (+this.selectionStart === +this.selectionEnd) return;
    
    let match = '';
    let inputString = this.inputText.toString();
    //Extract the selected word from the whole sentence.
    for(let i = +this.selectionStart; i < +this.selectionEnd; i++)
    {
      match += inputString[i];
    }

    let vector: Vector = this.countLeadingAndTrailingWhitespace(match);
    //Resize the start and end if necessary. Go to doc for more info.
    this.selectionStart = this.selectionStart + vector.start;
    this.selectionEnd = this.selectionEnd - vector.end;

    //New workd with special characters added.
    const replacement = format.symbol.open + match.trim() + format.symbol.close;

    let apply = await this._rpgService.applyToAll(match.trim(), replacement);
    if(!apply) return;
    this._eventsHandlerService.NextRpgWordAdded(match.trim(), typeClicked);
    this.content = this.inputBox.innerText.substr(0, this.selectionStart) + replacement + this.inputBox.innerText.substr(this.selectionEnd);
    this.toggleFormattingPopup = false;
  }

  //For more info look the method on the documentation.
  countLeadingAndTrailingWhitespace(str):Vector
  {
    let vector: Vector = {start:0, end:0};
    vector.start = (str.match(/^\s*/)?.[0].length) || 0;
    vector.end = (str.match(/\s*$/)?.[0].length) || 0;
    return vector;
  } 
}

