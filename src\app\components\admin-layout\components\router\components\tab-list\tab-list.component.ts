import { Component,<PERSON><PERSON><PERSON>roy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Tab } from 'src/app/lib/@bus-tier/models/Tab';
import { But<PERSON> } from 'src/app/lib/@pres-tier/data';
import { ReviewService, UserSettingsService } from 'src/app/services';
import { LanguageService } from 'src/app/services/language.service';
import { TabService } from 'src/app/services/tab.service';
import { TranslationService } from 'src/app/services/translation.service';
import { Alert } from 'src/lib/darkcloud';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';

@Component({
  selector: 'app-tab-list',
  templateUrl: './tab-list.component.html',
  styleUrls: ['./tab-list.component.scss']
})
export class TabListComponent extends TranslatableListComponent<Tab> implements OnD<PERSON>roy{
  timeout:any;
  public readonly addTagButton: Button.Templateable = {
    title: 'Add a new tab to the list',
    onClick: this.createTab.bind(this),
    iconClass: 'pe-7s-plus',
    btnClass: Button.Klasses.FILL_GREEN,
  };

  constructor(
    _activatedRoute: ActivatedRoute,
    private _tabService: TabService,
    _userSettingsService: UserSettingsService,
    private _router: Router,
    private _reviewService: ReviewService,
    protected override _translationService: TranslationService,
    protected override _languageService: LanguageService
  )
  {
    super(_tabService, _activatedRoute, _userSettingsService, 'name', _translationService, _languageService);
  }

  override async lstInit()
  {
    if(!(this.lstIds?.length <= 0))
      return;

    await this._tabService.toFinishLoading();
    this.lstIds = this._tabService.models.map(x => x.id);
  }

  public async createTab()
  {
    let newTab;
    try {
      newTab = await this._tabService.svcPromptCreateNew();
    } catch (e) {
      Alert.showError("Tag já existe!");
    }
    if(!newTab)
      return;

    this._tabService.srvAdd(newTab);
    this._tabService.modelIds.push(newTab.id)

    this.refreshList();
  }

  redirectToItemClasses()
  {
    this._router.navigate(['itemClass']);
  }

  updateColor(model, value, colorLabel: HTMLElement)
  {
    this.updateInformation(model, 'hex', value);
    colorLabel.style.backgroundColor = value;
  }

  refreshList(scroll?: boolean)
  {
    let listProxy = this.lstIds;
    this.lstIds = [];
   this.timeout = setTimeout(() => {
      this.lstIds = listProxy
      this._tabService.svcReviewAll()
    }, 1);
  }

  ngOnDestroy() {
    clearInterval(this.timeout)
 
  }

}
