import { Component } from '@angular/core';
import { ThemeService } from 'src/app/services/video-theme.service';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/SortableListComponent';
import { Sorting } from 'src/lib/darkcloud/angular/easy-mvc/Sorting';
import { Theme } from 'src/app/lib/@bus-tier/models';
import { ReviewService } from 'src/app/services/review.service';
import { Button } from 'src/app/lib/@pres-tier/data';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-theme-list',
  templateUrl: './theme-list.component.html',
})
/**
 * Displays and edits theme data as a list
 */
export class ThemeListComponent extends SortableListComponent<Theme> {
  constructor(
    _activatedRoute: ActivatedRoute,
    _themeService: ThemeService,
    _userSettingsService: UserSettingsService,
    private _reviewService: ReviewService,
    private _router: Router,
    protected _languageService: LanguageService,
    protected _translationService: TranslationService
  ) {
    super(_themeService, _activatedRoute, _userSettingsService, 'name');
  }

  public readonly videoButtonTemplate: Button.Templateable = {
    btnClass: Button.Klasses.BLUE,
    title: 'Go to Video List',
    onClick: this.redirectToVideoList.bind(this),
    iconClass: 'pe-7s-play',
  };

  protected override specialSort(parameter: Sorting.Parameter) {
    switch (parameter) {
      case 'assigned':
        this._modelService.models.sort((a, b) =>
          this.srtLstOrder === 'ascending'
            ? this._reviewService.reviewResults[a.id].assignedAt.length >
              this._reviewService.reviewResults[b.id].assignedAt.length
              ? 1
              : -1
            : this._reviewService.reviewResults[b.id].assignedAt.length >
              this._reviewService.reviewResults[a.id].assignedAt.length
            ? 1
            : -1
        );
        break;
      default:
        this.defaultSort(parameter);
        break;
    }
  }

  public redirectToVideoList() {
    this._router.navigate(['videos']);
  }
}
