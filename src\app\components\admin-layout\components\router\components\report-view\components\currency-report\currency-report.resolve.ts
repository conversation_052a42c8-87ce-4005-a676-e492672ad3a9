import { Injectable } from '@angular/core';
import { EventService, ItemService } from 'src/app/services';
import { EasyMVC } from 'src/lib/darkcloud/angular';
import { EventType, ItemType } from 'src/lib/darkcloud/dialogue-system';
import { Preloadable } from './currency-report.component';

@Injectable({
  providedIn: 'root',
})
export class CurrencyReportResolve extends EasyMVC.Resolve<Preloadable> 
{
  constructor(
    private _eventService: EventService,
    private _itemService: ItemService
  ) 
  {
    super(_eventService, _itemService);
  }
  async rsvOnResolve(): Promise<Preloadable> 
  {
    await this._eventService.toFinishLoading();
    const currencyEvents = this._eventService.models.filter(
      (event) =>
        event?.itemId != undefined &&
        +this._itemService.svcFindById(event?.itemId)?.type === +ItemType.CURRENCY
    );

    return {
      currencyGiveEvents: currencyEvents.filter(
        (ev) => +ev?.type === +EventType.GIVE_ITEM
      ),
      currencyTradeEvents: currencyEvents.filter(
        (ev) => +ev?.type === +EventType.TRADE_ITEM
      ),
      currencyReceiveEvents: currencyEvents.filter(
        (ev) => +ev?.type === +EventType.RECEIVE_ITEM
      ),
    };
  }
}
