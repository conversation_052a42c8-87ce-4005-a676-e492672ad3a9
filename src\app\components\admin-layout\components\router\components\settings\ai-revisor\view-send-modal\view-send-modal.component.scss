.background-div {	
  position: relative;
  display: flex;
  justify-content: left;
  z-index: 9999;
}	

.background-div.popup-open:before 	
{	
  content: "";	
  position: fixed;	
  top: 0;	
  left: 0;	
  width: 100%;	
  height: 100%;	
  background-color: rgba(0, 0, 0, 0.5);	
  z-index: 9998;	
  pointer-events: none;	
}	


.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5); /* Ajuste a opacidade aqui */
  z-index: 998; /* <PERSON>e ser menor que o modal */
}

.iconInter {
  text-align: center; 
  font-size: 20px !important; 
  margin-top: 1px;
  margin-left: 3px;
  position: relative;
  z-index: 12;
}
i:hover{
  color: red;
  cursor: pointer;
}

.popup-report
{
  border-radius: 8px;
  width: 1000px;
  position: fixed;
  padding: 24px;
  top: 15%;
  transform: translate(85%, -15%);
  z-index: 1000;
  opacity: 1;
  height: auto;

}

.modal-header {
  color: white !important;
  padding: 0px !important;

  .modal-title {
    text-align: left !important;
    margin-bottom: 7px;
    font-weight: 600 !important;
    .close {
      opacity: 0px !important;
      margin-top: 0px !important;
    }
  }

   button {
    span {
      color: black !important;
    }
   }
}

.close {
  opacity: 1 !important;
 }

 .contextInfo {
  color: black;
  text-align: left;
  overflow-y: auto;
  white-space: pre-wrap;
  height: auto;
  max-height: 700px;
  scrollbar-width: thin;
  scroll-behavior: auto;  
  scrollbar-color: black white;
  padding-top: 10px;
}

.p-text {
  color:azure !important; 
  text-align: left; 
  text-transform: none !important;
}

.span-text {
  font-weight: 700;
  color: black;
}

/* ========== ESTILOS PARA A LISTAGEM DE ITENS ENVIADOS ========== */

/* Container principal da listagem */
.items-list-container {
  width: 100%;
  max-height: 500px;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  background-color: #fff;
  margin-top: 10px;
}

/* Cabeçalho da tabela */
.table-header {
  display: flex;
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  font-weight: bold;
  position: sticky;
  top: 0;
  z-index: 10;
}

.header-cell {
  padding: 12px 8px;
  border-right: 1px solid #dee2e6;
  text-align: center;
  font-size: 14px;
  color: #495057;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-cell:last-child {
  border-right: none;
}

/* Colunas com larguras específicas */
.lote-column {
  flex: 0 0 80px;
  min-width: 80px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.id-column {
  flex: 0 0 200px;
  min-width: 100px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.name-column {
  flex: 0 0 160px;
  min-width: 160px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 12px;
}

.content-column {
  flex: 1;
  min-width: 250px;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}

/* Container com scroll vertical */
.items-scroll-container {
  max-height: 400px;
  overflow-y: auto;
  overflow-x: hidden;
}

/* Estilo da scrollbar */
.items-scroll-container::-webkit-scrollbar {
  width: 8px;
}

.items-scroll-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.items-scroll-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.items-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Lista de itens */
.items-list {
  display: flex;
  flex-direction: column;
}

/* Linha de item */
.item-row {
  display: flex;
  border-bottom: 1px solid #e9ecef;
  transition: background-color 0.2s ease;
}

.item-row:hover {
  background-color: #f8f9fa;
}

.item-row.even {
  background-color: #fdfdfd;
}

/* Célula de item */
.item-cell {
  padding: 10px 8px;
  border-right: 1px solid #e9ecef;
  font-size: 13px;
  color: #495057;
  word-wrap: break-word;
  overflow-wrap: break-word;
  min-height: 40px;
}

.item-cell:last-child {
  border-right: none;
}

/* Conteúdo específico */
.content-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.content-item {
  padding: 4px 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #007bff;
  font-size: 12px;
  line-height: 1.4;
  max-height: 60px;
  overflow-y: auto;
}

.content-textValueString {
  padding: 4px 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #007bff;
  font-size: 12px;
  line-height: 1.4;
  overflow-y: auto;
}

.content-item::-webkit-scrollbar {
  width: 4px;
}

.content-item::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

/* Mensagens de estado */
.no-content {
  color: #6c757d;
  font-style: italic;
  font-size: 12px;
}

.no-items {
  padding: 40px 20px;
  text-align: center;
  color: #6c757d;
}

.no-items p {
  margin: 0;
  font-size: 16px;
  font-style: italic;
}

/* Responsividade */
@media (max-width: 768px) {
  .popup-report {
    width: 95vw;
    transform: translate(2.5%, -15%);
  }

  .items-list-container {
    max-height: 300px;
  }

  .items-scroll-container {
    max-height: 250px;
  }

  .lote-column {
    flex: 0 0 60px;
    min-width: 60px;
    padding-left: 4px;
  }

  .id-column {
    flex: 0 0 200px;
    min-width: 100px;
    padding-left: 4px;
  }

  .name-column {
    flex: 0 0 100px;
    min-width: 100px;
    padding-left: 8px;
  }

  .header-cell,
  .item-cell {
    padding: 8px 4px;
    font-size: 11px;
    min-height: 35px;
  }
}