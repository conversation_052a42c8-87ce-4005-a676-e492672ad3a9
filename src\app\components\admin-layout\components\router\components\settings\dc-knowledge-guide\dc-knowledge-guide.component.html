<div class="m-margin">
  <ng-container *ngIf="knowledgeClasses.length > 0 && listDCKnowledgeGuide.length > 0">
    <table class="table-bordered">
      <thead>
        <tr>
          <th class="trBC" style="width: 4%;">Index</th>
          <th class="trBC" *ngFor="let header of listHeader; let i = index;" [ngStyle]="{'width': header === 'DC min' || header === 'DC max' ? '5%' : ''}">{{header}}</th>
          <th class="trBC" style="width: 5%;">Actions</th>
        </tr>
      </thead>

      <tbody>
        <ng-container *ngFor="let item of listDCKnowledgeGuide; let i = index">
          <tr>
            <td class="gray">{{i + 1}}</td>
            <td>
              <input style="width: 100%;"
                     class="background-input-table-color form-control form-short "
                     placeholder=" " type="number" #dcMin
                     [(ngModel)]="item.dcMin"
                     (change)="changeDCValue(i, 'dcMin', dcMin.value)" />
            </td>
            <td>
              <input style="width: 100%;"
                     class="background-input-table-color form-control form-short "
                     placeholder=" " type="number" 
                     [(ngModel)]="item.dcMax" #dcMax
                     (change)="changeDCValue(i, 'dcMax', dcMax.value)" />
            </td>
            <td>
              <input style="width: 100%;"
                     class="background-input-table-color form-control form-short "
                     placeholder=" " type="text"
                     [(ngModel)]="item.description"
                     (change)="changeDCValue(i, 'description', item.description)" />
            </td>
            <td>
              <input style="width: 100%;"
                     class="background-input-table-color form-control form-short "
                     placeholder=" " type="text"
                     [(ngModel)]="item.arcana"
                     (change)="changeDCValue(i, 'arcana', item.arcana)" />
            </td>
            <td>
              <input style="width: 100%;"
                     class="background-input-table-color form-control form-short "
                     placeholder=" " type="text"
                     [(ngModel)]="item.engineering"
                     (change)="changeDCValue(i, 'engineering', item.engineering)" />
            </td>
            <td>
              <input style="width: 100%;"
                     class="background-input-table-color form-control form-short "
                     placeholder=" " type="text"
                     [(ngModel)]="item.stealth"
                     (change)="changeDCValue(i, 'stealth', item.stealth)" />
            </td>
            <td>
              <input style="width: 100%;"
                     class="background-input-table-color form-control form-short "
                     placeholder=" " type="text"
                     [(ngModel)]="item.investigation"
                     (change)="changeDCValue(i, 'investigation', item.investigation)" />
            </td>
            <td class="td-actions"
                style="display: flex; justify-content: space-around;">
              <button class="btn btn-danger btn-fill btn-remove"
                      (click)="removeLineDC(i)">
                <i class="pe-7s-close"></i>
              </button>
            </td>
          </tr>
        </ng-container>
      </tbody>    
    </table>
  </ng-container>

  <ng-container *ngIf="listDCKnowledgeGuide.length === 0 && knowledgeClasses.length > 0">
    <div style="text-align: center;">
      <h4>Empty knowledge Guide list.</h4>
    </div>
  </ng-container>
  <ng-container *ngIf="listDCKnowledgeGuide.length > 0 && knowledgeClasses.length === 0">
    <div style="text-align: center;">
      <h4>Empty knowledge Class list.</h4>
    </div>
  </ng-container>
  <ng-container *ngIf="listDCKnowledgeGuide.length === 0 && knowledgeClasses.length === 0">
    <div style="text-align: center;">
      <h4>Empty knowledge Guide and knowledge Class lists.</h4>
    </div>
  </ng-container>


</div>
