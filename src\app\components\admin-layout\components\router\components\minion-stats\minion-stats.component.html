<div class="main-content">
  <div class="container-fluid">
    <!-- START: Header -->
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons 
          [cardTitle]="listName"
          [cardDescription]="description">
        </app-header-with-buttons>
        <div style="display: flex; flex-direction: row; justify-content: space-between; margin-left: 50px; margin-right: 50px;">
          <div >
            <h4 style="display: inline-block;">Area</h4>
            <select 
              class="dropdown filter-dropdown limited"
              style="display: inline-block; margin: 10px; margin-bottom: 15px; "
              #InputArea (change)="filterArea(InputArea.value)">
              <option value="ALL">All</option>
              <option *ngFor="let area of areas" value="{{ area.id }}">{{ area.hierarchyCode }}{{" : "}}{{ area.name }}</option>
            </select>
          </div>
          <div >
            <h4 style="display: inline-block;">HC</h4>
            <select 
              class="dropdown filter-dropdown limited"
              style="display: inline-block; margin: 10px; margin-bottom: 15px"
              #InputType (change)="filterHC(InputType.value)">
              <option value="ALL">All</option>
              <option *ngFor="let hc of HCs" value="{{ hc }}">{{ hc }}</option>
            </select>
          </div>    
          <div >
            <h4 style="display: inline-block;">Class</h4>
            <select 
              class="dropdown filter-dropdown limited"
              style="display: inline-block; margin: 10px; margin-bottom: 15px"
              #InputClass (change)="filterClass(InputClass.value)">
              <option value="ALL">All</option>
              <option *ngFor="let class of classes" value="{{ class }}">{{ class }}</option>
            </select>
          </div>
        </div>
        <app-header-search 
          (inputKeyup)="search($event)"
          (searchOptions)="lstOnChangeFilterOptions($event)">
        </app-header-search>
      </div>
    </div>
    <!-- END: Header -->

    <!-- START: List -->
    <div class="card">
      <table class="table table-list">
        <thead class="sticky">
          <tr>
            <th>Index</th>
            <th class="th-clickable" >ID</th>
            <th class="th-clickable" (click)="sortByString('area')">Area</th>
            <th class="th-clickable" (click)="sortByNumber('hc')" >HC</th>
            <th class="th-clickable" (click)="sortByString('name')" >Name</th>
            <th class="th-clickable" (click)="sortByNumber('ml')" >ML</th>
            <th class="th-clickable" (click)="sortByNumber('hp')" >HP</th>
            <th class="th-clickable" (click)="sortByNumber('atk')">ATK</th>
            <th class="th-clickable" (click)="sortByNumber('def')">DEF</th>
            <th class="th-clickable" (click)="sortByNumber('qi')">QI</th>
            <th class="th-clickable" (click)="sortByNumber('luk')">LUK</th>
            <th class="th-clickable" (click)="sortByNumber('pr')">PR</th>
            <th class="th-clickable" (click)="sortByNumber('ev')">EV</th>      
            <th class="th-clickable" >Actions</th>      
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let minion of charactersList; let i = index; trackBy: trackById">
            <tr>
              <td class="td-sort">{{ i + 1 }}</td>
              <td class="td-id">
                {{minion.id}}
              </td>
              <td class="td-id">
                {{(minion.area | area)?.name}}
              </td>
              <td class="td-id">
                {{minion.hc}}   
              </td>
              <td class="td-id">
                {{minion.name}}    
              </td>
              <td class="td-actions">
                <input
                  (change)="changeMinionValue(minion, ml.value, 'ml')"
                  class="background-input-table-color"
                  placeholder=" "
                  type="number"
                  #ml
                  value="{{minion.ml}}"/>    
              </td>
              <td class="td-actions">
                <input
                  (change)="changeMinionValue(minion, hp.value, 'hp')"
                  class="background-input-table-color"
                  placeholder=" "
                  type="number"
                  #hp
                  [value]="minion.hp"/>     
              </td>
              <td class="td-actions">
                <input
                  (change)="changeMinionValue(minion, atk.value, 'atk')"
                  class="background-input-table-color"
                  placeholder=" "
                  type="number"
                  #atk
                  [value]="minion.atk"/>     
              </td>
              <td class="td-actions">
                <input
                  (change)="changeMinionValue(minion, def.value, 'def')"
                  class="background-input-table-color"
                  placeholder=" "
                  type="number"
                  #def
                  [value]="minion.def"/>     
              </td>
              <td class="td-actions">
                <input
                  (change)="changeMinionValue(minion, qi.value, 'qi')"
                  class="background-input-table-color"
                  placeholder=" "
                  type="number"
                  #qi
                  [value]="minion.qi"/>    
              </td>
              <td class="td-actions">
                <input
                  (change)="changeMinionValue(minion, luk.value, 'luk')"
                  class="background-input-table-color"
                  placeholder=" "
                  type="number"
                  #luk
                  [value]="minion.luk"/>     
              </td>
              <td class="td-actions">
                <input
                  (change)="changeMinionValue(minion, pr.value, 'pr')"
                  class="background-input-table-color"
                  placeholder=" "
                  type="number"
                  #pr
                  [value]="minion.pr"/>   
              </td>
              <td class="td-actions">
                <input
                  (change)="changeMinionValue(minion, ev.value, 'ev')"
                  class="background-input-table-color"
                  placeholder=" "
                  type="number"
                  #ev
                  [value]="minion.ev"/>    
              </td>
              <td class="td-actions">
                <button (click)="copyFromExcel(minion)" style="background-color: orange; border-color:orange;"> 
                  <img src="assets/img/icons/icon-excel.png">
                </button>   
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
    <!-- END: List -->
  </div>
</div>