import { AfterViewChecked, Component } from '@angular/core';
import { Area, Character, Level, Option, Scenery, Speech } from 'src/app/lib/@bus-tier/models';
import { Minigame } from 'src/app/lib/@bus-tier/models/Minigame';
import { Interpreter } from 'src/app/lib/Interpreter';
import {
  AreaService,
  CharacterService,
  ClassService,
  ConditionService,
  DialogueService,
  DilemmaBoxService,
  EventService,
  ItemService,
  LevelService,
  OptionBoxService,
  OptionService,
  PopupService,
  ReviewService,
  SceneryService,
  SpeechService,
  StoryBoxService,
  UserSettingsService
} from 'src/app/services';
import { LanguageService } from 'src/app/services/language.service';
import { MinigameService } from 'src/app/services/minigame.service';
import { RoadBlockService } from 'src/app/services/road-block.service';
import { StoryExpansionPkgService } from 'src/app/services/story-expansion-pkg.service';
import { TranslationService } from 'src/app/services/translation.service';
import { <PERSON><PERSON>, Popup } from 'src/lib/darkcloud';
import { EasyMVC } from 'src/lib/darkcloud/angular';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';
import { ConditionType, DialogueType, LevelType } from 'src/lib/darkcloud/dialogue-system';
import { language } from 'src/lib/darkcloud/dialogue-system/game-types';
import { extractInt, Index } from 'src/lib/others';
import { StoryExpansionPkg } from './../../../../../../../../lib/@bus-tier/models/StoryExpansionPkg';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-level-list',
  templateUrl: './level-list.component.html',
  styleUrls: ['./level-list.component.scss']
})

export class LevelListComponent extends TranslatableListComponent<Level> implements AfterViewChecked {
  get LevelType() {
    return LevelType;
  }

  get dialogueType() {
    return DialogueType;
  }

  public get averageLevels(): number {
    return Math.round(this._levelService.models.length / this._areaService.models.length);
  }

  public language: language = 'PT-BR';

  protected override lstFilterParameters: EasyMVC.Filter[] = [{ name: 'areaId', preventAllFiltering: true }];
  listDescription: string = '';

  currentArea: Area;
  counter: number = 0;
  allLevelIdsFromAreaAndSubarea: string[] = [];
  currentUndefinedParentLevel: Level;

  public levelHeights: Index<number> = {};
  public levelBranchIndex: Index<number> = {};
  public iconIds: string[] = [];
  public preloadedAreas: Area[];
  public averageAmountWordsPerLevel: number;
  public averageAmountWordsAllAreas: number;
  public amountDescriptionAllAreas: number;
  public amountWordsDescriptionAllAreas: number;
  public amountLevelsAllAreas: number;
  public amountWordsAllAreas: number;
  public amountPaths = [];
  public canEnumerate = false;
  public minigames: Minigame[] = [];
  levels;
  tempLevelsIds;
  runOnce: boolean = true;

  constructor(
    private _itemService: ItemService,
    private _areaService: AreaService,
    private _reviewService: ReviewService,
    _userSettingsService: UserSettingsService,
    private _characterService: CharacterService,
    private _conditionService: ConditionService,
    protected _levelService: LevelService,
    private _speechService: SpeechService,
    public _dialogueService: DialogueService,
    public _eventService: EventService,
    public _storyboxService: StoryBoxService,
    public _optionboxService: OptionBoxService,
    public _dilemmaBoxService: DilemmaBoxService,
    public _roadBlockService: RoadBlockService,
    private _sceneryService: SceneryService,
    private _optionService: OptionService,
    private _popupService: PopupService,
    _activatedRoute: ActivatedRoute,
    private _router: Router,
    private _classService: ClassService,
    private _minigameService: MinigameService,
    private _storyExpansionService: StoryExpansionPkgService,
    protected override _translationService: TranslationService,
    protected override _languageService: LanguageService,
  ) {
    super(_levelService, _activatedRoute, _userSettingsService, 'name', _translationService, _languageService);
  }

  fillLevelsAreaByArea(currentSelectedArea?) {

    this.levels = [];
    this.currentArea = this._areaService.svcFindById(currentSelectedArea);
    this._areaService.currentSelectedArea = this.currentArea.id;
    for (let i = 0; i < this._levelService.models.length; i++) {
      if (this._levelService.models[i].id.split('.')[0] == currentSelectedArea) {
        this.levels.push(this._levelService.models[i]);
      }
    }
    this.addPathOrderFieldWhereDoesNotExists();
    this.removeOldPathOrder();
    this.calculateBranchesAmountByArea();
    this.tempLevelsIds = this.lstIds;
    this.listDescription = "Showing " + this.lstIds.length + " results";
  }


  override lstInit() {
    if (!this.currentArea && this._levelService.currentSelectedArea) {
      this.currentArea = this._levelService.currentSelectedArea;
    }

    this.removeUndefinedLinkedLevel();
    this.preloadedAreas = this._areaService.models;

    this.minigames = this._minigameService.models;

    this._userSettingsService.data?.icons.forEach((icon) => {
      this.iconIds.push(icon.id);
    });
    this.amountWordsAllAreas = 0;
    this._levelService.models.forEach((level) => {

      this.amountWordsAllAreas +=
        this._reviewService.reviewResults[level.id].amountOfWords || 0;
    });

    const levelsWithMessages: string[] = [];
    const areasWithMessages: string[] = [];
    []
      .concat(this._speechService.models)
      .concat(this._optionService.models)
      .forEach((t: Option | Speech) => {
        const levelId = Level.getSubIdFrom(t.id);
        const areaId = Area.getSubIdFrom(levelId);
        if (!levelsWithMessages.includes(levelId)) {
          levelsWithMessages.push(levelId);
          if (!areasWithMessages.includes(areaId)) {
            areasWithMessages.push(areaId);
          }
        }
      });

    this.averageAmountWordsAllAreas =
      Math.round(this.amountWordsAllAreas / areasWithMessages.length) || 0;
    this.averageAmountWordsPerLevel =
      Math.round(this.amountWordsAllAreas / levelsWithMessages.length) || 0;
    this.amountLevelsAllAreas = this._levelService.models.length;

    this.amountWordsDescriptionAllAreas = 0;
    this.amountDescriptionAllAreas = this._levelService.models.filter((l) => {
      this.amountWordsDescriptionAllAreas += l.description?.length ?? 0;
      return l.description ? true : false;
    }).length;

    this._levelService.models
      .filter(
        (level) =>
          this._reviewService.reviewResults[level.id].parent_levelIds_sameArea
            .length <= 1
      )
      .forEach((level) => {
        this.levelHeights[level.id] = 0;
        this.levelBranchIndex[level.id] = 0;
      });

    this.addPathOrderFieldWhereDoesNotExists();
    this.removeOldPathOrder();
    
    setTimeout(() => {
      this.tempLevelsIds = this.lstIds;
      this.listDescription = "Showing " + this.lstIds.length + " results";
    }, 800)

  // this.inicializedFieldsReviewed();
  }

  inicializedFieldsReviewed() {
    this._levelService.models.forEach((level) => {
      level.isReviewedName = !!level.isReviewedName;
      level.isReviewedDescription = !!level.isReviewedDescription;
      this._levelService.svcToModify(level);
    });
  }

  ngAfterViewChecked(): void {
    this.calculateBranchesAmountByArea();
  }

  async increaseAreaPathAmount(level: Level) {
    if (level.linkedLevelIds.length == 0) {
      this.counter += 2;
      this.amountPaths = Array(this.counter).fill(0).map((x, i) => i + 1);
    }
    else if (level.linkedLevelIds.length >= 1) {
      this.counter += 1;
      this.amountPaths = Array(this.counter).fill(0).map((x, i) => i + 1);
    }
  }

  calculateBranchesAmountByArea() {
    this.counter = 0;
    this.getSubAreaLevelIds();

    this._levelService.models.forEach(level => {
      if (level.linkedLevelIds.length == 2 && this.allLevelIdsFromAreaAndSubarea.includes(level?.id)) {
        for (let i = 0; i < level?.pathOrder.length; i++) {
          if (level?.pathOrder[i] != -1 && level?.pathOrder[i] != null) {
            this.counter += 1;
          }
        }
      }
      else if (level.linkedLevelIds.length > 2 && this.allLevelIdsFromAreaAndSubarea.includes(level?.id)) {
        for (let i = 0; i < level?.pathOrder.length; i++) {
          if (level?.pathOrder[i] != -1 && level?.pathOrder[i] != null) {
            this.counter += 1;
          }
        }
      }
    })
    this.amountPaths = Array(this.counter).fill(0).map((x, i) => i + 1);
  }

  getSubAreaLevelIds() {
    this.allLevelIdsFromAreaAndSubarea = [];
    if (this.currentArea?.hierarchyCode == "A0") {
      this.allLevelIdsFromAreaAndSubarea = this.currentArea.levelIds;
      return;
    }
    if (this.currentArea?.hierarchyCode == undefined && this.runOnce && this.currentArea?.name) {
      this.runOnce = false;
      Alert.showError(`The ${this.currentArea?.name} area is missing the hierarchy code field`);
    }
    let areaNumber = this.currentArea?.hierarchyCode?.match(/\d+/g);
    if (areaNumber == undefined) return;

    this._areaService.models.forEach(area => {
      if (area?.hierarchyCode != "A0") {
        let subAreaNumber = area?.hierarchyCode?.match(/\d+/g);
        if (subAreaNumber[0] == areaNumber[0]) {
          this.allLevelIdsFromAreaAndSubarea = this.allLevelIdsFromAreaAndSubarea.concat(area.levelIds);
        }
      }
    })
  }

  noRepeatedEntryForPathOrder(level: Level): number {
    let index = 1;

    if (level.linkedLevelIds.length > 1) {
      for (let j = 0; j < this._levelService.models.length; j++) {
        for (let i = 0; i < this._levelService.models[j].pathOrder.length; i++) {
          if (this.allLevelIdsFromAreaAndSubarea.includes(this._levelService?.models[j]?.id) &&
            index == this._levelService?.models[j]?.pathOrder[i]) {
            index += 1;
            j = 0;
            i = 0;
            break;
          }
        }
      }
    }
    return index;
  }

  addPathOrderWhenBranching(): number {
    let index = 1;
    for (let j = 0; j < this._levelService.models.length; j++) {
      for (let i = 0; i < this._levelService.models[j].pathOrder.length; i++) {
        if (index == this._levelService.models[j].pathOrder[i] &&
          this.allLevelIdsFromAreaAndSubarea.includes(this._levelService.models[j]?.id)) {
          index++;
          j = 0;
        }
      }
    }

    return index;
  }

  public getLevelIndex(levelId: string): number {
    let splitId = levelId.split('.');

    let area = this._areaService.svcFindById(splitId[0]);
    let levels = area.levelIds;

    let t = levels.indexOf(levelId);
    return t
  }

  getCurrentArea() {
    const area = this._areaService.svcFindById(this.lstFilterValue['areaId'] as string);
    this.currentArea = area;
  }

  async addBaseLevel() {
    let areaId = (this.lstFilterValue['areaId'] as string)
    let newLevel = await this._levelService.svcPromptCreateNew(this.lstFilterValue['areaId'] as string);
    this._levelService.srvAdd(newLevel, this._levelService.svcNextIndex());
    let area = this._areaService.svcFindById(areaId);
    this.calculateBranchesAmountByArea();

    await this._areaService.addLevel(area, newLevel.id);
    this.lstFetchLists();
  }

  public accessLevel(linkedLevelId: string) {
    const level = this._levelService.svcFindById(linkedLevelId);
    if (!level) return;

    const levelAreaId = Area.getSubIdFrom(level.id);
    if (levelAreaId !== (this.lstFilterValue['areaId'] as string)) {
      this.lstFilterValue['areaId'] = levelAreaId;
      this.lstOnChangeFilter();
    }
    this.HighlightElement(level.id, 100, true);
  }

  public redirectToDialoguesManager(level: Level) {
    this._router.navigate(['levels/' + level.id + '/dialogues']);
  }

  async toPromptEnableDialogue(level: Level) {
    if (
      await Alert.showConfirm(
        'Enable Dialogues?',
        'By enabling them, you can access them without any speakers.',
        'Yes, enable ' + level.id + ' dialogues'
      )
    ) {
      this.updateInformation(level, 'enablesDialogue', true);
      this._router.navigate(['levels/' + level.id + '/dialogues']);
    }
  }

  getLevelOrtography(level: Level) {
    this._translationService.getLevelOrtography(level, true);
  }

  async removeUndefinedLinkedLevel() {
    for (let i = 0; i < this._levelService.models.length; i++) {
      let aux = this._levelService.models[i].linkedLevelIds;
      for (let j = 0; j < aux.length; j++) {
        if (aux[j] == undefined || aux[j] == null || aux[j] == "") {
          aux.pop();
          if (aux.length == 1) {
            this._levelService.models[i]?.pathOrder?.pop();
          }
          await this._levelService.svcToModify(this._levelService.models[i]);
        }
      }
    }
  }

  removeOldPathOrder() {
    this._levelService.models.forEach(async level => {
      let linkedLength = level?.linkedLevelIds?.length;
      let pathOrderLength = level?.pathOrder?.length;
      let difference = pathOrderLength - linkedLength;

      if (linkedLength > 1 && pathOrderLength > linkedLength) {
        while (difference > 0) {
          level?.pathOrder?.pop();
          difference--;
        }
        this._levelService.svcToModify(level);
      }

      if (linkedLength == 1 && pathOrderLength != 0) {
        while (difference >= 0) {
          level?.pathOrder?.pop();
          difference--;
        }
        this._levelService.svcToModify(level);
      }

      while (difference < 0 && level.linkedLevelIds.length > 1) {
        level.pathOrder.push(+this.noRepeatedEntryForPathOrder(level));
        await this._levelService.svcToModify(level);
        difference += 1;
      }
    })
  }

  addPathOrderFieldWhereDoesNotExists() {
    this._levelService.models.forEach(level => {
      if (!level?.pathOrder) {
        level["pathOrder"] = []
      }
    })
  }

  override async lstAfterFetchList() {
    this.UpdateHeights();
    this.calculateBranchesAmountByArea();
  }

  ChangeLevelPathOrder(level: Level, linkIndex: number, newPathOrder: any) {
    newPathOrder = newPathOrder.target.value
    let area = this._areaService.svcFindById(this.lstFilterValue['areaId']?.toString());
    let subAreas = [];
    subAreas.push(area);
    subAreas = this._areaService.GetSubAreas(area);
    let areaLevels = [];
    area.levelIds.forEach(levelId => { areaLevels.push(levelId); });
    subAreas.forEach(area => { area.levelIds.forEach(levelId => { areaLevels.push(levelId); }); });

    let oldPathOrderLevel = this._levelService.models.find(l => l.pathOrder?.includes(+newPathOrder) && areaLevels.includes(l.id));
    if (oldPathOrderLevel) {
      let indexOld = oldPathOrderLevel.pathOrder.indexOf(+newPathOrder);
      oldPathOrderLevel.pathOrder[indexOld] = level.pathOrder[linkIndex];
      this._levelService.svcToModify(oldPathOrderLevel);
    }
    if (level.id == oldPathOrderLevel?.id) {
      level = oldPathOrderLevel;
    }
    level.pathOrder[linkIndex] = +newPathOrder;
    this._levelService.svcToModify(level);
    this.lstFetchLists();
  }

  override lstAfterInitLoadFilters() {
    const levelId = this._activatedRoute.snapshot.fragment || this.lstAnchorId;
    if (levelId) {
      const areaId = Area.getSubIdFrom(levelId);
      if (this._areaService.svcFindById(areaId)) {
        this.lstFilterValue['areaId'] = areaId;
        this.lstSaveFilters();
      }
    }
  }

  private UpdateHeights() {
    this.lstIds.forEach((id) => {
      this.assignHeight(id);
    });
  }

  protected override filterItem(level: Level) {
    return (Area.getSubIdFrom(level.id) === (this.lstFilterValue['areaId'] as string));
  }

  changeCanEnumerate() {
    this.canEnumerate = !this.canEnumerate;
  }

  public async onChangeLink(level: Level, index: string, levelLinkIndexString: string) {
    const previousLevel = this._levelService.svcFindById(level.linkedLevelIds[index]);

    let levelId: string;
    level.linkedLevelIds[index] = undefined;

    if (levelLinkIndexString?.length > 0) {
      levelId = this._levelService.models[extractInt(levelLinkIndexString.replace(/\s/gu, ''))]?.id;
      level.linkedLevelIds[index] = levelId;
    }
    level.pathOrder.push(this.noRepeatedEntryForPathOrder(level));
    await this._levelService.svcToModify(level);

    if (previousLevel) {
      this._levelService.svcReviewById(previousLevel.id);
    }
    if (levelId) {
      this._levelService.svcReviewById(levelId);
    }
    this.currentUndefinedParentLevel = undefined;
    this.UpdateHeights();
  }

  toPromptAddCondition(level: Level) {
    const interpreter = new Interpreter
      ({
        characters: this._characterService.models,
        items: this._itemService.models,
        klasses: this._classService.models
      });
    let conditionsToString = '\n';
    level.conditionIds?.forEach((id, index) => {
      const condition = this._conditionService.svcFindById(id);
      conditionsToString += index + 1 + ': ' + interpreter.interpretInterface(condition, 'Condition') + '\n';
    });
    Alert.showOptions('Certain death if the player...', 'Condition', conditionsToString, 'Select').then(async (result) => {
      if (!result) return;
      switch (result) {
        case Alert.AlertOptions.POP:
          if (level.conditionIds.length > 0) {
            await this._conditionService.svcToRemove(level.conditionIds[level.conditionIds.length - 1]);
            this._levelService.popCondition(level);
          }
          break;
        case Alert.AlertOptions.CLEAR:
          await this._conditionService.svcToRemove(level.conditionIds);
          this._levelService.clearConditions(level);
          break;
        case Alert.AlertOptions.ADD:
          const type = await Alert.showRadioEnum('Select Condition Type',
            {
              [+ConditionType.LOADSOUT_BOSS]: 'Loads out Boss',
              [+ConditionType.LOADSOUT_ITEM]: 'Loads out Item',
              [+ConditionType.LOADSOUT_CHARACTER_CLASS]: 'Loads out Character Class'
            });
          if (type === undefined) return;

          const conditionInstance = await this._conditionService.svcPromptCreateNew(type, level);
          if (conditionInstance == undefined) return;

          await this._conditionService.srvAdd(conditionInstance);
          this._levelService.addCondition(level, conditionInstance);
          break;
      }
      this.toPromptAddCondition(level);
    });
  }


  public async addStoryExpansionContent(levelId: string) {
    let storyExp: StoryExpansionPkg = this.verifyLevelidInsideStoryexpansionpkg(levelId);
    if (storyExp != undefined) {
      //1.1 Se existir e for igual, remove esse e NAO adiciona nada.
      if (this.isStoryexpansionAlreadyClicked(storyExp)) await this._storyExpansionService.svcToRemove(storyExp.id);
      //1.2 Se existir e for diferente desse, remove o velho e adiciona esse.
      else {
        let canRemove = await this._storyExpansionService.toPromptRemove(storyExp);
        if (canRemove) {
          const area = this._areaService.svcFindById(Area.getSubIdFrom(levelId));
          this._storyExpansionService.createNewExpansionPkg(levelId, 0, area.id);
          this.ngOnInit();
        }
      }
    }
    else {
      //1.2 Se NAO existir cria novo 	
      const area = this._areaService.svcFindById(Area.getSubIdFrom(levelId));
      this._storyExpansionService.createNewExpansionPkg(levelId, 0, area.id);
    }
    this.ngOnInit();
  }

  isStoryexpansionAlreadyClicked = (storyExp): boolean => storyExp.id.split('.D')[1]?.length == 0 ||
    storyExp.id.split('.D')[1]?.length == undefined;

  verifyLevelidInsideStoryexpansionpkg(levelId: string) {
    //1. Verificar se o id ja existe nos models	
    for (let i = 0; i < this._storyExpansionService.models.length; i++) {
      if (this.isLevelWithCorrespondingStoryexpansion(levelId, i)) {
        return this._storyExpansionService.models[i];
      }
    }
    return undefined;
  }

  isLevelWithCorrespondingStoryexpansion = (levelId: string, i: number): boolean =>
    this._storyExpansionService.models[i]?.level?.split('.D')[0] == levelId;

  async toPromptChangeScenery(level: Level) {
    const selectedSceneryButton = await this._popupService.fire<Area, Scenery>(
      new Popup.Interface(
        {
          title: 'Select Area',
          actionsClass: 'column',
        },
        Popup.toButtonList(
          this._areaService.models,
          {
            parameters: ['hierarchyCode', 'name'],
            mapping: '[<hierarchyCode>]: <name>',
          },
          { undefinedTitle: 'No Area' }
        ),
        {
          inputButton: {
            value: this._areaService.svcFindById(Area.getSubIdFrom(level.id)),
          },
          next: (selectedAreaButton) => {
            if (!selectedAreaButton) return null;

            return new Popup.Interface(
              {
                title: 'Scenery',
                actionsClass: 'column',
              },
              Popup.toButtonList(this._sceneryService.models.filter((scenery) => scenery.areaId === selectedAreaButton.value?.id),
                'name',
                {
                  undefinedTitle: 'No Scenery',
                }
              ),
              {
                inputButton:
                {
                  value: this._sceneryService.svcFindById(level.sceneryId),
                },
              }
            );
          },
        }
      )
    );

    if (!selectedSceneryButton) return;

    level.sceneryId = selectedSceneryButton.value?.id;
    await this._levelService.svcToModify(level);
  }

  public async pushLevelLink(level: Level) {
    this.currentUndefinedParentLevel = level;

    //Level do NOT have linked level. Create one link!
    if (level.linkedLevelIds.length == 0) {
      this.addNextLinkedLevel(level);
      await this._levelService.svcToModify(level);
      this.UpdateHeights();
    }
    //When there is one linked level. Create 2 pathOrder and one linked level
    else if (level.linkedLevelIds.length == 1) {
      this.addNextLinkedLevel(level);
      level.pathOrder.push(+this.noRepeatedEntryForPathOrder(level));
      await this._levelService.svcToModify(level);
      level.pathOrder.push(+this.noRepeatedEntryForPathOrder(level) + 1);
      await this._levelService.svcToModify(level);
    }
    //Level HAVE linked levels. Create 
    else if (level.linkedLevelIds.length > 1) {
      const conf = await Alert.showConfirm(
        'Do you want to connect?',
        'Use it to create a new branch using a OLD LEVEL that DO NOT have any link!',
        'Yes, Connect!'
      );

      if (conf) {
        this.addNextLinkedLevel(level);
        await this._levelService.svcToModify(level);
        this.UpdateHeights();
      }
    }


  }

  addNextLinkedLevel(level: Level) {
    if (level.linkedLevelIds[level.linkedLevelIds.length - 1] == undefined &&
      level.linkedLevelIds.length > 0) {
      Alert.showError("You should fill the last level link before creating a new one!")
      return;
    }

    level.linkedLevelIds.push(undefined);

    this._levelService.svcToModify(level);
    this._levelService.toSave();
  }

  public async PopLevelLink(level: Level) {

    if (level.linkedLevelIds.length == 0) return;

    if (level.linkedLevelIds.length == 1) {
      const nextLevel = this.getNextLevel(level);
      let aux = this._reviewService.reviewResults[nextLevel?.id];
      aux?.parent_levelIds_sameArea?.pop();
      aux?.parent_levelIds?.pop();
    }
    else {
      let aux = this._reviewService?.reviewResults[level?.linkedLevelIds[level?.linkedLevelIds?.length - 1]];
      aux?.parent_levelIds_sameArea.pop();
      aux?.parent_levelIds.pop();
    }

    this.removeLastLinkedLevelLink(level);
    this.removeLinkedPathOrder(level);

    //Just decrease when have more then one branch
    if (level.linkedLevelIds.length > 1)
      this.descreaseAreaPathsAmount(level);

    this._levelService.svcToModify(level);
    this._levelService.toSave();
  }

  getNextLevel(level: Level): Level {
    return this._levelService.models[this._reviewService.reviewResults[level.linkedLevelIds[0]]?.index];
  }

  removeLastLinkedLevelLink(level: Level) {
    level.linkedLevelIds.pop();
  }

  removeLinkedPathOrder(level: Level) {
    level?.pathOrder?.pop()
    this.removeLastPathOrder(level);
  }

  removeLastPathOrder(level: Level) {
    if (level.linkedLevelIds.length == 1 && level.pathOrder.length > 0) {
      while (level.pathOrder.length > 0) {
        level.pathOrder.pop();
      }
    }
  }


  descreaseAreaPathsAmount(level: Level) {
    if (this.currentArea.pathsAmount <= 0 || level.linkedLevelIds.length == 1) return;
    this.counter--;
    this.calculateBranchesAmountByArea();
  }

  private assignHeight(id: string) {
    const levelId: string = id;
    this.levelHeights[levelId] = -1;
    this.levelBranchIndex[levelId] = -1;
    this._reviewService.reviewResults[levelId].parent_levelIds_sameArea
      .filter((lvlId) => lvlId && this.lstIds.find((lId) => lId === lvlId))
      .forEach((parentId) => {
        if (this._reviewService.reviewResults[parentId].index > this._reviewService.reviewResults[levelId].index) return;

        const heightOfParent = this.levelHeights[parentId];
        const parentChildrenIds = this._reviewService.reviewResults[parentId].child_levelIds_sameArea;
        let lIndex = parentChildrenIds.indexOf(levelId);
        lIndex = lIndex < 0 ? 0 : lIndex;

        if (this.levelHeights[levelId] === -1 || heightOfParent + lIndex < this.levelHeights[levelId]) {
          this.levelHeights[levelId] = parentChildrenIds.length > 1 ? heightOfParent === 0 && lIndex === 0 ? 0 : heightOfParent + 1 : heightOfParent;
          this.levelBranchIndex[levelId] = heightOfParent + lIndex > 0 ? parentChildrenIds.length > 1 ?
            this.levelBranchIndex[parentId] + lIndex + 1 : this.levelBranchIndex[parentId] : 0;
        }
      });
    if (this.levelHeights[levelId] === -1 || this._reviewService.reviewResults[levelId].parent_levelIds_sameArea.length === 0) {
      this.levelHeights[levelId] = 0;
      this.levelBranchIndex[levelId] = 0;
    }
  }

  public async cycleIcon(level: Level) {
    if (this._userSettingsService.data.icons.length == 0) Alert.showAlert('No Icons', 'There is no icons. Put some in the icons settings page', 'info');

    let iconIndex: number = this.getInformation(level.id)?.iconIndex;

    if (iconIndex) {
      level.iconIndex = iconIndex;
      this.setIconinformation(level.id, undefined);
    }

    iconIndex = level.iconIndex == undefined ? 0 : level.iconIndex;

    iconIndex = iconIndex >= this._userSettingsService.data.icons.length ? 0 : iconIndex + 1;
    level.iconIndex = iconIndex;

    await this._levelService.svcToModify(level);
    this.lstFetchLists();
  }

  public async toPromptAddLevelInBetween(itemAbove?: Level) {
    await this.removeUndefinedLinkedlevelWhenCreatingNewLevels();
    let newObj: Level;
    const area = this._areaService.svcFindById(this.lstFilterValue['areaId'] as string);
    const objAbove = this._levelService.models[this._reviewService.reviewResults[itemAbove.id]?.index];
    newObj = await this._levelService.svcPromptCreateNew(area.id);

    newObj["pathOrder"] = [];
    if (!newObj) return;

    await this._levelService.srvAdd(newObj);
    this._areaService.addLevel(area, newObj.id);
    await this._levelService.transferLevelLinks(objAbove, newObj);
    this.lstAppendToSearchIndex(newObj);
    this.lstFetchLists();
    this.HighlightElement(newObj.id, 100, false, undefined, true);
  }

  public async toPromptAddLevel(itemAbove?: Level) {
    await this.removeUndefinedLinkedlevelWhenCreatingNewLevels();

    let objBelow: Level = this._levelService.models[this._reviewService.reviewResults[itemAbove.id]?.index + 1];

    const area = this._areaService.svcFindById(this.lstFilterValue['areaId'] as string);
    if (!area) return;

    //Creates a level and adds it to the end of the list
    let newObj: Level;
    newObj = await this._levelService.svcPromptCreateNew(area.id);
    if (!newObj) return;

    //Not every level have the pathOrder. We add it here.
    if (!newObj?.pathOrder || !objBelow?.pathOrder) {
      newObj["pathOrder"] = [];
      objBelow["pathOrder"] = [];
    }
    this.increaseAreaPathAmount(itemAbove);


    this.lstAnchorId = newObj.id;
    await this._levelService.srvAdd(newObj);
    this._areaService.addLevel(area, newObj.id);

    this._levelService.addLevelLink(itemAbove, newObj.id, false, objBelow);


    //For more information on this section search for: if(this.verifyChildlevel), in the documentation.
    if (this.verifyChildlevelByLeveltype(itemAbove) && itemAbove.linkedLevelIds.length == 1) {
      itemAbove.pathOrder.push(-1);
      await this._levelService.svcToModify(itemAbove);

      itemAbove.pathOrder.push(this.addPathOrderWhenBranching());
      await this._levelService.svcToModify(itemAbove);
    }
    else if (itemAbove.linkedLevelIds.length == 1) {
      itemAbove.pathOrder.push(this.addPathOrderWhenBranching());
      await this._levelService.svcToModify(itemAbove);

      itemAbove.pathOrder.push(this.addPathOrderWhenBranching());
      await this._levelService.svcToModify(itemAbove);
    }
    else {
      itemAbove.pathOrder.push(this.addPathOrderWhenBranching());
      await this._levelService.svcToModify(itemAbove);
    }

    this._levelService.svcToModify(itemAbove);
    this._levelService.toSave();

    this.assignHeight(newObj.id);
    this.lstAppendToSearchIndex(newObj);
    this.lstFetchLists();
    this.HighlightElement(newObj.id, 100, true, undefined, true);
    this.getCurrentArea();
    this.lstFetchLists();
  }

  verifyChildlevelByLeveltype(level: Level): boolean {
    let childID = this._reviewService.reviewResults[level.id].child_levelIds_sameArea[0];
    let childLevel = this._levelService.svcFindById(childID);

    //For more information, search the name of the method on the documentation.
    if (childLevel.type == LevelType.MINIGAME || childLevel.type == LevelType.TRANSITION) return true;

    return false;
  }

  async removeUndefinedLinkedlevelWhenCreatingNewLevels() {
    if (!this.currentUndefinedParentLevel) return;
    if (this.currentUndefinedParentLevel.linkedLevelIds[this.currentUndefinedParentLevel.linkedLevelIds.length - 1] == undefined) {
      this.currentUndefinedParentLevel.linkedLevelIds.pop();
      this.currentUndefinedParentLevel.pathOrder.pop();
      this.decreaseAmountPaths(1);
      await this._levelService.svcToModify(this.currentUndefinedParentLevel);
    }
  }

  decreaseAmountPaths(amountToRemove: number) {
    this.counter -= amountToRemove;
    this.amountPaths = Array(this.counter).fill(0).map((x, i) => i + 1);
  }


  public async toPromptAddLinkLevel(itemAbove?: Level) {
    await this.removeUndefinedLinkedlevelWhenCreatingNewLevels();
    let objBelow = this._levelService.models[this._reviewService.reviewResults[itemAbove.id]?.index + 1];

    let newObj: Level;

    const area = this._areaService.svcFindById(this.lstFilterValue['areaId'] as string);
    if (!area) return;

    newObj = await this._levelService.svcPromptCreateNew(area.id);
    if (!newObj) return;


    //Not every level have the pathOrder. We add it here.
    if (!newObj.pathOrder || !objBelow.pathOrder) {
      newObj["pathOrder"] = []
      objBelow["pathOrder"] = []
    }

    //tranfere the first linked level from the parent to the new level
    newObj.linkedLevelIds.push(itemAbove.linkedLevelIds[0]);
    //remove the first linked level
    itemAbove.linkedLevelIds.shift();

    this.lstAnchorId = newObj.id;
    await this._levelService.srvAdd(newObj);
    this._areaService.addLevel(area, newObj.id);

    this._levelService.addLevelLink(itemAbove, newObj.id, false, objBelow, true);

    this.updatePathorderOnMinigameNTransitionLeveltype(itemAbove, newObj);

    this._levelService.svcToModify(itemAbove);
    this._levelService.toSave();

    this.assignHeight(newObj.id);
    this.lstAppendToSearchIndex(newObj);
    this.lstFetchLists();
    this.HighlightElement(newObj.id, 100, true, undefined, true);
    this.getCurrentArea();
  }

  async updatePathorderOnMinigameNTransitionLeveltype(itemAbove: Level, newObj: Level) {

    let nextLevel: Level = this._levelService.svcFindById(newObj.linkedLevelIds[0])
    if (nextLevel.type == LevelType.MINIGAME || nextLevel.type == LevelType.TRANSITION) {
      await this.increaseAreaPathAmount(itemAbove);
      itemAbove.pathOrder[0] = this.addPathOrderWhenBranching();
      this._levelService.svcToModify(itemAbove);
    }
  }

  public async toMoveItem(level: Level, transpose: number, ...levelIdsToUpdate: string[]) {
    const index = this._levelService.indexOfId(level.id);
    const otherIndex = index + transpose;
    const otherLevel = this._levelService.models[otherIndex];
    if (!otherLevel) return;

    await this._levelService.switchPlaces(index, otherIndex);
    this._levelService.svcReviewById(level.id);
    this._levelService.svcReviewById(otherLevel.id);

    this._reviewService.reviewResults[level.id].parent_levelIds_sameArea.forEach((lId) => {
      this._levelService.svcReviewById(lId);
    });

    levelIdsToUpdate.forEach((lId) => {
      this._levelService.svcReviewById(lId);
    });
    this.lstFetchLists();
    this.HighlightElement(level.id, 100, true, '#a1e3e6', true);
  }

  public override async lstPromptRemove(level: Level) {
    const remove = await Alert.showConfirm("Delete Level?", "Do you realy wants to delete this level? This action cannot be undo!", "DELETE")
    if (!remove) return;

    this.removeLevelFromAllLevelFromThisAreaList(level);
    this.removeLevelFromLinkedlevelArrayAllLevels(level);

    this._levelService.models = await this._levelService.models.filter(l => l.id != level.id);

    await this._areaService.RemoveLevel(this._areaService.svcFindById(Area.getSubIdFrom(level.id)), level.id);


    let lateLevel: Level = this.getLateLevel(level);
    if (lateLevel && lateLevel.linkedLevelIds.length > 1) {
      this.descreaseAreaPathsAmount(level);
    }

    this.lstResetHighlights();

    let levelBefore: Level = this.getParentLevel(level);
    if (levelBefore != undefined) {
      let levelIndex: number = this.findLinkedlevelIndexFromLevelbeforeUsingLevel(levelBefore, level);
      //Remove level references from levelBefore
      levelBefore = this.removeLinkedlevelByIndex(levelBefore, levelIndex);
      await this._levelService.svcToModify(levelBefore);

      //Add nextLevel references to levelBefore
      if (level?.linkedLevelIds.length > 1) {
        levelBefore = this.insertLinkedlevelByIndex(levelBefore, level, levelIndex);
        this.removeLastPathOrder(levelBefore);
        await this._levelService.svcToModify(levelBefore);
      }
    }
    this.removeStoryExpansion(level);
    this.removeSpeechesAndOptionsInsideLevel(level);
    this._eventService.removeIdEventPkg(level.id);
    this._storyboxService.removeIdStoryboxPkg(level.id);
    this._userSettingsService.removeIdObjectInformations(level.id);
    this._optionboxService.removeIdsOptionboxPkg(level.id);
    this._optionService.removeIdsOptionPkg(level.id);
    this._dilemmaBoxService.removeIdsDilemmaBoxPkg(level.id);
    this._dialogueService.removeIdDialoguePkg(level.id);
    this._roadBlockService.removeIdsOptionPkg(level.id);
    this.updateOutdatedPathorder();
    this._levelService.svcReviewAll();
    this.lstFetchLists(true);
    this.UpdateHeights();
  }

  async removeStoryExpansion(level: Level) {
    for (let i = 0; i < this._storyExpansionService.models.length; i++)
      if (this._storyExpansionService.models[i].level == level.id)
        await this._storyExpansionService.svcToRemove(this._storyExpansionService.models[i].id);
  }

  async removeSpeechesAndOptionsInsideLevel(level: Level) {
    for (let i = 0; i < this._speechService.models.length; i++) {
      if (this._speechService.models[i].id.split('.D')[0].trim() == level.id.trim()) {
        await this._speechService.svcToRemove(this._speechService.models[i].id);
        i = i - 1;//When remove the array update the size. We repeat the last index.
      }
    }

    for (let i = 0; i < this._optionService.models.length; i++) {
      if (this._optionService.models[i].id.split('.D')[0].trim() == level.id.trim()) {
        await this._optionService.svcToRemove(this._optionService.models[i].id);
        i = i - 1;
      }
    }
  }

  async updateOutdatedPathorder() {
    for (let i = 0; i < this.allLevelIdsFromAreaAndSubarea.length; i++) {
      let level = this._levelService.svcFindById(this.allLevelIdsFromAreaAndSubarea[i]);
      for (let j = 0; j < level?.pathOrder.length; j++) {
        if (level?.pathOrder[j] > this.amountPaths.length) {
          level.pathOrder.splice(j, 1);
          level.pathOrder.splice(j, 0, this.noRepeatedEntryForPathOrder(level));
          await this._levelService.svcToModify(level);
        }
      }
    }
  }

  async removeLevelFromAllLevelFromThisAreaList(level: Level) {
    if (level?.pathOrder.length == 1) {

      if (this.hasLinkedLevelIdsContainLevelid(this.getLateLevel(level))) {
        this.counter--;
      }
      else return;
    }
    else {
      for (let i = 0; i < this.allLevelIdsFromAreaAndSubarea.length; i++) {
        if (level.id == this.allLevelIdsFromAreaAndSubarea[i]) {
          this.counter -= level.pathOrder.length;
          break;
        }
      }
    }

    this.amountPaths = await Array(this.counter).fill(0).map((x, i) => i + 1);
  }

  removeLevelFromLinkedlevelArrayAllLevels(level: Level) {
    let canBreak: boolean = false;
    for (let i = 0; i < this._levelService.models.length; i++) {
      for (let j = 0; j < this._levelService.models[i].linkedLevelIds.length; j++) {
        if (this._levelService.models[i].linkedLevelIds[j] == level.id) {
          this._levelService.models[i].linkedLevelIds.splice(j, 1);
          canBreak = true;
          break;
        }
      }
      if (canBreak) break;
    }
  }

  hasLinkedLevelIdsContainLevelid(level: Level): boolean {
    for (let i = 0; i < level?.linkedLevelIds.length; i++) {
      if (level?.linkedLevelIds[i] == level.id) {
        return true;
      }
    }
    return false;
  }

  insertLinkedlevelByIndex(levelBefore: Level, deletedLevel: Level, index: number): Level {
    levelBefore?.linkedLevelIds.splice(index, 0, deletedLevel?.linkedLevelIds[0]);
    return levelBefore;
  }

  insertPathorderlByIndex(levelBefore: Level, deletedLevel: Level, index: number): Level {
    levelBefore?.pathOrder.splice(index, 0, deletedLevel?.pathOrder[0]);
    return levelBefore;
  }

  findLinkedlevelIndexFromLevelbeforeUsingLevel(levelBefore: Level, deletedLevel: Level): number {
    let index;
    for (let i = 1; i <= levelBefore?.linkedLevelIds.length; i++) {
      if (levelBefore?.linkedLevelIds[i] == deletedLevel.id) {
        index = i;
        break;
      }
    }
    return index;
  }

  removeLinkedlevelByIndex(levelBefore: Level, index: number): Level {
    levelBefore?.linkedLevelIds.splice(index, 1);
    return levelBefore;
  }

  removePathOrderByIndex(level: Level, index: number): Level {
    level?.pathOrder.splice(index, 1);
    return level;
  }

  //It remove the child line between two levels.
  removeChildLinkedLevel(childLevel: Level) {
    this._reviewService?.reviewResults[childLevel.id]?.child_levelIds_sameArea.pop();
  }

  //It remove the parent line between two levels.
  removeParentLinkedLevel(parentLevel: Level) {
    this._reviewService?.reviewResults[parentLevel.id]?.parent_levelIds_sameArea.pop();
  }

  //It creates a line between two levels
  connectChildLinkedLevel(lateLevel: Level, levelBefore: Level) {
    this._reviewService?.reviewResults[lateLevel.id]?.child_levelIds_sameArea.push(levelBefore.id);
  }

  //It creates a line between two levels
  connectParentLinkedLevel(lateLevel: Level, levelBefore: Level) {
    this._reviewService?.reviewResults[lateLevel.id]?.parent_levelIds_sameArea.push(levelBefore.id);
  }

  getBeforeLevel(level: Level): Level {
    return this._levelService.models[this._reviewService.reviewResults[level.id].index - 1];
  }
  getLateLevel(level: Level): Level {
    return this._levelService.models[this._reviewService.reviewResults[level.id].index + 1];
  }

  getParentLevel(level: Level): Level {
    let parentLevel: Level = undefined;
    for (let i = 0; i < this._levelService.models.length; i++) {
      for (let j = 0; j < this._levelService.models[i]?.linkedLevelIds.length; j++) {
        if (this._levelService.models[i]?.linkedLevelIds[j] == level.id) {
          parentLevel = this._levelService.models[i];
          break;
        }
      }
    }
    return parentLevel;
  }

  public isLevelTranslated(level: Level) {
    return this._translationService.checkTranslation(level.id, 'EN-US');
  }


  changePathorderDependentFromEncounterType(level: Level) {
    let parentID = this._reviewService.reviewResults[level.id].parent_levelIds_sameArea[0];
    let parentLevel = this._levelService.svcFindById(parentID);
    let levelIndexOnParentLinkedlevelList = 0;

    for (let i = 0; i < parentLevel.linkedLevelIds.length; i++) {
      if (parentLevel.linkedLevelIds[i] == level.id) {
        levelIndexOnParentLinkedlevelList = i + 1;
        break;
      }
    }
    //For more information, search the name of the method on the documentation.
    if (level.type == LevelType.MINIGAME || level.type == LevelType.TRANSITION) {
      parentLevel.pathOrder.splice(levelIndexOnParentLinkedlevelList - 1, 1, -1);
      this.counter -= 1;
      this.amountPaths = Array(this.counter).fill(0).map((x, i) => i + 1);

    }
    else {
      this.counter += 1;
      this.amountPaths = Array(this.counter).fill(0).map((x, i) => i + 1);
      parentLevel.pathOrder.splice(levelIndexOnParentLinkedlevelList - 1, 1, this.noRepeatedEntryForPathOrder(parentLevel));
    }

    this._levelService.svcToModify(parentLevel);
  }

  search(term: string) {
    this.lstIds = [];
    for (let i = 0; i < this.tempLevelsIds?.length; i++) {
      let l: Level = this._levelService.svcCloneById(this.tempLevelsIds[i]);
      for (let j = 0; j < l.battleCharacterIds.length; j++) {
        let char: Character = this._characterService.svcFindById(l.battleCharacterIds[j]);
        if (char.name.toUpperCase().includes(term.toUpperCase())) {
          this.lstIds.push(l.id);
        }
      }

      if (this._userSettingsService.data.objectInformations[l.id]?.authorNotes?.toUpperCase().includes(term.toUpperCase())) {
        this.lstIds.push(l.id);
      }

      for (let j = 0; j < l.speakerIds.length; j++) {
        let char: Character = this._characterService.svcFindById(l.speakerIds[j]);
        if (char.name.toUpperCase().includes(term.toUpperCase())) {
          this.lstIds.push(l.id);
        }
      }
      if (l?.description?.toUpperCase().includes(term.toUpperCase()) ||
        l?.name?.toUpperCase().includes(term.toUpperCase())) {
        this.lstIds.push(l.id);
      }
    }
    if (!term) this.lstIds = this.tempLevelsIds;
    this.listDescription = "Showing " + this.lstIds?.length + " results";
  }

  changeName(level: Level, fieldName, value:string) {
    level.isReviewedName = false;
    level.revisionCounterNameAI = 0;
    this.lstOnChange(level, fieldName, value);
  }
  
  changeDescription(level: Level, fieldName, value:string) {
    level.isReviewedDescription = false;
    level.revisionCounterDescriptionAI = 0;
    this.lstOnChange(level, fieldName, value);
  }

}
