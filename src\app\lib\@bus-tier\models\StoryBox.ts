import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';

export class StoryBox
  extends Base<Data.Hard.IStoryBox, Data.Result.IStoryBox>
  implements Required<Data.Hard.IStoryBox> {
  static generateId(parentId: string, index?: number): string {
    return (
      parentId + '.' + IdPrefixes.STORYBOX + index /*(index === undefined ? '' : index)*/
    );
  }
  constructor(
    index: number,
    parentId: string,
    dataAccess: StoryBox['TDataAccess']
  ) {

    super(
      {
        hard: {
          id: StoryBox.generateId(parentId, index),
          storyProgressIds: []
        },
      },
      dataAccess
    );
  }
  protected getInternalFetch() {
    return {};
  }
  public get storyProgressIds(): string[] {
    return this.hard.storyProgressIds;
  }
  public set storyProgressIds(value: string[]) {
    this.hard.storyProgressIds = value;
  }
  public get label(): string {
    return this.hard.label;
  }
  public set label(value: string) {
    this.hard.label = value;
  }
  public get labelOption(): string {
    return this.hard.labelOption;
  }
  public set labelOption(value: string) {
    this.hard.labelOption = value;
  }
  public get choiceNegative(): string {
    return this.hard.choiceNegative;
  }
  public set choiceNegative(value: string) {
    this.hard.choiceNegative = value;
  }
  public get choicePositive(): string {
    return this.hard.choicePositive;
  }
  public set choicePositive(value: string) {
    this.hard.choicePositive = value;
  }
  public get investigationPositive(): string {
    return this.hard.investigationPositive;
  }
  public set investigationPositive(value: string) {
    this.hard.investigationPositive = value;
  }
  public get investigationNegative(): string {
    return this.hard.investigationNegative;
  }
  public set investigationNegative(value: string) {
    this.hard.investigationNegative = value;
  }
  public get AndOrCondition(): string {
    return this.hard.AndOrCondition;
  }
  public set AndOrCondition(value: string) {
    this.hard.AndOrCondition = value;
  }
  public get messageOption(): string {
    return this.hard.messageOption;
  }
  public set messageOption(value: string) {
    this.hard.messageOption = value;
  }
  public get descriptionDCGuideOption(): string {
    return this.hard.descriptionDCGuideOption;
  }
  public set descriptionDCGuideOption(value: string) {
    this.hard.descriptionDCGuideOption = value;
  }
  public get subContextDescription(): string {
    return this.hard.subContextDescription;
  }
  public set subContextDescription(value: string) {
    this.hard.subContextDescription = value;
  }
  public get subcontext(): string {
    return this.hard.subcontext;
  }
  public set subcontext(value: string) {
    this.hard.subcontext = value;
  }
  public get resultDCOption(): number {
    return this.hard.resultDCOption;
  }
  public set resultDCOption(value: number) {
    this.hard.resultDCOption = value;
  }
  public get nameKnowledge(): string {
    return this.hard.nameKnowledge;
  }
  public set nameKnowledge(value: string) {
    this.hard.nameKnowledge = value;
  }
  public get descriptionKnowledge(): string {
    return this.hard.descriptionKnowledge;
  }
  public set descriptionKnowledge(value: string) {
    this.hard.descriptionKnowledge = value;
  }

  public get type(): number {
    return this.hard.type;
  }
  public set type(value: number) {
    this.hard.type = value;
  }
  public get fatorSituationModifier(): string {
    return this.hard.fatorSituationModifier;
  }
  public set fatorSituationModifier(value: string) {
    this.hard.fatorSituationModifier = value;
  }
  public get valueSituationModifier(): number {
    return this.hard.valueSituationModifier;
  }
  public set valueSituationModifier(value: number) {
    this.hard.valueSituationModifier = value;
  }
  public get descriptionSituationalModifier(): string {
    return this.hard.descriptionSituationalModifier;
  }
  public set descriptionSituationalModifier(value: string) {
    this.hard.descriptionSituationalModifier = value;
  }

}
