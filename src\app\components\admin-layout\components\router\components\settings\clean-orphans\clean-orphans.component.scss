.main-div {
  padding: 0 15px;
}

.card-container 
{
    display: flex;
    flex-direction: column;
    align-items: center;
  
    .card 
    {
      border: 1px solid #ccc;
      padding: 15px;
      margin: 5px;
      width: 50vw;
      opacity: 1;
      transition: opacity 0.5s ease-in-out;
    }
   
}

.list-header-orphans {
  display: flex;
  justify-content: space-around;
}

.list-orphans {
  width: 580px !important; 
  padding-top: 20px; 
  margin-left: 20px;
}

.noEmpty {
  text-align: left !important; 
  color: red;
}

table {
  width: 100%;
}

th, td {
  padding: 5px;
  text-align: center;
}
th {
  background-color: #333;
  color: white; 
  font-weight: 400 !important; 
}

th[colspan] {
  background-color: #444;
}
.header-title {
  background-color: #555;
  color: white;
}

.trBC {
  background-color: #595959 !important;
}

.bc {
  background-color: white;
}

.btn-removeAll {
  position: relative;
  float: right;
  margin-right: 10px;
}

.tr-clickable:hover {
  background-color: rgb(194, 194, 194) !important;
}
    
.titleCategory{
  display: flex; 
  justify-content: center; 
  font-weight: 600; 
}


#customers {
  font-family: Arial, Helvetica, sans-serif;
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 20px;
}

#customers td, #customers th {
  border: 1px solid #ddd;
  padding: 8px;
}

#customers tr:nth-child(even){background-color: #f2f2f2;}

#customers tr:hover {background-color: #ddd;}

#customers th {
  padding-top: 12px;
  padding-bottom: 12px;
  text-align: left;
  background-color: #04AA6D;
  color: white;
}


