import { Component } from '@angular/core';
import { language } from 'src/lib/darkcloud/dialogue-system/game-types';
import * as XLSX from 'xlsx';
import { IListUnique } from '../../../../../../../../lib/darkcloud/angular/dsadmin/v9/data/hard/IUniqueCharactere';
import { IListArea } from '../../../../../../../../lib/darkcloud/angular/dsadmin/v9/data/hard/IUniqueCharactersByHCAndBL';
import { LevelType } from '../../../../../../../../lib/darkcloud/dialogue-system';
import {
  Area,
  Character,
  TotalArchetypes,
  UniqueCharactersByHCAndBL,
} from '../../../../../../../lib/@bus-tier/models';
import { BattleUpgrade } from '../../../../../../../lib/@bus-tier/models/BattleUpgrade';
import { LevelPoints } from '../../../../../../../lib/@bus-tier/models/LevelPoints';
import { UniqueCharactere } from '../../../../../../../lib/@bus-tier/models/UniqueCharactere';
import { Button } from '../../../../../../../lib/@pres-tier/data';
import {
  AreaService,
  BattleUpgradeService,
  CharacterService,
  LevelService,
  TierService,
  TotalArchetypesService,
  UniqueCharactersByHCAndBLService,
} from '../../../../../../../services';
import { LevelPointsService } from '../../../../../../../services/levelPoints.service';
import { UniqueCharactereService } from '../../../../../../../services/uniqueCharacteres.service';
import { ListHC } from './../../../../../../../../lib/darkcloud/angular/dsadmin/v9/data/hard/ILevelPoints';


interface CharacterHCBL {
  areaId: string;
  bl: string;
  idCharacter: string;
  indexBL: string;
  nameArea: string;
  nameCharacter: string;
  orderArea: string;
  rarity: string;
  rarityId: string;
  totalHCBL: number;
}

interface QuantityHCBL {
  rarity: string;
  rarityId: string;
  listHC: IListArea[];
}

@Component({
  selector: 'app-reports-hcexcel',
  templateUrl: './reports-hcexcel.component.html',
  styleUrls: ['./reports-hcexcel.component.scss'],
})
export class ReportsHCExcelComponent {
  public readonly exportExcelButtonTemplate: Button.Templateable = {
    btnClass: [...Button.Klasses.FILL_BLUE, 'Excel'],
    title: 'Export to Excel',
    onClick: this.downloadAsExcel.bind(this),
    iconClass: 'pe-7s-cloud-download',
  };

  weaponRarityNames = [];
  orderAreaList = [];
  allCharacters: Character[] = [];
  characteresRarity: Character[] = [];
  description: string;
  listOrderArea: Area[];
  conditionFields: string[] = ['Minion', 'Boss'];
  multiplyArea = [];
  charactersList = [];
  areaListHCBL = [];

  nonExistentItems: CharacterHCBL[] = [];
  quantityPosition: QuantityHCBL[] = [];
  countMinionLevel = 0;
  countBossLevel = 0;
  listLevelPoints: LevelPoints[];
  listCicleLevelMininon: ListHC;
  listCicleLevelBoss: ListHC;
  ListTotalUnique: UniqueCharactere[];
  duplicatesArray: { uniqueTotal: number; idDuplic: string }[] = [];
  public language: language = 'PT-BR';
  sortNameOrder = +1;
  battleList: BattleUpgrade[] = [];
  listUniqueByHCAndBL: UniqueCharactersByHCAndBL[] = [];
  totalListArchetypes: TotalArchetypes[] = [];

  constructor(
    private _tierListService: TierService,
    private _areaService: AreaService,
    private _levelService: LevelService,
    private _levelPointsService: LevelPointsService,
    private _uniqueCharactereService: UniqueCharactereService,
    private _characterService: CharacterService,
    private _uniqueCharactersByHCAndBLService: UniqueCharactersByHCAndBLService,
    private _battleUpgradeService: BattleUpgradeService,
    private _totalArchetypesService: TotalArchetypesService
  ) {}

  async ngOnInit(): Promise<void> {
    // Aguarde o carregamento de todos os serviços
    await Promise.all([
      this._tierListService.toFinishLoading(),
      this._areaService.toFinishLoading(),
      this._levelPointsService.toFinishLoading(),
      this._uniqueCharactereService.toFinishLoading(),
      this._characterService.toFinishLoading(),
      this._battleUpgradeService.toFinishLoading(),
      this._totalArchetypesService.toFinishLoading(),
    ]);
    this.totalListArchetypes = this._totalArchetypesService.models;
    // Processa os dados após garantir que todos estão carregados
    this.weaponRarityNames = this._tierListService.models.filter(
      (tier) => tier.selectDrop === 'Character Rarity'
    );
    this.listOrderArea = this._areaService.models.filter(
      (x) => x.order.toString() !== ''
    );
    //this.ListTotalUnique = this._uniqueCharactereService.models;

    // Chamar métodos que dependem de dados carregados
    this.createdInspirationPoints();
    this.getDataArea();
    this.removeAreaDuplicate();
    this.lineupOrderHC();
    this.orderListCicleLevel();
    this.orderListevelPoints();

    // Finalize processando dados que dependem de personagens
    this.createdUniqueCaractersByHCAndBL();
    
  }

  removeAreaDuplicate() {
    //Remove order duplicados
    this.orderAreaList = this.listOrderArea.filter(
      (item, index, self) =>
        index === self.findIndex((t) => t.order === item.order)
    );
  }

  lineupOrderHC() {
    this.sortNameOrder *= +1;
    this.orderAreaList.sort((a, b) => {
      return this.sortNameOrder * a.order.localeCompare(b.order);
    });
  }

  orderListCicleLevel() {
    this.sortNameOrder *= +1;
    for (let index = 0; index < this.ListTotalUnique.length; index++) {
      this.ListTotalUnique[index].listCicleLevel.sort((a, b) => {
        return this.sortNameOrder * a.orderCicle.localeCompare(b.orderCicle);
      });
    }
  }

  isCharacterTypeBossMinionSubboss = (character: Character): boolean =>
    character.type == 3 || character.type == 4 || character.type == 2;

  //TOTAL UNIQUE CHARACTERES
  createdInspirationPoints() {
    // Inicializa e limpa a lista de modelos do serviço
    /*
    this._uniqueCharactereService.models = this._uniqueCharactereService.models = [];
    this._uniqueCharactereService.toSave();
    */

    this.characteresRarity = [];

    // Cria base de dados Unique Charactere
    this.weaponRarityNames.forEach((x) => {
      const unique = this._uniqueCharactereService.createNewUniqueCharactere(x);
    });

    this.ListTotalUnique = this._uniqueCharactereService.models;

    this.getCompare();

    // Pega os dados dos personagens
    this.allCharacters = this._characterService.models.filter(
      this.isCharacterTypeBossMinionSubboss
    );

    // Inicializa listCicleLevel em ListTotalUnique
    const listDataCicleTemplate: IListUnique[] = this.listOrderArea.map(
      (area) => ({
        idCicle: area.id.toString(),
        orderCicle: area.order.toString(),
        nameCicle: area.name.toString(),
        totalUniqueCharacteres: 0, // Inicializa o contador
      })
    );

    // Preencher os ciclos para cada item da ListTotalUnique
    this.ListTotalUnique.forEach((uniqueItem) => {
      uniqueItem.listCicleLevel = JSON.parse(
        JSON.stringify(listDataCicleTemplate)
      );
    });

    // Atualizar totalUniqueCharacteres
    this.allCharacters.forEach((character) => {
      let characterAreaId;
      if (character?.areaId !== undefined) {
        characterAreaId = character.areaId.toString();
      }

      const characterRarityId = character.rarityId;

      this.ListTotalUnique.forEach((uniqueItem) => {
        if (uniqueItem.idRarity === characterRarityId) {
          uniqueItem.listCicleLevel.filter((x) => {
            if (x.idCicle === characterAreaId) {
              x.totalUniqueCharacteres += 1;
            }
          });
          this._uniqueCharactereService.svcToModify(uniqueItem);
        }
      });
    });

    for (let i = 0; i < this.ListTotalUnique.length; i++) {
      const uniqueItem = this.ListTotalUnique[i];
      const listCicleLevel = uniqueItem.listCicleLevel;

      // Mapa para armazenar as somas dos totalUniqueCharacteres por orderCicle
      const orderCicleMap = new Map<string, number>();

      // Primeiro, percorra todos os itens e some os totalUniqueCharacteres para orderCicles iguais
      for (let j = 0; j < listCicleLevel.length; j++) {
        const cicleLevel = listCicleLevel[j];
        const orderCicle = cicleLevel.orderCicle;
        const totalUniqueCharacteres = cicleLevel.totalUniqueCharacteres;

        // Se já existe esse orderCicle no mapa, acumule o totalUniqueCharacteres
        if (orderCicleMap.has(orderCicle)) {
          const somaAtual = orderCicleMap.get(orderCicle) || 0;
          orderCicleMap.set(orderCicle, somaAtual + totalUniqueCharacteres);
        } else {
          // Se não existe, adicione o orderCicle no mapa com o valor atual de totalUniqueCharacteres
          orderCicleMap.set(orderCicle, totalUniqueCharacteres);
        }
      }

      // Agora, percorra novamente e atualize os valores de totalUniqueCharacteres
      const updatedCicleLevel = [];
      const seenOrderCicles = new Set<string>(); // Para rastrear quais orderCicles já foram processados

      for (let j = 0; j < listCicleLevel.length; j++) {
        const cicleLevel = listCicleLevel[j];
        const orderCicle = cicleLevel.orderCicle;

        // Se este orderCicle ainda não foi adicionado na lista final (para evitar duplicatas)
        if (!seenOrderCicles.has(orderCicle)) {
          // Atualiza o totalUniqueCharacteres com a soma acumulada no mapa
          cicleLevel.totalUniqueCharacteres =
            orderCicleMap.get(orderCicle) || cicleLevel.totalUniqueCharacteres;
          updatedCicleLevel.push(cicleLevel);

          seenOrderCicles.add(orderCicle);
        }
      }

      // Atualiza o array listCicleLevel com os itens não duplicados
      this.ListTotalUnique[i].listCicleLevel = updatedCicleLevel;
      this._uniqueCharactereService.svcToModify(this.ListTotalUnique[i]);
    }
  }

  // Verifica se houve alteração no nome do Rarity
  getCompare() {
    this._uniqueCharactereService.toFinishLoading();
    const listCharacters = this._uniqueCharactereService.models;

    if (listCharacters.length > 0) {
      this.weaponRarityNames.forEach((x) => {
        for (let index = 0; index < listCharacters.length; index++) {
          if (
            x.id === listCharacters[index].idRarity &&
            x.name !== listCharacters[index].nameCharacterRarity
          ) {
            listCharacters[index].nameCharacterRarity = x.name;
            this._uniqueCharactereService.svcToModify(listCharacters[index]);
          }
        }
      });
    }
  }

  //TOTAL DE LEVEL POINTS (LP)
  getDataArea() {
    this.listLevelPoints = this._levelPointsService.models;
    /*
        this._levelPointsService.models = [];
        this._levelPointsService.toSave();
        */

    this.listLevelPoints[0].listCicleLevel = [];
    this.listLevelPoints[1].listCicleLevel = [];

    for (let index = 0; index < this.listOrderArea.length; index++) {
      this.countMinionLevel = 0;
      this.countBossLevel = 0;

      const levels = this._levelService.svcFilterByIds(
        this.listOrderArea[index].levelIds
      );
      levels.forEach((level) => {
        if (level.type == LevelType.MINION) {
          this.countMinionLevel++;
        } else if (level.type == LevelType.BOSS) {
          this.countBossLevel++;
        }
      });

      const listCicle0: ListHC = {
        nameCicle: this.listOrderArea[index].name,
        orderCicle: this.listOrderArea[index].order.toString(),
        totalLevelPoint: this.countMinionLevel,
      };
      this.listLevelPoints[0].listCicleLevel.push(listCicle0);

      const listCicle1: ListHC = {
        nameCicle: this.listOrderArea[index].name,
        orderCicle: this.listOrderArea[index].order.toString(),
        totalLevelPoint: this.countBossLevel,
      };
      this.listLevelPoints[1].listCicleLevel.push(listCicle1);
    }

    //Soma os totalLevelPoint por orderCicle
    for (let i = 0; i < this.listLevelPoints.length; i++) {
      const item = this.listLevelPoints[i];
      const listCicleLevel = item.listCicleLevel;

      // Mapa para armazenar as somas dos totalLevelPoint por orderCicle
      const orderCicleMap = new Map<string, number>();

      // Primeiro passo: somar os totalLevelPoint de orderCicles iguais
      for (let j = 0; j < listCicleLevel.length; j++) {
        const cicleLevel = listCicleLevel[j];
        const orderCicle = cicleLevel.orderCicle;
        const totalLevelPoint = cicleLevel.totalLevelPoint;

        // Se já existe esse orderCicle no mapa, acumula o totalLevelPoint
        if (orderCicleMap.has(orderCicle)) {
          const somaAtual = orderCicleMap.get(orderCicle) || 0;
          orderCicleMap.set(orderCicle, somaAtual + totalLevelPoint);
        } else {
          // Se não existe, adiciona o orderCicle no mapa com o valor inicial
          orderCicleMap.set(orderCicle, totalLevelPoint);
        }
      }

      // Segundo passo: atualizar os totalLevelPoint e remover duplicatas
      const updatedCicleLevel = [];

      for (let j = 0; j < listCicleLevel.length; j++) {
        const cicleLevel = listCicleLevel[j];
        const orderCicle = cicleLevel.orderCicle;

        // Verifica se o orderCicle já foi processado
        if (!updatedCicleLevel.find((x) => x.orderCicle === orderCicle)) {
          // Atualiza o totalLevelPoint com a soma acumulada
          const pointsTotal =
            orderCicleMap.get(orderCicle) || cicleLevel.totalLevelPoint;
          cicleLevel.totalLevelPoint = pointsTotal;

          updatedCicleLevel.push(cicleLevel);
        }
      }

      this.listLevelPoints[i].listCicleLevel = updatedCicleLevel;
      this._levelPointsService.svcToModify(this.listLevelPoints[i]);
    }
  }

  orderListevelPoints() {
    this.sortNameOrder *= +1;
    for (let index = 0; index < this.listLevelPoints.length; index++) {
      this.listLevelPoints[index].listCicleLevel.sort((a, b) => {
        return this.sortNameOrder * a.orderCicle.localeCompare(b.orderCicle);
      });
    }
  }

  public downloadAsExcel() {
    // 1º Tabela: TOTAL UNIQUE CHARACTERES
    const characterTable = document.createElement('table');
    const tHead = characterTable.createTHead();
    const tHeadRow1 = tHead.insertRow();
    const tHeadRow2 = tHead.insertRow();

    // Cabeçalho superior "TOTAL UNIQUE CHARACTERES"
    const thHeader = tHeadRow1.insertCell();
    thHeader.colSpan = this.orderAreaList.length + 1;
    thHeader.innerText = 'TOTAL UNIQUE CHARACTERES';

    // Cabeçalho das ordens "Caracter Rarity"
    tHeadRow2.insertCell().innerText = 'Caracter Rarity';
    this.orderAreaList.forEach(
      (x) => (tHeadRow2.insertCell().innerText = x.order.toString())
    );

    const tBody = characterTable.createTBody();

    // Preenchendo os dados da primeira tabela
    this.ListTotalUnique.forEach((unique) => {
      const tBodyRow = tBody.insertRow();
      tBodyRow.insertCell().innerText = unique.nameCharacterRarity;
      unique.listCicleLevel.forEach((order) => {
        tBodyRow.insertCell().innerText = order.totalUniqueCharacteres
          ? order.totalUniqueCharacteres.toString()
          : '0';
      });
    });

    // 2º Tabela: TOTAL DE LEVEL POINTS (LP)
    const levelTable = document.createElement('table');
    const tHeadLevel = levelTable.createTHead();
    const tHeadLevelRow1 = tHeadLevel.insertRow();
    const tHeadLevelRow2 = tHeadLevel.insertRow();

    // Cabeçalho superior "TOTAL DE LEVEL POINTS (LP)"
    const thLevelHeader = tHeadLevelRow1.insertCell();
    thLevelHeader.colSpan = this.orderAreaList.length + 1;
    thLevelHeader.innerText = 'TOTAL DE LEVEL POINTS (LP)';

    // Cabeçalho das ordens "Minion/Boss"
    tHeadLevelRow2.insertCell().innerText = '';
    this.orderAreaList.forEach(
      (x) => (tHeadLevelRow2.insertCell().innerText = x.order.toString())
    );

    const tBodyLevel = levelTable.createTBody();

    // Preenchendo os dados da segunda tabela
    this.listLevelPoints.forEach((level) => {
      const tBodyLevelRow = tBodyLevel.insertRow();
      tBodyLevelRow.insertCell().innerText = level.nameTypeLevel;
      level.listCicleLevel.forEach((point) => {
        tBodyLevelRow.insertCell().innerText = point.totalLevelPoint
          ? point.totalLevelPoint.toString()
          : '0';
      });
    });

    // 3º Tabela: UNIQUE CHARACTERES BY HC AND BL
    const hcBLlTable = document.createElement('table');
    const tHeadHcBl = hcBLlTable.createTHead();
    const tHeadHcBlRow1 = tHeadHcBl.insertRow();
    const tHeadHcBlRow2 = tHeadHcBl.insertRow();
    const tHeadHcBlRow3 = tHeadHcBl.insertRow();

    // Cabeçalho superior "UNIQUE CHARACTERES BY HC AND BL"
    const hHcBlHeader = tHeadHcBlRow1.insertCell();
    hHcBlHeader.colSpan = this.multiplyArea.length + 1;
    hHcBlHeader.innerText = 'UNIQUE CHARACTERES BY HC AND BL';

    // Cabeçalho das ordens "HC"
    tHeadHcBlRow2.insertCell().innerText = 'HC';
    this.multiplyArea.forEach(
      (x) => (tHeadHcBlRow2.insertCell().innerText = x.orderArea)
    );

    // Cabeçalho das ordens "BL"
    tHeadHcBlRow3.insertCell().innerText = 'BL';
    for (let index = 0; index < this.multiplyArea.length; index++) {
      const op = index + 0;
      tHeadHcBlRow3.insertCell().innerText = op.toString();
    }

    const tBodyHcBl = hcBLlTable.createTBody();

    // Preenchendo os dados da 3ª tabela
    this.listUniqueByHCAndBL.forEach((unique) => {
      const tBodyRowHCBL = tBodyHcBl.insertRow();
      tBodyRowHCBL.insertCell().innerText = unique.nameRarity;

      // Cria um array para armazenar os valores correspondentes aos índices
      const rowValues = Array(this.multiplyArea.length).fill('');

      unique.hcList.forEach((order) => {
        if (order.indexBL >= 0 && order.indexBL < this.multiplyArea.length) {
          rowValues[order.indexBL] = order.totalHCBL.toString();
        }
      });

      // Insere as células na linha de acordo com os índices
      rowValues.forEach((value) => {
        tBodyRowHCBL.insertCell().innerText = value;
      });
    });

    // 4º Tabela: Not Compatible
    const bLTable = document.createElement('table');
    const theadBl = bLTable.createTHead();
    const theadBlRow1 = theadBl.insertRow();
    const theadBlRow2 = theadBl.insertRow();

    // Cabeçalho superior "Not Compatible"
    const thHcBlHeader = theadBlRow1.insertCell();
    thHcBlHeader.colSpan = this.nonExistentItems.length + 1;
    thHcBlHeader.innerText = 'Not Compatible';

    // Cabeçalho das ordens "Not Compatible"
    theadBlRow2.insertCell().innerText = 'Ordem';
    theadBlRow2.insertCell().innerText = 'Name';
    theadBlRow2.insertCell().innerText = 'HC';
    theadBlRow2.insertCell().innerText = 'BL';
    theadBlRow2.insertCell().innerText = 'Area';
    theadBlRow2.insertCell().innerText = 'Raraity';

    const tBodyBl = bLTable.createTBody(); // Crie o corpo da tabela bLTable

    // Preenchendo os dados da quarta tabela
    for (let index = 0; index < this.nonExistentItems.length; index++) {
      const tBodyBLRow = tBodyBl.insertRow();
      tBodyBLRow.insertCell().innerText = index.toString();
      tBodyBLRow.insertCell().innerText = this.nonExistentItems[index]
        .nameCharacter
        ? this.nonExistentItems[index].nameCharacter
        : '';
      tBodyBLRow.insertCell().innerText = this.nonExistentItems[index].orderArea
        ? this.nonExistentItems[index].orderArea
        : '';
      tBodyBLRow.insertCell().innerText = this.nonExistentItems[index].bl
        ? this.nonExistentItems[index].bl
        : '';
      tBodyBLRow.insertCell().innerText = this.nonExistentItems[index].nameArea
        ? this.nonExistentItems[index].nameArea
        : '';
      tBodyBLRow.insertCell().innerText = this.nonExistentItems[index].rarity
        ? this.nonExistentItems[index].rarity
        : '';
    }

    levelTable.appendChild(bLTable); // Adicione a tabela bLTable ao corpo da tabela levelTable

    // 5º Tabela: TOTAL ARCHETYPES
    const archetypesTable = document.createElement('table');
    archetypesTable.setAttribute('border', '1'); // Adiciona borda para melhor visualização

    const tHeadArchetypes = archetypesTable.createTHead();
    const tHeadArchetypesRow1 = tHeadArchetypes.insertRow();
    const tHeadArchetypesRow2 = tHeadArchetypes.insertRow();

    // Cabeçalho superior "TOTAL ARCHETYPES"
    const thArchetypesHeader = tHeadArchetypesRow1.insertCell();
    thArchetypesHeader.colSpan = this.orderAreaList.length + 2; // +2 para incluir a coluna 'Total'
    thArchetypesHeader.innerText = 'TOTAL ARCHETYPES';

    // Criando a linha de cabeçalho com "ARCHETYPES" e "Total"
    tHeadArchetypesRow2.insertCell().innerText = 'ARCHETYPES';
    tHeadArchetypesRow2.insertCell().innerText = 'Total'; // Adicionando a nova coluna "Total"

    // Criando as colunas de orderAreaList
    this.orderAreaList.forEach((x) => {
      const th = tHeadArchetypesRow2.insertCell();
      th.innerText = x.order.toString();
    });

  // Criando o corpo da tabela
  const tBodyARCHETYPES = archetypesTable.createTBody();

// Criando as linhas normais primeiro
this.totalListArchetypes.forEach((unique, index) => {
  const tBodyRowArch = tBodyARCHETYPES.insertRow();

  // Coluna "ARCHETYPES"
  const archetypeCell = tBodyRowArch.insertCell();
  archetypeCell.innerText = unique.nameArchetype !== undefined ? unique.nameArchetype : '';

  // Coluna "Total"
  const totalCell = tBodyRowArch.insertCell();
  totalCell.innerText = unique.total_line !== undefined ? unique.total_line.toString() : '';

  // Colunas de valores orderAreaList
  this.orderAreaList.forEach((orderItem) => {
    const cell = tBodyRowArch.insertCell();

    if (unique.listCicleLevel !== undefined) {
      const matchingOrder = unique.listCicleLevel.find(
        (order) => order.orderCicle === orderItem.order
      );

      cell.innerText = matchingOrder ? matchingOrder.totalUniqueCharacteres.toString() : '0';
    } 
  });

  const archetypeTotal = unique.total_name === 'Total';

  if (archetypeTotal && index === this.totalListArchetypes.length - 1) {
    // Cria uma nova linha para a última linha de totais
    const tBodyRowArchTotal = tBodyARCHETYPES.insertRow();

    // Coluna "Total Name"
    const archetypeCellTotal = tBodyRowArchTotal.insertCell();
    archetypeCellTotal.innerText = unique.total_name; // Pegando o nome da última linha corretamente

    // Preenchendo os valores da última linha
    unique.total_column?.forEach((total) => {
      if (total !== null) {
        const cell = tBodyRowArchTotal.insertCell();
        cell.innerText = total.toString();
      }

    });

    // Remove a penúltima linha em branco
    tBodyARCHETYPES.deleteRow(index);
  }

}); 

    // Criando as planilhas Excel corretamente
    const ws1: XLSX.WorkSheet = XLSX.utils.table_to_sheet(characterTable);
    const ws2: XLSX.WorkSheet = XLSX.utils.table_to_sheet(levelTable);
    const ws3: XLSX.WorkSheet = XLSX.utils.table_to_sheet(hcBLlTable);
    const ws4: XLSX.WorkSheet = XLSX.utils.table_to_sheet(bLTable);
    const ws5: XLSX.WorkSheet = XLSX.utils.table_to_sheet(archetypesTable); // Agora usa a tabela correta

    // Criando um novo arquivo Excel com as planilhas
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws1, 'Total Unique Characters');
    XLSX.utils.book_append_sheet(wb, ws2, 'Total Level Points');
    XLSX.utils.book_append_sheet(wb, ws3, 'Unique Characters by HC and BL');
    XLSX.utils.book_append_sheet(wb, ws4, 'Not Compatible');
    XLSX.utils.book_append_sheet(wb, ws5, 'Total Archetypes');

    // Salvando o arquivo Excel
    const now = new Date();
    const filename = `${now.toLocaleDateString()}_${now.toLocaleTimeString()}_DSAdmin_CHARACTERS_LP.xlsx`;
    XLSX.writeFile(wb, filename);

    
  }

  // Funcionalidades do UNIQUE CHARACTERES BY HC AND BL

  multiplyOrderArea() {
    this.multiplyArea = [];
    this.orderAreaList.forEach((area, index) => {
      for (let i = 0; i < 5; i++) {
        this.multiplyArea.push({
          idArea: area.id,
          nameArea: area.name,
          orderArea: area.order,
        });
      }
    });

    //Adiciona area extra 10
    this.multiplyArea.push({
      idArea: '10',
      nameArea: 'Extra 10',
      orderArea: 10,
    });

    this.multiplyArea = this.multiplyArea.map((area, i) => ({
      ...area,
      indexBL: i,
    }));

    // Areas duplicadas
    this.listOrderArea.forEach((dupli) => {
      this.areaListHCBL.push({
        idArea: dupli.id,
        nameArea: dupli.name,
        orderArea: dupli.order,
        indexBL: '',
      });
    });

    this.areaListHCBL;
  }

  async createCharacterArray() {
    // Verifique se os dados estão disponíveis
    if (
      !this._characterService.models?.length ||
      !this._battleUpgradeService.models?.length
    ) {
      console.warn('Dados necessários não estão carregados.');
      return;
    }

    this.charactersList = [];
    this._battleUpgradeService.models.forEach((battle) => {
      const character = this._characterService.models.find(
        (model) => model.id === battle.character && model.rarityId !== 'TR5'
      );

      if (character) {
        this.charactersList.push({
          bl: battle.bl || 0,
          areaId: character.areaId,
          idCharacter: character.id,
          nameCharacter: character.name,
          rarity: character.rarity,
          rarityId: character.rarityId,
        });
      }
    });

    this.charactersList = this.charactersList.map((character) => {
      const matchedArea = this.areaListHCBL.find(
        (multi) => multi.idArea === character.areaId
      );

      if (matchedArea) {
        return {
          ...character,
          nameArea: matchedArea.nameArea,
          orderArea: matchedArea.orderArea,
          indexBL: '',
        };
      }
      return character;
    });

    this.charactersList = this.charactersList.filter((character) => {
      const matchingArea = this.multiplyArea.find(
        (area) =>
          area.idArea === character.areaId && area.indexBL === character.bl
      );

      if (matchingArea) {
        character.indexBL = matchingArea.indexBL;
        return true; // Mantém o item no charactersList
      } else {
        this.nonExistentItems.push(character);
        return false; // Remove o item do charactersList
      }
    });

    //Adiciona o nome da área para os inexistentes
    this.nonExistentItems.filter((item) => {
      this._areaService.models.forEach((area) => {
        if (area.id == item.areaId) {
          return (item.nameArea = area.name);
        } else {
          return item;
        }
      });
    });

    //Verif se os inexistentes existem na listagem areaListHCBL (original)
    for (let index = 0; index < this.nonExistentItems.length; index++) {
      const item = this.nonExistentItems[index];
      const matchingArea = await this.areaListHCBL.find(
        (area) =>
          area?.orderArea == item?.orderArea && area?.idArea == item?.areaId
      );

      if (matchingArea) {
        const matchingMultiplyArea = this.multiplyArea.find(
          (multi) =>
            multi?.orderArea == item?.orderArea && multi?.indexBL == item?.bl
        );

        if (matchingMultiplyArea) {
          item.indexBL = matchingMultiplyArea.indexBL;
          this.charactersList.push(item);
          this.nonExistentItems.splice(index, 1);
          index--; // atualize o índice do loop
        }
      }
    }
  }

  calculateQuantityPosition() {
    // Limpa o array quantityPosition para uma nova construção
    this.quantityPosition = [];

    // Agrupa os itens de charactersList por rarityId
    const groupedByRarityId = this.charactersList.reduce((acc, character) => {
      if (!acc[character.rarityId]) {
        acc[character.rarityId] = [];
      }
      acc[character.rarityId].push(character);
      return acc;
    }, {} as Record<string, any[]>);

    // Itera sobre cada grupo de rarityId
    for (const rarityId in groupedByRarityId) {
      const charactersByRarity = groupedByRarityId[rarityId];

      // Agrupa os itens por 'bl' dentro do mesmo rarityId
      const groupedByBL = charactersByRarity.reduce((acc, character) => {
        if (!acc[character.bl]) {
          acc[character.bl] = [];
        }
        acc[character.bl].push(character);
        return acc;
      }, {} as Record<string, any[]>);

      // Inicializa o objeto para o rarityId atual
      const quantityEntry: QuantityHCBL = {
        rarity: charactersByRarity[0].rarity, // Todos no grupo possuem o mesmo rarity
        rarityId: rarityId,
        listHC: [],
      };

      // Itera sobre os grupos de 'bl' e calcula os valores
      for (const bl in groupedByBL) {
        const charactersByBL = groupedByBL[bl];

        // Adiciona os resultados ao listHC
        quantityEntry.listHC.push({
          areaId: charactersByBL[0].areaId,
          orderArea: charactersByBL[0].orderArea,
          indexBL: charactersByBL[0].indexBL,
          totalHCBL: charactersByBL.length,
        });
      }

      // Adiciona o objeto do rarityId atual ao array quantityPosition
      this.quantityPosition.push(quantityEntry);
    }
    return this.quantityPosition;
  }

  async createdUniqueCaractersByHCAndBL() {
    // Aguarde o carregamento dos dados necessários
    await Promise.all([
      this._characterService.toFinishLoading(),
      this._battleUpgradeService.toFinishLoading(),
    ]);

    this.multiplyOrderArea();
    // Criar o array de personagens
    this.createCharacterArray();

    // Processar dados de personagens únicos
    this.listUniqueByHCAndBL = [];
    this._uniqueCharactersByHCAndBLService.models = [];
    this._uniqueCharactersByHCAndBLService.toSave();

    this.weaponRarityNames.forEach((rarity) => {
      if (rarity.id !== 'TR5') {
        this._uniqueCharactersByHCAndBLService.createNewUniqueCharactersByHCAndBL(
          rarity
        );
      }
    });

    const result: QuantityHCBL[] = this.calculateQuantityPosition();

    this.listUniqueByHCAndBL = this._uniqueCharactersByHCAndBLService.models;

    for (let index = 0; index < this.listUniqueByHCAndBL.length; index++) {
      result.forEach((x) => {
        if (x.rarityId === this.listUniqueByHCAndBL[index].idRarity) {
          this.listUniqueByHCAndBL[index].hcList = x.listHC;
        }
      });
    }
  }
}
