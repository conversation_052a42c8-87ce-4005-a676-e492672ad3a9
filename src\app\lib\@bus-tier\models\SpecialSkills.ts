import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { SpecialSkill } from '../../../../lib/darkcloud/angular/dsadmin/v9/data/hard';
import { Base } from './Base';

export class SpecialSkills extends Base<Data.Hard.ISpecialSkills, Data.Result.ISpecialSkills> implements Required<Data.Hard.ISpecialSkills>
{
  public static generateId(index: number): string {
    return IdPrefixes.SPECIALSKILLS + index;
  }

  constructor( index: number, dataAccess: SpecialSkills['TDataAccess']) 
  {
    super(
    {
      hard: 
      {
        id: SpecialSkills.generateId(index),     
      },
    },
    dataAccess
    );
  }
  protected getInternalFetch() 
  {
    return {};
  }
  public get idChacracter(): string
  {
    return this.hard.idChacracter;
  }
  public set idChacracter(value: string) 
  {
    this.hard.idChacracter = value;
  } 
  public get nameChacracter(): string
  {
    return this.hard.nameChacracter;
  }
  public set nameChacracter(value: string) 
  {
    this.hard.nameChacracter = value;
  } 
  public get rarityChacracter(): string
  {
    return this.hard.rarityChacracter;
  }
  public set rarityChacracter(value: string) 
  {
    this.hard.rarityChacracter = value;
  } 

  public get listSpecialSkills(): SpecialSkill[]
  {
    return this.hard.listSpecialSkills;
  }
  public set listSpecialSkills(value: SpecialSkill[]) 
  {
    this.hard.listSpecialSkills = value;
  } 
}
