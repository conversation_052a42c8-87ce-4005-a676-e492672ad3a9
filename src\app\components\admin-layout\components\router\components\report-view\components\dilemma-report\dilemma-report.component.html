<div class="row">
    <div class="col-md-12">
        <div class="report-table-wrapper">
            <ng-container *ngIf="dilemmaBoxes.length > 0">
                <table class="table report-table rable-striped">
                    <thead class="sticky" style="top: 1px;">
                        <tr>
                            <th>Index</th>
                            <th class="th-clickable" (click)="sortArrayByType(dilemmaBoxes)">Type</th>
                            <th class="th-clickable" (click)="sortArrayByLocation(dilemmaBoxes)">Location</th>
                            <th class="th-clickable" (click)="sortArrayByDilemma(dilemmaBoxes)">Dilemma</th>
                            <th class="th-clickable" (click)="sortArrayByAtributto(dilemmaBoxes, atributto.id)"
                                *ngFor="let atributto of atributtoClasses; let i = index">{{atributto.atributte}}</th>
                            <th class="th-clickable" (click)="sortArrayByThreshold(dilemmaBoxes)">Threshold</th>
                        </tr>
                    </thead>

                    <tbody>
                        <ng-container *ngFor="let dilemma of dilemmaBoxes; let i = index">
                            <tr [title]="dilemma?.idDilemmaBox" class="tr-clickable" (click)="access(dilemma.idDilemmaBox)">
                                <td class="ind"><span>{{i}}</span></td>
                                <td class="td-10">Dillema Point</td>
                                <td style=" width: 20% !important;">
                                    <span style="height: 10px; width: 10px; border-radius: 50%; display: inline-block;"
                                        class="dot">
                                    </span>
                                    {{([dilemma?.id] | location).length > 0 ? ([dilemma?.id] | location) : 'Ophan'}}
                                </td>
                                <td class="td-20">{{dilemma.message}}</td>
                                <td class="td-10" *ngFor="let point of dilemma.points">{{point.valuePoints}}</td>
                                <td class="td-10">{{dilemma.threshold ? dilemma.threshold :
                                    thresholdOptions[0]?.valueThreshold }}</td>
                            </tr>
                        </ng-container>

                    </tbody>
                </table>
            </ng-container>
            <ng-container *ngIf="dilemmaBoxes.length === 0">
                <div class="card" style="text-align: center;">
                    <h4>Empty List</h4>
                </div>
            </ng-container>
        </div>


    </div>
</div>