import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Component, OnD<PERSON>roy, OnInit } from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { firstValueFrom } from "rxjs";
import { Language } from "src/app/lib/@bus-tier/models/Language";
import { IndexStorageService, ItemService, UserSettingsService } from "src/app/services";
import { LanguageService } from "src/app/services/language.service";
import { TranslationService } from "src/app/services/translation.service";
import { Alert } from "src/lib/darkcloud";
import { SortableListComponent } from "src/lib/darkcloud/angular/easy-mvc";


@Component({
    selector: 'app-language-list',
    templateUrl: './language-list.component.html',
    styleUrls: ['./language-list.component.scss']
})
export class LanguageListComponent extends SortableListComponent<Language> implements OnInit, On<PERSON><PERSON>roy
{
    timeout;
    public languages = [];
    public isChecked = [true];

    httpOptions = 
    {
        headers: new HttpHeaders({'Content-Type':  'application/json'})
    };

    constructor(
        _userSettingsService: UserSettingsService,
        _activatedRoute: ActivatedRoute,
        private _languageService: LanguageService,
        private _translationService: TranslationService,
        private _indexStorageService: IndexStorageService,
        private http: HttpClient
    )
    {
        super(_languageService, _activatedRoute, _userSettingsService, 'name');
        this.loadLanguages();
        this._indexStorageService.onLanguageCreated$.subscribe((language) => 
        {
           this.timeout = setTimeout(() => 
           {
                this.lstInit();
            }, 800)
        })
    }

    public override lstInit() 
    {
        this.loadLanguages();
    }
    
    public get activeLanguage()
    {
        return this._languageService.activeLanguage;
    }

    public set activeLanguage(value: Language | {name: string})
    {
        this._languageService.activeLanguage = value;
    }

    getLanguageProgress(language: string)
    {
        let translationPack = this._translationService.models.find(x => x.id == language);
        if(!translationPack) return 0;

        let result = translationPack.data.length / this._translationService.totalTranslatableObjects;
        if(result > 1) result = 1;

        return result * 100 + "%";
    }

    public async removeLanguage(language: string) 
    {
      Alert.showError
      // removes from the database
      const confirm = await Alert.showRemoveAlert(
        `${language}" language? ALL the data related to this language will be lost FOREVER!`
      );

      if (confirm) 
      {
        this.http.post(`http://localhost:3000/hstoredeletetable?language=${language}`,{}, this.httpOptions).subscribe
        ({
            next: (v) => 
            {
                this.loadLanguages();
                console.log(v)
            },
            error: (e) => 
            {
                Alert.showError(e, 'This is a server ERROR. Please take a screen shot of this error and show to the developer!');
                console.error("Errorrrr",e)
            },
            complete: () =>
            {
                console.info('complete') 
                this.loadLanguages();
            } 
        })
        return true;
      }
      return false;
    }

    async selectActiveLanguage(language: string, index: number)
    {
        IndexStorageService.activeLanguage = language;
        this._languageService.activeLanguage = new Language(0, language, this._userSettingsService)
        this._indexStorageService.setOnLanguageChange(language);
        this.loadLanguages()
        this.isChecked[index] = !this.isChecked[index]
    }

    async loadLanguages()
    {
        try
        {
            let json: any[] = await firstValueFrom<any>(this.http.post(`http://localhost:3000/hstoretables`,{}, this.httpOptions));
            IndexStorageService.languages = json
            this.languages = IndexStorageService.languages;
            this.isChecked = IndexStorageService.languages.map(l => l == IndexStorageService.activeLanguage)
        }
        catch(e)
        {
            Alert.showError(e, 'This is a server ERROR. Please take a screen shot of this error and show to the developer!');
            console.error("Errorrrr",e)
        }
    }

    downloadLanguagePack(language: Language)
    {
        //Download the current translation data for the given language
        this._translationService.exportLanguageData(language.name);
    }

    ngOnDestroy()
    {
        clearTimeout(this.timeout)
    }
}
