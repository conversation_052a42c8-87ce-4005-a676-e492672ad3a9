<div class="card list-header" style="height: 70px; margin: 30px; margin-bottom: 0px;">
    <div class="header" style="display:flex; flex-direction:row; justify-content: space-between;">
        <div style="display:flex; flex-direction:row; justify-content: space-between; gap: 5px;">
            <button routerLink="/charactersSelector"
                class="{{!this.inThisPage ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}">Characters
                Selection</button>
            <button routerLink="/animationsSelector"
                class="{{!this.inThisPage ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"> Animations Selection
            </button>
            <button routerLink="/itemsSelector"
                class="{{this.inThisPage ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}">Items Selection</button>
        </div>
    </div>
</div>

<div class="main-content">

    <div style="margin-top:70px;" class="card">
        <app-header-with-buttons [cardTitle]="'Selection Section'" [cardDescription]="description"
            [rightButtonTemplates]="[excelButtonTemplate, exportExcelButtonTemplate]" [isBackButtonEnabled]="false">
        </app-header-with-buttons>
        <app-header-search (inputKeyup)="search($event)" (searchOptions)="searchConditions($event)"></app-header-search>
    </div>

          <div class="card horizontal-scroll">
            <table class="table table-list card">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th class="th-clickable" (click)="sortByName('itemName')">Name</th>
                        <th class="th-clickable" (click)="sortByName('description')">Notes</th>
                        <th class="th-clickable" (click)="sortElements('concept')">Concept</th>
                        <th class="th-clickable" (click)="sortElements('finish')">Finish</th>
                        <th>Items Section
                            <select #items (change)="onChangeArea(items.value)"
                                class="dropdown filter-dropdown limited center">
                                <option default value="All">All</option>
                                <option *ngFor="let items of this._itemClassService.models" [value]="items.name">
                                    {{items.name}}</option>
                            </select>
                        </th>
                    </tr>
                </thead>

                <tbody *ngIf="itemsSelector">
                    <ng-container *ngFor="let itemSelector of itemsSelector; let i = index">
                        <tr *ngIf="itemsSelector">
                            <td>
                                {{i}}
                            </td>
                            <td>
                                {{itemSelector.itemName}}
                            </td>
                            <td>
                                <textarea placeholder=" " class="form-control" type="text" #notes
                                    (change)="onChangeNotes(itemSelector, notes.value)"
                                    value="{{ itemSelector.description }}"></textarea>
                            </td>
                            <td>
                                <input type="checkbox" [checked]="itemSelector.concept"
                                    (click)="onChangeCheckbox(itemSelector, 'concept')">
                            </td>
                            <td>
                                <input type="checkbox" [checked]="itemSelector.finish"
                                    (click)="onChangeCheckbox(itemSelector, 'finish')">
                            </td>
                            <td>
                                {{itemSelector?.itemsSection}}
                            </td>
                        </tr>
                    </ng-container>
                </tbody>
            </table>
        </div>    
  
            <!-- Spinner-->
            <ng-container *ngIf="loadingSpinner">
                <app-loading-spinner></app-loading-spinner>
            </ng-container>  
    
    <ng-container *ngIf="!ClassItens.length">
        <div class="noSelection">
          <p style="font-weight: 800;">
            <i class="pe-7s-attention"></i>
            No Items Selection</p>
          <p>Check if I import the database correctly.</p>
        </div>
      </ng-container>
</div>