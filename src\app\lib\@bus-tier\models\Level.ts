import { IdPrefixes, LevelType } from 'src/lib/darkcloud/dialogue-system';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { Area } from './Area';
import { Base } from './Base';

export class Level
  extends Base<Data.Hard.ILevel, Data.Result.ILevel>
  implements Data.Hard.ILevel
{
  public static generateId(areaId: string, index: number): string {
    return areaId + '.' + IdPrefixes.LEVEL + index;
  }
  public static getSubIdFrom(otherId: string): string {
    return this.getSubId(this.generateId(Area.generateId(0), 0), otherId);
  }
  public static getLoopIdFrom(otherId: string)
  {
    let splitId = otherId.split('.');
    return splitId[0] + '.' + splitId[1];
  }
  constructor(index: number, areaId: string, dataAccess: Level['TDataAccess']) {
    super(
      {
        hard: {
          id: Level.generateId(areaId, index),
          type: LevelType.MINION,
          speakerIds: [],
          battleCharacterIds: [],
          linkedLevelIds: [],
          dialogueIds: [],
          conditionIds: [],
        },
      },
      dataAccess
    );
  }
  protected getInternalFetch() {
    return {};
  }
  public get battleCharacterIds(): string[] {
    return this.hard.battleCharacterIds;
  }
  public set battleCharacterIds(value: string[]) {
    this.hard.battleCharacterIds = value;
  }
  public get name(): string {
    return this.hard.name;
  }
  public set name(value: string) {
    this.hard.name = value;
  }
  public get description(): string {
    return this.hard.description;
  }
  public set description(value: string) {
    this.hard.description = value;
  }
  public get type(): LevelType {
    return this.hard.type;
  }
  public set type(value: LevelType) {
    this.hard.type = value;
  }
  public get speakerIds(): string[] {
    return this.hard.speakerIds;
  }
  public set speakerIds(value: string[]) {
    this.hard.speakerIds = value;
  }
  public get linkedLevelIds(): string[] {
    return this.hard.linkedLevelIds;
  }
  public set linkedLevelIds(value: string[]) {
    this.hard.linkedLevelIds = value;
  }
  public get dialogueIds(): string[] {
    return this.hard.dialogueIds;
  }
  public set dialogueIds(value: string[]) {
    this.hard.dialogueIds = value;
  }
  public get firstAttack(): boolean {
    return this.hard.firstAttack;
  }
  public set firstAttack(value: boolean) {
    this.hard.firstAttack = value;
  }
  public get conditionIds(): string[] {
    return this.hard.conditionIds;
  }
  public set conditionIds(value: string[]) {
    this.hard.conditionIds = value;
  }
  public get blockGrind(): boolean {
    return this.hard.blockGrind;
  }
  public set blockGrind(value: boolean) {
    this.hard.blockGrind = value;
  }
  public get removeSpecialItemBeforeGrind(): boolean {
    return this.hard.removeSpecialItemBeforeGrind;
  }
  public set removeSpecialItemBeforeGrind(value: boolean) {
    this.hard.removeSpecialItemBeforeGrind = value;
  }
  public get sceneryId(): string {
    return this.hard.sceneryId;
  }
  public set sceneryId(value: string) {
    this.hard.sceneryId = value;
  }

  public get minigameId()
  {
    return this.hard.minigameId;
  }
  public set minigameId(value: string)
  {
    this.hard.minigameId = value;
  }

  public get scoreRequirement()
  {
    return this.hard.scoreRequirement;
  }
  public set scoreRequirement(value: number)
  {
    this.hard.scoreRequirement = value;
  }

  public get xp()
  {
    return this.hard.xp;
  }
  public set xp(value: number)
  {
    this.hard.xp = value;
  }

  public get iconIndex()
  {
    return this.hard.iconIndex;
  }
  public set iconIndex(value: number)
  {
    this.hard.iconIndex = value;
  }
  public get order()
  {
    return this.hard.order;
  }
  public set order(value: number)
  {
    this.hard.order = value;
  }
  public get pathOrder()
  {
    return this.hard.pathOrder;
  }
  public set pathOrder(value: number[])
  {
    this.hard.pathOrder = value;
  }

  public get isUnique()
  {
    return this.hard.isUnique;
  }
  public set isUnique(value: boolean)
  {
    this.hard.isUnique = value;
  }
  public get isFirst()
  {
    return this.hard.isFirst;
  }
  public set isFirst(value: boolean)
  {
    this.hard.isFirst = value;
  }  
  public get isReviewedDescription(): boolean {
    return this.hard.isReviewedDescription;
  }
  public set isReviewedDescription(value: boolean) {
    this.hard.isReviewedDescription = value;
  }  
  public get isReviewedName(): boolean {
    return this.hard.isReviewedName;
  }
  public set isReviewedName(value: boolean) {
    this.hard.isReviewedName = value;
  }
  public get revisionCounterNameAI(): number {
    return this.hard.revisionCounterNameAI;
  }
  public set revisionCounterNameAI(value: number) {
    this.hard.revisionCounterNameAI = value;
  }
  public get revisionCounterDescriptionAI(): number {
    return this.hard.revisionCounterDescriptionAI;
  }
  public set revisionCounterDescriptionAI(value: number) {
    this.hard.revisionCounterDescriptionAI = value;
  }
}
