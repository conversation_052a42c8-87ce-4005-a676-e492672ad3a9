import { Injectable } from '@angular/core';
import { AreaService } from 'src/app/services/area.service';
import { CharacterService } from 'src/app/services/character.service';
import { DialogueService } from 'src/app/services/dialogue.service';
import { LevelService } from 'src/app/services/level.service';
import { MissionService } from 'src/app/services/mission.service';
import { Area } from 'src/app/lib/@bus-tier/models';
import { extractInt, sortData } from 'src/lib/others';
import { Preloadable } from './level-dialogue-editor.component';
import { SoundService } from 'src/app/services';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';

@Injectable({
  providedIn: 'root',
})
export class LevelDialogueEditorResolve implements Resolve<Preloadable> 
{
  constructor(
    private _dialogueService: DialogueService,
    private _levelService: LevelService,
    private _characterService: CharacterService,
    private _missionService: MissionService,
    private _areaService: AreaService,
    private _soundService: SoundService,

  ) {}

  async resolve(route: ActivatedRouteSnapshot): Promise<Preloadable> 
  {
    let dialogueId = route.paramMap.get('dialogueId');
    let levelId = route.paramMap.get('levelId');
   
    await this._dialogueService.toFinishLoading();
    await this._levelService.toFinishLoading();
    await this._characterService.toFinishLoading();
    await this._missionService.toFinishLoading();
    await this._areaService.toFinishLoading();
    await this._soundService.toFinishLoading();

    const dialogue = this._dialogueService.svcCloneById(dialogueId);
    const level = this._levelService.svcCloneById(levelId);
    const areaId = Area.getSubIdFrom(level?.id);
    const speakers = this._characterService.svcCloneByIds(level?.speakerIds);
    const preloadedCharacters = sortData(this._characterService.models, 'name');
    const missions = sortData(
      this._missionService.models.filter(
        (mission) =>
          extractInt(
            this._areaService.svcFindById(mission.areaId)?.hierarchyCode
          ) === extractInt(this._areaService.svcFindById(areaId)?.hierarchyCode)
      ),
      'name'
    );

    return {
      dialogue,
      level,
      speakers,
      missions,
      preloadedCharacters,
    };
  }
}
