<div class="main-menu-efect">
    <div class="container-fluid">
      <div class="list-header-row update">
        <div class="card">
          <div style="display: flex; justify-content: space-between;">
            <div class="card-header-content">
              <h3 class="title">Subcontext Knowledge</h3>
              <p style="width:60vw;" class="category">{{ description}}</p>
            </div>
            <div style="display: flex; align-items: end; justify-content: end; align-items: center;">
              <div class="btn-atributte">
                <button style="margin-left: 2px;" class="btn btn-fill" (click)="btnClickContext()">Back Knowledge</button>
              </div>
              <ng-container>
                <!--BUTTON EXCEL-->
                <div id="button" style="position: absolute; margin-top: 60px;">
                  <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'" class="add-buttons"
                    [buttonTemplates]="[excelButtonTemplate]">
                  </app-button-group>
                </div>
              </ng-container>
            </div>
          </div>
        </div>
      </div>
  
      <ng-container *ngIf="isSubContext">
        <div style="display: flex; flex-direction: row; overflow-x: auto; width: 100%;">
          <table class="table table-list borderList">
            <thead>
              <tr>
                <th *ngFor="let title of titles"> {{title}}</th>
              </tr>
            </thead>
            <tbody>
              <ng-container *ngFor="let item of listSubContext; let i = index">
                <tr>
                  <td class="td-id aligTitle" [attr.rowspan]="item.subContext.length">
                    {{ item.knowledge }}
                  </td>
                  <td class="td-id width-buscontext ">
                    <input class="background-input-table-color form-control form-short text-center" placeholder=" "
                      type="text" #subContext [ngClass]="{'empty-input': !subContext.value}" [value]="item.subContext[0] "
                      (change)="changeSubContextValue(i, 0,'subContext', subContext.value)" />
                  </td>
                  <td class="td-id">
                    <input class="background-input-table-color form-control form-short" placeholder=" " type="text"
                      #description [ngClass]="{'empty-input': !description.value}" value="{{item.description[0]}}"
                      (change)="changeSubContextValue(i, 0, 'description', description.value)" />
                  </td>
                </tr>
                <!--LINHA 2-->
                <tr>
                  <td class="td-id width-buscontext ">
                    <input class="background-input-table-color form-control form-short text-center" placeholder=" "
                      type="text" #subContext2 [ngClass]="{'empty-input': !subContext2.value}"
                      [value]="item.subContext[1] "
                      (change)="changeSubContextValue(i, 1,'subContext', subContext2.value)" />
                  </td>
                  <td class="td-id">
                    <input class="background-input-table-color form-control form-short" placeholder=" " type="text"
                      #description2 [ngClass]="{'empty-input': !description2.value}" value="{{item.description[1]}}"
                      (change)="changeSubContextValue(i, 1, 'description', description2.value)" />
                  </td>
                </tr>
                <!--LINHA 3-->
                <tr>
                  <td class="td-id width-buscontext ">
                    <input class="background-input-table-color form-control form-short text-center" placeholder=" "
                      type="text" #subContext3 [ngClass]="{'empty-input': !subContext3.value}"
                      [value]="item.subContext[2] "
                      (change)="changeSubContextValue(i, 2,'subContext', subContext3.value)" />
                  </td>
                  <td class="td-id">
                    <input class="background-input-table-color form-control form-short" placeholder=" " type="text"
                      #description3 [ngClass]="{'empty-input': !description3.value}" value="{{item.description[2]}}"
                      (change)="changeSubContextValue(i, 2, 'description', description3.value)" />
                  </td>
                </tr>
                <!--LINHA 4-->
                <tr>
                  <td class="td-id width-buscontext ">
                    <input class="background-input-table-color form-control form-short text-center" placeholder=" "
                      type="text" #subContext4 [ngClass]="{'empty-input': !subContext4.value}"
                      [value]="item.subContext[3] "
                      (change)="changeSubContextValue(i, 3,'subContext', subContext4.value)" />
                  </td>
                  <td class="td-id">
                    <input class="background-input-table-color form-control form-short" placeholder=" " type="text"
                      #description4 [ngClass]="{'empty-input': !description4.value}" value="{{item.description[3]}}"
                      (change)="changeSubContextValue(i, 3, 'description', description4.value)" />
                  </td>
                </tr>
              </ng-container>
            </tbody>
          </table>
        </div>
      </ng-container>
  
      <ng-container *ngIf="!isSubContext">
        <div class="card" style="text-align: center; padding: 20px;">
          <h3>Empty list. Click to create the list.</h3>
        </div>
      </ng-container>