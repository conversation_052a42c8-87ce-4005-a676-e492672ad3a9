import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Mission, Objective } from 'src/app/lib/@bus-tier/models';
import { ObjectiveService } from 'src/app/services/objective.service';
import { Alert } from 'src/lib/darkcloud';
import { MissionService } from 'src/app/services/mission.service';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { EventService, ReviewService } from 'src/app/services';
import { Router } from '@angular/router';

@Component({
  selector: 'app-objective-sublist',
  templateUrl: './objective-sublist.component.html',
  styleUrls: ['./objective-sublist.component.scss']
})

export class ObjectiveSublistComponent implements OnInit 
{

  @Input() missionId: string;
  @Output() xpChanged: EventEmitter<string> = new EventEmitter();

  mission: Mission;

  public objectiveIds: string[];

  constructor(
    private _reviewService: ReviewService,
    private _router: Router,
    private _objectiveService: ObjectiveService,
    private _missionService: MissionService,
    private _eventService: EventService,
    _userSettingsService: UserSettingsService
  ) {}

  public async ngOnInit() 
  {
    this._missionService.toFinishLoading();
   // this.inicializedFieldsReviewed();
    this.mission = this._missionService.svcFindById(this.missionId);
  }

  inicializedFieldsReviewed() {
    this._missionService.models.forEach((objective) => {      
      objective.isReviewedDescription = !!objective.isReviewedDescription;
      this._missionService.svcToModify(objective);
    });
  }

  public async onChange(objective: Objective, parameter?: string, value?: string) 
  {
    if (parameter) 
    {
      objective[parameter] = value;
    }
    objective.isReviewedDescription = false;
    objective.revisionCounterDescriptionAI = 0;
    await this._objectiveService.svcToModify(objective);
  }

  public async toRemove(objective: Objective) 
  {
    if (await Alert.showRemoveAlert(objective.id)) 
    {
      await this._objectiveService.svcToRemove(objective.id);
      await this._missionService.RemoveObjective(this.mission, objective.id);

      this.removeObjectiveFromEvents(objective);
    }
  }

  async removeObjectiveFromEvents(objective: Objective)
  {
    for(let i = 0; i < this._eventService.models.length; i++)
    {
      if(this._eventService.models[i].objectiveId == objective.id)
      {
        await this._eventService.svcToRemove(objective.id);
      }
    }
  }

  public async add() 
  {
    try 
    {
      const objective = await this._objectiveService.svcPromptCreateNew(this.mission.id);
      if (!objective) return;
      
      if(!this._missionService.missionHaveObjective(this.mission))
        objective.hidded = false;
      else
        objective.hidded = true;

      await this._objectiveService.srvAdd(objective);
      await this._missionService.addObjective(this.mission, objective.id);
    }
    catch (error) 
    {
      Alert.showError(error);
    }
  }

  public async changeXp(objective: Objective, value: string)
  {
    objective.xp = +value;
    await this._objectiveService.svcToModify(objective)
    await this._objectiveService.toSave();
  }

  public async changeHidded(objective: Objective)
  {
    objective.hidded = !objective.hidded;
    await this._objectiveService.svcToModify(objective)
    await this._objectiveService.toSave();
  }
  accessSPO(spId) 
  {    
    this._router.navigate(
      [
        'levels/' +  spId.levelId +  '/dialogues/' +  spId.dialogueId,
      ],
      { fragment: spId }
    );
  }
}
