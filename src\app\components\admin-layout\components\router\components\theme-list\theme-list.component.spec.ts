import { Pipe, PipeTransform } from "@angular/core";
import { ComponentFixture, TestBed } from "@angular/core/testing";
import { IndexStorageService, ReviewService, ThemeService, UserSettingsService } from "src/app/services";
import { ThemeListComponent } from "./theme-list.component";
import { ActivatedRoute, Router } from "@angular/router";

class MockRouting{}

class MockService{
    getData<T>(...args: any){ return []; }
}

@Pipe({
    name: 'themes'
})
class ThemesPipeStub implements PipeTransform
{
    transform(value: any){ return value; }
}

describe('ThemeListComponent', () => {
    let component: ThemeListComponent;
    let fixture: ComponentFixture<ThemeListComponent>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            declarations: [ThemeListComponent, ThemesPipeStub],
            imports: [],
            providers: [
                {provide: ActivatedRoute, useClass: MockRouting},
                {provide: ThemeService, useClass: MockService},
                {provide: UserSettingsService, useClass: MockService},
                {provide: ReviewService, useClass: MockService},
                {provide: IndexStorageService, useClass: MockService},
                {provide: Router, useClass: MockRouting}
            ]
        });

        fixture = TestBed.createComponent(ThemeListComponent);
        component = fixture.componentInstance;
    });

    it('should be created', () => {
        expect(component).toBeTruthy();
    });

});