import { Subscription } from 'rxjs';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { RPGFormatting } from 'src/lib/darkcloud';
import { sortData } from 'src/lib/others';
import { SearchService } from 'src/app/services/search.service';
import { AreaService } from 'src/app/services/area.service';
import { CharacterService } from 'src/app/services/character.service';
import { ItemService } from 'src/app/services/item.service';
import { Detections, IDetection, RpgService } from 'src/app/services/rpg.service';
import { Alert } from 'src/lib/darkcloud';
import { GameTypes } from 'src/lib/darkcloud/dialogue-system';
import { RPGFormattingAlert } from 'src/lib/darkcloud/utils/alert';
import { Router } from '@angular/router';

@Component({
  selector: 'app-rpg-wording-detection-manager',
  templateUrl: './rpg-wording-detection-manager.component.html',
  styleUrls: ['./rpg-wording-detection-manager.component.scss']
})
export class RpgWordingDetectionManagerComponent implements OnInit, OnDestroy 
{
  RPGType = RPGFormatting.RPGType;
  detections: { [rpgFormattingType: number]: Detections } = {};
  private _subscription: Subscription;
  all: any[];
  nAnalyzedDetected: number;

  constructor(
    private _rpgService: RpgService,
    private _searchService: SearchService,
    private _router: Router,
    private _areaService: AreaService,
    private _characterService: CharacterService,
    private _itemService: ItemService
  ) {}

  searchFor(term: string) 
  {
    this._searchService.searchFor(term, 'message');
    this._router.navigate(['search']);
  }

  ngOnInit(): void 
  {
    this.detections = this._rpgService.detections;
    RPGFormatting.FORMATS.forEach((f) => 
    {
      this.detections[+f.type] = sortData(this.detections[+f.type], 'word');
    });
    this._subscription = this._rpgService.detectionsSubject.subscribe((v) => 
    {
      this.detections = v;
      RPGFormatting.FORMATS.forEach((f) => 
      {
        this.detections[+f.type] = sortData(this.detections[+f.type], 'word');
      });
    });

    this.all = this._rpgService.all;
    this._subscription.add(
      this._rpgService.allSubject.subscribe((v) => 
      {
        this.all = v;
      })
    );

    this.nAnalyzedDetected = this._rpgService.nAnalyzedDetected;
    this._subscription.add(this._rpgService.nAnalyzedDetectedSubject.subscribe((v) => (this.nAnalyzedDetected = v)));
  }

  accessArea(name: string) 
  {
    this._router.navigate(['areas'], 
    {
      fragment: this._areaService.models.find((t) => t.name === name).id,
    });
  }

  accessCharacter(name: string) 
  {
    this._router.navigate(['characters'], 
    {
      fragment: this._characterService.models.find((t) => t.name === name).id,
    });
  }

  accessItem(name: string) 
  {
    this._router.navigate(['items'], 
    {
      fragment: this._itemService.models.find((t) => t.name === name).id,
    });
  }

  toPromptDetectionReview(detection: IDetection) 
  {
    RPGFormattingAlert.showReview(
      `"${detection.word}" (${detection.amount} times)`,
      detection.detected.map(t => t.message)
    );
  }

  async toPromptRemoveDetectedWord(detection: IDetection, type: RPGFormatting.RPGType) 
  {
    if (
      await Alert.showConfirm(
        `Are you sure you want to remove "${detection.word}"\n${GameTypes.rpgFormattingTypeName[+type]} formatting?`,
        `This will remove the formatting for ${detection.detected.length} messages`, 'Review'
      )
    ) 
    {
      if (await RPGFormattingAlert.showReviewUnlink('These messages will be reformatted:', detection.detected.map(t => t.message))) 
      {
        this._rpgService.removeDetectedWord(detection, type);
      }
    }
  }
  
  ngOnDestroy(): void 
  {
    this._subscription.unsubscribe();
  }
}
