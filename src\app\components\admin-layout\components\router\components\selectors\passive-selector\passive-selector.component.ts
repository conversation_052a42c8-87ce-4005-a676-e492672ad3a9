import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/SortableListComponent';
import { Component } from '@angular/core';
import { Passive } from 'src/app/lib/@bus-tier/models';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { ReviewService } from 'src/app/services/review.service';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { PassiveService } from 'src/app/services';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-passive-selector',
  templateUrl: './passive-selector.component.html',
})
/**
 * Displays and edits emotion data as a list
 */
export class PassiveSelectorComponent extends SortableListComponent<Passive> {
  constructor(
    _activatedRoute: ActivatedRoute,
    protected _passiveService: PassiveService,
    _userSettingsService: UserSettingsService,
    private _router: Router,
    private _reviewService: ReviewService,
    protected _languageService: LanguageService,
    protected _translationService: TranslationService,
  ) {
    super(_passiveService, _activatedRoute, _userSettingsService, 'name');
  }

 /*  @Input() selectedPassivesId: string[];
  @Output() selectedClassessIdChange = new EventEmitter<string[]>();
  popupOpen = false;
  selectedPassives: Passive[];
  passives: Passive[];

  async lstInit(): Promise<void>
  {
    await this._classService.toFinishLoading();
    this.classes = this._classService.models;
    this.popupOpen = false
    this.selectedClasses = [];
    if(!this.selectedClassessId) this.selectedClassessId = [];
    this.classes.forEach(c => {
      if(this.selectedClassessId.includes(c.id))
      {
        let clas = this._classService.svcFindById(c.id);
        this.selectedClasses.push(clas);
      }
    });
    this.ref.detectChanges();
  }

  togglePopup(event)
  {
    if(event)
    {
        if (event.target !== event.currentTarget) return;
    }
    this.popupOpen = !this.popupOpen;
    if(this.popupOpen)
    {
      this.classes = this._classService.models;
    }
  }

  changeValue(event, clas: Class)
  {
    if(event.target.checked)
    {
      this.selectedClasses.push(clas);
    }
    else
    {
      this.selectedClasses = this.selectedClasses.filter(c => c.id !== clas.id);
    }
    this.selectedClassessIdChange.emit(this.selectedClasses.map(c => c.id));
  } */

}
