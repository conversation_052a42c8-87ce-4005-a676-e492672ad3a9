import { AfterViewInit, Component, OnInit } from '@angular/core';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/SortableListComponent';
import { AnimationsSelector, Character } from 'src/app/lib/@bus-tier/models';
import { UserSettingsService, AreaService, AnimationsSelectorService, CharactersSelectorService, CharacterService } from 'src/app/services';
import { Button } from 'src/app/lib/@pres-tier/data';
import { SpinnerService } from './../../../../../../../../spinner/spinner.service';
import * as XLSX from 'xlsx';
import { Alert } from 'src/lib/darkcloud';
import { ActivatedRoute } from '@angular/router';


@Component({
  selector: 'app-animations-selector',
  templateUrl: './animations-selector.component.html',
  styleUrls: ['./animations-selector.component.scss'],
})

export class AnimationsSelectorComponent extends SortableListComponent<AnimationsSelector> implements AfterViewInit, OnInit
{
  animationsSelectors:any[] = [];
  inThisPage:boolean = false;
  loadingSpinner: Boolean = false;
  sortByNameOrder = -1;

  caseSentitive;
  accentSentitive;
  description;

  constructor(
    private _spinnerService:SpinnerService,
    public _areaService:AreaService,
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    public _animationsSelectorService: AnimationsSelectorService,
    private _charactersSelectorService: CharactersSelectorService,
    private _charactersService: CharacterService
    ) 
  {
    super(_animationsSelectorService, _activatedRoute, _userSettingsService, 'name');
  }

  public readonly excelButtonTemplate: Button.Templateable = 
  {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };
  
  public readonly exportExcelButtonTemplate: Button.Templateable = 
  {
    btnClass: [...Button.Klasses.FILL_BLUE, 'Excel'],
    title: 'Export to Excel',
    onClick: this.downloadAsExcel.bind(this),
    iconClass: 'pe-7s-cloud-download',
  };

  public override async ngAfterViewInit(): Promise<void> 
  {
    this.inThisPage = true;
    this.loadingSpinner = true;
    setTimeout(()=>
    {
      for(let i = 0; i < this._charactersService.models.length; i++)
      { 
        let characterAnimation: AnimationsSelector = this.getCharacterInAnimations(this._charactersService.models[i])
        if(characterAnimation)
          this.animationsSelectors.push(characterAnimation);
        else
        {
          let animation = this._animationsSelectorService.createNewAnimationsSelector();
          animation.character = this._charactersService.models[i].name;
          animation.description = '';
          animation.finish = false;
          animation.rev1 = false;
          animation.rev2 = false;
          this.animationsSelectors.push(animation);
        }
      }
      this.loadingSpinner = false;
      this.description = `Showing ${this.animationsSelectors.length} results`;
    },1000);
            
    return null;
  }

    getCharacterInAnimations(character:Character):AnimationsSelector
    {
      for(let i=0; i < this._animationsSelectorService.models.length; i++)
        if(character.name == this._animationsSelectorService.models[i].character) return this._animationsSelectorService.models[i];

      return null;
    }
    
    sortElements(value:string)
    {
      let notChecked = [];
      let checked = [];

      for(let i = 0; i < this.animationsSelectors.length; i++)
      {
        if(this.animationsSelectors[i][value] == true) checked.push(this.animationsSelectors[i]);          
        else notChecked.push(this.animationsSelectors[i]);          
      }

      if(this.animationsSelectors[0][value] == false)
        this.animationsSelectors = [].concat(checked).concat(notChecked);
      else
        this.animationsSelectors = [].concat(notChecked).concat(checked);

    }

    sortByName(value) 
    {
      this.sortByNameOrder *= -1;
      this.animationsSelectors.sort((a, b) => 
      {
          return this.sortByNameOrder *  a[value]?.localeCompare(b[value]);
      });
    }
   
    async onChangeCheckbox(value:any, key:string)
    {
      if(value[key] == undefined) value[key] = true;
      else value[key] = !value[key];
      await this._animationsSelectorService.svcToModify(value);
      await this._animationsSelectorService.toSave();
    }

    async onChangeNotes(charSelector:any, description:any)
    {
      if(charSelector.description == undefined) charSelector.description = description;      
      else charSelector.description = description;
      
      await this._animationsSelectorService.svcToModify(charSelector);
      await this._animationsSelectorService.toSave();
    }
   
    search(value)
    {
      if(value == '') 
      {
        this.animationsSelectors = [];
        this._animationsSelectorService.models.forEach(char => this.animationsSelectors.push(char));
      } 

      if(!this.caseSentitive && !this.accentSentitive)
        this.animationsSelectors = this._animationsSelectorService.models.filter(char=> 
        this.simplifyString(char.character)?.includes(this.simplifyString(value)) || 
        this.simplifyString(char.description)?.includes(this.simplifyString(value)));

      else if(this.caseSentitive && this.accentSentitive)
        this.animationsSelectors = this._animationsSelectorService.models.filter(char=> 
        char.character?.includes(value) || char.description?.includes(value));

      else if(this.caseSentitive && !this.accentSentitive)
        this.animationsSelectors = this._animationsSelectorService.models.filter(char=> 
        this.simplifyString(char.character)?.toUpperCase().includes(this.simplifyString(value).toUpperCase()) || 
        this.simplifyString(char.description)?.toUpperCase().includes(this.simplifyString(value).toUpperCase()));

      else if(!this.caseSentitive && this.accentSentitive)
        this.animationsSelectors = this._animationsSelectorService.models.filter(char=> 
        char.character?.toUpperCase().includes(value.toUpperCase()) || 
        char.description?.toUpperCase().includes(value.toUpperCase()));

        this.description = `Showing ${this.animationsSelectors.length} results`;
    }

    searchConditions(value)
    {
      if(value.caseSensitive == true && value.accentSensitive == false)
      {
        this.caseSentitive = true;
        this.accentSentitive = false;
      }
      else if (value.accentSensitive == true && value.caseSensitive == false)
      {
        this.accentSentitive = true;
        this.caseSentitive = false;
      }
      else if(value.caseSensitive == true  && value.accentSensitive == true )
      {
        this.accentSentitive = true;
        this.caseSentitive = true;
      }
      else
      {
        this.accentSentitive = false;
        this.caseSentitive = false;
      }
    }

    public downloadAsExcel() 
    {
      const animationsTable = document.createElement('table');
      const tHead = animationsTable.createTHead();
      const tHeadRow = tHead.insertRow();
      tHeadRow.insertCell().innerText = 'Name';
      tHeadRow.insertCell().innerText = 'Notes';
      tHeadRow.insertCell().innerText = 'Finish';
      tHeadRow.insertCell().innerText = 'Rev1';
      tHeadRow.insertCell().innerText = 'Rev2';
 
      const tBody = animationsTable.createTBody();

      this.animationsSelectors.forEach(item =>
      {
        const tBodyRow = tBody.insertRow();
        tBodyRow.insertCell().innerText = item.character;
        tBodyRow.insertCell().innerText = item.description == undefined ? '' : item.description;
        tBodyRow.insertCell().innerText = item.finish ? 'X' : '';
        tBodyRow.insertCell().innerText = item.rev1 ? 'X' : '';
        tBodyRow.insertCell().innerText = item.rev2 ? 'X' : '';
      })

      const ws: XLSX.WorkSheet = XLSX.utils.table_to_sheet(animationsTable);
      const wb: XLSX.WorkBook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'Animations Selector List');
  
      const now = new Date();
      XLSX.writeFile(wb, now.toLocaleDateString() + '_' + now.toLocaleTimeString() + ' DSAdmin Animations Selector List.xlsx');
    }

    async onExcelPaste(): Promise<void> 
    {
      this._spinnerService.setState(true);
      const text = await navigator.clipboard.readText();
      const lines = text.split(/\r?\n/).filter(line => line);
      if(this.DisplayErrors(lines, 5)) return;

      for (let l = 0; l < lines.length; l++) 
      {
        let line = lines[l];
        let cols = line.split(/\t/);
        let item = this.animationsSelectors.find((i) =>
          this.simplifyString(i.character) == this.simplifyString(cols[0]));
        if (!item) continue;
        let character = this.animationsSelectors.find((i) => i.character == item.character);

        if (!character) continue;
  
        if (cols[1]?.trim()) character.description = cols[1];
        else character.description = undefined;
                        
        if (cols[2]?.trim()) 
        {
          character.finish = cols[2]
          .split(' ')
          .join('')
          .split('.')
          .join('')
          .replace(',','.') == 'X' ? true : false;
        } 
        else character.finish = undefined;

        if (cols[3]?.trim()) 
        {
          character.rev1 = cols[3]
          .split(' ')
          .join('')
          .split('.')
          .join('')
          .replace(',','.') == 'X' ? true : false;
        } 
        else character.rev1 = undefined;
        
        if (cols[4]?.trim()) 
        {
          character.rev2 = cols[4]
          .split(' ')
          .join('')
          .split('.')
          .join('')
          .replace(',','.') == 'X' ? true : false;
        } 
        else character.rev2 = undefined;
  
        await this._animationsSelectorService.svcToModify(character);
        await this._animationsSelectorService.toSave();
      }
      this._spinnerService.setState(false);
      this.description = `Showing ${this.animationsSelectors.length} results`;
      this.ngOnInit();
    }

    override simplifyString(str: string): string 
    {
      return (str?.normalize('NFD')?.replace(/[\u0300-\u036f]/g, '')?.toLocaleUpperCase())?.trim();
    }

    DisplayErrors(array, length:number) 
    {
      let count = array[0].split(/\t/);
      if (count.length < length) 
      {
        Alert.showError('Copy ALL '+ length +' columns!');
        this._spinnerService.setState(false);
        return true;
      }
  
      if (count[0] === '') 
      {
        Alert.showError('You are probably copying a blank column!');
        this._spinnerService.setState(false);
        return true;
      }
  
      return false;
    }
}
