import { ComponentFixture, TestBed } from "@angular/core/testing";
import { Level } from "src/app/lib/@bus-tier/models";
import { Minigame } from "src/app/lib/@bus-tier/models/Minigame";
import { IndexStorageService, LevelService, PopupService, ReviewService, UserSettingsService } from "src/app/services";
import { MinigameService } from "src/app/services/minigame.service";
import { Alert } from "src/lib/darkcloud";
import { IBase } from "src/lib/darkcloud/angular/dsadmin/v9/data/hard";
import { MinigameComponent } from "./minigame.component";
import { ActivatedRoute } from "@angular/router";

class MockService{
    models: IBase[] = [];

    svcToRemove(...args: any){
        return undefined;
    }

    svcToModify(...args: any){
        return undefined;
    }
}

class MockRouting{
    snapshot = {
        fragment: 'My TEST'
    }
}

class IndexStorageServiceStub{
    getData<T>(...args: any){ return []; }
}

describe('MinigameComponent', () => {
    let fixture: ComponentFixture<MinigameComponent>;
    let component: MinigameComponent;

    let levelService: LevelService;
    let minigameService: MinigameService;

    beforeEach(() => {
        TestBed.configureTestingModule({
            declarations: [],
            providers: [
                {provide: ReviewService, useClass: MockService},
                {provide: MinigameService, useClass: MockService},
                {provide: PopupService, useClass: MockService},
                {provide: ActivatedRoute, useClass: MockRouting},
                {provide: UserSettingsService, useClass: MockService},
                {provide: LevelService, useClass: MockService},
                {provide: IndexStorageService, useClass: IndexStorageServiceStub}
            ]
        });

        fixture = TestBed.createComponent(MinigameComponent);
        component = fixture.componentInstance;

        levelService = TestBed.inject(LevelService);
        minigameService = TestBed.inject(MinigameService);

        fixture.detectChanges();
    });

    it('should be created', () => {
        expect(component).toBeTruthy();
    });

    it('should remove minigame', async () => {
        let minigame = new Minigame(0, 'Test_NAME', null);
        spyOn(Alert, 'showConfirm').and.resolveTo(true);
        let spy = spyOn(minigameService, 'svcToRemove').and.resolveTo();
        spyOn(levelService, 'svcToModify').and.resolveTo();
        levelService.models = [new Level(0, 'TestID', null)];

        await component.removeMinigame(minigame);

        expect(spy).toHaveBeenCalledWith(minigame.id);
    });

});