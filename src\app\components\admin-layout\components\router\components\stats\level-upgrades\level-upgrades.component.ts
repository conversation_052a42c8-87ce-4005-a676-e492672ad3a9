import { Component, OnDestroy } from '@angular/core';
import { LevelUpgrade } from 'src/app/lib/@bus-tier/models/LevelUpgrade';
import { AreaService, LevelUpgradeService, ReviewService, UserSettingsService } from 'src/app/services';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc';
import { Button } from 'src/app/lib/@pres-tier/data';
import { Alert } from 'src/lib/darkcloud';
import { SpinnerService } from 'src/app/spinner/spinner.service';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-level-upgrades',
  templateUrl: './level-upgrades.component.html',
  styleUrls: ['./level-upgrades.component.scss'],
})
export class LevelUpgradeComponent extends SortableListComponent<LevelUpgrade> implements OnDestroy 
{
  timeout:any;
  levelUpgradeList: LevelUpgrade[] = [];
  description:string = '';

  constructor(
    private spinnerService: SpinnerService,
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    private _levelUpgradeService: LevelUpgradeService,
    private _areaService: AreaService,
    private _router: Router,
    private _reviewService: ReviewService
  ) 
  {
    super(_levelUpgradeService, _activatedRoute, _userSettingsService, 'name');
  }

  public readonly excelButtonTemplate: Button.Templateable = 
  {
    title: 'Paste content from excel',
    onClick: this.onLevelUpgradesPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  override async lstInit() 
  {
    await this._levelUpgradeService.toFinishLoading();
    this.levelUpgradeList = this._levelUpgradeService.models;
    this.fixDataAddingHCField();
  }

  fixDataAddingHCField()
  {
    for(let i = 0; i < this.levelUpgradeList.length; i++)
      if(this.levelUpgradeList[i].hc == undefined)
        this.levelUpgradeList[i].hc = this.levelUpgradeList[i].astralPlan;
    
    this.description = "Showing " + this.levelUpgradeList.length + " results";
  }

  async onLevelUpgradesPaste(): Promise<void> 
  {
    this.spinnerService.setState(true);
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter((line) => line);
    let lastAstralPlan = 0;
    let lastHC: number = 0;
    let lastPlayerLevel: number = 0; 
    //if (this.displayErrors(lines)) return;

    for (let l = 0; l < lines.length; l++) 
    {
      let line = lines[l];
      let rows = line.split(/\t/);

      if (rows.length < 6) continue;

      let hc;

      if(+rows[0] || rows[0] == '' || +rows[0] == 0)
      {
        if(rows[0] == '') hc = lastHC;
        if(rows[0]?.trim())
        {
          hc = rows[0];
          lastHC = hc;
        }
      }
      else hc = undefined;

      let playerLevel;
      if(rows[1]?.trim()) 
      {
        lastPlayerLevel = +rows[1];
        playerLevel = +rows[1];
      }
      else 
      {
        playerLevel = lastPlayerLevel+1;
        lastPlayerLevel = lastPlayerLevel + 1;
      }

      let astralPlan;
      if (rows[2]?.trim()) 
      {
        lastAstralPlan = +rows[2];
        astralPlan = +rows[2];
      } 
      else astralPlan = lastAstralPlan;
      
      let totalXP;
      if(rows[3]?.trim()) totalXP = parseFloat(rows[3].replace(/\./g, '').replace(',', '.'));
      else totalXP = undefined;

      let timeToUpgrade;
      if (rows[4]?.trim()) 
      {
        timeToUpgrade = +rows[4]
        .split(' ')
        .join('')
        .replace('.', '')
        .replace(',', '.');
      }
      else timeToUpgrade = undefined;

      let rubies;
      if (rows[5]?.trim()) 
      {
        rubies = +rows[5]
        .split(' ')
        .join('')
        .replace('.', '')
        .replace(',', '.');
      }
      else rubies = undefined;

      await this.AddLevelUpgrade(hc, playerLevel, astralPlan, totalXP, timeToUpgrade, rubies);
      Alert.ShowSuccess('Main Character Upgrade imported successfully!');
     }
   
    this.spinnerService.setState(false);
  }


  displayErrors(array) 
  {
    let count = array[0].split(/\t/);

    if (array[array.length - 1] === '\t\t\t\t\t') 
    {
      this.spinnerService.setState(false);
      Alert.showError('You are probably copying EMPTY lines');
      return true;
    }

    if (count.length < 6) 
    {
      this.spinnerService.setState(false);
      Alert.showError('You need to copy the CIRCULO DO INFERNO column values too!');
      return true;
    }

    if (count[0] === '') 
    {
      this.spinnerService.setState(false);
      Alert.showError('You are probably copying a blank column!');
      return true;
    }

    return false;
  }

  override simplifyString(str: string): string 
  {
    return str?.normalize('NFD')?.replace(/[\u0300-\u036f]/g, '')?.toLocaleUpperCase()?.trim();
  }

  async AddLevelUpgrade(hc: string, playerLevel: number, astralPlan: number, 
    totalXP: number, timeToUpgrade: number, rubies: number) 
  {
    let levelUpgrade: LevelUpgrade = this._levelUpgradeService.getByPlayerLevel(playerLevel);
    if (!levelUpgrade) 
    {
      levelUpgrade = await this._levelUpgradeService.svcPromptCreateNew(playerLevel);
      await this._levelUpgradeService.srvAdd(levelUpgrade);
    }

    for(let i = 0; i < this._areaService.models.length; i++)
    {
      if(+this._areaService.models[i].order == +hc) 
      {
        levelUpgrade.areaId = hc;
        break;
      }
    }

    levelUpgrade.playerLevel = playerLevel;
    levelUpgrade.astralPlan = astralPlan;
    levelUpgrade.totalXP = totalXP;
    levelUpgrade.timeToUpgrade = timeToUpgrade;
    levelUpgrade.rubies = rubies;
    levelUpgrade.hc = +hc;
    await this._levelUpgradeService.svcToModify(levelUpgrade);
  }

  async changeValue(levelUpgrade:LevelUpgrade, value:string, fieldName:string)
  {
    levelUpgrade[fieldName] = value ? +value : undefined;
    await this._levelUpgradeService.svcToModify(levelUpgrade);
    this.ngOnInit();
  }

  ngOnDestroy() 
  {
    clearInterval(this.timeout);
  }
}
