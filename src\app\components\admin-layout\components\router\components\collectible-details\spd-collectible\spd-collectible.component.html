<div style="margin-top: 20px; padding-bottom: 70px;" class="card">

    <ng-container >
        <div style="padding: 20px;">
            <table class="table table-list" style="width: 320px;">
                <thead>
                  <tr>
                    <th class="dark-gray" colspan="3">
                      <h4>SPD</h4>
                    </th>
                  </tr>
                  <tr>
                    <th class="one-Column">SPD.new</th>
                    <th class="ev_green" style="width: 50%;">EV(DEFSPD) (%)</th>
                    <th class="ev_orange" style="width: 50%;">PR(atkspd) (%)</th>
                  </tr>
                </thead>
                <tbody>
                  <ng-container *ngFor="let luk of listISPDCollectible.valuesInt; let i = index;">
                    <tr>
                      <td>
                          {{luk.qiNew}}
                      </td>
                      <td class="ev_green">
                          {{luk.ev_defluk ? luk.ev_defluk + '%' : ''}}
                      </td>
                      <td class="ev_orange">
                        {{luk.pr_atkluk ? luk.pr_atkluk + '%' : ''}}
                    </td>
                    </tr>
                  </ng-container>
                </tbody>
              </table>
              <div *ngIf="listISPDCollectible">
                <span><b>SPD_BASE (Velocidade):</b> {{spd ? spd : 'Sem valor no Primal Modifier'}}</span><br>
                <span><b>EV_BASE (Evasão):</b> {{ev_base ? ev_base : 'Sem valor no Primal Modifier'}}</span><br>
                <span><b>PR_BASE (Precisão):</b> {{pr_base ? pr_base : 'Sem valor no Primal Modifier'}}</span><br>
                <span><b>EV_MAX:</b> {{ev_max ? ev_max : 'Sem valor no EV-MAX'}}</span>
              </div>   
        </div>    
    </ng-container>

</div>





