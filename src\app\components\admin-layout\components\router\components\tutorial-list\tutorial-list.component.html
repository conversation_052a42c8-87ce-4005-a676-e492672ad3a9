<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons [cardTitle]="listName" [cardDescription]="cardDescription"
          [rightButtonTemplates]="[addButtonTemplate]"></app-header-with-buttons>
        <app-header-search (inputKeyup)="lstOnChangeFilter($event)"
          (searchOptions)="lstOnChangeFilterOptions($event)"></app-header-search>
      </div>
    </div>
    <!--List-->
    <div class="card">
      <table class="table table-list">
        <thead class="sticky">
          <tr>
            <th>Index</th>
            <th class="th-clickable" (click)="sortListByParameter('id')">ID</th>
            <th>Assigned</th>
            <th class="th-clickable" (click)="sortListByParameter('name')">Name & Description</th>
            <th class="th-clickable" (click)="sortListByParameter('notes')">Notes</th>
            <th>Assigned at</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="
                let tutorial of lstIds | tutorials;
                let i = index;
                trackBy: trackById
              ">
            <tr id="{{ tutorial.id }}">
              <td class="td-sort">{{ i + 1 }}</td>
              <td class="td-id">{{ tutorial.id }}</td>
              <td>
                <ng-container *ngIf="
                      (tutorial | review).assignedAt.length > 0;
                      else notAssigned
                    ">
                  <i style="position: relative" placement='top' delay='250' ttWidth="auto" ttAlign="left"
                    ttPadding="10px" tooltip="Assigned at {{
                        (tutorial | review).assignedAt | location
                      }}" class="pe-7s-check success"></i>
                </ng-container>
                <ng-template #notAssigned>
                  <i style="position: relative" placement='top' delay='250' ttWidth="auto" ttAlign="center"
                    ttPadding="10px" tooltip="Assigned in battle, but not a battle character"
                    class="pe-7s-attention attention"></i>
                </ng-template>
              </td>
              <td class="td-notes">
                <!--  <input class="form-control form-short"
                       type="text"
                       value="{{ tutorial.name }}"
                       #name
                       (change)="lstOnChange(tutorial, 'name', name.value)"
                       *ngIf="lstLanguage == 'PT-BR'"/> -->
                <input class="form-control form-short" type="text"
                  value="{{ (tutorial | translation : lstLanguage : tutorial.id : 'name') }}" #name
                  (change)="lstOnChange(tutorial, 'name', name.value)" />
                <!-- <textarea class="form-control"
                          type="text"
                          value="{{ tutorial.description }}"
                          #description
                          (change)="lstOnChange(tutorial, 'description', description.value)"
                          *ngIf="lstLanguage == 'PT-BR'">
                  </textarea> -->
                <textarea class="form-control" type="text"
                  value="{{ (tutorial | translation : lstLanguage : tutorial.id : 'description') }}" #description
                  (change)="lstOnChange(tutorial, 'description', description.value)">
                  </textarea>
              </td>
              <td class="td-notes">
                <textarea class="form-control borderless"
                  value="{{ (tutorial | translation : lstLanguage : tutorial.id : 'notes') }}" #notes
                  (change)="lstOnChange(tutorial, 'notes', notes.value)"></textarea>
              </td>
              <ng-container *ngIf="
                    (tutorial | review).assignedAt.length > 0;
                    else blankSpace
                  ">
                <ng-container *ngFor="let eventId of (tutorial | review).assignedAt">
                  <td>{{ ([eventId] | location).toString() }}</td>
                </ng-container>
              </ng-container>
              <ng-template #blankSpace>
                <td></td>
              </ng-template>
              <td class="td-actions">
                <button class="btn btn-danger btn-fill btn-remove" (click)="lstPromptRemove(tutorial)">
                  <i class="pe-7s-close"></i>
                </button>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>