<div class="content-weapon">
  <div class="card" style="margin-top: 20px;">
    <app-header-with-buttons
      [cardTitle]="title"
      [cardDescription]="description"
      [isBackButtonEnabled]="false">
    </app-header-with-buttons>

    <!-- Professional Informational Note -->
    <div class="info-note-professional">
      <div class="info-note-content">
        <i class="fa fa-info-circle info-icon"></i>
        <span class="info-text">
          Apenas exibe os Blueprints que foram efetivamente atribuídos na história.
        </span>
      </div>
    </div>
  </div>
  <br />
  <div class="content" style="margin-top: 85px;">
    <div class="m-cammon">
        <table class="table-bordered">
        <thead>
            <tr>
                <th>Index</th>
                <th >Name</th>
                <th class="th-clickable" (click)="orderSortCommonHC()">Hell Circle (HC)</th>
                <th class="light-blue-bg th-clickable" (click)="orderSortBluePrint()">Blue Print<br>Received HC</th>
                <th>Single Ingredient</th>
                <th>Special Weapon</th>
                <th>Qi-min<br>50 - 300</th>
                <th>Luck-min<br>1 - 12</th>
                <th>WLBase</th>
                <th>WL Range</th>
            </tr>
        </thead>
        <tbody>
            <ng-container *ngFor="let special of listHCSpecialWeapons; let i = index">       
                <ng-container *ngFor="let received of special.bluePrintReceivedHC; let j = index">
                    <tr>
                        <!-- Index -->
                        <td class="gray-bg" *ngIf="j === 0" [attr.rowspan]="special.bluePrintReceivedHC.length">{{i + 1}}</td>
                        
                        <!-- Name -->
                        <td style="padding-left: 10px; padding-right: 10px;" *ngIf="j === 0" [attr.rowspan]="special.bluePrintReceivedHC.length">{{special?.name}}</td>
    
                        <!-- Hell Circle (HC) -->
                        <td *ngIf="j === 0" [attr.rowspan]="special.bluePrintReceivedHC.length">{{special.hc}}</td>
                        
                        <!-- Blue Print Received HC -->
                        <td> 
                            <ng-container>
                                <p style="padding-top: 8px; text-align: left; padding-left: 10px; padding-right: 10px;">{{GetBluePrintName(received?.idNameBPR)}}</p>
                            </ng-container>                        
                        </td>
                             <!-- Single Ingredient -->
                             <td>                           
                                <p style="padding-top: 8px; text-align: left; padding-left: 10px; padding-right: 10px;">{{ GetIngredient(received?.idNameIngredient) }}</p>                               
                            </td>
                            
                            <!-- Special Weapon -->
                            <td>
                                <p style="padding-top: 8px; text-align: left; padding-left: 10px; padding-right: 10px;">{{ GetSpecialWeaponName(received?.idNameSW) }}</p>
                            </td>
                        
                        
                        <!-- Qi-min -->
                        <td >
                            <input placeholder=" " style="padding-left: 10px;" type="number" [value]="received?.qi_Min" #qiMin [ngClass]="{'empty-input': !qiMin?.value}"
                            (change)="onChangeCommonWeapons(special, qiMin?.value, 'qi_Min', j)">  
                        </td>
    
                        <!-- Luck-min -->
                        <td>
                            <input placeholder=" " style="padding-left: 10px;" type="number" [value]="received?.luck_min" #luckMin [ngClass]="{'empty-input': !luckMin?.value}"
                            (change)="onChangeCommonWeapons(special, luckMin.value, 'luck_min', j)">  
                        </td>
    
                        <!-- WLBase -->
                        <td>
                            <input placeholder=" " style="padding-left: 10px;" type="number" [value]="received?.wlBase" #wlBase [ngClass]="{'empty-input': !wlBase.value}"
                            (change)="onChangeCommonWeapons(special, wlBase.value, 'wlBase', j)">  
                        </td>
    
                        <!-- WL Range -->
                        <td [ngClass]="getWlRangeClass(special.hc)" *ngIf="j === 0" [attr.rowspan]="special.bluePrintReceivedHC.length">
                            {{valueRange}}
                         </td>
                    </tr>      
            </ng-container>
        </ng-container>
        </tbody>
    </table>
    </div>
  </div>
</div>

