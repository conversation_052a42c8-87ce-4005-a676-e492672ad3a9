import { Component } from '@angular/core';
import { Chest, Item } from 'src/app/lib/@bus-tier/models';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { TranslationService } from 'src/app/services/translation.service';
import { LanguageService } from 'src/app/services/language.service';
import { ChestService, ItemService } from 'src/app/services';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';
import { language } from 'src/lib/darkcloud/dialogue-system/game-types';
import { TranslationCheckService } from 'src/app/services/translation-check.service';
import { Alert } from 'src/lib/darkcloud';
import { ItemClassService } from 'src/app/services/item-class.service';
import { ItemClass } from 'src/app/lib/@bus-tier/models/ItemClass';
import { ActivatedRoute } from '@angular/router';


@Component({
  selector: 'app-chest-list',
  templateUrl: './chest-list.component.html',
})

export class ChestListComponent extends TranslatableListComponent<Chest> 
{
  public language: language = 'PT-BR';
  public chestList: Chest[] = [];
  sortByAcronymOrder = -1;
  sortByChestOrder = -1;
  sortByCharacterOrder = -1;
  characters:string[] = ['Minions', 'Boss e Subbosses'];
  description: string = '';
  choosedChest:string = '';

  constructor(
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    private _chestService: ChestService,
    protected override _translationService: TranslationService,
    protected override _languageService: LanguageService,
    private _translationCheckService: TranslationCheckService,
    public _itemClassService: ItemClassService,
    private _itemService: ItemService
    )
  {
    super(_chestService, _activatedRoute, _userSettingsService, 'name', _translationService, _languageService);
  }

  override async ngOnInit()
  {
    await this._chestService.toFinishLoading();
    this.choosedChest = this.choosedChestDropdown();
    setTimeout(()=>
    {
      this.addItemToChest();
    }, 700)
  }

  choosedChestDropdown():string
  {
    for(let i = 0; i < this._chestService.models.length; i++)
      if(this._chestService.models[i].acronym) return this._chestService.models[i].chestType;

      return 'All';
  }

  async addItemToChest()
  {
    this.chestList = this._chestService.models;
    //1. If item is not in chess add it.
    for(let i = 0; i < this._itemClassService.models.length; i++)
    {
      if(!this.isItemInChest(this._itemClassService.models[i]))
      {
        for(let j = 0; j < this._itemClassService.models[i].itemIds.length; j++)
        {
          let item:Item = this._itemService.svcFindById(this._itemClassService.models[i].itemIds[j]);
          let chest: Chest = await this._chestService.svcPromptCreateNew(item.name, 
            this._itemClassService.models[i].name);
          this.chestList.push(chest);
        }
      }
    }
    this.description = `Showing ${this.chestList.length} results`;
    this.filterChestBasedOnType();
  }

  isItemInChest(chest:ItemClass): boolean
  {
    for(let i = 0; i < this.chestList.length; i++)
      if(chest.name == this.chestList[i].chestType) return true;

    return false;
  }

  filterChestBasedOnType()
  {
    if(this.choosedChest != 'All')
      this.chestList = this.chestList.filter(chest => chest.chestType == this.choosedChest);
    this.description = `Showing ${this.chestList.length} results`;
  }

  async onChangeChest(ches:string, chest:Chest, fieldName: string)
  {
    if(this.isANewChest(chest, fieldName))
    {
      let answer = await Alert.showConfirm('DANGER', `You already have created a chest using 
        ${this.chestType()} item type. You will lose all the data!`, 'OK');
      
      if(!answer)
      {
        chest.acronym = undefined;
        await this._chestService.svcToModify(chest); 
        return;
      }
      await this.clearAllChestFields();      
      chest[fieldName] = ches;
      await this._chestService.svcToModify(chest);  
    }
    else
    {
      chest[fieldName] = ches;
      await this._chestService.svcToModify(chest);  
    }
  }

  chestType():string
  {
    for(let i = 0; i < this._chestService.models.length; i++)
      if(this._chestService.models[i].acronym != undefined &&
         this._chestService.models[i].acronym != '') return this._chestService.models[i].chestType;
    return '';
  }
  
  isANewChest(chest:Chest, fieldName:string): boolean
  {
    if(fieldName == 'acronym')
    for(let i = 0; i < this._chestService.models.length; i++)
      if(this._chestService.models[i].acronym != undefined && this._chestService.models[i].acronym != '' && 
        this._chestService.models[i].chestType !== chest.chestType) return true;      
    return false;
  }

  async clearAllChestFields()
  {
    for(let i = 0; i < this._chestService.models.length; i++)
    {
      this._chestService.models[i].acronym = '';
      this._chestService.models[i].unlockTime = undefined;
      this._chestService.models[i].characterType = '';
      await this._chestService.svcToModify(this._chestService.models[i]);
    }
  }

  onSelectNewChest(item)
  {
    let aux:Chest[] = []; 
    this.chestList = [];
    if(item == 'All') 
    {
      this.chestList = this._chestService.models;
      this.description = `Showing ${this.chestList.length} results`;
      return;
    }
    for(let i = 0; i < this._chestService.models.length; i++)
      if(this._chestService.models[i].chestType == item) aux.push(this._chestService.models[i]);

    this.chestList = aux;
    this.description = `Showing ${this.chestList.length} results`;
  }

  async removeElement(chest : Chest)
  {
    const confirm = await Alert.showRemoveAlert(chest.acronym);  
    if (!confirm) return;
    
    this._chestService.models = await this._chestService.models.filter(s => s.id !== chest.id);
    await this._chestService.toSave();
    this.chestList = this.chestList.filter(s => s.id !== chest.id);
  }

  search(searchWord)
  {
    let aux = this._chestService.models;
    this.chestList = [];
    this.chestList = aux.filter(chest => 
    {
      if(chest.chestName?.includes(searchWord) || chest.acronym?.includes(searchWord) ||
        chest.chestType?.includes(searchWord)) return true;
      
      return false;
    });
    if(searchWord == '') this.chestList = aux;
    this.description = `Showing ${this.chestList.length} results`;
  }

  sortByChest() 
  {
    this.sortByChestOrder *= -1;
    this.chestList.sort((a, b) => 
    {
      if(!a.chestName && b.chestName) return 1;
      if(a.chestName && !b.chestName) return -1;
      if(!a.chestName && !b.chestName) return 0;
      return this.sortByChestOrder * a.chestName?.localeCompare(b.chestName);
    });
  }
 
  sortByAcronym() 
  {
    this.sortByAcronymOrder *= -1;
    this.chestList.sort((a, b) => 
    {
      if(!a.acronym && b.acronym) return 1;
      if(a.acronym && !b.acronym) return -1;
      if(!a.acronym && !b.acronym) return 0;
      return this.sortByAcronymOrder * a.acronym.localeCompare(b.acronym);
    });
  }

  sortByChestType() 
  {
    this.sortByCharacterOrder *= -1;
    this.chestList.sort((a, b) => 
    {
      if(!a.chestType && b.chestType) return 1;
      if(a.chestType && !b.chestType) return -1;
      if(!a.chestType && !b.chestType) return 0 ;
      return this.sortByCharacterOrder * a.chestType?.localeCompare(b.chestType);
    });
  }
}
