.btn-undefined {
    background-color: transparent !important;
    border-color: #888 !important;
    color: #888 !important;
    font-weight: 400;
    opacity: .8;
    text-transform: capitalize;
   // margin-bottom: 1.1px;
   // margin-top: 1.1px;
  }
  .btn:hover, .btn:focus {
    opacity: 1;
    outline: 0 !important;
    background-color: transparent;
    color: #777;
    border-color: #777;
}

.battle {
  float: left;
  padding-left: 10%;
  text-align-last: center;
  margin-left: 10px;
}

.pe-7s-info {
  font-size: 20px !important;
  position: relative;
  top: 3px;
  margin-left: 7%;
}
/*
*[data-tooltip] 
{
  position: relative;
}
*/
*[data-tooltip]::after 
{
  content: attr(data-tooltip);
   width: 200px;
   padding: 8px;
   z-index: 10;
   text-align: center !important;
}

*[data-tooltip]:hover::after 
{
  opacity: 1;
}

.padId {
  padding-left: 15px !important;
  padding-right: 15px !important;
}

/*Modal do sistema */
.item-overlay
{
    position: absolute;
    //border: 15px solid black;
    height: 100%;
    width: 100%;
    background-color: rgb(230, 230, 230);
    z-index: 150;
}

.popup-report
{ 
  border-radius: 8px;
  width: 400px;
  position: fixed;
 // left: 35%;
  padding: 24px;
  top: 50%;
 // transform: translate(-60%, -50%);
 transform: translate(-10%, -40%);
  z-index: 1000;
  opacity: 1;  

}
.modal-header {
  color: white !important;
  padding: 0px !important;

  .modal-title {
    text-align: left !important;
    margin-bottom: 7px;
    .close {
      opacity: 0px !important;
      margin-top: 0px !important;
    }
  }

   button {
    span {
      color: white !important;
    }
   }
}

.close {
  opacity: 1 !important;
 }

 .contextBattle {
  color: white;
  text-align: left;
  overflow-y: auto;
  white-space: pre-wrap;
  height: auto;
  max-height: 700px;
  scrollbar-width: thin;
  scroll-behavior: auto;  
  scrollbar-color: white black;
}
.background-div {	
  position: fixed;
  z-index: 9999;	
}	

.background-div.popup-open:before 	
{	
  content: "";	
  position: fixed;	
  top: 0;	
  left: 0;	
  width: 100%;	
  height: 100%;	
  background-color: rgba(0, 0, 0, 0.5);	
  z-index: 9998;	
  pointer-events: none;	
}	

// FIM DO MODAL´
.ball-circle {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #2196F3;
  border: 2px solid #FFFFFF;
  float: right;
}

