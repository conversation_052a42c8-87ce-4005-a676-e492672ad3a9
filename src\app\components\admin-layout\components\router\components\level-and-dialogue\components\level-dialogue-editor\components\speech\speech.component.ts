import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { Subscription } from 'rxjs/internal/Subscription';
import {
  Character,
  Emotion,
  Sound,
  Speech,
} from 'src/app/lib/@bus-tier/models';
import { CharacterService } from 'src/app/services/character.service';
import { ClassService } from 'src/app/services/class.service';
import { EmotionService } from 'src/app/services/emotion.service';
import { EventHandlerService } from 'src/app/services/eventHandler.service';
import { PopupService } from 'src/app/services/popup.service';
import { SoundService } from 'src/app/services/sound.service';
import { SpeechService } from 'src/app/services/speech.service';
import { Voices } from 'src/lib/darkcloud/dialogue-system';
import { RpgContentInputComponent } from '../rpg-content-input/rpg-content-input.component';
import { AIPromptService } from 'src/app/services/ai-prompt.service';
import { Popup } from 'src/lib/darkcloud';
import { HighlightElement } from 'src/lib/others';
;

@Component({
  selector: 'tr[app-speech]',
  templateUrl: './speech.component.html',
})
export class SpeechComponent implements OnInit {
  @Input() index: number;
  @Input() speechId: string;
  @Input() toMove: (speech: Speech, transpose: number) => void;
  @Input() toRemove: (speech: Speech) => void;
  @Input() preloadedSpeakers: Character[];
  @Input() language: string;
  @ViewChild(RpgContentInputComponent)
  preloadedEmotions: Emotion[];
  HighlightElement = HighlightElement;
  speech: Speech;
  rpgContentInput: RpgContentInputComponent;
  rpgSubscription: Subscription;
  selectType: string;


  constructor(
    private _speechService: SpeechService,
    private _emotionService: EmotionService,
    private _characterService: CharacterService,
    private _popupService: PopupService,
    private _soundService: SoundService,
    private _classService: ClassService,
    private _eventsHandlerService: EventHandlerService, 
    private _aiPromptService: AIPromptService,

  ) {}

  ngOnInit(): void {
    this._aiPromptService.toFinishLoading();
    this.speech = this._speechService.svcCloneById(this.speechId);
    this.preloadedEmotions = this._emotionService.models;
    this.rpgSubscription = this._eventsHandlerService
      .OnRpgWordAdded()
      .subscribe((object) => {
        if (this.speech.message?.includes(object.word)) {
          this.speech = this._speechService.svcCloneById(this.speechId);
        }
      });
  
  }

  public async onChange() {
    await this._speechService.svcToModify(this.speech);
  }

  public async onChangeText(value, property: string) {
    this.speech[property] = value || parseInt(value, 10);
    this.speech.isReviewedMessage = false;
    this.speech.revisionCounterMessageAI = 0;
    await this._speechService.svcToModify(this.speech);
  }

  clickOpenAIPrompt(speech: Speech, typeSelect: string) {
    this.selectType = typeSelect;
    this.speech = speech;
  }

  onModalClose() {
    this.selectType = '';
  }

  public async toPromptSelectSpeaker(speech: Speech) {
    const selectedBtn = await this._popupService.fire<Character, Character>(
      new Popup.Interface(
        {
          title: 'Select Speaker',
          actionsClass: 'column',
        },
        Popup.toButtonList(this.preloadedSpeakers, 'name'),
        {
          hideButton: {
            value: this._characterService.svcFindById(speech.speakerId),
          },
        }
      )
    );

    if (!selectedBtn) return;

    speech.speakerId = selectedBtn.value?.id;
    await this._speechService.svcToModify(speech);
  }

  public async toPromptSelectEmotion(speech: Speech) {
    const selectedBtn = await this._popupService.fire<Emotion, Emotion>(
      new Popup.Interface(
        {
          title: 'Select Emotion',
          actionsClass: 'column',
        },
        Popup.toButtonList(this._emotionService.models, 'name', {
          undefinedTitle: 'No Emotion',
        }),
        {
          hideButton: {
            value: this._emotionService.svcFindById(speech.emotionId),
          },
        }
      )
    );

    if (!selectedBtn) return;

    speech.emotionId = selectedBtn.value?.id;
    await this._speechService.svcToModify(speech);
  }

  public async toPromptSelectAudio(speech: Speech) {
    const speaker = this._characterService.svcFindById(speech.speakerId);
    const voices = this._classService.svcFindById(speaker.classId).voices;
    await this._soundService.toFinishLoading();
    let selectedBtn;

    if (voices >= Voices.HUMAN_AND_NONHUMAN) {
      selectedBtn = await this._popupService.fire<Voices, Voices>(
        new Popup.Interface(
          {
            title: 'Select Voice',
            actionsClass: 'column',
          },
          Popup.voiceButtons.filter(
            (t) => speech.emotionId || t.value == Voices.NONHUMAN
          ),
          {
            next: (selectedVoiceBtn) => {
              if (!selectedVoiceBtn) return null;

              const voice = selectedVoiceBtn.value;
              return new Popup.Interface(
                {
                  title: 'Select Audio',
                  actionsClass: 'column',
                },
                this._soundService.getButtonsFor(
                  speaker,
                  speech.emotionId,
                  voice
                ),
                {
                  inputButton: {
                    value: this._soundService.svcFindById(speech.audioId),
                  },
                }
              );
            },
          }
        )
      );
      if (!selectedBtn) return;
    } else {
      selectedBtn = await this._popupService.fire<Sound, Sound>(
        new Popup.Interface(
          {
            title: 'Select Audio',
            actionsClass: 'column',
          },
          this._soundService.getButtonsFor(speaker, speech.emotionId, voices),
          {
            inputButton: {
              value: this._soundService.svcFindById(speech.audioId),
            },
          }
        )
      );
    }

    if (!selectedBtn) return;
    speech.audioId = selectedBtn.value?.id;
    await this._speechService.svcToModify(speech);
  }
  ngOnDestroy() {
    // prevent memory leak when component is destroyed
    this.rpgSubscription.unsubscribe();
  }
}
