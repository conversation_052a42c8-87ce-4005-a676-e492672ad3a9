import { Component, EventEmitter, Output } from '@angular/core';
import { DispelTable } from 'src/app/lib/@bus-tier/models';
import { Button } from 'src/app/lib/@pres-tier/data';
import { DispelTableService } from 'src/app/services';
import { Alert } from 'src/lib/darkcloud';

@Component({
  selector: 'app-dispel-table',
  templateUrl: './dispel-table.component.html',
  styleUrls: ['./dispel-table.component.scss']
})
export class DispelTableComponent {

  
  titles = ['ID', 'CATEGORY', 'SKILL RESIST USER','STATUS EFFECT NAME', 'DESCRIPTION', 'POWER POINTS (PP)', 'ALL'];
  listDispelTable: DispelTable[] = [];
  activeLanguage = 'PTBR';
  @Output() activeTab2 = new EventEmitter<string>();
  isListDispelEmpty: boolean;
 
  public readonly excelButtonTemplate: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };
  constructor(
    private _dispelTableService: DispelTableService
  ){}
 
 
  async ngOnInit(): Promise<void>{
    
      this.removeEmptyItems();
       this.listDispelTable = this._dispelTableService.models;
       this.isListDispelEmpty = this.listDispelTable.length === 0;  
 
    }
 
    removeEmptyItems() {
      this._dispelTableService.toFinishLoading();
      this._dispelTableService.models = this._dispelTableService.models.filter(dispelItem => dispelItem.idDispel !== "");
      this._dispelTableService.toSave();
    }

    async onExcelPaste() {
      const text = await navigator.clipboard.readText();
      const lines = text.split(/\r?\n/).filter(line => line);    
      const processedData: string[][] = [];
    
      if (lines.length > 0) {
        lines.forEach(line => {
          // Divide cada linha em colunas e remove a primeira coluna
          const values = line.split("\t").map(value => value.trim()).slice(1);
    
          processedData.push(values);
        });
    
        // Verifica se o número de colunas em cada linha corresponde ao número de colunas em this.titles
        const isColumnCountValid = processedData.every(row => row.length === this.titles.length);
    
        if (!isColumnCountValid) {
          Alert.showError('Invalid number of columns');
          return;
        }
  
        this._dispelTableService.models = [];
        this._dispelTableService.toSave();
    
        for (let index = 0; index < processedData.length; index++) {
          this._dispelTableService.createNewnDispelTable(processedData[index]);
        }    
 
        Alert.ShowSuccess('Dispel Table imported successfully!');
        this.activeTab2.emit('dispelTable');
        this.ngOnInit();
      }
    }
     
    changeDispel(rowIndex: number, name: string, newValue: string){
 
      if (name === 'idDispel') {
       this.listDispelTable[rowIndex].idDispel = newValue;        
      }
      else if (name === 'category') {
        this.listDispelTable[rowIndex].category = newValue;        
       }
       else if (name === 'skillResistUser') {
        this.listDispelTable[rowIndex].skillResistUser = newValue;        
       }
       else if (name === 'statusEffectName') {
        this.listDispelTable[rowIndex].statusEffectName = newValue;        
       }
       else if (name === 'description') {
        this.listDispelTable[rowIndex].description = newValue;        
       }
       else if (name === 'powerPoints') {
        this.listDispelTable[rowIndex].powerPoints = newValue;        
       }
       else if (name === 'allDispel') {
        this.listDispelTable[rowIndex].allDispel = newValue;        
       }
 
      this._dispelTableService.svcToModify(this.listDispelTable[rowIndex]);
    }    
 
}
