import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class DiceColorService {
  private diceColorMap: Map<string, string> = new Map();
  
  // Color palette for dice pairs - distinct colors for visual variety
  private colorPalette: string[] = [
    '#FF6B6B', // Red
    '#45B7D1', // Blue
    '#96CEB4', // Green
    '#FFEAA7', // Yellow
    '#DDA0DD', // Purple
    '#98D8C8', // Mint
    '#F7DC6F', // Gold
    '#BB8FCE', // Lavender
    '#85C1E9', // Sky Blue
    '#F8C471', // Orange
    '#82E0AA', // Light Green
    '#F1948A', // Pink
    '#AED6F1', // Light Blue
    '#D7BDE2'  // Light Purple
  ];
  
  private usedColorIndices: Set<number> = new Set();

  constructor() {}

  /**
   * Get a unique color for a dice pair (option + its failure variant).
   * Uses the option ID as the key to ensure both success and failure get the same color.
   */
  getColorForDicePair(optionId: string): string {
    if (!optionId || optionId.trim() === '') {
      return '#CCCCCC'; // Default gray for invalid IDs
    }

    const normalizedOptionId = optionId.trim();
    
    // Return existing color if already assigned
    if (this.diceColorMap.has(normalizedOptionId)) {
      return this.diceColorMap.get(normalizedOptionId)!;
    }

    // Find next available color
    let colorIndex = 0;
    while (this.usedColorIndices.has(colorIndex) && colorIndex < this.colorPalette.length) {
      colorIndex++;
    }

    // If we've used all predefined colors, generate a random one
    let color: string;
    if (colorIndex >= this.colorPalette.length) {
      color = this.generateRandomColor();
    } else {
      color = this.colorPalette[colorIndex];
      this.usedColorIndices.add(colorIndex);
    }

    // Store the mapping
    this.diceColorMap.set(normalizedOptionId, color);
    return color;
  }

  /**
   * Get color for a dice failure variant by finding its parent option
   */
  getColorForDiceFailure(failureStoryBoxId: string, parentOptionId: string): string {
    if (!parentOptionId) {
      return '#CCCCCC';
    }
    
    return this.getColorForDicePair(parentOptionId);
  }

  /**
   * Generate a random color when predefined palette is exhausted
   */
  private generateRandomColor(): string {
    const hue = Math.floor(Math.random() * 360);
    const saturation = 60 + Math.floor(Math.random() * 40); // 60-100%
    const lightness = 50 + Math.floor(Math.random() * 30);  // 50-80%
    return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
  }

  /**
   * Remove a dice pair color mapping (useful when options are deleted)
   */
  removeDiceColor(optionId: string): void {
    if (!optionId) return;
    
    const normalizedOptionId = optionId.trim();
    const color = this.diceColorMap.get(normalizedOptionId);
    
    if (color) {
      // Find and free up the color index if it was from predefined palette
      const colorIndex = this.colorPalette.indexOf(color);
      if (colorIndex !== -1) {
        this.usedColorIndices.delete(colorIndex);
      }
      
      this.diceColorMap.delete(normalizedOptionId);
    }
  }

  /**
   * Get all currently assigned dice pair colors
   */
  getAllDiceColors(): Map<string, string> {
    return new Map(this.diceColorMap);
  }

  /**
   * Clear all color assignments (useful for testing or reset)
   */
  clearAllColors(): void {
    this.diceColorMap.clear();
    this.usedColorIndices.clear();
    console.log('🎲 Dice color cache cleared - colors will be reassigned');
  }
}
