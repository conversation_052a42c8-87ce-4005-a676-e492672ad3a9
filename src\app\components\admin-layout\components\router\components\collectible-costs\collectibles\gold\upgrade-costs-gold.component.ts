import { ChangeDetectorRef, Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Character, CollectibleRarityGold } from 'src/app/lib/@bus-tier/models';
import { Button } from 'src/app/lib/@pres-tier/data';
import { CollectibleRarityGoldService, TierService } from 'src/app/services';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Alert } from 'src/lib/darkcloud';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/SortableListComponent';

@Component({
  selector: 'app-upgrade-costs-gold',
  templateUrl: './upgrade-costs-gold.component.html',
  styleUrls: ['./upgrade-costs-gold.component.scss'],

})

export class UpgradeCostsGoldComponent extends SortableListComponent<CollectibleRarityGold> 
{
  tierList:string[] = [];
  collectibles:CollectibleRarityGold[] = [];

  constructor(
    _activatedRoute: ActivatedRoute,
    protected _collectibleRarityGoldService: CollectibleRarityGoldService,
    _userSettingsService: UserSettingsService,
    protected _languageService: LanguageService,
    private _tierListService: TierService,
    protected _translationService: TranslationService,
    private ref: ChangeDetectorRef,
  ) 
  {
    super(_collectibleRarityGoldService, _activatedRoute, _userSettingsService, 'name');
  }
  
  public readonly excelButtonTemplate: Button.Templateable = 
  {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  protected override async lstInit() 
  {
    await this._tierListService.toFinishLoading();
    await this._collectibleRarityGoldService.toFinishLoading();
    this.collectibles = this._collectibleRarityGoldService.models;
    this.tierList = this._tierListService.fillRarityArrayDynimically('Character Rarity', this.tierList).filter(x => x != 'Inferior');
    this.getListChacterRarity();
  }

  getListChacterRarity() 
  {  
  // Array auxiliar para definir na ordem
  const orderDesired = ["Elementar", "Comum", "Raro", "Épico", "Lendário"];  
  this.tierList.sort((a, b) => orderDesired.indexOf(a) - orderDesired.indexOf(b)); 
  }

  public downloadSceneryOrtography(character: Character) 
  {
    this._translationService.getCharacterOrtography(character, true);
  }

  async changeBlockDrops(codeBlockDrop: CollectibleRarityGold, value: string, fieldName:string)
  {
    codeBlockDrop.hard[fieldName] = value == ''? undefined : +value;
    await this._collectibleRarityGoldService.svcToModify(codeBlockDrop);
    await this._collectibleRarityGoldService.toSave();
    this.ref.detectChanges();
  }

  async onExcelPaste(): Promise<void> {
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter(line => line);
  
    let collectibleRarityCodeBlocksFields: string[] = [];
    this._collectibleRarityGoldService.models = [];
  
    for (let i = 0; i < this.tierList.length; i++) {
      collectibleRarityCodeBlocksFields.push(this.tierList[i].toLowerCase() + 'GoldAmount');
    }  
    
    for (let l = 0; l < lines.length; l++) {
      const line = lines[l];
      const cols = line.split(/\t/);  

      let collectibleRarityGold = this._collectibleRarityGoldService.models.find((laboratory) =>
        laboratory.bossLevel ===
        +cols[0].split(' ').join('').split('.').join('').replace(',', '.')
      );
  
      if (!collectibleRarityGold) {
        const labLevel = +cols[0].split(' ').join('').split('.').join('').replace(',', '.');
        collectibleRarityGold = this._collectibleRarityGoldService.createNewCollectibleRarityGold(labLevel);
      }
  
      for (let i = 0; i < collectibleRarityCodeBlocksFields.length; i++) {    
        if (cols[i + 1]?.trim()) {
          collectibleRarityGold.hard[collectibleRarityCodeBlocksFields[i]] = +cols[i + 1].split(' ')
            .join('')
            .split('.')
            .join('')
            .replace(',', '.')
            .replace('%', '');
        } else {    
          collectibleRarityGold.hard[collectibleRarityCodeBlocksFields[i]] = undefined;
        }
      }  

      this._collectibleRarityGoldService.svcToModify(collectibleRarityGold);
    }

    this._collectibleRarityGoldService.toSave(); 
    Alert.ShowSuccess("Collectible Rarity Gold imported successfully!");
    this.collectibles = this._collectibleRarityGoldService.models;
    this.ref.detectChanges();
  }
  
  displayErrors(array)
  {
    let count = array[0].split(/\t/)
    if(count.length < this.tierList.length)
    {
      Alert.showError("Copy the BOSS LEVEL column values too!")
      return true
    }
    
    if(count[0] === "")
    {
      Alert.showError("You are probably copying a blank column!")
      return true
    }

    return false
  }
}
