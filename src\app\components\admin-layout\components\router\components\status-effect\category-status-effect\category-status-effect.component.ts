import { Component, EventEmitter, Output } from '@angular/core';
import { Category } from '../../../../../../../lib/@bus-tier/models/Category';
import { CategoryStatusEffectService } from '../../../../../../../services';
import { TranslationService } from '../../../../../../../services/translation.service';

@Component({
  selector: 'app-category-status-effect',
  templateUrl: './category-status-effect.component.html',
  styleUrls: ['./category-status-effect.component.scss']
})
export class CategoryStatusEffectComponent {

  public description:string = '';
  listCategory: Category[] = [];
  @Output() descriptionOutput = new EventEmitter<string>();
  listElementAffinities = [{id: '01', name: 'Resist'}, {id: '02', name: 'Weak'}];

  constructor(
    private _categoryStatusEffectService: CategoryStatusEffectService,
    protected _translationService: TranslationService,
  ) {

  }

  public async ngOnInit(): Promise<void> 
{
  this._categoryStatusEffectService.toFinishLoading();
  this. listCategory = this._categoryStatusEffectService.models; 
  this.descriptionOutput.emit(`Showing ${this.listCategory.length} results`);
}

  async changeStatusEffectValue(index: number, value:string, fieldName: string)
    {
        if (fieldName === 'category') {
          this.listCategory[index].category = value === '' ? null : value;
        } else if (fieldName === 'typology') {
          this.listCategory[index].typology = value === '' ? null : value;
        } else if (fieldName === 'description') {
          this.listCategory[index].description = value === '' ? null : value; 
        }
    
        this._categoryStatusEffectService.svcToModify(this.listCategory[index]);
        this._categoryStatusEffectService.toSave();
        this. listCategory = this._categoryStatusEffectService.models;
        this.descriptionOutput.emit(`Showing ${this.listCategory.length} results`); 
  }

  filterRelation(index: number,relation: string) {
    this.listCategory[index].relation = relation;
    this._categoryStatusEffectService.svcToModify(this.listCategory[index]);
    this._categoryStatusEffectService.toSave();
    this. listCategory = this._categoryStatusEffectService.models;
  }
    removeLineCategory(index: number) {

      const itemToRemove = this.listCategory[index];

      if (itemToRemove && itemToRemove.id) {
        this._categoryStatusEffectService.svcToRemove(itemToRemove.id);
      }

      this.listCategory.splice(index, 1); // Remove o item da lista local
      this.ngOnInit(); 
    }

    public getModifierOrtography(status: Category)
    {
      this._translationService.getCategoryOrtography(status, true);
    }


}


