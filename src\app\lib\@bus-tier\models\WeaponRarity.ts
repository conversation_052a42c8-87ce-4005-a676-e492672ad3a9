import { IdPrefixes} from 'src/lib/darkcloud/dialogue-system';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { Base } from './Base';

export class WeaponRarity  extends Base<Data.Hard.IWeaponRarity, Data.Result.IWeaponRarity>  implements Required<Data.Hard.IWeaponRarity>
{
  private static generateId(index: number): string {
    return IdPrefixes.WEAPONRARITY + index;
  }
  constructor(
    index: number,
    name: string,
    dataAccess: WeaponRarity['TDataAccess']
  ) {
    super(
      {
        hard: {
          id: WeaponRarity.generateId(index),
          name,
        },
      },
      dataAccess
    );
  }
  protected getInternalFetch() {
    return {};
  }
  

  public get baseLevel(): number
  {
    return this.hard.baseLevel;
  }
  public set baseLevel(value: number)
  {
    this.hard.baseLevel = value;
  }

  public get availablesSilicatesSlots(): number
  {
    return this.hard.availablesSilicatesSlots;
  }
  public set availablesSilicatesSlots(value: number)
  {
    this.hard.availablesSilicatesSlots = value;
  }

  public get name(): string
  {
    return this.hard.name;
  }
  public set name(value: string)
  {
    this.hard.name = value;
  }

  public get stars(): number
  {
    return this.hard.stars;
  }
  public set stars(value: number)
  {
    this.hard.stars = value;
  }

  public get rarityId(): string
  {
    return this.hard.rarityId;
  }
  public set rarityId(value: string)
  {
    this.hard.rarityId = value;
  }


}
