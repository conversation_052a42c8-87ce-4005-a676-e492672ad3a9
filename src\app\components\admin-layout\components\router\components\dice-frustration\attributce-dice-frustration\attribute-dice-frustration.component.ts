import { ChangeDetectorRef, Component } from '@angular/core';
import { Alert } from '../../../../../../../../lib/darkcloud';
import { Button } from '../../../../../../../lib/@pres-tier/data';
import { AttributeDiceFrustrationService, UserSettingsService } from '../../../../../../../services';
import { AttributeDiceFrustration } from 'src/app/lib/@bus-tier/models';
import { TranslationService } from 'src/app/services/translation.service';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-attributce-dice-frustration',
  templateUrl: './attribute-dice-frustration.component.html',
  styleUrls: ['./attribute-dice-frustration.component.scss']
})
export class AtributteDiceFrustrationComponent {

  titles = ['LIGHT', 'MODERATE', 'CRITICAL'];
  description: string;
  activeLanguage = 'PTBR';
  listFrustrationLevels: AttributeDiceFrustration[] = [];

  public readonly excelButtonTemplate: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  constructor(
        _activatedRoute: ActivatedRoute,
        _userSettingsService: UserSettingsService,
    public ref: ChangeDetectorRef,
    public _attributediceFrustrationService: AttributeDiceFrustrationService,
    protected _translationService: TranslationService,
  ) { 

  }


  async ngOnInit(): Promise<void> {

    this._attributediceFrustrationService.toFinishLoading();
    setTimeout(() => {
      this.listFrustrationLevels = this._attributediceFrustrationService.models;
      this.description = `Showing ${this.listFrustrationLevels.length} results`;
    }, 60);

  }

  async onExcelPaste() {
    try {
      const text = await navigator.clipboard.readText();
      const lines = text.split(/\r?\n/).filter((line) => line);

      this._attributediceFrustrationService.models = [];
      this._attributediceFrustrationService.toSave();

      for (const line of lines) {
        const values = line.split('\t');
        const nameSubContext = values[0];

        if (values.length !== this.titles.length) {
          Alert.showError(
            `Error: Number of columns in the imported data does not match the expected (${this.titles.length}).`
          );
          return;
        }

        if (nameSubContext) {
          this.listFrustrationLevels = [];
          this.listFrustrationLevels.push(
            await this._attributediceFrustrationService.createNewDiceFrustrationLevels()
          );
        }

        for (let index = 0; index < this.listFrustrationLevels.length; index++) {
          if (nameSubContext || nameSubContext !== "") {
            this.listFrustrationLevels[index].subContext = nameSubContext;
            this.listFrustrationLevels[index].light.push(values[1]);
            this.listFrustrationLevels[index].moderate.push(values[2]);
            this.listFrustrationLevels[index].critical.push(values[3]);
          }
          else {
            this.listFrustrationLevels[index].light.push(values[1]);
            this.listFrustrationLevels[index].moderate.push(values[2]);
            this.listFrustrationLevels[index].critical.push(values[3]);
          }

          this._attributediceFrustrationService.svcToModify(this.listFrustrationLevels[index]);
          this._attributediceFrustrationService.toSave();
        }
      }

      this.ref.detectChanges();
      Alert.ShowSuccess("Attribute Dice Frustration list copied successfully!");
      this.ngOnInit();
    }
    catch (error) {
      Alert.showError("Error importing data from Excel.");
      console.error(error);
    }
  }

  changeFrustrationValue(rowIndex: number, rowSub: number, name: string, newValue: string) {
    if (name === 'light') {
      this.listFrustrationLevels[rowIndex].light[rowSub] = newValue == '' ? null : newValue;
      this.listFrustrationLevels[rowIndex].isReviewedLight[rowSub] = false;
      this.listFrustrationLevels[rowIndex].revisionCounterLightAI[rowSub] = 0;
    } else if (name === 'moderate') {
      this.listFrustrationLevels[rowIndex].moderate[rowSub] = newValue == '' ? null : newValue;
      this.listFrustrationLevels[rowIndex].isReviewedModerate[rowSub] = false;
      this.listFrustrationLevels[rowIndex].revisionCounterModerateAI[rowSub] = 0;
    } else if (name === 'critical') {
      this.listFrustrationLevels[rowIndex].critical[rowSub] = newValue == '' ? null : newValue;
      this.listFrustrationLevels[rowIndex].isReviewedCritical[rowSub] = false;
      this.listFrustrationLevels[rowIndex].revisionCounterCriticalAI[rowSub] = 0;
    }
    this._attributediceFrustrationService.svcToModify(this.listFrustrationLevels[rowIndex]);
  }

  downloadAttributeDiceOrtography(attribute: AttributeDiceFrustration, index: number) {
    this._translationService.getAttributeDiceFrustrationOrtography(attribute, true, index);
  }

}
