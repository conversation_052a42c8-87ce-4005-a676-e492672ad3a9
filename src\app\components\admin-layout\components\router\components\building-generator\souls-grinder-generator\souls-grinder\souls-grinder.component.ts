import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/SortableListComponent';
import { ChangeDetectorRef, Component } from '@angular/core';
import { Character} from 'src/app/lib/@bus-tier/models';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Button } from 'src/app/lib/@pres-tier/data';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { SoulsGrinderService } from 'src/app/services';
import { Alert } from 'src/lib/darkcloud';
import { SoulsGrinder } from 'src/app/lib/@bus-tier/models/SoulsGrinder';
import { SpinnerService } from './../../../../../../../../spinner/spinner.service';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-souls-grinder',
  templateUrl: './souls-grinder.component.html',
})
/**
 * Displays and edits emotion data as a list
 */
export class SoulsGrinderComponent extends SortableListComponent<SoulsGrinder> {
  constructor(
    private spinnerService: SpinnerService,
    _activatedRoute: ActivatedRoute,
    protected _soulsGrinderService: SoulsGrinderService,
    _userSettingsService: UserSettingsService,
    protected _languageService: LanguageService,
    protected _translationService: TranslationService,
    private ref: ChangeDetectorRef,

  ) {
    super(_soulsGrinderService, _activatedRoute, _userSettingsService, 'name');
  }
  description = ""
  public readonly excelButtonTemplate: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };

  public downloadSceneryOrtography(character: Character)
  {
    this._translationService.getCharacterOrtography(character, true);
  }

  protected override lstInit()
  {

  }

  protected override lstAfterFetchList()
  {
    let soulsTypeCAmount = []
    if(this._soulsGrinderService.models.filter(model => model.type === "0").length === 0)
    {
      for(let l = 1; l <= 20; l++)
      {
        this._soulsGrinderService.createNewSoul(l, "0")
      }
      this._soulsGrinderService.toSave();
      this.lstFetchLists();
    }
    //remove empty element that just has lablevel == 0.
    this._soulsGrinderService.models.find(blueprint => 
      {
        if(blueprint.soulsLevel === 0)          
        this._soulsGrinderService.svcToRemove(blueprint.id)
      })
      soulsTypeCAmount = this._soulsGrinderService.models.filter(model => model.type === "0")
    this.description = `Showing ${soulsTypeCAmount.length} results`;
   
  }

  async onExcelPaste(): Promise<void>
  {
    this.spinnerService.setState(true)
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter(line => line);
    
   if(this.DisplayErrors(lines)) return
   
    for(let l = 0; l < lines.length; l++)
    {
      let line = lines[l];
      let cols = line.split(/\t/);
           

      let soulsGrinder = this._soulsGrinderService.models.find(tm => tm.soulsLevel == +(cols[0].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.')) && tm.type === "0");

      if(!soulsGrinder)
      {
        soulsGrinder = this._soulsGrinderService.createNewSoul(+(cols[0].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.')), "0");
      }
      
      if(cols[1]?.trim())
      {
        soulsGrinder.titaniumCost = +(cols[1].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.'));
      }
      else
      {
        soulsGrinder.titaniumCost = undefined;
      }
      if(cols[2]?.trim())
      {
        soulsGrinder.requiredTime = +(cols[2].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.'));
      }
      else
      {
        soulsGrinder.requiredTime = undefined;
      }
      if(cols[3]?.trim())
      {
        soulsGrinder.rubiesSkipCost = +(cols[3].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.'));
      }
      else
      {
        soulsGrinder.rubiesSkipCost = undefined;
      }
      if(cols[4]?.trim())
      {
        soulsGrinder.localStorage = +(cols[4].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.'));
      }
      else
      {
        soulsGrinder.localStorage = undefined;
      }
      if(cols[5]?.trim())
      {
        soulsGrinder.productionPerTime = +(cols[5].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.'));
      }
      else
      {
        soulsGrinder.productionPerTime = undefined;
      }
      if(cols[6]?.trim())
      {
        soulsGrinder.goldSkipCost = +(cols[6].split(' ')
        .join('')
        .split('.')
        .join('')
        .replace(',','.'));
      }
      else
      {
        soulsGrinder.goldSkipCost = undefined;
      }

      await   this._soulsGrinderService.svcToModify(soulsGrinder);
      await  this._soulsGrinderService.toSave();
      Alert.ShowSuccess('Souls Grinder imported successfully!');

    }
    this.lstFetchLists();
    this.ref.detectChanges();
    this.spinnerService.setState(false)
  }
  DisplayErrors(array)
  {
    let count = array[0].split(/\t/)
    if(count.length < 7)
    {
      Alert.showError("Copy the SOULS GRINDER LEVEL column values too!")
      this.spinnerService.setState(false)
      return true
    }
    
    if(count[0] === "")
    {
      Alert.showError("You are probably copying a blank column!")
      this.spinnerService.setState(false)
      return true
    }

    return false
  }

  sortBySoulsLevelOrder = -1;
  sortBySoulsLevel() {
    this.sortBySoulsLevelOrder *= -1;
    this._soulsGrinderService.models.sort((a, b) => {
      if (!a.soulsLevel && b.soulsLevel) return 1;
      if (a.soulsLevel && !b.soulsLevel) return -1;
      if (!a.soulsLevel && !b.soulsLevel) return 0;
      return this.sortBySoulsLevelOrder * (a.soulsLevel - b.soulsLevel);
    });
    this.lstFetchLists();

  }

  sortByTitaniumCostOrder = -1;
  sortByTitaniumCost() {
    this.sortByTitaniumCostOrder *= -1;
    this._soulsGrinderService.models.sort((a, b) => {
      if (!a.titaniumCost && b.titaniumCost) return 1;
      if (a.titaniumCost && !b.titaniumCost) return -1;
      if (!a.titaniumCost && !b.titaniumCost) return 0;
      return this.sortByTitaniumCostOrder * (a.titaniumCost - b.titaniumCost);
    });
    this.lstFetchLists();

  }

  sortByBuildingTimeOrder = -1;
  sortByBuildingTime() {
    this.sortByBuildingTimeOrder *= -1;
    this._soulsGrinderService.models.sort((a, b) => {
      if (!a.requiredTime && b.requiredTime) return 1;
      if (a.requiredTime && !b.requiredTime) return -1;
      if (!a.requiredTime && !b.requiredTime) return 0;
      return this.sortByBuildingTimeOrder * (a.requiredTime - b.requiredTime);
    });
    this.lstFetchLists();

  }

  sortByRubiesCostOrder = -1;
  sortByRubiesCost() {
    this.sortByRubiesCostOrder *= -1;
    this._soulsGrinderService.models.sort((a, b) => {
      if (!a.rubiesSkipCost && b.rubiesSkipCost) return 1;
      if (a.rubiesSkipCost && !b.rubiesSkipCost) return -1;
      if (!a.rubiesSkipCost && !b.rubiesSkipCost) return 0;
      return this.sortByRubiesCostOrder * (a.rubiesSkipCost - b.rubiesSkipCost);
    });
    this.lstFetchLists();

  }

  sortByLocalStorageOrder = -1;
  sortByLocalStorage() {
    this.sortByLocalStorageOrder *= -1;
    this._soulsGrinderService.models.sort((a, b) => {
      if (!a.localStorage && b.localStorage) return 1;
      if (a.localStorage && !b.localStorage) return -1;
      if (!a.localStorage && !b.localStorage) return 0;
      return this.sortByLocalStorageOrder * (a.localStorage - b.localStorage);
    });
    this.lstFetchLists();

  }

  sortByProductionTimeOrder = -1;
  sortByProductionTime() {
    this.sortByProductionTimeOrder *= -1;
    this._soulsGrinderService.models.sort((a, b) => {
      if (!a.productionPerTime && b.productionPerTime) return 1;
      if (a.productionPerTime && !b.productionPerTime) return -1;
      if (!a.productionPerTime && !b.productionPerTime) return 0;
      return this.sortByProductionTimeOrder * (a.productionPerTime - b.productionPerTime);
    });
    this.lstFetchLists();

  }

  sortByGoldCostOrder = -1;
  sortByGoldCost() {
    this.sortByGoldCostOrder *= -1;
    this._soulsGrinderService.models.sort((a, b) => {
      if (!a.goldSkipCost && b.goldSkipCost) return 1;
      if (a.goldSkipCost && !b.goldSkipCost) return -1;
      if (!a.goldSkipCost && !b.goldSkipCost) return 0;
      return this.sortByGoldCostOrder * (a.goldSkipCost - b.goldSkipCost);
    });
    this.lstFetchLists();

  }

  changeTitaniumCost(soul: SoulsGrinder, value: number) {
    soul.titaniumCost = value;
    this._soulsGrinderService.svcToModify(soul);
    this._soulsGrinderService.toSave();
  }

  changeRequiredTime(soul: SoulsGrinder, value: number) {
    soul.requiredTime = value;
    this._soulsGrinderService.svcToModify(soul);
    this._soulsGrinderService.toSave();
  }

  changeRubiesCost(soul: SoulsGrinder, value: number) {
    soul.rubiesSkipCost = value;
    this._soulsGrinderService.svcToModify(soul);
    this._soulsGrinderService.toSave();
  }

  changeLocalStorage(soul: SoulsGrinder, value: number) {
    soul.localStorage = value;
    this._soulsGrinderService.svcToModify(soul);
    this._soulsGrinderService.toSave();
  }

  changeProductionTime(soul: SoulsGrinder, value: number) {
    soul.productionPerTime = value;
    this._soulsGrinderService.svcToModify(soul);
    this._soulsGrinderService.toSave();
  }

  changeGoldCost(soul: SoulsGrinder, value: number) {
    soul.goldSkipCost = value;
    this._soulsGrinderService.svcToModify(soul);
    this._soulsGrinderService.toSave();
  }
 
 
}
