import { ChangeDetectorRef, Component, EventEmitter,Input, Output } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Character, Dialogue, DilemmaBox, Level, Mission, OptionBox, Speech, StoryBox } from 'src/app/lib/@bus-tier/models';
import { AIPrompt } from 'src/app/lib/@bus-tier/models/AIPrompt';
import { AnswerDilemmaBoxService, DialogueService, DilemmaBoxService, DilemmaService, EmotionService, OpenAIEnvironmentService, OpenAiPromptService, OptionBoxService, OptionService, SpeechService, StoryBoxService } from 'src/app/services';
import { AIPromptService } from 'src/app/services/ai-prompt.service';
import { EasyMVC } from 'src/lib/darkcloud/angular';
import { HighlightElement } from 'src/lib/others';

export interface Preloadable {
  dialogue: Dialogue;
  level: Level;
  speakers: Character[];
  missions: Mission[];
  preloadedCharacters: Character[];
}

export interface IArrayOption {
  name: string;
  details: string;
  idEnvironmentAi: string;
  prompt: string;
}

@Component({
  selector: 'app-modal-ai-speeches',
  templateUrl: './modal-ai-speeches.component.html',
  styleUrls: ['./modal-ai-speeches.component.scss']
})
export class ModalAiSpeechesComponent extends EasyMVC.PreloadComponent<Preloadable> {

   @Input() speech: Speech;
   @Input() selectType: string;
   @Output() closeModalEvent = new EventEmitter();
   HighlightElement = HighlightElement;
   popupStats = false;
   responsePrompt = [];
   selectedPrompt: string = '';
   description: string = '';
   btnSelected: any;
   openModalGeneral = false;
   selectedOption ='';
   title: string;
   arrayOptions = [];
   selectOption: IArrayOption;
   selectBoxIds = [];
   storyProgressIds = [];
   messageDialogues = [];
   preloadedSpeakers: Character[];
   sendCharaterMessage: string;
   character: string;
   emotion: string;

   constructor(
    private _activatedRoute: ActivatedRoute,
    private _aiPromptService: AIPromptService,
    private _openAiPromptService: OpenAiPromptService,
    private _change: ChangeDetectorRef,
    private _speechService: SpeechService,
    private _dialogueService: DialogueService,
    private _optionBoxService: OptionBoxService,
    private _storyBoxService: StoryBoxService,
    private _optionService: OptionService,
    private _dilemmaBoxService: DilemmaBoxService,
    private _dilemmaService: DilemmaService,
    private _answerDilemmaBoxService: AnswerDilemmaBoxService,
    private _emotionService: EmotionService,
    private _openAIEnvironmentService: OpenAIEnvironmentService,
   ) {
    super(_activatedRoute);
    }
   
//Método que verifica se o clique foi feito fora do componente e fecha ele.
  ngAfterViewInit(): void {
      setTimeout(() => {
        document.addEventListener('click', this.handleClickOutside.bind(this));
      });
    }

  handleClickOutside(event: MouseEvent): void {
  const target = event.target as Node;

  const ids = ['component_Grammar_creative', 'Component_geral', 'component-confirm'];
  const isInsideAnyComponent = ids.some(id => {
    const el = document.getElementById(id);
    return el && el.contains(target);
  });

  // Se não estiver dentro de nenhum dos componentes, fecha o modal
  if (!isInsideAnyComponent) {
    this.closeModal();
  }
}
        

   ngOnInit() { 
    this._aiPromptService.toFinishLoading();
    this.preloadedSpeakers = this.preloadedData.speakers;
    this.cleanVariables();
    this.addExtraInfoToSpeech();    
    this.popupStats = true;

    if (this.selectType === 'Ghostwriter') {
      this.title = this.selectType;    
      this.getSpeechInformation(); 
      this.getDataFromAIPrompt();
    }      
      else {
        this.getDataFromAIPrompt();
      }   
   }

   cleanVariables() {
    this.responsePrompt = [];
    this.selectedPrompt = '';
    this.selectedOption = '';
    this.description = '';
    this.title = '';   
    this.btnSelected = '';
    this.selectOption = null;
    this.popupStats = false;
    this.openModalGeneral = false;
   }

   addExtraInfoToSpeech() {    
    this.character = this.preloadedSpeakers.find((speaker) => speaker.id === this.speech.speakerId)?.name;
     this.emotion = '(' + this._emotionService.svcFindById(this.speech?.emotionId)?.name + '): ';
    this.sendCharaterMessage = this.character + ' ' + this.emotion + this.speech.message; 
   }

getSpeechInformation() {
    this.messageDialogues = [];
    this.storyProgressIds = [];

   const idDialogue = this.speech.id.replace(/^(.+?\..+?\..+?)\..*$/, '$1'); // pega o id do dialogue
   const dialogue=  this._dialogueService.svcFindById(idDialogue);
   const selectId = this.speech.id.replace(/^(.+?\..+?\..+?\..+?)\..*$/, '$1'); //pega o id do componente dentro do dialogue

   /*
    Nesse código, o método indexOf() é usado para encontrar a posição do selectId dentro do array dialogueBoxIds. 
    Se o selectId for encontrado, o método slice() é usado para pegar os demais IDs que existem na posição acima e adicionar no array this.selectBoxIds recebendo assim os ids anteriores ao selectId.
  */
   const index = dialogue.boxIds.indexOf(selectId);
   if (index !== -1) {
     this.selectBoxIds = dialogue.boxIds.slice(0, index + 1);
   }
  
   this.selectBoxIds.forEach((id) => {
    const box = this._storyBoxService.svcFindById(id) || this._optionBoxService.svcFindById(id) || this._dilemmaBoxService.svcFindById(id);

    if (box instanceof StoryBox) {

    //Remove o id selecionado do array storyProgressIds e adiciona os ids anteriores ao array storyProgressIds
      const indexStoryProgressIds = box.storyProgressIds.indexOf(this.speech.id);
      if (indexStoryProgressIds !== -1) {
        this.storyProgressIds = box.storyProgressIds.slice(0, indexStoryProgressIds);

        this.storyProgressIds.forEach((id) => {
          const speech = this._speechService.svcFindById(id);
            
          if (speech) {
            this.addInfoToSpeech(speech);
            }
          });  
         return;         
      }
       else {
        box.storyProgressIds.forEach((id) => {
          const speech = this._speechService.svcFindById(id);
            
            if (speech) {
              this.addInfoToSpeech(speech);
            }
        });           
     }  
}
else if (box instanceof OptionBox) {

  for (let i = 0; i < box.optionIds.length; i++) {
    const option = this._optionService.svcFindById(box.optionIds[i]);

    // Remove o id selecionado do array storyProgressIds e adiciona os ids anteriores ao array storyProgressIds
    let answer = this._storyBoxService.svcFindById(option.answerBoxId);
    const indexStoryProgressIds = answer?.storyProgressIds.indexOf(this.speech.id);

    if (indexStoryProgressIds === 0) {
      return;
    }

    if (indexStoryProgressIds && indexStoryProgressIds !== -1) {
      this.storyProgressIds = answer?.storyProgressIds.slice(0, indexStoryProgressIds);
      this.storyProgressIds = this.storyProgressIds.filter((id) => id !== this.speech.id);
    
      this.storyProgressIds.forEach((id) => {
        const speech = this._speechService.svcFindById(id);

        if (speech) {
          this.addInfoToSpeech(speech);
        }
      });
    
      return;
    } else {
      answer?.storyProgressIds.forEach((id) => {
        const speech = this._speechService.svcFindById(id);

        if (speech) {
          this.addInfoToSpeech(speech);
        }
      });
    }

    // AnswerBox Negative
    if (option?.answerBoxNegativeId) {
      let negativeAnswer = this._storyBoxService.svcFindById(option.answerBoxNegativeId);
      const indexProgressIds = negativeAnswer?.storyProgressIds.indexOf(this.speech.id);

      if (indexProgressIds === 0) {
        return;
      }
      
      if (indexProgressIds && indexProgressIds !== -1) {
        this.storyProgressIds = answer?.storyProgressIds.slice(0, indexProgressIds);
        this.storyProgressIds = this.storyProgressIds.filter((id) => id !== this.speech.id);

        this.storyProgressIds.forEach((id) => {
          const speech = this._speechService.svcFindById(id);
  
          if (speech) {
            this.addInfoToSpeech(speech);
          }
        });
       
        return
      } else {
        negativeAnswer?.storyProgressIds.forEach((id) => {
          const speech = this._speechService.svcFindById(id);
  
          if (speech) {
            this.addInfoToSpeech(speech);
          }
        });
      }
    }
  }
}
     //Dilemma Box
     else if (box instanceof DilemmaBox) {
      for (let i = 0; i < box.optionDilemmaIds.length; i++) {
        const dilemma = this._dilemmaService.svcFindById(box.optionDilemmaIds[i]);
        let answerDilemma = this._answerDilemmaBoxService.svcFindById(dilemma.idDilemmaBox);
        const indexStoryProgressIds = answerDilemma?.storyProgressIds.indexOf(this.speech.id);
        
        if (indexStoryProgressIds === 0) {
          return;
        }

        if (indexStoryProgressIds && indexStoryProgressIds !== -1) {
          this.storyProgressIds = answerDilemma?.storyProgressIds.slice(0, indexStoryProgressIds);
          this.storyProgressIds = this.storyProgressIds.filter((id) => id !== this.speech.id);  

          this.storyProgressIds.forEach((id) => {
            const speech = this._speechService.svcFindById(id);
  
            if (speech) {
              this.addInfoToSpeech(speech);
            }
          });
          
          return;
        } else {
          answerDilemma?.storyProgressIds.forEach((id) => {
            const speech = this._speechService.svcFindById(id);
  
            if (speech) {
              this.addInfoToSpeech(speech);
            }
          });
        }      
      }            
     }
   });
   this.messageDialogues.push({ text: this.character + ' ' + this.emotion});
   }

   addInfoToSpeech( speech: Speech) {
    const character =  this.preloadedSpeakers.find((speaker) => speaker.id === speech.speakerId)?.name + ' ';
    const emotion = '(' + this._emotionService.svcFindById(speech?.emotionId)?.name + '): ';
    const message = speech?.message;
    this.messageDialogues.push({ text: `${character} ${emotion} ${message}` });
   }

   getDescription(selectType: string) {
    this.description = this._aiPromptService.models.find((x) => x.selectType === selectType)?.description;
   }

  async getGhostwriterAIPrompt(selectPrompt: IArrayOption) {
    this.responsePrompt = [];

   this._openAiPromptService.getChatGhostwriterResponse(selectPrompt, this.messageDialogues).subscribe({
    next: (data) => { 
    const textOriginal = data.choices?.[0]?.message?.content || '';    
    this.responsePrompt = this.limparRespostaBruta(textOriginal);
    },
    error: (error) => {
      console.error('Erro ao obter resposta:', error);
    },
    complete: () => { 
     // console.log('RESPOSTA DO CHATGPT no REFRESH', this.responsePrompt);
      this._change.detectChanges();
    }
  }); 
}

limparRespostaBruta(resposta: string): string[] {
  return resposta
    .split('\n')
    .map(linha => linha
      .trim()
      .replace(/^[-*\d.\s]*\s*/g, '') // remove numeração, bullets
      .replace(/^.*?:\s*/, '')        // remove "Personagem: "
    )
    .filter(linha => linha.length > 0);
}


async getAIPrompt(speech: Speech, selectPrompt: IArrayOption) {
  this.responsePrompt = [];

 this._openAiPromptService.getChatResponse(selectPrompt, speech.message).subscribe({
  next: (data) => { 
  const content = data.choices[0].message.content;

    if (content.includes('\n')) {
      const lines = content.split('\n');
      this.responsePrompt = lines.map(line => {
        const trimmedLine = line.trim().replace(/^"|"$/g, '').replace(/"/g, '');
        return trimmedLine !== '' ? { text: trimmedLine } : null; //verfi se a linha é vazia
      }).filter(line => line !== null); //remover linhas vazias
    } else {
      this.responsePrompt = [{ text: content.trim().replace(/^"|"$/g, '').replace(/"/g, '') }];
    }
 
  },  
  error: (error) => {
    console.error('Erro ao obter resposta:', error);
  },
  complete: () => { 
    this._change.detectChanges();
  }
}); 
}

getDataFromAIPrompt() {

  this._aiPromptService.models.forEach((x) => {
    if (x.selectType === this.selectType && x?.promptName) {
      this.arrayOptions.push({
        name: x.promptName,  
        details: x.description,
        idEnvironmentAi: x.idEnvironmentAi,
        prompt: x.prompt
      });
    }
  });
  this.sortArrayOptionsByName();
}

sortArrayOptionsByName() {
  this.arrayOptions.sort((a, b) => {
    return a.name.localeCompare(b.name);
  });
}

clickOption(option: IArrayOption) {
  this.title = option.name;  
  this.selectedOption = option.name;
  this.description = option.details;
  this.selectOption  = option;
  this.openModalGeneral = true;
  this._change.detectChanges();

  if (this.selectType === 'Ghostwriter') {
    this.getGhostwriterAIPrompt(option);
  } else {
     this.getAIPrompt(this.speech, option);
  }
 
}

ClickButton(messsage: string) {
  this.btnSelected = messsage;
}

refreshPopup() {
  if (this.selectType === 'Ghostwriter') {
    this.getGhostwriterAIPrompt(this.selectOption);
  }
   else {
    this.getAIPrompt(this.speech, this.selectOption);
   } 
}

confirmChange() {
  this.speech.message = this.btnSelected;
  this._speechService.svcToModify(this.speech);
  this._change.detectChanges();
  HighlightElement(this.speech?.id, 10, true)
  this.closeModal();
}

cancel(){
  this.btnSelected = '';
}

closeModal() {
  this.selectType = '';
  this.cleanVariables();
  this.closeModalEvent.emit();
}

ngOnDestroy(): void {
  document.removeEventListener('click', this.handleClickOutside.bind(this));
}



}
