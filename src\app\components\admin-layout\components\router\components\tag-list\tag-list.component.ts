import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Tag } from 'src/app/lib/@bus-tier/models/Tag';
import { But<PERSON> } from 'src/app/lib/@pres-tier/data';
import { ReviewService, UserSettingsService } from 'src/app/services';
import { LanguageService } from 'src/app/services/language.service';
import { TagService } from 'src/app/services/tag.service';
import { TranslationService } from 'src/app/services/translation.service';
import { Alert } from 'src/lib/darkcloud';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc';

@Component({
  selector: 'app-tag-list',
  templateUrl: './tag-list.component.html',
  styleUrls: ['./tag-list.component.scss']
})
export class TagListComponent extends SortableListComponent<Tag> implements OnDestroy{
  timeout:any
  public readonly addTagButton: Button.Templateable = {
    title: 'Add a new tag to the list',
    onClick: this.createTag.bind(this),
    iconClass: 'pe-7s-plus',
    btnClass: Button.Klasses.FILL_GREEN,
  };

  constructor(
    _activatedRoute: ActivatedRoute,
    private _tagService: TagService,
    _userSettingsService: UserSettingsService,
    private _router: Router,
    private _reviewService: ReviewService,
    protected _translationService: TranslationService,
    protected _languageService: LanguageService
  )
  {
    super(_tagService, _activatedRoute, _userSettingsService, 'name');
  }

  override async  lstInit()
  {
    if(!(this.lstIds?.length <= 0))
      return;

    await this._tagService.toFinishLoading();
    this.lstIds = this._tagService.models.map(x => x.id);
  }

  public async createTag()
  {
    let newTag;
    try {
      newTag = await this._tagService.svcPromptCreateNew();
    } catch (e) {
      Alert.showError("Tag já existe!");
    }
    if(!newTag)
      return;

    this._tagService.srvAdd(newTag);
    this._tagService.modelIds.push(newTag.id)

    this.refreshList();
  }

  redirectToItemClasses()
  {
    this._router.navigate(['itemClass']);
  }

  updateColor(model, value, colorLabel: HTMLElement)
  {
    this.updateInformation(model, 'hex', value);
    colorLabel.style.backgroundColor = value;
  }

  refreshList(scroll?: boolean)
  {
    let listProxy = this.lstIds;
    this.lstIds = [];
   this.timeout = setTimeout(() => {
      this.lstIds = listProxy
      this._tagService.svcReviewAll()
    }, 1);
  }

  ngOnDestroy() {
    clearInterval(this.timeout)
   
  }

}
