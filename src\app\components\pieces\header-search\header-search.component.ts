import { Component, EventEmitter, OnInit, Output } from '@angular/core';

export interface SearchOptions
{
  caseSensitive: boolean,
  accentSensitive: boolean
}

@Component({
  selector: 'app-header-search',
  templateUrl: './header-search.component.html',
  styleUrls: ['./header-search.component.scss'],
})
export class HeaderSearchComponent {
  @Output() inputKeyup = new EventEmitter<string>();
  @Output() searchOptions = new EventEmitter<SearchOptions>();
  caseSensitive = false;
  accentSensitive = false;

  public searchOptionsChanged()
  {
    this.searchOptions.emit({
      caseSensitive: this.caseSensitive,
      accentSensitive: this.accentSensitive
    });
  }

  public accentSensitiveChanged($event)
  {
    this.accentSensitive = $event;
    this.searchOptionsChanged();
  }
  public caseSensitiveChanged($event)
  {
    this.caseSensitive = $event;
    this.searchOptionsChanged();
  }
}
