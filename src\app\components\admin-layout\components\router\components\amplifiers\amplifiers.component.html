<div style="margin-top: 20px; margin-left: 30px; margin-right: 30px;">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card" style="padding-bottom: 10px;">
        <app-header-with-buttons 
          [cardTitle]="'Skill Tree Amplifiers'"
          [cardDescription]="description">
        </app-header-with-buttons>
      </div>
      <ng-container *ngIf="amplifiersList.length > 0">
        <table class="table-list">
            <thead class="sticky">
              <tr>
                <th colspan="2" style="font-size: 20px;">Skill Tree Amplifiers</th>
              </tr>
            </thead>
            <tbody>     
                <tr *ngFor="let amplifier of amplifiersList; let i = index">
                  <!-- Nome do amplificador -->
                  <td style="position: relative; background-color: rgb(107, 107, 107); text-align: center; padding-bottom: 1px; color:aliceblue">
                    {{ amplifier?.nameAmplifier }}
                  </td>                  
                  <!-- Campo de input dinâmico -->
                  <td class="border-td">
                    <input class="background-input-table-color form-control form-short"
                      placeholder=" "
                      type="number"
                      #amplifierInput
                      [value]="amplifier?.valueAmplifier"
                      (change)="changeAplifier(i, amplifierInput.value)"
                      [ngClass]="{'empty-input': !amplifierInput.value}"
                      style="border-style:solid; width: 100%; text-align: center;"
                    />
                  </td>
                </tr>

            </tbody>
          </table>
      </ng-container>

    </div>
  </div>
