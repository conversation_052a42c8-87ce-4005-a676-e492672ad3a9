<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">

        <div class="card-header-content" style="position: absolute; top: 10%;">
          <h3 class="title">{{ cardTitle }}</h3>
          <p class="category">{{ description}}</p>
        </div>

        <div style="display: grid; align-items: end; justify-content: end;">
          <div style="margin-right: 110px; margin-bottom: 16px;">
            <button (click)="switchToTab('bonus')"
              class="{{activeTab === 'bonus' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}">
              BONUS
            </button>
            <button (click)="switchToTab('condition')"
              class="{{activeTab === 'condition' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}" style="margin-left: 2px;">
              CONDITION (Trigger)
            </button>
            <button (click)="switchToTab('duration')"
              class="{{activeTab === 'duration' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}" style="margin-left: 2px;">
              DURATION
            </button>
            <button (click)="switchToTab('passiveAllowed')"
              class="{{activeTab === 'passiveAllowed' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}" style="margin-left: 2px;">
              PASSIVE ALLOWED FOR WEAPONS
            </button>
          </div>
          <div style="position: relative; margin-bottom: 3px;">
            <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'" class="add-buttons"
              [buttonTemplates]="[excelButtonTemplate]">
            </app-button-group>
          </div>
        </div>
      </div>
    </div>
  </div>

  <app-bonus *ngIf="activeTab === 'bonus'" (descrptionOutput)="receiveText($event)"></app-bonus>
  <app-condition *ngIf="activeTab === 'condition'" (descrptionOutput)="receiveText($event)"></app-condition>
  <app-duration *ngIf="activeTab === 'duration'" (descrptionOutput)="receiveText($event)"></app-duration>
  <app-passive-allowed *ngIf="activeTab === 'passiveAllowed'"
    (descrptionOutput)="receiveText($event)"></app-passive-allowed>