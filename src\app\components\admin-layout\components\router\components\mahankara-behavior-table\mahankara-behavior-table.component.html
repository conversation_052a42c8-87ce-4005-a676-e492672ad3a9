<ng-container *ngIf="listMahankaraBehavior.length > 0">
    <div style="display: flex; flex-direction: row; overflow-x: auto; width: 100%;">
        <table class="table table-list borderList">
            <thead>
                <tr>            
                    <th *ngFor="let title of titles"> {{title}}</th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let item of listMahankaraBehavior; let i = index">                                
                        <td class="td-id aligTitle">
                            <input class="background-input-table-color form-control form-short noCursor" placeholder=" " disabled
                                type="number" #mahankaraUpgrade [ngClass]="{'empty-input': !mahankaraUpgrade.value}"
                                [value]="item.mahankaraUpgrade" (change)="changeMahankaraValue(i,'mahankaraUpgrade', mahankaraUpgrade.value)" />
                        </td>
                        <td class="td-id aligTitle">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="number" #bLAddition [ngClass]="{'empty-input': !bLAddition.value}"
                                [value]="item.bLAddition" (change)="changeMahankaraValue(i,'bLAddition', bLAddition.value)" />
                        </td>
                        <td class="td-id aligTitle">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="number" #partyMemberBLAddition [ngClass]="{'empty-input': !partyMemberBLAddition.value}"
                                [value]="item.partyMemberBLAddition" (change)="changeMahankaraValue(i, 'partyMemberBLAddition', partyMemberBLAddition.value)" />
                        </td>
                        <td class="td-id aligTitle">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="number" #turnDuration [ngClass]="{'empty-input': !turnDuration.value}" 
                                [value]="item.turnDuration" (change)="changeMahankaraValue(i, 'turnDuration', turnDuration.value)" />
                        </td>    
                </tr>
            </tbody>
        </table>
    </div>
</ng-container>


<ng-container *ngIf="listMahankaraBehavior.length === 0">
    <div class="card" style="text-align: center; padding: 20px;">
        <h3>Empty list. Click to create the list.</h3>
    </div>
</ng-container>
