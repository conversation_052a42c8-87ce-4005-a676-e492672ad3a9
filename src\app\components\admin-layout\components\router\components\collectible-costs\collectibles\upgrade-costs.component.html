
    <div style="position: relative; top: -55px; margin-right: 40px;">
            <div *ngIf="active === 'upgrade'"
            style="position:relative; float:right">
           <button style="position:relative; float:right"
                   class="{{active3 === 'gold' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                   (click)="switchToTab2('gold')">
               Gold
           </button>
           <button style="position:relative; float:right; margin-right: 2px;"
                   class="{{active3 === 'code' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                   (click)="switchToTab2('code')">
               Code Blocks
           </button>
       </div>
       <div *ngIf="active === 'ascension'"
            style="position:relative; float:right">
           <button style="position:relative; float:right"
                   class="{{active3 === 'souls' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                   (click)="switchToTab2('souls')">
               Souls
           </button>
       </div>
    </div>
        


<app-upgrade-costs-gold *ngIf="active3 === 'gold'" ></app-upgrade-costs-gold>
<app-upgrade-costs-code-blocks *ngIf="active3 === 'code'" ></app-upgrade-costs-code-blocks>
<app-upgrade-costs-souls *ngIf="active3 === 'souls'" ></app-upgrade-costs-souls>