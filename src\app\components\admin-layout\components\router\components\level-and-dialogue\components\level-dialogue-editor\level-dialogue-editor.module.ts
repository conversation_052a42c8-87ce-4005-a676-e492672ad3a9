import { RpgFormattingPopupComponent } from './components/rpg-formatting-popup/rpg-formatting-popup.component';
import { RpgContentInputComponent } from './components/rpg-content-input/rpg-content-input.component';
import { SpeechComponent } from './components/speech/speech.component';
import { StoryBoxComponent } from './components/story-box/story-box.component';
import { MarkerComponent } from './components/marker/marker.component';
import { EventComponent } from './components/event/event.component';
import { AnswerBoxComponent } from './components/answer-box/answer-box.component';
import { LevelDialogueEditorComponent } from './level-dialogue-editor.component';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BrowserModule } from '@angular/platform-browser';
import { FormsModule } from '@angular/forms';
import { MajorModule } from 'src/app/major.module';
import { ContentInputModule } from 'src/app/components/content-input/content-input.module';
import { PopupModule } from 'src/app/components/popup/popup.module';
import { AudioListenerModule } from 'src/app/components/audio-listener/audio-list.module';
import { PiecesModule } from 'src/app/components/pieces/pieces.module';
import { RoadblockComponent } from './components/roadblock/roadblock.component';
import { DialogueTreeViewComponent } from '../dialogue-tree-view/dialogue-tree-view';
import { DialogueTreeLayerComponent } from '../dialogue-tree-view/components/DialogueTreeLayer/dialogue-tree-layer';
import { DialogueTreeBranchComponent } from '../dialogue-tree-view/components/DialogueTreeBranch/dialogue-tree-branch';
import { DialogueTreeLeafComponent } from '../dialogue-tree-view/components/DialogueTreeLeaf/dialogue-tree-leaf';
import { RoadblockInfoComponent } from '../dialogue-tree-view/components/RoadblockInfo/roadblock-info.component';
import { DialogueSimulatorComponent } from '../dialogue-simulator/dialogue-simulator.component';
import { DialogueBoxSimulatorComponent } from '../dialogue-simulator/components/dialogue-box-simulator.component';
import { DiceOverlayComponent } from 'src/app/components/shared/dice-overlay/dice-overlay.component';
import { DiceChoicePopupComponent } from 'src/app/components/shared/dice-choice-popup/dice-choice-popup.component';
import { ChoiceBoxComponent } from './components/choice-box/choie-box.component';
import { DilemmaBoxComponent } from './components/dilemma-box/dilemma-box.component';
import { AnswerBoxDilemmaComponent } from './components/answerDilemma-box/answerDilemma-box.component';
import { InvestigationComponent } from './components/investigation/investigation.component';
import { AnswerInvestigationBoxComponent } from './components/answer-investigation-box/answer-investigation-box.component';
import { RouterModule } from '@angular/router';
import { ModalAiSpeechesComponent } from './components/speech/modal-ai-speeches/modal-ai-speeches.component';

@NgModule({
  imports: [
    RouterModule,
    CommonModule,
    BrowserModule,
    FormsModule,
    MajorModule,
    ContentInputModule,
    PopupModule,
    AudioListenerModule,
    PiecesModule
  ],
  declarations: [
    LevelDialogueEditorComponent,
    DialogueSimulatorComponent,
    DialogueBoxSimulatorComponent,
    DiceOverlayComponent,
    DiceChoicePopupComponent,
    DialogueTreeViewComponent,
    DialogueTreeLayerComponent,
    DialogueTreeBranchComponent,
    DialogueTreeLeafComponent,
    RoadblockInfoComponent,
    StoryBoxComponent,
    ChoiceBoxComponent,
    AnswerBoxComponent,
    EventComponent,
    MarkerComponent,
    SpeechComponent,
    RpgContentInputComponent,
    RpgFormattingPopupComponent,
    RoadblockComponent,
    DilemmaBoxComponent,
    AnswerBoxDilemmaComponent,
    InvestigationComponent,
    AnswerInvestigationBoxComponent,
    ModalAiSpeechesComponent
  ],
  exports: [LevelDialogueEditorComponent],
})
export class LevelDialogueEditorModule {}
