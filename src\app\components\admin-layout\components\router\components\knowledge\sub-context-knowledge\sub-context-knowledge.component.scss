.main-menu-efect {
    padding-right: 15px;
    padding-left: 15px; 
    min-height: calc(100% - 210px);
    margin-top: 30px;
  }
  
  .card-header-content {
  display: block;
  margin-left: 20px;
  margin-right: 15px;
  width: 30%;
  }
  
  .card {
    // padding-top: 8px !important;
     padding-top: 17px !important;
     padding-bottom: 10px;
   }
  
   .card-header-content {
     display: block;
     margin-left: 30px;
     margin-right: 15px;
     width: 30%;
   }
  
   //tabela
   .card-container 
  {
    display: flex;
    flex-direction: column;
    align-items: center;
  
    .card 
    {
      border: 1px solid #ccc;
      padding: 15px;
      margin: 5px;
      width: 50vw;
      opacity: 1;
      transition: opacity 0.5s ease-in-out;
    } 
  }
  
  
  h3{
      font-size: 28px;
      margin: 10px 0 10px;
  }
  
  .borderList {
      border: 1px solid #ddd !important;
  }
  
  .addButton
  {  
   position: absolute;
      top: -1px;
      right: 13px;
  }
  
    .default-color {
      background-color: #AEAAAA !important;
    }
  
    .aligTitle {
      background-color: white !important;
      color: #565656 !important;
      text-align-last: center !important;
      width: 8%;
    }
  
    .noCursor {
      cursor: default !important;
    }
  
    .btn-atributte {
      display: flex;
      margin-bottom: 10px;
      position: absolute;
      right: 120px;
    }
    
    .text-center {
      text-align: center !important;   
    }
    .width-buscontext {
      width: 400px !important;
    }
    