import { ChangeDetectorRef, Component, EventEmitter, Output } from '@angular/core';
import { TierService } from 'src/app/services';
import { BonusService } from 'src/app/services/bonus.service';
import { SpinnerService } from 'src/app/spinner/spinner.service';
import { Alert } from 'src/lib/darkcloud';


@Component({
  selector: 'app-bonus',
  templateUrl: './bonus.component.html',
  styleUrls: ['./bonus.component.scss']
})
export class BonusComponent {
  @Output() pasteExcelData = new EventEmitter<void>();
  @Output() descrptionOutput = new EventEmitter<string>();
  tierList: string[] = [];
  listBonus: any[] = [];
  bonusExcel: any[] = [];
  description: string;

  constructor(
    private bonusService: BonusService,
    private _tierListService: TierService,
    private spinnerService: SpinnerService,
    private ref: ChangeDetectorRef
  ) {}

  public async ngOnInit() {
    await this._tierListService.toFinishLoading();
    this.tierList = this._tierListService.fillCollectibleRarity('Weapon Rarity');
    this.bonusService.toFinishLoading();
    this.listBonus = this.bonusService.models;
    this.removeEmptyLines();
    this.descrptionOutput.emit(`Showing ${this.listBonus.length} results`);
  }

  removeEmptyLines() {
    this.listBonus = this.listBonus.filter((bonus) => bonus.idValue !== '' && bonus.description !== '');
    this.listBonus.forEach((x) => this.bonusService.svcToModify(x));
  }

  async onExcelPaste(): Promise<void> {
    this.spinnerService.setState(true);
    
    this.bonusService.toFinishLoading();
    this.listBonus = this.bonusService.models = [];
    this.bonusService.toSave();    

    try {
      const text = await navigator.clipboard.readText();
      let lines = text.split(/\r?\n/).filter(line => line);

      // Remove linhas vazias no final
      while (lines.length > 0 && !lines[lines.length - 1].trim()) {
        lines.pop();
      }

      if (lines.length === 0) {
        throw new Error('No data found. Check if you copied the lines correctly.');
      }

      // Itera sobre cada linha de dados do Excel
      this.bonusExcel = lines.map((line, lineIndex) => {
        const cols = line.split(/\t/).map(col => col.trim()); // Divide por colunas (tab) e remove espaços em branco

        // Verifica se a primeira coluna (ID) está vazia
        if (!cols[0]) {
          throw new Error(`The ${lineIndex + 1} line has the first empty column. Check the data.`);
        }

        // Verifica se o número de colunas está correto
        const expectedColumns = 3 + this.tierList.length; // 3 colunas fixas + colunas correspondentes às tiers
        if (cols.length < expectedColumns) {
          throw new Error(
            `Number of columns insufficient on the line ${lineIndex + 1}. Expected: ${expectedColumns}, found: ${cols.length}`
          );
        }

        return {
          idValue: cols[0], // 1ª coluna -> ID
          description: cols[1], // 2ª coluna -> DESCRIPTION
          type: cols[2], // 3ª coluna -> TYPE
          valuesRarity: this.tierList.map((tier, idx) => ({
            name: tier,
            value: Number(cols[idx + 3])
          }))
        };
      });

      this.addNewBonus();

      Alert.ShowSuccess('Excel copied successfully!');
      this.bonusService.toSave();
      this.ref.detectChanges();
      this.ngOnInit();

    } catch (error) {
      Alert.showError(error.message || 'Error processing excel data.');
    } finally {
      this.spinnerService.setState(false);
    }
  }

  async addNewBonus(): Promise<void> {
    if (this.bonusExcel.length === 0) {
      Alert.showError('No valid bonuses found to save.');
      return;
    } 
      this.bonusExcel.forEach(bonusExcel => {
          this.bonusService.createNewBonus(bonusExcel);   
      });

    }
}
