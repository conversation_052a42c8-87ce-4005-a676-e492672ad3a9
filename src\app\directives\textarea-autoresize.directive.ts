import { Directive, HostListener, ElementRef, <PERSON><PERSON>nit, On<PERSON><PERSON>roy } from '@angular/core';

@Directive({
  selector: '[appTextareaAutoresize]'
})
export class TextareaAutoresizeDirective implements OnInit, OnDestroy {

  timeout:any;
  constructor(private elementRef: ElementRef) { }

  @HostListener(':input')
  onInput() {
    this.resize();
  }  



  ngOnInit() {
    if (this.elementRef.nativeElement.scrollHeight) {
     this.timeout = setTimeout(() => this.resize());

    }
  }

  resize() {
    this.elementRef.nativeElement.style.height = '0';
    this.elementRef.nativeElement.style.height = this.elementRef.nativeElement.scrollHeight + 'px';
  }

  ngOnDestroy() {
    clearInterval(this.timeout)

  }

}