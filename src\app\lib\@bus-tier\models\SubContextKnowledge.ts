import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';

export class SubContextKnowledge extends Base<Data.Hard.ISubContextKnowledge, Data.Result.ISubContextKnowledge> implements Required<Data.Hard.ISubContextKnowledge>
{
  public static generateId(index: number): string {
    return IdPrefixes.SUBCONTEXTKNOWLEDGE + index;
  }

  constructor( index: number, dataAccess: SubContextKnowledge['TDataAccess']) 
  {
    super(
    {
      hard: 
      {
        id: SubContextKnowledge.generateId(index),     
      },
    },
    dataAccess
    );
  }
  protected getInternalFetch() 
  {
    return {};
  }
  public get knowledge(): string
  {
    return this.hard.knowledge;
  }
  public set knowledge(value: string) 
  {
    this.hard.knowledge = value;
  }
  public get subContext(): string[]
  {
    return this.hard.subContext;
  }
  public set subContext(value: string[]) 
  {
    this.hard.subContext = value;
  }
  public get description(): string[]
  {
    return this.hard.description;
  }
  public set description(value: string[]) 
  {
    this.hard.description = value;
  }

}
