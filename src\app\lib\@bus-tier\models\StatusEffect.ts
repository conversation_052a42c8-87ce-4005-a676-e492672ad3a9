import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { Base } from './Base';

export class StatusEffect  extends Base<Data.Hard.IStatusEffect, Data.Result.IStatusEffect> implements Required<Data.Hard.IStatusEffect>
{
  public static generateId(index: number, microloop?: boolean): string {
    if(microloop)
    {
      return StatusEffect.generateMicroloopContainerId(index);
    }
    return IdPrefixes.STATUS_EFFECT + index;
  }
  public static generateMicroloopContainerId(index: number): string
  {
    return IdPrefixes.MICROLOOP + index;
  }

  public static getSubIdFrom(otherId: string, microloop?: boolean): string {
    return Base.getSubId(this.generateId(0, microloop), otherId);
  }
  constructor( index: number, name:string, dataAccess?: StatusEffect['TDataAccess'], microloop?: boolean
  ) {
    super(
      {
        hard: {
          id: StatusEffect.generateId(index,microloop),
          name,
        },
      },
      dataAccess
    );
  }
  protected getInternalFetch() {
    return {};
  }

  public get trigger(): string 
  {
    return this.hard.trigger;
  }

  public set trigger(value: string) 
  {
    this.hard.trigger = value;
  }

  public get target(): string 
  {
    return this.hard.target;
  }

  public set target(value: string) 
  {
    this.hard.target = value;
  }

  public get status(): string 
  {
    return this.hard.status;
  }
  
  public set status(value: string) 
  {
    this.hard.status = value;
  }

  public get skill(): string 
  {
    return this.hard.skill;
  }

  public set skill(value: string) 
  {
    this.hard.skill = value;
  }

  public get operator(): string 
  {
    return this.hard.operator;
  }

  public set operator(value: string) 
  {
    this.hard.operator = value;
  }

  public get amount(): string 
  {
    return this.hard.amount;
  }

  public set amount(value: string) 
  {
    this.hard.amount = value;
  } 

  public get name(): string 
  {
    return this.hard.name;
  }

  public set name(value: string) 
  {
    this.hard.name = value;
  } 

  public get description(): string 
  {
    return this.hard.description;
  }

  public set description(value: string) 
  {
    this.hard.description = value;
  } 

  public get variance(): string 
  {
    return this.hard.variance;
  }

  public set variance(value: string) 
  {
    this.hard.variance = value;
  } 
}
