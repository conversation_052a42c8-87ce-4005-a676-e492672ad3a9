
<!-- Main dialogue simulator container with chat-style interface -->
<div class="wrapper">
  <div class="simulator-container">
    <!-- Header section with dialogue info and simulation controls -->
    <div class="chat-title">
      <div class="header-content">
        <!-- Dialogue identification -->
        <div class="dialogue-info">
          <h1>DIALOGUE SIMULATION</h1>
          <h2>{{ dialogue.id }}</h2>
        </div>

        <!-- Simulation control panel -->
        <div class="controls-section">
          <!-- Message animation speed control -->
          <div class="control-group">
            <label class="control-label">MESSAGE SPEED</label>
            <div class="speed-control">
              <input type="range" [value]="2000" min="100" max="3000" #speedRanger
                     (input)="changeMessageSpeed(speedRanger.value); updateSliderProgress($event)"
                     class="speed-slider"
                     style="--slider-progress: 67%">
              <span class="speed-value">{{ speedRanger.value }}ms</span>
            </div>
          </div>

          <!-- Roadblock visibility toggle -->
          <div class="control-group">
            <label class="control-label">ROADBLOCK VIEW:</label>
            <button
              class="roadblock-button"
              (click)="toggleRoadBlockView()"
              [class.active]="isRoadBlockViewEnabled">
              {{ isRoadBlockViewEnabled ? 'Enabled' : 'Disabled' }}
            </button>
          </div>

          <!-- Dice system enable/disable toggle -->
          <div class="control-group">
            <label class="control-label">DICE SIMULATION:</label>
            <button
              class="dice-button"
              (click)="toggleDiceSimulation()"
              [class.active]="isDiceSimulationEnabled">
              {{ isDiceSimulationEnabled ? 'Enabled' : 'Disabled' }}
            </button>
          </div>
        </div>
      </div>
    </div>
    <!-- Main chat messages area with auto-scroll -->
    <div class="messages" #messages>
      <div class="messages-content">
        <!-- Render each loaded dialogue box sequentially -->
        <div *ngFor="let box of loadedBoxes">
          <dialogue-box-simulator
            [roadBlockViewEnabled]="isRoadBlockViewEnabled"
            [diceSimulationEnabled]="isDiceSimulationEnabled"
            [dialogue]="dialogue"
            [box]="box"
            [messageTyppingDelay]="messageTyppingDelay"
            [messagesBetweenDelay]="messagesBetweenDelay"
            (proccessFinished)="processNextBox()"
            (updateScroll)="updateScroll()"></dialogue-box-simulator>
        </div>
        <!-- End of dialogue indicator -->
        <div class="endMessage" *ngIf="finishedSimulation">End</div>
        <!-- Spacer element to maintain scroll position -->
        <div class="lockChat">.</div>
      </div>
    </div>

    <!-- Message input area (currently disabled/decorative) -->
    <div class="message-box">
      <textarea type="text" class="message-input" placeholder="Type message..."></textarea>
      <!-- Send button commented out as this is simulation-only -->
      <!-- <button type="submit" class="message-submit">Send</button> -->
    </div>
  </div>
</div>

<!-- Global dice system components that overlay the entire interface -->

<!-- Dice choice popup: Appears before dice rolls to let users choose approach -->
<app-dice-choice-popup></app-dice-choice-popup>

<!-- Dice overlay: Shows the 3D dice rolling animation and results -->
<app-dice-overlay></app-dice-overlay>
