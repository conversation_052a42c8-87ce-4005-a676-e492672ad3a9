<div class="main-content">
    <div class="container-fluid">
        <div class="card list-header-row">
            <app-header-with-buttons
            [cardTitle]="listName"
            [rightButtonTemplates]="[addButtonTemplate]"
            [isBackButtonEnabled]="true"
            (cardBackButtonClick)="redirectToMicroloopList()">
            </app-header-with-buttons>
        </div>
    </div>
    <div class="card m-container">
        <table class="table table-list">
            <thead>
                <tr>
                    <th class="th-clickable " (click)="sortListByParameter('id')">ID</th>
                    <th>Single Occurrence</th>
                    <th>First Occurrence</th>
                    <th>Scenery</th>
                    <th class="th-clickable " (click)="sortListByParameter('name')">Name & Description</th>
                    <th>Type of Entounter</th>
                    <th>Speaker IDs</th>
                    <th>Battle Character IDs</th>
                    <th>Dialogue</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <tr style="height: 100px !important;"
                *ngFor="let loop of loops; let i = index" id="{{ loop.id }}">
                    <td>{{ loop.id }}</td>
                    <td><input type="checkbox" [checked]="loop.isUnique" (change)="markAsUnique(loop)"></td>
                    <td><input type="checkbox" [checked]="loop.isFirst" (change)="markAsFirst(loop)"></td>
                    <td style="text-align: center !important;">
                        <button class="btn btn-simple btn-icon"
                        (click)="toPromptChangeScenery(loop)"
                        [ngClass]="!!loop.sceneryId | sceneryButtonClass"
                        [ngStyle]="{'background-color': (loop.sceneryId| scenery| information)?.hex,'border-color': (loop.sceneryId | scenery | information)?.hex}"
                        style="color: white; display: block; margin: auto;"><i class="pe-7s-photo" style="color: white; display: block; margin: auto;"></i></button>
                        <button *ngIf="+loop.type === LevelType.BOSS"
                                [ngClass]="loop.conditionIds.length | conditionsClass"
                                (click)="toPromptAddCondition(loop)">
                            <img src="assets/img/icons/skull-icon.png" />
                        </button>
                    </td>
                    <td>
                      <input style="border-style:solid;" [ngClass]="{'input-background-color': loop.name.length > 0, 'form-control form-short input-empty-background-color': loop.name.length == 0}"
                             type="text"
                             value="{{ (loop | translation : lstLanguage : loop.id : 'name') }}"
                             #name
                             (change)="lstOnChange(loop, 'name', name.value)"
                             />
                      <textarea [ngClass]="{'form-control  area-text-empty-background-color': loop.description?.length == 0 || !loop.description, 'form-control  area-text-background-color':loop.description?.length > 0}"
                                type="text"
                                value="{{ (loop | translation : lstLanguage : loop.id : 'description') }}"
                                #description
                                (change)="lstOnChange(loop, 'description', description.value)"
                                >
                        </textarea>
                    </td>
                    <td style="text-align: center !important;">
                        <select class="dropdown filter-dropdown auto center" (change)='lstOnChangeEvent(loop, "type", $event)'>
                            <option [selected]="loop.type == LevelType.MINION" value="{{ LevelType.MINION }}">Minion</option>
                            <option [selected]="loop.type == LevelType.BOSS" value="{{ LevelType.BOSS }}">Boss</option>
                            <option [selected]="loop.type == LevelType.TRANSITION" value="{{ LevelType.TRANSITION }}">Transition</option>
                            <option [selected]="loop.type == LevelType.MINIGAME" value="{{ LevelType.MINIGAME }}">Minigame</option>
                            <option [selected]="loop.type == LevelType.DEFAULT" value="{{ LevelType.DEFAULT }}">None</option>
                        </select>
                        <input [value]="loop.scoreRequirement" type="number" name="MinigameScoreRequirement" id="mingame" *ngIf="loop.type == LevelType.MINIGAME" (change)="lstOnChangeEvent(loop, 'scoreRequirement', $event)">
                    </td>
                    <td class="td-focus td-auto td-center">
                        <app-speakers-table [level]="loop" [microloop]="true" class="center"></app-speakers-table>
                    </td>
                    <td class="td-focus td-auto td-center">
                        <app-battle-characters-table [level]="loop" [editable] class="center" [microloop]="true"
                        *ngIf="loop.type == LevelType.BOSS || loop.type == LevelType.MINION"></app-battle-characters-table>
                    </td>
                    <td class="td-actions td-small">
                        <button class="btn btn-fill" [ngClass]="loop | dialoguesButtonClass" (click)="redirectToDialogueManager(loop)">
                            <i class="pe-7s-pen"></i>
                        </button>
                    </td>
                    <td class="td-actions td-small">
                        <button class="btn btn-danger btn-remove btn-fill"
                        (click)="removeLoop(loop)">
                            <i class="pe-7s-close-circle"></i>
                        </button>
                        <button class="btn btn-gray btn-fill translation-button" (click)="downloadMicroloopOrtography(loop)">
                            <div class="mat-translate"></div>
                        </button>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
