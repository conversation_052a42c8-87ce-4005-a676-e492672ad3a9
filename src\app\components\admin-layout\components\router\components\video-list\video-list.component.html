<div class="main-content">
  <div class="container-fluid">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card">
        <app-header-with-buttons [cardTitle]="listName" [cardDescription]="cardDescription"
          [rightButtonTemplates]="rightButtonTemplates">
        </app-header-with-buttons>
        <app-header-search (inputKeyup)="lstOnChangeFilter($event)"
          (searchOptions)="lstOnChangeFilterOptions($event)"></app-header-search>
      </div>
    </div>
    <div class="card">
      <table class="table table-list">
        <thead class="sticky">
          <tr>
            <th>Index</th>
            <th class="th-clickable" (click)="sortListByParameter('id')">
              ID
            </th>
            <th class="th-clickable" (click)="sortListByParameter('assigned')">
              Assigned
            </th>
            <th class="th-clickable" (click)="sortListByParameter('videoName')">
              Name & Title
            </th>
            <th>Notes</th>
            <th></th>
            <th class="th-clickable" (click)="sortListByParameter('type')">
              Type
            </th>
            <th>
              Area
              <!--Area filter dropdown-->
              <select class="dropdown filter-dropdown limited center" name="areaIdFilter"
                [(ngModel)]="lstFilterValue['areaId']" (change)="lstOnChangeFilter()">
                <option value="ALL">All</option>
                <option *ngFor="let area of preloadedAreas" value="{{ area.id }}">
                  {{ area.hierarchyCode }}: {{ area.name }}
                </option>
              </select>
            </th>
            <th>Delete</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="
                let video of lstIds | videos;
                let i = index;
                trackBy: trackById
              ">
            <tr id="{{ video.id }}">
              <td class="td-sort">{{ i + 1 }}</td>
              <td class="td-id">{{ video.id }}</td>
              <td>
                <ng-container [ngSwitch]="(video | review).assignedAt.length">
                  <i *ngSwitchCase="1" style="position: relative" placement='top' delay='250' ttWidth="auto"
                    ttAlign="left" ttPadding="10px" tooltip="Assigned at {{
                        ((video | review).assignedAt | location).toString()
                      }}" class="pe-7s-check success"></i>
                  <i *ngSwitchDefault style="position: relative" placement='top' delay='250' ttWidth="auto"
                    ttAlign="left" ttPadding="10px" tooltip=" Assigned more than once ({{
                        ((video | review).assignedAt | location).toString()
                      }})" class="pe-7s-close-circle error"></i>
                  <i *ngSwitchCase="0" style="position: relative" placement='top' delay='250' ttWidth="auto"
                    ttAlign="center" ttPadding="10px" tooltip="Not assigned" class="pe-7s-attention warning"></i>
                </ng-container>
                <ng-container *ngIf="+video.type === VideoType.ANIMATIC">
                  <ng-container *ngIf="(video | review).blankQnA; else okQna">
                    <i style="position: relative" placement='top' delay='250' ttWidth="auto" ttAlign="center"
                      ttPadding="10px" tooltip="Blank QnA" class="pe-7s-attention warning"></i>
                  </ng-container>
                  <ng-template #okQna>
                    <i style="position: relative" placement='top' delay='250' ttWidth="auto" ttAlign="center"
                      ttPadding="10px" tooltip="No issues on QnA" class="pe-7s-check success"></i>
                  </ng-template>
                </ng-container>
              </td>
              <td class="td-20">
                <ng-container *ngIf="
                      +video.type === VideoType.ANIMATIC;
                      else cutsceneNameAndTitle
                    ">
                  <button class="form-short btn label" (click)="toPromptSelectBindCharacter(video)">
                    {{ ((video | animatic).characterId | character)?.name }}
                  </button>
                  <button class="form-short btn label" (click)="toPromptSelectBindCharacter(video)">
                    {{ ((video | animatic).characterId | character)?.title }}
                  </button>
                </ng-container>
                <ng-template #cutsceneNameAndTitle>
                  <input type="text" class="form-control form-short" #name [value]="(video | cutscene).name || ''"
                    (change)="lstOnChange(video, 'name', name.value)" />
                  <input type="text" class="form-control form-short" [value]="(video | cutscene).title || ''" #title
                    (change)="lstOnChange(video, 'title', title.value)" />
                </ng-template>
              </td>
              <td class="td-70" [attr.colspan]="+video.type === VideoType.ANIMATIC ? 1 : 2">
                <textarea class="form-control form-long borderless" #authorNotes
                  value="{{ (video | information)?.authorNotes || '' }}" (change)="
                      updateInformation(video, 'authorNotes', authorNotes.value)
                    "></textarea>
              </td>
              <td *ngIf="+video.type === VideoType.ANIMATIC" class="td-auto">
                <button class="btn label" (click)="
                      animaticIdToggle =
                        animaticIdToggle === video.id ? null : video.id
                    ">
                  Q&A
                </button>
              </td>
              <td>
                <ng-container *ngIf="
                      +video.type === VideoType.ANIMATIC;
                      else cutsceneIcon
                    ">
                  <button (click)="toPromptChangeVideoType(video)" title="Animatic" class="btn btn-fill btn-Animatic">
                    <i class="pe-7s-film"></i></button>
                </ng-container>
                <ng-template #cutsceneIcon>
                  <button title="Video" class="btn btn-fill btn-Cutscene" (click)="toPromptChangeVideoType(video)">
                    <i class="pe-7s-video"></i>
                  </button>
                </ng-template>
              </td>
              <td>
                <button [ngClass]="
                      (video.areaId | area) ? 'btn btn-info btn-fill' : 'btn'
                    " (click)="toPromptSelectBindArea(video)">
                  {{ (video.areaId | area)?.name || "undefined" }}
                </button>
              </td>
              <td class="td-actions">
                <button class="btn btn-danger btn-fill btn-remove" (click)="lstPromptRemove(video)">
                  <i class="pe-7s-close"></i>
                </button>
              </td>
            </tr>
            <ng-container *ngIf="
                  +video.type === VideoType.ANIMATIC &&
                  animaticIdToggle === video.id
                ">
              <tr>
                <td colspan="8">
                  <div class="animatic-themes-wrapper">
                    <div>
                      <button class="btn btn-simple btn-success" (click)="toPromptAddThemeToAnimatic(video)">
                        <i class="pe-7s-plus"></i>
                      </button>
                    </div>
                    <ng-container *ngFor="
                          let themeId of (video | animatic)?.themeIds;
                          let i = index
                        ">
                      <div class="btn btn-fill label" (click)="toRemoveThemeFromAnimatic(video, themeId)">
                        {{ (themeId | theme).name }}
                      </div>
                    </ng-container>
                  </div>
                </td>
              </tr>
              <tr class="animatic-qna-row">
                <td colspan="9" class="td-highlight">
                  <table class="animatic-qna-wrapper">
                    <tbody>
                      <tr *ngFor="
                            let qna of (((video | animatic)?.qnaIds) | qnas);
                            let j = index
                          ">
                        <td class="td-sort">Question</td>
                        <td class="td-50">
                          <input type="text" class="form-control form-short" [value]="qna.question" (change)="
                                onChangeQuestion(video, j, question.value)
                              " #question />
                        </td>
                        <ng-container>
                          <td class="td-sort">Answer</td>
                          <td class="td-50">
                            <textarea class="form-control" [value]="qna.answer" (change)="
                                  onChangeAnswer(video, j, answer.value)
                                " #answer></textarea>
                          </td>
                        </ng-container>
                      </tr>
                      <tr>
                        <td class="td-sort">Question</td>
                        <td class="td-50">
                          <p class="form-short form-control">
                            {{ "ANIMATIC_LAST_QUESTION" | keyword }}
                          </p>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </ng-container>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>