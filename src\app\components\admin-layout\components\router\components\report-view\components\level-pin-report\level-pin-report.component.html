<div class="row">
  <div class="col-md-12">
    <div class="report-table-wrapper">
      <table class="table report-table table-striped">
        <thead class="sticky"
               style="top: 1px;">
          <tr>
            <th class="th-clickable"
                (click)="sorting.sortPinMarkers(sorting.byType)">Type</th>
            <th class="th-clickable"
                (click)="sorting.sortPinMarkers(sorting.byLocation)">Location
            </th>
            <th class="th-clickable"
                (click)="sorting.sortPinMarkers(sorting.byLevelLocation)">Pinned Level
            </th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let marker of pinnedMarkers">
            <tr [title]="marker.id"
                class="tr-clickable"
                (click)="access(marker.id)">
              <td class="td-20">{{ marker.type | markerTypeName }}</td>
              <td class="td-20">{{ ([marker.id] | location) }}</td>
              <td class="td-50">{{ [(marker.levelId | level).id] | location }}</td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>
