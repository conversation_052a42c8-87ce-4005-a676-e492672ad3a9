<div class="list-header-row update">
  <div class="card">
    <app-header-with-buttons [cardTitle]="'AI Prompt'"
    [cardDescription]="'Information about the prompts that are used in AI'"
    [isBackButtonEnabled]="true" (cardBackButtonClick)="redirectToSettings()"
    [rightButtonTemplates]="[addAIPromptTemplate]"></app-header-with-buttons>
  </div>
</div>

<div class="main-content">
  <div class="card">
    <div class="table-responsive horizontal-scroll-container">
      <table class="table table-list">
       <thead style="z-index: 0 !important;">
          <tr>
            <th style="width: 50px; min-width: 50px;">Index</th>
            <th style="width: 210px; min-width: 210px;">Type</th>
            <th style="width: 220px; min-width: 220px;">Name</th>
            <ng-container *ngIf="this.listEnvironments.length > 0">
              <th style="width: 210px; min-width: 210px;">Environment Name</th>
            </ng-container>
            <th style="width: 300px; min-width: 300px;">PROMPT</th>
            <th style="width: 300px; min-width: 300px;">Description</th>
            <th style="width: 250px; min-width: 250px;">NOTES</th>
            <th style="width: 100px; min-width: 100px;">Action</th>
          </tr>
        </thead>
      <tbody>
        <ng-container *ngFor="let prompt of prompts; let i = index">
          <tr id="{{ prompt.id }}">
            <td class="td-id gray" style="width: 50px;">
             {{i + 1}}
            </td>
            <td style="width: 210px;">
              <select class="dropdown filter-dropdown limited typeSelect" #InputType
                (change)="selectType(InputType.value, prompt)" [(ngModel)]="prompt.selectType">
                <option value="" selected>Select type</option>   
                <option *ngFor="let type of types" value="{{ type }}">{{ type }}</option>
            </select>
            </td>
            <td style="width: 220px; min-width: 220px;">
              <input
                id="promptName_{{i}}"
                class="form-control form-short form-title fontPlaceholder"
                type="text"
                value="{{prompt.promptName}}"
                #promptName
                (change)="changePrompt(prompt, promptName.value, 'promptName')"
                [disabled]="!isPromptNameEnabled(prompt.selectType)"
                [placeholder]="isPromptNameEnabled(prompt.selectType) ? 'Enter name...' : ''" />
            </td>
            <ng-container *ngIf="this.listEnvironments.length > 0">
              <td style="width: 210px; min-width: 210px;">
                <select class="dropdown filter-dropdown limited typeSelect" #Environment
                  (change)="selectEnvironment(Environment.value, prompt)" [(ngModel)]="prompt.nameEnvironmentAi">
                  <option value="" selected>Select Environment</option>
                  <option *ngFor="let environment of listEnvironments" value="{{ environment.name }}">{{ environment.name }}</option>
                </select>
                </td>
            </ng-container>
            <td style="width: 300px; min-width: 300px;">
              <textarea
                #promptPrompt
                type="text"
                value="{{ prompt.prompt || '' }}"
                placeholder="Prompt..."
                (change)="this.changePrompt(prompt, promptPrompt.value, 'prompt')"
                class="form-control textarea-field">
                {{prompt.prompt}}
              </textarea>
            </td>
            <td style="width: 300px; min-width: 300px;">
              <textarea
                #promptDescription
                type="text"
                value="{{ prompt.description|| '' }}"
                placeholder="Description..."
                (change)="this.changePrompt(prompt, promptDescription.value, 'description')"
                class="form-control textarea-field">
                {{prompt.description}}
              </textarea>
            </td>
            <td style="width: 250px; min-width: 250px;">
              <textarea
                #promptNotes
                type="text"
                value="{{ prompt.notes || '' }}"
                placeholder="Notes..."
                (change)="this.changePrompt(prompt, promptNotes.value, 'notes')"
                class="form-control textarea-field">
                {{prompt.notes}}
              </textarea>
            </td>
            <td class="td-actions" style="width: 100px; min-width: 100px;">
              <button class="btn btn-danger btn-fill btn-remove" (click)="deletePrompt(prompt)">
                <i class="pe-7s-close"></i>
              </button>
            </td>
          </tr>
        </ng-container>
      </tbody>
    </table>
    </div>
  </div>
</div>
