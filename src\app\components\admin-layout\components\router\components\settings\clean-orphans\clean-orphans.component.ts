import { ChangeDetectorRef, Component, EventEmitter, OnInit, Output } from '@angular/core';
import { Subscription } from 'rxjs';
import { Area, Dialogue, Level } from 'src/app/lib/@bus-tier/models';
import { LocationPipe } from 'src/app/pipes/location.pipe';
import { AnswerDilemmaBoxService, AreaService, DialogueService, DilemmaBoxService, DilemmaService, EventService, LevelHelperService, LevelService, MarkerService, MissionService, ObjectiveService, OptionBoxService, OptionService, SearchService, SpeechService, StoryBoxService } from 'src/app/services';
import { ItemClassService } from 'src/app/services/item-class.service';
import { MicroloopContainerService } from 'src/app/services/microloop-container.service';
import { MicroloopService } from 'src/app/services/microloop.service';
import { RoadBlockService } from 'src/app/services/road-block.service';
import { Al<PERSON>, Search } from 'src/lib/darkcloud';
import { links } from 'src/lib/darkcloud/angular/dsadmin/constants/url';
import { SettingsComponent } from '../settings.component';
import { Name } from 'src/lib/darkcloud/dialogue-system';
import { Router } from '@angular/router';


@Component({
  selector: 'app-clean-orphans',
  templateUrl: './clean-orphans.component.html',
  styleUrls: ['./clean-orphans.component.scss']
})
export class CleanOrphansComponent implements OnInit {

  // ========== PROPRIEDADES DE CONTROLE ==========
  @Output() refresh: EventEmitter<void> = new EventEmitter(); // Emite evento para atualizar componente pai
  ResultType = Search.ResultType; // Tipo de resultado para busca

  // ========== ARRAYS DE ÓRFÃOS PARA REMOÇÃO ==========
  rBToBeRemoved = []; // Lista de RoadBlocks órfãos identificados
  objectiveToBeRemoved = []; // Lista de Objectives órfãos identificados
  markerToBeRemoved = []; // Lista de Markers órfãos identificados
  eventToBeRemoved = []; // Lista de Events órfãos identificados
  speechToBeRemoved = []; // Lista de Speeches órfãos identificados
  storyboxToBeRemoved = []; // Lista de StoryBoxes órfãos identificados
  optionBoxToBeRemoved = []; // Lista de OptionBoxes órfãos identificados
  optionToBeRemoved = []; // Lista de Options órfãos identificados
  dilemmaBoxToBeRemoved = []; // Lista de DilemmaBoxes órfãos identificados
  answerDilemmaBoxToBeRemoved = []; // Lista de AnswerDilemmaBoxes órfãos identificados
  dialogueOrphanIdsToRemove = []; // Lista de Dialogues órfãos identificados
  dilemmaToBeRemoved = []; // Lista de Dilemmas órfãos identificados

  // ========== PROPRIEDADES DE CONTROLE DE ESTADO ==========
  location: string[] = []; // Array com localização dos elementos
  term = ''; // Termo de busca
  subscription: Subscription; // Subscription para observables
  filterTypeName: string = ''; // Nome do tipo de filtro atual
  listOrphons = []; // Lista atual de órfãos sendo exibidos
  listEmptyOfOrphans = []; // Lista de tipos que não possuem órfãos
  listOrphansNoEmpty = []; // Lista de tipos que possuem órfãos com quantidade
  orphanTypes = []; // Array com todos os tipos de órfãos disponíveis
  titleOrphans = ''; // Título da seção atual de órfãos
  isValidMessageCheck: boolean = false; // Flag para controlar exibição de loading
  isNextObjective: boolean = false; // Flag para controlar próximo objetivo
  currentType: Name; // Armazena o nome do check atual
  currentIndex; // Índice atual do processamento
  infoIndex: number; // Índice da informação atual sendo processada
  isRemove = false; // Flag que indica se está em processo de remoção


  /**
   * ========== CONSTRUTOR ==========
   * Injeta todas as dependências necessárias para o funcionamento do componente
   * Inclui todos os services dos diferentes tipos de elementos do sistema de diálogos
   */
  constructor(
    private _router: Router, // Serviço de navegação
    private _searchService: SearchService, // Serviço de busca
    private _itemClassService: ItemClassService, // Serviço de classes de itens
    private _speechService: SpeechService, // Serviço de falas/speeches
    private _dialogueService: DialogueService, // Serviço de diálogos
    private _eventService: EventService, // Serviço de eventos
    private _markerService: MarkerService, // Serviço de marcadores
    private _roadBlockService: RoadBlockService, // Serviço de bloqueios de estrada
    private _optionboxService: OptionBoxService, // Serviço de caixas de opções
    private _optionService: OptionService, // Serviço de opções
    private _levelService: LevelService, // Serviço de níveis
    private _objectiveService: ObjectiveService, // Serviço de objetivos
    private _microloopService: MicroloopService, // Serviço de microloops
    private _storyboxService: StoryBoxService, // Serviço de caixas de história
    private _areaService: AreaService, // Serviço de áreas
    private _microloopContainerService: MicroloopContainerService, // Serviço de containers de microloop
    private _missionService: MissionService, // Serviço de missões
    private _dilemmaService: DilemmaService, // Serviço de dilemas
    private _dilemmaBoxService: DilemmaBoxService, // Serviço de caixas de dilema
    private _answerDilemmaBoxService: AnswerDilemmaBoxService, // Serviço de respostas de dilema
    private locationPipe: LocationPipe, // Pipe para transformação de localização
    private _change: ChangeDetectorRef, // Referência para detecção de mudanças
    private settingsComponent: SettingsComponent // Componente pai de configurações
  ) { }

  /**
   * ========== INICIALIZAÇÃO DO COMPONENTE ==========
   * Método executado na inicialização do componente
   * Carrega todos os services necessários e inicia a verificação de órfãos
   */
  async ngOnInit() {
    // Aguarda o carregamento completo de todos os services necessários
    this._roadBlockService.toFinishLoading();
    this._objectiveService.toFinishLoading();
    this._markerService.toFinishLoading();
    this._eventService.toFinishLoading();
    this._speechService.toFinishLoading();
    this._storyboxService.toFinishLoading();
    this._optionService.toFinishLoading();
    this._optionboxService.toFinishLoading();
    this._answerDilemmaBoxService.toFinishLoading();
    this._dilemmaService.toFinishLoading();
    this._dilemmaBoxService.toFinishLoading();
    this._microloopContainerService.toFinishLoading();
    this._dialogueService.toFinishLoading();

    // Inicia o processo de verificação de órfãos começando pelos RoadBlocks
    this.checkOrphanRoadblocks();
  }

  /**
   * ========== VERIFICAÇÃO DE ROADBLOCKS ÓRFÃOS ==========
   * Método principal para identificar RoadBlocks que não possuem referências válidas
   * RoadBlocks órfãos são aqueles que não estão vinculados a nenhum StoryBox, Option, OptionBox, DilemmaBox ou AnswerDilemmaBox
   */
  checkOrphanRoadblocks = async () => {
    // Reseta as variáveis de controle
    this.titleOrphans = '';
    this.location = [];
    this.infoIndex = 0; // Define o índice como 0 (RoadBlocks)
    this.rBToBeRemoved = [];

    // Exibe loading apenas se não estiver em uma verificação válida
    if (!this.isValidMessageCheck) {
      await Alert.showLoading(undefined, 'Checking for Orphan Roadblocks');
    }

    // Executa a lógica de identificação de RoadBlocks órfãos
    this.rBToBeRemoved = this.removeOrphanRB(this.rBToBeRemoved);
    this.isRemove = false;

    // Processa a lista de órfãos encontrados
    this.checkListOrphans(Name.ROADBLOCK);
  }

  /**
   * ========== IDENTIFICAÇÃO DE ROADBLOCKS ÓRFÃOS ==========
   * Percorre todos os RoadBlocks e verifica se possuem referências válidas
   * @param RBToBeRemoved - Array que receberá os RoadBlocks órfãos identificados
   * @returns Array com os RoadBlocks órfãos encontrados
   */
  removeOrphanRB(RBToBeRemoved: any[]) {
    // Percorre todos os RoadBlocks do sistema
    for (let i = 0; i < this._roadBlockService.models.length; i++) {
      const RBID = this._roadBlockService.models[i].id;
      const newId = RBID.replace(/\.[^.]+$/, ""); // Remove o conteúdo após o último ponto para obter o ID pai
      this.location = this.checkLocation([newId]); // Verifica a localização do elemento

      // Verifica se o RoadBlock possui referências válidas em outros componentes
      if (
        !this.checkRBInStorybox(newId) &&
        !this.ckeckRBOption(newId) &&
        !this.checkRBInOptionbox(newId) &&
        !this.checkRBInDilemmaBox(newId) &&
        !this.checkRBInAnswerDilemmaBox(newId)
      ) {
        // Se não possui referências, adiciona à lista de órfãos
        RBToBeRemoved.push({
          typeName: Name.ROADBLOCK,
          id: RBID,
          local: this.location.length > 0 ? this.location[0] : "Orphan",
          field: 'id',
          type: this._roadBlockService.models[i].type,
          key: "hard"
        });
      }
    }

    this.titleOrphans = 'These are Orphan Roadblocks that will be removed';
    this.isValidMessageCheck = true;
    return RBToBeRemoved;
  }

  /**
   * ========== MÉTODOS DE VERIFICAÇÃO DE REFERÊNCIAS PARA ROADBLOCKS ==========
   */

  /** Verifica se o RoadBlock está referenciado em algum StoryBox */
  checkRBInStorybox(newId: string): boolean {
    return this._storyboxService.models.some(model => model.id === newId);
  }

  /** Verifica se o RoadBlock está referenciado em alguma Option */
  ckeckRBOption(newId: string): boolean {
    return this._optionService.models.some(model => model.answerBoxId === newId || model.answerBoxNegativeId === newId);
  }

  /** Verifica se o RoadBlock está referenciado em algum OptionBox */
  checkRBInOptionbox(newId: string): boolean {
    return this._optionboxService.models.some(model => model.id === newId);
  }

  /** Verifica se o RoadBlock está referenciado em algum DilemmaBox */
  checkRBInDilemmaBox(newId: string): boolean {
    return this._dilemmaBoxService.models.some(model => model.id === newId);
  }

  /** Verifica se o RoadBlock está referenciado em algum AnswerDilemmaBox */
  checkRBInAnswerDilemmaBox(newId: string): boolean {
    return this._answerDilemmaBoxService.models.some(model => model.id === newId);
  }


  /**
   * ========== VERIFICAÇÃO DE LOCALIZAÇÃO ==========
   * Utiliza o LocationPipe para transformar um array de IDs em informações de localização
   * @param location - Array de strings contendo os IDs para verificação de localização
   * @returns Array com as informações de localização transformadas pelo pipe
   */
  checkLocation(location: string[]) {
    const existLocation = this.locationPipe.transform(location);
    return existLocation;
  }


  /**
   * ========== VERIFICAÇÃO E PROCESSAMENTO DE LISTAS DE ÓRFÃOS ==========
   * Método central que gerencia o fluxo de verificação de órfãos para cada tipo de elemento
   * Controla a navegação entre diferentes tipos de verificação e atualiza as listas de órfãos
   * @param type - Tipo de elemento sendo verificado (ROADBLOCK, OBJECTIVE, MARKER, etc.)
   */
  async checkListOrphans(type: string) {
    // Função auxiliar para verificar e adicionar itens únicos ao array
    const addIfNotExists = (list, item) => {
      if (!list.includes(item)) {
        list.push(item);
      }
    };

    if (type === Name.ROADBLOCK) {
      this.infoIndex = 0;
      if (this.rBToBeRemoved.length === 0) {
        addIfNotExists(this.listEmptyOfOrphans, Name.ROADBLOCK);
        this.isValidMessageCheck = false;
        this.checkOrphanObjective();
      } else {
        this.listOrphons = this.rBToBeRemoved;
      }
    } else if (type === Name.OBJECTIVE) {
      this.infoIndex = 1;
      if (this.objectiveToBeRemoved.length === 0) {
        addIfNotExists(this.listEmptyOfOrphans, Name.OBJECTIVE);
        this.isValidMessageCheck = false;
        this.checkOrphanMarkers();
      } else {
        this.listOrphons = this.objectiveToBeRemoved;
      }
    } else if (type === Name.MARKER) {
      this.infoIndex = 2;
      if (this.markerToBeRemoved.length === 0) {
        addIfNotExists(this.listEmptyOfOrphans, Name.MARKER);
        this.isValidMessageCheck = false;
        this.checkOrphanEvents();
      } else {
        this.listOrphons = this.markerToBeRemoved;
      }
    } else if (type === Name.EVENT) {
      this.infoIndex = 3;
      if (this.eventToBeRemoved.length === 0) {
        addIfNotExists(this.listEmptyOfOrphans, Name.EVENT);
        this.isValidMessageCheck = false;
        this.checkOrphanSpeeches();
      } else {
        this.listOrphons = this.eventToBeRemoved;
      }
    } else if (type === Name.SPEECH) {
      this.infoIndex = 4;
      if (this.speechToBeRemoved.length === 0) {
        addIfNotExists(this.listEmptyOfOrphans, Name.SPEECH);
        this.isValidMessageCheck = false;
        this.checkOrphanStorybox();
      } else {
        this.listOrphons = this.speechToBeRemoved;
      }
    } else if (type === Name.STORY_BOX) {
      this.infoIndex = 5;
      if (this.storyboxToBeRemoved.length === 0) {
        addIfNotExists(this.listEmptyOfOrphans, Name.STORY_BOX);
        this.isValidMessageCheck = false;
        this.checkOrphanOptions();
      } else {
        this.listOrphons = this.storyboxToBeRemoved;
      }
    } else if (type === Name.OPTION) {
      this.infoIndex = 6;
      if (this.optionToBeRemoved.length === 0) {
        addIfNotExists(this.listEmptyOfOrphans, Name.OPTION);
        this.isValidMessageCheck = false;
        this.checkOrphanOptionBox();
      } else {
        this.listOrphons = this.optionToBeRemoved;
      }
    } else if (type === Name.OPTION_BOX) {
      this.infoIndex = 7;
      if (this.optionBoxToBeRemoved.length === 0) {
        addIfNotExists(this.listEmptyOfOrphans, Name.OPTION_BOX);
        this.isValidMessageCheck = false;
        this.checkOrphanAnswerDilemma();
      } else {
        this.listOrphons = this.optionBoxToBeRemoved;
      }
    } else if (type === Name.ANSWERDILEMMABOX) {
      this.infoIndex = 8;
      if (this.answerDilemmaBoxToBeRemoved.length === 0) {
        addIfNotExists(this.listEmptyOfOrphans, Name.ANSWERDILEMMABOX);
        this.isValidMessageCheck = false;
        this.checkOrphanDilemma();
      } else {
        this.listOrphons = this.answerDilemmaBoxToBeRemoved;
      }
    } else if (type === Name.DILEMMA) {
      this.infoIndex = 9;
      if (this.dilemmaToBeRemoved.length === 0) {
        addIfNotExists(this.listEmptyOfOrphans, Name.DILEMMA);
        this.isValidMessageCheck = false;
        this.checkOrphanDilemmaBox();
      } else {
        this.listOrphons = this.dilemmaToBeRemoved;
      }
    } else if (type === Name.DILEMMABOX) {
      this.infoIndex = 10;
      if (this.dilemmaBoxToBeRemoved.length === 0) {
        addIfNotExists(this.listEmptyOfOrphans, Name.DILEMMABOX);
        this.isValidMessageCheck = false;
        this.checkOrphanDialogues();
      } else {
        this.listOrphons = this.dilemmaBoxToBeRemoved;
      }
    } else if (type === Name.DIALOGUE) {
      this.infoIndex = 11;
      if (this.dialogueOrphanIdsToRemove.length === 0) {
        addIfNotExists(this.listEmptyOfOrphans, Name.DIALOGUE);
        this.isValidMessageCheck = false;
        await this.settingsComponent.toPromptExportData('dsa'); //export DSA
      } else {
        this.listOrphons = this.dialogueOrphanIdsToRemove;
      }
    }
  }


  /**
   * ========== VERIFICAÇÃO DE OBJECTIVES ÓRFÃOS ==========
   * Método principal para identificar Objectives que não estão vinculados a nenhuma Mission
   * Objectives órfãos são aqueles que não aparecem no array objectiveIds de nenhuma Mission
   */
  checkOrphanObjective = async () => {
    // Reseta as variáveis de controle
    this.titleOrphans = '';
    this.location = [];
    this.isRemove = false;
    this.infoIndex = 1; // Define o índice como 1 (Objectives)
    this.objectiveToBeRemoved = [];

    // Exibe loading apenas se não estiver em uma verificação válida
    if (!this.isValidMessageCheck) {
      await Alert.showLoading(undefined, 'Checking for Orphan Objective');
    }
    this.rBToBeRemoved = [];
    this.objectiveToBeRemoved = this.removeOrphanObjective();
    this.isValidMessageCheck = true;
    this.checkListOrphans(Name.OBJECTIVE);
  }

  /**
   * ========== IDENTIFICAÇÃO DE OBJECTIVES ÓRFÃOS ==========
   * Percorre todos os Objectives e verifica se estão referenciados em alguma Mission
   * @returns Array com os Objectives órfãos encontrados
   */
  removeOrphanObjective() {
    const objectiveRemove = [];

    // Percorre todos os Objectives do sistema
    for (const objective of this._objectiveService.models) {
      // Verifica se o Objective não está vinculado a nenhuma Mission
      if (!this.checkObjectiveInMission(objective.id)) {
        this.location = this.checkLocation([objective.id]);
        objectiveRemove.push({
          typeName: Name.OBJECTIVE,
          id: objective.id,
          local: this.location.length > 0 ? this.location[0] : "Orphan",
          field: 'id',
          type: 0,
          key: "hard"
        });
      }
    }

    this.titleOrphans = 'These are Orphan Objectives that will be removed';
    return objectiveRemove;
  }

  /**
   * ========== VERIFICAÇÃO DE OBJECTIVE EM MISSIONS ==========
   * Verifica se um Objective específico está referenciado no array objectiveIds de alguma Mission
   * @param objectiveId - ID do Objective a ser verificado
   * @returns true se o Objective estiver vinculado a alguma Mission, false caso contrário
   */
  checkObjectiveInMission(objectiveId) {
    for (const mission of this._missionService.models) {
      if (mission.objectiveIds.includes(objectiveId)) {
        return true;
      }
    }
    return false;
  }

  /**
   * ========== VERIFICAÇÃO DE MARKERS ÓRFÃOS ==========
   * Método principal para identificar Markers que não possuem referências válidas
   * Markers órfãos são aqueles que não estão vinculados a StoryBoxes ou AnswerDilemmaBoxes válidos
   * e cujos diálogos pai não existem em Levels ou Microloops
   */
  checkOrphanMarkers = async () => {
    // Reseta as variáveis de controle
    this.titleOrphans = '';
    this.location = [];
    this.isRemove = false;
    this.infoIndex = 2; // Define o índice como 2 (Markers)
    this.markerToBeRemoved = [];

    // Exibe loading apenas se não estiver em uma verificação válida
    if (!this.isValidMessageCheck) {
      await Alert.showLoading(undefined, 'Checking for Orphan Markers');
    }
    this.markerToBeRemoved = this.removeOrphanMarkers(this.markerToBeRemoved);
    this.isValidMessageCheck = true;
    this.checkListOrphans(Name.MARKER);
  }

  /**
   * ========== IDENTIFICAÇÃO DE MARKERS ÓRFÃOS ==========
   * Percorre todos os Markers e verifica se possuem referências válidas
   * Um Marker é considerado órfão se não estiver vinculado a StoryBoxes ou AnswerDilemmaBoxes
   * e se seu diálogo pai não existir em Levels ou Microloops
   * @param markerToBeRemoved - Array que receberá os Markers órfãos identificados
   * @returns Array com os Markers órfãos encontrados
   */
  removeOrphanMarkers(markerToBeRemoved) {
    for (let index = 0; index < this._markerService.models.length; index++) {
      const idMarker = this._markerService.models[index].id;
      const newMarker = idMarker.replace(/\.[^.]+$/, ""); // Remove o conteúdo após o último ponto
      this.location = this.checkLocation([newMarker]);
      const idDialogue = idMarker.split('.').slice(0, 2).join('.'); // Extrai o ID do diálogo pai

      // Verifica se o Marker possui referências válidas
      if ((this.checkMarkerStorybox(idMarker, newMarker) || this.checkMarkerAnswerDilemma(idMarker, newMarker))
        && (this.checkMarkerExistLevel(idDialogue) || this.checkMarkerExistMicroloops(idDialogue))) {
        continue; // Se possui referências válidas, pula para o próximo
      }
      else {
        // Se não possui referências válidas, adiciona à lista de órfãos
        markerToBeRemoved.push({
          typeName: Name.MARKER,
          id: idMarker,
          local: this.location.length > 0 ? this.location[0] : "Orphan",
          field: 'id',
          type: this._markerService.models[index].type,
          key: "hard",
        });
      }
    }
    this.titleOrphans = 'These are Orphan Markers that will be removed';
    this.isValidMessageCheck = true;
    return markerToBeRemoved;
  }

  /**
   * ========== MÉTODOS DE VERIFICAÇÃO DE REFERÊNCIAS PARA MARKERS ==========
   */

  /** Verifica se o Marker está referenciado em algum StoryBox */
  checkMarkerStorybox(id: string, newId: string): boolean {
    return this._storyboxService.models.some((storybox) => storybox.id === newId &&
      storybox.storyProgressIds.includes(id)
    );
  }

  /** Verifica se o Marker está referenciado em algum AnswerDilemmaBox */
  checkMarkerAnswerDilemma(eventId: string, newId: string) {
    return this._answerDilemmaBoxService.models.some((answer) => answer.id === newId &&
      answer.storyProgressIds.includes(eventId)
    );
  }

  /** Verifica se o diálogo pai do Marker existe em algum Level */
  checkMarkerExistLevel(idDialogue: string) {
    return this._levelService.models.some((level) => level.id === idDialogue);
  }

  /** Verifica se o diálogo pai do Marker existe em algum Microloop */
  checkMarkerExistMicroloops(id: string) {
    return this._microloopService.models.some((level) => level.id === id);
  }

  /**
   * ========== VERIFICAÇÃO DE EVENTS ÓRFÃOS ==========
   * Método principal para identificar Events que não possuem referências válidas
   * Events órfãos são aqueles que não estão vinculados a nenhum StoryBox ou AnswerDilemmaBox
   */
  checkOrphanEvents = async () => {
    // Reseta as variáveis de controle
    this.titleOrphans = '';
    this.location = [];
    this.isRemove = false;
    this.infoIndex = 3; // Define o índice como 3 (Events)
    this.eventToBeRemoved = [];

    // Exibe loading apenas se não estiver em uma verificação válida
    if (!this.isValidMessageCheck) {
      await Alert.showLoading(undefined, 'Checking for Orphan Event');
    }

    // Percorre todos os Events do sistema
    for (let index = 0; index < this._eventService.models.length; index++) {
      const evenId = this._eventService.models[index].id
      this.location = this.checkLocation([evenId]);

      // Verifica se o Event não possui referências válidas
      if (!this.checkEventInStorybox(evenId) &&
        !this.checkEventInAnswerDilemmaBox(evenId)) {
        this.eventToBeRemoved.push({
          typeName: Name.EVENT,
          id: evenId,
          local: this.location.length > 0 ? this.location[0] : "Orphan",
          field: 'id',
          type: this._eventService.models[index].type,
          key: "hard"
        });
      }
    }
    this.titleOrphans = 'These are Orphan events that will be removed';
    this.isValidMessageCheck = true;
    this.checkListOrphans(Name.EVENT);
  }

  /**
   * ========== MÉTODOS DE VERIFICAÇÃO DE REFERÊNCIAS PARA EVENTS ==========
   */

  /** Verifica se o Event está referenciado em algum StoryBox */
  checkEventInStorybox(eventId: string): boolean {
    return this._storyboxService.models.some((storybox) =>
      storybox.storyProgressIds.includes(eventId)
    );
  }

  /** Verifica se o Event está referenciado em algum AnswerDilemmaBox */
  checkEventInAnswerDilemmaBox(eventId: string): boolean {
    return this._answerDilemmaBoxService.models.some((anwser) =>
      anwser.storyProgressIds.includes(eventId)
    );
  }

  /**
   * ========== VERIFICAÇÃO DE SPEECHES ÓRFÃOS ==========
   * Método principal para identificar Speeches que não possuem referências válidas
   * Speeches órfãos são aqueles que não estão vinculados a nenhum StoryBox ou AnswerDilemmaBox
   */
  checkOrphanSpeeches = async () => {
    // Reseta as variáveis de controle
    this.titleOrphans = '';
    this.location = [];
    this.isRemove = false;
    this.infoIndex = 4; // Define o índice como 4 (Speeches)
    this.speechToBeRemoved = [];

    // Exibe loading apenas se não estiver em uma verificação válida
    if (!this.isValidMessageCheck) {
      await Alert.showLoading(undefined, 'Checking for Orphan Speeches');
    }

    // Percorre todos os Speeches do sistema
    for (let index = 0; index < this._speechService.models.length; index++) {
      const idSpeech = this._speechService.models[index].id;
      const newSpeech = idSpeech.replace(/\.[^.]+$/, ""); // Remove o conteúdo após o último ponto
      this.location = this.checkLocation([newSpeech]);

      // Verifica se o Speech não possui referências válidas
      if (!this.removeOrphanSpeeches(idSpeech) && !this.checkEventInAnswerDilemmaBox(idSpeech)) {
        this.speechToBeRemoved.push({
          typeName: Name.SPEECH,
          id: idSpeech,
          local: this.location.length > 0 ? this.location[0] : "Orphan",
          field: 'id',
          type: 0,
          key: "hard",
        });
      }
    }

    this.titleOrphans = 'These are Orphan Speeches that will be removed';
    this.isValidMessageCheck = true;
    this.checkListOrphans(Name.SPEECH);
  }

  /**
   * ========== VERIFICAÇÃO DE SPEECH EM STORYBOXES ==========
   * Verifica se um Speech específico está referenciado no array storyProgressIds de algum StoryBox
   * @param id - ID do Speech a ser verificado
   * @returns true se o Speech estiver vinculado a algum StoryBox, false caso contrário
   */
  removeOrphanSpeeches(id: string): boolean {
    return this._storyboxService.models.some((storybox) => storybox.storyProgressIds.includes(id));
  }

  // StoryBox
  checkOrphanStorybox = async () => {
    this.titleOrphans = '';
    this.location = [];
    this.isRemove = false;
    this.infoIndex = 5;
    this.storyboxToBeRemoved = [];

    if (!this.isValidMessageCheck) {
      await Alert.showLoading(undefined, 'Checking for Orphan Storyboxes');
    }

    for (let index = 0; index < this._storyboxService.models.length; index++) {
      const idStory = this._storyboxService.models[index].id;
      const newStory = idStory.replace(/\.[^.]+$/, ""); // Remove o conteúdo após o último ponto.
      this.location = this.checkLocation([newStory]); // Determina a localização do storybox. 

      
      if (idStory.includes('undefined') || this.location.length == 0) {
        this.storyboxToBeRemoved.push({
          typeName: Name.STORY_BOX,
          id: idStory,
          local: this.location.length > 0 ? this.location[0] : "Orphan",
          field: 'id',
          type: 0,
          key: "hard",
        }); 
        this.subComponentsIsExistOrphansStoryBox(newStory);      
      }  
     // Expressão regular para remover do id apóso quarto ponto
      const truncatedIdStory = idStory.split('.').slice(0, 4).join('.');
      this.AnalyzeDilogue(truncatedIdStory, idStory);
    }

    this.titleOrphans = 'These are Orphan StoryBoxes that will be removed';
    this.isValidMessageCheck = true;
    this.checkListOrphans(Name.STORY_BOX);
  }

  /**
   * ========== VERIFICAÇÃO DE SUBCOMPONENTES ÓRFÃOS DE STORYBOX ==========
   * Verifica se existem subcomponentes órfãos relacionados a um StoryBox específico
   * Chama a verificação para Events, Speeches, Markers e RoadBlocks
   * @param id - ID do StoryBox para verificar subcomponentes órfãos
   */
  subComponentsIsExistOrphansStoryBox(id: string) {
    this.checkSubComponentOrphansStoryBox(this._eventService.models, Name.EVENT, id);
    this.checkSubComponentOrphansStoryBox(this._speechService.models, Name.SPEECH, id);
    this.checkSubComponentOrphansStoryBox(this._markerService.models, Name.MARKER, id);
    this.checkSubComponentOrphansStoryBox(this._roadBlockService.models, Name.ROADBLOCK, id, "StoryBoxId");
  }

  /**
   * ========== VERIFICAÇÃO GENÉRICA DE SUBCOMPONENTES ÓRFÃOS ==========
   * Verifica subcomponentes órfãos de um tipo específico relacionados a um ID
   * Adiciona os órfãos encontrados à lista de StoryBoxes a serem removidos
   * @param subComponents - Array de modelos do tipo de subcomponente
   * @param typeName - Nome do tipo de subcomponente
   * @param id - ID para filtrar subcomponentes relacionados
   * @param field - Campo a ser verificado (padrão: "id")
   */
  checkSubComponentOrphansStoryBox(subComponents: any[], typeName: string, id: string, field = "id") {
    if (id != undefined) {
      // Filtra subcomponentes que contêm o ID especificado
      const orphanSubComponents = subComponents.filter((subComponent) => subComponent[field]?.includes(id));

      // Adiciona cada subcomponente órfão à lista de remoção
      orphanSubComponents.forEach((subComponent) => {
        this.storyboxToBeRemoved.push({
          typeName: typeName,
          id: subComponent.id,
          local: this.location.length > 0 ? this.location[0] : "Orphan",
          field: 'id',
          type: subComponent.type,
          key: "hard",
        });
      });
    }
  }

  /**
   * ========== ANÁLISE DE DIÁLOGO ÓRFÃO ==========
   * Verifica se um diálogo específico existe e está referenciado
   * Se não existir, adiciona à lista de StoryBoxes órfãos para remoção
   * @param id - ID do diálogo truncado para verificação
   * @param idStory - ID completo do StoryBox relacionado
   */
  AnalyzeDilogue(id: string, idStory: string) {
    const noExist = this._dialogueService.models.find(dialogue => dialogue.boxIds.includes(id));
    if (!noExist) {
     this.storyboxToBeRemoved.push({
       typeName: Name.DIALOGUE,
       id: idStory,
       local: this.location.length > 0 ? this.location[0] : "Orphan",
       field: 'id',
       type: 0,
       key: "hard",
     });
    }
   }
  // Fim do StoryBox

  // Option
  checkOrphanOptions = async () => {
    this.titleOrphans = '';
    this.location = [];
    this.isRemove = false;
    this.infoIndex = 6;
    this.optionToBeRemoved = [];

    if (!this.isValidMessageCheck) {
      await Alert.showLoading(undefined, 'Checking for orphan Option');
    }

    this.checkIdInOption();

    const processedOptions = [];
    for (let index = 0; index < this.optionToBeRemoved.length; index++) {
      const idOption = this.optionToBeRemoved[index].id;
      const newOption = idOption.replace(/\.[^.]+$/, ""); // Remove o conteúdo após o último ponto.
      this.location = this.checkLocation([newOption]); // Determina a localização do storybox.
      processedOptions.push({
        typeName: Name.OPTION,
        id: idOption,
        local: this.location.length > 0 ? this.location[0] : "Orphan",
        field: 'id',
        type: this.optionToBeRemoved[index].type,
        key: "hard",
      });
    }
    this.optionToBeRemoved = processedOptions;
    this.titleOrphans = 'These are Orphan Option that will be removed';
    this.isValidMessageCheck = true;
    this.checkListOrphans(Name.OPTION);
  }

  /**
   * ========== IDENTIFICAÇÃO DE OPTIONS ÓRFÃOS ==========
   * Filtra Options que não possuem StoryBoxes válidos como destino
   * Verifica se os IDs answerBoxId e answerBoxNegativeId existem em StoryBoxes
   */
  checkIdInOption() {
    const orphanOptions = this._optionService.models.filter((option) => {
      return !this._storyboxService.models.find((storyBox) => storyBox.id === option.answerBoxId || storyBox.id === option?.answerBoxNegativeId);
    });
    this.optionToBeRemoved = [...orphanOptions];
  }
  // Fim do Option

  // OptionBox
  checkOrphanOptionBox = async () => {
    this.titleOrphans = '';
    this.location = [];
    this.isRemove = false;
    this.infoIndex = 7;
    this.optionBoxToBeRemoved = [];

    if (!this.isValidMessageCheck) {
      await Alert.showLoading(undefined, 'Checking for Orphan OptionBox');
    }
    for (let index = 0; index < this._optionboxService.models.length; index++) {
      const idOptionBox = this._optionboxService.models[index].id;
      this.location = this.checkLocation([idOptionBox]);

      this._optionboxService.models[index].optionIds.forEach(id => {
        if (!this._optionService.models.some(op => op.id === id) || this.location.length === 0) {
          this.optionBoxToBeRemoved.push({
            typeName: Name.OPTION_BOX,
            id: idOptionBox,
            local: this.location.length > 0 ? this.location[0] : "Orphan",
            field: 'id',
            type: 0,
            key: "hard",
          });
        }
      });
      if (this.location.length === 0) {
        this.subComponentsIsExistOrphansOptionBox(idOptionBox);
      }
    }

    this.titleOrphans = 'These are Orphan OptionBox that will be removed';
    this.isValidMessageCheck = true;
    this.checkListOrphans(Name.OPTION_BOX);
  }

  // Sub components
  subComponentsIsExistOrphansOptionBox(id: string) {
    this.checkSubComponentOrphansOptionBox(this._eventService.models, Name.EVENT, id);
    this.checkSubComponentOrphansOptionBox(this._speechService.models, Name.SPEECH, id);
    this.checkSubComponentOrphansOptionBox(this._markerService.models, Name.MARKER, id);
    this.checkSubComponentOrphansOptionBox(this._roadBlockService.models, Name.ROADBLOCK, id, "StoryBoxId");
  }

  checkSubComponentOrphansOptionBox(subComponents: any[], typeName: string, id: string, field = "id") {
    const orphanSubComponents = subComponents.filter((subComponent) => subComponent[field]?.includes(id));

    orphanSubComponents.forEach((subComponent) => {
      this.optionBoxToBeRemoved.push({
        typeName: typeName,
        id: subComponent.id,
        local: this.location.length > 0 ? this.location[0] : "Orphan",
        field: 'id',
        type: subComponent.type,
        key: "hard",
      });
    });
  }
  // Fim dos Sub components
  // Fim do OptionBox

  //As funcionalidades dos componente do Dilemma ao ser criado gerou poucos orfãos, pois se corrigiu rápido o erro na funcionalidade do remove (Action)
  // Answer DilemmaBox
  checkOrphanAnswerDilemma = async () => {
    this.titleOrphans = '';
    this.location = [];
    this.isRemove = false;
    this.infoIndex = 8;
    this.answerDilemmaBoxToBeRemoved = [];

    if (!this.isValidMessageCheck) {
      await Alert.showLoading(undefined, 'Checking for Orphan Answer DilemmaBox');
    }

    const answerDilemma = this._answerDilemmaBoxService.models;
    if (answerDilemma.length > 0) {
      for (let index = 0; index < answerDilemma.length; index++) {
        const idAnswerDilemma = answerDilemma[index].id;
        const newIdAnswerDilemma = idAnswerDilemma.replace(/\.[^.]+$/, "");
        this.location = this.checkLocation([newIdAnswerDilemma]);

        const noDilemma = this._dilemmaService.models.every(dil => dil.idDilemmaBox != idAnswerDilemma);

        if (noDilemma) {
          this.answerDilemmaBoxToBeRemoved.push({
            typeName: Name.ANSWERDILEMMABOX,
            id: idAnswerDilemma,
            local: this.location.length > 0 ? this.location[0] : "Orphan",
            field: 'id',
            type: 0,
            key: "hard",
          });
        }
      }
    }

    this.titleOrphans = 'These are Orphan Answer DilemmaBox that will be removed';
    this.isValidMessageCheck = true;
    this.checkListOrphans(Name.ANSWERDILEMMABOX);
  }

  //Dilemma
  checkOrphanDilemma = async () => {
    this.titleOrphans = '';
    this.location = [];
    this.isRemove = false;
    this.infoIndex = 9;
    this.dilemmaToBeRemoved = [];

    if (!this.isValidMessageCheck) {
      await Alert.showLoading(undefined, 'Checking for Orphan Dilemma');
    }

    const dilemma = this._dilemmaService.models;
    if (dilemma.length > 0) {
      for (let index = 0; index < dilemma.length; index++) {
        const idDilemma = dilemma[index].id;
        const newIdDilemma = idDilemma.replace(/\.[^.]+$/, "");
        this.location = this.checkLocation([newIdDilemma]);

        const noDilemma = this._dilemmaBoxService.models.some(dil => dil.optionDilemmaIds.includes(idDilemma));

        if (!noDilemma) {
          this.dilemmaToBeRemoved.push({
            typeName: Name.DILEMMA,
            id: idDilemma,
            local: this.location.length > 0 ? this.location[0] : "Orphan",
            field: 'id',
            type: 0,
            key: "hard",
          });
        }
      }
    }

    this.titleOrphans = 'These are Orphan Dilemma that will be removed';
    this.isValidMessageCheck = true;
    this.checkListOrphans(Name.DILEMMA);
  }

  //DilemmaBox
  checkOrphanDilemmaBox = async () => {
    this.titleOrphans = '';
    this.location = [];
    this.isRemove = false;
    this.infoIndex = 10;
    this.dilemmaBoxToBeRemoved = [];

    if (!this.isValidMessageCheck) {
      await Alert.showLoading(undefined, 'Checking for Orphan DilemmaBox');
    }

    const dilemmaBoxes = this._dilemmaBoxService.models;

    if (dilemmaBoxes.length > 0) {
      dilemmaBoxes.forEach((dilemmaBox) => {
        const idDilemmaBox = dilemmaBox.id;
        this.location = this.checkLocation([idDilemmaBox]);

        if (this.location.length === 0) {
          this.dilemmaBoxToBeRemoved.push({
            typeName: Name.DILEMMABOX,
            id: idDilemmaBox,
            local: this.location.length > 0 ? this.location[0] : "Orphan",
            field: 'id',
            type: 0,
            key: "hard",
          });

          this.checkListOrphansDilemmas(idDilemmaBox);
          this.subComponentsIsExistOrphans(idDilemmaBox);
        }
        this.AnalyzeDilogueDilemmaBox(idDilemmaBox);

      });
    }

    this.titleOrphans = 'These are Orphan DilemmaBox that will be removed';
    this.isValidMessageCheck = true;
    this.checkListOrphans(Name.DILEMMABOX);
  }
  /**
   * ========== VERIFICAÇÃO DE DILEMMAS ÓRFÃOS RELACIONADOS ==========
   * Identifica Dilemmas e AnswerDilemmaBoxes órfãos relacionados a um DilemmaBox específico
   * Adiciona os órfãos encontrados à lista de DilemmaBoxes a serem removidos
   * @param id - ID do DilemmaBox para verificar componentes relacionados órfãos
   */
  checkListOrphansDilemmas(id: string) {
    // Filtra Dilemmas que contêm o ID do DilemmaBox
    const dilemmas = this._dilemmaService.models.filter((dilemma) => dilemma.id.includes(id));
    // Filtra AnswerDilemmaBoxes que contêm o ID do DilemmaBox
    const answerDilemmaBoxes = this._answerDilemmaBoxService.models.filter((answer) => answer.id.includes(id));

    // Adiciona cada Dilemma órfão à lista de remoção
    dilemmas.forEach((dilemma) => {
      this.dilemmaBoxToBeRemoved.push({
        typeName: Name.DILEMMA,
        id: dilemma.id,
        local: this.location.length > 0 ? this.location[0] : "Orphan",
        field: 'id',
        type: 0,
        key: "hard",
      });
    });

    // Adiciona cada AnswerDilemmaBox órfão à lista de remoção
    answerDilemmaBoxes.forEach((answer) => {
      this.dilemmaBoxToBeRemoved.push({
        typeName: Name.ANSWERDILEMMABOX,
        id: answer.id,
        local: this.location.length > 0 ? this.location[0] : "Orphan",
        field: 'id',
        type: 0,
        key: "hard",
      });
    });
  }

  /**
   * ========== ANÁLISE DE DIÁLOGO ÓRFÃO PARA DILEMMABOX ==========
   * Verifica se um DilemmaBox específico está referenciado em algum diálogo
   * Se não estiver, adiciona à lista de DilemmaBoxes órfãos para remoção
   * @param id - ID do DilemmaBox para verificação
   */
 AnalyzeDilogueDilemmaBox(id: string) {
  const noExist = this._dialogueService.models.find(dialogue => dialogue.boxIds.includes(id));
  if (!noExist) {
   this.dilemmaBoxToBeRemoved.push({
     typeName: Name.DIALOGUE,
     id: id,
     local: this.location.length > 0 ? this.location[0] : "Orphan",
     field: 'id',
     type: 0,
     key: "hard",
   });
  }
 }
  // Sub components
  subComponentsIsExistOrphans(id: string) {
    this.checkSubComponentOrphans(this._eventService.models, Name.EVENT, id);
    this.checkSubComponentOrphans(this._speechService.models, Name.SPEECH, id);
    this.checkSubComponentOrphans(this._markerService.models, Name.MARKER, id);
    this.checkSubComponentOrphans(this._roadBlockService.models, Name.ROADBLOCK, id, "StoryBoxId");
  }

  checkSubComponentOrphans(subComponents: any[], typeName: string, id: string, field = "id") {
    const orphanSubComponents = subComponents.filter((subComponent) => subComponent[field].includes(id));

    orphanSubComponents.forEach((subComponent) => {
      this.dilemmaBoxToBeRemoved.push({
        typeName: typeName,
        id: subComponent.id,
        local: this.location.length > 0 ? this.location[0] : "Orphan",
        field: 'id',
        type: subComponent.type,
        key: "hard",
      });
    });
  }
  // Fim dos Sub components

  //Dialogue orphan
  checkOrphanDialogues = async () => {
    this.titleOrphans = '';
    this.location = [];
    this.isRemove = false;
    this.infoIndex = 11;
    this.dialogueOrphanIdsToRemove = [];

    if (!this.isValidMessageCheck) {
      await Alert.showLoading(undefined, 'Checking for Orphan Dialogues');
    }

    this.location = [];

    for (const dialogue of this._dialogueService.models) {
      const idDialogue = dialogue.id.split('.').slice(0, -1).join('.');
      this.location = this.checkLocation([dialogue.id]);

      if (!this.checkDialogueInLevelDialogueidsArray(idDialogue)) {
        this.dialogueOrphanIdsToRemove.push({
          typeName: Name.DIALOGUE,
          id: dialogue.id,
          local: this.location.length > 0 ? this.location[0] : "Orphan",
          field: 'id',
          type: dialogue.type,
          key: "hard",
        });
      }
    }

    this.titleOrphans = 'These are Orphan Dialogue Boxes that will be removed';
    this.isValidMessageCheck = true;
    this.checkListOrphans(Name.DIALOGUE);
  }

  /**
   * ========== VERIFICAÇÃO DE DIÁLOGO EM LEVELS E MICROLOOPS ==========
   * Verifica se um diálogo específico existe em Levels ou Microloops
   * Utilizado para determinar se um diálogo é órfão ou possui referências válidas
   * @param dialogueId - ID do diálogo a ser verificado
   * @returns true se o diálogo existir em Levels ou Microloops, false caso contrário
   */
  checkDialogueInLevelDialogueidsArray(dialogueId: string) {
    return (
      this._levelService.models.some(level => level.id === dialogueId) ||
      this._microloopService.models.some(microloop => microloop.id === dialogueId)
    );
  }
  // Fim do Dialogue


  /**
   * ========== REDIRECIONAMENTO PARA ELEMENTO ESPECÍFICO ==========
   * Navega para a página específica do elemento selecionado na lista de órfãos
   * Constrói a URL correta baseada no tipo de elemento e suas características
   * @param result - Objeto Search.Result contendo informações do elemento a ser redirecionado
   */
  public redirectTo(result: Search.Result) {
    // Verifica se o elemento já foi removido
    if (result.wasRemoved) return;

    let link: string = links[result.typeName].path; // Obtém o link base para o tipo de elemento
    const cleanId = result.id.replace(/(\.[^.]+)$/, ""); // Remove a última parte do ID
    const obj = this._searchService.getObj(result); // Obtém o objeto completo do elemento

    // Verifica se o elemento é órfão ou não possui objeto válido
    if (result.local === 'Orphan' || this.location.length === 0 || obj === undefined) {
      return Alert.showError('Id not found in redirect');
    }

    // Tratamento especial para Microloops (IDs que contêm 'ML')
    if (result.id.includes('ML')) {
      const idML = cleanId.split('.').slice(0, 2).join('.');
      if (Area.getSubIdFrom(idML)) {
        link = 'microloops/' + cleanId.split('.')[0] + '.' + cleanId.split('.')[1] +
          '/dialogues/' + cleanId.split('.')[0] + '.' + cleanId.split('.')[1] + '.' + cleanId.split('.')[2];
      } else {
        return Alert.showError('Id not found in redirect');
      }
    }

    // Processamento para elementos que não são Microloops
    if (obj && !cleanId.includes('ML')) {
      // Substitui placeholder :levelId no link
      if (link.includes(':levelId')) {
        if (Level.getSubIdFrom(obj.id) !== '..') {
          link = link.replace(':levelId', Level.getSubIdFrom(obj.id));
        } else {
          return Alert.showError('Id not found in redirect');
        }
      }

      // Substitui placeholder :dialogueId no link
      if (link.includes(':dialogueId')) {
        if (Dialogue.getSubIdFrom(obj.id, 'PT-BR') !== '..') {
          link = link.replace(':dialogueId', Dialogue.getSubIdFrom(obj.id, 'PT-BR'));
        } else {
          return Alert.showError('Id not found in redirect');
        }
      }

      // Tratamento especial para boundItems
      if (link.includes('boundItems')) {
        let itemClassId = this._itemClassService.models.filter(itemClass => itemClass.itemIds.includes(obj.id))[0]?.id
        this._router.navigate([link, { id: itemClassId }], { fragment: obj?.id });
        return;
      }
    }

    // Navegação final para o link construído
    this._router.navigate([link], { fragment: obj?.id });
  }

  /**
   * ========== PROCESSAMENTO DE ID ==========
   * Processa um ID removendo partes específicas baseadas em padrões
   * Remove marcadores e sufixos desnecessários do ID
   * @param id - ID a ser processado
   * @returns ID processado sem os sufixos removidos
   */
  getId(id: string) {
    // Verifica se o id contém 'mrk' seguido de dígitos e uma palavra, e se sim, remove essas duas partes
    // Senão, remove apenas a última parte
    const novoId = id.replace(/\.mrk\d+\.\w+$/, '').replace(/\.\w+$/, '');
    return novoId;
  }

  /**
   * ========== REDIRECIONAMENTO PARA CONFIGURAÇÕES ==========
   * Navega de volta para a página principal de configurações
   */
  public redirectToSettings() {
    this._router.navigate(['settings']);
  }

  /**
   * ========== REMOÇÃO EM LOTE DE ÓRFÃOS ==========
   * Remove todos os órfãos da lista atual baseado no tipo de elemento sendo processado
   * Utiliza os services específicos para realizar a remoção e atualiza as listas de controle
   */
  removeAllOrphans() {
    // Função auxiliar para adicionar itens únicos ao array
    const addIfNotExists = (list, item) => {
      if (!list.includes(item)) {
        list.push(item);
      }
    };

    if (this.rBToBeRemoved.length > 0 && this.infoIndex === 0) {
      let localIds = this.rBToBeRemoved.map(item => item.id);
      this._roadBlockService.svcToRemove(localIds);
      addIfNotExists(this.listEmptyOfOrphans, Name.ROADBLOCK);
    }
    else if (this.objectiveToBeRemoved.length > 0 && this.infoIndex === 1) {
      let objectIds = this.objectiveToBeRemoved.map(item => item.id);
      this._objectiveService.svcToRemove(objectIds);
      addIfNotExists(this.listEmptyOfOrphans, Name.OBJECTIVE);
    }
    else if (this.markerToBeRemoved.length > 0 && this.infoIndex === 2) {
      let markersIds = this.markerToBeRemoved.map(item => item.id);
      this._markerService.svcToRemove(markersIds);
      addIfNotExists(this.listEmptyOfOrphans, Name.MARKER);
    }
    else if (this.eventToBeRemoved.length > 0 && this.infoIndex === 3) {
      let event = this.eventToBeRemoved.map(item => item.id);
      this._eventService.svcToRemove(event);
      addIfNotExists(this.listEmptyOfOrphans, Name.EVENT);
    }
    else if (this.speechToBeRemoved.length > 0 && this.infoIndex === 4) {
      let speech = this.speechToBeRemoved.map(item => item.id);
      this._speechService.svcToRemove(speech);
      addIfNotExists(this.listEmptyOfOrphans, Name.SPEECH);
    }
    else if (this.storyboxToBeRemoved.length > 0 && this.infoIndex === 5) {
      let story = this.storyboxToBeRemoved.map(item => item.id);
      this._storyboxService.svcToRemove(story);
      addIfNotExists(this.listEmptyOfOrphans, Name.STORY_BOX);
    }
    else if (this.optionToBeRemoved.length > 0 && this.infoIndex === 6) {
      let option = this.optionToBeRemoved.map(item => item.id);
      this._optionService.svcToRemove(option);
      addIfNotExists(this.listEmptyOfOrphans, Name.OPTION);
    }
    else if (this.optionBoxToBeRemoved.length > 0 && this.infoIndex === 7) {
      let optionBox = this.optionBoxToBeRemoved.map(item => item.id);
      this._optionboxService.svcToRemove(optionBox);
      addIfNotExists(this.listEmptyOfOrphans, Name.OPTION_BOX);
    }
    else if (this.answerDilemmaBoxToBeRemoved.length > 0 && this.infoIndex === 8) {
      let answerDilemmaBox = this.answerDilemmaBoxToBeRemoved.map(item => item.id);
      this._answerDilemmaBoxService.svcToRemove(answerDilemmaBox);
      addIfNotExists(this.listEmptyOfOrphans, Name.ANSWERDILEMMABOX);
    }
    else if (this.dilemmaToBeRemoved.length > 0 && this.infoIndex === 9) {
      let dilemma = this.dilemmaToBeRemoved.map(item => item.id);
      this._dilemmaService.svcToRemove(dilemma);
      addIfNotExists(this.listEmptyOfOrphans, Name.DILEMMA);
    }
    else if (this.dilemmaBoxToBeRemoved.length > 0 && this.infoIndex === 10) {
      let dilemmaBox = this.dilemmaBoxToBeRemoved.map(item => item.id);
      this._dilemmaBoxService.svcToRemove(dilemmaBox);
      addIfNotExists(this.listEmptyOfOrphans, Name.DILEMMABOX);
    }
    else if (this.dialogueOrphanIdsToRemove.length > 0 && this.infoIndex === 11) {
      let dialogue = this.dialogueOrphanIdsToRemove.map(item => item.id);
      this._dialogueService.svcToRemove(dialogue);
      this._microloopService.svcToRemove(dialogue);
      addIfNotExists(this.listEmptyOfOrphans, Name.DIALOGUE);
    }

    this.isValidMessageCheck = false;
    this._change.detectChanges();
    this.totalRemovalMessages();
  }

  /**
   * ========== REMOÇÃO INDIVIDUAL DE ÓRFÃO ==========
   * Remove um órfão específico da lista baseado no seu ID
   * Atualiza a interface e reinicia a verificação para o tipo atual
   * @param id - ID do elemento órfão a ser removido
   */
  async removeLineOrphan(id: string) {
    // Remoção de RoadBlock individual
    if (this.rBToBeRemoved.length > 0 && this.infoIndex === 0) {
      this._roadBlockService.svcToRemove(id);
      this.listOrphons = this.rBToBeRemoved.filter(item => item.id !== id);
      await Alert.ShowSuccess(`Id: ${id} successfully removed!`);
      this.rBToBeRemoved = [];
      this.isValidMessageCheck = true;
      this.checkOrphanRoadblocks();
    }
    else if (this.objectiveToBeRemoved.length > 0 && this.infoIndex === 1) {
      this._objectiveService.svcToRemove(id);
      this.listOrphons = this.objectiveToBeRemoved.filter(obj => obj.id != id);
      await Alert.ShowSuccess(`Id: ${id} successfully removed!`);
      this.objectiveToBeRemoved = [];
      this.checkOrphanObjective();
    }
    else if (this.markerToBeRemoved.length > 0 && this.infoIndex === 2) {
      this._markerService.svcToRemove(id);
      this.listOrphons = this.markerToBeRemoved.filter(marker => marker.id !== id);
      await Alert.ShowSuccess(`Id: ${id} successfully removed!`);
      this.markerToBeRemoved = [];
      this.checkOrphanMarkers();
    }
    else if (this.eventToBeRemoved.length > 0 && this.infoIndex === 3) {
      this._eventService.svcToRemove(id);
      this.listOrphons = this.eventToBeRemoved.filter(event => event.id !== id);
      await Alert.ShowSuccess(`Id: ${id} successfully removed!`);
      this.eventToBeRemoved = [];
      this.checkOrphanEvents();
    }
    else if (this.speechToBeRemoved.length > 0 && this.infoIndex === 4) {
      this._speechService.svcToRemove(id);
      this.listOrphons = this.speechToBeRemoved.filter(event => event.id !== id);
      await Alert.ShowSuccess(`Id: ${id} successfully removed!`);
      this.speechToBeRemoved = [];
      this.checkOrphanSpeeches();
    }
    else if (this.storyboxToBeRemoved.length > 0 && this.infoIndex === 5) {
      this._storyboxService.svcToRemove(id);
      this._storyboxService.removeIdStoryboxPkg(id);
      this.removeSubComponentOrphansStoryBox(id);
      this.listOrphons = this.storyboxToBeRemoved.filter(event => event.id !== id);
      await Alert.ShowSuccess(`Id: ${id} successfully removed!`);
      this.storyboxToBeRemoved = [];
      this.checkOrphanStorybox();
    }
    else if (this.optionToBeRemoved.length > 0 && this.infoIndex === 6) {
      this._optionService.svcToRemove(id);
      this.listOrphons = this.optionToBeRemoved.filter(event => event.id !== id);
      await Alert.ShowSuccess(`Id: ${id} successfully removed!`);
      this.optionToBeRemoved = [];
      this.checkOrphanOptions();
    }
    else if (this.optionBoxToBeRemoved.length > 0 && this.infoIndex === 7) {
      this._optionboxService.svcToRemove(id);
      this._optionboxService.removeIdsOptionboxPkg(id);
      this.removeSubComponentOrphansStoryBox(id);
      this.listOrphons = this.optionBoxToBeRemoved.filter(event => event.id !== id);
      await Alert.ShowSuccess(`Id: ${id} successfully removed!`);
      this.optionBoxToBeRemoved = [];
      this.checkOrphanOptionBox();
    }
    else if (this.answerDilemmaBoxToBeRemoved.length > 0 && this.infoIndex === 8) {
      this._answerDilemmaBoxService.svcToRemove(id);
      this.listOrphons = this.answerDilemmaBoxToBeRemoved.filter(event => event.id !== id);
      await Alert.ShowSuccess(`Id: ${id} successfully removed!`);
      this.answerDilemmaBoxToBeRemoved = [];
      this.checkOrphanAnswerDilemma();
    }
    else if (this.dilemmaToBeRemoved.length > 0 && this.infoIndex === 9) {
      this._dilemmaService.svcToRemove(id);
      this.listOrphons = this.dilemmaToBeRemoved.filter(event => event.id !== id);
      await Alert.ShowSuccess(`Id: ${id} successfully removed!`);
      this.dilemmaToBeRemoved = [];
      this.checkOrphanDilemma();
    }
    else if (this.dilemmaBoxToBeRemoved.length > 0 && this.infoIndex === 10) {
      this._dilemmaBoxService.svcToRemove(id);
      this._dilemmaBoxService.removeIdsDilemmaBoxPkg(id);
      this.removeDilemmaBoxSubComponents(id);
      this.listOrphons = this.dilemmaBoxToBeRemoved.filter(event => event.id !== id);
      await Alert.ShowSuccess(`Id: ${id} successfully removed!`);
      this.dilemmaBoxToBeRemoved = [];
      this.checkOrphanDilemmaBox();
    }
    else if (this.dialogueOrphanIdsToRemove.length > 0 && this.infoIndex === 11) {
      this._dialogueService.svcToRemove(id);
      this.listOrphons = this.dialogueOrphanIdsToRemove.filter(event => event.id !== id);
      await Alert.ShowSuccess(`Id: ${id} successfully removed!`);
      this.dialogueOrphanIdsToRemove = [];
      this.checkOrphanDialogues();
    }

  }

  /**
   * ========== REMOÇÃO DE SUBCOMPONENTES ÓRFÃOS DE STORYBOX ==========
   * Remove subcomponentes específicos (Events e Speeches) que estão órfãos
   * Identifica o tipo de subcomponente pelo ID e chama os métodos apropriados de remoção
   * @param id - ID do subcomponente a ser removido
   */
  removeSubComponentOrphansStoryBox(id: string) {
    // Remove Events órfãos
    if (id.includes('evt')) {
      this._eventService.svcToRemove(id);
      this._eventService.removeIdEventPkg(id);
    }
    // Remove Speeches órfãos
    else if(id.includes('spc')) {
      this._speechService.svcToRemove(id);
      this._speechService.removeIdsSpeechPkg(id);
    }
  }

  /**
   * ========== REMOÇÃO DE SUBCOMPONENTES DE DILEMMABOX ==========
   * Remove subcomponentes específicos (Dilemmas e AnswerDilemmaBoxes) que estão órfãos
   * Identifica o tipo de subcomponente pelo ID e chama os métodos apropriados de remoção
   * @param id - ID do subcomponente a ser removido
   */
  removeDilemmaBoxSubComponents(id: string) {
    // Remove Dilemmas órfãos
    if (id.includes('dlm')) {
      this._dilemmaService.removeIdsDilemmaPkg(id);
      this._dilemmaService.svcToRemove(id);
    }
    // Remove AnswerDilemmaBoxes órfãos
    else  if (id.includes('ADB')) {
      this._answerDilemmaBoxService.removeIdAnswerDilemmaBoxPkg(id);
      this._answerDilemmaBoxService.svcToRemove(id);
    }
  }


  /**
   * ========== MENSAGENS DE REMOÇÃO TOTAL ==========
   * Exibe mensagens de sucesso após a remoção em lote de órfãos
   * Limpa as listas correspondentes e avança para o próximo tipo de verificação
   */
  async totalRemovalMessages() {
    this.isRemove = true;

    // Mensagem de sucesso para RoadBlocks removidos
    if (this.rBToBeRemoved.length > 0 && this.infoIndex === 0) {
      await Alert.ShowSuccess('RoadBlocks orphans list successfully removed!');
      this.rBToBeRemoved = [];
      this.listOrphons = [];
      this.nextAllOrphans();
    }
    else if (this.objectiveToBeRemoved.length > 0 && this.infoIndex === 1) {
      await Alert.ShowSuccess('Objectives orphans list successfully removed!');
      this.objectiveToBeRemoved = [];
      this.listOrphons = [];      
      this.nextAllOrphans();
    }
    else if (this.markerToBeRemoved.length > 0 && this.infoIndex === 2) {
      await Alert.ShowSuccess('Markers orphans list successfully removed!');
      this.markerToBeRemoved = [];
      this.listOrphons = [];      
      this.nextAllOrphans();
    }
    else if (this.eventToBeRemoved.length > 0 && this.infoIndex === 3) {
      await Alert.ShowSuccess('Event orphans list successfully removed!');
      this.eventToBeRemoved = [];
      this.listOrphons = [];      
      this.nextAllOrphans();
    }
    else if (this.speechToBeRemoved.length > 0 && this.infoIndex === 4) {
      await Alert.ShowSuccess('Speech orphans list successfully removed!');
      this.speechToBeRemoved = [];
      this.listOrphons = [];      
      this.nextAllOrphans();
    }
    else if (this.storyboxToBeRemoved.length > 0 && this.infoIndex === 5) {
      await Alert.ShowSuccess('StoryBox orphans list successfully removed!');
      this.storyboxToBeRemoved = [];
      this.listOrphons = [];      
      this.nextAllOrphans();
    }
    else if (this.optionToBeRemoved.length > 0 && this.infoIndex === 6) {
      await Alert.ShowSuccess('Option orphans list successfully removed!');
      this.optionToBeRemoved = [];
      this.listOrphons = [];      
      this.nextAllOrphans();
    }
    else if (this.optionBoxToBeRemoved.length > 0 && this.infoIndex === 7) {
      await Alert.ShowSuccess('OptionBox orphans list successfully removed!');
      this.optionBoxToBeRemoved = [];
      this.listOrphons = [];      
      this.nextAllOrphans();
    }
    else if (this.answerDilemmaBoxToBeRemoved.length > 0 && this.infoIndex === 8) {
      await Alert.ShowSuccess('Answer DilemmaBox orphans list successfully removed!');
      this.answerDilemmaBoxToBeRemoved = [];
      this.listOrphons = [];     
      this.nextAllOrphans();
    }
    else if (this.dilemmaToBeRemoved.length > 0 && this.infoIndex === 9) {
      await Alert.ShowSuccess('Dilemma orphans list successfully removed!');
      this.dilemmaToBeRemoved = [];
      this.listOrphons = [];      
      this.nextAllOrphans();
    }
    else if (this.dilemmaBoxToBeRemoved.length > 0 && this.infoIndex === 10) {
      await Alert.ShowSuccess('DilemmaBox orphans list successfully removed!');
      this.dilemmaBoxToBeRemoved = [];
      this.listOrphons = [];      
      this.nextAllOrphans();
    }
    else if (this.dialogueOrphanIdsToRemove.length > 0 && this.infoIndex === 11) {
      await Alert.ShowSuccess('Dilogue orphans list successfully removed!');
      this.dialogueOrphanIdsToRemove = [];
      this.listOrphons = [];      
      await this.settingsComponent.toPromptExportData('dsa'); //export DSA
    }
  }


  /**
   * ========== NAVEGAÇÃO ENTRE TIPOS DE ÓRFÃOS ==========
   * Controla o fluxo de verificação entre diferentes tipos de elementos órfãos
   * Define a sequência de verificação e avança para o próximo tipo automaticamente
   */
  async nextAllOrphans() {
    // Define todos os tipos de órfãos e sua ordem de verificação
    this.orphanTypes = [
      { index: 0, name: Name.ROADBLOCK },
      { index: 1, name: Name.OBJECTIVE },
      { index: 2, name: Name.MARKER },
      { index: 3, name: Name.EVENT },
      { index: 4, name: Name.SPEECH },
      { index: 5, name: Name.STORY_BOX },
      { index: 6, name: Name.OPTION },
      { index: 7, name: Name.OPTION_BOX },
      { index: 8, name: Name.ANSWERDILEMMABOX },
      { index: 9, name: Name.DILEMMA },
      { index: 10, name: Name.DILEMMABOX },
      { index: 11, name: Name.DIALOGUE }
    ];

    // Adiciona à lista de tipos que possuem órfãos (se não estiver em processo de remoção)
    if(!this.isRemove) {
      this.addListOrphansNoEmpty();
    }

    // Avança para o próximo tipo na sequência
    if (this.infoIndex < this.orphanTypes.length -1) {
      const currentIndex = this.orphanTypes.findIndex((type) => type.index === this.infoIndex);
      const nextIndex = currentIndex + 1;
      const nextType = this.orphanTypes[nextIndex];

      this.infoIndex = nextType.index;
      this.nextMetodo(nextType.name);
    }
    // Caso especial para o último tipo (Dialogues)
    else  if(this.infoIndex === this.orphanTypes.length) {
      this.checkOrphanDialogues();
    }
    // Todos os tipos foram processados
    else {
      console.warn("Todos os órfãos foram processados.");
    }
  }

  /**
   * ========== ADIÇÃO À LISTA DE ÓRFÃOS NÃO VAZIOS ==========
   * Adiciona tipos de órfãos que possuem elementos à lista de tipos com órfãos
   * Inclui a contagem de órfãos para cada tipo e evita duplicatas
   */
  addListOrphansNoEmpty() {
    // Função auxiliar para evitar duplicatas
    const addIfNotExists = (list, item) => {
      if (!list.some(existing => existing.name === item.name)) {
        list.push(item);
      }
    };

    // Adiciona cada tipo que possui órfãos à lista com sua respectiva contagem
    if (this.rBToBeRemoved.length > 0) {
      addIfNotExists(this.listOrphansNoEmpty, { name: Name.ROADBLOCK, amount: this.rBToBeRemoved.length });
    }
    if (this.objectiveToBeRemoved.length > 0) {
      addIfNotExists(this.listOrphansNoEmpty, { name: Name.OBJECTIVE, amount: this.objectiveToBeRemoved.length });
    }
    if (this.markerToBeRemoved.length > 0) {
      addIfNotExists(this.listOrphansNoEmpty, { name: Name.MARKER, amount: this.markerToBeRemoved.length });
    }
    if (this.eventToBeRemoved.length > 0) {
      addIfNotExists(this.listOrphansNoEmpty, { name: Name.EVENT, amount: this.eventToBeRemoved.length });
    }
    if (this.speechToBeRemoved.length > 0) {
      addIfNotExists(this.listOrphansNoEmpty, { name: Name.SPEECH, amount: this.speechToBeRemoved.length });
    }
    if (this.storyboxToBeRemoved.length > 0) {
      addIfNotExists(this.listOrphansNoEmpty, { name: Name.STORY_BOX, amount: this.storyboxToBeRemoved.length });
    }
    if (this.optionToBeRemoved.length > 0) {
      addIfNotExists(this.listOrphansNoEmpty, { name: Name.OPTION, amount: this.optionToBeRemoved.length });
    }
    if (this.optionBoxToBeRemoved.length > 0) {
      addIfNotExists(this.listOrphansNoEmpty, { name: Name.OPTION_BOX, amount: this.optionBoxToBeRemoved.length });
    }
    if (this.answerDilemmaBoxToBeRemoved.length > 0) {
      addIfNotExists(this.listOrphansNoEmpty, { name: Name.ANSWERDILEMMABOX, amount: this.answerDilemmaBoxToBeRemoved.length });
    }
    if (this.dilemmaToBeRemoved.length > 0) {
      addIfNotExists(this.listOrphansNoEmpty, { name: Name.DILEMMA, amount: this.dilemmaToBeRemoved.length });
    }
    if (this.dilemmaBoxToBeRemoved.length > 0) {
      addIfNotExists(this.listOrphansNoEmpty, { name: Name.DILEMMABOX, amount: this.dilemmaBoxToBeRemoved.length });
    }
    if (this.dialogueOrphanIdsToRemove.length > 0) {
      addIfNotExists(this.listOrphansNoEmpty, { name: Name.DIALOGUE, amount: this.dialogueOrphanIdsToRemove.length });
    }
  }

  /**
   * ========== DISPATCHER DE MÉTODOS DE VERIFICAÇÃO ==========
   * Chama o método de verificação apropriado baseado no nome do tipo de órfão
   * Limpa a lista atual de órfãos e direciona para o método correto de verificação
   * @param name - Nome do tipo de órfão para o qual chamar o método de verificação
   */
  nextMetodo(name: string) {
    this.listOrphons = []; // Limpa a lista atual de órfãos

    // Direciona para o método de verificação apropriado baseado no tipo
    if (name === Name.ROADBLOCK) {
      this.checkOrphanRoadblocks();
    } else if (name === Name.OBJECTIVE) {
      this.checkOrphanObjective();
    } else if (name === Name.MARKER) {
      this.checkOrphanMarkers();
    } else if (name === Name.EVENT) {
      this.checkOrphanEvents();
    } else if (name === Name.SPEECH) {
      this.checkOrphanSpeeches();
    } else if (name === Name.STORY_BOX) {
      this.checkOrphanStorybox();
    } else if (name === Name.OPTION) {
      this.checkOrphanOptions();
    } else if (name === Name.OPTION_BOX) {
      this.checkOrphanOptionBox();
    } else if (name === Name.ANSWERDILEMMABOX) {
      this.checkOrphanAnswerDilemma();
    } else if (name === Name.DILEMMA) {
      this.checkOrphanDilemma();
    } else if (name === Name.DILEMMABOX) {
      this.checkOrphanDilemmaBox();
    } else {
      // Caso padrão: verifica Dialogues órfãos
      this.checkOrphanDialogues();
    }
  }


  /**
   * ========== NAVEGAÇÃO PARA TIPO ESPECÍFICO DE ÓRFÃO ==========
   * Remove um tipo específico da lista de órfãos não vazios e navega para sua verificação
   * Utilizado quando o usuário clica em um tipo específico de órfão para visualizar
   * @param orphan - Objeto contendo informações do tipo de órfão selecionado
   * @param index - Índice do órfão na lista para remoção
   */
  getNoClenOrphan(orphan: any, index: number) {
    this.listOrphansNoEmpty.splice(index, 1); // Remove o tipo da lista de órfãos não vazios
    this.nextMetodo(orphan.name); // Navega para a verificação do tipo específico
  }

}
