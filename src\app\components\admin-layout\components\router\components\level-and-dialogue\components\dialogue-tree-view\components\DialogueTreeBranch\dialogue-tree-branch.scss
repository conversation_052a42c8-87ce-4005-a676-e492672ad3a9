
// Removed hardcoded $line-height - now using dynamic height from component
$line-width: 4px;
$line-color: white;

.top-branch
{
  display: flex;
}

.middle-branch
{
  position: relative;
  display: flex; // Enable flex layout for scenarios that need it
}

.line
{
  position: absolute;
  height: $line-width;
/*   min-width: $line-width; */
  margin-left: auto;
  margin-right: auto;
  width: 100%;
  background-color: $line-color;
}

.middle-line
{
  position: absolute;
  height: $line-width;
  background-color: $line-color;
  // Width and margin-left will be set dynamically via ngStyle
}

.bottom-branch
{
  display: flex;
}

.branch-space
{
  flex-grow: 1;
  // Height is now set dynamically via ngStyle in template
  // Default fallback height for cases where dynamic height isn't set
  min-height: 50px;
}


 .branch-space.has-option-roadblocks
 {
  min-width: 815px;
 }

.branch-line
{
  // Height is now set dynamically via ngStyle in template
  // Default fallback height for cases where dynamic height isn't set
  min-height: 40px;
  width: $line-width;
  margin-left: auto;
  margin-right: auto;
  background-color: $line-color;
}



.branch-arrow
{
  width: 8px;
  height: 8px;
  margin: auto;
  border-left:
    8px solid transparent;
  border-right:
    8px solid transparent;
  border-top:
    14px solid $line-color;
}
