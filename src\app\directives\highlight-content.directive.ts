import { Directive, ElementRef, Input, OnChanges, SimpleChanges } from '@angular/core';
import { SearchOnPageService } from '../services/search-on-page.service';

@Directive
({
  selector: '[highlightContent]'
})
export class HighlightContentDirective implements OnChanges 
{
  @Input('highlightContent') content: string;
  @Input() isInputFocused: boolean;
  runOnce: boolean = true;	
  match: string[] = [];	
  words: string[] = [];
  constructor(private elementRef: ElementRef, private _searchOnPageService: SearchOnPageService) {}

  ngOnChanges(changes: SimpleChanges) 
  {
    if (changes['isInputFocused'] || (changes['content'] && this.content)) 
    {
      this.highlightContent();
    }
  }

  	
  private highlightContent() 	
  {	
   const regex = /{{([^}]+)}}|««([^»]+)»»|\[\[([^\]]+)\]\]|\<\<([^>]+)\>\>|#\$([^\$]+)\$#|\(\(([^)]+)\)\>/g;	
   const content = this.elementRef.nativeElement.innerText || this.content;	
   
   let contentToHighlight = content.replace(regex, (match, word1, word2, word3, word4, word5) => 	
   {	
     const word = word1 || word2 || word3 || word4 || word5;	
   
     if (!this.match?.includes(match) && match != 'undefined' && match != undefined) 	
     {
       this.match.push(match);	
       this.words.push(word);	
       this._searchOnPageService.words.push(word);	
     }	
     return word;	
   });	
   if (this.isInputFocused)	
   {       	
       contentToHighlight = this.content;	
   }	
   else if (!this.isInputFocused)	
   {	
     contentToHighlight = contentToHighlight.replace(regex, (match, word1, word2, word3, word4, word5) => 	
     {	
       const word = word1 || word2 || word3 || word4 || word5;	
       return word;	
     });	
   }	
   this.elementRef.nativeElement.innerText = contentToHighlight;	
 }
}
