<div style="margin-top: 20px;" class="list-header-row update">
  <div style="padding-top: 10px;" class="card">
    <app-header-with-buttons [cardTitle]="'Elemental Affinities'" [cardDescription]="''"
      [valueBossLevel]="valueBossLevel" [nameClass]="nameClass" [nameRarity]="nameRarity" [type]="type"
      [rightButtonTemplates]="[excelButtonTemplate]" [isBackButtonEnabled]="false">
    </app-header-with-buttons>
  </div>
</div>
<div class="card" style="margin-bottom: 25px;">
  <table class="table table-list">
    <thead>
      <tr>
        <th class="dark-gray" colspan="7">
          <h4>Elemental Affinities</h4>
        </th>
      </tr>
      <tr>
        <th (click)="sortListByStatus()" class="th-clickable gray" rowspan="3">Skill</th>
        <th (click)="sortListByAcronym()" class="th-clickable gray" rowspan="3">Acronym</th>
        <th class="gray" colspan="3">DEF (%)</th>
        <th class="gray">ATK (%)</th>
        <th class=" th-clickable gray" rowspan="3">Ascension Order</th>
      </tr>
      <tr>
        <th (click)="sortListBySPDEF()" class="th-clickable gray" rowspan="2">Sp. DEF</th>
        <th class="gray">Resistence (+)</th>
        <th class="gray">Weakeness (-)</th>
        <th (click)="sortListBySPATK()" class="th-clickable gray" rowspan="2">Sp. ATK</th>
      </tr>
      <tr>
        <th (click)="sortListByResistence()" class="th-clickable light-gray">Receives Less DAMAGE</th>
        <th (click)="sortListByWeakeness()" class="th-clickable light-gray">Receives More DAMAGE</th>
      </tr>
    </thead>
    <tbody>
      <ng-container *ngFor="let status of statusInfo">
        <tr>
          <td>{{status.status}}</td>

          <td>
            <ng-container *ngIf="status?.acronym">
              {{status.acronym}}
            </ng-container>
            <ng-container *ngIf="status.acronym == undefined || status.acronym == ''">
              <span [ngClass]="{'text-danger pulse-red': status.acronym == undefined || status.acronym == ''}">Campo obrigatório</span>
            </ng-container>        
          </td>
          <td class="td-id">
            <input class="background-input-table-color form-control form-short " placeholder=" " type="string" #spDef
              [value]="getSpDef(status)" (change)="changeSPDEF(status, spDef.value)" />
          </td>
          <td class="td-id">
            <input class="background-input-table-color form-control form-short " placeholder=" " type="number"
              #resistence [value]="status.resistence" [ngClass]="{'empty-input': !resistence.value}"
              (change)="changeResitence(status, resistence.value)" />
          </td>
          <td class="td-id">
            <input class="background-input-table-color form-control form-short " placeholder=" " type="number"
              #weakeness [value]="status.weakeness" (change)="changeWeakeness(status, weakeness.value)" />
          </td>
          <td class="td-id">
            <input class="background-input-table-color form-control form-short " placeholder=" " type="number" #spAtk
              [value]="status.spAtk" [ngClass]="{'empty-input': !spAtk.value}"
              (change)="changeSPATK(status, spAtk.value)" />
          </td>
          <td class="td-id">
            <input class="background-input-table-color form-control form-short " placeholder=" " type="number"
              #ascensionOrder [value]="status.ascensionOrder" [ngClass]="{'empty-input': !ascensionOrder.value}"
              (change)="changeAscension(status, ascensionOrder.value)" />
          </td>
        </tr>
      </ng-container>
    </tbody>
  </table>
</div>