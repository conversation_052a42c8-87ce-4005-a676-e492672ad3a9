import { IdPrefixes} from 'src/lib/darkcloud/dialogue-system';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { Base } from './Base';

export class Maps  extends Base<Data.Hard.IMaps, Data.Result.IMaps>  implements Required<Data.Hard.IMaps>
{
  private static generateId(index: number): string 
  {
    return IdPrefixes.MAPS + index;
  }

  constructor(
    index: number,
    name: string,
    dataAccess: Maps['TDataAccess']
  ) 
  {
    super(
      {
        hard: {
          id: Maps.generateId(index),
          name,
        },
      },
      dataAccess
    );
  }
  protected getInternalFetch() {
    return {};
  }
  

  public get baseLevel(): number
  {
    return this.hard.baseLevel;
  }
  public set baseLevel(value: number)
  {
    this.hard.baseLevel = value;
  }

  public get area(): string
  {
    return this.hard.area;
  }
  public set area(value: string)
  {
    this.hard.area = value;
  }

  public get name(): string
  {
    return this.hard.name;
  }
  public set name(value: string)
  {
    this.hard.name = value;
  }

  public get description(): string
  {
    return this.hard.description;
  }
  public set description(value: string)
  {
    this.hard.description = value;
  }

  public get note(): string
  {
    return this.hard.note;
  }
  public set note(value: string)
  {
    this.hard.note = value;
  }
  public get classification(): string
  {
    return this.hard.classification;
  }
  public set classification(value: string)
  {
    this.hard.classification = value;
  }

  public get revisionCounterNameAI(): number 
  {
    return this.hard.revisionCounterNameAI;
  }
  public set revisionCounterNameAI(value: number) 
  {
    this.hard.revisionCounterNameAI = value;
  }
  public get revisionCounterDescriptionAI(): number 
  {
    return this.hard.revisionCounterDescriptionAI;
  }
  public set revisionCounterDescriptionAI(value: number) 
  {
    this.hard.revisionCounterDescriptionAI = value;
  } 
  public get isReviewedName(): boolean 
  {
    return this.hard.isReviewedName;
  }
  public set isReviewedName(value: boolean) 
  {
    this.hard.isReviewedName = value;
  } 
  public get isReviewedDescription(): boolean 
  {
    return this.hard.isReviewedDescription;
  }
  public set isReviewedDescription(value: boolean) 
  {
    this.hard.isReviewedDescription = value;
  }

  public get hierarchyCodeArea(): string
  {
    return this.hard.hierarchyCodeArea;
  }
  public set hierarchyCodeArea(value: string)
  {
    this.hard.hierarchyCodeArea = value;
  }
}
