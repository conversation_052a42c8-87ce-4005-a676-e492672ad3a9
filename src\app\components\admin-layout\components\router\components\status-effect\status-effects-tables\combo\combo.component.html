<div class="div-container">
    <div [ngClass]="listComboTable.length > 0 ? 'width-2880' : ''">
        <table class="table table-list borderList">
            <thead>    
                <tr>
                    <th [attr.colspan]="titles.length +1">
                        <h3>Combo</h3>
                    </th>
                    <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'" class="addButton"
                        [buttonTemplates]="[excelButtonTemplate]">
                    </app-button-group>
                </tr>
                <ng-container *ngIf="listComboTable.length > 0">           
                    <tr>
                        <th class="default-color">Order</th>
                        <th class="default-color">ID</th>
                        <th class="default-color">CATEGORY</th>
                        <th class="skill-resist" colspan="6">SKILL RESIST USER</th>
                        <th class="default-color">OPERATOR</th>
                        <th class="default-color">SP ATK VALUE</th>
                        <th class="default-color">SP DEF VALUE</th>
                        <th class="default-color">STATUS EFFECT NAME</th>
                        <th class="default-color">DESCRIPTION</th>
                        <th class="default-color">POWER POINTS (PP)</th>
                        <th class="default-color">DURATION (TURNS)</th>
                      </tr>                      
                </ng-container>
            </thead>
            <ng-container *ngIf="listComboTable.length > 0">
                <tbody>
                    <tr *ngFor="let item of listComboTable; let i = index">
                        <td style="background-color: #ddd; width: 30px">{{ i + 1 }}</td>
                            <td class="td-id aligTitle" style="width: 6%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #idCombo [ngClass]="{'empty-input': !idCombo.value}"
                                    [value]="item.idCombo" (change)="changeCombo(i,'idCombo',  idCombo.value)" />
                            </td>
                             <td class="td-id aligTitle" style="width: 6%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #category [ngClass]="{'empty-input': !category.value}"
                                    [value]="item.category" (change)="changeCombo(i,'category',  category.value)" />
                            </td>
                            <!-- SKILL RESIST USER - 6 posições -->
                            <td class="td-id aligTitle" style="width: 5%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #skillResistUser0 [ngClass]="{'empty-input': !skillResistUser0.value}"
                                    [value]="item.skillResistUser?.[0] || ''" (change)="changeCombo(i,'skillResistUser', skillResistUser0.value, 0)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 5%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #skillResistUser1 [ngClass]="{'empty-input': !skillResistUser1.value}"
                                    [value]="item.skillResistUser?.[1] || ''" (change)="changeCombo(i,'skillResistUser', skillResistUser1.value, 1)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 5%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #skillResistUser2 [ngClass]="{'empty-input': !skillResistUser2.value}"
                                    [value]="item.skillResistUser?.[2] || ''" (change)="changeCombo(i,'skillResistUser', skillResistUser2.value, 2)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 5%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #skillResistUser3 [ngClass]="{'empty-input': !skillResistUser3.value}"
                                    [value]="item.skillResistUser?.[3] || ''" (change)="changeCombo(i,'skillResistUser', skillResistUser3.value, 3)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 5%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #skillResistUser4 [ngClass]="{'empty-input': !skillResistUser4.value}"
                                    [value]="item.skillResistUser?.[4] || ''" (change)="changeCombo(i,'skillResistUser', skillResistUser4.value, 4)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 5%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #skillResistUser5 [ngClass]="{'empty-input': !skillResistUser5.value}"
                                    [value]="item.skillResistUser?.[5] || ''" (change)="changeCombo(i,'skillResistUser', skillResistUser5.value, 5)" />
                            </td>
                            <td class="td-id td-combo">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #operator [ngClass]="{'empty-input': !operator.value}"
                                    [value]="item.operator" (change)="changeCombo(i, 'operator', operator.value)" />
                            </td>
                            <td class="td-id aligTitle td-combo">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #sp_ATK_Value [ngClass]="{'empty-input': !sp_ATK_Value.value}"
                                    [value]="item.sp_ATK_Value" (change)="changeCombo(i,'sp_ATK_Value', sp_ATK_Value.value)" />
                            </td>
                            <td class="td-id aligTitle td-combo">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #sp_DEF_Value [ngClass]="{'empty-input': !sp_DEF_Value.value}"
                                    [value]="item.sp_DEF_Value" (change)="changeCombo(i,'sp_DEF_Value', sp_DEF_Value.value)" />
                            </td>
                            <td class="td-id" style="width: 10%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #statusEffectName [ngClass]="{'empty-input': !statusEffectName.value}"
                                [value]="item.statusEffectName" (change)="changeCombo(i, 'statusEffectName', statusEffectName.value)" />
                            </td>
                            <td class="td-id" style="width: 30%; word-break: break-word;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #description [ngClass]="{'empty-input': !description.value}"
                                    [value]="item.description" (change)="changeCombo(i, 'description', description.value)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 6%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #powerPoints [ngClass]="{'empty-input': !powerPoints.value}"
                                    [value]="item.powerPoints" (change)="changeCombo(i, 'powerPoints', powerPoints.value)" />
                            </td>
                            <td class="td-id aligTitle" style="width: 6%;">
                                <input class="background-input-table-color form-control form-short" placeholder=" "
                                    type="text" #duration [ngClass]="{'empty-input': !duration.value}"
                                    [value]="item.duration" (change)="changeCombo(i, 'duration', duration.value)" />
                            </td>
                    </tr>
                </tbody>
            </ng-container>
            <ng-container *ngIf="listComboTable.length === 0">
                <div class="card" style="text-align: center; padding: 20px;">
                    <h3>The list has not yet been imported.</h3>
                </div>
            </ng-container>
        </table>
</div>
    
</div>

