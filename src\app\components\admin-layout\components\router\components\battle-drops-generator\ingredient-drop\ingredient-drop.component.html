<div style="margin-top: 20px; margin-left: 15px; margin-right: 15px;">
    <!--Header-->
    <div class="list-header-row update">
      <div class="card" style="padding-bottom: 10px;">
        <app-header-with-buttons 
          [cardTitle]="'Ingredient Drop'"
          [cardDescription]="description"
          [rightButtonTemplates]="[excelButtonTemplate]">
        </app-header-with-buttons>
      </div>
        <table class="table-list">
          <thead class="sticky">
            <tr>
              <th colspan="2" style="font-size: 20px;">Total Particle Type by Expertise</th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let particle of this.particlesList; let i = index">
              <tr>
                <td style="position: relative; background-color: rgb(107, 107, 107); text-align: center; padding-bottom: 1px; color:aliceblue">
                  {{particle.type}}
                  <div style="position: absolute; bottom: 0; left: 0; width: 100%; height: 1px; background-color: rgb(181, 181, 181);"></div>
                </td>
                <td class="border-td">
                  <input
                    placeholder=" "
                    #particleAmount
                    (change)="changeParticle(particle, particleAmount.value)"
                    [ngClass]="{'empty-input': !particleAmount.value}"
                    value="{{particle?.amount}}"
                    type="number"
                    style="border-style:solid; width: 100%;"
                  />
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
    </div>
  </div>
