import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Atributte } from 'src/app/lib/@bus-tier/models/Atributte';
import { Button } from 'src/app/lib/@pres-tier/data';
import { AtributteService } from 'src/app/services/atributte.service';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationCheckService } from 'src/app/services/translation-check.service';
import { TranslationService } from 'src/app/services/translation.service';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Alert } from 'src/lib/darkcloud';
import { FILTER_SUFFIX_PATH } from 'src/lib/darkcloud/angular/dsadmin/constants/others';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';
import { language } from 'src/lib/darkcloud/dialogue-system/game-types';

@Component({
  selector: 'app-atributte',
  templateUrl: './atributte.component.html',
  styleUrls: ['./atributte.component.scss']
})
export class AtributteComponent extends TranslatableListComponent<Atributte> {

  constructor(
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    private _atributteService: AtributteService,
    protected override _translationService: TranslationService,
    protected override _languageService: LanguageService,
    private _translationCheckService: TranslationCheckService,
  ) {
    super(_atributteService, _activatedRoute, _userSettingsService, 'name', _translationService, _languageService);
  }

  public language: language = 'PT-BR';
  public atributtoClasses: Atributte[] = [];
  activeTab: string;
  description: string;
  btnSubContext: string = FILTER_SUFFIX_PATH;
  isTab = false;
  listExcelSubContext: string[] = [];

  public readonly statusTemplate: Button.Templateable =
    {
      title: 'Add a new instance to the list',
      onClick: this.addStatus.bind(this),
      iconClass: 'pe-7s-plus',
      btnClass: Button.Klasses.FILL_GREEN,
    };

  async addStatus() {
    let atributtoClasses;

    try {
      atributtoClasses = await this._atributteService.svcPromptCreateNew();
    }
    catch (e) {
      Alert.showError("This Atributte already exists!");
      return
    }
    if (!atributtoClasses) return;

    await this._atributteService.srvAdd(atributtoClasses);

    if (this.atributtoClasses.includes(atributtoClasses)) return;
    else this.atributtoClasses.push(atributtoClasses);
  }

  override async lstInit() {
    this._atributteService.toFinishLoading();
    this.atributtoClasses = this._atributteService.models;
    this._atributteService.svcReviewAll();
    this.lstIds = this.atributtoClasses.map(x => x.id);

    setTimeout(() => this.description = `Showing ${this.atributtoClasses.length} results`, 50);

    const tab = localStorage.getItem(
      `tab-OthersComponent${FILTER_SUFFIX_PATH}`
    );
    this.activeTab = tab === 'null' || !tab ? 'keywords' : tab;
    this.isTab = false;
  }

  async removeElement(status: Atributte) {
    const confirm = await Alert.showRemoveAlert(status.atributte + ' ' + 'atributte');
    if (!confirm) return;

    this._atributteService.models = await this._atributteService.models.filter(s => s.id !== status.id);
    await this._atributteService.toSave();
    this.atributtoClasses = this.atributtoClasses.filter(s => s !== status);
  }

  public getModifierOrtography(status: Atributte) {
    this._translationService.getAtributteOrtography(status, true);
  }

  //The two below sort methods are here because it dosent work with the old code but the others parameters did worked.
  sortBySkillOrder = -1;
  sortBySkill() {
    this.sortBySkillOrder *= -1;
    this._atributteService.models.sort((a, b) => {
      return this.sortBySkillOrder * a.atributte.localeCompare(b.atributte);
    });
    this.lstFetchLists();
  }
  sortByAcronymOrder = -1;
  sortByAcronym() {
    this.sortByAcronymOrder *= -1;
    this._atributteService.models.sort((a, b) => {
      return this.sortByAcronymOrder * a.acronym.localeCompare(b.acronym);
    });
    this.lstFetchLists();
  }

  clickBtn(isActive: boolean) {
    if (isActive) {
      this.activeTab = 'Subcontext';
      this.isTab = true;
    }
    localStorage.setItem(`tab-AtributteComponent${FILTER_SUFFIX_PATH}`, this.activeTab);
  }

  clickBtnAtributteHandler(event: boolean): void {
    if (event) {
      this.activeTab = 'keywords';
      this.isTab = false;
      // Atualizar o armazenamento local para persistir o estado
      localStorage.setItem(`tab-AtributteComponent${FILTER_SUFFIX_PATH}`, this.activeTab);
    }
  }

  atributteChange(atributte: Atributte, fieldAtributte: string, value: string) {
    atributte.isReviewedAtributte = false;
    atributte.revisionCounterAtributteAI = 0;
    this.lstOnChange(atributte, 'atributte', value);
  }

  descriptionChange(atributte: Atributte, description: string, value: string) {
    atributte.isReviewedDescription = false;
    atributte.revisionCounterDescriptionAI = 0;
    this.lstOnChange(atributte, 'description', value);
  }
}
