import { Component, EventEmitter, Output } from '@angular/core';
import { Alert } from '../../../../../../../../../lib/darkcloud';
import { DefensiveIdBlocks } from '../../../../../../../../lib/@bus-tier/models';
import { Button } from '../../../../../../../../lib/@pres-tier/data';
import { DefensiveIdBlockservice } from '../../../../../../../../services';

@Component({
  selector: 'app-defensive-id-blocks',
  templateUrl: './defensive-id-blocks.component.html',
  styleUrls: ['./defensive-id-blocks.component.scss']
})
export class DefensiveIdBlocksComponent {
  titles = [1, 2, 3, 4, 5, 6];
  listDefensive: DefensiveIdBlocks[] = [];
  activeLanguage = 'PTBR';
  @Output() activeTab2 = new EventEmitter<string>();

  public readonly excelButtonTemplate: Button.Templateable = {
    title: 'Paste content from excel',
    onClick: this.onExcelPaste.bind(this),
    iconClass: 'excel-icon',
    btnClass: Button.Klasses.FILL_ORANGE,
  };
  constructor(
    private _defensiveIdBlockservice: DefensiveIdBlockservice
  ){}


  async ngOnInit(): Promise<void>{

      this.removeEmptyItems();
      this.listDefensive = this._defensiveIdBlockservice.models;
    }

    removeEmptyItems() {
      this._defensiveIdBlockservice.toFinishLoading();  

      this._defensiveIdBlockservice.models = this._defensiveIdBlockservice.models.filter(boostItem => {
        return this.titles.some((_, index) => boostItem.positionNameDefensive[index] !== "");
      });     
      this._defensiveIdBlockservice.toSave();    
    }

    async onExcelPaste() {
      const text = await navigator.clipboard.readText();
      const lines = text.split(/\r?\n/).filter(line => line);    
      const processedData: string[][] = [];
    
      if (lines.length > 0) {
        lines.forEach(line => {
          // Divide cada linha em colunas e remove a primeira coluna
          const values = line.split("\t").map(value => value.trim()).slice(1);
    
          processedData.push(values);
        });
    
        // Verifica se o número de colunas em cada linha corresponde ao número de colunas em this.titles
        const isColumnCountValid = processedData.every(row => row.length === this.titles.length);
    
        if (!isColumnCountValid) {
          Alert.showError('Invalid number of columns');
          return;
        }
  
        this._defensiveIdBlockservice.models = [];
        this._defensiveIdBlockservice.toSave();
    
        for (let index = 0; index < processedData.length; index++) {
          this._defensiveIdBlockservice.createNewDefensiveIdBlocks(processedData[index]);
        }    

        Alert.ShowSuccess('Defensive imported successfully!');
        this.activeTab2.emit('defensive');
        this.ngOnInit();
      }
    }
    
 
    changeDefensive(rowIndex: number, colIndex: number, newValue: string){

      if (this.listDefensive[rowIndex]) {
        this.listDefensive[rowIndex].positionNameDefensive[colIndex] = newValue;
        this._defensiveIdBlockservice.svcToModify(this.listDefensive[rowIndex]);
      }
    }
    
 


}
