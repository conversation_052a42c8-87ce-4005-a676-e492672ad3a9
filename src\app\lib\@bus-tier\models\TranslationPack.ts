import { Data } from "src/lib/darkcloud/angular/dsadmin";
import { ITextContent } from "src/lib/darkcloud/angular/dsadmin/v9/data/hard";
import { Base } from "./Base";
import { Objective } from "./Objective";

export class TranslationPack
extends Base<Data.Hard.ITranslationPack, Data.Result.ITranslationPack>
implements Required<Data.Hard.ITranslationPack>
{

    private _objectives?: any[];
    public static generateId(language: string)
    {
        return language;
    }

    constructor(
        language: string,
        dataAccess: TranslationPack['TDataAccess']
    ){
        super(
            {
                hard: {
                    id: TranslationPack.generateId(language),
                    data: []
                },
            },
            dataAccess
        )
    }

    public get data()
    {
        return this.hard.data;
    }
    public set data(value: ITextContent[])
    {
        this.hard.data = value;
    }
    public get objectives(): any[] {
      return this._objectives;
    }
    public set objectives(value: any[]) {
      this._objectives = value;
    }

}
