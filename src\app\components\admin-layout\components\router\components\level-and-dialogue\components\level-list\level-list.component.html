<div class="main-content">
  <div class="container-fluid">
    <!-- START: Card Top -->
    <div class="list-header-row update">
      <div class="card card-header">
        <app-header-with-buttons 
          [cardTitle]="listName" [cardDescription]="listDescription">
        </app-header-with-buttons>
        <app-header-search 
          (inputKeyup)="search($event)" (searchOptions)="lstOnChangeFilterOptions($event)">
        </app-header-search>
        
        <div style="display: flex;">
          <select class="dropdown filter-dropdown limited"
                #currentSelectedArea
                [(ngModel)]="lstFilterValue['areaId']"
                (ngModelChange)="lstOnChangeFilter(); fillLevelsAreaByArea(currentSelectedArea.value);">

            <option *ngFor="let area of preloadedAreas"
                [value]="area.id"
                [selected]="area?.id == this.currentArea?.id"
                [style.background-color]="area.levelIds.length>0 ? 'white' : 'lightpink'">
                {{ area.hierarchyCode }}: {{ area.name }} 
            </option>
          </select>

          <h2 class="card shuffle-btn" [ngStyle]="{'background-color': this.canEnumerate ? 'green' : ''}" (click)="changeCanEnumerate()" 
            ><i class="pe-7s-shuffle pe-5x pe-va"></i>
          </h2>
        </div>
      </div>
    </div>
    <!-- END: Card Top -->

    <!-- START: Table Header -->
    <div class="card card-table">
      <table class="table table-list"
             id="level-list">
        <thead>
          <tr >
            <th>ID</th>
            <th>Links</th>
            <th>Order</th>
            <th>Icon</th>
            <th>Type of Encounter & Notes</th>
            <th>Speakers</th>
            <th>Battle Characters</th>
            <th>In-Map
              <div class="ball-circle"></div>
            </th>
            <th>Dialogues</th>
            <th>Actions</th>
          </tr>
        </thead>
    <!-- END: Table Header -->
    <ng-container *ngIf="(lstIds.length === 0 && this.lstFilterValue['areaId'] && !lstSearchTerm)">
      <button class="btn btn-success root-button" (click)="addBaseLevel()">
        <i class="pe-7s-plus"></i>
     </button>
    </ng-container>


    <!-- START: Table Body -->
      <ng-container *ngIf="lstIds.length > 0">
          <tbody>
            <tr *ngFor="let level of lstIds | levels;let i = index" id="{{ lstIds[i] }}">
                <!-- This is use to get the area using the any level from this area -->
                <!--Listing sort order-->
                <td class="td-sort">
                  <ng-container *ngIf="!lstSearchTerm; else disableSort">
                    <button class="btn btn-simple btn-success" title="Level up"
                      (click)="toMoveItem(level,-1,lstIds[i - 2],lstIds[i - 1],lstIds[i + 1],lstIds[i + 2])">
                      <i class="pe-7s-angle-up-circle"></i>
                    </button>
                    <p class="td-id">{{ lstIds[i] }}</p>
                    <button class="btn btn-simple btn-danger" title="Level below"
                      (click)="toMoveItem(level,+1,lstIds[i - 2],lstIds[i - 1],lstIds[i + 1],lstIds[i + 2])">
                      <i class="pe-7s-angle-down-circle"></i>
                    </button>
                  </ng-container>
                  <ng-template #disableSort>
                    <button disabled
                      title="Can't sort level when searching for a term"
                      class="btn btn-simple btn-default"><i class="pe-7s-angle-up-circle"></i>
                    </button>
                    <p class="td-id">{{ lstIds[i] }}</p>
                    <button disabled
                      title="Can't sort level when searching for a term"
                      class="btn btn-simple btn-default"><i class="pe-7s-angle-down-circle"></i>
                    </button>
                  </ng-template>
                </td>
                <div hidden>{{this.getCurrentArea()}}</div> <!--This is used to get the current area.--> 
              <!--Linked Levels-->
              <td [attr.title]="!(level | review).child_levelIds_sameArea? 'NO LINKS': (level | review).notTransition_linksToOtherArea? 
                'Not Transition: LINKS TO OTHER AREA': (level | review).isTransition_noLinksToOtherArea? 'Transition: DOES NOT LINK TO OTHER AREA': 
                !(level | review).linkListInOrder? 'Exit link: NOT EXPECTED': ''"
                [ngClass]="(level | review).notTransition_linksToOtherArea ||(level | review).isTransition_noLinksToOtherArea
                ||!(level | review).linkListInOrder ||level.linkedLevelIds.length === 0? 'link-list-problem': 'link-list'">
                <!--Add linked levels-->
                <button class="btn btn-simple btn-success" (click)="pushLevelLink(level)"><i class="pe-7s-plus"></i></button>
                <div class="row">
                  <div style="width:150px !important" class="col-6">
                    <ng-container *ngFor="let linkedLevelId of level.linkedLevelIds;let linkIndex = index">
                      <div>
                        <!-- Input for linked levels -->
                        <input [attr.title]="'Level List: '+ ([level.linkedLevelIds[linkIndex]] | location)?.toString()"
                          type="text" style="width:40px!important;" class="form-control form-short form-link"
                          [value]="(linkedLevelId | level | review)?.index" #levelLinkIndex
                          (change)="onChangeLink(level, ''+linkIndex, levelLinkIndex.value)" autofocus />
                        
                        <!-- START: Dropdown of the pathOrder -->
                        <select *ngIf="(linkedLevelId | deactivatePathOrder) && level?.pathOrder?.length > 0 
                          && linkedLevelId != undefined" 
                          style="position: relative; height:30px; left: -8px;"
                          (change)="ChangeLevelPathOrder(level, +linkIndex, $event)" >
                            <option *ngFor="let path of amountPaths"
                            [value]="path"
                            [selected]="level?.pathOrder[linkIndex] == path">{{"P "}}{{ path }}
                            </option>
                        </select>
                        
                        <div *ngIf="!(linkedLevelId | deactivatePathOrder) && level?.pathOrder?.length > 0" 
                          style="position: relative; height:30px; left: -8px;">{{""}}</div>
                        <!-- END:  Dropdown of the pathOrder -->
                    
                        <!-- Link icon-->
                        <ng-container *ngIf="linkIndex === 0">
                          <div style="min-width: 90px; position: relative; right: -80px;">
                            <button style="padding: 6px;" class="btn btn-simple btn-primary btn-linkedLevel"
                            (click)="accessLevel(linkedLevelId)"><i class="pe-7s-link"></i></button>
                          </div>
                        </ng-container>
  
                      </div>
  
                    </ng-container>
                  </div>
                </div>
                <button class="btn btn-simple btn-danger" title=" Remove Level" (click)="PopLevelLink(level)"><i class="pe-7s-less"></i></button>
              </td>
  
              <!--Branching label & Level ID-->
              <td class="branching-td td-auto" [style.min-width]="levelHeights[level?.id] * 30 + 160 + 'px'">
                <div class="branching">
                  <div class="branch-line child"
                       [ngStyle]="level| levelChildStyle: levelHeights[level?.id]: levelBranchIndex[level?.id]">
                  </div>
                  <div class="branch-line parent"
                       [ngStyle]="level| levelParentStyle: levelHeights[level?.id]: levelBranchIndex[level?.id]">
                  </div>
                  <div class="circle branch-circle"
                       (click)="redirectToDialoguesManager(level)"
                       [attr.title]="'Linked by ' +((level | review).parent_levelIds | location)?.toString()"
                       [ngStyle]="level| levelCircleStyle: levelHeights[level?.id]: levelBranchIndex[level?.id]"
                       [style.background-color]="level?.type | levelColor">
  
                    <div *ngIf="!this.canEnumerate" class="shortLevelId">{{ (level | review).index }}</div>
  
                    <div *ngIf="this.canEnumerate" class="shortLevelId">{{ this.getLevelIndex(level?.id) }}</div>
                  
                    <div *ngIf="(level | review).unlockedBy.length>0"
                         class="circle branch-error-circle unlocked-branch"
                         [style.border-color]="level?.type | levelColor"
                         title="Unlocked by {{((level | review).unlockedBy | location)?.toString()}}">
                      {{ ((level | review).unlockedBy | location).length }}
                    </div>
                    <!-- linked not in order warning -->
                    <div *ngIf="(level | review).parent_levelIds.length === 0"
                         class="circle branch-error-circle no-parent-links"
                         title="In-bound link: DOESN'T EXIST">X </div>
                    <!-- linked not in order warning -->
                    <div *ngIf="(level | review).child_levelIds_otherArea.length>0;else childtoSameArea"
                         class="circle branch-error-circle to-other-area-circle no-child-links"
                         title="Exit link: TO OTHER AREA">O </div>
                    <ng-template #childtoSameArea>
                      <div *ngIf="(level | review).child_levelIds.length === 0"
                           class="circle branch-error-circle no-child-links"
                           title="Exit link: DOESN'T EXIST">X </div>
                    </ng-template>
                  </div>
  
                  <div class="btn-group absolute-btn-group"
                       [style.margin-left]="levelHeights[level?.id] * 30 + 10 + 'px'">
                      <!-- Create new Linked level-->
                      <button *ngIf="levelHeights[level?.id] == 0 &&level?.linkedLevelIds.length>1"
                        class="btn btn-default btn-fill btn-sm"
                        (click)="toPromptAddLinkLevel(level)"
                        title="Add level link"><i class="pe-7s-angle-down-circle"></i>
                      </button>
                      <!-- Create a new Level-->
                      <button
                        *ngIf="levelHeights[level?.id] == 0 ||levelHeights[level?.id]>0"
                        class="btn btn-success btn-fill btn-sm"
                        (click)="toPromptAddLevelInBetween(level)"
                        title="Add a level"><i class="pe-7s-plus"></i>
                      </button>
                      <!-- Create branch button -->
                      <button
                        *ngIf="level.linkedLevelIds.length>0;else noLinkedLevel"
                        class="btn btn-primary btn-fill btn-sm"
                        (click)="toPromptAddLevel(level)"
                        title="Add level branch"><i class="pe-7s-share"></i>
                      </button>
                    <!-- Warning to nom linked level. Avoid creating a branch when the level does not have a linked level-->
                    <ng-template #noLinkedLevel>
                      <button 
                        disabled
                        class="btn btn-default btn-fill btn-sm"
                        title="Can't add branch, no linked level"><i class="pe-7s-share"></i>
                      </button>
                    </ng-template>
                  </div>
                  <div class="special-column"
                       [ngStyle]="levelHeights[level?.id] | specialColumnStyle">
                      <button 
                        title="First Attack (Enemy Advantage)"
                        *ngIf="+level?.type === LevelType.BOSS"
                        [ngClass]="level.firstAttack | firstAttackClass"
                        (click)="lstOnChange(level,'firstAttack',level?.firstAttack ? undefined : true)">
                      <img src="assets/img/icons/swords-icon.png" />
                      </button>
                      <button
                        *ngIf="+level?.type === LevelType.BOSS"
                        title="Certain Death Conditions"
                        [ngClass]="level?.conditionIds.length| conditionsClass"
                        (click)="toPromptAddCondition(level)"><img src="assets/img/icons/skull-icon.png" />
                      </button>
                      <button title="Story Expansion Pack" style="background-color: orange;"
                          [ngClass]="(level?.id | storyExpansionBtn) ? 
                          'btn btn-fill btn-warning ng-star-inserted' : 'btn btn-fill no-button btn-default ng-star-inserted'"	
                          (click)="addStoryExpansionContent(level.hard.id)">	
                        <i class="pe-7s-notebook" style="font-size: 25px;"></i>	
                      </button>
                  </div>
                </div>
              </td>
              <!--Level icon-->
              <td class="td-auto" style="cursor: pointer">
                <div class="special-column">  
                  <button class="btn btn-simple btn-icon"
                    [attr.title]="iconIds[level | iconIndex]? iconIds[level | iconIndex] : 'Level icon'" (click)="cycleIcon(level)">
                    <i [ngClass]="iconIds[level | iconIndex] + ' icon title'"></i>
                  </button>
                  <button  
                    *ngIf="''+(+level.type !== LevelType.TRANSITION &&lstFilterValue['areaId']) | area"
                    [title]="(level.sceneryId | scenery)?.name || 'Scenary'"
                    [ngClass]="!!level.sceneryId | sceneryButtonClass"
                    (click)="toPromptChangeScenery(level)"
                    [ngStyle]="{'background-color': (level.sceneryId| scenery| information)?.hex,'border-color': (level.sceneryId | scenery | information)?.hex}">
                    <i class="pe-7s-photo"></i>
                  </button>
                </div>
              </td>
              <!--Type of Encounter & Author Notes-->
              <td class="td-relative width-30pc">
                <select 
                  class="dropdown filter-dropdown auto"
                  [(ngModel)]="level.type"
                  (ngModelChange)="changePathorderDependentFromEncounterType(level)"
                  (ngModelChange)="lstOnChange(level, 'type', level?.type)">
                  <option value="{{ LevelType.MINION }}">Minion</option>
                  <option value="{{ LevelType.BOSS }}">Boss</option>
                  <option value="{{ LevelType.TRANSITION }}">Transition</option>
                  <option value="{{ LevelType.MINIGAME }}">Minigame</option>
                  <option value="{{ LevelType.DEFAULT }}">Secondary</option>
                </select>
                  <div *ngIf="level?.type == LevelType.MINIGAME">
                    <tr>
                      <td>
                        <select name="Minigame" id="Minigame" class="dropdown filter-dropdown auto" value="{{ level?.minigameId }}" #minigameId
                        (change)="lstOnChange(level, 'minigameId', minigameId.value)">
                          <option *ngIf="!level?.minigameId && minigames.length > 0" selected="true">Select a Minigame</option>
                          <option
                          *ngFor="let minigame of minigames; let i = index"
                          value="{{ minigame.id }}"
                          [selected]="level?.minigameId == minigame.id">{{minigame.name}}</option>
                          <option *ngIf="minigames.length == 0">No minigames available...</option>
                        </select>
                      </td>
                      <td>
                        <input
                        type="number"
                        name="Score Requirement"
                        id="Test"
                        placeholder="Score requirement..."
                        [value]="level?.scoreRequirement"
                        #scoreRequirement
                        (change)="lstOnChange(level, 'scoreRequirement', scoreRequirement.value)">
                      </td>
                    </tr>
                  </div>
                <div>
                  <textarea  
                    style="background-color:rgb(230,230,230) !important; " class="form-control borderless"
                    placeholder="Notes..."
                    type="text"
                    value="{{ (level | information)?.authorNotes || '' }}"
                    #notes
                    (change)="updateInformation(level, 'authorNotes', notes.value)">
                  </textarea>
                </div>
              </td>
              <td class="td-focus td-auto">
                <app-speakers-table [level]="level"></app-speakers-table>
              </td>
              <td class="td-auto">
                <app-battle-characters-table 
                  *ngIf="+level?.type === LevelType.BOSS ||+level?.type === LevelType.MINION"
                  [level]="level"
                  [editable]="true">
                </app-battle-characters-table>
              </td>
              <!--Level in-map title and description-->
              <td id="in-map" class="td-highlight td-relative">
                  <input 
                    class="form-control form-short"
                    type="text"
                    value="{{ (level | translation : lstLanguage : level?.id : 'name') }}"
                    #name
                    (change)="changeName(level, 'name', name.value)" />
                  <textarea
                    class="form-control form-long"
                    type="text"
                    value="{{ (level | translation : lstLanguage : level?.id : 'description') }}"
                    #description
                    (change)="changeDescription(level, 'description', description.value)">
                </textarea>
              </td>
              <!--Dialogues-->
              <td class="td-auto middle">
                <ng-container
                    *ngIf="level?.speakerIds.length>0 ||((level | information)?.enablesDialogue && (level?.dialogueIds | hasValidDialogue));else noSpeakers">
                  <div 
                    class="category"
                    title="Amount of words includes: title, description and the story, answer, choice and investigation boxes">
                    {{ (level | review).amountOfWords }}<br/>
                  </div>
                  <div 
                    class="row middle"
                    style="display: flex">
                    <div 
                      class="col"
                      style="width: 13px; padding: 0"
                      *ngFor="let dialogue of level?.dialogueIds | dialogues">
                      <span
                        title="{{(dialogue | dialogueTypeName) ==='Release'? (level | review).unlockedMissionDialogueBy.length>0 ?
                        'Release Unlocked by ' + ((level | review).unlockedMissionDialogueBy| location)?.toString(): 'Release Dialog not unlocked': ''}}"
                        class="notification-circle small"
                        [style.background-color]="dialogue | dialogueColor">
                      </span>
                    </div>
                  </div>
                  <button 
                    class="btn btn-fill"
                    [ngClass]="level | dialoguesButtonClass"
                    (click)="redirectToDialoguesManager(level)">
                    <i class="pe-7s-pen"></i>
                  </button>
                </ng-container>
                <ng-template #noSpeakers>
                  <button class="btn btn-fill no-button"
                  [ngClass]="level | dialoguesButtonClass"
                  (click)="toPromptEnableDialogue(level)">
                  <i class="pe-7s-pen"></i>
                </button>
              </ng-template>
              </td>
              <!--Delete Button-->
              <td class="td-actions td-auto">
                <button class="btn btn-danger btn-fill btn-remove"
                  (click)="lstPromptRemove(level)"><i class="pe-7s-close"></i>
                </button>
                <button class="btn btn-fill btn-gray translation-button"
                  (click)="getLevelOrtography(level)">
                  <div class="mat-translate"></div>
                </button>
              </td>
            </tr>
          </tbody>
        </ng-container>

      </table>

        
      <ng-container *ngIf="lstIds.length == 0">
        <div class="noLveles">
          <p style="font-weight: 800;">
            <i class="pe-7s-attention"></i>
            No Levels</p>
          <p *ngIf="preloadedAreas.length == 0">Check if I import the database correctly.</p>
        </div>
      </ng-container>

    </div>
  </div>
</div>
