import { ChangeDetectorRef, Component, Input } from '@angular/core';
import { Level, Area, Character } from 'src/app/lib/@bus-tier/models';
import { AreaService } from 'src/app/services/area.service';
import { CharacterService } from 'src/app/services/character.service';
import { LevelService } from 'src/app/services/level.service';
import { PopupService } from 'src/app/services/popup.service';
import { Popup } from 'src/lib/darkcloud';
import { LevelType, CharacterType, GameTypes } from 'src/lib/darkcloud/dialogue-system';
import { MicroloopService } from 'src/app/services/microloop.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-battle-characters-table',
  templateUrl: './battle-characters-table.component.html',
  styleUrls: ['./battle-characters-table.component.scss'],
})
export class BattleCharactersTableComponent 
{

  @Input() editable: boolean;
  @Input() level: Level;
  @Input() microloop: boolean
  @Input() isDialogue: boolean = false;
  isCollectible: boolean = false;
  public readonly LevelType = LevelType;

  constructor(
    private _areaService: AreaService,
    private _characterService: CharacterService,
    private _levelService: LevelService,
    private _popupService: PopupService,
    private _router: Router,
    private _microloopService: MicroloopService,
    private _change: ChangeDetectorRef,
  ) {}

  ngOnInit() {

  /*  for(let i = 0; i < this.level.battleCharacterIds.length; i++)
    {
      this.isCollectible = this._characterService.models.find((character) => character.id === this.level.battleCharacterIds[i]).isCollectible;     
    } 
    */   
   setTimeout(() => { 
       //Verifica se o primeiro personagem é Collectible;
      const character = this._characterService.models.find(char => char.id === this.level.battleCharacterIds[0]);
      if (character) {
        if (character.isCollectible) {
          this.isCollectible = true;
          return; // Para a execução assim que encontrar um Collectible;
        }
      }    
      this._change.detectChanges();
    }, 100);
  }

  public accessCharacter(characterId: string) {
    this._router.navigate(['characters'], { fragment: characterId });
  }

  public async toPromptAddBattleCharacterToLevel(level: Level) 
  {
    const selectedCharacterButton = await this._popupService.fire< Area, Character>(
      new Popup.Interface(
        {
          title: 'Select Area',
          actionsClass: 'column',
        },
        Popup.toButtonList(
          this._areaService.models,
          {
            parameters: ['hierarchyCode', 'name'],
            mapping: '[<hierarchyCode>]: <name>',
          },
          { undefinedTitle: 'No Area' }
        ),
        {
          inputButton: 
          {
            value: this._areaService.svcFindById(Area.getSubIdFrom(level.id)),
          },
          next: (selectedAreaButton) => {
            if (!selectedAreaButton) {
              return null;
            }
            let characters = +level.type === LevelType.BOSS ? 
            this._characterService.filterByType(CharacterType.BOSS, CharacterType.SUBBOSS): 
            this._characterService.filterByType(CharacterType.MINION, CharacterType.NPC);
            characters = characters.filter((character) => character.areaId === selectedAreaButton.value?.id);
            return new Popup.Interface<Character, Character>(
            {
              title: 'Select Character',
              actionsClass: 'column',
            },
            Popup.toButtonList(characters, 'name', 
            {
              classEnum: GameTypes.characterTypeName,
              classParameter: 'type',
            })
            );
          },
        }
      )
    );

    if (!selectedCharacterButton) return;    

    this.microloop ? this._microloopService.addBattleCharacter(level, selectedCharacterButton.value.id) :
    this._levelService.addBattleCharacter( level, selectedCharacterButton.value.id );
      setTimeout(() => {
      this._change.detectChanges();
    }, 100);
     this.ngOnInit();
      setTimeout(() => {
      this._change.detectChanges();
    }, 100); 
  }

  public async toPromptRemoveOneBattleCharacterFromLevel( level: Level, minionId: string ) 
  {
    this.microloop ? this._microloopService.RemoveOneBattleCharacter(level, minionId) : this._levelService.RemoveOneBattleCharacter(level, minionId);
  }
}
