import { Component, Input, SimpleChanges } from '@angular/core';
import { DCKnowledgeGuide, Knowledge } from 'src/app/lib/@bus-tier/models';
import { DCKnowledgeGuideService, KnowledgeService } from 'src/app/services';
import { Alert } from 'src/lib/darkcloud';

@Component({
  selector: 'app-dc-knowledge-guide',
  templateUrl: './dc-knowledge-guide.component.html',
  styleUrls: ['./dc-knowledge-guide.component.scss']
})
export class DcKnowledgeGuideComponent {

  @Input() copyKnowledgeBehavior: string[];
  knowledgeClasses: Knowledge[] = [];
  listDCKnowledgeGuide: DCKnowledgeGuide[] = [];
  isFirstChange = true;
  listHeader = [];

  constructor(
    private _knowledgeService: KnowledgeService,
    private _dCKnowledgeGuideService: DCKnowledgeGuideService
  ) { }

  ngOnChanges(changes: SimpleChanges) {

    if (changes['copyKnowledgeBehavior']) {
      if (this.isFirstChange) {
        // Ignorar a primeira alteração no ciclo de vida
        this.isFirstChange = false;
      } else if (this.copyKnowledgeBehavior && this.copyKnowledgeBehavior.length > 0) {
        this.onExcelPaste();
      }
    }
  }
  public async ngOnInit() {
    this._knowledgeService.toFinishLoading();

    setTimeout(() => {
      this.listHeader = [];
      this.knowledgeClasses = this._knowledgeService.models;
      this.sortKnowledgeAZ();
      this.listHeader.push('DC min', 'DC max', 'Description');
  
      this.knowledgeClasses.forEach(x => {
        this.listHeader.push(x.knowledge);
      });
  
      this._dCKnowledgeGuideService.toFinishLoading();
      this.listDCKnowledgeGuide = this._dCKnowledgeGuideService.models || [];
    }, 100);
  }

  isAscending = true;
  sortKnowledgeAZ() {
    this.knowledgeClasses.sort((a, b) => {
      const nameA = a.knowledge ? a.knowledge.toLowerCase() : '';
      const nameB = b.knowledge ? b.knowledge.toLowerCase() : '';
  
      return this.isAscending ? nameA.localeCompare(nameB) : nameB.localeCompare(nameA);
    });
  }
  

  async onExcelPaste() {
    this.listDCKnowledgeGuide = [];
    this._dCKnowledgeGuideService.models = [];
    this._dCKnowledgeGuideService.toSave();

    // 1ª Validação: Verifica se `this.copyKnowledgeBehavior` contém dados
    if (!this.copyKnowledgeBehavior || this.copyKnowledgeBehavior.length === 0) {
        Alert.showError('No data found in the copied Excel content.');
        return this.ngOnInit();
    }

    // Separa os headers (1ª linha) e os dados restantes
    const [headers, ...data] = this.copyKnowledgeBehavior;
    const columns = headers.split('\t').map(col => col.toLowerCase().trim());

    // 2ª Validação: Garante que existem pelo menos 3 colunas
    if (columns.length < this.listHeader.length) {
        Alert.showError("Excel copied missing columns.");
        return this.ngOnInit();
    }

    // **Transforma os valores do cabeçalho esperado em minúsculas**
    const normalizedListHeader = this.listHeader.map(header => header.toLowerCase().trim());

    // 3ª Validação: Verifica se os títulos copiados correspondem aos esperados (independentemente da ordem)
    if (!columns.every(col => normalizedListHeader.includes(col))) {
        Alert.showError(`A primeira linha "${columns.join(', ')}" não corresponde ao cabeçalho esperado.`);
        return this.ngOnInit();
    }

    // **Criar um mapeamento para a posição de cada coluna com base no cabeçalho copiado**
    const columnIndexMap: { [key: string]: number } = {};
    columns.forEach((col, index) => {
        columnIndexMap[col] = index;
    });

    // 4ª Validação: Processa as linhas copiadas (a partir da segunda linha)
    for (const line of data) {
        const values = line.split('\t');

        // Garantir que a linha tenha o mesmo número de colunas que o cabeçalho esperado
        if (values.length < normalizedListHeader.length) {
            console.warn("Linha com dados incompletos encontrada:", line);
            continue;
        }

        const newItem = await this._dCKnowledgeGuideService.createNewDCKnowledgeGuide();

        // Atribuir valores com base na posição correta dos campos copiados
        newItem.dcMin = columnIndexMap['dc min'] !== undefined ? +values[columnIndexMap['dc min']] : null;
        newItem.dcMax = columnIndexMap['dc max'] !== undefined ? +values[columnIndexMap['dc max']] : null;
        newItem.description = columnIndexMap['description'] !== undefined ? values[columnIndexMap['description']] : null;
        newItem.arcana = columnIndexMap['arcanismo'] !== undefined ? values[columnIndexMap['arcanismo']] : null;
        newItem.engineering = columnIndexMap['engenharia'] !== undefined ? values[columnIndexMap['engenharia']] : null;
        newItem.stealth = columnIndexMap['furtividade'] !== undefined ? values[columnIndexMap['furtividade']] : null;
        newItem.investigation = columnIndexMap['investigação'] !== undefined ? values[columnIndexMap['investigação']] : null;

        this._dCKnowledgeGuideService.svcToModify(newItem);
    }

    await this._dCKnowledgeGuideService.toSave();
    Alert.ShowSuccess('DC Knowledge Guide imported successfully!');
    this.ngOnInit();
}


  changeDCValue(index: number, field: string, value: string) {
    // Altera o campo do item baseado no index
    if (field === 'dcMin') {
      this.listDCKnowledgeGuide[index].dcMin = value === '' ? null : +value;
    } else if (field === 'dcMax') {
      this.listDCKnowledgeGuide[index].dcMax = value === '' ? null : +value;
    }
    else if (field === 'description') {
      this.listDCKnowledgeGuide[index].description = value;
    }
    else if (field === 'arcana') {
      this.listDCKnowledgeGuide[index].arcana = value;
    }
    else if (field === 'engineering') {
      this.listDCKnowledgeGuide[index].engineering = value;
    }
    else if (field === 'stealth') {
      this.listDCKnowledgeGuide[index].stealth = value;
    }
    else if (field === 'investigation') {
      this.listDCKnowledgeGuide[index].investigation = value;
    }

    this._dCKnowledgeGuideService.svcToModify(this.listDCKnowledgeGuide[index]);
    this._dCKnowledgeGuideService.toSave();
  }

  removeLineDC(index: number) {
    const itemToRemove = this.listDCKnowledgeGuide[index];

    if (itemToRemove && itemToRemove.id) {
      this._dCKnowledgeGuideService.svcToRemove(itemToRemove.id);
    }

    this.listDCKnowledgeGuide.splice(index, 1); // Remove o item da lista local
    this.ngOnInit();
  }
}
