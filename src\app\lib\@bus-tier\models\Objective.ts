import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { Base } from './Base';

export class Objective
  extends Base<Data.Hard.IObjective, Data.Result.IObjective>
  implements Required<Data.Hard.IObjective>
{
  public static generateId(missionId: string, index: number): string {
    return missionId + '.' + IdPrefixes.OBJECTIVE + index;
  }
  constructor(
    index: number,
    missionId: string,
    dataAccess: Objective['TDataAccess']
  ) {
    super(
      {
        hard: {
          id: Objective.generateId(missionId, index),
        },
      },
      dataAccess
    );
  }
  protected getInternalFetch() {
    return {};
  }
  public get description(): string {
    return this.hard.description;
  }
  public set description(value: string) {
    this.hard.description = value;
  }
  public get xp(): number {
    return this.hard.xp;
  }
  public set xp(value: number) {
    this.hard.xp = value;
  }
  public get hidded(): boolean {
    return this.hard.hidded;
  }
  public set hidded(value: boolean) {
    this.hard.hidded = value;
  }
  
  public get isReviewedDescription(): boolean {
    return this.hard.isReviewedDescription;
  }
  public set isReviewedDescription(value: boolean) {
    this.hard.isReviewedDescription = value;
  }
  public get revisionCounterDescriptionAI(): number {
    return this.hard.revisionCounterDescriptionAI;
  }
  public set revisionCounterDescriptionAI(value: number) {
    this.hard.revisionCounterDescriptionAI = value;
  }
}
