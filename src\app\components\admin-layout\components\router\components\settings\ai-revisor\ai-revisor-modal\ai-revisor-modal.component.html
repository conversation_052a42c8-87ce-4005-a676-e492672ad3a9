<div class="background-div handleOut" aria-hidden="true">
    <div class="modal-backdrop"></div>
    <div id="modal-close" @popup class="popup-report" (mouseleave)="onMouseLeave()"
        style="background-color: black;">
        <div class="modal-header">
            <div style="display: flex; justify-content: space-between;">
                <p style="color:azure !important; text-align: center;" class="modal-title">Informações</p>
                <button type="button" class="close handleOut" (click)="closeAreaStatsPopup()"
                    data-dismiss="background-div" aria-label="Fechar">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        </div>
        <div class="contextInfo">
            <p class="modal-title title p-text">
                1 - <span class="span-text">Aprovado: </span> Faz a modificação e sai da lista de revisão.
            </p>
            <p class="modal-title title p-text">
                2 - <span class="span-text">Reprovado: </span> Não faz a modificação e sai da lista de revisão.
            </p>
            <p class="modal-title title p-text">
                3 - <span class="span-text">Sem sugestões: </span> Após X verificações, sai da lista de revisão.
            </p>
            <p class="modal-title title p-text">
                4 - <span class="span-text">Apresentado, mas sem decisão do utilizador: </span> Continua com o contador de verificações zerado.
            </p>
        </div>

    </div>
</div>