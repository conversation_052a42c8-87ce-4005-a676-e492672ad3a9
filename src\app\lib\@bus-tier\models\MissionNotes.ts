import { Data } from "src/lib/darkcloud/angular/dsadmin";
import { IdPrefixes } from "src/lib/darkcloud/dialogue-system";
import { Base } from "./Base";

export class MissionNotes
extends Base<Data.Hard.IMissionNotes, Data.Result.IMissionNotes>
implements Required<Data.Hard.IMissionNotes>
{
    public static generateId(index: number)
    {
        return IdPrefixes.MISSIONNOTES + index;
    }

    constructor(
        index: number,
        dataAccess: MissionNotes['TDataAccess']
    )
    {
        super({hard: {id: MissionNotes.generateId(index)}}, dataAccess);
    }

    public get name()
    {
        return this.hard.name;
    }

    public set name(value: string)
    {
        this.hard.name = value;
    }

    public get description()
    {
        return this.hard.description;
    }

    public set description(value: string)
    {
        this.hard.description = value;
    }

    public get notes()
    {
        return this.hard.notes;
    }

    public set notes(value: string)
    {
        this.hard.notes = value;
    }
    
    public get createdDate()
    {
        return this.hard.createdDate;
    }

    public set createdDate(value: string)
    {
        this.hard.createdDate = value;
    }
}
