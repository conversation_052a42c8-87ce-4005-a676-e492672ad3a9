import { Component, ElementRef, ViewChild } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { SoundService, UserSettingsService } from 'src/app/services';
import { Sound } from 'src/app/lib/@bus-tier/models';
import { Alert } from 'src/lib/darkcloud';
import { Router } from '@angular/router';

@Component({
  selector: 'app-uploader-list',
  templateUrl: './audio-uploader.component.html',
  styleUrls: ['./audio-uploader.component.scss'],
})
export class AudioUploaderComponent
{
  @ViewChild('fileInput', { static: false }) fileInput: ElementRef;
  directory: string = "Directory: DSAdmin/resources/app/dist/dsadmin/assets/voices";
  
  constructor(
    private http: HttpClient,
    private _router: Router,
    private _soundService: SoundService,
    readonly userSettingsService: UserSettingsService){}

  redirectToEmotionList() 
  {
    this._router.navigate(['audios']);
  }

  upload(event:any)
  {
    const files: FileList = event.target.files;
    for(let i = 0; i < files.length; i++)
    { 
      this.sendFile(files[i]);
    }
  }

  sendFile(file)
  {
    const formData = new FormData()
    formData.append('file',file)

      this.http.post('http://localhost:3000/audioUploader', formData).subscribe({
      next: (v) => 
      {
        console.log("This is the next from the audio uploader component: ", v);
      },
      error: (e) => 
      {
        Alert.showError(e, 'This is a server ERROR. Please take a screenshot of this error and show it to the developer!');
        console.error("Errorrrr", e);
        return;
      },
      complete: () => 
      {
        Alert.ShowSuccess("Audio was successfully added to the database!");
        console.info('Audio was saved in the database.');
        this.fileInput.nativeElement.value = '';
      }
    });
    
      this._soundService.srvAdd(new Sound(file.name, this.userSettingsService));
      this._soundService.toSave();
  }

}
