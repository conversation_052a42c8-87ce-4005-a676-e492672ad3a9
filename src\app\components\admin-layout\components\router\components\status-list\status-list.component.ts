import { Component } from '@angular/core';
import { Status} from 'src/app/lib/@bus-tier/models';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { TranslationService } from 'src/app/services/translation.service';
import { LanguageService } from 'src/app/services/language.service';
import { SilicatosService, StatusService } from 'src/app/services';
import { Button } from 'src/app/lib/@pres-tier/data';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';
import { language } from 'src/lib/darkcloud/dialogue-system/game-types';
import { TranslationCheckService } from 'src/app/services/translation-check.service';
import { Alert } from 'src/lib/darkcloud';
import { ActivatedRoute } from '@angular/router';


@Component({
  selector: 'app-status-list',
  templateUrl: './status-list.component.html',
  styleUrls: ['./status-list.component.scss']
})

export class StatusListComponent extends TranslatableListComponent<Status> 
{ 
  public language: language = 'PT-BR';
  public statusClasses: Status[] = [];
 
  constructor(
    _activatedRoute: ActivatedRoute,
    _userSettingsService: UserSettingsService,
    private _statusService: StatusService,
    protected override _translationService: TranslationService,
    protected override _languageService: LanguageService,
    private _translationCheckService: TranslationCheckService,
    private _silicatosService : SilicatosService

  ) 
  {
    super(_statusService, _activatedRoute, _userSettingsService, 'name', _translationService, _languageService);
  }
 
  public readonly statusTemplate: Button.Templateable = 
  {
    title: 'Add a new instance to the list',
    onClick: this.addStatus.bind(this),
    iconClass: 'pe-7s-plus',
    btnClass: Button.Klasses.FILL_GREEN,
  };
  
  override async lstInit ()
  {
    this.statusClasses = this._statusService.models;
    this._statusService.svcReviewAll();
    this.lstIds = this.statusClasses.map(x => x.id);
  }

  async addStatus()
  {
    let statusClasses;

    try 
    {
      statusClasses = await this._statusService.svcPromptCreateNew();
    } 
    catch (e) 
    {
      Alert.showError("This SKILL already exists!");
      return
    }
    if(!statusClasses) return;

    await this._statusService.srvAdd(statusClasses);
    this.addingNullValueIntoSilicatoStatusList(statusClasses)
    if(this.statusClasses.includes(statusClasses)) return;    
    else this.statusClasses.push(statusClasses);
  }

  changeSkill( skill: Status, termSkill: string, valueSkill: string) {

    if(termSkill == 'skill') {
      skill.skill = valueSkill;
      skill.isReviewedSkill = false;
      skill.revisionCounterSkillAI = 0;
    } 
    else if (termSkill == 'acronym') {
      skill.acronym = valueSkill;
    } 
    else {
      skill.description = valueSkill;
      skill.isReviewedDescription = false;
      skill.revisionCounterDescriptionAI = 0;
    }

    this._statusService.svcToModify(skill);
    this.ngOnInit();
  }

  /*
  Here we are adding a null value inside the silicato status list when a new status is created. This is to keep 
  the list order on display.
  */
async addingNullValueIntoSilicatoStatusList(statusClasses)
{
  let statusIndex = this._statusService.models.indexOf(statusClasses);
  this._silicatosService.models.forEach(silicato=> silicato.status.splice(statusIndex, 0, null));
  await this._silicatosService.toSave();
}
async removeElement(status : Status)
{
  const confirm = await Alert.showRemoveAlert(status.skill + ' ' + 'skill');  
  if (!confirm) return;
  
  this.removeElementFromSilicatoStatusList(status);
  this._statusService.models = await this._statusService.models.filter(s => s.id !== status.id);
  await this._statusService.toSave();
  this.statusClasses = this.statusClasses.filter(s => s !== status);
}

/*Here we are removing the element based on the alphabetical order of the skill. This will be usefull 
when saving/deleting from the silicato status array
*/
  async removeElementFromSilicatoStatusList(status)
  {
    let statusIndex = this._statusService.models.indexOf(status);

    this._silicatosService.models.forEach(silicato=> silicato.status.splice(statusIndex, 1));
    await this._silicatosService.toSave();
  }

  public getStatusOrtography(status: Status)
  {
    this._translationService.getStatusOrtography(status, true);
  }

  //The two below sort methods are here because it dosent work with the old code but the others parameters did worked.
  sortBySkillOrder = -1;
  sortBySkill() 
  {
    this.sortBySkillOrder *= -1;
    this._statusService.models.sort((a, b) => 
    {
      return this.sortBySkillOrder *  a.skill.localeCompare(b.skill);
    });
    this.lstFetchLists();
  }
 
  sortByAcronymOrder = -1;
  sortByAcronym() 
  {
    this.sortByAcronymOrder *= -1;
    this._statusService.models.sort((a, b) => 
    {
      return this.sortByAcronymOrder *  a.acronym.localeCompare(b.acronym);
    });
    this.lstFetchLists();
  }
}
