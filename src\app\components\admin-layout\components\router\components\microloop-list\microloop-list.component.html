<div class="main-content">
    <div class="container-fluid">
        <div class="card list-header-row">
            <app-header-with-buttons
            [cardTitle]="listName"
            [cardDescription]="cardDescription"
            [rightButtonTemplates]="[addButtonTemplate]">
            </app-header-with-buttons>
        </div>
    </div>
    <div class="card" style="margin-left: 15px; margin-right: 15px;">
        <table class="table table-list">
            <thead>
                <tr>
                    <th class="th-clickable" (click)="sortListByParameter('id')">ID</th>
                    <th class="th-clickable" (click)="sortListByParameter('name')">Name & Description</th>
                    <th class="th-clickable" (click)="sortListByParameter('notes')">Notes</th>
                    <th></th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <tr style="height: 100px !important;"
                *ngFor="let loop of lstIds | microloopContainers">
                    <td>{{ loop.id }}</td>
                    <td class="td-notes">
                      <input style="border-style:solid;" class="form-control form-short"
                              type="text"
                              value="{{ (loop | translation : lstLanguage : loop.id : 'name') }}"
                              #name
                              (change)="lstOnChange(loop, 'name', name.value)"
                               />
                      <textarea class="form-control"
                                type="text"
                                value="{{ (loop | translation : lstLanguage : loop.id : 'description') }}"
                                #description
                                (change)="lstOnChange(loop, 'description', description.value)"
                                >
                        </textarea>
                    </td>
                    <td class="td-notes">
                      <textarea class="form-control borderless"
                                value="{{ (loop | translation : lstLanguage : loop.id : 'notes') }}"

                                #notes
                                (change)="lstOnChange(loop, 'notes', notes.value)"></textarea>
                    </td>
                    <td class="td-small">
                      <button class="btn btn-primary btn-fill"
                      (click)="routeToMicroloopListComponent(loop)"
                      title="Loop list">
                          <i class="pe-7s-more"></i>
                      </button>
                    </td>
                    <td class="td-actions td-small">
                        <button
                        class="btn btn-danger btn-fill btn-remove"
                        (click)="lstPromptRemove(loop)"
                        >
                            <i class="pe-7s-close"></i>
                        </button>
                        <button class="btn btn-gray btn-fill translation-button"
                        title="Transaltion"
                        (click)="downloadMicroloopContainerOrtography(loop)">
                            <div class="mat-translate"></div>
                        </button>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
