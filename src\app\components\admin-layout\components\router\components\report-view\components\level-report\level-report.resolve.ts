import { Injectable } from '@angular/core';
import { MarkerService } from 'src/app/services';
import { EasyMVC } from 'src/lib/darkcloud/angular';
import { MarkerType } from 'src/lib/darkcloud/dialogue-system';
import { Preloadable } from './level-report.component';

@Injectable({
  providedIn: 'root',
})
export class LevelReportResolve extends EasyMVC.Resolve<Preloadable> 
{
  constructor(private _markerService: MarkerService) 
  {
    super(_markerService);
  }
  async rsvOnResolve(): Promise<Preloadable> 
  {
    return {
      unlockMarkers: this._markerService
        .filterByType(MarkerType.UNLOCK_LEVEL)
        .filter((marker) => marker.levelId),
      releaseMarkers: this._markerService
        .filterByType(MarkerType.RELEASE_DIALOGUE)
        .filter((marker) => marker.levelId),
    };
  }
}
