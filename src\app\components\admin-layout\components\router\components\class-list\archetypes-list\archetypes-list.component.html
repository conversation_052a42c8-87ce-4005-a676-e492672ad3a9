<div class="main-content">
    <div class="container-fluid">
      <!--Header-->
      <div class="card list-header-row">
        <app-header-with-buttons [cardTitle]="'Archetype List'"
                                 [cardDescription]="cardDescription"
                                 [rightButtonTemplates]="[addButtonTemplate]"
                                 [isBackButtonEnabled]="true"
                                 (cardBackButtonClick)="redirectToItemClasses()">
        </app-header-with-buttons>
        <app-header-search (inputKeyup)="lstOnChangeFilter($event)"></app-header-search>
      </div>
      <!--List-->
      <div class="card">
        <table class="table table-list">
          <thead style="top: 115px">
            <tr>
              <th>Index</th>
              <th class="th-clickable" (click)="sortListByParameter('id')">
                ID
              </th>
              <th>Label</th>
              <th class="th-clickable" (click)="sortListByParameter('name')">
                  Name & Description
              </th>
              <th class="th-clickable" (click)="sortListByParameter('notes')">Notes</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let archetype of lstIds | archetypeList; let i = index;">
              <tr id="{{ archetype.id }}">
                <td class="td-sort">{{ i + 1 }}</td>
                <td class="td-id archId">{{ archetype.id }}</td>
                <td #colorLabel class="archLabel" [ngStyle]="{ background: (archetype | information)?.hex }" (click)="color.click()">
                  <input #color type="color" value="{{ (archetype | information)?.hex || '' }}" style="visibility: hidden" (change)="updateColor(archetype, color.value, colorLabel)" />
                </td>
                <td>
                <input
                    type="text" class="form-control form-short" value = "{{ archetype.name }}" #name (change)="lstOnChange(archetype, 'name', name.value)" placeholder="Name...">
                <textarea
                    name="description" id="description" style="width: 100%;" value="{{ archetype.description }}" #description (change)="lstOnChange(archetype, 'description', description.value)">
                 </textarea>
                </td>
                <td class="td-notes">
                  <textarea class="form-control borderless"
                            value="{{ (archetype | translation : lstLanguage : archetype.id : 'notes') }}" #notes (change)="lstOnChange(archetype, 'notes', notes.value)">
                  </textarea>
                </td>
                <td class="td-actions archAction">
                  <button class="btn btn-danger btn-fill btn-remove" (click)="lstPromptRemove(archetype)">
                    <i class="pe-7s-close"></i>
                  </button>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
    </div>
  </div>
