<div style="display: flex; flex-direction: row; overflow-x: auto; width: 100%; justify-content: center;">
    <table class="table table-list borderList">
        <thead>
            <ng-container *ngIf="listDefensive.length > 0">
                <tr>
                    <th rowspan="3">Defensive</th>
                </tr>
            </ng-container>
            <tr>
                <th [attr.colspan]="titles.length">
                    <h3>Repetitions</h3>
                </th>
                <app-button-group *ngIf="activeLanguage === 'PTBR' || activeLanguage === 'PT-BR'" class="addButton"
                    [buttonTemplates]="[excelButtonTemplate]">
                </app-button-group>
            </tr>
            <ng-container *ngIf="listDefensive.length > 0">
                <tr>
                    <th style="background-color: #AEAAAA !important;" *ngFor="let title of titles"> {{title}}</th>
                </tr>
            </ng-container>
        </thead>
        <ng-container *ngIf="listDefensive.length > 0">
            <tbody>
                <tr *ngFor="let item of listDefensive; let i = index">
                    <td style="background-color: #ddd;">{{ i + 1 }}</td>
                    <ng-container *ngFor="let rep of titles; let r = index">
                        <td class="td-id">
                            <input class="background-input-table-color form-control form-short" placeholder=" "
                                type="text" #boost [ngClass]="{'empty-input': !boost.value}"
                                [value]="item.positionNameDefensive?.[r]" (change)="changeDefensive(i, r, boost.value)" />
                        </td>
                    </ng-container>
                </tr>
            </tbody>
        </ng-container>
        <ng-container *ngIf="listDefensive.length === 0">
            <div class="card" style="text-align: center; padding: 20px;">
                <h3>The list has not yet been imported.</h3>
            </div>
        </ng-container>
    </table>
</div>