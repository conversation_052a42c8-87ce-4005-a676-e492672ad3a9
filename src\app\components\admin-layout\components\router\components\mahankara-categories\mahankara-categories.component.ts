import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { MahankaraCategories } from 'src/app/lib/@bus-tier/models';
import { MahankaraCategoriesService } from 'src/app/services';
import { Alert } from 'src/lib/darkcloud';

@Component({
  selector: 'app-mahankara-categories',
  templateUrl: './mahankara-categories.component.html',
  styleUrls: ['./mahankara-categories.component.scss'],
})
export class MahankaraCategoriesComponent implements OnChanges, OnInit {
  @Output() descriptionOutput = new EventEmitter<string>();
  @Input() copyExcelMahankaraCategories: string[];
  titles = ['Index Category', 'Speech Category', 'Description'];
  listCategories: MahankaraCategories[] = [];
  // Flag para controle de inicialização do copyExcelRepetition
  isFirstChange = true;

  constructor(
    private _mahankaraCategoriesService: MahankaraCategoriesService
  ) {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes['copyExcelMahankaraCategories']) {
      if (this.isFirstChange) {
        // Ignorar a primeira alteração no ciclo de vida
        this.isFirstChange = false;
      } else if (
        this.copyExcelMahankaraCategories &&
        this.copyExcelMahankaraCategories.length > 0
      ) {
        this.onExcelPaste();
      }
    }
  }

  async ngOnInit(): Promise<void> {
    this._mahankaraCategoriesService.toFinishLoading();
    this.listCategories = this._mahankaraCategoriesService.models;
    this.descriptionOutput.emit(
      `Showing ${this.listCategories.length} results`
    ); 
  }
  async onExcelPaste() {
    this._mahankaraCategoriesService.models = [];
    this._mahankaraCategoriesService.toSave();

    // Verifica se `this.copyExcelMahankaraBehavior` contém dados
    if (
      !this.copyExcelMahankaraCategories ||
      this.copyExcelMahankaraCategories.length === 0
    ) {
      Alert.showError('No data found in the copied Excel content.');
      return this.ngOnInit();
    }

    const expectedColumns = this.titles.length;

    //Verificar se todas as linhas possuem o número correto de colunas
    const invalidColumnRows = this.copyExcelMahankaraCategories.filter(
      (row) => {
        const cells = row.split('\t'); // O '\t' dividide em células - O delimitador \t é para tabulação (comum em colagens do Excel)
        return cells.length !== expectedColumns;
      }
    );

    if (invalidColumnRows.length > 0) {
      Alert.showError(
        `The number of columns does not match the expected count (${expectedColumns}). Please check the data.`
      );
      return this.ngOnInit();
    }

    this.copyExcelMahankaraCategories.forEach((row, index) => {
      const cells = row.split('\t'); // Divide a linha em células
      this._mahankaraCategoriesService.createNewMahankaraCategories(cells);
    });

    this.copyExcelMahankaraCategories = [];
    Alert.ShowSuccess('Categories imported successfully!');
    this.ngOnInit();
  }

  changeMahankaraValue(rowIndex: number, name: string, newValue: string) {
    if (name === 'indexCategory') {
      this.listCategories[rowIndex].indexCategory = newValue;
    } else if (name === 'speechCategory') {
      this.listCategories[rowIndex].speechCategory = newValue;
    } else if (name === 'description') {
      this.listCategories[rowIndex].description = newValue;
    }
    this._mahankaraCategoriesService.svcToModify(this.listCategories[rowIndex]);
  }
}
