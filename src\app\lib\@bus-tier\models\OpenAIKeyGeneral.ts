import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';

export class OpenAIKeyGeneral  extends Base<Data.Hard.IOpenAIKeyGeneral, Data.Result.IOpenAIKeyGeneral> implements Required<Data.Hard.IOpenAIKeyGeneral>
{
  public static generateId(index: number): string {
    return IdPrefixes.OPENAIKEYGENERAL + index;
  }

  constructor( index: number, dataAccess: OpenAIKeyGeneral['TDataAccess']) 
  {
    super(
    {
      hard: 
      {
        id: OpenAIKeyGeneral.generateId(index),     
      },
    },
    dataAccess
    );
  }
  protected getInternalFetch() 
  {
    return {};
  }

  public get name(): string 
  {
    return this.hard.name;
  }
  public set name(value: string) 
  {
    this.hard.name = value;
  }
  public get apiKey(): string 
  {
    return this.hard.apiKey;
  }
  public set apiKey(value: string) 
  {
    this.hard.apiKey = value;
  }
}
