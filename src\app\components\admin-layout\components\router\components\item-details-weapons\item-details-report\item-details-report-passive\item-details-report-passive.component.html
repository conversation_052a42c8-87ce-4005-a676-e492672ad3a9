<div>
  <h3>Passives</h3>

  <div class="card">
    <div style="padding: 15px; display: flex; justify-content: space-between;">

      <ng-container *ngIf="isWeaponsCommonsSpecial">

        <div style="width: 45%;">
          <div style="display: flex; padding-left: 50px; font-weight: 600;">
            <span>BONUS</span>
            <span style="margin-left: 150px;">CONDITION</span>
          </div>

          <!-- Dropdown para seleção -->
          <select class="dropdown filter-dropdown limited" (change)="selectItem($event)"
            [disabled]="passiveDB && passiveDB[0]?.codeType === 'IC3' && passiveDB[0]?.codeType !== 'IC20'"
            style="display: inline-block; margin: 10px; margin-bottom: 15px; width: 380px !important;">
            <option value="ALL">Select</option>
            <option *ngFor="let passive of passiveList.descriptions; let i = index" [value]="i">
              {{i + 1}} - {{ passive?.mergeDescription }}
            </option>
          </select>
        </div>


        <!-- Tabela de itens selecionados -->

        <div style="display: grid; justify-items: center; margin-right: 15px; width: 45%;">
          <span style="font-weight: 600;">{{currentCharacter?.codeType === 'IC3' ? 'Common weapon' : 'Special Weapon'}}
          </span>

          <ng-container *ngIf="passiveDB?.length > 0">
            <table id="customers" style="width: 100%;" *ngIf="passiveDB[0].descriptions.length > 0">
              <tr *ngFor="let item of passiveDB[0].descriptions; let i = index" (click)="returnToPassiveList(item)" style="cursor: pointer;">
                <td>
                  <span style="font-weight: 600; width: 80px;">
                    <ng-container *ngIf="passiveDB[0].descriptions.length === 1">Bonus 1: </ng-container>
                    <ng-container *ngIf="passiveDB[0].descriptions.length > 1">Bonus {{i + 1}}: </ng-container>
                  </span>
                  {{ item.mergeDescription }}
                </td>
              </tr>
            </table>
          </ng-container>
        </div>
      </ng-container>


      <ng-container *ngIf="!isWeaponsCommonsSpecial">
        <div style="margin-left: 27%;">
          <p>
            Passive Skill only for Common Weapons and Special Weapons
          </p>
        </div>
      </ng-container>
    </div>
  </div>
</div>