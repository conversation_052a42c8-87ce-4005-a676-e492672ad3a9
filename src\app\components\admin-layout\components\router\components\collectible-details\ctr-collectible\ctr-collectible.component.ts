import { ChangeDetectorRef, Component, Input } from '@angular/core';
import { Character, CTRCollectible, CtrEvMax, PrimalModifier } from 'src/app/lib/@bus-tier/models';
import { CTRCollectibleService, CTREVMAXService, PrimalModifierService } from 'src/app/services';

@Component({
  selector: 'app-ctr-collectible',
  templateUrl: './ctr-collectible.component.html',
  styleUrls: ['./ctr-collectible.component.scss']
})
export class CtrCollectibleComponent {
  @Input() character: Character;
  listCTR = [];
  primalModifier: PrimalModifier;
  ctr_base: number;
  luck: number;
  ctr_max: number;
  listCTRCollectibes: CTRCollectible;


  constructor(
    private _cTREVMAXService: CTREVMAXService,
    private _primalModifierService: PrimalModifierService,
    private _cTRCollectibleService: CTRCollectibleService,
    private _change: ChangeDetectorRef,
  ) { }

  async ngOnInit(): Promise<void> {
    this._cTREVMAXService.toFinishLoading();
    this._primalModifierService.toFinishLoading();

    this.listCTRCollectibes = this._cTRCollectibleService.createNewCTRCollectible(this.character);        
   // this._change.detectChanges();
    this.getDataValuesCTR();
  }

  getDataValuesCTR() {
    this.listCTRCollectibes.valuesCTR = [];
    this.ctr_base = 0;
    this.luck = 0;
    this.ctr_max = 0;

    setTimeout(() => {
      this.primalModifier = this._primalModifierService.models.find((i) => i.character === this.character.id);
      this.ctr_base = this.primalModifier.primalModifier.find((i) => i.fieldName === 'Chance Critica').fieldValue;
      this.luck = this.primalModifier.primalModifier.find((i) => i.fieldName === 'Sorte').fieldValue;
      
      if (this._cTREVMAXService.models.length > 0) {  
        this.ctr_max = this._cTREVMAXService.models[0].ctr_max;
      }
    
      for (let newLuck = 1; newLuck <= 12; newLuck++) {
        if (this.ctr_base && this.luck) { 
          this.listCTRCollectibes.valuesCTR.push({
            critical_change: this.calculateCriticalChance(newLuck).toFixed(2), 
            lukNew: newLuck
          });
        } else {
          this.listCTRCollectibes.valuesCTR.push({
            critical_change: '', 
            lukNew: newLuck
          });
        }         
      }
      this._cTRCollectibleService.svcToModify(this.listCTRCollectibes);
    },200);

  }
  
  private sign(value: number): number {
    return value > 0 ? 1 : value < 0 ? -1 : 0;
  }

 calculateCriticalChance(newLuck: number): number {
    const controlPositive = (this.sign(newLuck - this.luck) + 1) / 2;
    const controlNegative = 1 - controlPositive;

    const term1 = controlNegative * this.ctr_base * (newLuck / this.luck);
    const term2 =
      controlPositive *
      (((this.ctr_max - this.ctr_base) / (11.999999 - this.luck)) *
        (newLuck - this.luck) +
        this.ctr_base);

    return term1 + term2;
  }

reset(character) {
    this.character = character;
    this.ngOnInit();
  }

}
