import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/SortableListComponent';
import { Component } from '@angular/core';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { ReviewService } from 'src/app/services/review.service';
import { LanguageService } from 'src/app/services/language.service';
import { TranslationService } from 'src/app/services/translation.service';
import { AreaService, CharacterService, ClassService, LevelService, MinionStatsService, PrimalModifierService, StatusInfoService } from 'src/app/services';
import { BattleUpgradeService } from 'src/app/services/battle-upgrade.service';
import { MinionStats } from 'src/app/lib/@bus-tier/models/MinionStats';
import { Alert } from 'src/lib/darkcloud';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-minion-stats',
  templateUrl: './minion-stats.component.html',
  styleUrls: ['./minion-stats.component.scss'],
})

export class MinionStatsComponent extends SortableListComponent<MinionStats> 
{ 
  public rarities = ['Inferior', 'Elementar', 'Common', 'Rare', 'Epic', 'Legendary'];
  public types = ['Boss', 'Minions Collectable', 'Minions(All)', 'Subbosses','Collectible (Boss + Minion Collectible)'];
  areas = [];
  validaCharacters = [];
  areaFilter = "ALL";
  classFilter = "ALL";
  hcFilter = "ALL";
  charactersList = [];
  allCharacterList = [];
  classes = [];
  HCs = [];
  sortCollectibleRecordOrder = -1;
  description:string = '';
 
  constructor(
    _activatedRoute: ActivatedRoute,
    private _minionStatsService: MinionStatsService,
    _userSettingsService: UserSettingsService,
    private _areaService: AreaService,
    private _levelService: LevelService,
    private _router: Router,
    private _reviewService: ReviewService,
    protected _languageService: LanguageService,
    protected _translationService: TranslationService,
    protected _statusInfoService : StatusInfoService,
    protected _primalService : PrimalModifierService,
    protected _battleService : BattleUpgradeService,
    private _classService: ClassService,
    private _characterService: CharacterService
  ) 
  {
    super(_minionStatsService, _activatedRoute, _userSettingsService, 'name');
  }

  public override async ngOnInit(): Promise<void>    
  {
    await this._characterService.toFinishLoading();
    await this._minionStatsService.toFinishLoading();
    this.getClasses();
    this.getHC();
    this.areas = this._areaService.models;

    this.createMinionStatsFromCharacters();
    return null;
  }

  async createMinionStatsFromCharacters() 
  {
    //Runs for the first time to get the inferior minions
    this.charactersList = [];
    if(this._minionStatsService.models.length == 0)
    {
      for(let i = 0; i < this._characterService.models.length; i++)
      if(this._characterService.models[i].rarity == 'Inferior' && 
        this._characterService.models[i].type == 2)
      {
        let minion: MinionStats = await this._minionStatsService.svcPromptCreateNew(
          this._characterService.models[i].name, this._characterService.models[i].areaId,
          this._areaService.svcFindById(this._characterService.models[i].areaId)?.order,
          this._characterService.models[i].classId);
      
        this.charactersList.push(minion);
      }
      this.allCharacterList = this.charactersList;
      this.description = `Showing ${this.charactersList.length} results`;

      return;
    } 

    //Runs every time to add new characters that are not inside minionStats
    for(let i = 0; i < this._characterService.models.length; i++)
      for(let j = 0; j < this._minionStatsService.models.length; j++)
        if(this._characterService.models[i].rarity == 'Inferior' && 
          this._characterService.models[i].type == 2 &&
          this._characterService.models[i].name == this._minionStatsService.models[j].name)
        {
          this.charactersList.push(this._minionStatsService.models[j]);
          break;
        }

        else if(j == this._minionStatsService.models.length-1 && 
              this._characterService.models[i].rarity == 'Inferior' && 
              this._characterService.models[i].type == 2)
        {
          let minion: MinionStats = await this._minionStatsService.svcPromptCreateNew(
            this._characterService.models[i].name, this._characterService.models[i].areaId,
            this._areaService.svcFindById(this._characterService.models[i].areaId)?.order,
            this._characterService.models[i].classId);
        
          this.charactersList.push(minion);
        }
    this.allCharacterList = this.charactersList;
    this.description = `Showing ${this.charactersList.length} results`;

  }
 
  getClasses()
  {
    for(let i = 0; i < this._classService.models.length; i++)
      this.classes.push(this._classService.models[i].name);
  }

  getHC() 
  {
    for(let i = 0; i < this._areaService.models.length; i++)
      if(this._areaService.models[i].order != undefined && this._areaService.models[i].order.toString() != '')
      this.HCs.push(this._areaService.models[i].order);

    this.HCs = [...new Set(this.HCs)];
  }

  filterCharactersByArea()
  {
    this.validaCharacters = [];

    for(let i = 0; i < this.allCharacterList.length; i++)
      if(this.areaFilter == this.allCharacterList[i].area)
        this.validaCharacters.push(this.allCharacterList[i]);

    this.charactersList = [];
    this.charactersList = this.validaCharacters;
  }

  filterCharactersByClass()
  {
    this.charactersList = [];

    for(let i = 0; i < this._classService.models.length; i++)
      if(this._classService.models[i].name == this.classFilter)
      {
        this.classFilter = this._classService.models[i].id;
        break;
      }
    
    for(let i = 0; i < this.allCharacterList.length; i++)
      if(this.allCharacterList[i].klass == this.classFilter) this.charactersList.push(this.allCharacterList[i]); 
  }

  filterCharactersByHC()
  {
    this.charactersList = [];

    for(let i = 0; i < this.allCharacterList.length; i++)
      if(this.hcFilter == this.allCharacterList[i].hc)
        this.charactersList.push(this.allCharacterList[i]);
  }

  filterHC(type: string)
  {
    this.hcFilter = type;
    this.filterCharacters();
  }

  filterArea(areaId: string)
  {
    this.areaFilter = areaId;
    this.filterCharacters();
  }

  filterClass(klassName: string)
  {

    this.classFilter = this.getClassID(klassName);
    this.filterCharacters();
  }

  getClassID(klassName:string)
  {
    for(let i = 0; i < this._classService.models.length; i++)
      if(this._classService.models[i].name == klassName)
        return this._classService.models[i].id;
    
    return 'ALL';
  }

  filterCharacters()
  {
    if(this.classFilter == "ALL" && this.hcFilter == "ALL" && this.areaFilter == "ALL")
    {
      this.charactersList = [];
      this.charactersList = this.allCharacterList; 
    }
    else if(this.classFilter == "ALL" && this.hcFilter == "ALL" && this.areaFilter !== "ALL")
      this.filterCharactersByArea();
    else if(this.classFilter !== "ALL" && this.hcFilter == "ALL" && this.areaFilter == "ALL")
      this.filterCharactersByClass();
    else if(this.classFilter == "ALL" && this.hcFilter !== "ALL" && this.areaFilter == "ALL")
      this.filterCharactersByHC();
    else
    {
      this.charactersList = [];
      for(let i = 0; i < this.allCharacterList.length; i++)
        if(this.generalFilter(i))
          this.charactersList.push(this.allCharacterList[i]);
    }
  }

  generalFilter(i:number):boolean
  {
    let result: boolean = false;

    if(this.classFilter == 'ALL')
      return result = this.areaFilter == this.allCharacterList[i].area &&
        this.hcFilter == this.allCharacterList[i].hc;
    else if(this.areaFilter == 'ALL')
      return result = this.classFilter == this.allCharacterList[i].klass &&
        this.hcFilter == this.allCharacterList[i].hc;
    else if(this.hcFilter == 'ALL')
      return result = this.classFilter == this.allCharacterList[i].klass &&
        this.areaFilter == this.allCharacterList[i].area;
    else
      return result = this.classFilter == this.allCharacterList[i].klass &&
        this.areaFilter == this.allCharacterList[i].area &&
        this.hcFilter == this.allCharacterList[i].hc;
  }
 
  sortByNumberOrder: number = 1;
  sortByNumber(fieldName:string) 
  {
    this.sortByNumberOrder *= -1;
    this.charactersList.sort((a,b)=>
    {
      if (!a[fieldName] && b[fieldName]) return 1;
      if (a[fieldName] && !b[fieldName]) return -1;
      if (!a[fieldName] && !b[fieldName]) return 0;
      return this.sortByNumberOrder * (a[fieldName] - b[fieldName]);
    })
  }

  sortByStringOrder: number = 1;
  sortByString(fieldName:string)
  {
    this.sortByStringOrder *= -1;
    this.charactersList.sort((a,b)=>
    {
      if (!a[fieldName] && b[fieldName]) return 1;
      if (a[fieldName] && !b[fieldName]) return -1;
      if (!a[fieldName] && !b[fieldName]) return 0;
      let nameA = a[fieldName];
      let nameB = b[fieldName];
      return this.sortByStringOrder * nameA.localeCompare(nameB);
    })
  }

  search(searchTerm: string)
  {
    this.charactersList = [];

    for(let i = 0; i < this.allCharacterList.length; i++)
      if(this.allCharacterList[i].name.toLowerCase().includes(searchTerm.toLowerCase()))
        this.charactersList.push(this.allCharacterList[i]);

    if(searchTerm.length == 0)
      this.charactersList = this.allCharacterList;
    
    this.description = `Showing ${this.charactersList.length} results`;
  }

  async changeMinionValue(minion:MinionStats, value:string, minionField:string)
  {
    minion[minionField] = value;
    await this._minionStatsService.svcToModify(minion);
  }

  async copyFromExcel(minionStat:MinionStats): Promise<void>
  {
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/).filter(line => line);
    let minionFields: string[] = ['ml', 'hp', 'atk', 'def', 'qi', 'luk', 'pr', 'ev'];

    for (let l = 0; l < lines.length; l++) 
    {
      let line = lines[l];
      let cols = line.split(/\t/);
      for(let j = 0; j < minionFields.length; j++)
      {
        minionStat[minionFields[j]] = cols[j];
      }
    }
    await this._minionStatsService.svcToModify(minionStat);
    Alert.ShowSuccess('Minion Stats imported successfully!');
    this.ngOnInit();
  }
}
