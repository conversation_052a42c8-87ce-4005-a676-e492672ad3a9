import { Data } from 'src/lib/darkcloud/angular/dsadmin';
import { IdPrefixes } from 'src/lib/darkcloud/dialogue-system';
import { Base } from './Base';

export class Weapon extends Base<Data.Hard.IWeapon, Data.Result.IWeapon> implements Required<Data.Hard.IWeapon>
{
  protected static generateId(index: number): string 
  {
    return IdPrefixes.WEAPON + index;
  }

  constructor(
    index: number,
    itemId: string,
    dataAccess: Weapon['TDataAccess']
  )
  {
    super({hard: {id: Weapon.generateId(index), itemId}}, dataAccess);
  }

  public get itemId(): string
  {
    return this.hard.itemId;
  }
  public set itemId(value: string)
  {
    this.hard.itemId = value;
  }

  public get description(): string
  {
    return this.hard.description;
  }
  public set description(value: string)
  {
    this.hard.description = value;
  }

  public get shake(): boolean
  {
    return this.hard.shake;
  }
  public set shake(value: boolean)
  {
    this.hard.shake = value;
  }

  public get hit(): boolean
  {
    return this.hard.hit;
  }
  public set hit(value: boolean)
  {
    this.hard.hit = value;
  }

  public get split(): number
  {
    return this.hard.split;
  }
  public set split(value: number)
  {
    this.hard.split = value;
  }

  public get wlbase(): number
  {
    return this.hard.wlbase;
  }
  public set wlbase(value: number)
  {
    this.hard.wlbase = value;
  }

  public get qiMin(): number
  {
    return this.hard.qiMin;
  }
  public set qiMin(value: number)
  {
    this.hard.qiMin = value;
  }

  public get luckMin(): number
  {
    return this.hard.luckMin;
  }
  public set luckMin(value: number)
  {
    this.hard.luckMin = value;
  }

  public get classesId(): string[]
  {
    return this.hard.classesId;
  }
  public set classesId(value: string[])
  {
    this.hard.classesId = value;
  }

  public get charactersId(): string[]
  {
    return this.hard.charactersId;
  }
  public set charactersId(value: string[])
  {
    this.hard.charactersId = value;
  }

  public get enabledClassesId(): string[]
  {
    return this.hard.enabledClassesId;
  }
  public set enabledClassesId(value: string[])
  {
    this.hard.enabledClassesId = value;
  }

  public get enabledCharactersId(): string[]
  {
    return this.hard.enabledCharactersId;
  }
  public set enabledCharactersId(value: string[])
  {
    this.hard.enabledCharactersId = value;
  }

  public get passives(): string[]
  {
    return this.hard.passives;
  }
  public set passives(value: string[])
  {
    this.hard.passives = value;
  }

  public get effectId(): string
  {
    return this.hard.effectId;
  }
  public set effectId(value: string)
  {
    this.hard.effectId = value;
  }

}
