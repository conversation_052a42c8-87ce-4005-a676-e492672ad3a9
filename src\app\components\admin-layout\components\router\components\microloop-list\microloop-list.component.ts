import { Component, } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Area } from 'src/app/lib/@bus-tier/models';
import { Button } from 'src/app/lib/@pres-tier/data';
import { UserSettingsService } from 'src/app/services';
import { LanguageService } from 'src/app/services/language.service';
import { MicroloopContainerService } from 'src/app/services/microloop-container.service';
import { TranslationService } from 'src/app/services/translation.service';
import { Alert } from 'src/lib/darkcloud';
import { TranslatableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/TranslatableListComponent';
import { LevelType } from 'src/lib/darkcloud/dialogue-system';

@Component({
  selector: 'app-microloop-list',
  templateUrl: './microloop-list.component.html',
  styleUrls: ['./microloop-list.component.scss']
})
export class MicroloopListComponent extends TranslatableListComponent<Area> {

  LevelType = LevelType;

  constructor(
    _activatedRoute: ActivatedRoute,
    private _microloopContainerService: MicroloopContainerService,
    _userSettingsService: UserSettingsService,
    private _router: Router,
    protected override _translationService: TranslationService,
    protected override _languageService: LanguageService
  )
  {
    super(_microloopContainerService, _activatedRoute, _userSettingsService, 'name', _translationService, _languageService);
  }

 override listName: string = 'Microloop List';

  hierarchyLevels: string[] = [];

  public override readonly addButtonTemplate: Button.Templateable = 
  {
    title: 'Add a new instance to the list',
    onClick: this.createNewMicroloop.bind(this),
    iconClass: 'pe-7s-plus',
    btnClass: Button.Klasses.FILL_GREEN,
  };

  async createNewMicroloop()
  {
    const name = await Alert.showPrompt('Area Name');
    if(this.isMicroloopAlreadyCreated(name))
    {
      Alert.showError(`The microloop: ${name} already exists!`);
      return;
    }
    if(name == '')
    {
      Alert.showError('You need to give a name to the microloop!');
      return;
    }
    else if(name == undefined) return;
    
    let loop = await this._microloopContainerService.svcPromptCreateNew(name);
    if(!loop) return;

    this._microloopContainerService.srvAdd(loop);
    setTimeout(() => {
      this.HighlightElement(loop.id, 110, true);
    }, 100);

    this.lstFetchLists();

  }

  isMicroloopAlreadyCreated(areaName: string)
  {
    for(let i = 0; i < this._microloopContainerService.models.length; i++)
    {
      if(this._microloopContainerService.models[i]?.name == areaName)
      {
        return true;
      }
    }
    return false;
  }

  routeToMicroloopListComponent(container: Area)
  {
    this._router.navigate(['microloopList', container.id, 'microloops'], {fragment: container.id});
  }

  downloadMicroloopContainerOrtography(loopContainer: Area)
  {
    this._translationService.getMicroloopContainerOrtography(loopContainer, true);
  }

}
