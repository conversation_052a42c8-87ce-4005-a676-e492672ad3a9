import { ChangeDetectorRef, Component, Input } from '@angular/core';
import { PrimalModifier } from 'src/app/lib/@bus-tier/models/PrimalModifier';
import { Button } from 'src/app/lib/@pres-tier/data';
import { BattleUpgradeService, CharacterService, ClassService, ModifierService, ParryPryService, PrimalModifierService, TierService } from 'src/app/services';
import { CustomService } from 'src/app/services/custom.service';
import { UserSettingsService } from 'src/app/services/user-settings.service';
import { Alert } from 'src/lib/darkcloud';
import { Primal } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard';
import { ICollectibleDetails } from 'src/lib/darkcloud/angular/dsadmin/v9/data/result';
import { SortableListComponent } from 'src/lib/darkcloud/angular/easy-mvc/SortableListComponent';
import { BattleUpgrade } from '../../../../../../../lib/@bus-tier/models/BattleUpgrade';
import { GameTypes } from 'src/lib/darkcloud/dialogue-system';
import { ActivatedRoute } from '@angular/router';
import { removeAccents } from 'src/lib/others';

@Component({
  selector: 'app-primal-modifier',
  templateUrl: './primal-modifier.component.html',
  styleUrls: ['./primal-modifier.component.scss'],
})

export class PrimalModifierComponent  {
  @Input() character = '';
  currentModifier: PrimalModifier;
  primalModifier: PrimalModifier;
  primalList: PrimalModifier;
  primals: Primal[] = [];
  currentCharacter: BattleUpgrade;
  valueBossLevel: string;
  nameClass: string;
  nameRarity: string;
  type: string;
  valueParry: string;
  existParry = false;
  valueDiference = false;
  // Array para controlar diferenças individuais de cada input
  inputDifferences: boolean[] = [];

  constructor(
    protected _customService: CustomService,
    _activatedRoute: ActivatedRoute,
    private ref: ChangeDetectorRef,
    protected _primalModifierService: PrimalModifierService,
    _userSettingsService: UserSettingsService,
    private _modifierService: ModifierService,
    protected _battleUpgradeService: BattleUpgradeService,
    private _characterService: CharacterService,
    private _classService: ClassService,
    private _parryPryService: ParryPryService,
    private _tierService: TierService,
  ) {   
  }

  public readonly excelButtonTemplate: Button.Templateable =
    {
      title: 'Paste content from excel',
      onClick: this.onExcelPaste.bind(this),
      iconClass: 'excel-icon',
      btnClass: Button.Klasses.FILL_ORANGE,
    };

   async ngOnInit() {
    try {
      // Aguarda o carregamento de todos os serviços necessários
      await Promise.all([
        this._primalModifierService.toFinishLoading(),
        this._modifierService.toFinishLoading(),
        this._battleUpgradeService.toFinishLoading(),
        this._characterService.toFinishLoading(),
        this._classService.toFinishLoading(),
        this._tierService.toFinishLoading(),
        this._parryPryService.toFinishLoading()
      ]);

      // Busca o modifier atual
      this.currentModifier = this._primalModifierService.models.find(modifier => modifier.character == this.character);
      this.currentCharacter = this._battleUpgradeService.models.find(modifier => modifier.character == this.character);

      // Se não encontrar o currentModifier, cria um novo
      if (!this.currentModifier) {
        this.currentModifier = this._primalModifierService.createNewPrimalModifier(this.character);
      }

      // Executa os métodos após garantir que os dados estão carregados
      this.createPrimalModifier();

      // Aguarda um pouco para garantir que createPrimalModifier terminou
      // getValueParry() já chama applyPercentageToLastItems() no final
      setTimeout(() => {
        this.getValueParry();
      }, 100);

    } catch (error) {
      console.error('Erro ao carregar dados no ngOnInit:', error);
    }
  }


  createPrimalModifier() {
    // 1. Remove itens obsoletos (que não existem mais no _modifierService.models)
    const itemsToRemove = this.currentModifier.primalModifier.filter(primal => {
      const normalizedFieldName = removeAccents(primal.fieldName?.toLowerCase() || '');

      // Verifica se o item ainda existe no _modifierService.models
      const stillExists = this._modifierService.models.some(modifier => {
        const normalizedSkill = removeAccents(modifier.skill?.toLowerCase() || '');
        return normalizedFieldName === normalizedSkill;
      });

      return !stillExists; // Retorna true para itens que devem ser removidos
    });

    // Remove os itens obsoletos
    if (itemsToRemove.length > 0) {
      this.currentModifier.primalModifier = this.currentModifier.primalModifier.filter(primal => {
        const normalizedFieldName = removeAccents(primal.fieldName?.toLowerCase() || '');

        const shouldKeep = this._modifierService.models.some(modifier => {
          const normalizedSkill = removeAccents(modifier.skill?.toLowerCase() || '');
          return normalizedFieldName === normalizedSkill;
        });

        return shouldKeep;
      });
    }

    // 2. Adiciona novos itens (que existem no _modifierService.models mas não no primalModifier)
    for (let i = 0; i < this._modifierService.models.length; i++) {
      const skillName = this._modifierService.models[i].skill;
      const normalizedSkill = removeAccents(skillName?.toLowerCase() || '');

      // Verifica se o skillName já existe no primalModifier (ignorando acentos)
      const existingItem = this.currentModifier.primalModifier.find(primal => {
        const normalizedFieldName = removeAccents(primal.fieldName?.toLowerCase() || '');
        return normalizedFieldName === normalizedSkill;
      });

      // Se não existir, adiciona o novo item
      if (!existingItem) {
        this.currentModifier.primalModifier.push({
          fieldName: skillName,
          fieldValue: undefined
        });   
      }
    }

    // Salva as alterações
    this._primalModifierService.svcToModify(this.currentModifier);  
    let valueCharacter = this.currentCharacter === undefined ? this.character : this.currentCharacter.character;

      if (this.currentCharacter !== undefined) {
        const op = this.currentCharacter.bl != undefined ? this.currentCharacter.bl : 0;
        this.valueBossLevel = `BL: ${op}`;
      } else {
        this.valueBossLevel = 'BL: 0';
      }

      const character = this._characterService.models.find(x => x.id === valueCharacter);
      this.nameRarity = character?.rarity;
      this.nameClass = this._classService.models.find(x => x.id === character?.classId)?.name;
      this.type = GameTypes.characterTypeName[character.type];

      this._primalModifierService.svcToModify(this.currentModifier);
      this.ref.detectChanges();
  }

  /**
   * Aplica '%' nos últimos itens do array baseado na existência de 'Parry'
   * - Se existe 'Parry': aplica nos 3 últimos itens
   * - Se NÃO existe 'Parry': aplica nos 2 últimos itens
   * - Remove '%' de itens que não estão de acordo com a regra
   */
  applyPercentageToLastItems(): void {
    if (!this.currentModifier || !this.currentModifier.primalModifier) {
      return;
    }
    // Verifica se existe um item com fieldName == 'Parry'
    const hasParryItem = this.currentModifier.primalModifier.some(item => item.fieldName === 'Parry');

    // Define quantos itens do final devem receber '%'
    const itemsToAddPercentage = hasParryItem ? 4 : 3;

    // Aplica a lógica de percentage com validação
    this.currentModifier.primalModifier = this.currentModifier.primalModifier.map((item, index) => {
      const shouldReceivePercentage = index >= this.currentModifier.primalModifier.length - itemsToAddPercentage;

      if (shouldReceivePercentage) {
        // Item DEVE ter percentage - adiciona '%' se não existir
        if (!item.percentage) {
          item.percentage = '%';
        }
      } else {
        // Item NÃO DEVE ter percentage - remove se existir
        if (item.percentage === '%') {
          delete item.percentage;
        } else if (item.percentage) {
          console.log(`Item ${index} (${item.fieldName}) tem percentage customizado: ${item.percentage} - mantendo`);
        }
      }

      return item;
    });
  }

  /**
   * Inicializa o array de diferenças para cada input
   * Aplica background-color APENAS se fieldName === 'Parry' E valor for diferente
   */
  initializeInputDifferences(): void {
    if (!this.currentModifier || !this.currentModifier.primalModifier) {
      return;
    }

    // Inicializa o array com o tamanho correto
    this.inputDifferences = new Array(this.currentModifier.primalModifier.length).fill(false);

    // Para cada item, verifica APENAS se é Parry e se tem diferença
    this.currentModifier.primalModifier.forEach((item, index) => {
      // Background-color APENAS para campos 'Parry' com valor diferente
      if (item.fieldName === 'Parry' && this.valueParry) {
        const currentValue = item.fieldValue?.toString().trim() || '';
        const parryValue = this.valueParry?.toString().trim() || '';
        this.inputDifferences[index] = currentValue !== parryValue;
      } else {
        // Todos os outros campos (não-Parry) sempre false = sem background
        this.inputDifferences[index] = false;
      }
    });
  }

getValueParry() {
    // Inicializa as variáveis
    this.valueParry = '';
    this.existParry = false;

    // Validações iniciais
    if (!this.currentModifier || !this.currentModifier.primalModifier) {
        return;
    }

    if (!this.nameClass || !this.nameRarity) {
        return;
    }

    // Carrega os dados do serviço
    this._parryPryService.toFinishLoading();

    // Obtém o ID da raridade baseado no nome da raridade do personagem
    const idSelectRarity = this._tierService.getCollectibleRarity('Character Rarity').find(x => x.name == this.nameRarity)?.id;

    if (!idSelectRarity) {
        return;
    }

    // 1. Verifica se existe um item com fieldName == 'Parry' no primalModifier
    const parryItem = this.currentModifier.primalModifier.find(prima => prima.fieldName === 'Parry');

    if (parryItem) {    
        // 2. Verifica se existe uma classe correspondente no parryPryService
        const matchingParry = this._parryPryService.models.find(parry => parry.className === this.nameClass);

        if (matchingParry) {    
            // 3. Busca a raridade correspondente
            const rarity = matchingParry.rarityValue.find(x => x.idRarity == idSelectRarity);
     
            if (rarity) {
                // Atualiza o valor do Parry apenas se não estiver definido
                if (parryItem.fieldValue == undefined) {
                    parryItem.fieldValue = parseFloat(rarity.value);
                    this.existParry = true;   
                }

                // Sempre atualiza o valueParry para comparação
                this.valueParry = rarity.value;    
            } else {
                console.log('Raridade não encontrada para idSelectRarity: ', idSelectRarity);
            }
        } else {
            console.log('Classe não encontrada no parryPryService: ', this.nameClass);
        }
    } 

    // SEMPRE aplica a lógica de percentage no final, independente se houve alteração no Parry
    // Isso garante que os últimos itens tenham % aplicado corretamente
    this.applyPercentageToLastItems();

    // Inicializa o array de diferenças após carregar os dados
    this.initializeInputDifferences();

    // Salva as modificações
    this._primalModifierService.svcToModify(this.currentModifier);
    this.ref.detectChanges();
}

/**
 * Altera o valor de um primal no modifier.
 * 
 * @param value O novo valor do primal.
 * @param index O índice do primal no array de primais.
 * @param primal O primal que está sendo alterado.
 */
async changePrimalValue(value: string, index: number, primal: Primal) {

  // Verifica se o input atual é do tipo 'Parry'
  const isParryField = this.currentModifier.primalModifier[index]?.fieldName === 'Parry';

  // Background-color APENAS se for campo 'Parry' E valor for diferente
  if (isParryField && this.valueParry) {
    // Compara valores convertendo ambos para string e removendo espaços
    const inputValue = value?.toString().trim();
    const parryValue = this.valueParry?.toString().trim();

    // Atualiza diferença APENAS para campo Parry
    this.inputDifferences[index] = inputValue !== parryValue;
    this.valueDiference = this.inputDifferences[index];
  } 
  else {
    // Para TODOS os campos que NÃO são Parry: sempre false (sem background)
    this.inputDifferences[index] = false;
  }

  this.currentModifier.hard.primalModifier[index].fieldValue = value === '' ? undefined : +value;
  this._primalModifierService.svcToModify(this.currentModifier);
  this.ref.detectChanges();
}

  async onExcelPaste(): Promise<void> {
    // this.spinnerService.setState(true);
    const text = await navigator.clipboard.readText();
    const lines = text.split(/\r?\n/);
    lines.filter((l) => l !== "");

    if (this.displayErrors(lines)) return;

    for (let l = 0; l < lines.length; l++) {
      let line = lines[l];
      let cols = line.split(/\t/);
      cols = cols.filter((col) => col !== "");

      this.primalList = this._primalModifierService.models.find((i) => i.character === this.character);
      let primal = this.primals.find((x) => x.fieldName === cols[0]?.split('')
        .join('')
        .split('.')
        .join('')
        .replace(',', '.'));

      if (!primal) continue;

      if (cols[1]?.trim()) {
        primal.fieldValue = +(cols[1].split(' ')
          .join('')
          .split('.')
          .join('')
          .replace(',', '.'));
      }
      else {
        primal.fieldValue = undefined;
      }

      this.primalList.primalModifier = [];
      this.primalList.primalModifier.push(primal);
    }
    this.primalList.primalModifier = this.primals;
    this._primalModifierService.svcToModify(this.primalList);
    this._primalModifierService.toSave();
    Alert.ShowSuccess('Primal Modifier successfully!');

    this.ngOnInit();
    this.ref.detectChanges();
    //this.spinnerService.setState(false);
    this.ngOnInit();
  }

  isPrimalModifierOnPrimals = (cols: string[], i: number): boolean => cols[0]?.trim().toLowerCase() == this.primals[i].fieldName.toLowerCase();

  displayErrors(array) {
    let count = array[0].split(/\t/);
    if (count.length < 2) {
      Alert.showError("Copy the MODIFIERS column values too!");
      return true;
    }

    if (count[0] === "") {
      Alert.showError("You are probably copying a blank column!");
      return true;
    }

    return false;
  }

  reset(character) {
    this.character = character;
    this.ngOnInit();
  }

  async changePercentage(primalModifier: PrimalModifier, value: any, index: number) {
    primalModifier.hard.primalModifier[index]['percentage'] = value;
    await this._primalModifierService.svcToModify(primalModifier);
    await this._primalModifierService.toSave();
  }
}
